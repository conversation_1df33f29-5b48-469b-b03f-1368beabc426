{"name": "front-end", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ag-grid-community/locale": "^33.1.1", "@babel/polyfill": "^7.4.4", "@mdi/font": "^7.0.96", "@riophae/vue-treeselect": "^0.4.0", "@types/file-saver": "^2.0.5", "ag-grid-community": "^31.3.4", "ag-grid-vue": "^31.3.4", "axios": "^0.26.1", "babel-plugin-import": "^1.13.5", "core-js": "^3.8.3", "date-fns": "^2.28.0", "echarts": "4.1.0", "element-ui": "^2.15.13", "file-saver": "^2.0.5", "is-regexp": "^3.1.0", "qs": "^6.10.3", "roboto-fontface": "*", "sass": "~1.32", "sass-loader": "^12.6.0", "screenfull": "^6.0.1", "sortablejs": "^1.15.0", "v-calendar": "^2.4.1", "vue": "^2.6.14", "vue-echarts": "4.0.2", "vue-moment": "^4.1.0", "vue-numeric": "^2.5.1", "vue-pdf-embed": "^1.1.4", "vue-router": "^3.5.1", "vue-table-with-tree-grid": "^0.2.4", "vue-tree-table-component": "^0.1.1", "vuelidate": "^0.7.7", "vuetify": "^2.6.0", "vuetify-multiple-draggable-dialogs": "^1.1.4", "vuetify-numeric": "^0.2.1", "vuetify-pro-dialog": "^2.0.3", "vuetify-toolkit": "^0.4.2", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "^5.0.0", "@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-service": "^5.0.0", "deepmerge": "^4.2.2", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "lint-staged": "^11.1.2", "nice-proxy": "^0.1.11", "prettier": "^2.4.1", "vue-cli-plugin-vuetify": "^2.4.8", "vue-property-decorator": "^8.5.1", "vue-template-compiler": "^2.6.14"}, "resolutions": {"@vue/cli-service/portfinder": "1.0.21"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": "vue-cli-service lint"}}