# 询价单页面详细列表展示优化总结

## 优化目标
在询价单页面的报价详情部分，为每个报价单添加展开功能，显示详细的费用明细列表：
- 预估修理费&加工费明细
- 预估其他费用明细  
- 预估备件费用明细
- 预估物料费用明细

## 主要修改内容

### 1. 表头优化
**文件**: `voyage-enquiry-detail.vue`

添加了展开按钮列：
```javascript
this.quoteHeaders = [
  { text: '', value: 'expand', sortable: false, width: '50px' },
  { text: '报价单号', value: 'quoteNo' },
  // ... 其他列
]
```

### 2. 数据加载优化
在 `loadQuoteList` 方法中为每个报价单获取详细列表数据：

```javascript
// 为每个报价单获取详细列表数据
const quoteListWithDetails = await Promise.all(
  quoteList.map(async (quote) => {
    try {
      // 获取报价单详细信息
      const { data: quoteDetail } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/getQuoteDetailById',
        { id: quote.id }
      )
      
      // 处理详细列表数据
      const detailList = quoteDetail.detailList || []
      const repairList = detailList.filter(item => item.model === 0)
      const otherList = detailList.filter(item => item.model === 1)
      const spareList = detailList.filter(item => item.model === 2)
      const materialList = detailList.filter(item => item.model === 3)
      
      return {
        ...quote,
        repairList,
        otherList,
        spareList,
        materialList,
        expanded: false, // 控制展开状态
      }
    } catch (error) {
      // 错误处理
      return { ...quote, repairList: [], otherList: [], spareList: [], materialList: [], expanded: false }
    }
  })
)
```

### 3. 展开按钮实现
在表格中添加展开按钮：

```vue
<!-- 展开按钮 -->
<template v-slot:[`item.expand`]="{ item }">
  <v-btn icon small @click="toggleExpand(item)">
    <v-icon>{{ item.expanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
  </v-btn>
</template>
```

### 4. 详细列表展示
在表格后添加展开的详细内容：

```vue
<!-- 展开的详细列表 -->
<div v-for="quote in quoteList" :key="quote.id">
  <v-expand-transition>
    <div v-if="quote.expanded" class="mt-4">
      <v-card outlined>
        <v-card-title class="subtitle-1">
          {{ quote.supplierName }} - {{ quote.quoteNo }} 详细列表
        </v-card-title>
        <v-card-text>
          <!-- 四个详细列表的表格 -->
        </v-card-text>
      </v-card>
    </div>
  </v-expand-transition>
</div>
```

### 5. 详细列表表格结构

#### 预估修理费&加工费明细
```vue
<v-simple-table dense>
  <thead>
    <tr>
      <th>修理项目</th>
      <th>数量</th>
      <th>单价</th>
      <th>总价</th>
      <th>备注</th>
    </tr>
  </thead>
  <tbody>
    <tr v-for="item in quote.repairList" :key="item.id">
      <td>{{ item.itemName || '-' }}</td>
      <td>{{ item.quotNum || '-' }}</td>
      <td>{{ formatCurrency(item.price, quote.ccyCode) }}</td>
      <td>{{ formatCurrency(item.total, quote.ccyCode) }}</td>
      <td>{{ item.remark || '-' }}</td>
    </tr>
  </tbody>
</v-simple-table>
```

#### 预估备件费用明细
```vue
<v-simple-table dense>
  <thead>
    <tr>
      <th>备件名称</th>
      <th>备件编号</th>
      <th>图纸号</th>
      <th>序列号</th>
      <th>数量</th>
      <th>单价</th>
      <th>总价</th>
      <th>备注</th>
    </tr>
  </thead>
  <tbody>
    <tr v-for="item in quote.spareList" :key="item.id">
      <td>{{ item.componentName || '-' }}</td>
      <td>{{ item.componentNo || '-' }}</td>
      <td>{{ item.drawingSerialNumber || '-' }}</td>
      <td>{{ item.serialNumber || '-' }}</td>
      <td>{{ item.quotNum || '-' }}</td>
      <td>{{ formatCurrency(item.price, quote.ccyCode) }}</td>
      <td>{{ formatCurrency(item.total, quote.ccyCode) }}</td>
      <td>{{ item.remark || '-' }}</td>
    </tr>
  </tbody>
</v-simple-table>
```

### 6. 展开控制方法
添加 `toggleExpand` 方法：

```javascript
// 切换展开状态
toggleExpand(item) {
  this.$set(item, 'expanded', !item.expanded)
}
```

## 功能特点

### 1. 详细信息展示
- 点击展开按钮可查看每个报价单的详细费用明细
- 四种费用类型分别显示在独立的表格中
- 表格包含完整的项目信息（名称、编号、数量、单价、总价、备注等）

### 2. 智能数据处理
- 异步并发获取所有报价单的详细信息
- 根据 `model` 字段自动分类到对应的列表
- 错误处理确保页面稳定性

### 3. 用户体验优化
- 平滑的展开/收起动画效果
- 清晰的视觉层次和布局
- 统一的货币格式化显示

### 4. 性能考虑
- 使用 `Promise.all` 并发请求提高加载速度
- 只在需要时展开详细内容
- 合理的错误处理避免单个请求失败影响整体

## 显示效果

用户在询价单页面可以：

1. **查看报价汇总**：在主表格中看到每个供应商的费用汇总
2. **展开详细列表**：点击展开按钮查看具体的费用明细项目
3. **分类查看**：四种费用类型分别显示，便于对比分析
4. **完整信息**：每个明细项目包含完整的技术和商务信息

这样的设计让用户既能快速浏览报价汇总，又能深入了解每个报价单的详细构成，大大提升了询价单的信息透明度和决策支持能力。
