# 询价单页面优化总结

## 优化目标
在询价单页面的报价详情表格中，将报价单的费用信息细化显示，包括：
- 预估修理费&加工费 (repairExpense)
- 预估其他费用 (otherExpense)  
- 预估备件费用 (spareExpense)
- 预估物料费用 (materialExpense)

## 主要修改内容

### 1. 表头优化
**文件**: `voyage-enquiry-detail.vue`

**原有表头**:
```javascript
{ text: '修理费用', value: 'repairExpense' },
{ text: '修理费成交价', value: 'finalPrice' },
{ text: '其他费用', value: 'otherExpense' },
```

**新表头**:
```javascript
{ text: '预估修理费&加工费', value: 'repairExpense' },
{ text: '预估其他费用', value: 'otherExpense' },
{ text: '预估备件费用', value: 'spareExpense' },
{ text: '预估物料费用', value: 'materialExpense' },
{ text: '修理费成交价', value: 'finalPrice' },
```

### 2. 数据处理优化
在 `loadQuoteList` 方法中确保每个报价单都有完整的费用分类信息：

```javascript
// 确保每个报价单都有费用分类信息，如果没有则设置为0
this.quoteList = quoteList.map(quote => ({
  ...quote,
  repairExpense: quote.repairExpense || 0,
  otherExpense: quote.otherExpense || 0,
  spareExpense: quote.spareExpense || 0,
  materialExpense: quote.materialExpense || 0,
}))
```

### 3. 模板显示优化
为新增的费用列添加格式化显示：

```vue
<!-- 预估修理费&加工费 -->
<template v-slot:[`item.repairExpense`]="{ item }">
  {{ formatCurrency(item.repairExpense, item.ccyCode) }}
</template>

<!-- 预估其他费用 -->
<template v-slot:[`item.otherExpense`]="{ item }">
  {{ formatCurrency(item.otherExpense, item.ccyCode) }}
</template>

<!-- 预估备件费用 -->
<template v-slot:[`item.spareExpense`]="{ item }">
  {{ formatCurrency(item.spareExpense, item.ccyCode) }}
</template>

<!-- 预估物料费用 -->
<template v-slot:[`item.materialExpense`]="{ item }">
  {{ formatCurrency(item.materialExpense, item.ccyCode) }}
</template>
```

### 4. 货币格式化方法
新增 `formatCurrency` 方法来统一处理货币显示：

```javascript
formatCurrency(amount, currency) {
  if (!amount && amount !== 0) return '-'
  const num = Number(amount)
  if (isNaN(num)) return '-'
  
  // 根据币种决定小数位数
  const decimals = currency === 'JPY' ? 0 : 2
  return num.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}
```

### 5. 总价计算优化
更新 `total` 和 `totalAdd` 计算属性，包含所有四种费用类型：

```javascript
total() {
  // 获取中标的报价单
  const quoteWins = this.quoteList.filter((detail) => detail.isWins)
  if (quoteWins.length === 0) {
    return this.detail.otherCost || 0
  }
  // 获取第一个中标报价单
  const selectedQuote = quoteWins[0]
  // 各类费用
  const repairExpense = Number(selectedQuote.repairExpense) || 0
  const otherExpense = Number(selectedQuote.otherExpense) || 0
  const spareExpense = Number(selectedQuote.spareExpense) || 0
  const materialExpense = Number(selectedQuote.materialExpense) || 0
  // 汇率换算
  const rate = this.getRate(selectedQuote.ccyCode)
  // 计算总价（包含所有费用类型）
  return ((repairExpense + otherExpense + spareExpense + materialExpense) * rate).toFixed(2)
}
```

### 6. 折算美元合计优化
更新 `toUsd` 显示逻辑，包含所有费用类型：

```vue
<template v-slot:[`item.toUsd`]="{ item }">
  {{
      ((Number(item.repairExpense) + Number(item.otherExpense) + 
        Number(item.spareExpense) + Number(item.materialExpense)) *
        getRate(item.ccyCode)).toFixed(2)
  }}
</template>
```

## 功能特点

### 1. 详细费用分类
- 清晰显示四种不同类型的费用
- 便于用户了解报价单的详细构成
- 提高费用透明度

### 2. 智能格式化
- 根据币种自动调整小数位数（日元无小数位，其他币种2位小数）
- 使用千分位分隔符提高可读性
- 空值或无效值显示为 "-"

### 3. 准确计算
- 总价计算包含所有四种费用类型
- 汇率换算准确
- 数据处理健壮，避免 NaN 错误

### 4. 向后兼容
- 保持原有功能不变
- 对缺失数据进行默认值处理
- 不影响现有业务流程

## 显示效果

根据提供的接口数据示例，表格将显示：

| 报价单号 | 供应商名称 | 预估修理费&加工费 | 预估其他费用 | 预估备件费用 | 预估物料费用 | 修理费成交价 | 折算美元合计 |
|---------|-----------|-----------------|-------------|-------------|-------------|-------------|-------------|
| VRSTZJ25052201 | 供应商A | 22.00 | 16.00 | 1,029.00 | 6.00 | 1,073.00 | 1,073.00 |

这样的显示方式让用户能够清楚地看到每个报价单的详细费用构成，便于比较和决策。
