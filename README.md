# 海丰国际项目前端

## 项目规范

### 代码格式规范

换行符必需全部使用LF模式，即`\n`换行。

**注意**：请执行 `git config --global core.autocrlf input` 来让Git将CRLF转成LF。

在javascript中，无论前后端（前端指的是需经过webpack编译的前端）

- **严禁**使用`var`来声明变量，必需使用`let`或`const`
- 字符串必需使用**单引号**表示，要在字符串中加入变量的话必需使用**反引号**（模板字符串）

本项目已内置`eslint`和`prettier`进行代码格式化规范并已配置于git hook上，commit操作前将自动格式化文件。

首次使用需在vscode中安装以下插件：

- ESLint
- Prettier - Code formatter
- Vetur

`.vscode`中用户工作区中默认配置为推荐配置，不建议修改

在安装`Vetur`后能够加载部分模板（待完善）

**切勿修改项目根目录配置文件，如需解除`eslint`警报请自行在文件内部使用注释`disable eslint ...`进行取消**

### 仓库规范

`master`分支用于发布稳定版本。`dev`分支用于开发，请在自己的功能分支上进行开发，在开发完成后将`dev`分支在本地合并到自己分支上进行测试，测试成功后再发起合并请求。

合并请求的要求：

- 功能需要测试通过，解决所有警告，包括IDE警告、编译时警告、运行时警告
- 请求中不存在未完成的功能
- 代码均符合代码规范
- 请求中不得存在污染项目环境的文件（`.gitignore`中的文件本地可修改但不允许提交）

对于部分`.gitignore`中确需修改的文件，请执行：

```bash
git update-index --assume-unchanged <file>
```

来让git对文件不追踪（如改写部分配置文件），除非是该文件确可复用（如编写的`vetur`模板），否则**切勿提交**

## 技术栈

项目使用vue2+vuetify开发

所用插件

- axios
- vuetify-pro-dialog
- echarts@4.1.0
- v-calendar
- screenfull
- vue-pdf-embed
- vuetify-numeric

## 开发指南

### 添加页面流程

1. 在`@/views/`目录下创建页面文件，命名形如`input-info.vue`，即使用横线分割单词。不同业务模块（机务，海务...）在不同路径下添加，可设置多级目录分类。

2. 在`@/router/sysRoutes/`目录下选择对应业务模块的文件添加，说明如下

   ```javascript
   export default {
     path: '/maritime-affairs',  //一级模块路径，访问时将包含
     title: '海务管理',   //一级模块名称
     icon: 'mdi-anchor',   //一级模块图标
     children: [
       {   //二级模块
         title: '发文通函',   //二级模块名称
         icon: 'mdi-post-outline',   //二级模块图标
         group: '/circular',   //二级模块分组，实际为v-list-group的group参数，接受字符串或正则表达式以确定活动状态，必需包含children中的所有路由格式
         children: [
           {   //实际页面
             path: '/circular/list',   //实际页面路径，实际路由需加上一级模块路径，确保唯一!!!
             name: 'circular-list',   //路由名称，确保唯一!!!
             component: () => import('@/views/maritime-affairs/circular-list'),   //路由组件，懒加载
             meta: {
               title: '安全通报&指定人员发文',   //页面显示名称
               access: '安全通报&指定人员发文:列表',  //权限
             },
           },
         ],
       },
     ],
   }
   // 访问实际访问地址为/maritime-affairs/circular/list
```
   
   按上述规则添加对应页面即可

### 网络请求

在Vue上下文中调用`this.postAsync`、`this.getAsync`、`this.blobDownload`、`this.getBlobDownload`即可调用位于`/@/util/net.js`里的封装后的axios函数，具体使用方法自行查看函数定义。

### 全局数据

在Vue上下文中调用`this.$local.data`可调用位于`/@/util/localStorageHelper.js`中的方法。用户登录首次进入页面时，系统自动请求位于`/system/user/getUserInfoByUsername`的接口获取用户信息数据，并存储于`userInfo`中，使用方式如下

```javascript
// 获取用户名称,详细参数参加接口文档
this.$local.data.get('userInfo').nickName
// 其它全局信息存储
this.$local.data.set('xxxx',xxxx)
```

### 文件下载

由于MINIO端文件存储时文件名称会被强制转换为日期，若直接使用url下载则会得到无意义的文件名。
实际的文件名称与文件映射关系存储于数据库端，因此下载文件时，若要指定下载文件名则需调用文件服务接口接口传递`filePath`，实际的`fileName`可为任意字符（默认使用attachmentRecord内的name即可，示例代码如下

```javascript
// 获取用户名称,详细参数参加接口文档
`/api/system/file/download?fileName=${attachmentRecord.name}&filePath=${attachmentRecord.filePath}`
```

### 网络代理

由于项目后端较多，有时可能经常需要切换不同代理的后端地址，切换后需要重启且配置修改频繁，因此引入 [nice-proxy](https://www.npmjs.com/package/nice-proxy)，对于部分后端地址可自行配置`/nice-proxy/`下的文件（使用方案参考官方文档或`/nice-proxy/readme.md`）。后续即可无需重启项目更换后端代理地址。

#### 请记得修改该文件后执行 `git update-index --assume-unchanged <file>`!

## Project setup

```bash
yarn install
```

### Compiles and hot-reloads for development

```bash
yarn run serve
```

### Compiles and minifies for production

```bash
yarn run build
```

### Run your tests

```bash
yarn run test
```

### Lints and fixes files

```bash
yarn run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).
