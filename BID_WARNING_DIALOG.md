# 定标警告弹窗功能实现总结

## 功能需求
当定标时检测到警告信息，需要显示一个弹窗，标题是 `this.detail.alert` 的内容，让操作者选择继续定标或取消定标。

## 实现方案

### 1. 弹窗界面设计
创建了一个美观的警告弹窗，包含：

```vue
<!-- 定标警告弹窗 -->
<v-dialog v-model="submitDialog" max-width="600px" persistent>
  <v-card>
    <v-card-title class="headline d-flex align-center">
      <v-icon color="warning" class="mr-2" size="28">mdi-alert</v-icon>
      <span class="warning--text">定标警告</span>
    </v-card-title>
    <v-card-text>
      <div class="text-body-1 mb-4 black--text">
        {{ detail.alert }}
      </div>
      <v-divider class="mb-3"></v-divider>
      <div class="text-body-2 grey--text">
        请确认是否继续进行定标操作
      </div>
    </v-card-text>
    <v-card-actions>
      <v-spacer></v-spacer>
      <v-btn color="grey" text @click="cancelBidSubmit">
        取消定标
      </v-btn>
      <v-btn color="primary" @click="continueBidSubmit" :loading="quoteLoading">
        继续定标
      </v-btn>
    </v-card-actions>
  </v-card>
</v-dialog>
```

### 2. 数据流程设计

#### 现有的检查逻辑
在 `confirmquoteBid` 方法中已经实现了警告检查：

```javascript
async confirmquoteBid() {
  // ... 前置处理逻辑
  
  // 保存报价单详细信息
  const { data } = await this.postAsync(
    '/business/shipAffairs/voyageRepair/voyageEnquiryUpdateFinalPrice',
    quoteListForSave,
  )
  
  if (data) {
    // 检查供应商通用性
    const { data } = await this.getAsync(
      '/business/shipAffairs/voyageRepair/checkSupplyCommon',
      {
        enquiryId: this.$route.params.id,
        quoteId: quoteWins[0].id,
        remark: this.detail.remark,
        otherCost: this.detail.otherCost,
        attachmentIds: this.detail.attachmentIds,
        arriveDate: this.detail.arriveDate,
      },
    )
    
    this.quoteLoading = false
    if (data) {
      // 设置警告信息并显示弹窗
      this.detail.alert = data
      this.submitDialog = true
    }
  }
}
```

### 3. 用户交互处理

#### 取消定标
```javascript
// 取消定标
cancelBidSubmit() {
  this.submitDialog = false
  this.quoteBidDialog = false
  this.quoteLoading = false
}
```

**功能**：
- 关闭警告弹窗
- 关闭定标确认弹窗
- 停止加载状态
- 用户可以重新选择或修改

#### 继续定标
```javascript
// 继续定标
continueBidSubmit() {
  this.submitDialog = false
  // 继续执行原来的定标逻辑
  this.proceedWithBidSubmit()
}
```

**功能**：
- 关闭警告弹窗
- 调用实际的定标提交逻辑

### 4. 实际定标逻辑

#### 分离的定标执行方法
```javascript
// 执行实际的定标提交逻辑
async proceedWithBidSubmit() {
  const quoteWins = []
  this.quoteList.forEach((detail) => {
    if (detail.isWins) {
      quoteWins.push(detail)
    }
  })

  // 重新计算费用总价
  this.recalculateQuoteTotals()
  
  // 更新备注
  this.detail.remark = this.detail.remark + `\n费用说明：${this.costRemark}`
  this.quoteLoading = true

  // 准备数据并合并详细列表
  const quoteListForSave = this.quoteList.map((quote) => {
    const detailList = [
      ...(quote.repairList || []),
      ...(quote.otherList || []),
      ...(quote.spareList || []),
      ...(quote.materialList || []),
    ]

    return {
      ...quote,
      detailList,
      repairList: undefined,
      otherList: undefined,
      spareList: undefined,
      materialList: undefined,
    }
  })

  try {
    // 保存报价单详细信息
    const { data } = await this.postAsync(
      '/business/shipAffairs/voyageRepair/voyageEnquiryUpdateFinalPrice',
      quoteListForSave,
    )

    if (data) {
      // 执行开标操作
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/openBid',
        {
          enquiryId: this.$route.params.id,
          quoteId: quoteWins[0].id,
          remark: this.detail.remark,
          otherCost: this.detail.otherCost,
          attachmentIds: this.detail.attachmentIds,
          arriveDate: this.detail.arriveDate,
        },
      )
      
      this.quoteLoading = false
      if (!errorRaw) {
        this.closeAndTo(this.backRouteName)
      }
    }
  } catch (error) {
    this.quoteLoading = false
    console.error('定标失败:', error)
  }
}
```

### 5. 界面特点

#### 视觉设计
- **警告图标**：使用 `mdi-alert` 图标，橙色警告色
- **标题突出**：使用警告色文字突出显示
- **内容清晰**：警告信息用较大字体显示
- **分隔线**：使用分隔线区分警告内容和提示信息
- **按钮对比**：取消按钮用灰色，继续按钮用主色调

#### 交互体验
- **持久化弹窗**：使用 `persistent` 属性，防止意外关闭
- **加载状态**：继续定标按钮显示加载状态
- **清晰选择**：两个按钮功能明确，避免误操作

### 6. 业务流程

#### 正常定标流程
1. 用户点击定标 → 填写费用说明 → 点击确定
2. 系统保存数据 → 检查供应商通用性
3. 如果没有警告 → 直接执行开标操作
4. 定标完成 → 跳转到列表页

#### 有警告的定标流程
1. 用户点击定标 → 填写费用说明 → 点击确定
2. 系统保存数据 → 检查供应商通用性
3. **如果有警告 → 显示警告弹窗**
4. 用户选择：
   - **取消定标**：停止操作，可以重新选择
   - **继续定标**：忽略警告，继续执行开标操作
5. 定标完成 → 跳转到列表页

### 7. 数据变量

#### 新增的数据变量
```javascript
data() {
  return {
    submitDialog: false,  // 控制警告弹窗显示
    costRemark: '',       // 费用说明
    // ... 其他变量
  }
}
```

#### 警告信息存储
```javascript
// 警告信息存储在 detail.alert 中
this.detail.alert = data  // 来自后端接口的警告信息
```

## 功能优势

### 1. 用户体验
- **信息透明**：清楚显示警告内容
- **选择自由**：用户可以根据警告信息做出决策
- **操作安全**：防止误操作，提供二次确认

### 2. 业务安全
- **风险提示**：及时提醒潜在问题
- **决策支持**：为用户提供完整的决策信息
- **流程完整**：保持业务流程的完整性

### 3. 技术实现
- **代码复用**：分离警告检查和实际执行逻辑
- **错误处理**：完善的异常处理机制
- **状态管理**：清晰的状态控制和重置

这个实现确保了定标过程的安全性和用户体验，让操作者能够在充分了解风险的情况下做出明智的决策。
