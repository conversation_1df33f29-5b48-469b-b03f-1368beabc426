# finalNum 显示问题修复总结

## 问题描述
当 `item.finalNum == 0` 且 `this.detail.businessStatus != '报价完成'` 时，页面上 finalNum 不显示为 0 而显示为空。

## 问题原因分析

### 1. 数据处理逻辑问题
原始的 `processDetailList` 函数逻辑有问题：

```javascript
// 原始逻辑（有问题）
finalNum: item.finalNum == 0 && this.detail.businessStatus == '报价完成'
  ? item.quotNum
  : item.finalNum
```

这个逻辑的问题：
- 当 `finalNum == 0` 且状态不是 '报价完成' 时，返回 `item.finalNum`（即 0）
- 但是在模板中使用了 `|| ''` 逻辑，导致 0 被当作 falsy 值处理

### 2. 模板显示逻辑问题
原始模板使用了错误的显示逻辑：

```vue
<!-- 原始逻辑（有问题） -->
<span v-else>{{ item.finalNum || '' }}</span>
```

这会导致：
- 当 `finalNum` 为 0 时，`0 || ''` 返回 `''`（空字符串）
- 所以页面显示为空而不是 0

## 修复方案

### 1. 修复数据处理逻辑
将 `processDetailList` 函数改为更合理的逻辑：

```javascript
// 修复后的逻辑
const processDetailList = (list) => {
  return list.map((item) => ({
    ...item,
    finalNum:
      item.finalNum !== undefined && item.finalNum !== null
        ? item.finalNum
        : item.quotNum || 0,
    realPrice:
      item.realPrice !== undefined && item.realPrice !== null
        ? item.realPrice
        : item.price || 0,
  }))
}
```

**修复要点**：
- 使用 `!== undefined && !== null` 来判断值是否已设置
- 只有当值未设置时才使用默认值
- 保留所有已设置的值，包括 0

### 2. 修复模板显示逻辑
添加专门的显示方法：

```javascript
// 显示 finalNum，正确处理 0 值
displayFinalNum(value) {
  return value !== undefined && value !== null ? value : '-'
}
```

在模板中使用新的显示方法：

```vue
<!-- 修复后的逻辑 -->
<span v-else>{{ displayFinalNum(item.finalNum) }}</span>
```

**修复要点**：
- 只有当值为 `undefined` 或 `null` 时才显示 '-'
- 0 值会正确显示为 '0'
- 避免了 JavaScript 的 falsy 值陷阱

## 修复效果

### 修复前
- `finalNum = 0` 时显示为空
- 用户无法区分是否已设置值

### 修复后
- `finalNum = 0` 时正确显示为 '0'
- `finalNum = undefined/null` 时显示为 '-'
- 用户可以清楚地看到实际设置的值

## 涉及的文件和位置

### 数据处理修复
- **文件**: `voyage-enquiry-detail.vue`
- **位置**: `processDetailList` 函数（第 1524-1537 行）
- **修改**: 改进了 finalNum 和 realPrice 的默认值设置逻辑

### 显示方法添加
- **文件**: `voyage-enquiry-detail.vue`
- **位置**: methods 部分（第 1395-1399 行）
- **修改**: 添加了 `displayFinalNum` 方法

### 模板修复
- **文件**: `voyage-enquiry-detail.vue`
- **位置**: 四个表格的 finalNum 显示部分
  - 修理费&加工费表格（第 686 行）
  - 其他费用表格（第 777 行）
  - 备件费用表格（第 868 行）
  - 物料费用表格（第 977 行）
- **修改**: 将 `{{ item.finalNum || '' }}` 改为 `{{ displayFinalNum(item.finalNum) }}`

## 技术要点

### JavaScript 的 Falsy 值
在 JavaScript 中，以下值被认为是 falsy：
- `false`
- `0`
- `-0`
- `0n`
- `""`（空字符串）
- `null`
- `undefined`
- `NaN`

使用 `||` 操作符时，如果左侧是 falsy 值，会返回右侧的值。这就是为什么 `0 || ''` 返回 `''` 的原因。

### 正确的空值检查
要正确区分 0 和未设置的值，应该使用：
```javascript
value !== undefined && value !== null
```
而不是：
```javascript
value || defaultValue
```

这个修复确保了数值 0 能够正确显示，同时保持了良好的用户体验。
