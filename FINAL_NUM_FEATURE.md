# 实际数量(finalNum)功能实现总结

## 功能需求
在询价单页面的详细列表中添加实际数量字段 `finalNum`，要求：
1. 默认等于报价数量 `quotNum`
2. 定标时可以手动填写
3. 修理费成交价等于实际数量*单价相加之和

## 主要实现内容

### 1. 数据初始化
在 `loadQuoteList` 方法中为每个明细项目设置默认的实际数量：

```javascript
// 为每个明细项目设置默认的实际数量（等于报价数量）
const processDetailList = (list) => {
  return list.map(item => ({
    ...item,
    finalNum: item.finalNum !== undefined ? item.finalNum : item.quotNum
  }))
}

const repairList = processDetailList(detailList.filter((item) => item.model === 0))
const otherList = processDetailList(detailList.filter((item) => item.model === 1))
const spareList = processDetailList(detailList.filter((item) => item.model === 2))
const materialList = processDetailList(detailList.filter((item) => item.model === 3))
```

### 2. 表格结构优化
在所有详细列表表格中添加了实际数量列：

#### 修理费&加工费/其他费用/物料费用表格
```vue
<th class="text-center" style="width: 20%">修理项目</th>
<th class="text-center" style="width: 10%">报价数量</th>
<th class="text-center" style="width: 20%">实际数量</th>
<th class="text-center" style="width: 10%">单价</th>
<th class="text-center" style="width: 10%">总价</th>
<th class="text-center" style="width: 20%">备注</th>
```

#### 备件费用表格
```vue
<th class="text-center" style="width: 15%">备件名称</th>
<th class="text-center" style="width: 8%">备件编号</th>
<th class="text-center" style="width: 8%">图纸号</th>
<th class="text-center" style="width: 8%">序列号</th>
<th class="text-center" style="width: 8%">报价数量</th>
<th class="text-center" style="width: 15%">实际数量</th>
<th class="text-center" style="width: 10%">单价</th>
<th class="text-center" style="width: 10%">总价</th>
<th class="text-center" style="width: 30%">备注</th>
```

### 3. 可编辑实际数量
实际数量字段在定标时可以编辑：

```vue
<td class="text-center">
  <v-text-field
    v-if="canEditFinalNum"
    v-model="item.finalNum"
    type="number"
    dense
    single-line
    hide-details
    @input="updateItemTotal(item)"
    style="min-width: 80px;"
  ></v-text-field>
  <span v-else>{{ item.finalNum || '-' }}</span>
</td>
```

### 4. 编辑权限控制
添加计算属性控制实际数量的编辑权限：

```javascript
// 是否可以编辑实际数量（定标时可以编辑）
canEditFinalNum() {
  return (
    this.detail.businessStatus === '报价完成' ||
    this.detail.businessStatus === '超期' ||
    this.detail.businessStatus === '重新定标' ||
    this.detail.status === '4'
  )
}
```

### 5. 总价计算逻辑
#### 明细项目总价计算
```javascript
// 计算明细项目的总价（实际数量 * 单价）
calculateItemTotal(item) {
  const finalNum = Number(item.finalNum) || Number(item.quotNum) || 0
  const price = Number(item.price) || 0
  return finalNum * price
}
```

#### 实时更新总价
```javascript
// 更新明细项目的总价（实际数量 * 单价）
updateItemTotal(item) {
  item.total = this.calculateItemTotal(item)
  // 重新计算报价单的各类费用总价
  this.recalculateQuoteTotals()
}
```

#### 重新计算报价单总价
```javascript
// 重新计算报价单的费用总价
recalculateQuoteTotals() {
  this.quoteList.forEach((quote) => {
    // 计算修理费&加工费总价
    quote.repairExpense = quote.repairList?.reduce((sum, item) => {
      return sum + this.calculateItemTotal(item)
    }, 0) || 0

    // 计算其他费用总价
    quote.otherExpense = quote.otherList?.reduce((sum, item) => {
      return sum + this.calculateItemTotal(item)
    }, 0) || 0

    // 计算备件费用总价
    quote.spareExpense = quote.spareList?.reduce((sum, item) => {
      return sum + this.calculateItemTotal(item)
    }, 0) || 0

    // 计算物料费用总价
    quote.materialExpense = quote.materialList?.reduce((sum, item) => {
      return sum + this.calculateItemTotal(item)
    }, 0) || 0
  })
}
```

### 6. 定标保存逻辑
在 `confirmquoteBid` 方法中，保存前重新计算所有费用总价：

```javascript
async confirmquoteBid() {
  // ... 其他逻辑
  
  // 在保存前重新计算所有费用总价
  this.recalculateQuoteTotals()
  
  // 保存报价单详细信息（包括实际数量和重新计算的总价）
  const { data } = await this.postAsync(
    '/business/shipAffairs/voyageRepair/voyageEnquiryUpdateFinalPrice',
    this.quoteList,
  )
  
  // ... 其他逻辑
}
```

### 7. 总价显示优化
所有表格中的总价列都使用新的计算方法：

```vue
<td class="text-center">
  {{ formatCurrency(calculateItemTotal(item), quote.ccyCode) }}
</td>
```

## 功能特点

### 1. 智能默认值
- 实际数量默认等于报价数量
- 避免用户重复输入相同数值

### 2. 权限控制
- 只有在定标状态时才能编辑实际数量
- 其他状态下为只读显示

### 3. 实时计算
- 修改实际数量时立即重新计算总价
- 自动更新报价单的各类费用汇总

### 4. 数据一致性
- 确保明细总价与汇总费用一致
- 定标保存时重新计算所有相关数据

### 5. 用户体验
- 输入框样式简洁，不影响表格布局
- 实时反馈，修改后立即看到计算结果

## 业务流程

1. **报价阶段**：供应商填写报价数量和单价
2. **定标准备**：系统自动将实际数量设置为报价数量
3. **定标过程**：用户可以修改实际数量，系统实时计算新的总价
4. **定标确认**：保存时重新计算所有费用，确保数据准确性
5. **结果展示**：最终的修理费成交价基于实际数量计算

这个功能实现了灵活的定标流程，允许在定标时根据实际需求调整数量，同时确保费用计算的准确性和数据的一致性。
