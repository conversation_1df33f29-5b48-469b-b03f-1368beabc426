process.env.VUE_APP_TOKEN = 'hf_token'
process.env.VUE_APP_FULL_VERSION = require('child_process')
  .execSync('git rev-parse HEAD')
  .toString()
process.env.VUE_APP_VERSION = process.env.VUE_APP_FULL_VERSION.substr(0, 8)

const { useNiceProxy } = require('nice-proxy')

module.exports = {
  configureWebpack: {
    devtool: 'source-map',
  },
  devServer: {
    port: 2233,
    proxy: {
      '/api': {
        // 该地址实际已无作用，但由于vue-cli中该参数必须有值且为有效url，因此仍保留
        target: 'http://**************:8999',
        changeOrigin: true,
        router: useNiceProxy,
        pathRewrite: {
          '^/api': '/',
        },
      },
    },
  },
  transpileDependencies: true,
  pages: {
    index: {
      // entry for the page
      entry: 'src/main.js',
      title: 'localhost:2233',
      //title: '海丰国际船舶管理系统',
    },
  },
}
