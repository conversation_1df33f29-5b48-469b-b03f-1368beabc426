# 实际费用详情功能实现总结

## 功能需求
在航修订单详情页面中添加实际费用的详细列表，包括：
1. 实际修理费/加工费
2. 实际其他费用
3. 实际备件费用
4. 实际物料费用

每个类别都需要支持增加行和删除行的功能，并且有相应的必填项验证。

## 实现方案

### 1. 页面结构设计

#### 新增标签页
在 `subtitles` 中添加了 "实际费用详情" 标签页：
```javascript
subtitles: ['基本信息', '实际费用详情', '供应商信息']
```

#### 四个费用表格
- **实际修理费/加工费表格**：修理项目、单价、数量、实际总价、预估总价、备注
- **实际其他费用表格**：费用项目、单价、数量、实际总价、预估总价、备注
- **实际备件费用表格**：备件名称、备件编号、图纸号、序列号、单价、数量、实际总价、预估总价、备注
- **实际物料费用表格**：物料名称、物料编码、单价、数量、实际总价、预估总价

### 2. 数据结构设计

#### 数据分类
根据 `detailList` 中的 `model` 字段进行分类：
```javascript
// model = 0: 修理费/加工费
this.repairList = this.detail.detailList.filter(item => item.model === 0)
// model = 1: 其他费用
this.otherList = this.detail.detailList.filter(item => item.model === 1)
// model = 2: 备件费用
this.spareList = this.detail.detailList.filter(item => item.model === 2)
// model = 3: 物料费用
this.materialList = this.detail.detailList.filter(item => item.model === 3)
```

#### 默认值设置
```javascript
setDefaultValues(item) {
  if (item.finalNum === undefined || item.finalNum === null) {
    item.finalNum = item.quotNum || 0
  }
  if (item.realPrice === undefined || item.realPrice === null) {
    item.realPrice = item.price || 0
  }
  if (item.finalTotal === undefined || item.finalTotal === null) {
    item.finalTotal = (item.finalNum || 0) * (item.realPrice || 0)
  }
}
```

### 3. 编辑权限控制

#### 编辑条件
只有在特定业务状态下才能编辑：
```javascript
canEdit() {
  return (
    this.detail.businessStatus === '已安排' ||
    this.detail.businessStatus === '完工退回'
  )
}
```

#### 界面控制
- 可编辑时显示输入框和操作按钮
- 只读时显示文本内容

### 4. 增删功能实现

#### 添加项目
每个类别都有对应的添加方法：
```javascript
// 添加修理项目
addRepairItem() {
  this.repairList.push({
    itemName: '',
    realPrice: 0,
    finalNum: 0,
    finalTotal: 0,
    total: 0,
    remark: '',
    model: 0,
    orderId: this.detail.id
  })
}
```

#### 删除项目
```javascript
// 删除修理项目
removeRepairItem(index) {
  this.repairList.splice(index, 1)
}
```

### 5. 验证规则实现

#### 备件费用验证
当数量不为0时，以下字段为必填项：
- 备件名称 (`componentName`)
- 备件编号 (`componentNo`)
- 图纸号 (`drawingSerialNumber`)
- 单价 (`realPrice`)

```javascript
spareNameRules(item) {
  return [
    () => {
      if (Number(item.finalNum) > 0 && !item.componentName) {
        return '数量不为0时，备件名称为必填项'
      }
      return true
    }
  ]
}
```

#### 物料费用验证
当数量不为0时，以下字段为必填项：
- 物料名称 (`itemName`)
- 物料编码 (`code`)
- 单价 (`realPrice`)

```javascript
materialNameRules(item) {
  return [
    () => {
      if (Number(item.finalNum) > 0 && !item.itemName) {
        return '数量不为0时，物料名称为必填项'
      }
      return true
    }
  ]
}
```

### 6. 计算功能

#### 实时计算总价
当用户修改数量或单价时，自动计算总价：
```javascript
calculateItemTotal(item) {
  const finalNum = Number(item.finalNum) || 0
  const realPrice = Number(item.realPrice) || 0
  item.finalTotal = finalNum * realPrice
}
```

#### 货币格式化
```javascript
formatCurrency(value) {
  return (value || 0).toFixed(2)
}
```

### 7. 数据保存

#### 合并数据
保存时将四个分类列表合并为一个 `detailList`：
```javascript
async save(goBack, notMove = false) {
  // 合并详细列表数据
  const detailList = [
    ...this.repairList,
    ...this.otherList,
    ...this.spareList,
    ...this.materialList
  ]
  
  const saveData = {
    ...this.detail,
    detailList
  }
  
  const { errorRaw } = await this.postAsync(
    '/business/shipAffairs/voyageRepair/saveOrUpdateOrder',
    saveData,
  )
  // ...
}
```

### 8. 界面特点

#### 表格设计
- 使用 `v-simple-table` 组件，简洁美观
- 合理的列宽分配，确保信息完整显示
- 区分实际总价和预估总价，便于对比

#### 交互体验
- 添加按钮位于表格标题右侧，操作便捷
- 删除按钮使用图标按钮，节省空间
- 实时计算和验证，提供即时反馈

#### 视觉效果
- 每个费用类别使用独立的卡片容器
- 统一的样式和布局，保持一致性
- 清晰的标题和分组，便于理解

## 数据流程

### 1. 数据加载流程
1. 调用 `loadDetail()` 获取订单详情
2. 调用 `processDetailList()` 处理详细列表
3. 根据 `model` 字段分类数据
4. 设置默认值和计算总价

### 2. 数据编辑流程
1. 用户修改数量或单价
2. 触发 `calculateItemTotal()` 重新计算总价
3. 实时更新界面显示

### 3. 数据保存流程
1. 用户点击保存
2. 合并四个分类列表为 `detailList`
3. 调用后端接口保存数据
4. 返回成功或错误信息

## 业务价值

### 1. 精细化管理
- 提供详细的费用明细，便于成本控制
- 区分预估和实际费用，便于分析差异

### 2. 灵活性
- 支持动态增删项目，适应不同的修理需求
- 可编辑的实际数量和单价，反映真实情况

### 3. 数据完整性
- 必填项验证确保关键信息完整
- 自动计算减少人工错误

### 4. 用户体验
- 直观的表格界面，操作简单
- 实时反馈和验证，提高效率

这个实现提供了完整的实际费用详情管理功能，满足了航修业务的精细化管理需求，同时保持了良好的用户体验和数据完整性。
