import { routes } from '@/router/flattenUrl'

let navList = []
let navs = []
for (const key in routes) {
  navs.push({
    title: routes[key].title,
    icon: routes[key].icon,
    path: routes[key].path,
  })
  navList.push(
    routes[key].children.map((item) => {
      return {
        ...item,
        children: item.children.filter((child) => !child.meta.notShowInNav),
      }
    }),
  )
}

export { navs, navList }
