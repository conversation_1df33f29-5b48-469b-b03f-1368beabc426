<template>
  <v-navigation-drawer
    width="240"
    :mini-variant="minNav"
    mini-variant-width="64"
    color="primary"
    dark
    hide-overlay
    clipped
    :expand-on-hover="minNav"
    app
    v-model="drawer"
    v-if="items.length > 0"
  >
    <v-list>
      <v-list-item
        v-for="(item, index) in items"
        :key="index"
        @click="selectedIndex = index"
        :input-value="selectedIndex === index"
      >
        <v-list-item-avatar>
          <v-icon x-large>{{ item.icon }}</v-icon>
        </v-list-item-avatar>
        <v-list-item-title>
          {{ item.title }}
        </v-list-item-title>
      </v-list-item>
    </v-list>
    <v-divider></v-divider>
    <v-list flat dense shaped>
      <template v-for="list in demoNavLists[selectedIndex]">
        <v-list-group
          :key="list.title"
          :prepend-icon="list.icon"
          active-class="white--text"
          :group="list.group"
        >
          <template v-slot:activator>
            <v-list-item-content>
              <v-list-item-title>{{ list.title }}</v-list-item-title>
            </v-list-item-content>
          </template>
          <v-list-item
            v-for="item in list.children"
            :key="item.name"
            active-class="secondary white--text"
            :to="items[selectedIndex].path + item.path"
          >
            <v-list-item-content>
              <v-list-item-title>{{ item.meta.title }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list-group>
      </template>
    </v-list>
  </v-navigation-drawer>
</template>

<script>
import { routes } from '@/router/flattenUrl'
export default {
  model: {
    prop: 'navDrawer',
    event: 'updateMiniNav',
  },
  name: 'navigation-side-bar',
  props: {
    navDrawer: {
      type: Boolean,
      default: false,
    },
    resources: Array,
  },
  created() {
    // let navList = []
    // let navs = []
    // // console.log(this.$local.data.get('userInfo'))
    // // const resources = this.$local.data.get('userInfo').resources
    // this.items = navs
    // this.demoNavLists = navList
  },
  watch: {
    navDrawer(val) {
      if (this.$vuetify.breakpoint.mdAndDown) {
        this.drawer = val
      } else {
        this.minNav = val
      }
    },
    drawer(val) {
      this.$emit('updateMiniNav', val)
    },
    resources(val) {
      for (const key in routes) {
        if (val.includes(routes[key].access) || val.includes('ROLE_ADMIN')) {
          this.items.push({
            title: routes[key].title,
            icon: routes[key].icon,
            path: routes[key].path,
          })
          let demoNavList = routes[key].children.map((item) => {
            return {
              ...item,
              children: item.children.filter(
                (child) =>
                  !child.meta.notShowInNav &&
                  (val.includes(child.meta.access) ||
                    val.includes('ROLE_ADMIN')),
              ),
            }
          })
          this.demoNavLists.push(
            demoNavList.filter((i) => i.children.length > 0),
          )
        }
      }
      // this.$nextTick(() => {})
    },
  },
  data() {
    return {
      drawer: true,
      minNav: false,
      selectedIndex: 0,
      selectedItem: 1,
      items: [],
      demoNavLists: [],
    }
  },
  methods: {},
}
</script>
<style scoped></style>
