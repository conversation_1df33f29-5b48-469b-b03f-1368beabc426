<template>
  <v-app>
    <v-app-bar
      class="d-print-none"
      height="50px"
      color="primary"
      elevation="4"
      clipped-left
      app
      dark
    >
      <v-app-bar-nav-icon @click="miniNav = !miniNav">
        <v-icon>
          {{ miniNav ? 'mdi-menu' : 'mdi-backburger' }}
        </v-icon>
      </v-app-bar-nav-icon>
      <div>
        <v-img
          class="mr-3"
          src="@/assets/logo.png"
          height="60px"
          width="100px"
        ></v-img>
      </div>
      <v-toolbar-title>
        智慧船舶
        <div class="subtitle-1" style="display: inline">
          {{ this.$local.data.get('userInfo').isShipSyS ? '船端' : '岸端' }}(#{{
            version
          }})
        </div>
      </v-toolbar-title>
      <v-spacer></v-spacer>
      <v-toolbar-items>
        <!-- <v-chip :to="{ name: 'UserIndex' }" class="ma-2" color="primary" label>
          <v-icon left>mdi-account-circle-outline</v-icon>
          {{ nickName }}
        </v-chip>
        <v-btn class="d-md-flex d-none" @click="fullscreen" text>
          <v-icon>{{ fullscreenIcon }}</v-icon>
        </v-btn>
        <v-btn text @click="logout">
          <v-icon>mdi-logout</v-icon>
        </v-btn> -->
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn @click="jumpToOrder" text v-bind="attrs" v-on="on">
              <v-icon>mdi-order-bool-descending-variant</v-icon>
            </v-btn>
          </template>
          <span>工单系统</span>
        </v-tooltip>
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }" v-if="isNeed()">
            <v-btn @click="jump" text v-bind="attrs" v-on="on">
              <v-icon>mdi-face-man-shimmer</v-icon>
            </v-btn>
          </template>
          <span>魔学院免登录</span>
        </v-tooltip>
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-chip
              :to="{ name: 'UserIndex' }"
              class="ma-2"
              color="primary"
              label
              v-bind="attrs"
              v-on="on"
            >
              <v-icon left>mdi-account-circle-outline</v-icon>
              {{ nickName }}

              ({{ roleName }})
            </v-chip>
          </template>
          <span>个人信息</span>
        </v-tooltip>
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              class="d-md-flex d-none"
              @click="fullscreen"
              text
              v-bind="attrs"
              v-on="on"
            >
              <v-icon>{{ fullscreenIcon }}</v-icon>
            </v-btn>
          </template>
          <span>全屏显示</span>
        </v-tooltip>
        <v-tooltip bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn text @click="logout" v-bind="attrs" v-on="on">
              <v-icon>mdi-logout</v-icon>
            </v-btn>
          </template>
          <span>退出登陆</span>
        </v-tooltip>
      </v-toolbar-items>
    </v-app-bar>

    <navigation-side-bar
      class="d-print-none"
      v-model="miniNav"
      :resources="resources"
    />

    <v-main class="divder grey lighten-5">
      <v-expand-transition>
        <v-tabs
          class="d-print-none"
          color="secondary"
          :style="`position: fixed; z-index: 3; width:calc(100% - ${mainLeft}px);`"
          show-arrows
          height="35px"
          ref="tags"
        >
          <v-tooltip
            :disabled="!item.tooltip"
            v-for="(item, i) in tagList"
            :key="item.path"
            bottom
          >
            <template v-slot:activator="{ on, attrs }">
              <v-tab
                v-bind="attrs"
                v-on="on"
                overflow-x-hidden
                :name="i"
                @contextmenu.prevent="showMenu($event, item)"
                :to="item.path"
              >
                {{ item.meta.title }}
                <v-icon
                  size="20"
                  v-if="item.meta.title != '首页'"
                  @click.stop.prevent="closeSelectedTag(item)"
                  @contextmenu.stop.prevent=""
                >
                  mdi-close
                </v-icon>
              </v-tab>
            </template>
            <span>{{ item.tooltip }}</span>
          </v-tooltip>
        </v-tabs>
      </v-expand-transition>
      <v-menu
        v-model="tabMenu"
        :position-x="x"
        :position-y="y"
        absolute
        offset-y
        min-width="110"
        style="z-index: 100"
      >
        <v-list dense>
          <v-list-item
            @click="closeOtherTabs()"
            v-ripple="{ class: 'secondary--text' }"
          >
            <v-list-item-title>关闭其他</v-list-item-title>
          </v-list-item>
          <v-list-item
            @click="closeAllTabs()"
            v-ripple="{ class: 'secondary--text' }"
          >
            <v-list-item-title>关闭所有</v-list-item-title>
          </v-list-item>
          <v-list-item @click="refresh" v-ripple="{ class: 'secondary--text' }">
            <v-list-item-title>刷新</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
      <!-- 用于弹窗附加，使得弹窗位置正确 -->
      <div
        id="mask"
        :style="`left:${mainLeft}px;pointer-events: ${maskLayer};`"
      ></div>
      <v-container fluid class="pt-8 px-0" id="main-content">
        <keep-alive :include="$store.state.keepAlive.keepLiveRoute">
          <router-view ref="tab" :key="temp || $route.path"></router-view>
        </keep-alive>
      </v-container>
    </v-main>
  </v-app>
</template>

<script>
// 全屏插件
import screenfull from 'screenfull'
import NavigationSideBar from './navigation-side-bar.vue'
import { mapGetters } from 'vuex'
// import Sortable from 'sortablejs'
export default {
  name: 'layout-view',
  components: { NavigationSideBar },
  data() {
    return {
      nickName: '',
      roleName: '',
      miniNav: false,
      version: process.env.VUE_APP_VERSION,
      fullscreenIcon: 'mdi-fullscreen',
      tabMenu: false,
      contextMenuItem: null,
      x: 0,
      y: 0,
      resources: [],
      temp: '',
    }
  },

  created() {
    const dashboardRoute = {
      path: '/home',
      name: 'HomeView',
      component: () => import('../views/HomeView.vue'),
      meta: {
        title: '首页',
      },
    }
    this.addViewTags(dashboardRoute)
  },
  computed: {
    tagList() {
      return this.$store.state.viewTags.viewTags
    },
    mainLeft() {
      return this.$vuetify.breakpoint.mdAndDown ? 0 : this.miniNav ? 64 : 240
    },
    ...mapGetters(['maskLayer']),
  },

  watch: {
    $route(e) {
      if (e.path !== '/home') {
        this.addViewTags(e)
      }
    },
  },

  methods: {
    // 右键菜单
    showMenu(e, tag) {
      e.preventDefault()
      this.index = e.target.name
      this.tabMenu = false
      this.contextMenuItem = tag
      this.x = e.clientX
      this.y = e.clientY
      this.$nextTick(() => {
        this.tabMenu = true
      })
    },
    // 全屏
    fullscreen() {
      this.fullscreenIcon = screenfull.isFullscreen
        ? 'mdi-fullscreen'
        : 'mdi-fullscreen-exit'
      screenfull.toggle()
    },
    async jump() {
      // console.log('window.innerWidth', window.innerWidth)
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/Mo/getURL`,
        {
          mobileFlag: this.isMobile(),
        },
      )
      if (errorRaw) {
        return
      }
      // console.log(data)
      window.open(data)
    },
    async jumpToOrder() {
      // console.log('window.innerWidth', window.innerWidth)
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/workOrderSystem/getURL`,
      )
      if (errorRaw) {
        return
      }
      // console.log(data)
      window.open(data)
    },
    // isMobile() {
    //   const userAgentInfo = navigator.userAgent
    //   const mobileAgents = [
    //     'Mobile',
    //     'phone',
    //     'pad',
    //     'pod',
    //     'iPhone',
    //     'ios',
    //     'iPad',
    //     'Android',
    //     'BlackBerry',
    //     'IEMobile',
    //     'MQQBrowser',
    //     'JUC',
    //     'Fennec',
    //     'wOSBrowser',
    //     'webOS',
    //     'WebOS',
    //     'Harmony',
    //     'Opera Mini',
    //     'Windows Phone',
    //     'Symbian',
    //     'BrowserNG',
    //   ]
    //   const mobileFlag = mobileAgents.some((mobileAgent) => {
    //     return userAgentInfo.indexOf(mobileAgent) > 0
    //   })
    //   return mobileFlag
    // },
    isMobile() {
      return window.innerWidth <= 768
    },
    isNeed() {
      return !(this.$local.data.get('userInfo').userType == 4)
    },
    //标签拖拽排序,待添加
    // tagDrop() {
    //   const target = this.$refs.tags
    //   Sortable.create(target, {
    //     draggable: 'li',
    //     animation: 300,
    //   })
    // },
    //增加tag
    addViewTags(route) {
      if (route.name && !route.meta.fullpage) {
        this.$store.commit('pushViewTags', route)
        this.$store.commit('pushKeepLive', route.name)
      }
    },
    //高亮tag
    isActive(route) {
      return route.path === this.$route.path
    },
    //关闭tag
    closeSelectedTag(tag, autoPushLatestView = true) {
      this.$store.commit('removeViewTags', tag)
      this.$store.commit('removeIframeList', tag)
      this.$store.commit('removeKeepLive', tag.name)
      if (autoPushLatestView && this.isActive(tag)) {
        const latestView = this.tagList.slice(-1)[0]
        if (latestView) {
          this.$router.push(latestView)
        } else {
          this.$router.push('/home')
        }
      }
    },
    //TAB 关闭
    closeAllTabs() {
      if (this.$route.name !== 'HomeView') {
        this.$router.push({
          path: '/home',
        })
      }
      let tags = [...this.tagList]
      tags.forEach((tag) => {
        if (tag.name === 'HomeView') {
          return true
        } else {
          this.closeSelectedTag(tag, false)
        }
      })
    },
    //TAB 关闭其他
    closeOtherTabs() {
      const nowTag = this.contextMenuItem
      //判断是否当前路由，否的话跳转
      if (this.$route.path !== nowTag.path) {
        this.$router.push({
          path: nowTag.path,
          query: nowTag.query,
        })
      }
      let tags = [...this.tagList]
      tags.forEach((tag) => {
        if (tag.name === 'HomeView' || nowTag.path === tag.path) {
          return true
        } else {
          this.closeSelectedTag(tag, false)
        }
      })
    },
    // 刷新
    refresh(event, name = null) {
      const nowTag = this.contextMenuItem
      this.$store.commit('removeKeepLive', name ? name : nowTag.name)
      this.temp = Math.random(1000)
      setTimeout(() => {
        this.temp = ''
        this.$store.commit('pushKeepLive', name ? name : nowTag.name)
      }, 0)
    },
    async logout() {
      this.$store.commit('clearViewTags')
      this.$store.commit('clearKeepLive')
      await this.postAsync('/logout', {})
      localStorage.clear()
      this.$router.push('/login')
    },
    async loadUserInfo() {
      const { errorRaw, data } = await this.getAsync('/getUserInfo')
      if (errorRaw) {
        this.$dialog.message.error('用户信息加载失败，请尝试刷新或重新登录')
        return
      }
      this.$local.data.set('userInfo', data)
      this.nickName = data.nickName
      this.roleName = data.roleName
      if (data.passwordStatus == 2) {
        this.$router.push({ name: 'UserIndex' })
        return
      }
      this.resources = data.resources
    },
  },

  mounted() {
    this.activateMultipleDraggableDialogs()

    this.resources = this.$local.data.get('userInfo').resources
    this.nickName = this.$local.data.get('userInfo').nickName
    this.roleName = this.$local.data.get('userInfo').roleName

    if (this.$local.data.get('userInfo').passwordStatus == 2) {
      this.$router.push({ name: 'UserIndex' })
      return
    }
    if (this.$route.path !== '/home') {
      this.addViewTags(this.$route)
    }
  },
}
</script>

<style scoped>
#main-content {
  position: absolute;
  height: 85vh;
}
#mask {
  position: fixed;
  top: 100px; /* 距离顶部的距离 */
  right: 0;
  bottom: 0;
  z-index: 2;
}
@media print {
  .v-main {
    padding: 0 !important;
  }
}
</style>
