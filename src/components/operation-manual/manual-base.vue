<template>
  <div class="manual-container">
    <v-card>
      <v-card-title>
        <v-row align="center">
          <v-col cols="6">
            <v-text-field
              v-model="searchQuery"
              label="模糊搜索(名称/作者/备注)"
              prepend-icon="mdi-magnify"
              clearable
              @input="onInput1"
            ></v-text-field>
          </v-col>
          <v-col cols="6" class="text-right">
            <v-btn
              outlined
              tile
              color="success"
              class="mx-1"
              @click="showUploadDialog = true"
              v-permission="['操作手册:上传']"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              上传
            </v-btn>
            <v-btn
              outlined
              tile
              color="error"
              class="mx-1"
              @click="deleteSelectedManuals"
              :disabled="selected.length === 0"
              v-permission="['操作手册:删除']"
            >
              <v-icon left>mdi-delete-empty</v-icon>
              删除
            </v-btn>
          </v-col>
        </v-row>
      </v-card-title>

      <v-data-table
        :headers="headers"
        :items="manuals"
        :items-per-page="10"
        :loading="loading"
        v-model="selected"
        :show-select="true"
        class="elevation-1"
      >
        <template slot="item" slot-scope="{ item }">
          <tr>
            <td>
              <v-checkbox
                v-model="selected"
                :value="item"
                hide-details
              ></v-checkbox>
            </td>
            <td>
              <a @click="downloadManual(item)" class="manual-link">
                {{ item.name }}
              </a>
            </td>
            <td>{{ formatDate(item.updateDate) }}</td>
            <td>{{ item.author }}</td>
            <td>{{ item.remark }}</td>
            <td>
              <v-btn small text color="primary" @click="downloadManual(item)">
                下载
              </v-btn>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-card>

    <v-dialog v-model="showUploadDialog" max-width="500px">
      <v-card>
        <v-card-title>上传操作手册</v-card-title>
        <v-card-text>
          <v-text-field
            v-model="uploadForm.name"
            label="名称"
            :rules="[(v) => !!v || '请输入名称']"
          ></v-text-field>
          <v-textarea
            v-model="uploadForm.remark"
            label="备注"
            rows="3"
          ></v-textarea>
          <v-attach-list
            :attachments="this.uploadForm.fileId"
            @change="handleAttachmentChange"
            accept=".doc,.docx"
            title="操作手册文件"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="blue darken-1" text @click="closeUploadDialog">
            取消
          </v-btn>
          <v-btn color="blue darken-1" text @click="uploadManual">确认</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import VAttachList from '../v-attach-list.vue'

export default {
  name: 'manual-base',
  components: {
    VAttachList,
  },
  props: {
    manualType: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      searchQuery: '',
      loading: false,
      manuals: [],
      selected: [],
      headers: [
        {
          text: '名称',
          align: 'start',
          sortable: true,
          value: 'name',
        },
        { text: '更新日期', value: 'updateDate', sortable: true },
        { text: '作者', value: 'author', sortable: true },
        { text: '备注', value: 'remark' },
        { text: '操作', value: 'actions', sortable: false },
      ],
      showUploadDialog: false,
      uploadForm: {
        name: '',
        remark: '',
        fileId: null,
      },
      isManager: false,
    }
  },
  created() {
    this.fetchManuals()
  },
  methods: {
    fetchManuals() {
      //console.log('1111111111111111')
      this.loading = true
      this.getAsync('/operation-manuals/operationPage', {
        type: this.manualType,
      }).then((result) => {
        if (result.data) {
          if (result.data.records) {
            this.manuals = result.data.records || []
          } else {
            this.manuals = result.data || []
          }
        }
        this.selected = []
        this.loading = false
      })
    },
    onInput1(val) {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.getValue1(val)
      }, 500)
    },
    getValue1(val) {
      this.searchQuery = val
      this.searchManuals()
    },
    searchManuals() {
      this.loading = true
      this.getAsync('/operation-manuals/search', {
        type: this.manualType,
        query: this.searchQuery,
      }).then((result) => {
        if (result.data) {
          if (result.data.records) {
            this.manuals = result.data.records || []
          } else {
            this.manuals = result.data || []
          }
        }
        this.selected = []
        this.loading = false
      })
    },
    async downloadManual(manual) {
      const fileName = `${manual.name}.docx`
      await this.getBlobDownload(
        '/operation-manuals/download',
        { id: manual.id, type: this.manualType },
        fileName,
      )
    },
    handleAttachmentChange(fileIds) {
      this.uploadForm.fileId = fileIds[0]
    },
    closeUploadDialog() {
      this.showUploadDialog = false
      this.resetUploadForm()
    },
    resetUploadForm() {
      this.uploadForm = {
        name: '',
        remark: '',
        fileId: null,
      }
      this.handleAttachmentChange([])
    },
    async uploadManual() {
      if (!this.uploadForm.name || !this.uploadForm.fileId) {
        this.$dialog.message.error('请填写名称并上传文件')
        return
      }
      this.loading = true
      const { errorRaw } = await this.getAsync('/operation-manuals/upload', {
        name: this.uploadForm.name,
        remark: this.uploadForm.remark,
        type: this.manualType,
        fileId: this.uploadForm.fileId,
      })
      if (errorRaw) {
        this.loading = false
        return
      }
      this.$dialog.message.success('上传成功')
      this.showUploadDialog = false
      this.selected = []
      this.fetchManuals()
      this.loading = false
      this.resetUploadForm()
    },
    async deleteSelectedManuals() {
      if (this.selected.length === 0) return
      const ids = this.selected.map((item) => item.id)
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/operation-manuals/batch/delete',
        {
          idList: ids,
          type: this.manualType,
        },
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = []
      this.fetchManuals()
      this.loading = false
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
    },
  },
}
</script>

<style scoped>
.manual-container {
  padding: 16px;
}
.manual-link {
  color: #1976d2;
  text-decoration: none;
  cursor: pointer;
}
.manual-link:hover {
  text-decoration: underline;
}
</style>
