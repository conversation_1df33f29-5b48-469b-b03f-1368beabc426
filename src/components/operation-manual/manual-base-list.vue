<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="updateManual"
      :search-remain="searchObj"
      :single-select="true"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="showUploadDialog = true"
          v-permission="['操作手册:上传']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="updateManual"
          :disabled="!canUpdate"
          v-permission="['操作手册:更新']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          更新
        </v-btn>
        <v-btn
          outlined
          tile
          color="error"
          class="mx-1"
          @click="deleteSelectedManuals"
          :disabled="!hasSelection"
          v-permission="['操作手册:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.actions`]="{ item }">
        <v-btn small text color="primary" @click="downloadManual(item)">
          下载
        </v-btn>
      </template>
    </v-table-searchable>
    <v-dialog v-model="showUploadDialog" max-width="800px">
      <v-card>
        <v-card-title>上传操作手册</v-card-title>
        <v-card-text>
          <v-text-field
            v-model="detail.name"
            label="名称"
            :rules="[(v) => !!v || '请输入名称']"
          ></v-text-field>
          <v-textarea
            v-model="detail.remark"
            label="备注"
            rows="3"
          ></v-textarea>
          <v-row>
            <v-col cols="12">
              <v-alert type="info" color="green" text dense class="mb-0">
                更新文件时，请删除需要更新的文件并重新上传。
                仅支持上传Word，PPT，Excel，PDF
                格式；如需上传视频文件，请联系“李三刚”上传到培训模块。
              </v-alert>
            </v-col>
          </v-row>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="handleAttachmentChange"
            accept=".doc,.docx,.pdf,.pptx"
            title="操作手册上传"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="blue darken-1"
            text
            @click="closeUploadDialog"
            v-permission="['操作手册:取消更新']"
          >
            取消
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="uploadManual"
            v-permission="['操作手册:确认更新']"
          >
            确认
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import VAttachList from '@/components/v-attach-list.vue'

export default {
  components: { VAttachList },
  name: 'manual-base-list',
  props: {
    manualType: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
  },
  created() {
    this.tableName = this.title
    this.reqUrl = `/operation-manuals/operationPage?type=${this.manualType}`
    this.headers = [
      { text: '名称', value: 'name' },
      { text: '更新日期', value: 'updateDate', sortable: true },
      { text: '作者', value: 'author', sortable: true },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords' },
      //{ text: '操作', value: 'actions', sortable: false },
    ]
    this.fuzzyLabel = '模糊搜索(名称/作者/备注)'
    //this.pushParams = { name: 'manual-detail' }
  },

  data() {
    return {
      showUploadDialog: false,
      searchObj: {},
      detail: {
        id: '',
        name: '',
        remark: '',
        attachmentIds: [],
        attachmentRecords: [],
      },
      selected: [],
      loading: false,
      pushParams: null,
    }
  },

  computed: {
    hasSelection() {
      return Array.isArray(this.selected)
        ? this.selected.length > 0
        : !!this.selected
    },
    canUpdate() {
      return Array.isArray(this.selected)
        ? this.selected.length == 1
        : !!this.selected
    },
  },

  methods: {
    loadTableData() {
      if (this.$refs.table) {
        this.$refs.table.loadTableData()
      }
    },
    closeUploadDialog() {
      this.showUploadDialog = false
      this.detail = {
        name: '',
        remark: '',
        attachmentIds: [],
        attachmentRecords: [],
      }
      this.selected = false
    },
    handleAttachmentChange(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async uploadManual() {
      if (
        !this.detail.name ||
        !this.detail.attachmentIds ||
        (this.detail.attachmentIds && this.detail.attachmentIds.length == 0)
      ) {
        this.$dialog.message.error('请填写名称并上传文件')
        return
      }
      this.loading = true
      const reqUrl = this.isEdit
        ? '/operation-manuals/upload'
        : '/operation-manuals/upload'
      const { errorRaw } = await this.getAsync(reqUrl, {
        id: this.detail.id,
        name: this.detail.name,
        remark: this.detail.remark,
        type: this.manualType,
        attachmentIds: this.detail.attachmentIds,
      })
      this.loading = false
      if (errorRaw) {
        this.$dialog.message.error('上传失败，请重试')
        return
      }
      this.$dialog.message.success('上传成功')
      this.selected = false
      this.closeUploadDialog()
      this.loadTableData()
    },
    updateManual() {
      if (!this.canUpdate) return
      const selectedItem = Array.isArray(this.selected)
        ? this.selected[0]
        : this.selected
      console.log(this.selected)
      this.detail = {
        id: selectedItem.id,
        name: selectedItem.name,
        remark: selectedItem.remark || '',
        attachmentRecords: selectedItem.attachmentRecords,
        attachmentIds: selectedItem.attachmentIds,
      }
      this.isEdit = true
      this.showUploadDialog = true
    },
    async deleteSelectedManuals() {
      if (!this.hasSelection) return

      if (!(await this.$dialog.msgbox.confirm('确定删除选中的操作手册？')))
        return

      let ids = []
      if (Array.isArray(this.selected)) {
        ids = this.selected.map((item) => item.id)
      } else if (this.selected && this.selected.id) {
        ids = [this.selected.id]
      }

      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/operation-manuals/batch/delete',
        {
          idList: ids,
          type: this.manualType,
        },
      )
      this.loading = false

      if (errorRaw) {
        this.$dialog.message.error('删除失败，请重试')
        return
      }

      this.$dialog.message.success('删除成功')
      this.selected = Array.isArray(this.selected) ? [] : null
      this.loadTableData()
    },
    async downloadManual(manual) {
      const fileName = `${manual.name}_${manual.updateDate.substring(
        0,
        11,
      )}.docx`
      await this.getBlobDownload(
        '/operation-manuals/download',
        { id: manual.id, type: this.manualType },
        fileName,
      )
    },
  },
}
</script>

<style></style>
