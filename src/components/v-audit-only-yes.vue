<template>
  <v-card outlined>
    <v-card-title class="py-1">
      审批历史
      <v-spacer></v-spacer>
      <v-btn
        text
        :loading="loading"
        v-if="!img && !isShip"
        color="primary"
        @click="loadPic"
        class="d-print-none"
      >
        显示流程图
      </v-btn>
      <v-btn
        text
        v-else-if="!!img && !isShip"
        color="primary"
        @click="img = null"
      >
        隐藏流程图
      </v-btn>
    </v-card-title>
    <v-card-text class="pb-0">
      <v-img
        v-if="img"
        @click="dialog = true"
        max-height="500"
        max-width="1000"
        :src="img"
      ></v-img>
      <v-divider></v-divider>
      <v-data-table
        :headers="hisTaskHeaders"
        :items="hisTask"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
      >
        <template v-slot:[`item.status`]="{ item }">
          {{ statusMap[item.status] || '' }}
        </template>
      </v-data-table>
      <v-divider></v-divider>
    </v-card-text>
    <v-card-text
      v-if="auditParams.taskId && !auditParams.isReject"
      class="py-0"
    >
      <v-radio-group
        :rules="[rules.radio]"
        ref="radio"
        class="pb-0"
        v-model="adopt"
        row
      >
        审批意见：
        <v-radio label="同意" :value="true"></v-radio>
        <!-- <v-radio label="退回" :value="false"></v-radio> -->
      </v-radio-group>
      <v-textarea
        class="mt-1"
        ref="textarea"
        label="批注"
        outlined
        height="80"
        :rules="adopt ? [] : [rules.required]"
        v-model="comment"
      ></v-textarea>
    </v-card-text>
    <v-dialog v-model="dialog" max-width="80%" max-height="80%">
      <v-img :src="img" contain></v-img>
    </v-dialog>
  </v-card>
</template>
<script>
export default {
  name: 'v-audit-only-yes',
  props: {
    auditParams: {
      type: Object,
      default: () => ({
        taskId: '',
        processInstanceId: '',
      }),
    },
    showForm: {
      type: Boolean,
      default: true,
    },
  },
  inject: {
    form: { default: null },
  },
  data() {
    return {
      dialog: false,
      img: null,
      adopt: '',
      hisTask: [],
      comment: '',
      loading: false,
      rules: {
        radio: (v) => v !== '' || '请选择审批意见',
        required: (v) => !!v || '请输入批注',
      },
    }
  },
  watch: {
    dialog(val) {
      if (!val) {
        this.$nextTick(() => {
          this.$refs.image.$el.querySelector('img').style.maxHeight = null
        })
      }
    },
    img(val) {
      if (val)
        this.$nextTick(() => {
          this.$refs.image.$el.querySelector('img').style.maxHeight = '100%'
        })
    },
  },

  methods: {
    async loadHisTask() {
      const { data } = await this.getAsync(
        '/flow/task/getHisTaskInsListByProInsId',
        {
          processInstanceId: this.auditParams.processInstanceId,
        },
      )
      this.hisTask = data
    },
    arrayBufferToBase64(buffer) {
      let binary = ''
      let bytes = new Uint8Array(buffer)
      let len = bytes.byteLength
      for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
      }
      return `data:image/bmp;base64,${window.btoa(binary)}`
    },
    async loadPic() {
      this.loading = true
      const result = await this.getArrayBuffer(
        '/flow/diagram/getTracePicByProcessInstanceId',
        { processInstanceId: this.auditParams.processInstanceId },
      )
      this.img = this.arrayBufferToBase64(result.data)
      this.loading = false
    },

    validate(force, value) {
      if (!this.$refs.radio) return true
      if (this.$refs.radio.validate(force, value)) {
        return this.$refs.textarea.validate(force, value)
      }
      return false
    },

    // 供外部调用
    async submit() {
      if (!this.auditParams.taskId) return null
      if (this.auditParams.isReject) this.adopt = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/completeTaskAndCommentAndSetVar',
        {
          taskId: this.auditParams.taskId,
          comment: this.comment,
          adopt: this.adopt,
          params: {},
        },
      )
      return errorRaw
    },
  },

  created() {
    this.form && this.form.register(this)
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.hisTaskHeaders = [
      { text: '环节名称', value: 'activityName' },
      { text: '任务状态', value: 'status' },
      { text: '审批人', value: 'assignee' },
      { text: '审批时间', value: 'endTime' },
      { text: '审批意见', value: 'comment' },
    ]
    this.statusMap = {
      success: '审批通过',
      reject: '审批驳回',
    }
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
  mounted() {
    this.loadHisTask()
  },
}
</script>

<style></style>
