<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        船舶选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          :fuzzy-label="fuzzyLabel"
        >
          <template #searchflieds></template>
          <template #btns></template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'ship-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.tableName = '船舶列表'
    this.reqUrl = '/business/common/ship/detailInfo/page'
    this.headers = [
      { text: '中文船名', value: 'chShipName' },
      { text: '英文船名', value: 'enShipName' },
      { text: '船舶编号', value: 'shipCode' },
      { text: '船级社', value: 'classification' },
      { text: '船旗国', value: 'flagState' },
      { text: '付款公司', value: 'paymentCompany' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    ships: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {},
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    searchRemain(val) {
      this.searchObj = val
    },
    ships: {
      handler(val) {
        this.selected = val.map((i) => {
          return {
            ...i,
            money: 0,
            toUsd: 0,
            remark: '',
            vid: i.id,
            id: i.itemDetailId,
            chShipName: i.chShipName,
          }
        })
      },
      deep: true,
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const ships = this.selected.map((i) => {
        const comp = {
          ...i,
          money: 0,
          toUsd: 0,
          remark: '',
          chShipName: i.chShipName,
          itemDetailId: i.id,
          id: i.vid,
        }
        return comp
      })
      this.$emit('update:ships', ships)
      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
