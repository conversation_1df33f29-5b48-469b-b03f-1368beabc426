<template>
  <v-autocomplete
    ref="select"
    :dense="dense"
    v-model="val"
    :label="label"
    :items="list"
    :rules="rules"
    :loading="loading"
    :disabled="disabled"
    @change="change"
    item-text="dictLabel"
    item-value="dictValue"
    outlined
    :clearable="clearable"
  >
    <template v-if="type == '0'" #prepend-inner>
      <v-avatar color="primary" size="20">
        <span class="white--text">甲</span>
      </v-avatar>
    </template>
    <template v-else-if="type == '1'" #prepend-inner>
      <v-avatar color="indigo" size="20">
        <span class="white--text">轮</span>
      </v-avatar>
    </template>
  </v-autocomplete>
</template>

<script>
import { cacheGetDefault } from '@/util/cache'

export default {
  name: 'v-ship-station',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  props: {
    value: String,
    rules: Array,
    disabled: <PERSON>ole<PERSON>,
    // 0-甲板部 1-轮机部 3-全选，默认全选
    type: {
      type: [Number, String],
      default: 3,
    },
    // 是否使用船舶id，默认使用船舶编号
    useId: [Boolean, String],
    // 是否使用英文船名，默认使用中文船名
    useEnName: [Boolean, String],
    clearable: [Boolean, String],
    // 是否使用英文船名，默认使用中文船名
    dense: {
      type: Boolean,
      default: true,
    },
    label: {
      type: String,
      default: '船上岗位',
    },
  },
  data() {
    return {
      list: [],
      loading: false,
      val: null,
      hint: null,
    }
  },
  methods: {
    change(v) {
      this.$emit('update', v)
    },
    validate(force, value) {
      return this.$refs.select.validate(force, value)
    },
    reset() {
      this.$refs.select.reset()
    },
    resetValidation() {
      this.$refs.select.resetValidation()
    },

    async getStationList() {
      let that = this
      let data = await cacheGetDefault('ship-station', async () => {
        const { data, errorRaw } = await that.getAsync(
          '/business/crew/infra/positionList',
          {},
          false,
        )
        if (errorRaw) {
          that.$dialog.message.error('船上岗位列表获取失败，请重试')
          return null
        }
        if (data.length === 0) {
          that.$dialog.message.error('船上岗位为空，部分功能受损')
        }
        return data
      })
      if (this.type != 3)
        data = data.filter(
          (i) =>
            (this.type == '0' && i.dept === '甲板部') ||
            (this.type == '1' && i.type === '轮机部'),
        )
      if (this.useEnName) {
        this.list = data.map((item) => {
          return {
            dictValue: this.useId ? item.id : item.enName,
            dictLabel: item.enName,
          }
        })
      } else {
        this.list = data.map((item) => {
          return {
            dictValue: this.useId ? item.id : item.positionName,
            dictLabel: item.positionName,
          }
        })
      }
    },
  },
  watch: {
    value(val) {
      this.val = val
    },
  },
  created() {
    this.form && this.form.register(this)
  },
  async beforeMount() {
    this.loading = true
    await this.getStationList()
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    if (this.isShip) {
      const roles = this.$local.data.get('userInfo').roleName?.split(',')
      const station = this.list.find((i) => roles.includes(i.dictLabel))
      if (station) {
        this.useId
          ? this.$emit('update', station.dictValue)
          : this.$emit('update', station.dictLabel)
      }
    }
    this.loading = false
    this.val = this.value
    this.resetValidation()
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>

<style scoped></style>
