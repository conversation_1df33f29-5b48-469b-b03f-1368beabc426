<template>
  <div
    class="grid-container"
    :style="{ height: isFullScreen ? '100vh' : currentHeight + 'px' }"
  >
    <div class="grid-toolbar">
      <span>报价明细</span>
      <el-button
        class="action-btn"
        :class="{ 'is-fullscreen': isFullScreen }"
        @click="toggleFullScreen"
      >
        <i class="el-icon-full-screen action-icon"></i>
        {{ isFullScreen ? '退出全屏' : '全屏显示' }}
      </el-button>
      <el-button class="action-btn" @click="resetColumns">
        <i class="el-icon-refresh-right action-icon"></i>
        显示所有列
      </el-button>
    </div>
    <AgGridVue
      style="width: 100%; height: calc(100% - 40px)"
      :class="theme"
      :columnDefs="columnDefs"
      :rowData="rowData"
      :gridOptions="mergedGridOptions"
      :context="mergedContext"
      @grid-ready="onGridReady"
      @cell-value-changed="onCellValueChanged"
    />
    <div
      v-if="!isFullScreen"
      class="resize-handle"
      @mousedown="startResize"
      title="拖动调整高度"
    ></div>
  </div>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue'
import { AG_GRID_LOCALE_CN } from '@ag-grid-community/locale'
import GridSetter from '@/components/modules/MyAgGrid/components/Setter.vue'

export default {
  name: 'MyAgGrid',
  components: {
    AgGridVue,
    // eslint-disable-next-line vue/no-unused-components
    GridSetter,
  },
  props: {
    theme: {
      type: String,
      default: 'ag-theme-alpine',
    },
    columnDefs: {
      type: Array,
      default: () => [],
    },
    rowData: {
      type: Array,
      default: () => [],
    },
    gridOptions: {
      type: Object,
      default: () => ({}),
    },
    context: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      gridApi: null,
      columnApi: null,
      currentHeight: 600, // 默认高度
      defaultGridOptions: {
        tooltipShowDelay: 1000,
        localeText: AG_GRID_LOCALE_CN,
        suppressScrollOnNewData: true,
        maintainRowOrder: true,
        suppressHorizontalScroll: false,
        suppressAnimationFrame: false,
        maintainScrollPosition: true,
      },
      defaultColumnDefs: [
        {
          headerName: '操作',
          width: 100,
          field: 'setter',
          pinned: 'right',
          cellRenderer: 'GridSetter',
          sortable: false,
          filter: false,
          cellRendererParams: {
            actionHandler: this.handleAction,
            actionDelete: this.handleDelete,
          },
        },
      ],
      initialColumnDefs: null,
      isFullScreen: false,
      previousHeight: null, // 存储全屏前的高度
    }
  },
  computed: {
    mergedGridOptions() {
      return { ...this.defaultGridOptions, ...this.gridOptions }
    },
    mergedColumnDefs() {
      return [...this.defaultColumnDefs, ...this.columnDefs]
    },
    mergedContext() {
      return {
        componentParent: this,
        ...this.context,
      }
    },
  },
  methods: {
    onGridReady(params) {
      this.gridApi = params.api
      this.columnApi = params.columnApi
      this.initialColumnDefs = [...this.mergedColumnDefs]
      this.gridApi.sizeColumnsToFit()
    },
    toggleFullScreen() {
      if (!this.isFullScreen) {
        this.previousHeight = this.currentHeight // 保存当前高度
        const element = this.$el
        if (element.requestFullscreen) {
          element.requestFullscreen()
        } else if (element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen()
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen()
        }
        this.isFullScreen = true
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
        this.isFullScreen = false
        if (this.previousHeight) {
          this.currentHeight = this.previousHeight // 恢复之前的高度
        }
      }
    },
    resetColumns() {
      if (this.columnApi && this.initialColumnDefs) {
        const defaultState = this.initialColumnDefs.map((col) => ({
          colId: col.field,
          hide: false,
          width: col.width,
          flex: col.flex,
        }))

        this.columnApi.applyColumnState({
          state: defaultState,
          defaultState: { hide: false },
        })

        // 重置列顺序
        const defaultOrder = this.initialColumnDefs.map((col) => col.field)
        this.columnApi.moveColumns(defaultOrder)

        if (this.gridApi) {
          this.gridApi.sizeColumnsToFit()
        }
        //this.currentHeight = 500
      }
      console.log(this.currentHeight)
    },
    handleAction(data) {
      this.$emit('handleAction', data)
    },
    handleDelete(data) {
      this.$emit('handleDelete', data)
    },
    startResize(event) {
      if (this.isFullScreen) return // 全屏时不允许调整高度

      event.preventDefault()
      const startY = event.clientY
      const startHeight = this.currentHeight

      const doDrag = (e) => {
        const delta = e.clientY - startY
        this.currentHeight = Math.max(300, startHeight + delta) // 最小高度200px
      }

      const stopDrag = () => {
        document.removeEventListener('mousemove', doDrag)
        document.removeEventListener('mouseup', stopDrag)
      }

      document.addEventListener('mousemove', doDrag)
      document.addEventListener('mouseup', stopDrag)
    },
    onCellValueChanged(params) {
      // 触发单元格值变化事件
      this.$emit('cell-value-changed', params)
    },
  },
  watch: {
    rowData: {
      handler() {
        this.$nextTick(() => {
          if (this.gridApi) {
            this.gridApi.sizeColumnsToFit()
          }
        })
      },
      deep: true,
    },
  },
  mounted() {
    // 监听全屏变化
    const updateFullScreenState = () => {
      const isFullScreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      )
      this.isFullScreen = isFullScreen
    }

    document.addEventListener('fullscreenchange', updateFullScreenState)
    document.addEventListener('webkitfullscreenchange', updateFullScreenState)
    document.addEventListener('msfullscreenchange', updateFullScreenState)
  },
  beforeDestroy() {
    const updateFullScreenState = () => {}
    document.removeEventListener('fullscreenchange', updateFullScreenState)
    document.removeEventListener(
      'webkitfullscreenchange',
      updateFullScreenState,
    )
    document.removeEventListener('msfullscreenchange', updateFullScreenState)
  },
}
</script>

<style lang="scss" scoped>
.grid-container {
  position: relative;
  min-height: 300px;
  width: 100%;
  transition: height 0.3s ease;
  display: flex;
  flex-direction: column;

  &:fullscreen {
    height: 100vh !important;
    padding: 20px;
    background: white;
  }
}

.grid-toolbar {
  padding: 8px;
  background: linear-gradient(to right, #f5f7fa, #e4e7ed);
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: none;
  font-weight: 500;
  background-color: #3399cc;
  color: white;

  .action-icon {
    font-size: 18px;
    transition: all 0.3s ease;
  }

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

    .action-icon {
      animation: pulse 1s infinite;
    }
  }

  &.is-fullscreen {
    background-color: #ff5252 !important;

    &:hover {
      background-color: #ff1744 !important;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.resize-handle {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: transparent;
  cursor: row-resize;

  &:hover {
    background: rgba(0, 0, 0, 0.1);
  }

  &::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 2px;
    background: #ddd;
    border-radius: 1px;
  }
}

.grid-container * {
  user-select: none;
}
.grid-container.resizing {
  cursor: row-resize;
}

.ag-theme-quartz,
.ag-theme-alpine {
  .ag-header-cell-menu-button {
    opacity: 1;
  }

  .ag-header-cell-label {
    width: 100%;
  }

  .ag-header-icon {
    opacity: 1;
  }
}
.scroll-container {
  overflow-x: auto; /* 启用水平滚动 */
  padding-bottom: 100px; /* 为滚动条留出空间 */
}

/* 修改滚动条样式 */
:deep(.ag-body-horizontal-scroll::-webkit-scrollbar),
:deep(.ag-body-vertical-scroll::-webkit-scrollbar) {
  width: 26px; /* 增加垂直滚动条宽度 */
  height: 26px; /* 增加水平滚动条高度 */
}

:deep(.ag-body-horizontal-scroll::-webkit-scrollbar-thumb),
:deep(.ag-body-vertical-scroll::-webkit-scrollbar-thumb) {
  background-color: #909399; /* 滚动条颜色 */
  border-radius: 8px; /* 增加圆角 */
  border: 1px solid #f4f4f4; /* 减小边框宽度，让滚动条看起来更粗 */
  min-height: 40px; /* 设置最小高度，确保滚动条块足够大 */
}

:deep(.ag-body-horizontal-scroll::-webkit-scrollbar-track),
:deep(.ag-body-vertical-scroll::-webkit-scrollbar-track) {
  background-color: #f4f4f4; /* 滚动条轨道颜色 */
  border-radius: 8px; /* 增加轨道圆角 */
}

/* 修改 AgGridVue 的样式 */
:deep(.ag-root-wrapper) {
  flex: 1;
  min-height: 0;
  border: 1px solid #ddd;
}

:deep(.ag-root) {
  height: 100% !important;
}

:deep(.ag-body-viewport) {
  overflow: auto !important;
  min-height: 100px;
}

/* 修改滚动条容器样式 */
:deep(.ag-body-vertical-scroll-viewport) {
  overflow-y: scroll !important;
  max-height: 100% !important;
}

:deep(.ag-body-horizontal-scroll-viewport) {
  overflow-x: scroll !important;
  width: 100% !important;
}

/* 确保滚动条完全显示 */
:deep(.ag-body-horizontal-scroll),
:deep(.ag-body-vertical-scroll) {
  width: auto !important;
  height: auto !important;
  position: absolute;
  bottom: 0;
  right: 0;
}
</style>
