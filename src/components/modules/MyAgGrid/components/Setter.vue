<template>
  <div class="setter">
    <el-tooltip
      class="item"
      effect="light"
      content="删除"
      placement="bottom-start"
    >
      <i class="el-icon-delete" @click="delRow"></i>
    </el-tooltip>
    <el-tooltip
      class="item"
      effect="light"
      content="编辑"
      placement="bottom-start"
    >
      <i class="el-icon-document" @click="setRow"></i>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'GridSetter',
  methods: {
    //图标事件
    setRow() {
      this.params.actionHandler(this.params.data)
    },
    //删除数据
    delRow() {
      this.params.actionDelete(this.params.data)
    },
  },
}
</script>
