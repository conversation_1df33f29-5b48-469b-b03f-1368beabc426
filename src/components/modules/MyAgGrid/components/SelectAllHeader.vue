<template>
  <div style="display: flex; align-items: center; height: 100%; padding: 0 5px">
    <span style="margin-right: 8px">一键定标</span>
    <input
      type="checkbox"
      :disabled="!params.canEdit3"
      :checked="isAllSelected"
      @click="onCheckboxChange"
      style="width: 16px; height: 16px"
    />
  </div>
</template>

<script>
export default {
  name: 'SelectAllHeader',
  data() {
    return {
      isAllSelected: false,
      params: null,
    }
  },
  beforeMount() {
    this.params = this.params || {}
  },
  methods: {
    onCheckboxChange(event) {
      event.preventDefault()
      event.stopPropagation()
      const newState = !this.isAllSelected
      this.isAllSelected = newState
      if (this.params.onHeaderChange) {
        this.params.onHeaderChange(newState, this.params.quote)
      }
    },
    init(params) {
      this.params = params
      if (
        params.quote &&
        params.quote.detailList &&
        Array.isArray(params.quote.detailList)
      ) {
        this.isAllSelected = params.quote.detailList
          .filter((detail) => detail.finalPrice != 0 && detail.quotNum != 0)
          .every((detail) => detail.isWins)
      } else {
        this.isAllSelected = false
      }
    },
  },
  watch: {
    'params.quote.detailList': {
      deep: true,
      immediate: true,
      handler(newVal) {
        if (newVal && Array.isArray(newVal)) {
          this.isAllSelected = newVal
            .filter((detail) => detail.finalPrice != 0 && detail.quotNum != 0)
            .every((detail) => detail.isWins)
        } else {
          this.isAllSelected = false
        }
      },
    },
  },
}
</script>

<style scoped>
input[type='checkbox'] {
  cursor: pointer;
}
input[type='checkbox']:disabled {
  cursor: not-allowed;
}

:deep(.ag-header-cell-text) {
  display: block;
  height: 100%;
}

:deep(.ag-header-cell-text div) {
  line-height: 20px;
}
</style>
