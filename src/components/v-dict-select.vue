<template>
  <v-autocomplete
    ref="select"
    dense
    v-model="val"
    :label="label"
    :items="list"
    :rules="rules"
    :loading="loading"
    :disabled="disabled || hint !== null"
    :readonly="readonly"
    :hint="hint"
    :multiple="multiple"
    @change="change"
    item-text="dictLabel"
    item-value="dictValue"
    outlined
    :clearable="!readonly"
  ></v-autocomplete>
</template>

<script>
import dictHelper from '@/mixin/dictHelper'

export default {
  name: 'v-dict-select',
  mixins: [dictHelper],
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  props: {
    value: [String, Number],
    rules: Array,
    label: String,
    // 多选
    multiple: [Boolean, String],
    disabled: [Boolean, String],
    readonly: [Boolean, String],
    dictType: String,
  },
  data() {
    return {
      list: [],
      loading: false,
      val: null,
      hint: null,
    }
  },
  methods: {
    change(v) {
      this.$emit('update', v)
    },
    validate(force, value) {
      return this.$refs.select.validate(force, value)
    },
    reset() {
      this.$refs.select.reset()
    },
    resetValidation() {
      this.$refs.select.resetValidation()
    },
  },
  watch: {
    value(val) {
      this.val = val
    },
  },
  created() {
    this.form && this.form.register(this)
  },
  async beforeMount() {
    this.loading = true
    this.list = await this.getDictByType(this.dictType)
    this.loading = false
    this.val = this.value
    this.resetValidation()
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>

<style scoped></style>
