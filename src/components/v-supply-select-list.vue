<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    label="供应商"
    :headers="headers"
    item-text="name"
    item-value="id"
    :req-url="reqUrl"
    :search-remain="searchObj"
    @select="
      (item) => {
        $emit('select', item)
      }
    "
    max-width="1300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
    :rules="rules"
    :clearable="clearable"
    @clear="clearSup"
    fuzzy-label="供应商名称/SAP编号"
  >
    <!-- <template v-slot:[`item.name`]="{ item }">
      {{ item.supplierOutputDTO.name }}
    </template>
    <template v-slot:[`item.nameEn`]="{ item }">
      {{ item.supplierOutputDTO.nameEn }}
    </template>
    <template v-slot:[`item.sapCode`]="{ item }">
      {{ item.supplierOutputDTO.sapCode }}
    </template>
    <template v-slot:[`item.account`]="{ item }">
      {{ item.supplierOutputDTO.account }}
    </template> -->
  </v-dialog-select>
</template>
<script>
export default {
  name: 'v-supply-select-list',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/business/shipAffairs/Supplier/list'
    this.headers = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      // { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
      // { text: '赊销期', value: 'creditDate' },
      // { text: '起始时间', value: 'beginDate' },
      // { text: '结束时间', value: 'endDate' },
    ]
    this.equipmentStatuses = [
      { text: '使用', value: 1 },
      { text: '停用', value: 2 },
      { text: '报废', value: 3 },
    ]
    this.euipmentTypes = [
      { text: '主机', value: '0' },
      { text: '副机', value: '1' },
      { text: '辅助设备', value: '2' },
      { text: '通导设备', value: '3' },
    ]
    this.filterFunc = (items) =>
      items.map((item) => ({
        ...item.supplierPurchaserOutputDTO,
        ...item.supplierOutputDTO,
        currency: item.supplierBankListOutputDTO,
      }))
    this.form && this.form.register(this)
  },
  props: {
    // 仅可清空，不得对该值进行其它变动
    value: [String, Object],
    // 初始化时，如果有默认值，仅触发一次，value值自行控制
    initSelected: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: [Boolean, String],
      default: false,
    },
    readonly: {
      type: [Boolean, String],
      default: false,
    },
    rules: Array,
    paymentCompany: {
      type: String,
      default: '',
    },
    currency: {
      type: Array,
      default: () => [],
    },
    clearable: {
      type: [Boolean, String],
      default: false,
    },
  },
  data() {
    return {
      searchObj: {},
      loading: false,
      supItems: [],
      text: '',
      supplierName: '',
      selected: false,
      dialog: false,
      val: '',
    }
  },

  computed: {
    supHeaders() {
      return [
        {
          text: '供应商名称',
          value: 'name',
          filter: (value) => {
            if (!value) return true
            return value.toLowerCase().includes(this.supplierName.toLowerCase())
          },
        },
        { text: '英文名称', value: 'nameEn' },
        { text: '账号', value: 'account' },
        { text: 'sap代码', value: 'sapCode' },
        { text: '评级打分', value: 'score' },
      ]
    },
  },

  watch: {
    value(val) {
      this.val = val
    },
    val(val) {
      this.$emit('update', val)
    },
    initSelected(val) {
      if (val) {
        this.selected = this.initSelected
        this.text = this.initSelected.name
      }
    },
    paymentCompany(val) {
      // if (val) this.loadSups(this.supplierName)
      this.searchObj.paymentCompany = val
    },
  },

  methods: {
    async loadSups(supplierName) {
      this.loading = true
      const { data } = await this.getAsync(
        '/business/shipAffairs/Supplier/getSupplierMessageByType',
        { supplierName },
      )
      this.supItems = data.map((item) => ({
        ...item.supplierOutputDTO,
        currency: item.supplierBankListOutputDTO,
      }))
      if (this.selected)
        this.$emit(
          'update:currency',
          this.supItems.find((item) => item.id === this.selected.id).currency,
        )
      this.loading = false
    },

    confirm() {
      this.text = this.selected.name
      this.$emit('update', this.selected.id)
      this.$emit('update:currency', this.selected.currency)
      this.dialog = false
    },

    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },

    clearSup() {
      this.$emit('update', '')
    },
  },

  async mounted() {
    // if (this.initSelected) {
    //   this.selected = this.initSelected
    //   this.$emit('update', this.selected.id)
    //   this.$emit('update:currency', this.selected.currency)
    // }
    this.searchObj.paymentCompany = this.paymentCompany
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
