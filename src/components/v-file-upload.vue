<template>
  <v-row>
    <v-col sm="10">
      <v-file-input
        :accept="accept"
        v-model="files"
        color="primary"
        counter
        :label="label"
        multiple
        placeholder="选择文件"
        prepend-icon="mdi-paperclip"
        :show-size="1000"
        :disabled="disabled || finished"
      >
        <template v-slot:selection="{ index, text }">
          <v-chip v-if="index < 2" color="primary accent-4" dark label small>
            {{ text }}
          </v-chip>

          <span
            v-else-if="index === 2"
            class="text-overline grey--text text--darken-3 mx-2"
          >
            +{{ files.length - 2 }} File(s)
          </span>
        </template>
      </v-file-input>
    </v-col>
    <v-col sm="2">
      <v-progress-circular
        v-if="loading"
        size="24"
        color="info"
        indeterminate
      ></v-progress-circular>
      <v-btn
        color="primary"
        @click="upload"
        v-else
        :disabled="disabled || finished"
      >
        <v-icon left>mdi-cloud</v-icon>
        上传
      </v-btn>
    </v-col>
  </v-row>
</template>
<script>
export default {
  name: 'v-file-upload',
  model: {
    prop: 'attachments',
    event: 'update',
  },
  data() {
    return {
      files: [],
      finished: false,
      loading: false,
    }
  },
  computed: {
    disabled() {
      return this.files.length >= this.maxFiles
    },
  },
  props: {
    attachments: {
      type: Array,
      default: () => [],
    },
    label: {
      type: String,
      default: '附件上传',
    },
    accept: {
      type: String,
    },
    maxFiles: {
      type: Number,
      default: 1,
    },
    rules: Array,
  },

  methods: {
    async upload() {
      this.loading = true
      const formData = new FormData()
      for (let i = 0; i < this.files.length; i++) {
        formData.append('files', this.files[i])
      }
      const { errorRaw, data } = await this.postAsync(
        '/system/file/uploadFiles',
        formData,
      )
      if (errorRaw) {
        return
      } else {
        this.$emit(
          'update',
          data.map((item) => item.id),
        )
      }
      this.loading = false
      this.finished = true
    },
  },

  mounted() {},
}
</script>

<style></style>
