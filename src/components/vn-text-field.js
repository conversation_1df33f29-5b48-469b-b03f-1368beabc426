// money-text-field.js
import { VTextField } from 'vuetify/lib'

const formatterEn = new Intl.NumberFormat('en-US')

export default {
  name: 'vn-text-field',
  extends: VTextField,
  data() {
    return {
      inputValue: null,
    }
  },
  props: {
    blurredFormat: {
      type: Function,
      default: (v) => {
        // Format and remove the currency symbol - use prefix="€" attribute for cleaner look
        if (v) return formatterEn.format(v)
      },
    },
  },
  methods: {
    showValue() {
      if (!this.isFocused) {
        // Store the value before change
        this.inputValue = this.lazyValue
        this.lazyValue = this.blurredFormat(this.lazyValue)
      } else {
        // Show unformatted value on focus
        this.lazyValue = this.inputValue
      }
    },
  },
  watch: {
    isFocused() {
      this.showValue()
    },
    value() {
      // Handle v-model updates
      if (!this.isFocused) {
        this.showValue()
      }
    },
  },
  mounted() {
    this.showValue()
  },
}
