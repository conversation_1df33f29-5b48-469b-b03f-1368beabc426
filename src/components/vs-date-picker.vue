<template>
  <div>
    <v-menu
      v-if="!readonly"
      v-model="menu"
      :nudge-left="40"
      transition="scale-transition"
      offset-y
      min-width="auto"
      :close-on-content-click="false"
    >
      <template v-slot:activator="{ on, attrs }">
        <v-text-field
          ref="date"
          v-model="val"
          readonly
          :label="label"
          :prepend-inner-icon="prependIcon"
          :dense="dense"
          v-bind="attrs"
          :disabled="disabled"
          v-on="on"
          :rules="rules"
          @change="change"
          :outlined="outlined"
        ></v-text-field>
      </template>
      <vc-date-picker
        @dayclick="menu = false"
        mode="date"
        v-model="val"
        :model-config="{
          type: 'string',
          mask: 'YYYY-MM-DD',
        }"
        :masks="{ inputDate: 'YYYY-MM-DD' }"
        :min-date="minDate"
        :max-date="maxDate"
      ></vc-date-picker>
    </v-menu>
    <v-text-field
      v-else
      ref="date"
      v-model="val"
      readonly
      :label="label"
      :prepend-inner-icon="prependIcon"
      :dense="dense"
      :disabled="disabled"
      :rules="rules"
      @change="change"
      :outlined="outlined"
    ></v-text-field>
  </div>
</template>
<script>
export default {
  name: 'vs-date-time-picker',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  data() {
    return {
      menu: false,
      val: null,
    }
  },

  watch: {
    value(val) {
      this.val = val
    },
    val(val) {
      this.$emit('update', val)
    },
  },

  props: {
    value: String,
    outlined: [Boolean, String],
    useToday: [Boolean, String],
    rules: Array,
    label: String,
    disabled: [Boolean, String],
    readonly: [Boolean, String],
    dense: [Boolean, String],
    prependIcon: {
      type: String,
      default: 'mdi-calendar-clock',
    },
    minDate: String,
    maxDate: String,
  },

  methods: {
    change() {
      this.menu = false
      this.$emit('update', this.val)
    },
    validate(force, value) {
      return this.$refs.date.validate(force, value)
    },
    reset() {
      this.$refs.date.reset()
    },
    resetValidation() {
      this.$refs.date.resetValidation()
    },
  },

  created() {
    this.form && this.form.register(this)
    if (this.useToday) {
      this.val = new Date(Date.now()).toISOString().substr(0, 10)
    } else {
      this.val = this.value
    }
  },

  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>

<style></style>
