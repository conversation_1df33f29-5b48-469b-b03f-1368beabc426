<template>
  <div class="loading-container" v-if="loading">
    <div class="loading-text">
      <span>S</span>
      <span>I</span>
      <span>T</span>
      <span>C</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingAnimation',
  props: {
    loading: {
      type: Boolean,
      default: true,
    },
  },
}
</script>

<style scoped>
/* 背景渐变效果 */
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  pointer-events: none; /* 允许点击穿透 */
  background: rgb(51, 153, 204); /* 背景色 */
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.2); /* 强化阴影 */
  width: 350px; /* 固定宽度 */
}

/* 使用渐变过渡的文本 */
.loading-text {
  display: flex;
  gap: 20px;
  font-family: 'Impact', sans-serif; /* 更酷的字体 */
  font-weight: bold;
  font-size: 50px;
  letter-spacing: 8px; /* 增加字母间距 */
  color: #ffffff;
  text-transform: uppercase;
  background: linear-gradient(45deg, #ff6a00, #ee0979); /* 渐变效果 */
  -webkit-background-clip: text; /* 让背景应用到文字 */
  background-clip: text;
}

/* 动画效果 */
.loading-text span {
  opacity: 0;
  animation: slideIn 2s ease-in-out infinite, pulse 1.5s ease-in-out infinite; /* 增加脉动效果 */
  text-shadow: 5px 5px 8px rgba(0, 0, 0, 0.5); /* 强化文字阴影 */
  transform: skew(-10deg); /* 让文字倾斜 */
}

/* 每个字母的动画延时，保持节奏感 */
.loading-text span:nth-child(1) {
  animation-delay: 0s, 0.2s;
}

.loading-text span:nth-child(2) {
  animation-delay: 0.2s, 0.4s;
}

.loading-text span:nth-child(3) {
  animation-delay: 0.4s, 0.6s;
}

.loading-text span:nth-child(4) {
  animation-delay: 0.6s, 0.8s;
}

/* 定义滑入动画 */
@keyframes slideIn {
  0% {
    transform: translateY(-60px) skew(-10deg); /* 维持倾斜效果 */
    opacity: 0;
  }
  40% {
    transform: translateY(0) skew(-10deg);
    opacity: 1;
  }
  60% {
    transform: translateY(0) skew(-10deg);
    opacity: 1;
  }
  100% {
    transform: translateY(60px) skew(-10deg);
    opacity: 0;
  }
}

/* 定义脉动效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2); /* 放大效果 */
  }
  100% {
    transform: scale(1);
  }
}
</style>
