<template>
  <v-dialog attach="#mask" hide-overlay v-model="dialog" :max-width="maxWidth">
    <template v-slot:activator="{ on, attrs }">
      <v-text-field
        v-if="!readonly"
        ref="textfield"
        v-model="text"
        append-icon="mdi-chevron-down"
        @click:clear="$emit('clear')"
        readonly
        :outlined="outlined"
        v-bind="attrs"
        v-on="on"
        :loading="loading"
        :dense="dense"
        :disabled="disabled"
        :label="label"
        :rules="rules"
        :clearable="clearable"
      ></v-text-field>
      <v-text-field
        v-else
        ref="textfield"
        v-model="text"
        append-icon="mdi-chevron-down"
        readonly
        :outlined="outlined"
        :loading="loading"
        :dense="dense"
        :disabled="disabled"
        :label="label"
        :rules="rules"
        :clearable="clearable"
      ></v-text-field>
    </template>
    <v-card>
      <v-card-text>
        <v-card-title>{{ tableName }}</v-card-title>
        <v-table-searchable
          ref="table"
          table-name=""
          :search-dicts="searchDicts"
          :search-date="searchDate"
          v-model="selected"
          :fuzzy-label="fuzzyLabel"
          :fixed-header="false"
          :headers="headers"
          :req-url="reqUrl"
          :search-remain="searchRemain"
          :item-key="itemValue"
          :filter-func="filterFunc"
          outlined
        >
          <template #searchflieds>
            <slot name="searchflieds">
              <!-- 剩余参数插槽 -->
            </slot>
          </template>
          <template v-for="h in headers" v-slot:[`item.${h.value}`]="{ item }">
            <slot :item="item" :name="`item.${h.value}`"></slot>
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="dialog = false">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
export default {
  name: 'v-dialog-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  props: {
    // 仅可清空，不得对该值进行其它变动
    value: [String, Object],
    // 初始化时，如果有默认值，仅触发一次，value值自行控制
    initSelected: Object,
    dense: {
      type: [Boolean, String],
      default: true,
    },
    outlined: {
      type: [Boolean, String],
      default: true,
    },
    disabled: {
      type: [Boolean, String],
      default: false,
    },
    readonly: {
      type: [Boolean, String],
      default: false,
    },
    clearable: {
      type: [Boolean, String],
      default: false,
    },
    rules: Array,
    label: {
      type: String,
      default: '',
    },
    itemText: String,
    itemValue: String,
    // 表格部分
    tableName: {
      type: String,
      default: '请选择',
    },
    fuzzyLabel: String,
    searchDicts: {
      type: Array,
      default: () => [],
    },
    searchDate: Object,
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    headers: {
      type: Array,
      required: true,
    },
    reqUrl: {
      type: String,
      // required: true,
    },
    maxWidth: {
      type: [String, Number],
      default: 1100,
    },
    filterFunc: Function,
  },
  data() {
    return {
      dialog: false,
      loading: false,
      val: null,
      selected: false,
      text: '',
    }
  },

  watch: {
    value(val) {
      if (!val) {
        // 理论该值不会发生变化，只会清空
        this.selected = false
        this.text = ''
      }
    },
    initSelected(val) {
      if (val) {
        this.selected = this.initSelected
        this.text = this.initSelected[this.itemText]
      }
    },
    dialog(val) {
      if (val) this.$emit('open')
    },
  },

  methods: {
    confirm() {
      if (this.itemValue) {
        this.val = this.selected[this.itemValue]
      } else {
        this.val = this.selected
      }
      this.text = this.selected[this.itemText]
      this.$emit('update', this.val)
      this.$emit('select', this.selected)
      this.dialog = false
    },

    validate(force, value) {
      return this.$refs.textfield.validate(force, value)
    },
    reset() {
      this.$refs.textfield.reset()
    },
    resetValidation() {
      this.$refs.textfield.resetValidation()
    },
  },

  created() {
    this.form && this.form.register(this)
    if (this.initSelected) {
      this.selected = this.initSelected
      this.text = this.initSelected[this.itemText]
    }
  },
  beforeMount() {
    // this.resetValidation()
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
