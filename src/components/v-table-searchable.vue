<template>
  <v-card :class="disabled ? 'disable-interaction' : ''" :outlined="outlined">
    <v-card-title class="py-1 mb-4">
      <span v-if="showTableName">{{ tableName }}</span>
      <v-btn
        v-if="showReloadBtn"
        outlined
        tile
        color="primary"
        class="mx-1"
        @click.stop="loadTableData"
      >
        <v-icon left>mdi-cached</v-icon>
        列表刷新
      </v-btn>
      <v-btn
        v-if="showExportButton"
        outlined
        tile
        color="green"
        class="mx-1"
        @click="exportTableData"
      >
        <v-icon left>mdi-cached</v-icon>
        导出当前数据
      </v-btn>
      <v-spacer></v-spacer>
      <slot name="btns">
        <!-- 右侧按钮组 -->
      </slot>
    </v-card-title>
    <v-card-text class="py-1 mb-2">
      <v-row class="no-padding-y">
        <!-- <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          @click.stop="loadTableData"
        >
          <v-icon left>mdi-cached</v-icon>
          刷新
        </v-btn> -->
        <!-- <v-col v-if="!outlined && useSearch" cols="12" sm="6" md="1">
          <v-btn
            outlined
            tile
            color="primary"
            class="mx-1"
            @click.stop="loadTableData"
          >
            <v-icon left>mdi-magnify</v-icon>
            搜索
          </v-btn>
        </v-col> -->
        <v-col v-if="useSeries" cols="12" sm="6" md="2">
          <v-dict-select
            clearable
            v-model="seriesShip"
            label="船舶系列"
            dense
            outlined
            dict-type="series_ship"
          ></v-dict-select>
        </v-col>
        <v-col v-if="useShip" cols="12" sm="6" md="2">
          <v-ship-select :use-id="useShipId" v-model="ship"></v-ship-select>
        </v-col>
        <v-col v-if="useStatus" cols="12" sm="6" md="2">
          <v-select
            v-model="status"
            outlined
            label="审批状态"
            dense
            :items="statusMap"
            clearable
          ></v-select>
        </v-col>
        <slot name="searchflieds">
          <!-- 剩余参数插槽 -->
        </slot>
        <v-col
          cols="12"
          sm="6"
          md="2"
          v-for="(dic, i) in searchDicts"
          :key="dic.dicType"
        >
          <v-dict-select
            :dictType="dic.dicType"
            :label="dic.label"
            v-model="dictValues[i]"
          ></v-dict-select>
        </v-col>
        <v-col v-if="searchDate && searchDate.interval" cols="12" sm="6" md="4">
          <v-menu
            v-model="datesMenu"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                ref="dates"
                :value="dateRangeText"
                :label="searchDate.label || '时间范围'"
                append-icon="mdi-calendar"
                outlined
                dense
                readonly
                clearable
                @click:clear="dates = []"
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <vc-date-picker
              v-model="dates"
              mode="date"
              is-range
            ></vc-date-picker>
          </v-menu>
        </v-col>
        <v-col v-else-if="searchDate" cols="12" sm="6" md="2">
          <v-menu
            v-model="dateMenu"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="date"
                :label="searchDate.label"
                append-icon="mdi-calendar"
                outlined
                dense
                readonly
                clearable
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="date"
              @input="dateMenu = false"
            ></v-date-picker>
          </v-menu>
        </v-col>
        <v-spacer></v-spacer>
        <v-col cols="12" sm="6" md="3" v-if="fuzzyLabel">
          <v-text-field
            :label="fuzzyLabel"
            outlined
            v-model="fuzzyParam"
            dense
            append-icon="mdi-magnify"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="1">
          <v-select
            :items="pageOptions"
            v-model="options.page"
            label="页码"
            @change="handlePageChange"
            dense
            outlined
            small
          ></v-select>
        </v-col>
      </v-row>
    </v-card-text>
    <v-divider></v-divider>

    <v-data-table
      class="use-divider"
      :footer-props="{
        itemsPerPageOptions: [
          5, 10, 20, 30, 50, 200, 300, 500, 1000, 2000, 3000, 5000, 10000,
          20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000,
        ],
      }"
      v-model="selectedItem"
      :fixed-header="fixHeader"
      :dense="dense"
      :headers="realHeaders"
      :items="items"
      :loading="loading"
      :single-select="singleSelect"
      :item-key="itemKey"
      :max-height="fixHeader ? maxHeight : ''"
      :show-select="showSelect"
      @input="change"
      @click:row="selectRow"
      @dblclick:row="dbclick"
      @item-expanded="$emit('item-expanded')"
      :options.sync="options"
      :server-items-length="totalItems"
      :show-expand="showExpand"
      ref="table"
    >
      <template v-for="h in filterHeader" v-slot:[`item.${h.value}`]="{ item }">
        <slot
          v-if="dictKeys.includes(h.value)"
          :item="item"
          :name="`item.${h.value}`"
        >
          {{ value2Label(h.value, item[h.value]) }}
        </slot>
        <slot
          v-else-if="
            h.value === 'attachmentRecords' || h.value === 'attachments'
          "
          :item="item"
          :name="`item.${h.value}`"
        >
          <v-btn
            @click.stop="
              openAttachmentDialog(item.attachmentRecords || item.attachments)
            "
            dark
            x-small
            color="primary"
            elevation="0"
          >
            {{
              item.attachmentRecords
                ? item.attachmentRecords.length
                : item.attachments.length
            }}
          </v-btn>
        </slot>
        <slot
          v-else-if="h.value === 'shipInfo'"
          :item="item"
          :name="`item.${h.value}`"
        >
          {{ item[h.value] ? item[h.value].chShipName : '' }}
        </slot>
        <slot
          v-else-if="
            h.value === 'remark' && item[h.value] && item[h.value].length > 10
          "
          :item="item"
          :name="`item.${h.value}`"
        >
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <span v-on="on">{{ item[h.value].substring(0, 9) }}...</span>
            </template>
            <span>{{ item[h.value] }}</span>
          </v-tooltip>
        </slot>
        <slot
          v-else-if="h.value === 'status' && useStatus"
          :item="item"
          :name="`item.${h.value}`"
        >
          <v-chip small :color="statusColors[item.status]" :dark="true">
            {{ item.status == 2 ? `审批中` : statuses[item.status] }}
          </v-chip>
        </slot>
        <slot v-else-if="h.formatNumber">
          {{ formatNumber(item[h.value]) }}
        </slot>
        <slot v-else :item="item" :name="`item.${h.value}`">
          {{ item[h.value] }}
        </slot>
      </template>
      <template v-slot:expanded-item="{ headers, item }">
        <slot :headers="headers" :item="item" name="expanded-item"></slot>
      </template>
      <template v-if="!outlined" v-slot:[`footer.prepend`]>
        <v-btn
          v-if="selectedItem.length > 0 && !singleSelect"
          color="primary"
          @click="clearSelection"
        >
          <v-icon left>mdi-delete-sweep</v-icon>
          已勾选 ({{ selectedItem.length }} 条数据)
        </v-btn>
        <v-menu max-height="500" :close-on-content-click="false" offset-y>
          <template v-slot:activator="{ on, attrs }">
            <v-btn outlined color="secondory" v-bind="attrs" v-on="on">
              <v-icon left>mdi-filter-menu-outline</v-icon>
              选择列名显示
            </v-btn>
          </template>
          <v-list class="px-2 py-0 menu-content" dense>
            <v-switch
              class="mt-1 pb-0"
              v-for="(item, i) in headers"
              :key="i"
              :disabled="i === 0"
              color="primary"
              dense
              v-model="computedBoolColumns"
              :label="item.text"
              :value="item"
            ></v-switch>
          </v-list>
        </v-menu>
      </template>
    </v-data-table>
    <v-dialog
      v-model="attachmentDialog"
      max-width="700"
      hide-overlay
      attach="#mask"
    >
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-card>
</template>
<script>
/**
 * @description: 拥有搜索功能的表格，包含字典项搜索、日期搜索、模糊搜索，v-model为查询结果对象
 */
import dictHelper from '@/mixin/dictHelper'
import scrollBarFixed from '@/mixin/scrollBarFixed'
import vShipSelect from './v-ship-select.vue'
let time = null // 定义时间null,用于清除定时器处理单击/双击时间

// 请求搜索防抖
const debounce = (fn, delay = 300) => {
  let timer = null
  return function () {
    let context = this
    let args = arguments
    clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}
export default {
  components: { vShipSelect },
  name: 'v-table-searchable',
  mixins: [dictHelper, scrollBarFixed],
  model: {
    prop: 'value',
    event: 'update',
  },
  data() {
    return {
      dictValues: Array(this.searchDicts.length).fill(null),
      items: [],
      selectedItem: [],
      options: {},
      loading: true,
      totalItems: 0,
      dateMenu: false,
      datesMenu: false,
      date: '',
      dates: [],
      fuzzyParam: '',
      disabled: false,
      ship: '',
      seriesShip: '',
      status: '2',
      dictMaps: new Map([]),
      computedBoolColumns: [],
      attachmentDialog: false,
      attachments: [],
      attachmentHeader: [
        { text: '名称', value: 'name' },
        { text: '大小(kb)', value: 'fileSize' },
        { text: '上传时间', value: 'createTime' },
        { text: '上传人', value: 'userName' },
      ],
    }
  },
  props: {
    showExportButton: {
      type: Boolean,
      default: false, // 默认不显示导出按钮
    },
    showReloadBtn: {
      type: Boolean,
      default: true, // 默认显示列表刷新按钮
    },
    showTableName: {
      type: Boolean,
      default: true, // 默认显示表名称
    },
    // 表格名称
    tableName: {
      type: String,
      default: '数据表格',
    },
    value: {
      type: [Object, Array, Boolean],
    },
    // 模糊搜索展示标签，不使用则无控件
    fuzzyLabel: String,
    // 字典项搜索，数组对象，每个对象包含
    // dicType: 字典类型，对应后端字典表中的dic_type字段
    // label: 展示标签
    // key: 查询返回给后端的字段名
    searchDicts: {
      type: Array,
      default: () => [],
    },
    // 日期搜索，不使用则无控件，interval为true时，搜索范围为开始时间到结束时间，否则为日期选择
    searchDate: Object,
    // 使用船舶搜索控件
    useShip: {
      type: [Boolean, String],
      default: false,
    },
    // 使用船舶系列搜索控件
    useSeries: {
      type: [Boolean, String],
      default: false,
    },
    // 使用船舶id作为主编码
    useShipId: {
      type: [Boolean, String],
      default: false,
    },
    // 使用状态搜索（仅支持默认状态)：未开始(草稿)；2：进行中(已提交)；3已完成；4：驳回,
    useStatus: {
      type: [Boolean, String],
      default: false,
    },
    // 使用搜索按钮
    useSearch: {
      type: [Boolean, String],
      default: true,
    },
    // 自定义搜素插槽的剩余对象，以键值存放
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    // 表头列表与vuetify的表头列表一致
    headers: {
      type: Array,
      required: true,
    },
    specialHeaders: {
      type: Array,
      default: () => [],
    },
    // 表格请求数据源
    reqUrl: {
      type: String,
      required: true,
    },
    // 表格数据源key，用于for循环，默认为id
    itemKey: {
      type: String,
      default: 'id',
    },
    // 双击跳转参数
    pushParams: Object,
    // 是否为多选，多选时，v-model为数组，单选时，v-model为对象
    singleSelect: {
      type: Boolean,
      default: true,
    },
    dense: {
      type: Boolean,
      default: true,
    },
    outlined: {
      type: [Boolean, String],
      default: false,
    },
    showExpand: {
      type: [Boolean, String],
      default: false,
    },
    showSelect: {
      type: [Boolean, String],
      default: true,
    },
    fixHeader: {
      type: Boolean,
      default: true,
    },
    maxHeight: {
      type: String,
      default: '500px',
    },
    itemClass: String,
    filterFunc: {
      type: Function,
      default: (item) => item,
    },
  },
  activated() {
    if (this.$route.query.reload) {
      this.loadDicts()
      this.loadTableData()
    }
  },
  computed: {
    dictKeys: function () {
      return this.searchDicts.map((dic) => dic.key)
    },
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
    filterHeader() {
      return this.headers.filter(
        (h) =>
          this.computedBoolColumns.includes(h) &&
          h.value !== 'data-table-expand',
      )
    },
    realHeaders() {
      return this.headers.filter((h) => this.computedBoolColumns.includes(h))
    },
    // ...其他计算属性
    pageOptions() {
      const totalPages = Math.ceil(this.totalItems / this.options.itemsPerPage)
      return Array.from({ length: totalPages }, (_, i) => i + 1)
    },
  },
  watch: {
    value: {
      handler(val) {
        this.selectedItem = this.singleSelect ? [val] : val
      },
      immediate: true,
    },
    dictValues: {
      handler: function () {
        this.loadTableData()
      },
      deep: true,
    },
    options: {
      handler() {
        this.loadTableData(false)
      },
      deep: true,
    },
    date: {
      handler() {
        this.loadTableData()
      },
    },
    dates: {
      handler() {
        if (this.dates?.start && this.dates?.end) {
          this.datesMenu = false
          this.loadTableData()
        } else {
          this.loadTableData()
        }
      },
    },
    fuzzyParam: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.selectedItem = []
          this.$emit('update', this.singleSelect ? false : [])
          this.$emit('change', this.singleSelect ? false : [])
        }
        this.debounceLoadTableData(this)
      },
    },
    searchRemain: {
      handler() {
        this.debounceImmedLoadTableData(this)
      },
      deep: true,
    },
    ship: {
      handler() {
        if (this.$local.data.get('userInfo').isShipSyS) return
        this.loadTableData()
      },
    },
    status: {
      handler() {
        this.loadTableData()
      },
    },
    headers: {
      handler() {
        this.computedBoolColumns = this.headers.filter(
          (h) =>
            !h.hideDefault &&
            (!h.hideShip || !this.$local.data.get('userInfo').isShipSyS),
        )
      },
      immediate: true,
    },
    seriesShip: {
      handler() {
        this.loadTableData()
      },
    },
  },
  methods: {
    formatNumber(value) {
      if (value == null || isNaN(value)) return value
      console.log(
        'value',
        Number(value)
          .toFixed(2)
          .replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      )
      return Number(value)
        .toFixed(2)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    clearSelection() {
      this.selectedItem = [] // 清空选中项数组
    },
    handlePageChange(newPage) {
      console.log('New page:', newPage)
      console.log('Options page:', this.options.page)
      this.loadTableData(false) // 设置 restPage 为 false
    },

    debounceLoadTableData: debounce(function (that) {
      that.loadTableData()
    }, 500),
    debounceImmedLoadTableData: debounce(function (that) {
      that.loadTableData()
    }, 500),
    change(items) {
      if (this.singleSelect) {
        this.$emit('update', items.length ? items[0] : false)
        this.$emit('change', items.length ? items[0] : false)
      } else {
        this.$emit('update', items)
        this.$emit('change', items)
      }
    },
    selectRow(_, { isSelected, item }) {
      clearTimeout(time)
      time = setTimeout(() => {
        if (this.singleSelect) {
          this.selectedItem = isSelected ? [] : [item]
          this.$emit('update', !isSelected && item)
          this.$emit('change', !isSelected && item)
        } else {
          this.selectedItem = isSelected
            ? this.selectedItem.filter((i) => i.id !== item.id)
            : [...this.selectedItem, item]
          this.$emit('update', this.selectedItem)
          this.$emit('change', this.selectedItem)
        }
      }, 150)
    },
    dbclick(_, { item }) {
      clearTimeout(time)
      if (this.pushParams) {
        this.$router.push({
          name: this.pushParams.name,
          params: {
            ...this.pushParams.params,
            id: item[this.itemKey],
          },
        })
      } else if (this.singleSelect) {
        this.selectedItem = [item]
        this.$emit('update', item)
        this.$emit('change', item)
        this.$emit('dbclick', item)
      } else {
        this.selectedItem = [...this.selectedItem, item]
        this.$emit('update', this.selectedItem)
        this.$emit('change', this.selectedItem)
        this.$emit('dbclick', this.selectedItem)
      }
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
    async exportTableData() {
      console.log('exportTableData')
      console.log('headers', this.headers)
      console.log('specialHeaders', this.specialHeaders)
      console.log('items', this.items)
      // 构建请求数据结构
      // const requestData = {
      //   headers: this.headers,
      //   items: [
      //     {
      //       items: this.items,
      //     },
      //   ],
      // }
      // const url = '/system/resource/exportTableData'
      // const { errorRaw } = await this.postAsync(url, {
      //   headers: this.headers,
      //   items: this.items,
      // })

      await this.blobDownload(
        '/system/resource/exportTableData',
        {
          headers: this.headers,
          items: this.items,
          specialHeaders: this.specialHeaders,
        },
        'tableData.xlsx',
      )
      console.log(this.$refs.downPDFHref)
      // if (errorRaw) {
      //   return
      // }
    },
    resetTableData() {
      this.items = []
      this.totalItems = 0
    },
    async loadTableData(restPage = true) {
      // if (this.selectedItem.length > 0) {
      //   if (!confirm('新的搜索将清空所有选中项。继续吗？')) {
      //     return // 如果用户选择不继续，则停止加载数据
      //   }
      // }
      // this.selectedItem = [] // 清空选中项列表
      this.loading = true
      restPage && (this.options.page = 1) && (this.options.size = 15)
      const { sortBy, sortDesc, page, itemsPerPage } = this.options
      let params
      for (const item of this.searchDicts) {
        if (item.key) {
          params = {
            ...params,
            [item.key]: this.dictValues[this.searchDicts.indexOf(item)],
          }
        }
      }
      if (this.searchRemain) {
        params = {
          ...params,
          ...this.searchRemain,
        }
      }
      if (this.searchDate?.interval && this.dates?.start && this.dates?.end) {
        params = {
          ...params,
          fromTime: this.dates?.start.toISOString().split('T')[0],
          toTime: this.dates?.end.toISOString().split('T')[0],
        }
      } else if (this.searchDate) {
        params = {
          ...params,
          [this.searchDate.value]: this.date,
        }
      }
      if (this.useSeries) {
        params = {
          ...params,
          seriesShip: this.seriesShip,
          shipSeries: this.seriesShip,
        }
      }
      if (this.useShip) {
        params = {
          ...params,
          [this.useShipId ? 'shipId' : 'shipCode']: this.ship,
        }
      }
      if (this.useStatus) {
        params = {
          ...params,
          status: this.status,
        }
      }
      if (this.fuzzyParam) {
        params = {
          ...params,
          fuzzyParam: this.fuzzyParam,
        }
      }
      const { errorRaw, data } = await this.getAsync(this.reqUrl, {
        size: itemsPerPage,
        current: page,
        sort: sortBy?.length ? sortBy[0] : '',
        order: sortBy?.length ? (sortDesc[0] ? 'desc' : 'asc') : '',
        userId: this.$local.data.get('userInfo').id,
        ...params,
      })
      if (!errorRaw) {
        this.items = this.filterFunc(data.records)
        this.$emit('loadCompelte', data.rec)
        this.totalItems = data.total
      }
      this.loading = false
    },
    async loadDicts() {
      for (const item of this.searchDicts) {
        const dicType = item.dicType
        const dict = await this.getDictByType(dicType)
        this.dictMaps.set(dicType, dict)
      }
    },
    value2Label(key, value) {
      const dictType = this.searchDicts.find((dic) => dic.key === key).dicType
      return (
        this.dictMaps?.get(dictType)?.find((item) => item.dictValue === value)
          ?.dictLabel || value
      )
    },
  },

  created() {
    this.loadDicts()
    this.selectedItem = []
    // this.computedBoolColumns = this.headers.filter((h) => !h.hideDefault)
    this.selectedItem = this.singleSelect ? [this.value] : this.value
    this.statuses = ['暂无审批', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', '', 'warning', 'success', 'error']
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
    ]
  },
}
</script>

<style>
.use-divider th + th {
  border-left: 1px solid #dddddd;
  font-size: 17px !important;
}
.use-divider td + td {
  border-left: 1px solid #dddddd;
}
.v-dialog__content {
  position: absolute !important;
}
.no-padding-y [class^='col-'] {
  padding-top: 0 !important;
  padding-bottom: 1 !important;
}
.no-padding-y .v-text-field__details {
  display: none;
}
.menu-content {
  max-height: 500px;
  overflow-y: auto;
}

.menu-content::-webkit-scrollbar {
  width: 5px;
}

.menu-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2.5px;
}
.v-data-table-header {
  white-space: nowrap !important;
}
.v-select__slot {
  min-width: 100px;
}
</style>
