# 项目组件文档

该文件为项目组件（位于`@/components/`路径下的所有组件）说明，组件更新后相关说明将在该文件同步更新，请注意定期查看该文件变动历史，以免造轮子

## v-attach-list

**组件说明：** 附件组件，用于详情页面内

**属性**

| 属性名      | 类型            | 默认值 | 描述                                     |
| ----------- | --------------- | ------ | ---------------------------------------- |
| title | String     | 附件列表     | 附件名标题             |
| attachments | array           | []     | 原始附件列表，初始化表单数据             |
| disabled    | Boolean\|String | false  | 禁止附件的上传、删除操作，仅保留下载功能 |
| readonly | Boolean\|String | false | 同disabled |
| shipCode | String | '' | 文件归属，用于船岸同步，被设置了shipCode的文件参与同步：设置为对应shipCode的仅在该船间同步；设置为0表示广播同步；不设置不同步 |

**事件**

| 事件名 | 描述                                                         |
| ------ | ------------------------------------------------------------ |
| change | 当附件列表发生变化时会触发该事件，返回当前列表内的所有`attachmentIds` |

**注意事项**

注意，假如初始`attachments`为null，而用户始终没有上传文件，则最终不会触发`change`，此时如果相应的父组件内`attachmentIds`为null，提交后端后后端可能发生解析错误引起分页查询接口崩溃，为避免该情况，请务必保证上传的`attachmentIds`为`[]`

## v-audit

**组件说明：** 审批组件，用于详情页面内的审批逻辑制

**属性**

| 属性名      | 类型   | 默认值                                            | 描述                                              |
| ----------- | ------ | ------------------------------------------------- | ------------------------------------------------- |
| auditParams | Object | `{"taskId":'',"processInstanceId":'',isReject:''}` | 审批参数，包含任务id,流程实例id，是否为驳回状态， |

**函数**

| 函数名 | 描述                                                         |
| ------ | ------------------------------------------------------------ |
| submit | 该函数用于供外部调用，对设计审批的报表业务，在用户保存时调用该组件的函数即可完成当前节点的审批 |

**注意事项**

## v-date-time-picker

**组件说明：** 时间选择组件，输出格式为`YYYY-MM-DD HH:mm:ss`，用于需要分钟的时间选择组件

**属性**

| 属性名      | 类型            | 默认值 | 描述                                         |
| ----------- | --------------- | ------ | -------------------------------------------- |
| value       | String          |        | v-model绑定数据，格式为`YYYY-MM-DD HH:mm:ss` |
| disabled    | Boolean\|String |        | 禁止输入更改仅作显示                         |
| rules       |                 |        | 同`vuetify`                                  |
| label       |                 |        | 同`vuetify`                                  |
| dense       |                 |        | 同`vuetify`                                  |
| prependIcon |                 |        | 同`vuetify`                                  |
| readonly    | Boolean\|String |        | 同`vuetify`                                  |

## v-detail-view

**组件说明：** 详情界面的组件，留有插槽，用于规范统一所有二级页面样式

**属性**

| 属性名        | 类型            | 默认值 | 描述                                                         |
| ------------- | --------------- | ------ | ------------------------------------------------------------ |
| title         | String          |        | 页面左上显示的标题名称                                       |
| backRouteName | String          |        | 详情页面的回退界面的路由名，一般为相应的分页查询界面         |
| subtitles     | Array           |        | 包含的所有副标题，为数组，直接使用中文，会生成对应的内容插槽和按钮插槽，例：`['基础信息','采购详情']` |
| tooltip       | String          |        | 鼠标悬浮时提示，一般建议使用编码等唯一信息作为提示           |
| canSubmit     | Boolean\|String |        | 为`true`时会产生事件submit，用于区分save（即保存并提交和保存） |
| canSave       | Boolean         | true   | 为`false`时将会隐藏按钮                                      |
| canPrint      | Boolean\|String |        | 为`true`时将展示打印当前页面，打印可能需要适配页面           |

**事件**

| 事件名 | 描述                                                         |
| ------ | ------------------------------------------------------------ |
| save   | 用户单击**保存**按钮时触发，将会得到函数`goback`，在save函数内部调用用于返回`backRouteName`对应路由 |
| submit | 用户单击**保存并提交**按钮时触发，仅在`canSubmit`被设置时生效将会得到函数`goback`，在save函数内部调用用于返回`backRouteName`对应路由 |

**插槽**

| 插槽名      | 描述                                                         |
| ----------- | ------------------------------------------------------------ |
| titlebtns   | 标题插槽，将覆盖原本组件提供的按钮，完全自定义按钮组类型     |
| custombtns  | 保留原始按钮组的情况下，在原始按钮组后方添加自定义按钮，使用`titlebtns`后会失效 |
| topcontent  | 在第一个副标题上方放置，一般用于放置`v-audit`组件            |
| ${name}     | 对应副标题数组的名称，内容放置其内                           |
| ${name}按钮 | 对应副标题右侧的位置，用于放置按钮组                         |
| default     | 默认插槽，页面主体全部自定义，`topcontent`、`${name}`、`${name}按钮`插槽将失效 |



## v-dialog-select

**组件说明：** 弹窗表格选择组件，用于选择数量较多且需额外展示表格信息的选择字段，组件内使用了`v-table-searchable`组件，部分属性即为操控表格内的数据。

*该组件可能会有潜在问题，可能不适合所有情况，主要不适合在展示时不知value的情况*

**属性**

| 属性名       | 类型            | 默认值 | 描述                                                         |
| ------------ | --------------- | ------ | ------------------------------------------------------------ |
| value        | String          |        | 最终选中的值，仅可清空，对该值进行其它变动无效，组件不会处理 |
| initSelected | String          |        | 初始化时展示，但**实际不会返回value**，因此需要用户自行设置初始model（value仅在用户点击按钮时方会改变） |
| dense        | Boolean\|String |        | 同`vuetify`                                                  |
| outlined     | Boolean\|String |        | 同`vuetify`                                                  |
| disabled     | Boolean\|String |        | 同`vuetify`                                                  |
| clearable    | Boolean\|String |        | 同`vuetify`                                                  |
| rules        | Array           |        | 同`vuetify`                                                  |
| label        | String          |        | 同`vuetify`                                                  |
| itemText     | String          |        | 展示值                                                       |
| itemValue    | String          |        | 真实返回值                                                   |
| tableName    | String          |        | 同`v-table-searchable`                                       |
| fuzzyLabel   | String          |        | 同`v-table-searchable`                                       |
| searchDicts  | Array           |        | 同`v-table-searchable`                                       |
| searchDate   | Object          |        | 同`v-table-searchable`                                       |
| searchRemain | Object          |        | 同`v-table-searchable`                                       |
| headers      | Array           |        | 同`v-table-searchable`                                       |
| reqUrl       | String          |        | 同`v-table-searchable`                                       |
| filterFunc   | Function        |        | 同`v-table-searchable`                                       |
| maxWidth     | String\|Number  | 1100   | 用于控制窗口大小，对部分长表头内容                           |
| readonly     | Boolean\|String |        | 同`v-data-table`                                             |

**事件**

| 事件名 | 描述                                                     |
| ------ | -------------------------------------------------------- |
| open   | 对话框弹出时触发（预留事件，暂无作用）                   |
| select | 用户单击确定按钮是触发回调内第一个位置获得选中的`Object` |

## v-dict-select

**组件说明：** 数据字典选	择，需提前在数据字典相应接口类型配置

1. 配置字典类型`/system/dict-type/save`
2. 为添加的字典类型添加键值对`/system/dict-data/save`

**属性**

| 属性名   | 类型            | 默认值 | 描述                                                         |
| -------- | --------------- | ------ | ------------------------------------------------------------ |
| value    | String          |        | 最终选中的值，仅可清空，对该值进行其它变动无效，组件不会处理 |
| rules    | Array           |        | 同`vuetify`                                                  |
| label    | Boolean\|String |        | 同`vuetify`                                                  |
| dictType | String          |        | 配置的字典类型                                               |
| disabled | Boolean\|String |        | 同`vuetify`                                                  |
| readonly | Boolean\|String |        | 同`vuetify`                                                  |

## v-file-upload

*该组件废弃，不再维护，为初期临时使用测试*

## v-handler

**组件说明：**经办人，用户选择组件

**属性**

| 属性名     | 类型            | 默认值 | 描述               |
| ---------- | --------------- | ------ | ------------------ |
| value      | String          |        | 值                 |
| disabled   | Boolean         |        | 同`vuetify`        |
| rules      | Arrays          |        | 同`vuetify`        |
| label      | String          | 经办人 | 标签               |
| useCurrent | Boolean\|String |        | 使用当前用户为默认 |
| initUser   | Object\|Boolean | false  | 初始用户           |
| readonly   | Boolean\|String |        | 同`vuetify`        |

## v-import-btn

**组件说明：** 上传按钮，暂时为导入EXCEL时使用，采用`multipart/form-data`

**属性**

| 属性名      | 类型   | 默认值 | 描述         |
| ----------- | ------ | ------ | ------------ |
| importUrl   | String |        | 导入地址     |
| otherParams | Object |        | 表单额外参数 |

**事件**

| 事件名        | 描述           |
| ------------- | -------------- |
| importSuccess | 上传成功后触发 |

## v-ship-select

**组件说明：** 船舶选择组件，默认使用`shipCode`为返回值，船舶中文名作为展示值

**属性**

| 属性名    | 类型            | 默认值 | 描述                 |
| --------- | --------------- | ------ | -------------------- |
| value     | String          |        | 值                   |
| disabled  | Boolean         |        | 同`vuetify`          |
| rules     | Boolean\|String |        | 同`vuetify`          |
| useEnName | Boolean\|String |        | 使用船舶英文名为text |
| useId     | Boolean\|String |        | 使用`shipId`为返回值 |
| readonly  | Boolean\|String |        | 同`vuetify`          |

## v-supply-select

**组件说明：**供应商选择组件，用于部分供应商的选择。

**属性**

| 属性名    | 类型             | 默认值 | 描述                               |
| --------- | ---------------- | ------ | ---------------------------------- |
| value     | [String, Object] |        | 值                                 |
| disabled  | Boolean          |        | 同`vuetify`                        |
| rules     | Boolean\|String  |        | 同`vuetify`                        |
| useEnName | Boolean\|String  |        | 使用岗位英文名称                   |
| useId     | Boolean\|String  |        | 使用岗位`id`为返回值，**暂定废弃** |
| type      | Number\|String   | 3      | 0-甲板部 1-轮机部 3-全选，默认全选 |

## v-ship-station

**组件说明：** 船舶上岗位选择组件，默认直接返回岗位名称

**属性**

| 属性名   | 类型            | 默认值 | 描述                                |
| -------- | --------------- | ------ | ----------------------------------- |
| value    | String          |        | 供应商Id                            |
| disabled | Boolean         |        | 同`vuetify`                         |
| rules    | Boolean\|String |        | 同`vuetify`                         |
| shipCode | String          |        | 指定船舶可用的供应商                |
| currency | Array           | []     | 供应商可用的货币，可使用`.sync`接收 |

## v-table-list

**组件说明：** 用于详情页面内的表格，在基础的`vuetify`的数据表格基础上进行了改进：

1. 去除了分页，因此对详情页面内的数据不再采取分页操作
2. 增加了单击选择的功能

| 属性名  | 类型            | 默认值 | 描述         |
| ------- | --------------- | ------ | ------------ |
| headers | Array           |        | 表头         |
| items   | Boolean         |        | 数据项       |
| value   | Boolean\|Object |        | 选中的数据项 |
| itemKey | String          | id     | 需是唯一值   |

## v-table-searchable

**组件说明：** 分页查询组件，与后端配合

1. 组件将自动对`attachmentRecords` `attachments`字段对应的插槽进行处理
2. 组件将自动对位于`searchDicts`内的表头插槽进行处理
3. 组件将自动对`status`插槽进行处理，并包含`bussinessStatus`展示，非常规的`status`仍需要自行拟写插槽
4. 单击选中，双击跳转

| 属性名       | 类型                   | 默认值                 | 描述                                                         |
| ------------ | ---------------------- | ---------------------- | ------------------------------------------------------------ |
| tableName    | String                 |                        | 标题                                                         |
| fuzzyLabel   | String                 |                        | 模糊搜索展示标签，不使用则无控件项                           |
| value        | Boolean\|Object\|Array |                        | 选中的数据项，可多选                                         |
| itemKey      | String                 | id                     | 需是唯一值                                                   |
| searchDicts  | Array                  |                        | 字典项搜索，每个数组对象`{dicType:'字典类型',label:'展示标签',key:'返回给后端的字段'}` |
| searchDate   | Object                 |                        | 日期搜索，不使用则无控件，interval为true时，搜索范围为开始时间到结束时间，否则为日期选择 |
| useShip      | Boolean\|String        | false                  | 使用船舶搜索控件                                             |
| useShipId    | Boolean\|String        | false                  | 使用船舶id作为主编码                                         |
| searchRemain | Object                 |                        | 自定义搜素插槽的剩余对象，以键值存放                         |
| headers      | Array                  |                        | 表头列表与vuetify的表头列表一致,**额外增加新属性** `hideDefault`,为true时将默认隐藏该列 |
| reqUrl       | String                 |                        | 表格请求数据源                                               |
| itemKey      | String                 | id                     | 表格数据源key，用于for循环，默认为id                         |
| pushParams   | Object                 |                        | 双击跳转参数，为空时将会触发事件`dbclick`，且与单击效果相同  |
| singleSelect | Boolean                | true                   | 是否为多选，多选时，v-model为数组，单选时，v-model为对象     |
| dense        | Boolean                |                        |                                                              |
| outlined     |                        |                        |                                                              |
| fixHeader    |                        |                        |                                                              |
| maxHeight    |                        |                        |                                                              |
| itemClass    |                        |                        |                                                              |
| useStatus    | Boolean\|String        |                        | 使用状态搜索（仅支持默认状态）：未开始(草稿)；2：进行中(已提交)；3已完成；4：驳回, |
| useSearch    | Boolean\|String        | true                   | 使用搜索按钮                                                 |
| filterFunc   | Function               | `(records) => records` | 数据过滤器，当后端返回数据格式无法满足前端格式需求时使用，改写`records`格式，传入参数为原数组，需返回数组 |

**函数**

| 函数名        | 描述                                                         |
| ------------- | ------------------------------------------------------------ |
| loadTableData | 该函数可供外部调用手动刷新数据，可`restPage=false`来禁止页面数归1 |
| dbclick       | `pushParams`未设置时会触发，可用于同页编辑                   |

## vs-date-picker

**组件说明：** 日期选择组件，与官方不同，为输入框

**属性**

| 属性名      | 类型            | 默认值 | 描述                                         |
| ----------- | --------------- | ------ | -------------------------------------------- |
| value       | String          |        | v-model绑定数据，格式为`YYYY-MM-DD HH:mm:ss` |
| disabled    | Boolean\|String |        | 禁止输入更改仅作显示                         |
| rules       |                 |        | 同`vuetify`                                  |
| label       |                 |        | 同`vuetify`                                  |
| dense       |                 |        | 同`vuetify`                                  |
| prependIcon |                 |        | 同`vuetify`                                  |
| outlined    |                 |        | 同`vuetify`                                  |
| useToday    |                 |        | 采用今天作为初始值                           |
| readonly    | Boolean\|String |        | 同`vuetify`                                  |
| minDate     | String\|Date    |        | 最小允许选中日期                             |
| maxDate     | String\|Date    |        | 最大允许选中日期                             |

## v-year-month-picker

**组件说明：** 年月选择器

**属性**

| 属性名      | 类型            | 默认值 | 描述                                         |
| ----------- | --------------- | ------ | -------------------------------------------- |
| value       | String          |        | v-model绑定数据，格式为`YYYY-MM`              |
| label       |                 |        | 同`vuetify`                                  |
| dense       |                 |        | 同`vuetify`                                  |
| outlined    |                 |        | 同`vuetify`                                  |
| clearable   |                 |        | 同`vuetify`                                  |

