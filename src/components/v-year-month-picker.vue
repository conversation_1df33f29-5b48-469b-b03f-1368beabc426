<template>
  <v-menu ref="menu" transition="scale-transition" offset-y min-width="auto">
    <template v-slot:activator="{ on, attrs }">
      <v-text-field
        v-on="on"
        v-bind="attrs"
        append-icon="mdi-calendar"
        :outlined="outlined"
        :label="label"
        :dense="dense"
        v-model="date"
        :clearable="clearable"
        readonly
        :rules="rules"
        :disabled="disabled"
      ></v-text-field>
    </template>
    <v-date-picker
      type="month"
      no-title
      scrollable
      v-model="date"
      @input="$refs.menu.save(date)"
      :disabled="disabled"
    ></v-date-picker>
  </v-menu>
</template>
<script>
export default {
  name: 'v-year-month-picker',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: [String, Boolean],
    dense: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: [Boolean, String],
      default: true,
    },
    outlined: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '年月选择',
    },
    useCurrent: {
      type: Boolean,
      default: true,
    },
    rules: Array,
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      handler(val) {
        this.date = val
      },
    },
    date: {
      handler(val) {
        this.$emit('change', val)
      },
    },
  },
  data() {
    return {
      date: '',
    }
  },

  methods: {},

  mounted() {
    if (this.useCurrent) {
      this.date = new Date().toISOString().substr(0, 7)
    } else {
      this.date = this.value
    }
  },
}
</script>

<style></style>
