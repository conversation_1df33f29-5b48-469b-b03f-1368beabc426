<template>
  <v-select
    ref="select"
    :dense="dense"
    v-model="val"
    :label="label"
    :items="list"
    :rules="rules"
    :disabled="disabled"
    :readonly="canNotEdit ? true : readonly"
    @change="change"
    outlined
    :clearable="canNotEdit ? false : clearable"
  ></v-select>
</template>

<script>
export default {
  name: 'v-ship-dept',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  props: {
    value: [String, Array],
    rules: Array,
    readonly: [<PERSON>olean, String],
    disabled: [Boolean, String],
    clearable: {
      type: [Boolean, String],
      default: true,
    },
    dense: {
      type: Boolean,
      default: true,
    },
    label: {
      type: String,
      default: '申请部门',
    },
  },
  data() {
    return {
      list: [],
      val: null,
      canNotEdit: false,
    }
  },
  methods: {
    change(v) {
      this.$emit('update', v)
    },
    validate(force, value) {
      return this.$refs.select.validate(force, value)
    },
    reset() {
      this.$refs.select.reset()
    },
    resetValidation() {
      this.$refs.select.resetValidation()
    },
  },
  watch: {
    value: {
      handler(v) {
        this.val = v
      },
      immediate: true,
    },
  },
  created() {
    this.form && this.form.register(this)
    this.list = ['甲板部', '轮机部']
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    if (this.isShip) {
      // this.list = this.$local.data.get('userInfo').shipDept
      if (this.$local.data.get('userInfo').shipDept?.length === 1) {
        this.canNotEdit = true
        if (this.$local.data.get('userInfo').shipDept == '甲板部') {
          this.$emit('update', this.list[0])
        }
        if (this.$local.data.get('userInfo').shipDept == '轮机部') {
          this.$emit('update', this.list[1])
        }
      }
    }
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>
<style scoped>
.v-menu__content {
  z-index: 101 !important;
}
</style>
