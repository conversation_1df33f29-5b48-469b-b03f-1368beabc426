<template>
  <div>
    <input
      ref="uploader"
      class="d-none"
      type="file"
      multiple
      accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
      @change="onFileChanged"
    />
    <v-btn
      :loading="isSelecting"
      outlined
      tile
      color="primary"
      class="mx-1"
      @click="onButtonClick"
    >
      <v-icon left>mdi-microsoft-excel</v-icon>
      {{ buttonLabel }}
    </v-btn>
  </div>
</template>
<script>
export default {
  name: 'v-import-more-btn',
  props: {
    importUrl: {
      type: String,
      required: true,
    },
    otherParams: {
      type: Object,
      default: () => ({}),
    },
    buttonLabel: {
      type: String,
      default: '导入多个excel', // 如果没有提供 buttonLabel props，那么默认值将是 '导入多个excel'
    },
    extraParam: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      selectedFiles: [], // 改为数组
      isSelecting: false,
    }
  },

  methods: {
    onButtonClick() {
      window.addEventListener('focus', () => {}, {})
      this.$refs.uploader.click()
    },
    async onFileChanged(e) {
      if (!e.target.files.length) return
      this.isSelecting = true
      this.selectedFiles = Array.from(e.target.files) // 获取所有选择的文件
      let formData = new FormData()

      // 为每一个文件创建一个新的FormData实例
      for (let file of this.selectedFiles) {
        formData.append('files', file, file.name)
      }

      if (this.otherParams) {
        Object.keys(this.otherParams).forEach((key) => {
          formData.append(key, this.otherParams[key])
        })
      }
      console.log('extraParam', this.extraParam)
      if (this.extraParam) {
        Object.entries(this.extraParam).forEach(([key]) => {
          formData.append(key, this.extraParam[key])
        })
      }

      const { errorRaw } = await this.postAsync(this.importUrl, formData)
      this.$refs.uploader.value = null
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
        console.error('触发')
        this.$emit('importError', errorRaw)
      } else {
        this.$dialog.message.success('导入成功')
        this.$emit('importSuccess')
      }
      this.isSelecting = false
      this.selectedFiles = [] // 清空数组
    },
  },

  mounted() {},
}
</script>
