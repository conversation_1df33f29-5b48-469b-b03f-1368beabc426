<template>
  <div>
    <v-divider></v-divider>
    <v-data-table
      :show-select="showSelect"
      :headers="headers"
      :items="items"
      :hide-default-footer="!usePage"
      :disable-pagination="!usePage"
      v-model="selected"
      :item-key="itemKey"
      @click:row="selectRow"
      dense
      class="use-divider"
    >
      <template v-for="h in headers" v-slot:[`item.${h.value}`]="{ item }">
        <slot
          v-if="dictKeys.includes(h.value)"
          :item="item"
          :name="`item.${h.value}`"
        >
          {{ value2Label(h.value, item[h.value]) }}
        </slot>
        <slot
          v-else-if="h.value === 'attachmentRecords'"
          :item="item"
          :name="`item.${h.value}`"
        >
          <v-btn
            @click.stop="openAttachmentDialog(item.attachmentRecords)"
            dark
            x-small
            color="primary"
            elevation="0"
          >
            {{ item.attachmentRecords.length }}
          </v-btn>
        </slot>
        <slot
          v-else-if="
            h.value === 'remark' && item[h.value] && item[h.value].length > 10
          "
          :item="item"
          :name="`item.${h.value}`"
        >
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <span v-on="on">{{ item[h.value].substring(0, 9) }}...</span>
            </template>
            <span>{{ item[h.value] }}</span>
          </v-tooltip>
        </slot>
        <slot v-else :item="item" :name="`item.${h.value}`">
          {{ item[h.value] }}
        </slot>
      </template>
    </v-data-table>
    <v-divider></v-divider>
    <v-dialog
      attach="#mask"
      v-model="attachmentDialog"
      max-width="700"
      hide-overlay
    >
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  mixins: [dictHelper],
  name: 'v-table-list',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    headers: {
      type: Array,
      default: () => [],
    },
    items: {
      type: Array,
      default: () => [],
    },
    value: [Object, Array],
    itemKey: {
      type: String,
      default: 'id',
    },
    searchDicts: {
      type: Array,
      default: () => [],
    },
    usePage: {
      type: [Boolean, String],
      default: false,
    },
    showSelect: {
      type: [Boolean, String],
      default: true,
    },
  },
  data() {
    return {
      dictValues: Array(this.searchDicts.length).fill(null),
      selected: [],
      dictMaps: new Map([]),
      attachmentDialog: false,
      attachments: [],
      attachmentHeader: [
        { text: '名称', value: 'name' },
        { text: '大小(kb)', value: 'fileSize' },
        { text: '上传时间', value: 'createTime' },
        { text: '上传人', value: 'userName' },
      ],
    }
  },

  computed: {
    dictKeys: function () {
      return this.searchDicts.map((dic) => dic.key)
    },
  },

  watch: {
    selected(val) {
      if (val.length > 0) {
        this.$emit('change', val)
      } else {
        this.$emit('change', false)
      }
    },
    value(val) {
      if (!val) {
        this.selected = []
      }
    },
  },

  methods: {
    selectRow(_, { isSelected, item }) {
      this.selected = isSelected ? [] : [item]
      this.$emit('change', !isSelected && item)
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
    async loadDicts() {
      for (const item of this.searchDicts) {
        const dicType = item.dicType
        const dict = await this.getDictByType(dicType)
        this.dictMaps.set(dicType, dict)
      }
    },
    value2Label(key, value) {
      const dictType = this.searchDicts.find((dic) => dic.key === key).dicType
      return (
        this.dictMaps.get(dictType).find((item) => item.dictValue === value)
          ?.dictLabel || value
      )
    },
  },

  mounted() {
    this.loadDicts()
  },
}
</script>

<style>
.v-dialog__content {
  position: absolute !important;
}
</style>
