<template>
  <v-dialog-select
    ref="dialogSelect"
    :label="label"
    :item-text="userType == 2 || userType == 3 ? 'creName' : 'nickName'"
    :init-selected="initUser || currentUser"
    v-model="val"
    :headers="userHeaders"
    :rules="rules"
    :disabled="disabled"
    :readonly="readonly"
    :search-remain="searchObj"
    :req-url="reqUrl"
    @select="$emit('selectUser', $event)"
    :clearable="clearable"
    @clear="clearUser"
  >
    <template #searchflieds>
      <v-col v-if="userType == 2 || userType == 3" cols="12" sm="6" md="3">
        <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-text-field
          v-if="userType == 1 || userType == 0"
          label="用户名"
          outlined
          dense
          v-model="searchObj.nickName"
        ></v-text-field>
        <v-text-field
          v-else
          label="用户名"
          v-model="searchObj.creName"
          outlined
          dense
        ></v-text-field>
      </v-col>
      <v-col md="6" sm="6" cols="12" v-if="userType == 1 || userType == 0">
        <treeselect
          v-model="searchObj.deptId"
          :options="deptTree"
          placeholder="请选择部门"
          outlined
          dense
        />
      </v-col>
    </template>
  </v-dialog-select>
</template>
<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: { Treeselect },
  name: 'v-handler',
  model: {
    prop: 'value',
    event: 'change',
  },
  inject: {
    form: { default: null },
  },
  props: {
    initUser: {
      type: [Object, Boolean],
      default: false,
    },
    label: {
      type: String,
      default: '经办人',
    },
    value: {
      type: String,
      default: '',
    },
    readonly: [Boolean, String],
    disabled: [Boolean, String],
    clearable: {
      type: [Boolean, String],
      default: false,
    },
    rules: Array,
    useCurrent: {
      type: [Boolean, String],
      default: true,
    },
    // 0-船岸全部员工 1-岸端员工 2-船员(船上使用时仅在船) 3-历史在船员工
    userType: {
      type: [String, Number],
      default: 1,
    },
  },
  created() {
    const allUserHeaders = [
      { text: '用户名', value: 'nickName' },
      { text: '岗位名称', value: 'deptName' },
      { text: '部门名称', value: 'parentDeptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
    const crewHeaders = [
      { text: '用户名', value: 'creName' },
      { text: '身份证', value: 'creIdNo' },
      { text: '职务', value: 'post' },
      { text: '上船时间', value: 'onBoardTime' },
      { text: '下船时间', value: 'offBoardTime' },
    ]
    this.userHeaders =
      this.userType == 2 || this.userType == 3 ? crewHeaders : allUserHeaders
    this.reqUrl =
      this.userType == 2
        ? '/business/crew/osmOnShipCrew/page'
        : this.userType == 3
        ? '/business/crew/osmOnShipCrew/historyOnShip/page'
        : '/system/user/page'
    if (this.useCurrent) {
      this.currentUser = {
        id: this.$local.data.get('userInfo').id,
        nickName: this.$local.data.get('userInfo').nickName,
        creName: this.$local.data.get('userInfo').nickName,
      }
      this.$emit('change', this.currentUser.id)
    } else {
      this.currentUser = {}
    }
    this.getDeptTreeList()
    this.form && this.form.register(this)
  },
  computed: {
    val: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit(
          'change',
          this.userType == 2 || this.userType == 3 ? val.creId : val.id,
        )
        this.$emit('select', val)
      },
    },
  },
  data() {
    return {
      deptTree: [],
      searchObj: {
        nickName: '',
        creName: '',
        deptId: '1',
      },
    }
  },

  methods: {
    validate(force, value) {
      return this.$refs.dialogSelect.validate(force, value)
    },
    reset() {
      this.$refs.dialogSelect.reset()
    },
    resetValidation() {
      this.$refs.dialogSelect.resetValidation()
    },
    clearUser() {
      this.$emit('change', '')
    },
    async getDeptTreeList() {
      const { data } = await this.getAsync('/system/dept/getDeptTreeList')
      this.deptTree = data
    },
  },

  mounted() {},
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>

<style></style>
