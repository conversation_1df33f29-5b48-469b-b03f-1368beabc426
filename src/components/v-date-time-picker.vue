<template>
  <div>
    <v-menu
      v-if="!readonly"
      v-model="menu"
      :nudge-left="40"
      transition="scale-transition"
      offset-y
      min-width="auto"
      :close-on-content-click="false"
    >
      <template v-slot:activator="{ on, attrs }">
        <v-text-field
          ref="dateTime"
          v-model="val"
          :label="label"
          :prepend-inner-icon="prependIcon"
          :dense="dense"
          :outlined="outlined"
          v-bind="attrs"
          v-on="on"
          :rules="rules"
          @change="change"
          readonly
        ></v-text-field>
      </template>
      <vc-date-picker
        mode="dateTime"
        v-model="val"
        :model-config="{
          type: 'string',
          mask: 'YYYY-MM-DD HH:mm:ss',
        }"
        is24hr
        :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm:ss' }"
      ></vc-date-picker>
    </v-menu>
    <v-text-field
      v-else
      ref="dateTime"
      v-model="val"
      :label="label"
      :prepend-inner-icon="prependIcon"
      :dense="dense"
      :outlined="outlined"
      :rules="rules"
      @change="change"
      readonly
    ></v-text-field>
  </div>
</template>
<script>
export default {
  name: 'v-date-time-picker',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  data() {
    return {
      menu: false,
      val: null,
    }
  },

  watch: {
    value(val) {
      this.val = val
    },
    val(val) {
      this.$emit('update', val)
    },
  },

  props: {
    value: String,
    rules: Array,
    label: String,
    disabled: [Boolean, String],
    readonly: [Boolean, String],
    outlined: [Boolean, String],
    dense: [Boolean, String],
    prependIcon: {
      type: String,
      default: 'mdi-calendar-clock',
    },
  },

  methods: {
    change() {
      this.$emit('update', this.val)
    },
    validate(force, value) {
      return this.$refs.dateTime.validate(force, value)
    },
    reset() {
      this.val = ''
      this.$refs.dateTime.reset()
    },
    resetValidation() {
      this.$refs.dateTime.resetValidation()
    },
  },

  created() {
    this.form && this.form.register(this)
    this.val = this.value
  },

  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>

<style></style>
