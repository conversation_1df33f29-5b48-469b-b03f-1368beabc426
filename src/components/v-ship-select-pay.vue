<template>
  <v-autocomplete
    ref="select"
    :dense="dense"
    v-model="val"
    :label="changeLabel ? '目标船舶' : useEnName ? 'Ship Name' : '船舶名称'"
    :items="list"
    :rules="rules"
    :loading="loading"
    :disabled="disabled"
    :readonly="isShip ? true : readonly"
    :multiple="multiple"
    @change="change"
    item-text="dictLabel"
    item-value="dictValue"
    outlined
    :clearable="isShip ? false : clearable && !readonly"
  >
    <template v-if="multiple" #prepend-inner>
      <v-menu offset-y>
        <template v-slot:activator="{ on, attrs }">
          <v-btn small text v-bind="attrs" v-on="on">
            <v-icon left>mdi-select-group</v-icon>
            系列
          </v-btn>
        </template>
        <v-list>
          <v-list-item v-for="(item, index) in series" :key="index">
            <v-list-item-title @click="selectSeries(item.dictValue)">
              {{ item.dictLabel }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </template>
  </v-autocomplete>
</template>

<script>
import { cacheGetDefault } from '@/util/cache'
import dictHelper from '@/mixin/dictHelper'

export default {
  name: 'v-ship-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  mixins: [dictHelper],
  props: {
    value: [String, Array],
    rules: Array,
    readonly: [Boolean, String],
    disabled: [Boolean, String],
    changeLabel: [Boolean, String],
    dictType: String,
    // 是否使用船舶id，默认使用船舶编号
    useId: [Boolean, String],
    // 是否使用英文船名，默认使用中文船名
    useEnName: [Boolean, String],
    // 多选
    multiple: [Boolean, String],
    // 是否使用英文船名，默认使用中文船名
    clearable: {
      type: [Boolean, String],
      default: true,
    },
    dense: {
      type: Boolean,
      default: true,
    },
    payCompony: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      list: [],
      loading: false,
      val: null,
      hint: null,
      series: [],
    }
  },
  methods: {
    change(v) {
      this.$emit('update', v)
      const shipName = this.list.find((element) => element.dictValue === v)
      this.$emit('getShipName', shipName)
    },
    validate(force, value) {
      return this.$refs.select.validate(force, value)
    },
    reset() {
      this.$refs.select.reset()
    },
    resetValidation() {
      this.$refs.select.resetValidation()
    },

    async getShipList() {
      let that = this
      const data = await cacheGetDefault('ship-list', async () => {
        const { data, errorRaw } = await that.getAsync(
          '/business/common/ship/simpleInfo/listByPayCompony',
          { payCompony: this.payCompony },
          false,
        )
        if (errorRaw) {
          that.$dialog.message.error('船舶列表获取失败，请重试')
          return null
        }
        if (data.length === 0) {
          that.$dialog.message.error('船舶列表为空，部分功能受损')
        }
        return data
      })
      if (this.multiple) {
        this.series = await this.getDictByType('series_ship')
      }
      if (this.useEnName) {
        this.list = data.map((item) => {
          return {
            dictValue: this.useId ? item.id : item.shipCode,
            dictLabel: item.enShipName,
            shipSeries: item.shipSeries,
          }
        })
      } else {
        this.list = data.map((item) => {
          return {
            dictValue: this.useId ? item.id : item.shipCode,
            dictLabel: item.chShipName,
            shipSeries: item.shipSeries,
          }
        })
      }
    },

    selectSeries(series) {
      // this.val = this.list.filter((i) => i.shipSeries == series)
      this.$emit(
        'update',
        this.list.filter((i) => i.shipSeries == series).map((i) => i.dictValue),
      )
    },
  },
  watch: {
    value(val) {
      this.val = val
    },
    payCompony(val) {
      if (val) this.getShipList()
    },
  },
  created() {
    this.form && this.form.register(this)
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    if (this.isShip)
      this.useId
        ? this.$emit('update', this.$local.data.get('userInfo').shipId)
        : this.$emit('update', this.$local.data.get('userInfo').shipCode)
  },
  async beforeMount() {
    this.loading = true
    await this.getShipList()
    this.loading = false
    this.val = this.value
    this.resetValidation()
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
}
</script>
<style scoped>
.v-menu__content {
  z-index: 101 !important;
}
</style>
