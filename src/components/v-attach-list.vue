<template>
  <v-sheet class="d-print-none" outlined>
    <v-data-table
      :headers="headers"
      :items="items"
      :items-per-page="500"
      hide-default-footer
      item-key="id"
      dense
    >
      <template v-slot:top>
        <v-card-title class="py-1" flat>
          <v-toolbar-title>
            {{ title }}
            <div class="red--text subtitle-1" style="display: inline-block">
              {{ haveChange ? '-有文件变动，请注意保存' : '' }}
            </div>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn
            v-if="!disabled && !readonly"
            @click="onButtonClick"
            :loading="isSelecting"
            small
            outlined
            tile
            class="mx-1"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            上传
          </v-btn>
          <input
            ref="uploader"
            class="d-none"
            type="file"
            :accept="accept"
            @change="onFileChanged"
          />
        </v-card-title>
      </template>
      <template v-slot:[`item.name`]="{ item }">
        <div v-if="item.isDelete" class="text-decoration-line-through">
          {{ item.name }}
        </div>
        <div v-else-if="!item.initial" class="blue--text">
          {{ item.name }}-新增
        </div>
        <div v-else>
          {{ item.name }}
          <span class="red--text">
            {{ item.filePath ? '' : '(未同步,无法下载)' }}
          </span>
        </div>
      </template>
      <template v-slot:[`item.action`]="{ item }">
        <!-- <v-btn
          v-if="item.filePath"
          :href="`/api/system/file/download?fileName=${item.name}&filePath=${item.filePath}`"
          target="_blank"
          icon
          small
          class="mr-1"
        >
          <v-icon>mdi-download</v-icon>
        </v-btn> -->
        <v-btn
          v-if="item.filePath"
          :href="`/api/system/file/download?fileName=${encodeURIComponent(
            item.name,
          )}&filePath=${item.filePath}`"
          target="_blank"
          icon
          small
          class="mr-1"
        >
          <v-icon>mdi-download</v-icon>
        </v-btn>

        <!-- 文件暂未同步，无法下载 -->

        <template v-if="!disabled && !readonly">
          <v-btn icon v-if="!item.isDelete" small @click="delAttachment(item)">
            <v-icon>mdi-delete-empty</v-icon>
          </v-btn>
          <v-btn icon v-else small @click="cancelDel(item)">
            <v-icon>mdi-delete-off</v-icon>
          </v-btn>
        </template>
      </template>
    </v-data-table>
  </v-sheet>
</template>
<script>
/**
 * @description: 附件列表
 * @props {Array} attachments 附件列表
 * @event {Function} changeAttachment 附件变动回调,第一个参数为附件id列表
 */
export default {
  name: 'v-attach-list',
  data() {
    return {
      headers: [
        { text: '名称', value: 'name' },
        { text: '大小(kb)', value: 'fileSize' },
        { text: '上传时间', value: 'createTime' },
        { text: '上传人', value: 'userName' },
        { text: '操作', value: 'action', sortable: false },
      ],
      items: [],
      isSelecting: false,
    }
  },

  props: {
    attachments: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: [Boolean, String],
      default: false,
    },
    readonly: {
      type: [Boolean, String],
      default: false,
    },
    title: {
      type: String,
      default: '附件列表',
    },
    shipCode: {
      type: String,
      default: '',
    },
    accept: {
      type: String,
      default: 'all',
    },
  },

  computed: {
    haveChange() {
      return this.items.some(
        (item) =>
          // 被删除且是初始化 或者 没有被删除且是不是初始化
          (item.isDelete && item.initial) || (!item.initial && !item.isDelete),
      )
    },
  },

  watch: {
    attachments: {
      handler(val) {
        this.items = val.map((item) => {
          return {
            ...item,
            fileSize: item.fileSize,
            isDelete: false,
            initial: true,
          }
        })
      },
      deep: true,
      immediate: true,
    },
    items: {
      handler(val) {
        const attachmentIds = val
          .filter((item) => !item.isDelete)
          .map((item) => item.id)
        this.$emit('change', attachmentIds)
      },
      deep: true,
    },
  },

  methods: {
    onButtonClick() {
      window.addEventListener('focus', () => {}, { once: true })
      this.$refs.uploader.click()
    },
    async onFileChanged(e) {
      this.isSelecting = true
      let formData = new FormData()
      formData.append('file', e.target.files[0])
      formData.append('shipCode', this.shipCode)
      const { errorRaw, data } = await this.postAsync(
        '/system/file/upload',
        formData,
      )
      if (!errorRaw) {
        this.items.push({
          ...data,
          fileSize: data.fileSize / 1024,
          initial: false,
          isDelete: false,
        })
      }
      this.isSelecting = false
    },
    delAttachment(item) {
      this.items.find((i) => i.id === item.id).isDelete = true
    },
    cancelDel(item) {
      this.items.find((i) => i.id === item.id).isDelete = false
    },
  },

  created() {},
}
</script>

<style></style>
