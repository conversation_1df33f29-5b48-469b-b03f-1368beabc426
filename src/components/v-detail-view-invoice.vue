<template>
  <v-card>
    <v-card-title
      class="primary white--text py-1 my-0 title-sticky d-print-none"
    >
      {{ title }}
      <v-spacer></v-spacer>
      <slot name="titlebtns">
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <v-btn
          v-if="canSave && !hideBtn"
          width="80"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          :loading="loading"
        >
          保存为草稿
        </v-btn>
        <v-btn
          v-if="canSubmit"
          width="80"
          tile
          @click="submit"
          color="success"
          small
          class="mx-1"
          :loading="sloading"
        >
          保存并提交
        </v-btn>
        <v-btn
          v-if="canPrint"
          width="60"
          tile
          @click="printElement(title)"
          color="primary"
          small
          class="mx-1"
        >
          打印
        </v-btn>
        <v-btn
          v-if="canSaveFile"
          tile
          color="warning"
          small
          class="mx-1"
          @click="saveFile"
          :loading="quoteLoading"
          v-permission="['费用凭证:保存附件']"
        >
          <span class="mdi mdi-file-arrow-up-down-outline"></span>
          保存附件
        </v-btn>
        <v-btn
          v-if="canConfirm"
          tile
          color="error"
          small
          class="mx-1"
          @click="startProcess"
          :loading="quoteLoading"
          v-permission="['费用凭证:实际申请人确认']"
        >
          <v-icon left>mdi-check</v-icon>
          实际申请人确认
        </v-btn>
        <v-btn
          v-if="canConfirm"
          tile
          color="error"
          small
          class="mx-1"
          @click="returnProcess"
          :loading="quoteLoading"
          v-permission="['费用凭证:退回']"
        >
          <v-icon left>mdi-check</v-icon>
          退回
        </v-btn>
        <slot name="custombtns"></slot>
      </slot>
    </v-card-title>
    <slot name="topcontent"></slot>
    <div v-for="s in subtitles" :key="s">
      <v-card-title class="h6 mt-1 ml-1 left-border">
        {{ s }}
        <v-spacer></v-spacer>
        <slot :name="`${s}按钮`"></slot>
      </v-card-title>
      <slot :name="s"></slot>
      <v-divider></v-divider>
    </div>
    <slot></slot>
    <iframe
      ref="printFrame"
      style="visibility: hidden; position: absolute"
    ></iframe>
  </v-card>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import print from '@/mixin/print'
export default {
  name: 'v-detail-view-invoice',
  mixins: [routerControl, print],
  props: {
    title: String,
    backRouteName: String,
    subtitles: Array,
    tooltip: String,
    canSubmit: [Boolean, String],
    canSave: {
      type: Boolean,
      default: true,
    },
    canPrint: [Boolean, String],
    canSaveFile: [Boolean, String],
    canConfirm: [Boolean, String],
    detail: {
      type: Object,
    },
  },
  data() {
    return {
      hideBtn: false,
      loading: false,
      sloading: false,
      quoteLoading: false,
    }
  },

  watch: {
    tooltip(tooltip) {
      this.$store.commit('updateViewTagsTooltip', {
        tooltip,
        nowFullPath: this.$route.path,
      })
    },
  },

  methods: {
    async emitPromise(method, ...params) {
      let listener =
        this.$listeners[method] || this.$attrs[method] || this[method]
      if (listener) {
        //one can additionally wrap this in try/catch if needed and handle the error further
        let res = await listener(...params)
        return res === undefined || res
      }
      return false
    },
    async save() {
      this.loading = true
      await this.emitPromise('save', () => {
        this.loading = false
        this.closeAndTo(this.backRouteName)
      })
      this.loading = false
    },
    async submit() {
      // console.log('aaaaaaaaaaaaaaaaaaaaaaaaaa，', this.detail)
      this.sloading = true
      await this.emitPromise('submit', () => {
        this.sloading = false
        this.closeAndTo(this.backRouteName)
      })
      // await this.emitPromise('submit')
      this.sloading = false
    },
    updateTooltip(tooltip) {
      this.$store.commit('updateViewTagsTooltip', {
        tooltip,
        nowFullPath: this.$route.path,
      })
    },
    async saveFile() {
      console.log('aaaaaaaaaaaaaaaaaaaaaaaaaa，', this.detail)
      const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrderFile'
      const { errorRaw } = await this.postAsync(reqUrl, {
        ...this.detail,
      })
      if (!errorRaw) {
        this.$dialog.message.success(`附件保存成功`)
        this.closeAndTo(this.backRouteName)
      }
    },
    async startProcess() {
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/costOrder/process/start',
        { id: this.detail.id },
      )
      this.quoteLoading = false
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
    async returnProcess() {
      console.log('aaaaaaaaaaaaaaaaaaaaaaaaaa，', this.detail)
      this.quoteLoading = true
      // this.detail.status = 10
      // this.detail.businessStatus = '未提交'
      // const idList = this.projects.map((item) => item.id)
      const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
      const { errorRaw } = await this.postAsync(reqUrl, {
        id: this.detail.id,
        status: 10,
        businessStatus: '实际申请人退回',
        // idList,
      })
      if (errorRaw) return false
      this.quoteLoading = false
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
  },
  created() {
    this.updateTooltip(this.tooltip)
  },
}
</script>

<style>
.left-border {
  position: relative;
}
.left-border::before {
  content: '';
  width: 5px;
  height: 30px;
  position: absolute;
  top: 25%;
  left: 0;
  background: #3399cc;
}
.title-sticky {
  position: sticky;
  top: 85px;
  z-index: 3;
}
</style>
