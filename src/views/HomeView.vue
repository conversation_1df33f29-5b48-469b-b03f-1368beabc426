<template>
  <v-container fluid>
    <v-dialog v-model="showClearCacheDialog" persistent max-width="400">
      <v-card>
        <v-card-title class="headline primary--text">
          <v-icon left color="warning" class="mr-2">mdi-alert-circle</v-icon>
          系统提示：
        </v-card-title>
        <v-card-text class="pt-4 text-body-1">
          建议您定期清除浏览器缓存，以确保系统正常运行。是否立即清除？
          <div class="caption grey--text mt-2">
            注：清除缓存可以减少系统报错的可能性
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey darken-1" text @click="skipClearCache">
            <v-icon left small>mdi-close</v-icon>
            暂不清除
          </v-btn>
          <v-btn
            color="primary"
            text
            @click="clearBrowserCache"
            :loading="clearingCache"
          >
            <v-icon left small>mdi-check</v-icon>
            确认清除
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-sheet class="py-1 my-0" outlined>
      <v-row>
        <v-col v-if="!isAdmin" cols="6">
          <v-btn
            outlined
            tile
            color="primary"
            class="mx-1 mb-4"
            @click="refresh"
            :loading="loading"
          >
            <v-icon>mdi-cached</v-icon>
            待办任务刷新
          </v-btn>
          <v-row>
            <!-- eslint-disable vue/no-use-v-if-with-v-for -->
            <!-- <v-col cols="12"> -->
            <process-num-card
              v-if="resources.includes('首页:' + p.name)"
              v-for="(p, i) in proecesses"
              :key="i"
              :processDefinitionName="p.name"
              :req-url="p.reqUrl"
              :router-name="p.routerName"
              :router-params="p.routerParams"
            ></process-num-card>
            <!-- </v-col> -->
            <!-- <v-col
              v-if="resources.includes('首页:' + p.name)"
              v-for="(p, i) in proecesses"
              :key="i"
              cols="4"
            >
              <process-num-card
                :processDefinitionName="p.name"
                :req-url="p.reqUrl"
                :router-name="p.routerName"
                :router-params="p.routerParams"
              ></process-num-card>
            </v-col> -->
          </v-row>
        </v-col>
        <v-col>
          <v-row>
            <v-col>
              <v-table-searchable
                class="elevation-3"
                ref="table"
                table-name="待办任务"
                v-model="selected"
                :headers="headers"
                req-url="/flow/task/getRunTaskListNoJW"
                :showSelect="false"
                :fix-header="false"
                item-key="taskId"
                outlined
              >
                <template v-slot:[`item.operate`]="{ item }">
                  <v-btn
                    outlined
                    dense
                    color="info"
                    tile
                    small
                    @click="openDetail(item)"
                  >
                    查看详情
                  </v-btn>
                </template>
              </v-table-searchable>
            </v-col>
            <v-col v-if="resources.includes('首页:船岸提醒')">
              <v-table-searchable
                class="elevation-3"
                ref="table"
                table-name="船岸提醒"
                v-model="selected"
                :headers="massageHeaders"
                :showSelect="false"
                req-url="/business/common/ship/taskpromptmassage/page"
                :fix-header="false"
                item-key="id"
                @click="update"
                @dblclick="update"
                outlined
              >
                <template v-slot:[`item.businessName`]="{ item }">
                  <router-link
                    :to="{
                      name: item.route,
                      params: { id: item.businessId },
                      //this.$router.resolve({
                      //  name: item.processDefinitionName,
                      //}).route.meta.permissionId,
                      //'dept-report-detail',
                      //params: { id: item.systemReportId },
                    }"
                  >
                    {{ item.businessName }}
                  </router-link>
                </template>
              </v-table-searchable>
            </v-col>
          </v-row>
        </v-col>
        <v-col v-if="resources.includes('首页:待处理报表')" cols="12">
          <v-table-searchable
            outlined
            table-name="待处理报表"
            :headers="reportHeaders"
            req-url="/business/seaAffairs/deptReport/getFirstPageMsg"
          ></v-table-searchable>
        </v-col>
      </v-row>
    </v-sheet>
  </v-container>
</template>

<script>
import processNumCard from './home/<USER>/process-num-card.vue'
// @ is an alias to /src
// import HelloWorld from '@/components/HelloWorld.vue'

export default {
  components: { processNumCard },
  name: 'HomeView',
  created() {
    // businessKey	业务标识	string
    // claimTime	接收时间	string
    // curTaskUserId	当前操作人	string
    // curTaskUserName	当前操作人名称	string
    // dueDate	到期时间	string
    // formId	业务表id	string
    // priority	优先级	integer
    // processDefinitionName	流程定义名称	string
    // processInstanceId	流程实例id	string
    // processInstanceName	流程实例名称	string
    // startTime	发起时间	string
    // startUserId	发起人id	string
    // startUserName	发起人名称	string
    // tableName	业务表标识	string
    // taskId	任务id	string
    // taskName	任务名称	string
    this.headers = [
      // { text: '业务id', value: 'processInstanceId' },
      { text: '业务', value: 'processDefinitionName' },
      { text: '当前节点', value: 'taskName' },
      { text: '发起人', value: 'startUserName' },
      { text: '发起时间', value: 'startTime' },
      { text: '操作', value: 'operate' },
    ]
    this.massageHeaders = [
      { text: '业务类型', value: 'businessName' },
      { text: '船舶', value: 'shipName' },
      { text: '业务详情', value: 'businessDesc' },
      { text: '发起人', value: 'inputPerson' },
      { text: '更新时间', value: 'inputDate' },
    ]
    this.reportHeaders = [
      { text: '船舶', value: 'shipInfo' },
      { text: '报表', value: 'formName' },
      { text: '周期', value: 'period' },
    ]
    this.searchDate = {
      label: '发布时间',
      value: 'pubTime',
    }

    this.isAdmin = this.$local.data
      .get('userInfo')
      .resources?.includes('ROLE_ADMIN')

    this.proecesses = [
      {
        name: '本轮资料申请单',
        reqUrl: '/business/seaAffairs/shipMaterials/listApply',
        routerName: 'ship-apply-list',
      },
      {
        name: '船舶内审(2月内到期)',
        reqUrl: '/business/seaAffairs/innerAudit/page?remindType=0',
        routerName: 'ship-audit-inner-list',
        routerParams: { remindType: 0 },
      },
      {
        name: '船舶内审(1月内到期)',
        reqUrl: '/business/seaAffairs/innerAudit/page?remindType=1',
        routerName: 'ship-audit-inner-list',
        routerParams: { remindType: 1 },
      },
      {
        name: '中间审核',
        reqUrl: '/business/seaAffairs/outAudit/page?remindType=0',
        routerName: 'ship-audit-outter-list',
        routerParams: { remindType: 0 },
      },
      {
        name: '换证审核',
        reqUrl: '/business/seaAffairs/outAudit/page?remindType=1',
        routerName: 'ship-audit-outter-list',
        routerParams: { remindType: 1 },
      },
      // { name: '事故人员伤亡报告' },
      // { name: '海损机损事故报告' },

      { name: '备件申请', routerName: 'spare-apply-list' },
      {
        name: '备件申请待完结',
        routerName: 'spare-apply-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/componentApplyPage?status=3&isEnd=false',
      },
      { name: '备件采购询价开标流程', routerName: 'spare-enquiry-list' },
      {
        name: '备件询价待开标(机务)',
        routerName: 'spare-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=01&status=1&applyType!=5&businessStatus=超期,报价完成,重新定标',
        routerParams: {
          status: '1',
          businessStatus: '超期,报价完成,重新定标',
        },
      },
      {
        name: '备件询价待开标(通导)',
        routerName: 'spare-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=01&status=1&applyType=5&businessStatus=超期,报价完成,重新定标',
        routerParams: {
          status: '1',
          businessStatus: '超期,报价完成,重新定标',
        },
      },
      {
        name: '备件询价单-重新发起询价(机务)',
        routerName: 'spare-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=01&status=1&applyType!=5&businessStatus=超期无报价,机务主管退回',
        routerParams: {
          status: '',
          businessStatus: '超期无报价,机务主管退回,通导信息主管退回',
          // applyType: '1',
        },
      },
      {
        name: '备件询价单-重新发起询价(通导)',
        routerName: 'spare-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=01&status=1&applyType=5&businessStatus=超期无报价,通导信息主管退回',
        routerParams: {
          status: '',
          businessStatus: '超期无报价,机务主管退回,通导信息主管退回',
          // applyType: '1',
        },
      },
      {
        name: '备件询价待开标(商务)',
        routerName: 'spare-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=01&status=1&applyType!=5&businessStatus=待商务主管定标',
        routerParams: {
          status: '1',
          businessStatus: '待商务主管定标',
        },
      },
      {
        name: '备件询价待开标(通导采购)',
        routerName: 'spare-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=01&status=1&applyType=5&businessStatus=待商务主管定标',
        routerParams: {
          status: '1',
          businessStatus: '待商务主管定标',
        },
      },
      {
        name: '备件询价已驳回(机务)',
        routerName: 'spare-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage1?enquiryType=01&status=4&applyType!=5',
        routerParams: {
          status: '4',
          businessStatus: '',
        },
      },
      {
        name: '备件询价已驳回(通导)',
        routerName: 'spare-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage1?enquiryType=01&status=4&applyType=5',
        routerParams: {
          status: '4',
          businessStatus: '',
        },
      },
      {
        name: '物料询价已驳回(机务)',
        routerName: 'materials-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage1?enquiryType=02&status=4',
        routerParams: {
          status: '4',
          businessStatus: '',
        },
      },
      {
        name: '备件订单待确认',
        routerName: 'spare-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1000,100991&orderType=01&applyType!=5',
      },
      {
        name: '通导备件订单待确认',
        routerName: 'spare-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1000,100991&orderType=01&applyType=5',
      },
      { name: '备件订单入库', routerName: 'spare-in-list' },
      {
        name: '备件订单待入库',
        routerName: 'spare-in-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1001,1002&orderType=01',
      },
      {
        name: '备件调减入库待审批',
        routerName: 'spare-in-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage1?status=2&inoutMode=1&inoutType=0&inoutNature=0&businessStatus=财务对账负责人',
        routerParams: {
          status: '2',
          inoutMode: '1',
        },
      },
      {
        name: '物料调减入库待审批',
        routerName: 'materials-in-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage1?status=2&inoutMode=1&inoutType=0&inoutNature=1&businessStatus=财务对账负责人',
        routerParams: {
          status: '2',
          inoutMode: '1',
        },
      },
      {
        name: '滑油调减入库待审批',
        routerName: 'soil-in-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage1?status=2&inoutMode=1&inoutType=0&inoutNature=2&businessStatus=财务对账负责人',
        routerParams: {
          status: '2',
          inoutMode: '1',
        },
      },
      { name: '备件消耗', routerName: 'spare-out-list' },
      { name: '备件库存盘点流程', routerName: 'stock-check-list' },
      { name: '滑油申请', routerName: 'soil-apply-list' },
      {
        name: '滑油申请待完结',
        routerName: 'soil-apply-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/greaseApplyPage?status=3&isEnd=false',
      },
      { name: '滑油采购询价开标流程', routerName: 'soil-enquiry-list' },
      {
        name: '滑油询价待开标',
        routerName: 'soil-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=03&status=1&businessStatus=超期,报价完成,重新定标',
        routerParams: {
          status: '1',
          businessStatus: '超期,报价完成,重新定标',
        },
      },
      {
        name: '滑油订单待确认',
        routerName: 'soil-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1000,100991&orderType=03',
      },
      {
        name: '滑油订单待确认(采购主管)',
        routerName: 'soil-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=100111&orderType=03',
      },
      {
        name: '滑油订单入库',
        routerName: 'soil-in-list',
        // reqUrl:
        //   '/business/shipAffairs/purchaseManage/purchaseStockInOutPage?status=2&isMe=true&inoutNature=2&inoutType=0',
      },
      {
        name: '滑油订单待入库',
        routerName: 'soil-in-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1002&orderType=03',
      },
      { name: '滑油消耗', routerName: 'soil-out-list' },
      {
        name: '滑油消耗单草稿',
        routerName: 'soil-out-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseStockInOutPage?inoutNature=2&inoutType=1&status=1',
        routerParams: {
          status: '1',
        },
      },
      { name: '滑油库存盘点流程', routerName: 'soil-stock-check-list' },
      { name: '滑油月度报表', routerName: 'grease-consum-list' },
      {
        name: '滑油月度消耗-填写提醒',
        routerName: 'soil-out-list',
        reqUrl: '/business/shipAffairs/ism/getPageOfGreaseConsumption1',
      },
      {
        name: '滑油月度报表-填写提醒',
        routerName: 'grease-consum-list',
        reqUrl: '/business/shipAffairs/ism/getPageOfGreaseConsumption2',
      },
      // { name: '滑油库存盘点流程', routerName: 'spare-out-list' },
      { name: '物料申请', routerName: 'materials-apply-list' },
      {
        name: '物料申请待完结',
        routerName: 'materials-apply-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/materialApplyPage?status=3&isEnd=false',
      },
      { name: '物料采购询价开标流程', routerName: 'materials-enquiry-list' },
      {
        name: '物料询价待开标(机务)',
        routerName: 'materials-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=02&status=1&businessStatus=超期,报价完成,重新定标',
        routerParams: {
          status: '1',
          businessStatus: '超期,报价完成,重新定标',
        },
      },
      {
        name: '物料询价待开标(商务)',
        routerName: 'materials-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=02&status=1&businessStatus=待商务主管定标',
        routerParams: {
          status: '1',
          businessStatus: '待商务主管定标',
        },
      },
      {
        name: '物料询价单-重新发起询价',
        routerName: 'materials-enquiry-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseEnquiryPage?enquiryType=02&status=1&businessStatus=超期无报价,机务主管退回',
        routerParams: {
          status: '',
          businessStatus: '超期无报价,机务主管退回',
        },
      },
      {
        name: '物料订单待确认',
        routerName: 'materials-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1000,100991&orderType=02',
      },
      {
        name: '物料订单待确认(采购主管)',
        routerName: 'materials-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=100111&orderType=02',
      },
      { name: '物料订单入库', routerName: 'materials-in-list' },
      {
        name: '物料订单待入库',
        routerName: 'materials-in-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1002&orderType=02',
      },
      { name: '物料消耗', routerName: 'materials-out-list' },
      { name: '物料库存盘点流程', routerName: 'materials-stock-check-list' },
      { name: '维护保养年度计划', routerName: 'yearplan-list' },
      {
        name: '航修申请',
        routerName: 'voyage-repair-apply-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/applyPage?status=2&isMe=true',
        routerParams: {
          status: '2',
          isMe: 'true',
        },
      },
      {
        name: '航修申请待完结',
        routerName: 'voyage-repair-apply-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/applyPage?status=3&isEnd=false',
      },
      {
        name: '航修修理单待完工',
        routerName: 'voyage-complete-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/orderPage?businessStatus=完工确认',
      },
      {
        name: '航修修理单待完工(供应商)',
        routerName: 'voyage-repair-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/orderPage?businessStatus=已安排',
      },
      {
        name: '航修修理单完工退回',
        routerName: 'voyage-repair-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/orderPage?businessStatus=完工退回',
      },
      // {
      //   name: '航修修理单完工待审核',
      //   routerName: 'voyage-repair-list',
      //   reqUrl:
      //     '/business/shipAffairs/voyageRepair/orderPage?businessStatus=完工待审核',
      // },
      {
        name: '航修询价开标审批',
        routerName: 'voyage-enquiry-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/enquiryPage?status=2&isMe=true',
        routerParams: {
          status: '2',
          isMe: 'true',
        },
      },
      {
        name: '航修询价待询价(机务)',
        routerName: 'voyage-enquiry-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/enquiryPage?status=1&repairType=0&businessStatus=待询价',
        routerParams: {
          status: '1',
          businessStatus: '待询价',
        },
      },
      {
        name: '航修询价待询价(通导)',
        routerName: 'voyage-enquiry-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/enquiryPage?status=1&repairType=1&businessStatus=待询价',
        routerParams: {
          status: '1',
          businessStatus: '待询价',
        },
      },
      {
        name: '航修询价待开标(机务)',
        routerName: 'voyage-enquiry-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/enquiryPage?status=1&repairType=0&businessStatus=超期,报价完成,重新定标',
        routerParams: {
          status: '1',
          businessStatus: '超期,报价完成,重新定标',
          // repairType: '0',
        },
      },
      // {
      //   name: '航修询价待开标(商务)',
      //   routerName: 'voyage-enquiry-list',
      //   reqUrl:
      //     '/business/shipAffairs/voyageRepair/enquiryPage?status=1&repairType=0&businessStatus=待商务主管定标',
      //   routerParams: {
      //     status: '1',
      //     businessStatus: '待商务主管定标',
      //     // repairType: '0',
      //   },
      // },
      {
        name: '航修询价待开标(通导)',
        routerName: 'voyage-enquiry-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/enquiryPage?enquiryType=01&status=1&repairType=1&businessStatus=超期,报价完成,重新定标',
        routerParams: {
          status: '1',
          businessStatus: '超期,报价完成,重新定标',
          // repairType: '1',
        },
      },
      // {
      //   name: '航修询价待开标(通导采购)',
      //   routerName: 'voyage-enquiry-list',
      //   reqUrl:
      //     '/business/shipAffairs/voyageRepair/enquiryPage?enquiryType=01&status=1&repairType=1&businessStatus=待商务主管定标',
      //   routerParams: {
      //     status: '1',
      //     businessStatus: '待商务主管定标',
      //     // repairType: '1',
      //   },
      // },
      {
        name: '航修询价单-重新发起询价(机务)',
        routerName: 'voyage-enquiry-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/enquiryPage?status=1&repairType=0&businessStatus=超期无报价,机务主管退回',
        routerParams: {
          status: '1',
          businessStatus: '超期无报价,机务主管退回,通导信息主管退回',
          // repairType: '0',
        },
      },
      {
        name: '航修询价单-重新发起询价(通导)',
        routerName: 'voyage-enquiry-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/enquiryPage?status=1&repairType=1&businessStatus=超期无报价,通导信息主管退回',
        routerParams: {
          status: '1',
          businessStatus: '超期无报价,机务主管退回,通导信息主管退回',
          // repairType: '0',
        },
      },
      // {
      //   name: '航修修理单待确认',
      //   routerName: 'voyage-repair-list',
      //   reqUrl:
      //     '/business/shipAffairs/voyageRepair/orderPage?businessStatus=待确认&repairType=机务项目',
      //   routerParams: {
      //     businessStatus: '待确认',
      //     repairType: '机务项目',
      //   },
      // },
      // {
      //   name: '航修修理单待确认(通导)',
      //   routerName: 'voyage-repair-list',
      //   reqUrl:
      //     '/business/shipAffairs/voyageRepair/orderPage?businessStatus=待确认&repairType=通导项目',
      //   routerParams: {
      //     businessStatus: '待确认',
      //     repairType: '通导项目',
      //   },
      // },
      //{ name: '航修修理单审批', routerName: 'voyage-repair-list' },
      {
        name: '航修完工单审批',
        routerName: 'voyage-complete-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/completionPage?status=2&isMe=true',
        routerParams: {
          status: '2',
          isMe: 'true',
        },
      },
      {
        name: '航修完工单待确认',
        routerName: 'voyage-complete-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/completionPage?businessStatus=已提交待确认',
        routerParams: {
          businessStatus: '已提交待确认',
        },
      },
      {
        name: '航修完工单草稿',
        routerName: 'voyage-complete-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/completionPage?businessStatus=草稿',
        routerParams: {
          businessStatus: '草稿',
        },
      },
      { name: '自修申请', routerName: 'self-repair-list' },
      { name: '自修奖申请', routerName: 'self-repair-bonus' },
      {
        name: '自修奖分配待确认',
        routerName: 'self-bonus-distribute-list',
        reqUrl:
          '/business/shipAffairs/repairBonus/pageList?businessStatus=COMPLETE',
        routerParams: {
          businessStatus: 'COMPLETE',
        },
      },
      { name: '坞修申请', routerName: 'dock-repair-apply-list' },
      {
        name: '坞修申请待完结',
        routerName: 'dock-repair-apply-list',
        reqUrl: '/dockRepairApply/getPageOfApply?status=3&isEnd=false',
      },
      {
        name: '坞修修理单待完工',
        routerName: 'dock-complete-list',
        reqUrl: '/dockRepairApply/getPageOfOrder?&businessStatus=已安排',
      },
      { name: '坞修询价开标审批', routerName: 'dock-enquiry-list' },
      { name: '坞修修理单审批', routerName: 'dock-repair-list' },
      { name: '坞修增量工程审批', routerName: 'dock-repair-add-list' },
      { name: '坞修完工单审批', routerName: 'dock-complete-list' },
      { name: '坞修结算单审批', routerName: 'dock-settlement-list' },
      { name: '坞修年度计划审批', routerName: 'dock-plan-list' },
      {
        name: '坞修待发起决算',
        routerName: 'dock-final-accounts-list',
        reqUrl: '/dockRepairApply/getPageOfAccounts?businessStatus=决算未提交',
      },
      { name: '费用凭证审批', routerName: 'cost-voucher-list' },
      {
        name: '费用凭证审批(待确认)',
        routerName: 'cost-voucher-list',
        reqUrl:
          '/business/shipAffairs/costOrder/page?status=99&auditFlag=true&isMe=true&applyPerson=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: 99,
          applyPerson: this.$local.data.get('userInfo').id,
        },
      },
      {
        name: '发票审批(草稿)',
        routerName: 'cost-voucher-list',
        reqUrl:
          '/business/shipAffairs/costOrder/page?auditFlag=true&status=10&businessStatus=未提交&manager=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: 10,
          manager: this.$local.data.get('userInfo').id,
          businessStatus: '未提交',
        },
      },
      {
        name: '发票审批(实际申请人退回)',
        routerName: 'cost-voucher-list',
        reqUrl:
          '/business/shipAffairs/costOrder/page?auditFlag=true&status=10&businessStatus=实际申请人退回&manager=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: 10,
          manager: this.$local.data.get('userInfo').id,
          businessStatus: '实际申请人退回',
        },
      },

      // { name: '常规供应审批', routerName: 'common-purchase-list' },
      { name: '单次预算管理审批', routerName: 'large-purchase-list' },
      {
        name: '预算管理审批(待确认)',
        routerName: 'large-purchase-list',
        reqUrl:
          '/business/shipAffairs/supplyCommon/page?businessStatus=待实际申请人确认&applicantId=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: '1',
          businessStatus: '待实际申请人确认',
          applicantId: this.$local.data.get('userInfo').id,
        },
      },
      {
        name: '预算管理审批(废弃确认)',
        routerName: 'large-purchase-list',
        reqUrl:
          '/business/shipAffairs/supplyCommon/page?businessStatus=待单船财务确认废弃&managerId=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: '3',
          businessStatus: '待单船财务确认废弃',
        },
      },
      {
        name: '预算管理审批(对账负责人)',
        routerName: 'large-purchase-list',
        reqUrl:
          '/business/shipAffairs/supplyCommon/page?businessStatus=对账负责人&managerId=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: '2',
          businessStatus: '对账负责人',
        },
      },
      { name: '批量预算管理审批', routerName: 'batch-cost-list' },
      {
        name: '批量预算发票待提交',
        routerName: 'batch-cost-list',
        reqUrl:
          '/business/shipAffairs/batchCostApply/page?status=3&businessStatus=预算数据已生成,费用项目、费用凭证已部分生成&auditFlag=true',
        routerParams: {
          status: '3',
          businessStatus: '预算数据已生成,费用项目、费用凭证已部分生成',
        },
      },
      { name: '供应商准入', routerName: 'supplier-in-list' },
      {
        name: '年度预算调整',
        routerName: 'budget-year-list',
        routerParams: { status: '2' },
      },
      {
        name: '年度费用预算(待确认)',
        routerName: 'budget-year-list',
        reqUrl: '/business/shipAffairs/budgetYear/page?status=0',
        routerParams: { status: '0' },
      },
      {
        name: '付款审批待采购主管确认',
        routerName: 'cost-payment-list',
        reqUrl:
          '/business/shipAffairs/costPay/page?status=35&managerCg=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: 35,
          managerCg: this.$local.data.get('userInfo').id,
        },
      },
      {
        name: '付款审批待发送OA',
        routerName: 'cost-payment-list',
        reqUrl:
          '/business/shipAffairs/costPay/page?status=36&manager=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: 36,
          manager: this.$local.data.get('userInfo').id,
        },
      },
      { name: '普通船员入职信息审批', routerName: 'crew-entry-information' },
      {
        name: '高级船员入职信息审批',
        routerName: 'crew-entry-information',
      },
      {
        name: '船员入职审核',
        routerName: 'entry-management',
        reqUrl: '/business/crew/registration/list?checkFlag=false',
      },
      {
        name: '甲板部在船船员职务晋升流程',
        routerName: 'on-broad-postion-up-list',
      },
      {
        name: '轮机部在船船员职务晋升流程',
        routerName: 'on-broad-postion-up-list',
      },
      {
        name: '上船前实际工资审批（未超过浮动）',
        routerName: 'list-boat-management',
      },
      {
        name: '上船前实际工资审批（超过浮动）',
        routerName: 'list-boat-management',
      },
      { name: '船长调配岗位面试', routerName: 'interview-management' },

      { name: '轮机长调配岗位面试', routerName: 'interview-management' },
      {
        name: '甲板部高级船员调配岗位面试',
        routerName: 'interview-management',
      },
      {
        name: '甲板部普通船员调配岗位面试',
        routerName: 'interview-management',
      },
      {
        name: '轮机部高级船员调配岗位面试',
        routerName: 'interview-management',
      },
      {
        name: '轮机部普通船员调配岗位面试',
        routerName: 'interview-management',
      },

      // { name: '安全检查', routerName: 'security-check-list' },
      { name: '安全检查', routerName: 'security-check-list-new' },
      { name: '船舶事故报告', routerName: 'accident-list-new' },
      // { name: '月度结算账单' },
      // { name: '（部门报表）岸基文件-岸基人员熟悉职责/培训记录' },
      // { name: '（部门报表）岸基文件-SMS文件培训记录簿' },

      { name: '补充备用金', routerName: 'spare-money-supply-list' },
      { name: '备用金-更改船长信息', routerName: 'captain-change-list' },
      { name: '消耗备用金', routerName: 'spare-money-consume-list' },

      // 供应商首页提醒
      {
        name: '备件报价',
        routerName: 'spare-quote-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseQuotePage?businessStatus=未填报,填报中&quoteType=01',
      },
      {
        name: '备件订单待发货',
        routerName: 'spare-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1001&orderType=01',
      },
      {
        name: '滑油报价',
        routerName: 'soil-quote-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseQuotePage?businessStatus=未填报,填报中&quoteType=03',
      },
      {
        name: '滑油订单待报价',
        routerName: 'soil-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=999&orderType=03',
      },
      {
        name: '滑油订单待发货',
        routerName: 'soil-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1001&orderType=03',
      },
      {
        name: '物料报价',
        routerName: 'materials-quote-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseQuotePage?businessStatus=未填报,填报中&quoteType=02',
      },
      {
        name: '物料订单待报价',
        routerName: 'materials-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=999&orderType=02',
      },
      {
        name: '物料订单待发货',
        routerName: 'materials-order-list',
        reqUrl:
          '/business/shipAffairs/purchaseManage/purchaseOrderPage?businessStatus=1001&orderType=02',
      },
      {
        name: '航修报价',
        routerName: 'voyage-quote-list',
        reqUrl:
          '/business/shipAffairs/voyageRepair/quotePage?businessStatus=未填报,填报中',
      },
      // {
      //   name: '航修修理单待安排',
      //   routerName: 'voyage-repair-list',
      //   reqUrl:
      //     '/business/shipAffairs/voyageRepair/orderPage?businessStatus=已确认',
      // },
      {
        name: '备件集采待确认',
        routerName: 'component-batch-purchase-list',
        reqUrl:
          '/business/shipAffairs/componentBatchPurchase/getNotConfirmDetail?type=机务&orderType=01&userId=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          type: '机务',
          businessStatus: '待各主管确认',
        },
      },
      {
        name: '备件集采待生成订单',
        routerName: 'component-batch-purchase-list',
        reqUrl:
          '/business/shipAffairs/componentBatchPurchase/page?businessStatus=超预算待确认&orderType=01',
        routerParams: {
          type: '机务',
          businessStatus: '超预算待确认',
        },
      },
      {
        name: '通导集采待确认',
        routerName: 'component-batch-purchase-listTD',
        reqUrl:
          '/business/shipAffairs/componentBatchPurchase/getNotConfirmDetail?type=通导&orderType=01&userId=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          type: '通导',
          businessStatus: '待各主管确认',
        },
      },
      {
        name: '通导集采待生成订单',
        routerName: 'component-batch-purchase-listTD',
        reqUrl:
          '/business/shipAffairs/componentBatchPurchase/page?businessStatus=超预算待确认&orderType=01',
        routerParams: {
          type: '通导',
          businessStatus: '超预算待确认',
        },
      },
      {
        name: '物料集采待确认',
        routerName: 'material-batch-purchase-list',
        reqUrl:
          '/business/shipAffairs/componentBatchPurchase/getNotConfirmDetailMaterial?orderType=02&userId=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          businessStatus: '待各主管确认',
        },
      },
      {
        name: '物料集采待生成订单',
        routerName: 'material-batch-purchase-list',
        reqUrl:
          '/business/shipAffairs/componentBatchPurchase/page?businessStatus=待采购主管确认&orderType=02',
        routerParams: {
          businessStatus: '待采购主管确认',
        },
      },
      // {
      //   name: '航修修理单待安排',
      //   routerName: 'voyage-repair-list',
      //   reqUrl: '/business/shipAffairs/voyageRepair/orderPage?businessStatus=1',
      // },
      {
        name: '坞修报价',
        routerName: 'dock-quote-list',
        reqUrl: '/dockRepairApply/getPageOfQuote?businessStatus=未填报,填报中',
      },
      {
        name: '坞修修理单待安排',
        routerName: 'dock-repair-list',
        reqUrl: '/dockRepairApply/getPageOfOrder?businessStatus=1',
      },
      {
        name: '上船前实际工资审批（超过浮动）',
        routerName: 'actual_salary_approval2',
        reqUrl:
          '/crew-management/crew-deployment-management/list-to-be-boarded-detail/',
      },
      {
        name: '上船前实际工资审批（未超过浮动）',
        routerName: 'actual_salary_approval1',
        reqUrl:
          '/crew-management/crew-deployment-management/list-to-be-boarded-detail/',
      },
      {
        name: '上船前实际工资审批（HR发起）',
        routerName: 'actual_salary_approval3',
        reqUrl:
          '/crew-management/crew-deployment-management/list-to-be-boarded-detail/',
      },
      {
        name: '甲板部高级船员',
        routerName: 'deck_officer_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '甲板部普通船员',
        routerName: 'deck_rating_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '山东甲板部普通船员',
        routerName: 'sd_deck_rating_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '轮机部高级船员',
        routerName: 'engine_officer_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '轮机部普通船员',
        routerName: 'engine_rating_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '山东轮机部普通船员',
        routerName: 'sd_engine_rating_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '大厨',
        routerName: 'chef_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '山东大厨',
        routerName: 'sd_chef_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '轮机长',
        routerName: 'chief_engineer_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '船长',
        routerName: 'captain_interview',
        reqUrl: '/crew-management/crew-deployment-management/interview-detail/',
      },
      {
        name: '甲板部在船职务晋升申请',
        routerName: 'positionChange1',
        reqUrl: '/crew-management/business-list/on-broad-postion-up-detail/',
      },
      {
        name: '轮机部在船职务晋升申请 ',
        routerName: 'positionChange2',
        reqUrl: '/crew-management/business-list/on-broad-postion-up-detail/',
      },
      {
        name: '甲板部在船调薪申请 ',
        routerName: 'positionChange3',
        reqUrl: '/crew-management/business-list/on-broad-postion-up-detail/',
      },
      {
        name: '船员杂费申请 ',
        routerName: 'salary_item_apply1',
        reqUrl: '/crew-management/invoice-management/expense-apply-detail/',
      },
      {
        name: '船员杂费申请 ',
        routerName: 'salary_item_apply2',
        reqUrl: '/crew-management/invoice-management/expense-apply-detail/',
      },
      {
        name: '船员杂费申请 ',
        routerName: 'salary_item_apply3',
        reqUrl: '/crew-management/invoice-management/expense-apply-detail/',
      },
      {
        name: '船员杂费申请 ',
        routerName: 'salary_item_apply4',
        reqUrl: '/crew-management/invoice-management/expense-apply-detail/',
      },
      {
        name: '船员杂费申请 ',
        routerName: 'salary_item_apply5',
        reqUrl: '/crew-management/invoice-management/expense-apply-detail/',
      },
      {
        name: '船员杂费申请 ',
        routerName: 'salary_item_apply6',
        reqUrl: '/crew-management/invoice-management/expense-apply-detail/',
      },
      {
        name: '船员杂费申请 ',
        routerName: 'salary_item_apply7',
        reqUrl: '/crew-management/invoice-management/expense-apply-detail/',
      },
      {
        name: '船员杂费对外支付申请 ',
        routerName: 'salary_item_external1',
        reqUrl:
          '/crew-management/invoice-management/expense-items-external-detail/',
      },
      {
        name: '轮机部在船调薪申请 ',
        routerName: 'positionChange4',
        reqUrl: '/crew-management/business-list/on-broad-postion-up-detail/',
      },
      {
        name: '山东甲板部在船船员职务晋升流程 ',
        routerName: 'positionChange5',
        reqUrl: '/crew-management/business-list/on-broad-postion-up-detail/',
      },
      {
        name: '山东轮机部在船船员职务晋升流程 ',
        routerName: 'positionChange6',
        reqUrl: '/crew-management/business-list/on-broad-postion-up-detail/',
      },
      {
        name: '山东轮机部在船船员职务晋升流程 ',
        routerName: 'positionChange7',
        reqUrl: '/crew-management/business-list/on-broad-postion-up-detail/',
      },
      {
        name: '山东甲板部在船船员职务晋升流程 ',
        routerName: 'positionChange7',
        reqUrl: '/crew-management/business-list/on-broad-postion-up-detail/',
      },
      {
        name: '安全检查审批',
        routerName: 'securityCheck',
        reqUrl:
          '/ism-management/ship-new-security/ship-new-security-detail-new/',
      },
      // {
      //   name: '安全检查报表审批',
      //   routerName: 'securityCheckForm',
      //   reqUrl: '/maritime-affairs/ship-dept-security-check/',
      // },
      {
        name: '不符合报告审批',
        routerName: 'nonConformanceReportNew',
        reqUrl:
          '/ism-management/ship-new-security/dept-report-info-detail1-new/',
      },
      {
        name: 'PSC/FSC检查',
        routerName: 'deptReportInspection',
        reqUrl:
          '/ism-management/ship-new-security/ship-new-security-dept-report-inspection-detail-new/',
      },
      {
        name: '管理复查审批',
        routerName: 'manageRecheck',
        reqUrl: '/ism-management/ship-management/detail/',
      },
      {
        name: '船舶安全自查结果汇总审批',
        routerName: 'ism_ShipSafetySelf',
        reqUrl:
          '/ism-management/ship-new-security/ship-new-safety-self-detail/',
      },
      {
        name: '船舶事故审批',
        routerName: 'shipAccident',
        reqUrl: '/ism-management/ship-new-accident/',
      },
      {
        name: '海务主管日常工作审批',
        routerName: 'marsupDailyWork',
        reqUrl: '/maritime-affairs/daily-work/detail/',
      },
      {
        name: '险情报告审批',
        routerName: 'nearMissReportNew',
        reqUrl:
          '/ism-management/ship-new-accident/dept-report-info-near-miss-detail/',
      },
      {
        name: '船员健康记录工作审批',
        routerName: 'crewHealthInformationForm',
        reqUrl:
          '/ism-management/crew-management/crew-health-information-detail/',
      },
      {
        name: '船况评估报告工作审批',
        routerName: 'ismShipConditionAssessmentReport',
        reqUrl:
          '/ism-management/ship-management/ship-condition-assessment-report-detail/',
      },
      {
        name: '船年度培训计划审批',
        routerName: 'ismAnnualTrainShipPlan',
        reqUrl:
          '/ism-management/crew-onship-train/annual-train-ship-plan-detail/',
      },
      {
        name: '月度检查清单审批',
        routerName: 'ismMonthCheckList',
        reqUrl: '/ism-management/accident-prevent/month-check-list-detail/',
      },
      {
        name: '单次预算(草稿)',
        routerName: 'large-purchase-list',
        reqUrl:
          '/business/shipAffairs/supplyCommon/page?status=1&dataSource=单次预算&applicantId=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: '1',
          applicantId: this.$local.data.get('userInfo').id,
          dataSource: '单次预算',
        },
      },
      {
        name: '单次预算(审批通过未生成费用)',
        routerName: 'large-purchase-list',
        reqUrl:
          '/business/shipAffairs/supplyCommon/page?status=3&dataSource=单次预算&isGenCost=false&applicantId=' +
          this.$local.data.get('userInfo').id,
        routerParams: {
          status: '3',
          applicantId: this.$local.data.get('userInfo').id,
          isGenCost: false,
          dataSource: '单次预算',
        },
      },
      { name: '机务主管日常工作报告', routerName: 'ship-daily-work-list' },
      {
        name: '机务主管日常工作报告(待提交)',
        routerName: 'ship-daily-work-list',
        reqUrl: '/business/shipAffairs/dailyWork/page?userType=机务主管',
      },
    ]
    this.checkCacheClearTime()
  },
  data() {
    return {
      selected: false,
      queue: [], // 定义请求队列
      resources: this.$local.data.get('userInfo').resources || [],
      routes1: this.$router.options.routes,
      showClearCacheDialog: false,
      clearingCache: false,
    }
  },
  watch: {
    async selected() {
      //单击事件处理方法
      this.update()
      // this.detailInfo = val
    },
    // openCard(val) {
    //   this.$refs.table.disabled = val
    // },
  },
  methods: {
    update() {
      // alert('1')
      //目前无法精准定位到具体提醒记录，只能跳转至列表界面，首页失效提醒功能暂时屏蔽（2023年11月15日10:55:46）
      // this.updateTaskPromptMassage(this.selected.businessId)
    },
    async updateTaskPromptMassage(id) {
      console.log(this.selected)
      // console.log(this.selected.id)
      // console.log(id)
      // let id = this.selected.id
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      await this.$refs.table.loadTableData() //刷新列表方法
      // this.closeForm()
    },
    openDetail(item) {
      console.log('item', item)
      const tableName = this.getReqUrlByRouterName(item.tableName)
      if (!tableName) {
        this.$dialog.message.error(`${item.tableName}表单不存在`)
        return
      }
      const realUrl = tableName + item.formId
      console.log('realUrl', realUrl)
      if (realUrl) {
        const baseUrl = window.location.origin
        window.location.href = baseUrl + realUrl
      }
    },

    getReqUrlByRouterName(routerName) {
      // 查找与给定routerName匹配的项目
      console.log(routerName)
      console.log(this.proecesses)
      const process = this.proecesses.find((p) => p.routerName === routerName)
      // 如果找到了匹配的项目，返回其reqUrl，否则返回空字符串或者null
      return process ? process.reqUrl : ''
    },
    refresh() {
      location.reload()
      // this.$parent.$parent.$parent.refresh(null, this.$options.name)
      // console.log(1111111111111)
      // this.$store.commit('removeKeepLive', 'HomeView')
      // this.temp = Math.random(1000)
      // setTimeout(() => {
      //   this.temp = ''
      //   this.$store.commit('pushKeepLive', 'HomeView')
      // }, 0)
      // console.log(2222222222)
    },
    async checkCacheClearTime() {
      if (!this.isShip) return
      try {
        const { errorRaw, data } = await this.getAsync('/getUserInfo1')
        if (errorRaw) return
        const lastClearTime = new Date(data).getTime()
        //console.log('lastClearTime:', lastClearTime)
        const currentTime = new Date().getTime()
        //console.log('currentTime:', currentTime)
        const oneDayInMs = 24 * 60 * 60 * 1000
        this.showClearCacheDialog = currentTime - lastClearTime > oneDayInMs
      } catch (error) {
        console.error('获取缓存清除时间失败:', error)
        this.showClearCacheDialog = false
      }
    },
    async clearBrowserCache() {
      this.clearingCache = true
      const { errorRaw } = await this.getAsync('/getUserInfo2', {
        data: new Date().toISOString(),
      })

      if (errorRaw) {
        throw new Error('缓存清除时间保存失败')
      }
      try {
        if (window.caches) {
          const keys = await window.caches.keys()
          await Promise.all(keys.map((key) => caches.delete(key)))
        }
        localStorage.clear()
        sessionStorage.clear()
        this.$dialog.message.success('浏览器缓存已清除,请重新登陆')
        this.showClearCacheDialog = false
      } catch (error) {
        console.error('清除缓存失败:', error)
        this.$dialog.message.error('清除缓存失败，请手动清除浏览器缓存')
      } finally {
        this.clearingCache = false
      }
    },

    // 跳过清除缓存
    async skipClearCache() {
      this.showClearCacheDialog = false
      this.$dialog.message.info('建议您定期清除浏览器缓存，以确保系统正常运行')
      const { errorRaw } = await this.getAsync('/getUserInfo2', {
        data: new Date().toISOString(),
      })
      if (errorRaw) {
        throw new Error('缓存清除时间保存失败')
      }
    },
  },
  computed: {
    isShip() {
      return this.$local.data.get('userInfo').isShipSyS
    },
  },
}
</script>
