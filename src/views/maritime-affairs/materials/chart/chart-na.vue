<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-4">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }} {{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-subtitle class="pb-0 mb-1">手动录入</v-card-subtitle>
      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2" v-for="(h, i) in headers" :key="i">
                <v-text-field
                  v-if="h.value === 'editionDate'"
                  v-model="chart[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
                <v-text-field
                  v-else-if="h.value === 'price'"
                  v-model="chart[h.value]"
                  :label="h.text"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="chart[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['海军图:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      :single-select="false"
      :search-remain="searchObj"
      @dbclick="editChart"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            v-model="searchObj.drawingNo"
            label="图书编号"
            outlined
            dense
            append-icon="mdi-magnify"
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="!haveSelect"
          @click="apply"
          v-permission="['本轮资料清单-图:申请更新']"
        >
          <v-icon left>mdi-update</v-icon>
          船端申请
        </v-btn>
        <v-import-btn
          import-url="/business/seaAffairs/materials-chart/materials-navy-chart/saveOrUpdate"
          @importSuccess="importSuccess"
          v-permission="['海军图:导入EXCEL']"
        ></v-import-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="formShow = true"
          v-permission="['海军图:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delChart"
          v-permission="['海军图:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'materials-chart-na',
  created() {
    this.tableName = '海军图'
    this.reqUrl =
      '/business/seaAffairs/materials-chart/materials-navy-chart/page'
    this.headers = [
      { text: '序号', value: 'number' },
      { text: '图书标号', value: 'drawingNo' },
      { text: '名称', value: 'title' },
      { text: '出版时间（年、月）', value: 'editionDate' },
      { text: '版次', value: 'edition' },
      { text: '图书类型', value: 'type' },
      { text: '单价（元）', value: 'price', hideShip: true },
      { text: '创建日期', value: 'createTime' },
      { text: '更新日期', value: 'updateTime' },
    ]
    this.isShip = this.$local.data.get('userInfo').isShipSyS
  },
  data() {
    return {
      selected: false,
      searchObj: {},
      chart: {},
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        yyyymm: (v) =>
          /^\d{4}-\d{2}$/.test(v) || '请输入正确的年月格式，如：2020-01',
        number: (v) => /^\d+$/.test(v) || '请输入数字',
      },
      isEdit: false,
      loading: false,
      formShow: false,
    }
  },
  computed: {
    haveSelect() {
      return this.selected && this.selected.length > 0
    },
  },

  methods: {
    async apply() {
      const { selected } = this
      if (!this.isShip) {
        this.$dialog.message.error('岸端无法操作')
        return
      }

      if (!this.haveSelect) {
        this.$dialog.message.error('请至少选择一条记录')
        return
      }
      if (
        selected.filter(
          (item) => item.newEditionFlag || item.newEditionFlag === null,
        ).length > 0
      ) {
        this.$dialog.message.error('只能选择已过期的记录')
        return
      }
      const applyList = selected.map((item) => {
        return {
          recordId: item.id,
          shipId: this.$local.data.get('userInfo').shipId,
          type: 20,
        }
      })
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/shipMaterials/writeRecord',
        applyList,
      )
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
      } else {
        this.$dialog.message.success('添加成功,请注意检查更新')
        this.selected = []
      }
    },
    async delChart() {
      if (!this.haveSelect) {
        this.$dialog.message.error('请至少选择一条记录')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      // const { errorRaw } = await this.getAsync(
      //   '/business/seaAffairs/materials-chart/materials-navy-chart/delete',
      //   { id: this.selected.id },
      //   false,
      // )
      //批量删除
      const idList = this.selected.map((item) => item.id)
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/materials-book/materials-navy-chart/deleteBatch',
        // { idList: this.selected.id },
        idList,
        // false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editChart() {
      this.chart = { ...this.selected[0] }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      this.selected = []
      this.selected = false
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/materials-chart/materials-navy-chart/update'
        : '/business/seaAffairs/materials-chart/materials-navy-chart/save'
      const { errorRaw } = await this.postAsync(reqUrl, this.chart, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.chart = {}
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
      await this.$nextTick()
    },
    closeForm() {
      this.$refs.form.reset()
      this.chart = {}
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
