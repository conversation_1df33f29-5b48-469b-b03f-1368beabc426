<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-4">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}英版图书
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-subtitle class="pb-0 mb-1">手动录入</v-card-subtitle>
      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="book.code"
                  label="图书编号"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="book.number"
                  label="序号"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="book.edition"
                  type="number"
                  label="版次"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="book.chTitle"
                  label="中文名称"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="book.enTitle"
                  label="英文名称"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field v-model="book.remark" label="备注"></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['英版书:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      :single-select="false"
      :search-remain="searchObj"
      @dbclick="editBook"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :hidden="!isShip"
          :disabled="!haveSelect && !isShip"
          @click="apply"
          v-permission="['本轮资料清单-书:申请更新']"
        >
          <v-icon left>mdi-update</v-icon>
          船端申请
        </v-btn>
        <v-import-btn
          import-url="/business/seaAffairs/materials-book/materials-en-book/saveOrUpdate"
          @importSuccess="importSuccess"
          v-permission="['英版书:导入EXCEL']"
        ></v-import-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="formShow = true"
          v-permission="['英版书:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delBook"
          v-permission="['英版书:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'materials-book-en',
  created() {
    this.tableName = '英版书'
    this.reqUrl = '/business/seaAffairs/materials-book/materials-en-book/page'
    this.headers = [
      { text: '序号', value: 'number' },
      { text: '中文名', value: 'chTitle' },
      { text: '英文名', value: 'enTitle' },
      { text: '编码', value: 'code' },
      { text: '版本', value: 'edition' },
      { text: '创建日期', value: 'createTime' },
      { text: '更新日期', value: 'updateTime' },
    ]
    this.isShip = this.$local.data.get('userInfo').isShipSyS
  },

  data() {
    return {
      selected: false,
      searchObj: {},
      book: {
        code: '',
        title: '',
        number: '',
        year: '',
        price: '',
        remark: '',
      },
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        int: (v) => /^[0-9]*$/.test(v) || '请输入整数',
      },
      isEdit: false,
      loading: false,
      formShow: false,
    }
  },
  computed: {
    haveSelect() {
      return this.selected && this.selected.length > 0
    },
  },

  methods: {
    async apply() {
      const { selected } = this
      if (!this.isShip) {
        this.$dialog.message.error('岸端无法操作')
        return
      }
      if (!this.haveSelect) {
        this.$dialog.message.error('请至少选择一条记录')
        return
      }
      if (
        selected.filter(
          (item) => item.newEditionFlag || item.newEditionFlag === null,
        ).length > 0
      ) {
        this.$dialog.message.error('只能选择已过期的记录')
        return
      }
      const applyList = selected.map((item) => {
        return {
          recordId: item.id,
          shipId: this.$local.data.get('userInfo').shipId,
          type: 11,
        }
      })
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/shipMaterials/writeRecord',
        applyList,
      )
      if (errorRaw) {
        return
      } else {
        this.$dialog.message.success('添加成功,请注意检查更新')
        this.selected = []
      }
    },
    async delBook() {
      if (!this.haveSelect) {
        this.$dialog.message.error('请至少选择一条记录')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      // const { errorRaw } = await this.getAsync(
      //   '/business/seaAffairs/materials-book/materials-en-book/delete',
      //   { id: this.selected.id },
      //   false,
      // )
      //批量删除
      const idList = this.selected.map((item) => item.id)
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/materials-book/materials-en-book/deleteBatch',
        // { idList: this.selected.id },
        idList,
        // false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editBook() {
      this.book = { ...this.selected[0] }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      this.selected = []
      this.selected = false
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/materials-book/materials-en-book/update'
        : '/business/seaAffairs/materials-book/materials-en-book/save'
      const { errorRaw } = await this.postAsync(reqUrl, this.book, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.book = {
        code: '',
        chTitle: '',
        enTitle: '',
        number: '',
        edition: '',
        remark: '',
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
      await this.$nextTick()
    },
    closeForm() {
      this.$refs.form.reset()
      this.book = {
        code: '',
        chTitle: '',
        enTitle: '',
        number: '',
        edition: '',
        remark: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
