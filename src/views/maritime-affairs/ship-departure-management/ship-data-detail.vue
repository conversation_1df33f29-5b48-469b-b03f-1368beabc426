<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['发船资料:保存']"
      :title="titleName"
      :tooltip="titleName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template v-slot:基础信息>
        <v-card-text>
          <v-form ref="form">
            <v-container class="px-0 py-0">
              <v-row>
                <v-col cols="12" md="2">
                  <v-ship-select
                    label="船舶名称"
                    outlined
                    dense
                    use-id
                    :rules="[rules.required]"
                    v-model="detailInfo.shipId"
                  ></v-ship-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="经办人"
                    outlined
                    dense
                    :rules="[rules.required]"
                    v-model="detailInfo.handler"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="年度"
                    outlined
                    dense
                    :rules="[rules.required]"
                    v-model="detailInfo.year"
                  ></v-text-field>
                </v-col>
                <!-- <v-col cols="12" md="2">
                  <vs-date-picker
                    label="发船日期"
                    outlined
                    dense
                    :rules="[rules.required]"
                    v-model="detailInfo.setoffTime"
                  ></vs-date-picker>
                </v-col> -->
              </v-row>
              <v-row>
                <v-col>
                  <v-textarea
                    dense
                    outlined
                    label="备注"
                    v-model="detailInfo.remark"
                  ></v-textarea>
                </v-col>
              </v-row>
              <!-- <v-attach-list
                :attachments="detailInfo.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list> -->
            </v-container>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:发船资料详细信息按钮 v-if="newCard !== `new`">
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="createModule"
          v-permission="['发船资料的详细信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectItem"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editModule"
          v-permission="['发船资料的详细信息:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectItem"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delModule"
          v-permission="['发船资料的详细信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:发船资料详细信息>
        <v-table-list
          ref="table"
          :headers="headers"
          :items="items"
          v-model="selectItem"
          @dbclick="editModule"
        >
          <template v-slot:[`item.materialType`]="{ item }">
            {{ getLabel(item.materialType) }}
          </template>
          <template v-slot:[`item.shipConfirm`]="{ item }">
            <v-btn
              v-if="item.shipConfirm !== null"
              x-small
              color="primary"
              elevation="0"
              @click="confirmInfomnew(item)"
              v-permission="['发船资料的详细信息:编辑']"
            >
              {{ item.shipConfirm == 0 ? '尚未确认' : '已确认' }}
            </v-btn>
          </template>
          <template v-slot:[`item.directorConfirm`]="{ item }">
            <v-btn
              x-small
              color="primary"
              elevation="0"
              v-if="item.directorConfirm == 0"
              @click="updirectorConfirmnew(item)"
              v-permission="['发船资料的详细信息:尚未确定']"
            >
              尚未确定
            </v-btn>
            <v-btn
              x-small
              color="primary"
              elevation="0"
              v-else
              v-permission="['发船资料的详细信息:已确定']"
            >
              已确定
            </v-btn>
          </template>
        </v-table-list>
      </template>
      <v-attach-list
        class="mt-2"
        :attachments="detailInfo.attachmentRecords"
        @change="changeAttachment"
      ></v-attach-list>
    </v-detail-view>
    <v-dialog v-model="expand" hide-overlay max-width="1100">
      <v-card>
        <v-card-title>
          详情信息---{{ flag === 0 ? '新增' : '修改' }}
          <v-spacer></v-spacer>
          <v-icon @click="close">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="2">
                  <v-dict-select
                    label="资料类型"
                    dict-type="materialsSetoff_type"
                    v-model="ChildDetail.materialType"
                  ></v-dict-select>
                </v-col>
                <v-col cols="12" md="4">
                  <v-text-field
                    label="资料内容"
                    outlined
                    dense
                    required
                    v-model="ChildDetail.materialContent"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="4">
                  <v-text-field
                    label="资料备注"
                    required
                    outlined
                    dense
                    v-model="ChildDetail.materialRemark"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    block
                    @click="upDetailNew"
                    v-permission="['资料内容:保存']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    保存
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogold" width="500" hide-overlay>
      <v-card>
        <v-card-title>
          船端反馈填写
          <!-- <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon> -->
        </v-card-title>
        <v-card-text>
          <v-form>
            <v-textarea
              label="反馈信息"
              v-model="confirmDTO.shipFeedback"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-btn
            @click="upData"
            outlined
            tile
            color="success"
            class="mx-1"
            block
            v-permission="['船端反馈填写:提交']"
          >
            提交
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <ship-info-edit-dialog
      v-model="dialog"
      :initialData="initialData"
      @success="success"
    ></ship-info-edit-dialog>
    <ship-confirm-dialog
      v-model="dialogFeedback"
      :initialData="confirmDTO"
      @confimeSuccess="confimeSuccess"
    ></ship-confirm-dialog>
  </v-container>
</template>
<script>
import shipInfoEditDialog from './shipInfo-edit-dialog.vue'
import ShipConfirmDialog from './shipConfirm-dialog.vue'
export default {
  components: { shipInfoEditDialog, ShipConfirmDialog },
  name: 'ship-data-detail',
  created() {
    this.backRouteName = 'ship-data-list'
    this.subtitles = ['基础信息', '发船资料详细信息']
    this.headers = [
      { text: '资料类型', value: 'materialType' },
      { text: '资料内容', value: 'materialContent', width: 350 },
      { text: '资料备注', value: 'materialRemark' },
      { text: '发船日期', value: 'setoffTime' },
      { text: '经办人', value: 'handler' },
      // { text: '附件', value: 'attachmentRecords' },
      { text: '船端是否确认', value: 'shipConfirm' },
      { text: '船端反馈内容', value: 'shipFeedback' },
      { text: '船端确认信息', value: 'shipConfirmDesc' },
      { text: '主管是否确认', value: 'directorConfirm' },
      { text: '主管确认信息', value: 'directorConfirmDesc' },
    ]
    this.newCard = this.$route.params.id
  },
  data() {
    return {
      dialog: false,
      dialogFeedback: false,
      selected: false,
      expand: false,
      dialogold: false,
      flag: 0,
      items: [],
      dictMap: [],
      initialData: {},
      ChildDetail: {},
      confirmDTO: {},
      selectItem: false,
      detailInfo: {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        attachmentRecords: [],
      },
      titleName: '新增发船资料',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  methods: {
    algorithm() {
      let abc = [
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'g',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z',
      ]
      let [max, min] = [
        Math.floor(Math.random() * (10 - 7 + 1) + 1),
        Math.floor(Math.random() * (17 - 10 + 1) + 17),
      ]
      abc = abc
        .sort(() => 0.4 - Math.random())
        .slice(max, min)
        .slice(0, 8)
        .join('')
      let a = new Date().getTime() + abc
      return a
    },
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    getLabel(value) {
      return this.dictMap.find((item) => item.dictValue == value)?.dictLabel
    },
    open() {
      this.ChildDetail = {}
      this.flag = 0
      this.selected = false
      this.expand = !this.expand
    },
    editElectronicchart() {
      this.flag = 1
      this.expand = !this.expand
      let materialType = this.selected.materialType
      this.ChildDetail = {
        ...this.selected,
        materialType: String(materialType),
      }
    },
    close() {
      this.expand = false
      this.ChildDetail = {}
    },
    // closeForm() {
    //   this.$emit('change', false)
    // },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.$route.params.id === `new`) {
        const { errowRaw } = await this.postAsync(
          `/business/seaAffairs/materialsSetoff/saveSetoff`,
          this.detailInfo,
        )
        if (errowRaw) {
          this.$dialog.message.error(`保存失败，请重试`)
          return
        }
        this.$dialog.message.success(`保存成功`)
        goBack()
      } else {
        const { errowRaw } = await this.postAsync(
          `/business/seaAffairs/materialsSetoff/updateSetoff`,
          this.detailInfo,
        )
        if (errowRaw) {
          this.$dialog.message.error(`保存失败，请重试`)
          return
        }
        goBack()
      }
    },
    async success(val) {
      let items = []
      if (!val.id) {
        val.vid = this.algorithm()
        val.parentId = this.$route.params.id
        items.unshift(val)
        await this.saveNewInfo(items)
      } else {
        items.unshift(val)
        await this.updateNewInfo(items)
        // this.items = this.items.map((ele) => {
        //   if (ele.vid === val.vid) {
        //     return val
        //   } else {
        //     return ele
        //   }
        // })
      }
      items = []
      await this.getChildInfo()
      // await this.saveAll(2)
    },
    //添加保存类型type:1为点击保存按钮保存并关闭编辑界面 2位发船资料详情保存，只刷新详情列表不关闭编辑界面
    async saveAll(type) {
      if (type == 1 && !this.$refs.form.validate()) {
        this.$dialog.message.error('有必填项未填写！')
        return
      }
      // if (await this.save()) {
      //   let saveItems = this.items.filter((ele) => !ele.id)
      //   saveItems.forEach((ele) => {
      //     ele.parentId = this.$route.params.id
      //     return ele
      //   })
      //   let updateItems = this.items.filter((ele) => ele.id)
      //   await this.saveNewInfo(saveItems)
      //   await this.updateNewInfo(updateItems)
      //   // this.selected.id = this.idParam
      //   if (type == 1) {
      //     this.selectItem = false
      //     // this.selected = false
      //     // this.openCard = false
      //     await this.$refs.table.loadTableData()
      //   } else {
      //     // if (!this.selected) {
      //     //   this.selected = this.detailInfo
      //     //   this.selected.id = this.idParam
      //     // }
      //     // this.selectItem = false
      //     // await this.$refs.table.loadTableData()
      //     // await this.getDetailInfo(this.idParam)
      //     // await this.getDetailInfo()
      //     await this.getChildInfo()
      //     await this.$refs.table.loadTableData()
      //     // this.isEdit = false
      //     this.$dialog.message.success('保存成功')
      //   }
      // } else {
      //   this.$dialog.message.error('保存失败')
      //   return
      // }
      let saveItems = this.items.filter((ele) => !ele.id)
      saveItems.forEach((ele) => {
        ele.parentId = this.$route.params.id
        return ele
      })
      let updateItems = this.items.filter((ele) => ele.id)
      await this.saveNewInfo(saveItems)
      await this.updateNewInfo(updateItems)
      // this.selected.id = this.idParam
      if (type == 1) {
        this.selectItem = false
        // this.selected = false
        // this.openCard = false
        await this.$refs.table.loadTableData()
      } else {
        // if (!this.selected) {
        //   this.selected = this.detailInfo
        //   this.selected.id = this.idParam
        // }
        // this.selectItem = false
        // await this.$refs.table.loadTableData()
        // await this.getDetailInfo(this.idParam)
        // await this.getDetailInfo()
        await this.getChildInfo()
        await this.$refs.table.loadTableData()
        // this.isEdit = false
        this.$dialog.message.success('保存成功')
      }
    },
    async saveNewInfo(items) {
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/materialsSetoff/saveSetoffRecord`,
        items,
      )
      if (errowRaw) {
        return false
      }
      return true
    },
    async updateNewInfo(items) {
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/materialsSetoff/updateSetoffRecord`,
        items,
      )
      if (errowRaw) {
        return false
      }
      return true
    },
    async getDetailInfo() {
      if (this.$route.params.id !== `new`) {
        const { errowRaw, data } = await this.getAsync(
          `/business/seaAffairs/materialsSetoff/getSetoff/${this.$route.params.id}`,
        )
        if (errowRaw) {
          return
        }
        this.titleName =
          data.shipName +
          '---' +
          // data.records[0].setoffTime +
          data.year +
          '---发船资料'
        this.detailInfo = data
        this.updateTaskPromptMassage(this.$route.params.id)
      }
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    async getParentInfo() {
      if (this.$route.params.id !== `new`) {
        const { errowRaw, data } = await this.getAsync(
          `/business/seaAffairs/materialsSetoff/pageListSetoff`,
          { id: this.$route.params.id },
        )
        if (errowRaw) {
          return
        }
        this.titleName =
          data.records[0].shipName +
          '---' +
          // data.records[0].setoffTime +
          data.records[0].year +
          '---发船资料'
        this.detailInfo = data.records[0]
      }
    },
    async getChildInfo() {
      const { errowRaw, data } = await this.getAsync(
        `/business/seaAffairs/materialsSetoff/searchChild/${this.$route.params.id}`,
      )
      if (errowRaw) {
        this.$dialog.message.error(`发船资料的详情请求失败`)
        return
      }
      this.items = data
    },
    async getDictMap() {
      const url = `/system/dict-data/getDictDataListByDictType`
      const { data, errowRaw } = await this.getAsync(url, {
        dictType: `materialsSetoff_type`,
      })
      if (!errowRaw) {
        this.dictMap = data
      }
    },
    async upDetailNew() {
      const items = []
      if (!this.selectItem) {
        // if (!this.selected) {
        items.push({ parentId: this.$route.params.id, ...this.ChildDetail })
        // const { errowRaw } = await this.postAsync(
        //   `/business/seaAffairs/materialsSetoff/saveSetoffRecord`,
        //   { parentId: this.$route.params.id, ...this.ChildDetail },
        // )
        const { errowRaw } = await this.postAsync(
          `/business/seaAffairs/materialsSetoff/saveSetoffRecord`,
          items,
        )
        if (errowRaw) {
          this.$dialog.message.error(`发船资料的详情新增失败`)
          return
        }
        this.expand = false
        this.selectItem = false
        this.selected = false
        this.ChildDetail = {}
        await this.getChildInfo()
      } else {
        items.push({ parentId: this.$route.params.id, ...this.ChildDetail })
        // const { errowRaw } = await this.postAsync(
        //   `/business/seaAffairs/materialsSetoff/updateSetoffRecord`,
        //   { parentId: this.$route.params.id, ...this.ChildDetail },
        // )
        const { errowRaw } = await this.postAsync(
          `/business/seaAffairs/materialsSetoff/updateSetoffRecord`,
          items,
        )
        if (errowRaw) {
          this.$dialog.message.error(`发船资料的详情新增失败`)
          return
        }
        this.expand = false
        this.selected = false
        this.selectItem = false
        this.ChildDetail = {}
        await this.getChildInfo()
      }
    },
    // async upDetailNewDialog(items) {
    //   // const items = []
    //   if (!this.selectItem) {
    //     // if (!this.selected) {
    //     items.push({ parentId: this.$route.params.id, ...this.ChildDetail })
    //     // const { errowRaw } = await this.postAsync(
    //     //   `/business/seaAffairs/materialsSetoff/saveSetoffRecord`,
    //     //   { parentId: this.$route.params.id, ...this.ChildDetail },
    //     // )
    //     const { errowRaw } = await this.postAsync(
    //       `/business/seaAffairs/materialsSetoff/saveSetoffRecord`,
    //       items,
    //     )
    //     if (errowRaw) {
    //       this.$dialog.message.error(`发船资料的详情新增失败`)
    //       return
    //     }
    //     this.expand = false
    //     this.selectItem = false
    //     this.selected = false
    //     this.ChildDetail = {}
    //     await this.getChildInfo()
    //   } else {
    //     items.push({ parentId: this.$route.params.id, ...this.ChildDetail })
    //     // const { errowRaw } = await this.postAsync(
    //     //   `/business/seaAffairs/materialsSetoff/updateSetoffRecord`,
    //     //   { parentId: this.$route.params.id, ...this.ChildDetail },
    //     // )
    //     const { errowRaw } = await this.postAsync(
    //       `/business/seaAffairs/materialsSetoff/updateSetoffRecord`,
    //       items,
    //     )
    //     if (errowRaw) {
    //       this.$dialog.message.error(`发船资料的详情新增失败`)
    //       return
    //     }
    //     this.expand = false
    //     this.selected = false
    //     this.selectItem = false
    //     this.ChildDetail = {}
    //     await this.getChildInfo()
    //   }
    // },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errowRaw } = await this.getAsync(
        `/business/seaAffairs/materialsSetoff/deleteSetoffRecord`,
        { id: this.selected.id },
      )
      if (errowRaw) {
        this.$dialog.message.error(`删除失败, 请重试`)
        return
      }
      this.selected = false
      this.$dialog.message.success(`删除成功`)
      await this.getChildInfo()
    },
    confirmInfom(item) {
      if (item.directorConfirm == 1) {
        this.$dialog.message.error(`主管已确认，船端反馈无法修改`)
        return
      }
      this.dialog = true
      this.confirmDTO = item
    },
    confirmInfomnew(item) {
      if (!item.id) {
        this.$dialog.message.error('该条记录还未提交，暂不可确认')
        return
      }
      if (item.directorConfirm == 1) {
        this.$dialog.message.error(`主管已确认，船端反馈无法修改`)
        return
      }
      this.confirmDTO = { ...item }
      this.dialogFeedback = true
    },
    async confimeSuccess() {
      await this.getChildInfo()
    },
    async updirectorConfirm(item) {
      if (item.shipConfirm == 0) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/materialsSetoff/directorConfirm`,
        { ...item, directorConfirm: 1 },
      )
      if (errowRaw) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      this.$dialog.message.success(`确认成功`)
      await this.getChildInfo()
    },
    async updirectorConfirmnew(item) {
      if (!item.id) {
        this.$dialog.message.error('该条记录还未提交，暂不可确认')
        return
      }
      if (item.shipConfirm == 0) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/materialsSetoff/directorConfirm`,
        { ...item, directorConfirm: 1 },
      )
      if (errowRaw) {
        return
      }
      this.$dialog.message.success(`确认成功`)
      await this.getChildInfo()
    },
    createModule() {
      this.dialog = true
      this.initialData = {}
      this.initialData.isEdit = true
    },
    editModule() {
      this.dialog = true
      this.initialData = {
        ...this.selectItem,
        materialType: String(this.selectItem.materialType),
      }
    },
    async delModule() {
      if (this.selectItem?.id) {
        if (this.selectItem?.directorConfirm == 1) {
          this.$dialog.message.error(`主管已确认，船端反馈无法删除`)
          return
        }
        if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
        const { errowRaw } = await this.getAsync(
          `/business/seaAffairs/materialsSetoff/deleteSetoffRecord`,
          { id: this.selectItem.id },
        )
        if (errowRaw) {
          return
        }
      } else {
        this.items = this.items.filter((ele) => ele.vid === this.selectItem.vid)
      }
      this.$dialog.message.success('删除成功')
      this.selectItem = false
      await this.getChildInfo()
    },
    async upData() {
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/materialsSetoff/shipConfirm`,
        { ...this.confirmDTO, shipConfirm: 1 },
      )
      if (errowRaw) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      this.dialog = false
      this.selected = false
      this.$dialog.message.success(`确认成功`)
      await this.getChildInfo()
    },
  },

  async mounted() {
    // await this.getParentInfo()
    await this.getDetailInfo()
    await this.getChildInfo()
    await this.getDictMap()
  },
}
</script>

<style></style>
