<template>
  <v-container fluid>
    <v-card v-if="openCard">
      <v-card-title>
        {{ isEdit ? '新增' : '修改' }}--{{ cardTable }}
        <v-spacer></v-spacer>
        <v-icon @click="openCard = false">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="pb-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="detailInfo.shipId"
                  :rules="[rules.required]"
                  required
                  useId
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="经办人"
                  outlined
                  dense
                  v-model="detailInfo.handler"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="年度"
                  outlined
                  dense
                  v-model="detailInfo.year"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <vs-date-picker
                  label="发船日期"
                  outlined
                  dense
                  v-model="detailInfo.setoffTime"
                  :rules="[rules.required]"
                  required
                ></vs-date-picker>
              </v-col> -->
            </v-row>
            <v-row>
              <v-col cols="12" class="py-0">
                <v-textarea
                  label="备注"
                  outlined
                  rows="1"
                  auto-grow
                  dense
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
              <v-col class="py-0" cols="12">
                <v-card-text>
                  <v-attach-list
                    ship-code="0"
                    :attachments="detailInfo.attachmentRecords"
                    @change="(ids) => (detailInfo.attachmentIds = ids)"
                  ></v-attach-list>
                </v-card-text>
              </v-col>
              <v-col class="my-0 py-0" cols="12">
                <v-card-title class="pt-0">
                  <v-spacer></v-spacer>
                  <v-btn
                    outlined
                    tile
                    small
                    color="success"
                    class="mx-1"
                    @click="createModule"
                    v-permission="['发船资料:新增']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    详情列表新增
                  </v-btn>
                  <v-btn
                    :disabled="!selectItem"
                    outlined
                    small
                    tile
                    color="warning"
                    class="mx-1"
                    @click="editModule"
                    v-permission="['发船资料:修改']"
                  >
                    <v-icon left>mdi-pencil</v-icon>
                    修改
                  </v-btn>
                  <v-btn
                    :disabled="!selectItem"
                    outlined
                    small
                    tile
                    color="error"
                    class="mx-1"
                    @click="delModule"
                    v-permission="['发船资料:删除']"
                  >
                    <v-icon left>mdi-delete-empty</v-icon>
                    删除
                  </v-btn>
                </v-card-title>
                <v-table-list
                  :headers="detailHeader"
                  :items="items"
                  v-model="selectItem"
                  item-key="vid"
                >
                  <template v-slot:[`item.materialType`]="{ item }">
                    {{ getLabel(item.materialType) }}
                  </template>
                  <template v-slot:[`item.shipConfirm`]="{ item }">
                    <v-btn
                      x-small
                      color="primary"
                      elevation="0"
                      @click="confirmInfom(item)"
                      v-permission="['发船资料编辑:编辑']"
                    >
                      {{ item.shipConfirm == 0 ? '尚未确认' : '已确认' }}
                    </v-btn>
                  </template>
                  <template v-slot:[`item.directorConfirm`]="{ item }">
                    <v-btn
                      x-small
                      color="primary"
                      elevation="0"
                      v-if="item.directorConfirm == 0"
                      @click="updirectorConfirm(item)"
                      v-permission="['发船资料:尚未确定']"
                    >
                      尚未确定
                    </v-btn>
                    <v-btn
                      x-small
                      color="primary"
                      elevation="0"
                      v-else
                      v-permission="['发船资料:已确定']"
                    >
                      已确定
                    </v-btn>
                  </template>
                </v-table-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="saveAll(1)"
                  block
                  v-permission="['发船资料:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '创建' : '保存' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :searchRemain="searchRemain"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-ship-select
            v-model="searchRemain.shipId"
            outlined
            dense
            use-id
          ></v-ship-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchRemain.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" md="2">
          <v-text-field
            label="经办人"
            outlined
            dense
            required
            v-model="searchRemain.handler"
            clearable
          ></v-text-field>
        </v-col> -->
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="`/maritime-affairs/book-data/ship-departure-management/new?shipId=${
            searchRemain.shipId || ''
          }`"
          v-permission="['发船资料:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="update"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delDetail"
          v-permission="['发船资料:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
    <ship-info-edit-dialog
      v-model="dialog"
      :initialData="initialData"
      @success="success"
    ></ship-info-edit-dialog>
    <ship-confirm-dialog
      v-model="dialogFeedback"
      :initialData="confirmDTO"
      @confimeSuccess="confimeSuccess"
    ></ship-confirm-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import shipInfoEditDialog from './shipInfo-edit-dialog.vue'
import ShipConfirmDialog from './shipConfirm-dialog.vue'
export default {
  components: { shipInfoEditDialog, ShipConfirmDialog },
  name: 'ship-data-list',
  mixins: [dictHelper],
  created() {
    this.tableName = '发船资料查询'
    this.reqUrl = '/business/seaAffairs/materialsSetoff/pageListSetoff'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '年度', value: 'year' },
      // { text: '发船日期', value: 'setoffTime' },
      // { text: '经办人', value: 'handler' },
      { text: '发船资料数', value: 'sendNum' },
      { text: '船端确认数', value: 'confirmNum' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.detailHeader = [
      { text: '资料类型', value: 'materialType' },
      { text: '资料内容', value: 'materialContent', width: 400 },
      { text: '资料备注', value: 'materialRemark' },
      { text: '船端确认标记', value: 'shipConfirm' },
      { text: '船端反馈内容', value: 'shipFeedback' },
      { text: '主管确认', value: 'directorConfirm' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '发船日期',
      value: 'setoffTime',
    }
    this.pushParams = { name: 'ship-data-detail' }
  },

  data() {
    return {
      dictMap: [],
      selected: false,
      loading: false,
      searchRemain: {},
      detail: [],
      dialog: false,
      dialogFeedback: false,
      isEdit: false,
      openCard: false,
      cardTable: '发船资料信息',
      detailInfo: {
        attachmentRecords: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      idParam: '',
      items: [],
      initialData: {},
      confirmDTO: {},
      selectItem: false,
    }
  },
  watch: {
    async selected(val) {
      await this.getDetailInfo()
      this.detailInfo = val
    },
    openCard(val) {
      this.$refs.table.disabled = val
    },
  },
  methods: {
    algorithm() {
      let abc = [
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'g',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z',
      ]
      let [max, min] = [
        Math.floor(Math.random() * (10 - 7 + 1) + 1),
        Math.floor(Math.random() * (17 - 10 + 1) + 17),
      ]
      abc = abc
        .sort(() => 0.4 - Math.random())
        .slice(max, min)
        .slice(0, 8)
        .join('')
      let a = new Date().getTime() + abc
      return a
    },
    getLabel(value) {
      return this.dictMap.find((item) => item.dictValue == value)?.dictLabel
    },
    async openDetails(itemId) {
      this.loading = true
      const { errowRaw, data } = await this.getAsync(
        `/business/seaAffairs/materialsSetoff/searchChild/${itemId}`,
        {},
        false,
      )
      if (!errowRaw) {
        this.detail = data
      }
      this.loading = false
      this.dialogFlag = true
    },
    async delDetail() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errowRaw } = await this.getAsync(
        `/business/seaAffairs/materialsSetoff/deleteSetoffRecord`,
        { id: this.selected.id },
        false,
      )
      if (!errowRaw) {
        const { errowRaw } = await this.getAsync(
          `/business/seaAffairs/materialsSetoff/deleteSetoff`,
          { id: this.selected.id },
          false,
        )
        if (errowRaw) {
          this.$dialog.message.error(`删除失败，请重试`)
          return
        }
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
    addNewInfo() {
      this.openCard = true
      this.isEdit = true
      this.detailInfo = {
        handler: this.$local.data.get('userInfo').nickName,
      }
      this.items = []
    },
    update() {
      this.updateTaskPromptMassage(this.selected.id)
      this.isEdit = false
      // this.openCard = true
      this.$router.push(`/maritime-affairs/paper-chart/${this.selected.id}`)
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    async save() {
      const url = this.isEdit
        ? `/business/seaAffairs/materialsSetoff/saveSetoff`
        : `/business/seaAffairs/materialsSetoff/updateSetoff`
      const { errowRaw, data } = await this.postAsync(url, this.detailInfo)
      if (errowRaw) {
        return false
      }
      this.idParam = data
      return true
    },
    async saveNewInfo(items) {
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/materialsSetoff/saveSetoffRecord`,
        items,
      )
      if (errowRaw) {
        return false
      }
      return true
    },
    async updateNewInfo(items) {
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/materialsSetoff/updateSetoffRecord`,
        items,
      )
      if (errowRaw) {
        return false
      }
      return true
    },
    async getDetailInfo(id) {
      if (id == null || id == '') id = this.selected.id
      // const { errowRaw, data } = await this.getAsync(
      //   `/business/seaAffairs/materialsSetoff/searchChild/${this.selected.id}`,
      // )
      const { errowRaw, data } = await this.getAsync(
        `/business/seaAffairs/materialsSetoff/searchChild/${id}`,
      )
      if (errowRaw) {
        return
      }
      this.items = data
      this.items = this.items.map((ele) => {
        ele.vid = ele.id
        return ele
      })
    },
    //添加保存类型type:1为点击保存按钮保存并关闭编辑界面 2位发船资料详情保存，只刷新详情列表不关闭编辑界面
    async saveAll(type) {
      if (type == 1 && !this.$refs.form.validate()) {
        this.$dialog.message.error('有必填项未填写！')
        return
      }
      if (await this.save()) {
        let saveItems = this.items.filter((ele) => !ele.id)
        saveItems.forEach((ele) => {
          if (this.isEdit) {
            ele.parentId = this.idParam
            return ele
          } else {
            ele.parentId = this.selected.id
            return ele
          }
        })
        let updateItems = this.items.filter((ele) => ele.id)
        await this.saveNewInfo(saveItems)
        await this.updateNewInfo(updateItems)
        // this.selected.id = this.idParam
        if (type == 1) {
          this.selectItem = false
          this.selected = false
          this.openCard = false
          await this.$refs.table.loadTableData()
        } else {
          if (!this.selected) {
            this.selected = this.detailInfo
            this.selected.id = this.idParam
          }
          // this.selectItem = false
          // await this.$refs.table.loadTableData()
          // await this.getDetailInfo(this.idParam)
          await this.getDetailInfo()
          this.isEdit = false
          this.$dialog.message.success('保存成功')
        }
      } else {
        this.$dialog.message.error('保存失败')
        return
      }
    },
    async success(val) {
      if (!val.id) {
        val.vid = this.algorithm()
        this.items.unshift(val)
      } else {
        this.items = this.items.map((ele) => {
          if (ele.vid === val.vid) {
            return val
          } else {
            return ele
          }
        })
      }
      await this.saveAll(2)
    },
    createModule() {
      this.dialog = true
      this.initialData = {}
      this.initialData.isEdit = true
    },
    editModule() {
      this.dialog = true
      this.initialData = {
        ...this.selectItem,
        materialType: String(this.selectItem.materialType),
      }
    },
    async delModule() {
      if (this.selectItem?.id) {
        const { errowRaw } = await this.getAsync(
          `/business/seaAffairs/materialsSetoff/deleteSetoffRecord`,
          { id: this.selectItem.id },
        )
        if (errowRaw) {
          return
        }
      } else {
        this.items = this.items.filter((ele) => ele.vid === this.selectItem.vid)
      }
      this.$dialog.message.success('删除成功')
      this.selectItem = false
      await this.getDetailInfo()
    },
    confirmInfom(item) {
      if (!item.id) {
        this.$dialog.message.error('该条记录还未提交，暂不可确认')
        return
      }
      if (item.directorConfirm == 1) {
        this.$dialog.message.error(`主管已确认，船端反馈无法修改`)
        return
      }
      this.confirmDTO = { ...item }
      this.dialogFeedback = true
    },
    async confimeSuccess() {
      await this.getDetailInfo()
    },
    async updirectorConfirm(item) {
      if (!item.id) {
        this.$dialog.message.error('该条记录还未提交，暂不可确认')
        return
      }
      if (item.shipConfirm == 0) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/materialsSetoff/directorConfirm`,
        { ...item, directorConfirm: 1 },
      )
      if (errowRaw) {
        return
      }
      this.$dialog.message.success(`确认成功`)
      await this.getDetailInfo()
    },
  },

  async mounted() {
    this.dictMap = await this.getDictByType('materialsSetoff_type')
  },
}
</script>

<style></style>
