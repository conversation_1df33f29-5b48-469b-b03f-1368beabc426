<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ this.initialData.isEdit ? '新增' : '修改' }}---详情信息
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-dict-select
                  label="资料类型"
                  dict-type="materialsSetoff_type"
                  v-model="formData.materialType"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="资料备注"
                  required
                  outlined
                  dense
                  v-model="formData.materialRemark"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <vs-date-picker
                  label="发船日期"
                  outlined
                  dense
                  v-model="formData.setoffTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="资料内容"
                  outlined
                  dense
                  required
                  v-model="formData.materialContent"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-card-text>
                  <v-attach-list
                    :attachments="formData.attachmentRecords"
                    @change="(ids) => (formData.attachmentIds = ids)"
                  ></v-attach-list>
                </v-card-text>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ this.initialData.isEdit ? '创建' : '保存' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'shipInfo-edit-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      // const { errowRaw } = await this.postAsync(
      //   `/business/seaAffairs/materialsSetoff/saveSetoffRecord`,
      //   { parentId: this.$route.params.id, ...this.formData },
      // )
      // if (errowRaw) {
      //   this.$dialog.message.error(`发船资料的详情新增失败`)
      //   return
      // }

      if (this.initialData.isEdit) {
        this.formData.shipConfirm = 0
        this.formData.directorConfirm = 0
      }
      this.$emit('change', false)
      this.$emit('success', this.formData)
      // this.$emit()
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
