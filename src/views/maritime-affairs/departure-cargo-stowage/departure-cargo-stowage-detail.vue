<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['货物积载信息:编辑']"
      :title="`货物积载信息`"
      tooltip="货物积载信息"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        !(
          !detail.auditParams ||
          detail.auditParams.taskId ||
          detail.status === '1' ||
          detail.status === '4'
        )
      "
      :can-save="detail.status === '1' || detail.status === '4'"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <!--  -->
      <template v-slot:custombtns>
        <v-btn
          v-if="!isShip && detail.supConfirm == 0"
          width="90"
          tile
          @click="savePrompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['货物积载信息:主管提交']"
        >
          确认
        </v-btn>
        <!-- <v-btn
          v-if="isShip"
          width="90"
          tile
          @click="savePrompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['货物积载信息:船端提交']"
        >
          提交并通知岸端
        </v-btn> -->
      </template>
      <template v-if="detail.status == 3" v-slot:titlebtns>
        <v-btn
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn>
        <!-- <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['船舶检查:下载部门报表']"
        >
          报表导出
        </v-btn> -->
      </template>
      <template #基本信息>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="4">
                <v-ship-select
                  v-model="detail.shipCode"
                  readonly
                ></v-ship-select>
              </v-col>
              <v-col
                class="py-1"
                cols="12"
                md="4"
                v-for="(h, i) in 基本信息字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  :readonly="h.value === 'marineSupervisor'"
                  dense
                  outlined
                ></v-text-field>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  dense
                  :label="h.label"
                  outlined
                  :readonly="
                    (detail.inspectionType === '2' &&
                      h.value === 'nextInspectionTime') ||
                    h.value === 'inspectionTime'
                  "
                  :hidden="
                    !detail.inspectionType != '2' &&
                    (h.value === 'nextInspectionTime' ||
                      h.value === 'marineReviewDate')
                  "
                ></vs-date-picker>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #船舶离港状态及货物积载信息>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col
                class="py-1"
                cols="12"
                md="4"
                v-for="(h, i) in 船舶离港状态及货物积载信息字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  :readonly="h.value === 'marineSupervisor'"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else-if="h.type === 'number'"
                  v-model="detail[h.value]"
                  type="number"
                  :label="h.label"
                  dense
                  outlined
                ></v-text-field>
                <v-switch
                  v-else-if="h.type === 'switch1'"
                  class="mt-1"
                  dense
                  v-model="detail[h.value]"
                  :label="`${detail[h.value] ? '是' : '否'}` + h.label"
                  color="success"
                ></v-switch>
                <v-switch
                  v-else-if="h.type === 'switch11'"
                  class="mt-1"
                  dense
                  v-model="detail[h.value]"
                  :label="h.label + `${detail[h.value] ? ':是' : ':否'}`"
                  color="success"
                ></v-switch>
                <v-switch
                  v-else-if="h.type === 'switch2'"
                  class="mt-1"
                  dense
                  v-model="detail[h.value]"
                  :label="`${detail[h.value] ? '有' : '无'}` + h.label"
                  color="success"
                ></v-switch>
                <v-switch
                  v-else-if="h.type === 'switch22'"
                  class="mt-1"
                  dense
                  v-model="detail[h.value]"
                  :label="h.label + `${detail[h.value] ? ':有' : ':无'}`"
                  color="success"
                ></v-switch>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  dense
                  :label="h.label"
                  outlined
                  :readonly="
                    (detail.inspectionType === '2' &&
                      h.value === 'nextInspectionTime') ||
                    h.value === 'inspectionTime'
                  "
                  :hidden="
                    !detail.inspectionType != '2' &&
                    (h.value === 'nextInspectionTime' ||
                      h.value === 'marineReviewDate')
                  "
                ></vs-date-picker>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #船端反馈>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="12">
                <v-textarea
                  label="船端反馈"
                  dense
                  outlined
                  :readonly="!isShip"
                  v-model="detail.shipFeedback"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #主管建议>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="12">
                <v-textarea
                  label="主管建议"
                  :readonly="isShip"
                  dense
                  outlined
                  v-model="detail.supSuggest"
                ></v-textarea>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="py-1" cols="12" md="4">
                <!-- <v-switch
                  class="mt-1"
                  dense
                  v-model="detail.supStatus"
                  :label="`${detail.supStatus ? '确认' : '退回'}`"
                  :readonly="isShip"
                  color="success"
                ></v-switch> -->
                <v-btn
                  v-if="detail.status === '2'"
                  outlined
                  tile
                  @click="savePrompt1(backRouteName)"
                  color="success"
                  class="mx-1"
                  v-permission="['货物积载信息:船端提交']"
                  block
                >
                  退回
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <v-card-text>
        <v-attach-list
          :attachments="detail.attachmentRecords"
          @change="changeAttachment"
        ></v-attach-list>
      </v-card-text>
    </v-detail-view>
    <v-dialog v-model="attachmentDialog" max-width="700" hide-overlay>
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import routerControl from '@/mixin/routerControl'
export default {
  // components: {
  //   addQuestionItemNew,
  //   addWorkItemNew,
  //   addCrewItemNew,
  //   addTrackItemNew,
  //   addOtherItemNew,
  // },
  mixins: [dictHelper, routerControl],
  name: 'departure-cargo-stowage-detail',
  created() {
    this.backRouteName = 'departure-cargo-stowage-list'
    // this.subtitles = [
    //   '填写进度',
    //   '船舶基本信息',
    //   '安全检查',
    //   '检查相关内容',
    //   '确认进度',
    // ]

    this.基本信息字段 = [
      { label: '港口', value: 'port', type: 'string' },
      { label: '靠泊日期', value: 'callingDate', type: 'date' },
    ]
    this.船舶离港状态及货物积载信息字段 = [
      { label: '船首离港吃水（m）', value: 'departureDraft', type: 'number' },
      {
        label: '船尾离港吃水（m）',
        value: 'departureDraftStern',
        type: 'number',
      },
      { label: '稳性stability（m）', value: 'stability', type: 'number' },
      {
        label: '盲区（m）', //'盲区（m）：local loading',
        value: 'blindArea',
        type: 'number',
      },
      {
        label: '横摇周期（秒）', //'横摇周期：rolling period（秒）',
        value: 'rollingPeriod',
        type: 'number',
      },
      // { label: '靠离泊', value: 'berthDisembark' },
      {
        label: '弯距（%）', //'弯距bending moment（%）',
        value: 'bendingMoment',
        type: 'number',
      },
      {
        label: '剪力（%）', //'剪力：shear force（%）',
        value: 'shearForce',
        type: 'number',
      },
      {
        label: '本港装船货重（mt）', //'本港装船货重（mt）：local loading',
        value: 'cargoWeightLocalLoading',
        type: 'number',
      },
      {
        label: '货重准确', //'货重cargo weight right or not是/否准确',
        value: 'cargoWeightRightOrNot',
        type: 'switch11',
      },
      {
        label: '瞒报或超重（吨）',
        value: 'concealOrOverweight',
        type: 'number',
      },
      {
        label: '全船总货重', //'全船总货重:total cargo weight（mt）',
        value: 'totalCargoWeight',
        type: 'number',
      },
      {
        label: '冷箱', //'冷箱：reefer container sum',
        value: 'reeferContainerSum',
        type: 'number',
      },
      {
        label: '本港装冷箱', //'冷箱本港装local loading',
        value: 'reeferContainerLocalLoading',
        type: 'number',
      },
      {
        label: '冷代干', //'冷代干：dry cargo in reefer container（no power）是/否有',
        value: 'dryCargoInReeferContainer',
        type: 'switch22',
      },
      {
        label: '冷代干数量',
        value: 'dryCargoInReeferContainerSum',
        type: 'number',
      },
      // { label: '提醒方式', value: 'reminderMethod', hidden: true },
      {
        label: '危险品', //'危险品：dangerous unit',
        value: 'dangerousUnit',
        type: 'number',
      },
      {
        label: '本港装', //'本港装 :local loading',
        value: 'dangerousUnitLocalLoading',
        type: 'number',
      },
      {
        label: '危险品满足隔离要求', //'危险品满足隔离要求:isolation to IMDG是/否',
        value: 'isolationToImdg',
        type: 'switch11',
      },
      {
        label: '冷危箱:', //'冷危箱:REEFER AND DG unit是/否有',
        value: 'reeferAndDgUnit',
        type: 'switch22',
      },
      { label: '冷危箱数量', value: 'reeferAndDgUnitSum', type: 'number' },
      {
        label: '大件', //'大件（OOG-out of gauge）：有无',
        value: 'oogOutGauge',
        type: 'switch22',
      },
      { label: '大件本港装', value: 'oogOutGaugeLocalLoading', type: 'number' },
      // { label: '大件总数', value: 'oogOutGaugeSum', type: 'number' },
      {
        label: '大件目的港',
        value: 'oogOutGaugeDestinationPort',
        type: 'string',
      },
      {
        label: '卷钢', //'卷钢:coil steel有无',
        value: 'coilSteel',
        type: 'switch22',
      },
      {
        label: '装在舱内', //'是否装在舱内loading in hold',
        value: 'coilSteelLoadingInHold',
        type: 'switch11',
      },
      {
        label: '远离危险品', //'是否远离far from DG container危险品）',
        value: 'coilSteelFarFromDgContainer',
        type: 'switch11',
      },
      {
        label: '大件单独加固', //'大件 有/否 单独加固',
        value: 'oogOutGaugeIndividualReinforcement',
        type: 'switch11',
      },
      {
        label: '列总堆重符合要求', //'列总堆重: row weght是/否符合要求',
        value: 'rowWeght',
        type: 'switch11',
      },
      {
        label: '通过LASHING FORCE CHECKING', //'LASHING FORCE CHECKING是/否通过',
        value: 'lashingForceChecking',
        type: 'switch11',
      },
      {
        label: '淡水', //'开航油水存量：fuel and fresh waterof departure淡水',
        value: 'departureWater',
        type: 'number',
      },
      { label: '燃油', value: 'departureFuel', type: 'number' },
      {
        label: '将遭遇恶劣天气', //'本航次预计是/否 将遭遇恶劣天气;expected bad weather encounter or not',
        value: 'expectedBadWeather',
        type: 'switch11',
      },
      {
        label: '采取有效大风浪航行前安全检查及准备工作', //'是/否采取有效大风浪航行前安全检查及准备工作; taking safety measure of PRE-checking',
        value: 'safetyMeasurePreChecking',
        type: 'switch11',
      },
      {
        label: '采取因大风浪可能导致的货损预防措施', //'是/否采取因大风浪可能导致的货损预防措施; taking precaution measure of cargo damage by heavy sea',
        value: 'precautionMeasureHeavyseaCargoDamage',
        type: 'switch11',
      },
      // { text: '燃油', value: 'remark' },
      // { text: '燃油', value: 'remark' },
      // { text: '燃油', value: 'remark' },
    ]
    this.attachmentHeader = [
      { text: '名称', value: 'name' },
      { text: '大小(kb)', value: 'fileSize' },
      { text: '上传时间', value: 'createTime' },
      { text: '上传人', value: 'userName' },
    ]
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = [
      'info',
      'info',
      'info',
      'info',
      'warning',
      'warning',
      'warning',
      'warning',
      'warning',
      'success',
      'error',
    ]
  },
  computed: {
    // TODO:待不符合报告添加完毕，提交时将进行校验，未填写的不符合报告将不会被提交
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    isFilled() {
      return this.fillList.filter((i) => i.status == 1).length === 0
    },
    canEdit() {
      return ['1', '4'].includes(this.detail.status)
    },
    isShip() {
      //console.log(this.$local.data.get('userInfo').isShipSyS)//船端时返回true
      return this.$local.data.get('userInfo').isShipSyS
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/securityCheck/exportById?id=${this.detail.id}`
        : ''
      //''
    },
  },
  watch: {
    load() {
      console.log('open ...')
    },
  },
  data() {
    return {
      subtitles: [
        // '填写进度',
        // '船舶基本信息',
        '基本信息',
        // '安全检查',
        '船舶离港状态及货物积载信息',
        '船端反馈',
        '主管建议',
        // '需跟踪事项',
        // '其他工作',
        // '确认进度',
      ],
      detail: { status: 0, inspectionType: 0, auditParams: '' },
      fillList: [],
      // checkList: [],
      // workList: [],
      // crewList: [],
      // trackList: [],
      // otherList: [],
      // securityReport: {},
      // selectedQues: false,
      // selectedWork: false,
      // selectedCrew: false,
      // selectedTrack: false,
      // selectedOther: false,
      dialog: false,
      // dialogWork: false,
      // dialogCrew: false,
      // dialogTrack: false,
      // dialogOther: false,
      initialData: {},
      // initialWorkData: {},
      // initialCrewData: {},
      // initialTrackData: {},
      // initialOtherData: {},
      attachmentDialog: false,
      attachments: [],
    }
  },

  methods: {
    async save(goBack, notMove = false) {
      this.savePrompt(goBack, notMove)
      // if (!this.$refs.form.validate()) {
      //   return
      // }
      // const { errorRaw } = await this.postAsync(
      //   '/business/seaAffairs/DepartureStowageInformation/save',
      //   this.detail,
      // )
      // if (notMove) return this.detail.id
      // if (!errorRaw) goBack()
      goBack()
    },
    async savePrompt(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      this.detail.supStatus = true //岸端默认直接确认
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/DepartureStowageInformation/savePrompt',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) this.closeAndTo(this.backRouteName) //this.closeAndTo(this.backRouteName)
      //this.closeAndTo(this.backRouteName)
      // goBack() //this.closeAndTo(this.backRouteName)
      this.closeAndTo(this.backRouteName)
    },
    async savePrompt1(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.detail.supSuggest == '') {
        this.$dialog.message.error(`请填写主管建议`)
        return
      }
      this.detail.supStatus = false //岸端退回
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/DepartureStowageInformation/savePrompt',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) this.closeAndTo(this.backRouteName) //this.closeAndTo(this.backRouteName)
      //this.closeAndTo(this.backRouteName)
      // goBack() //this.closeAndTo(this.backRouteName)
      this.closeAndTo(this.backRouteName)
    },
    async submit(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/DepartureStowageInformation/savePrompt',
        this.detail,
      )
      if (!errorRaw) goBack()
      //this.closeAndTo(this.backRouteName)
      goBack()

      // if (!(this.$refs?.aform?.validate() ?? true)) return
      // if (
      //   (this.detail.status == '1' || this.detail.status == '4') &&
      //   !this.isFilled &&
      //   !(await this.$dialog.msgbox.confirm(
      //     '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
      //   ))
      // )
      //   return

      // const data = await this.save(goBack, true)
      // if (!data) return false
      // if (!this.detail.auditParams) {
      //   const { errorRaw } = await this.getAsync(
      //     '/business/seaAffairs/DepartureStowageInformation/process/submit',
      //     { id: data },
      //   )
      //   if (!errorRaw) goBack()
      // } else {
      //   const error = await this.$refs.audit.submit()
      //   if (!error) goBack()
      // }
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/DepartureStowageInformation/record/${this.$route.params.id}`,
      )
      this.detail = data

      this.attachmentRecords = data.attachmentRecords
      if (this.canEdit)
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船舶离港状态及货物积载信息',
          '船端反馈',
          '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      else if (this.detail.status === '2')
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船舶离港状态及货物积载信息',
          '船端反馈',
          '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      else
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船舶离港状态及货物积载信息',
          '船端反馈',
          '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      // this.updateTaskPromptMassage(this.$route.params.id)
      // await this.loadCheckList()
      // await this.loadWorkList()
      // await this.loadCrewList()
      // await this.loadTrackList()
      // await this.loadOtherList()
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
  },

  mounted() {
    this.loadDetail()
    this.updateTaskPromptMassage(this.$route.params.id)
  },
  beforeDestroy() {
    this.$store.commit('removeBussinessParam', this.detail.id)
  },
}
</script>

<style>
.scroll-content1 {
  /* position: sticky; */
  height: 400px;
  overflow-y: auto;
}
</style>
