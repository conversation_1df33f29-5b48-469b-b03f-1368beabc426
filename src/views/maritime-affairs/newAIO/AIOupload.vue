<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        最新AIO
        <v-spacer></v-spacer>
        <v-btn v-permission="['最新AIO:编辑']" color="primary" fab small>
          <v-icon @click="close" v-if="isEditing">mdi-close</v-icon>
          <v-icon @click="edit" v-else>mdi-pencil</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="newAIO.code"
                  label="AIO编号"
                  :disabled="!isEditing"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="newAIO.name"
                  label="AIO名称"
                  :disabled="!isEditing"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="newAIO.illustration"
                  label="AIO文档说明"
                  :rules="[rules.required]"
                  required
                  :disabled="!isEditing"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="newAIO.handler"
                  label="经办人"
                  required
                  :disabled="!isEditing"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="newAIO.attachmentRecords"
                  @change="changeAIOattach"
                  :disabled="!isEditing"
                ></v-attach-list>
              </v-col>
              <v-col v-show="isEditing" cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['最新AIO:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-card class="mt-2">
      <!-- <v-card-title>次新AIO</v-card-title>
      <v-card-text>
        <v-form ref="aform">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="oldAIO.code"
                  label="AIO编号"
                  :disabled="!isEditing"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="oldAIO.name"
                  label="AIO名称"
                  :rules="[rules.required]"
                  required
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="oldAIO.illustration"
                  label="AIO文档说明"
                  :rules="[rules.required]"
                  required
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="oldAIO.handler"
                  label="经办人"
                  required
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="oldAIO.attachmentRecords"
                  disabled
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text> -->
      <v-table-searchable
        ref="table"
        :table-name="tableName"
        v-model="selected"
        :fuzzy-label="fuzzyLabel"
        :headers="headers"
        :req-url="reqUrl"
        :fix-header="false"
        :search-remain="searchObj"
        :push-params="pushParams"
      ></v-table-searchable>
    </v-card>
  </v-container>
</template>
<script>
export default {
  name: 'aio-upload',
  created() {
    this.tableName = '历史AIO'
    this.reqUrl = '/business/seaAffairs/recentAio/AIOList'
    //     attachmentRecords	附件信息列表，限制最多5个文件	array	CommonAttachment
    // code	编号	string
    // handler	经办人	string
    // id	物理主键	string
    // illustration	AIO说明	string
    // name	AIO名称	string
    // remark	备注	string
    this.headers = [
      { text: '编号', value: 'code', sortable: false },
      { text: 'AIO名称', value: 'name', sortable: false },
      { text: 'AIO说明', value: 'illustration', sortable: false },
      { text: '经办人', value: 'handler', sortable: false },
      { text: '备注', value: '备注', sortable: false },
      { text: '附件', value: 'attachmentRecords', sortable: false },
    ]
  },
  data() {
    return {
      dateMenu: false,
      formData: {
        illustration: '',
        name: '',
        handle: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      },
      formDatas: [],
      files: [],
      disabled: false,
      newAIO: {},
      oldAIO: {},
      editAIO: {},
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
      finished: false,
      loading: false,
      isEditing: false,
    }
  },
  computed: {
    // 获取当前路由的参数
    // isEdit() {
    //   return this.$route.params.id
    // },
  },
  methods: {
    async upload() {
      this.loading = true
      const formData = new FormData()
      for (let i = 0; i < this.files.length; i++) {
        formData.append('files', this.files[i])
      }
      const { errorRow } = await this.postAsync(
        '/system/file/uploadFiles',
        formData,
      )
      if (errorRow) {
        return
      }
      this.loading = false
      this.finished = true
    },
    linkDownload(url) {
      window.open(url, '_blank') // 新窗口打开外链接
    },

    edit() {
      this.isEditing = true
      this.editAIO = { ...this.newAIO }
      this.newAIO = {}
      this.newAIO.handler = this.$local.data.get('userInfo').nickName
    },

    close() {
      this.isEditing = false
      this.newAIO = { ...this.editAIO }
    },

    changeAIOattach(attachmentIds) {
      this.newAIO.attachmentIds = attachmentIds
    },

    async delCircular() {
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/recentAio/deleteBatch',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      // const oldId = this.oldAIO.id
      // const newId = this.editAIO.id
      // const res1 = await this.postAsync(
      //   '/business/seaAffairs/recentAio/updateAIO',
      //   { ...this.editAIO, id: oldId },
      // )
      const res2 = await this.postAsync(
        '/business/seaAffairs/recentAio/addAIO',
        // { ...this.newAIO, id: newId },
        { ...this.newAIO },
      )
      // if (!res1.errorRaw && !res2.errorRaw) {
      if (!res2.errorRaw) {
        this.isEditing = false
        await this.loadnewAIO()
      }
    },
    async loadnewAIO() {
      const { data, errorRow } = await this.getAsync(
        '/business/seaAffairs/recentAio/AIOList',
        {},
      )
      if (!errorRow) {
        this.formDatas = data.records
        console.log(this.formDatas)
      }
      if (this.formDatas.length >= 1) {
        this.newAIO = this.formDatas[0] || {}
        this.oldAIO = this.formDatas[1] || {}
      }
    },
  },

  mounted() {
    this.loadnewAIO()
  },
}
</script>

<style></style>
