<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['海务主管日常工作:编辑']"
      :title="`海务主管日常工作-${
        ['', '未提交', '审批中', '审批通过', '驳回'][detail.status]
      }`"
      tooltip="海务主管日常工作"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-save="isSave"
      :can-submit="
        !detail.auditParams ||
        detail.auditParams.taskId ||
        detail.status === '1' ||
        detail.status === '4'
      "
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <!-- <template
        v-if="!isShip && (detail.status == 1 || detail.status == 4)"
        v-slot:custombtns
      >
        <v-btn
          width="90"
          tile
          @click="saveprompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['船舶检查:保存并通知船端']"
        >
          保存并通知船端
        </v-btn>
      </template> -->
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <v-btn
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn> -->
        <!-- <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['船舶检查:下载部门报表']"
        >
          报表导出
        </v-btn> -->
      </template>
      <template #基本信息>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col
                class="py-1"
                cols="12"
                md="2"
                v-for="(h, i) in 基本信息字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  :readonly="h.value === 'marineSupervisor'"
                  dense
                  outlined
                ></v-text-field>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  dense
                  :label="h.label"
                  outlined
                  :readonly="
                    (detail.inspectionType === '2' &&
                      h.value === 'nextInspectionTime') ||
                    h.value === 'inspectionTime'
                  "
                  :hidden="
                    !detail.inspectionType != '2' &&
                    (h.value === 'nextInspectionTime' ||
                      h.value === 'marineReviewDate')
                  "
                ></vs-date-picker>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #安全检查按钮>
        <!-- <v-btn
          v-if="!(detail.inspectionType != '1' && isShip)"
          @click="createQues"
          outlined
          :disabled="!canEdit"
          :hidden="detail.inspectionType != '1' && isShip"
          tile
          small
          color="success"
          class="mx-1"
          v-permission="['安全检查:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn> -->
        <v-btn
          :disabled="!selectedQues"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editQues"
          v-permission="['海务主管日常工作-安全检查:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedQues || !canEdit"
          outlined
          :loading="loading1"
          small
          tile
          color="success"
          class="mx-1"
          @click="saveQuesList"
          v-permission="['海务主管日常工作-安全检查:保存']"
        >
          <v-icon left>mdi-pencil</v-icon>
          保存
        </v-btn>
      </template>
      <template #安全检查>
        <!-- <v-table-list
          ref="table"
          class="scroll-content1"
          v-model="selectedQues"
          :fix-header="true"
          :headers="checkHeaders"
          :items="checkList"
        > -->
        <v-table-list-new
          v-model="selectedQues"
          :headers="checkHeaders"
          :single-select="true"
          :items="checkList"
          @dbclick="editQues"
        >
          <!-- <v-row>
                              <v-col
                                class="py-1"
                                cols="12"
                                md="2"
                                v-for="(h, i) in checkHeaders"
                                :key="i"
                              >
                                <v-switch
                                  class="mt-1"
                                  v-if="h.value == 'enterExitPort'"
                                  dense
                                  readonly
                                  v-model="item[h.value]"
                                  color="success"
                                ></v-switch>
                              </v-col>
                            </v-row> -->
          <template v-slot:[`item.enterExitPort`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.enterExitPort"
              :label="`${item.enterExitPort ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.berthDisembark`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.berthDisembark"
              :label="`${item.berthDisembark ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.antiTheftArea`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.antiTheftArea"
              :label="`${item.antiTheftArea ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.narrowWaterway`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.narrowWaterway"
              :label="`${item.narrowWaterway ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.vtsArea`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.vtsArea"
              :label="`${item.vtsArea ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.fishArea`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.fishArea"
              :label="`${item.fishArea ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.foggyArea`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.foggyArea"
              :label="`${item.foggyArea ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.badWeather`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.badWeather"
              :label="`${item.badWeather ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.keyPort`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.keyPort"
              :label="`${item.keyPort ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.keyVessel`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.keyVessel"
              :label="`${item.keyVessel ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.cargoLoad`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.cargoLoad"
              :label="`${item.cargoLoad ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.anchorReminder`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.anchorReminder"
              :label="`${item.anchorReminder ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
        </v-table-list-new>
      </template>
      <template #日常工作按钮>
        <v-btn
          @click="createWork"
          outlined
          :disabled="!canEdit"
          tile
          small
          color="success"
          class="mx-1"
          v-permission="['海务主管日常工作-日常工作:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedWork"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editWork"
          v-permission="['海务主管日常工作-日常工作:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedWork || !canEdit"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delWork"
          v-permission="['海务主管日常工作-日常工作:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #日常工作>
        <v-table-list
          v-model="selectedWork"
          :headers="workHeaders"
          :items="workList"
          @dbclick="editWork"
        ></v-table-list>
      </template>
      <template #船员管理按钮>
        <v-btn
          @click="createCrew"
          outlined
          :disabled="!canEdit"
          tile
          small
          color="success"
          class="mx-1"
          v-permission="['海务主管日常工作-船员管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedCrew"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editCrew"
          v-permission="['海务主管日常工作-船员管理:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedCrew || !canEdit"
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click="saveCrewList"
          v-permission="['海务主管日常工作-安全检查:保存']"
        >
          <v-icon left>mdi-pencil</v-icon>
          保存
        </v-btn>
        <v-btn
          :disabled="!selectedCrew || !canEdit"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCrew"
          v-permission="['海务主管日常工作-船员管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #船员管理>
        <v-table-list
          v-model="selectedCrew"
          :headers="crewHeaders"
          :items="crewList"
          @dbclick="editCrew"
        >
          <template v-slot:[`item.interview`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.interview"
              :label="`${item.interview ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.train`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.train"
              :label="`${item.train ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
          <template v-slot:[`item.crewCheck`]="{ item }">
            <v-switch
              class="mt-1"
              dense
              v-model="item.crewCheck"
              :label="`${item.crewCheck ? '适用' : '不适用'}`"
              color="success"
            ></v-switch>
          </template>
        </v-table-list>
      </template>
      <template #需跟踪事项按钮>
        <v-btn
          @click="createTrack"
          outlined
          :disabled="!canEdit"
          tile
          small
          color="success"
          class="mx-1"
          v-permission="['海务主管日常工作-需跟踪事项:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedTrack"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editTrack"
          v-permission="['海务主管日常工作-需跟踪事项:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedTrack || !canEdit"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delTrack"
          v-permission="['海务主管日常工作-需跟踪事项:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #需跟踪事项>
        <v-table-list
          v-model="selectedTrack"
          :headers="trackHeaders"
          :items="trackList"
          @dbclick="editTrack"
        ></v-table-list>
      </template>
      <template #其他工作按钮>
        <v-btn
          @click="createOther"
          outlined
          :disabled="!canEdit"
          tile
          small
          color="success"
          class="mx-1"
          v-permission="['海务主管日常工作-其他工作:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedOther"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editOther"
          v-permission="['海务主管日常工作-其他工作:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedOther || !canEdit"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delOther"
          v-permission="['海务主管日常工作-其他工作:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #其他工作>
        <v-table-list
          v-model="selectedOther"
          :headers="otherHeaders"
          :items="otherList"
          @dbclick="editOther"
        ></v-table-list>
      </template>
      <v-card-text>
        <v-attach-list
          :attachments="detail.attachmentRecords"
          @change="changeAttachment"
        ></v-attach-list>
      </v-card-text>
    </v-detail-view>
    <add-question-item-new
      v-model="dialog"
      :initialData="initialData"
      @success="questionSuccess"
    ></add-question-item-new>
    <add-work-item-new
      v-model="dialogWork"
      :initialData="initialWorkData"
      @success="workSuccess"
    ></add-work-item-new>
    <add-crew-item-new
      v-model="dialogCrew"
      :initialData="initialCrewData"
      @success="crewSuccess"
    ></add-crew-item-new>
    <add-track-item-new
      v-model="dialogTrack"
      :initialData="initialTrackData"
      @success="trackSuccess"
    ></add-track-item-new>
    <add-other-item-new
      v-model="dialogOther"
      :initialData="initialOtherData"
      @success="otherSuccess"
    ></add-other-item-new>
    <v-dialog v-model="attachmentDialog" max-width="700" hide-overlay>
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
// bonus	检查考评奖金额	number
// bonusReply	检查考评奖批复	string
// briefComments	简要评语	string
// businessStatus	业务状态	string
// captain	船长	string
// captainAutograph	船长签名	string
// checkPort	受检港口	string
// cheifEngineer	轮机长	string
// dpReview	DP审核意见	string
// dpReviewDate	DP审核日期	string(date-time)
// firstMate	大副	string
// id	物理主键	string
// inspectionProcess	检查经过	string
// inspectionTime	检查时间	string(date-time)
// inspectionType	检查类型	string
// inspector	检查人员	string
// inspectorAutograph	检查人员签名	string
// isClosed	是否结案	boolean
// marineReviewDate	海务主管审核日期	string(date-time)
// marineSupervisor	海务主管	string
// marineSupervisorAutograph	海务主管签名	string
// secondMate	二副	string
// shipCode	船舶代码	string
// status	流程状态	string
// supervisorReview	主管审核意见	string
// systemReportId	安全检查表id(部门报表id)	string
// thridMate	三副	string
// correctEvidenceIds	纠正证据（附件列表）	string
// id	物理主键	string
// isCorrected	是否纠正	boolean
// itemType	项目类别	string
// measures	措施	string
// number	序号	string
// personLiable	责任人	string
// problemDescription	问题描述	string
// questionType	问题类别(非字典) 0-无缺陷 1-缺陷项 2-建议项	string
// reportName	报表名称	string
// reportStatus	报表流程状态	string
// securityCheckId	安全检查id	string
// systemReportId	不符合报告id	string
import dictHelper from '@/mixin/dictHelper'
import addQuestionItemNew from './private/add-question-item-new.vue'
import addWorkItemNew from './private/add-work-item-new.vue'
import addCrewItemNew from './private/add-crew-item-new.vue'
import addTrackItemNew from './private/add-track-item-new.vue'
import addOtherItemNew from './private/add-other-item-new.vue'
export default {
  components: {
    addQuestionItemNew,
    addWorkItemNew,
    addCrewItemNew,
    addTrackItemNew,
    addOtherItemNew,
  },
  mixins: [dictHelper],
  name: 'daily-work-detail',
  created() {
    this.backRouteName = 'daily-work-list'
    // this.subtitles = [
    //   '填写进度',
    //   '船舶基本信息',
    //   '安全检查',
    //   '检查相关内容',
    //   '确认进度',
    // ]

    this.基本信息字段 = [
      { label: '海务主管', value: 'marineSupervisor', type: 'string' },
      { label: '记录日期', value: 'dailyWorkTime', type: 'date' },
    ]
    this.checkHeaders = [
      { text: '船名', value: 'shipName' },
      { text: '船位动态', value: 'shipPosition' },
      { text: '进出港靠离泊', value: 'enterExitPort', type: 'switch' },
      // { text: '靠离泊', value: 'berthDisembark' },
      { text: '防偷盗区域', value: 'antiTheftArea' },
      { text: '狭水道', value: 'narrowWaterway' },
      { text: 'VTS管辖区域', value: 'vtsArea' },
      { text: '商渔船密集区', value: 'fishArea' },
      { text: '雾区', value: 'foggyArea' },
      { text: '恶劣天气', value: 'badWeather' },
      { text: '有VSAT', value: 'withVsat' },
      { text: '无VSAT', value: 'noVsat' },
      { text: '违规行为', value: 'violation' },
      { text: '重点港口', value: 'keyPort' },
      { text: '重点船舶', value: 'keyVessel' },
      { text: '货物配载', value: 'cargoLoad' },
      { text: '抛锚提醒', value: 'anchorReminder' },
      { text: '备注', value: 'remark' },
      // { text: '提醒方式', value: 'reminderMethod', hidden: true },
      { text: '提醒方式', value: 'reminderMethodName', hidden: true },
    ]
    this.workHeaders = [
      { text: '序号', value: 'number' },
      { text: '船名', value: 'shipName' },
      { text: '船舶备用金', value: 'shipReserveFund' },
      { text: '订购海图', value: 'orderNauticalChart' },
      { text: '文档管理存档', value: 'documentManagementArchive' },
    ]
    this.crewHeaders = [
      { text: '序号', value: 'number' },
      { text: '船名', value: 'shipName' },
      { text: '姓名', value: 'name' },
      { text: '船员职务', value: 'crewPost' },
      { text: '职务', value: 'post' },
      { text: '面试', value: 'interview' },
      { text: '培训', value: 'train' },
      { text: '考核', value: 'crewCheck' },
      { text: '日期', value: 'operationDate' },
      { text: '备注', value: 'remark' },
    ]
    this.trackHeaders = [
      { text: '序号', value: 'number' },
      { text: '船名', value: 'shipName' },
      { text: '跟踪事项', value: 'trackingMatter' },
    ]
    this.otherHeaders = [
      { text: '序号', value: 'number' },
      { text: '其他事项内容', value: 'content' },
    ]
    this.reportHeaders = [
      { text: '报表名称', value: 'reportName' },
      { text: '上传人', value: 'poster' },
      { text: '上传时间', value: 'postTime' },
      { text: '状态', value: 'status' },
    ]
    this.attachmentHeader = [
      { text: '名称', value: 'name' },
      { text: '大小(kb)', value: 'fileSize' },
      { text: '上传时间', value: 'createTime' },
      { text: '上传人', value: 'userName' },
    ]
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = [
      'info',
      'info',
      'info',
      'info',
      'warning',
      'warning',
      'warning',
      'warning',
      'warning',
      'success',
      'error',
    ]
  },
  computed: {
    // TODO:待不符合报告添加完毕，提交时将进行校验，未填写的不符合报告将不会被提交
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    isFilled() {
      return this.fillList.filter((i) => i.status == 1).length === 0
    },
    canEdit() {
      return ['1', '4'].includes(this.detail.status)
    },
    isSave() {
      return this.$route.params.id == 'new'
        ? true
        : this.detail.status == '0' ||
          this.detail.status == '1' ||
          this.detail.status == '' ||
          this.detail.status == '4' ||
          this.detail.status == null
        ? true
        : false
    },
    isShip() {
      //console.log(this.$local.data.get('userInfo').isShipSyS)//船端时返回true
      return this.$local.data.get('userInfo').isShipSyS
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/securityCheck/exportById?id=${this.detail.id}`
        : ''
      //''
    },
  },
  watch: {
    load() {
      console.log('open ...')
    },
  },
  data() {
    return {
      subtitles: [
        // '填写进度',
        // '船舶基本信息',
        '基本信息',
        '安全检查',
        '日常工作',
        '船员管理',
        '需跟踪事项',
        '其他工作',
        // '确认进度',
      ],
      detail: { status: 0, inspectionType: 0, auditParams: '' },
      fillList: [],
      checkList: [],
      workList: [],
      crewList: [],
      trackList: [],
      otherList: [],
      securityReport: {},
      selectedQues: false,
      selectedWork: false,
      selectedCrew: false,
      selectedTrack: false,
      selectedOther: false,
      dialog: false,
      dialogWork: false,
      dialogCrew: false,
      dialogTrack: false,
      dialogOther: false,
      loading1: false,
      initialData: {},
      initialWorkData: {},
      initialCrewData: {},
      initialTrackData: {},
      initialOtherData: {},
      attachmentDialog: false,
      attachments: [],
    }
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/updateDetail',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) goBack()
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (
        (this.detail.status == '1' || this.detail.status == '4') &&
        !this.isFilled &&
        !(await this.$dialog.msgbox.confirm(
          '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return

      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/MarsupDailyWork/process/submit',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/MarsupDailyWork/record/${this.$route.params.id}`,
      )
      this.detail = data

      this.attachmentRecords = data.attachmentRecords
      if (this.canEdit)
        this.subtitles = [
          '基本信息',
          '安全检查',
          '日常工作',
          '船员管理',
          '需跟踪事项',
          '其他工作',
        ]
      else if (this.detail.status === '2')
        this.subtitles = [
          '基本信息',
          '安全检查',
          '日常工作',
          '船员管理',
          '需跟踪事项',
          '其他工作',
        ]
      else
        this.subtitles = [
          '基本信息',
          '安全检查',
          '日常工作',
          '船员管理',
          '需跟踪事项',
          '其他工作',
        ]

      await this.loadCheckList()
      await this.loadWorkList()
      await this.loadCrewList()
      await this.loadTrackList()
      await this.loadOtherList()
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    createQues() {
      this.initialData = {
        shipCode: this.detail.shipCode,
        problemPhotos: [],
        correctEvidences: [],
        detailStatus: this.detail.status,
      }
      this.dialog = true
    },
    editQues() {
      console.log(this.selectedQues)
      this.initialData = {
        ...this.selectedQues,
        detailStatus: this.detail.status,
      }
      this.dialog = true
    },
    editQues1(item) {
      console.log(item)
      this.initialData = {
        ...item,
        detailStatus: this.detail.status,
      }
      this.dialog = true
    },
    async saveQues() {
      const url = '/business/seaAffairs/MarsupDailyWork/detail/addCheck'
      const { errorRaw } = await this.postAsync(url, {
        ...this.selectedQues,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        this.$dialog.message.success(`保存成功`)
      }
      this.questionSuccess()
    },
    async saveQuesList() {
      const url = '/business/seaAffairs/MarsupDailyWork/detail/addCheckList'
      this.loading1 = true
      const { errorRaw } = await this.postAsync(url, this.checkList)
      this.loading1 = false
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        this.$dialog.message.success(`保存成功`)
      }
      this.questionSuccess()
    },
    createWork() {
      this.initialWorkData = {
        shipCode: this.detail.shipCode,
        problemPhotos: [],
        correctEvidences: [],
        detailStatus: this.detail.status,
      }
      this.dialogWork = true
    },
    editWork() {
      this.initialWorkData = {
        ...this.selectedWork,
        detailStatus: this.detail.status,
      }
      this.dialogWork = true
    },
    createCrew() {
      this.initialCrewData = {
        shipCode: this.detail.shipCode,
        problemPhotos: [],
        correctEvidences: [],
        detailStatus: this.detail.status,
      }
      this.dialogCrew = true
    },
    editCrew() {
      this.initialCrewData = {
        ...this.selectedCrew,
        detailStatus: this.detail.status,
      }
      this.dialogCrew = true
    },
    async saveCrew() {
      const url = '/business/seaAffairs/MarsupDailyWork/detail/addCrew'
      const { errorRaw } = await this.postAsync(url, {
        ...this.selectedCrew,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        this.$dialog.message.success(`保存成功`)
      }
      this.crewSuccess()
    },
    async saveCrewList() {
      const url = '/business/seaAffairs/MarsupDailyWork/detail/addCrewList'
      const { errorRaw } = await this.postAsync(url, this.crewList)
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        this.$dialog.message.success(`保存成功`)
      }
      this.crewSuccess()
    },
    createTrack() {
      this.initialTrackData = {
        shipCode: this.detail.shipCode,
        problemPhotos: [],
        correctEvidences: [],
        detailStatus: this.detail.status,
      }
      this.dialogTrack = true
    },
    editTrack() {
      this.initialTrackData = {
        ...this.selectedTrack,
        detailStatus: this.detail.status,
      }
      this.dialogTrack = true
    },
    createOther() {
      this.initialOtherData = {
        shipCode: this.detail.shipCode,
        problemPhotos: [],
        correctEvidences: [],
        detailStatus: this.detail.status,
      }
      this.dialogOther = true
    },
    editOther() {
      this.initialOtherData = {
        ...this.selectedOther,
        detailStatus: this.detail.status,
      }
      this.dialogOther = true
    },
    async delQues() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/deleteCheck',
        [this.selectedQues.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadCheckList()
        this.selectedQues = false
      }
    },
    async loadCheckList() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/checkList',
        { id: this.detail.id },
      )
      this.checkList = data
      // this.detail.natureAccident = JSON.parse(this.detail.natureAccident) //转换多选项处理
    },
    async delWork() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/deleteWork',
        [this.selectedWork.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadWorkList()
        this.selectedWork = false
      }
    },
    async loadWorkList() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/workList',
        { id: this.detail.id },
      )
      this.workList = data
    },
    async delCrew() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/deleteCrew',
        [this.selectedCrew.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadCrewList()
        this.selectedCrew = false
      }
    },
    async loadCrewList() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/crewList',
        { id: this.detail.id },
      )
      this.crewList = data
    },
    async delTrack() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/deleteTrack',
        [this.selectedTrack.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadTrackList()
        this.selectedTrack = false
      }
    },
    async loadTrackList() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/trackList',
        { id: this.detail.id },
      )
      this.trackList = data
    },
    async delOther() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/deleteOther',
        [this.selectedOther.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadOtherList()
        this.selectedOther = false
      }
    },
    async loadOtherList() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/MarsupDailyWork/detail/otherList',
        { id: this.detail.id },
      )
      this.otherList = data
    },
    async questionSuccess() {
      await this.loadCheckList()
      this.selectedQues = false
    },
    async workSuccess() {
      await this.loadWorkList()
      this.selectedWork = false
    },
    async crewSuccess() {
      await this.loadCrewList()
      this.selectedCrew = false
    },
    async trackSuccess() {
      await this.loadTrackList()
      this.selectedTrack = false
    },
    async otherSuccess() {
      await this.loadOtherList()
      this.selectedOther = false
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
  },

  mounted() {
    this.loadDetail()
  },
  beforeDestroy() {
    this.$store.commit('removeBussinessParam', this.detail.id)
  },
}
</script>

<style>
.scroll-content1 {
  /* position: sticky; */
  height: 400px;
  overflow-y: auto;
}
</style>
