<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
    @close="cancelDialog"
    :beforeClose="onBeforeClose"
  >
    <v-card>
      <v-card-title>
        新增需跟踪事项记录
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="formData.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="跟踪事项"
                  dense
                  outlined
                  v-model="formData.trackingMatter"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <!-- <v-btn
          v-if="formData.questionType === '1'"
          depressed
          :disabled="!!nonstandartReportId"
          @click="addNonstandart"
        >
          {{ !!nonstandartReportId ? '已填加' : '添加不符合报告' }}
        </v-btn> -->
        <v-btn depressed color="primary" :disabled="!isEdit" @click="save">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'add-track-item-new',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
    // this.formData = {}
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({
        isCorrected: false,
        // problemPhotos: [],
        // correctEvidences: [],
      }),
    },
    nonstandartReportTempId: String,
    checkType: [String, Number],
  },
  created() {
    this.questionTypes = [
      { text: '无缺陷', value: '0' },
      { text: '缺陷项', value: '1' },
      { text: '建议项', value: '2' },
    ]
  },
  data() {
    return {
      dialog: false,
      dict_type: 'security_ques_types',
      formData: {
        isCorrected: false,
        // problemPhotos: [],
        // correctEvidences: [],
        // attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      questionId: '',
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      // this.$refs?.form?.formData?.reset()
      this.$refs?.form?.resetValidation()
      this.questionTypes = [
        { text: '无缺陷', value: '0' },
        { text: '缺陷项', value: '1' },
      ]
      // <!--"`${this.checkType === '1' ? 'security_ques_types_psc' : 'security_ques_types'}`"-->
      if ('2' == this.checkType) this.dict_type = 'security_ques_types_psc'
      if (['0', '1'].includes(this.checkType))
        this.questionTypes.push({ text: '建议项', value: '2' })
      this.$nextTick(() => {
        this.formData = { ...this.initialData }
        this.questionId =
          this.formData.id || Math.floor(Math.random() * 1000 + 1)
      })
    },
  },
  computed: {
    isEdit() {
      return (
        this.initialData?.detailStatus == 1 ||
        this.initialData?.detailStatus == 4
      )
    },
    isNotNew() {
      return this.initialData?.id
    },
    nonstandartReportId() {
      return (
        this.$store.state.reportParams.businessParams.find(
          (b) => b.id === this.questionId,
        )?.reportId || this.formData.systemReportId
      )
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.correctEvidenceIds = attachmentIds
    },
    changeAttachment1(attachmentIds) {
      this.formData.problemPhotoIds = attachmentIds
    },
    closeForm() {
      // if (this.formData.questionType == 1 && !this.nonstandartReportId) {
      //   //this.$dialog.message.warning('请填写不符合报告')
      //   this.$dialog.message.warning('未添加不符合报告请点击确认按钮')
      //   return
      // }
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      // if (this.formData.questionType == 1 && !this.nonstandartReportId) {
      //   this.$dialog.message.warning('请填写不符合报告')
      //   return
      // }
      const url = '/business/seaAffairs/MarsupDailyWork/detail/addTrack'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        marsupDailyWorkId: this.$route.params.id,
        systemReportId: this.nonstandartReportId,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        // this.$refs.form.reset()
      }
      // this.$refs.form.reset()
    },
    // addNonstandart() {
    //   this.$store.commit('emitBussiness', {
    //     businessType: 'securityCheck',
    //     businessId: this.$route.params.id,
    //     templateId: this.nonstandartReportTempId,
    //     id: this.questionId,
    //   })
    //   this.$router.push({
    //     name: 'report-emit-detail',
    //     params: { id: this.nonstandartReportTempId },
    //   })
    // },
    addNonstandart() {
      console.log(this.initialData.shipCode)
      this.$store.commit('emitBussiness', {
        businessType: 'securityCheck_Non',
        businessId: this.$route.params.id,
        templateId: this.nonstandartReportTempId,
        id: this.questionId,
        otherParams: {
          shipCode: this.initialData.shipCode,
          // type: this.detail.accidentType,
          // shipFlag:
          //   this.initialData.shipBaseMixOutputDTO.flagPort +
          //   ',' +
          //   this.initialData.shipBaseMixOutputDTO.flagState,
          // shipName: this.initialData.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        //'report-emit-detail',
        name: 'dept-report-info-detail1-new',
        params: {
          id: this.nonstandartReportTempId,
          businessId1: this.$route.params.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },
    async cancelDialog() {
      console.log('关闭按钮触发。。。')
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
    },
    // 取消默认关闭弹框事件
    onBeforeClose(action, done) {
      console.log('关闭按钮触发2。。。')
      return done(false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
