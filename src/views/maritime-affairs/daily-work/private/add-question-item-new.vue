<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
    @close="cancelDialog"
    :beforeClose="onBeforeClose"
  >
    <v-card>
      <v-card-title>
        新增安全检查记录
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="formData.shipCode"
                  readonly
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  label="船位动态（填表时）"
                  dense
                  outlined
                  v-model="formData.shipPosition"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-row>
                  <v-col cols="12" md="1">
                    <label>进出港</label>
                  </v-col>
                  <v-col cols="12" md="2">
                    <v-switch
                      class="mt-1"
                      dense
                      v-model="formData.questionType"
                      :label="`${formData.questionType ? '适用' : '不适用'}`"
                      color="success"
                    ></v-switch>
                  </v-col>
                </v-row>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.enterExitPort"
                  :label="`进出港靠离泊:${
                    formData.enterExitPort ? '适用' : '不适用'
                  }`"
                  color="success"
                ></v-switch>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.berthDisembark"
                  :label="`靠离泊:${
                    formData.berthDisembark ? '适用' : '不适用'
                  }`"
                  color="success"
                ></v-switch>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.antiTheftArea"
                  :label="`防偷盗区域:${
                    formData.antiTheftArea ? '适用' : '不适用'
                  }`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.narrowWaterway"
                  :label="`狭水道:${
                    formData.narrowWaterway ? '适用' : '不适用'
                  }`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.vtsArea"
                  :label="`VTS管辖区域:${formData.vtsArea ? '适用' : '不适用'}`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.fishArea"
                  :label="`商渔船密集区:${
                    formData.fishArea ? '适用' : '不适用'
                  }`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.foggyArea"
                  :label="`雾区:${formData.foggyArea ? '适用' : '不适用'}`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.badWeather"
                  :label="`恶劣天气:${formData.badWeather ? '适用' : '不适用'}`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="有VSAT"
                  dense
                  outlined
                  v-model="formData.withVsat"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="无VSAT"
                  dense
                  outlined
                  v-model="formData.noVsat"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="违规行为"
                  dense
                  outlined
                  v-model="formData.violation"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.keyPort"
                  :label="`重点港口:${formData.keyPort ? '适用' : '不适用'}`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.keyVessel"
                  :label="`重点船舶:${formData.keyVessel ? '适用' : '不适用'}`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.cargoLoad"
                  :label="`货物配载:${formData.cargoLoad ? '适用' : '不适用'}`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="formData.anchorReminder"
                  :label="`抛锚提醒:${
                    formData.anchorReminder ? '适用' : '不适用'
                  }`"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" md="6">
                <!-- <v-text-field
                  label="提醒方式（平台、微信、邮件、电话）"
                  dense
                  outlined
                  v-model="formData.reminderMethod"
                ></v-text-field> -->
                <v-dict-select
                  dict-type="reminder_method_type"
                  label="提醒方式（平台、微信、邮件、电话）"
                  outlined
                  collapse-tags
                  multiple
                  v-model="formData.reminderMethod"
                  @change="(val) => $emit('input', String(val))"
                  dense
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="备注"
                  dense
                  outlined
                  v-model="formData.remark"
                ></v-textarea>
              </v-col>

              <!-- <v-col cols="12" md="12">
                <v-textarea
                  label="备注Remark"
                  dense
                  rows="3"
                  outlined
                  v-model="formData.remark"
                ></v-textarea>
              </v-col> -->
              <!-- <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col> -->
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <!-- <v-btn
          v-if="formData.questionType === '1'"
          depressed
          :disabled="!!nonstandartReportId"
          @click="addNonstandart"
        >
          {{ !!nonstandartReportId ? '已填加' : '添加不符合报告' }}
        </v-btn> -->
        <v-btn depressed color="primary" :disabled="!isEdit" @click="save">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'add-question-item-new',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
    // this.formData = {}
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({
        // isCorrected: false,
        // problemPhotos: [],
        // correctEvidences: [],
      }),
    },
    nonstandartReportTempId: String,
    checkType: [String, Number],
  },
  created() {
    this.questionTypes = [
      { text: '无缺陷', value: '0' },
      { text: '缺陷项', value: '1' },
      { text: '建议项', value: '2' },
    ]
  },
  data() {
    return {
      dialog: false,
      dict_type: 'security_ques_types',
      formData: {
        // isCorrected: false,
        // problemPhotos: [],
        // correctEvidences: [],
        // attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      questionId: '',
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      // this.$refs?.form?.formData?.reset()
      this.$refs?.form?.resetValidation()
      this.questionTypes = [
        { text: '无缺陷', value: '0' },
        { text: '缺陷项', value: '1' },
      ]
      // <!--"`${this.checkType === '1' ? 'security_ques_types_psc' : 'security_ques_types'}`"-->
      if ('2' == this.checkType) this.dict_type = 'security_ques_types_psc'
      if (['0', '1'].includes(this.checkType))
        this.questionTypes.push({ text: '建议项', value: '2' })
      this.$nextTick(() => {
        this.formData = { ...this.initialData }
        this.formData.reminderMethod = JSON.parse(this.formData.reminderMethod) //转换多选项处理

        // this.questionId =
        //   this.formData.id || Math.floor(Math.random() * 1000 + 1)
      })
    },
  },
  computed: {
    isEdit() {
      console.log(this.initialData)
      return (
        this.initialData?.detailStatus == 1 ||
        this.initialData?.detailStatus == 4
      )
    },
    isNotNew() {
      return this.initialData?.id
    },
    nonstandartReportId() {
      return (
        this.$store.state.reportParams.businessParams.find(
          (b) => b.id === this.questionId,
        )?.reportId || this.formData.systemReportId
      )
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.correctEvidenceIds = attachmentIds
    },
    changeAttachment1(attachmentIds) {
      this.formData.problemPhotoIds = attachmentIds
    },
    closeForm() {
      // if (this.formData.questionType == 1 && !this.nonstandartReportId) {
      //   //this.$dialog.message.warning('请填写不符合报告')
      //   this.$dialog.message.warning('未添加不符合报告请点击确认按钮')
      //   return
      // }
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      // if (this.formData.questionType == 1 && !this.nonstandartReportId) {
      //   this.$dialog.message.warning('请填写不符合报告')
      //   return
      // }
      const url = '/business/seaAffairs/MarsupDailyWork/detail/addCheck'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        marsupDailyWorkId: this.$route.params.id,
        systemReportId: this.nonstandartReportId,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        // this.$refs.form.reset()
      }
      // this.$refs.form.reset()
    },
    // addNonstandart() {
    //   this.$store.commit('emitBussiness', {
    //     businessType: 'securityCheck',
    //     businessId: this.$route.params.id,
    //     templateId: this.nonstandartReportTempId,
    //     id: this.questionId,
    //   })
    //   this.$router.push({
    //     name: 'report-emit-detail',
    //     params: { id: this.nonstandartReportTempId },
    //   })
    // },
    addNonstandart() {
      console.log(this.initialData.shipCode)
      this.$store.commit('emitBussiness', {
        businessType: 'securityCheck_Non',
        businessId: this.$route.params.id,
        templateId: this.nonstandartReportTempId,
        id: this.questionId,
        otherParams: {
          shipCode: this.initialData.shipCode,
          // type: this.detail.accidentType,
          // shipFlag:
          //   this.initialData.shipBaseMixOutputDTO.flagPort +
          //   ',' +
          //   this.initialData.shipBaseMixOutputDTO.flagState,
          // shipName: this.initialData.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        //'report-emit-detail',
        name: 'dept-report-info-detail1-new',
        params: {
          id: this.nonstandartReportTempId,
          businessId1: this.$route.params.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },
    async cancelDialog() {
      console.log('关闭按钮触发。。。')
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
    },
    // 取消默认关闭弹框事件
    onBeforeClose(action, done) {
      console.log('关闭按钮触发2。。。')
      return done(false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
