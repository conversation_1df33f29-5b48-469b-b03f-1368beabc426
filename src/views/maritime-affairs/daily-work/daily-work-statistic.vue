<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        海务主管日常工作统计(次数)
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="loadStatistic"
        >
          <v-icon left>mdi-magnify-expand</v-icon>
          搜索
        </v-btn>
        <v-btn outlined tile color="primary" @click.stop="exportData">
          <v-icon left>mdi-magnify-expand</v-icon>
          导出Excel
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-row>
          <!-- <v-col cols="12" sm="6" md="2">
            <v-text-field
              outlined
              dense
              label="年份"
              clearable
              v-model="searchObj.year"
            ></v-text-field>
          </v-col> -->
          <v-col cols="12" sm="6" md="4">
            <v-menu
              v-model="datesMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  ref="dates"
                  :value="dateRangeText"
                  label="时间范围"
                  append-icon="mdi-calendar"
                  outlined
                  dense
                  readonly
                  clearable
                  @click:clear="dates = []"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <vc-date-picker
                v-model="dates"
                mode="date"
                is-range
              ></vc-date-picker>
            </v-menu>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-title class="py-1">
        安全检查
        <v-spacer></v-spacer>
      </v-card-title>
      <v-data-table
        :headers="headers"
        :items="list"
        title="安全检查"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
      ></v-data-table>
      <v-card-title class="py-1">
        日常工作
        <v-spacer></v-spacer>
      </v-card-title>
      <v-data-table
        :headers="headers1"
        :items="list1"
        title="日常工作"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
      ></v-data-table>
      <v-card-title class="py-1">
        船员管理
        <v-spacer></v-spacer>
      </v-card-title>
      <v-data-table
        :headers="headers2"
        :items="list2"
        title="船员管理"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
      ></v-data-table>
    </v-card>
  </v-container>
</template>
<script>
// boardingInspection	登轮检查次数	integer(int32)
// defectNumOfBoardingInspection	登轮检查缺陷项数量	integer(int32)
// defectNumOfFSCInspection	FSC检查缺陷项数量	integer(int32)
// defectNumOfPSCInspection	PSC检查缺陷项数量	integer(int32)
// defectNumOfSelfInspection	船舶自查缺陷项数量	integer(int32)
// fscinspection		integer(int32)
// irregularInspection	不定期检查次数	integer(int32)
// proposalNumOfSelfInspection	船舶自查建议项数量	integer(int32)
// proposalNumofBoardingInspeciton	登轮检查建议项数量	integer(int32)
// pscinspection		integer(int32)
// selfInspection	船舶自查次数	integer(int32)
// shipCnName	中文船名	string
// shipEnName	英文船名	string
export default {
  name: 'daily-work-statistic',
  created() {
    this.headers = [
      { text: '', value: 'a', width: '0', sortable: false },
      { text: '船名', value: 'shipName' },
      // { text: '英文船名', value: 'shipEnName' },
      { text: '进出港', value: 'enterExitPort' },
      { text: '靠离泊', value: 'berthDisembark' },
      { text: '狭水道', value: 'narrowWaterway' },
      { text: 'VTS管辖区域', value: 'vtsArea' },
      { text: '渔区', value: 'fishArea' },
      { text: '雾区', value: 'foggyArea' },
      { text: '恶劣天气', value: 'badWeather' },
      { text: '有VSAT', value: 'withVsat' },
      { text: '无VSAT', value: 'noVsat' },
      { text: '违规行为', value: 'violation' },
      { text: '重点港口', value: 'keyPort' },
      { text: '重点船舶', value: 'keyVessel' },
      { text: '货物配载', value: 'cargoLoad' },
    ]
    this.headers1 = [
      { text: '', value: 'a', width: '0', sortable: false },
      // { text: '序号', value: 'number' },
      { text: '船名', value: 'shipName' },
      { text: '船舶备用金', value: 'shipReserveFund' },
      { text: '订购海图', value: 'orderNauticalChart' },
      { text: '文档管理存档', value: 'documentManagementArchive' },
    ]
    this.headers2 = [
      { text: '', value: 'a', width: '0', sortable: false },
      // { text: '序号', value: 'number' },
      { text: '船名', value: 'shipName' },
      // { text: '姓名', value: 'name' },
      { text: '面试', value: 'interview' },
      { text: '培训', value: 'train' },
      { text: '考核', value: 'crewCheck' },
      // { text: '日期', value: 'operationDate' },
      // { text: '备注', value: 'remark' },
    ]
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
  },

  data() {
    return {
      subtitles: [
        // '填写进度',
        // '船舶基本信息',
        // '基本信息',
        '安全检查',
        '日常工作',
        '船员管理',
        // '需跟踪事项',
        // '其他工作',
        // '确认进度',
      ],
      selected: false,
      searchObj: {
        year: new Date().getFullYear(),
        fromTime: null,
        toTime: null,
      },
      dates: [],
      datesMenu: false,
      list: [],
      list1: [],
      list2: [],
    }
  },

  methods: {
    async loadStatistic() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/MarsupDailyWork/statistic/checkTimesAndQuestionNumber',
        {
          ...this.searchObj,
          fromTime: this.dates?.start?.toISOString()?.split('T')[0] || null,
          toTime: this.dates?.end?.toISOString()?.split('T')[0] || null,
        },
      )
      this.list = data
      this.loadStatistic1()
      this.loadStatistic2()
    },
    async loadStatistic1() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/MarsupDailyWork/statistic/checkTimesAndQuestionNumber1',
        {
          ...this.searchObj,
          fromTime: this.dates?.start?.toISOString()?.split('T')[0] || null,
          toTime: this.dates?.end?.toISOString()?.split('T')[0] || null,
        },
      )
      this.list1 = data
    },
    async loadStatistic2() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/MarsupDailyWork/statistic/checkTimesAndQuestionNumber2',
        {
          ...this.searchObj,
          fromTime: this.dates?.start?.toISOString()?.split('T')[0] || null,
          toTime: this.dates?.end?.toISOString()?.split('T')[0] || null,
        },
      )
      this.list2 = data
    },
    async exportData() {
      this.searchObj.fromTime =
        this.dates?.start?.toISOString()?.split('T')[0] || null
      this.searchObj.toTime =
        this.dates?.end?.toISOString()?.split('T')[0] || null
      await this.blobDownload(
        '/business/seaAffairs/MarsupDailyWork/excelStatistic',
        this.searchObj,
        // {
        //   ...this.searchObj,
        //   fromTime: this.dates?.start?.toISOString()?.split('T')[0] || null,
        //   toTime: this.dates?.end?.toISOString()?.split('T')[0] || null,
        // },
      )
    },
  },

  mounted() {
    this.loadStatistic()
  },
}
</script>

<style></style>
