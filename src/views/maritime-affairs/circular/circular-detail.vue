<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['一般通函:编辑']"
      :title="`船旗国通告-${isEdit ? formData.code : '新增'}`"
      backRouteName="circular-list"
      :tooltip="isEdit ? formData.code : '新增'"
      :subtitles="[]"
      @save="save"
    >
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.code"
                  label="文件编号"
                  required
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.name"
                  label="文件名称"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-menu
                  v-model="dateMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="formData.pubTime"
                      label="上传日期"
                      append-icon="mdi-calendar"
                      readonly
                      clearable
                      outlined
                      dense
                      :rules="[rules.required]"
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="formData.pubTime"
                    @input="dateMenu = false"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.source"
                  label="文件来源"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  label="录入人"
                  v-model="formData.handler"
                  :readonly="isEdit"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-dict-select
                  dictType="sea_ciucular_type"
                  label="文件类型"
                  v-model="formData.type"
                  :rules="[rules.required]"
                ></v-dict-select>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="attachmentRecords"
                  @change="changeAttachment"
                  ship-code="0"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'circular-detail',
  data() {
    return {
      dateMenu: false,
      attachmentRecords: [],
      formData: {
        code: '',
        content: '',
        name: '',
        pubTime: new Date(Date.now()).toISOString().substr(0, 10),
        source: '',
        type: '',
        handler: this.$local.data.get('userInfo').nickName,
        businessType: '1',
        attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },

    async getCircular() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/seaAffairs/circular/record/${this.$route.params.id}`,
      )
      this.attachmentRecords = data.attachmentRecords
      this.formData = data
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.isEdit) {
        const { errorRaw } = await this.postAsync(
          '/business/seaAffairs/circular/update',
          {
            ...this.formData,
            isOld: true,
          },
        )
        if (errorRaw) return
      }
      const url = '/business/seaAffairs/circular/save'
      const { errorRaw } = await this.postAsync(url, this.formData)
      if (!errorRaw) {
        this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        goBack()
      }
    },
  },
  created() {
    this.getCircular()
  },
}
</script>

<style></style>
