<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/maritime-affairs/circular-appoint/detail/new"
          v-permission="['海务安全通报&指定人员发文:新增指定人员发文']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增指定人员发文
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/maritime-affairs/circular-special/detail/new"
          v-permission="['海务安全通报&指定人员发文:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delCircular"
          v-permission="['海务安全通报&指定人员发文:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'circular-special-list',
  created() {
    this.tableName = '海务安全提示&公司内部通函'
    this.reqUrl = '/business/seaAffairs/circular/special/page'
    this.searchDicts = [
      {
        dicType: 'sea_circular_special_type',
        label: '类型',
        key: 'type',
      },
    ]
    this.headers = [
      { text: '年度编号', value: 'code' },
      { text: '发布日期', value: 'pubTime' },
      { text: '类型', value: 'type' },
      { text: '经办人', value: 'handler' },
      { text: '内容', value: 'content' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.fuzzyLabel = '年度编号'
    this.searchDate = {
      label: '发布日期',
      value: 'pubTime',
    }
    this.pushParams = {
      name: 'circular-special-detail',
    }
  },
  data: () => ({
    selected: undefined,
  }),

  methods: {
    editCircular() {
      this.$router.push(
        `/maritime-affairs/circular-special/${this.selected.id}`,
      )
    },
    async delCircular() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/circular/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
