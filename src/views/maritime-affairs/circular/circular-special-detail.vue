<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['海务安全通报&指定人员发文:编辑']"
      :title="`提示&通函-${isEdit ? formData.code : '新增'}`"
      backRouteName="circular-special-list"
      :tooltip="isEdit ? formData.code : '新增'"
      :subtitles="[]"
      @save="save"
    >
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.code"
                  label="年度编号"
                  required
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-menu
                  v-model="dateMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="formData.pubTime"
                      label="发布日期"
                      append-icon="mdi-calendar"
                      readonly
                      clearable
                      :rules="[rules.required]"
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="formData.pubTime"
                    @input="dateMenu = false"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  label="文件类型"
                  v-model="formData.type"
                  disabled
                  :items="types"
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  label="经办人"
                  v-model="formData.handler"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="发文内容"
                  v-model="formData.content"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="attachmentRecords"
                  @change="changeAttachment"
                  ship-code="0"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'circular-special-detail',
  data() {
    return {
      dateMenu: false,
      attachmentRecords: [],
      formData: {
        code: '',
        content: '',
        name: '',
        pubTime: new Date(Date.now()).toISOString().substr(0, 10),
        source: '',
        type: '0',
        handler: this.$local.data.get('userInfo').nickName,
        businessType: '0',
        attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
      types: [
        { value: '0', text: '海务安全通报' },
        { value: '1', text: '指定人员发文', disabled: true },
      ],
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },

    async getCircular() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/seaAffairs/circular/record/${this.$route.params.id}`,
      )
      this.attachmentRecords = data.attachmentRecords
      this.formData = data
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/circular/update'
        : '/business/seaAffairs/circular/save'
      const { errorRaw } = await this.postAsync(url, this.formData)
      if (!errorRaw) {
        this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        goBack()
      }
    },
  },

  created() {
    this.getCircular()
  },
}
</script>

<style></style>
