<template>
  <v-container fluid>
    <v-expansion-panels class="mb-1">
      <v-expansion-panel>
        <v-expansion-panel-header>链接</v-expansion-panel-header>
        <v-expansion-panel-content>
          <v-list dense>
            <v-list-item v-for="(item, i) in circular" :key="i">
              <v-list-item-content>
                <div>
                  {{ item.dictLabel }}:
                  <a :href="item.cssClass" target="_blank">
                    {{ item.cssClass }}
                  </a>
                </div>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>

    <v-table-searchable
      ref="table"
      table-name="船旗国通告"
      :search-dicts="searchDicts"
      :search-date="searchDate"
      v-model="selected"
      fuzzy-label="内容/年度编号"
      :headers="headers"
      req-url="/business/seaAffairs/circular/common/page"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/maritime-affairs/circular/detail/new"
          v-permission="['一般通函:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delCircular"
          v-permission="['一般通函:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.isOld`]="{ item }">
        {{ item.isOld ? '是' : '最新' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'circular-list',
  mixins: [dictHelper],
  created() {
    this.searchDicts = [
      {
        dicType: 'sea_ciucular_type',
        label: '通函类型',
        key: 'type',
      },
    ]
    this.headers = [
      { text: '文件编号', value: 'code' },
      { text: '文件名称', value: 'name' },
      { text: '上传日期', value: 'pubTime' },
      { text: '文件来源', value: 'source' },
      { text: '通函类型', value: 'type' },
      { text: '历史记录', value: 'isOld' },
      { text: '经办人', value: 'handler' },
      { text: '附件', value: 'attachmentRecords', sortable: false },
    ]
    this.searchDate = {
      label: '发布时间',
      value: 'pubTime',
    }
    this.pushParams = {
      name: 'circular-detail',
    }
  },
  data: () => ({
    selected: false,
    circular: [],
  }),

  methods: {
    editCircular() {
      this.$router.push(`/maritime-affairs/circular/${this.selected.id}`)
    },
    async delCircular() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/circular/deleteBatch',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
    async loadTyps() {
      this.circular = await this.getDictByType('sea_ciucular_type')
    },
  },

  mounted() {
    this.loadTyps()
  },
}
</script>

<style></style>
