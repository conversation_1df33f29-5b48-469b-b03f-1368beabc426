<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="primary white--text py-1 my-0">
        电子海图季度设置
      </v-card-title>
      <v-card-title></v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="2">
            <v-text-field
              label="年度"
              :disabled="isEdit"
              v-model="QuarterData.year"
            ></v-text-field>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col>
                <v-menu
                  v-model="QuarterMenu1"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      :disabled="QuarterData.year.length !== 4"
                      v-model="QuarterData.quarterFirstStart"
                      label="第一季度起始时间"
                      append-icon="mdi-calendar"
                      readonly
                      clearable
                      :rules="[rules.required]"
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="QuarterData.quarterFirstStart"
                    @input="QuarterMenu = false"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col>
                <v-menu
                  v-model="QuarterMenu11"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="QuarterData.quarterFirstEnd"
                      :disabled="!QuarterData.quarterFirstStart"
                      label="第一季度结束时间"
                      append-icon="mdi-calendar"
                      :rules="[rules.required]"
                      readonly
                      clearable
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="QuarterData.quarterFirstEnd"
                    @input="QuarterMenu = false"
                    :min="QuarterData.quarterFirstStart"
                  ></v-date-picker>
                </v-menu>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-menu
                  v-model="QuarterMenu2"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="QuarterData.quarterSecondStart"
                      :disabled="!QuarterData.quarterFirstEnd"
                      label="第二季度起始时间"
                      append-icon="mdi-calendar"
                      readonly
                      clearable
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="QuarterData.quarterSecondStart"
                    @input="QuarterMenu = false"
                    :min="QuarterData.quarterFirstEnd"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col>
                <v-menu
                  v-model="QuarterMenu22"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="QuarterData.quarterSecondEnd"
                      :disabled="!QuarterData.quarterSecondStart"
                      label="第二季度结束时间"
                      append-icon="mdi-calendar"
                      :rules="
                        QuarterData.quarterSecondStart ? [rules.required] : []
                      "
                      readonly
                      clearable
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="QuarterData.quarterSecondEnd"
                    @input="QuarterMenu = false"
                    :min="QuarterData.quarterSecondStart"
                  ></v-date-picker>
                </v-menu>
              </v-col>
            </v-row>

            <v-row>
              <v-col>
                <v-menu
                  v-model="QuarterMenu3"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="QuarterData.quarterThirdStart"
                      :disabled="!QuarterData.quarterSecondEnd"
                      label="第三季度起始时间"
                      append-icon="mdi-calendar"
                      readonly
                      clearable
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="QuarterData.quarterThirdStart"
                    @input="QuarterMenu = false"
                    :min="QuarterData.quarterSecondEnd"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col>
                <v-menu
                  v-model="QuarterMenu33"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="QuarterData.quarterThirdEnd"
                      :disabled="!QuarterData.quarterThirdStart"
                      label="第三季度结束时间"
                      append-icon="mdi-calendar"
                      readonly
                      :rules="
                        QuarterData.quarterThirdStart ? [rules.required] : []
                      "
                      clearable
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="QuarterData.quarterThirdEnd"
                    @input="QuarterMenu = false"
                    :min="QuarterData.quarterThirdStart"
                  ></v-date-picker>
                </v-menu>
              </v-col>
            </v-row>

            <v-row>
              <v-col>
                <v-menu
                  v-model="QuarterMenu4"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="QuarterData.quarterFourthStart"
                      :disabled="!QuarterData.quarterThirdEnd"
                      label="第四季度起始时间"
                      append-icon="mdi-calendar"
                      readonly
                      clearable
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="QuarterData.quarterFourthStart"
                    @input="QuarterMenu = false"
                    :min="QuarterData.quarterThirdEnd"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col>
                <v-menu
                  v-model="QuarterMenu44"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="QuarterData.quarterFourthEnd"
                      :disabled="!QuarterData.quarterFourthStart"
                      label="第四季度结束时间"
                      append-icon="mdi-calendar"
                      readonly
                      clearable
                      :rules="
                        QuarterData.quarterFourthStart ? [rules.required] : []
                      "
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="QuarterData.quarterFourthEnd"
                    @input="QuarterMenu = false"
                    :min="QuarterData.quarterFourthStart"
                  ></v-date-picker>
                </v-menu>
              </v-col>
            </v-row>
            <v-col cols="12">
              <v-btn
                v-permission="['电子海图-季度设置:编辑']"
                outlined
                tile
                color="success"
                class="mx-1"
                @click="save"
                block
              >
                <v-icon left>mdi-plus-circle</v-icon>
                {{ isEdit ? '保存' : '创建' }}
              </v-btn>
            </v-col>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  name: 'electronic-chart-season-detail',
  data() {
    return {
      date: new Date(Date.now()).toISOString().substr(0, 10),
      QuarterMenu1: false,
      QuarterMenu11: false,
      QuarterMenu2: false,
      QuarterMenu22: false,
      QuarterMenu3: false,
      QuarterMenu33: false,
      QuarterMenu4: false,
      QuarterMenu44: false,
      QuarterData: {
        year: '',
        quarterFirstStart: '',
        quarterFirstEnd: '',
        quarterSecondStart: '',
        quarterSecondEnd: '',
        quarterThirdStart: '',
        quarterThirdEnd: '',
        quarterFourthStart: '',
        quarterFourthEnd: '',
      },
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    firstDayOfYear() {
      return new Date(
        new Date(this.QuarterData.year, 0, 1) -
          new Date().getTimezoneOffset() * 60000,
      )
        .toISOString()
        .substr(0, 10)
    },
  },
  watch: {
    'QuarterData.year'(val) {
      if (!this.isEdit && val.length === 4) {
        this.QuarterData.quarterFirstStart = this.firstDayOfYear
      }
    },
  },
  methods: {
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      let quarterArray = []
      if (this.QuarterData.quarterFirstEnd) {
        quarterArray.push(
          this.getAsync('/business/seaAffairs/eChartSetting/save', {
            year: this.QuarterData.year,
            quarter: 1,
            quarterStart: this.QuarterData.quarterFirstStart,
            quarterEnd: this.QuarterData.quarterFirstEnd,
            handler: this.$local.data.get('userInfo').nickName,
          }),
        )
      }
      if (this.QuarterData.quarterSecondEnd) {
        quarterArray.push(
          this.getAsync('/business/seaAffairs/eChartSetting/save', {
            year: this.QuarterData.year,
            quarter: 2,
            quarterStart: this.QuarterData.quarterSecondStart,
            quarterEnd: this.QuarterData.quarterSecondEnd,
            handler: this.$local.data.get('userInfo').nickName,
          }),
        )
      }
      if (this.QuarterData.quarterThirdEnd) {
        quarterArray.push(
          this.getAsync('/business/seaAffairs/eChartSetting/save', {
            year: this.QuarterData.year,
            quarter: 3,
            quarterStart: this.QuarterData.quarterThirdStart,
            quarterEnd: this.QuarterData.quarterThirdEnd,
            handler: this.$local.data.get('userInfo').nickName,
          }),
        )
      }
      if (this.QuarterData.quarterFourthEnd) {
        quarterArray.push(
          this.getAsync('/business/seaAffairs/eChartSetting/save', {
            year: this.QuarterData.year,
            quarter: 4,
            quarterStart: this.QuarterData.quarterFourthStart,
            quarterEnd: this.QuarterData.quarterFourthEnd,
            handler: this.$local.data.get('userInfo').nickName,
          }),
        )
      }
      const result = await Promise.all(quarterArray)
      if (result.every((item) => !item.errorRaw)) {
        this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.push({
          path: '/maritime-affairs/book-data/electronic-chart-season/list',
          query: {
            reload: true,
          },
        })
      }
    },

    async loadSeason() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/seaAffairs/eChartSetting/record/${this.$route.params.id}`,
      )
      if (data) {
        this.QuarterData = data
        this.$store.commit('updateViewTagsTooltip', {
          tooltip: `${data?.year}年度`,
          nowFullPath: this.$route.path,
        })
      }
    },
  },

  mounted() {
    this.loadSeason()
  },
}
</script>

<style></style>
