<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-ship-select
            v-model="searchObj.shipId"
            outlined
            dense
            use-id
          ></v-ship-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.purchaseYear"
            label="年度"
            outlined
            dense
            append-icon="mdi-magnify"
          ></v-text-field>
        </v-col>
      </template>
      <!-- <template v-slot:[`item.cost`]="{ item }">
        {{
          (
            item.firstQuarterCost +
            item.secondQuarterCost +
            item.thirdQuarterCost +
            item.firthQuarterCost
          ).toFixed(2)
        }}
      </template> -->
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="`/maritime-affairs/book-data/electronic-chart/new?shipId=${
            searchObj.shipId || ''
          }`"
          v-permission="['电子海图:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="calculate"
          :loading="loading1"
          v-permission="['电子海图:计算']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          计算
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delElectronicchart"
          v-permission="['电子海图:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'electronic-chart-list',
  created() {
    this.tableName = '电子海图费用'
    this.reqUrl = '/business/seaAffairs/electronic-chart/page'
    this.searchDicts = []
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '订购年度', value: 'purchaseYear' },
      { text: '费用', value: 'totalCost' },
      { text: '经办人', value: 'handler' },
      // { text: '第1季度费用', value: 'firstQuarterCost' },
      // { text: '第2季度费用', value: 'secondQuarterCost' },
      // { text: '第3季度费用', value: 'thirdQuarterCost' },
      // { text: '第4季度费用', value: 'firthQuarterCost' },
      { text: '1月份费用', value: 'january' },
      { text: '2月份费用', value: 'february' },
      { text: '3月份费用', value: 'march' },
      { text: '4月份费用', value: 'april' },
      { text: '5月份费用', value: 'may' },
      { text: '6月份费用', value: 'june' },
      { text: '7月份费用', value: 'july' },
      { text: '8月份费用', value: 'auguest' },
      { text: '9月份费用', value: 'september' },
      { text: '10月份费用', value: 'october' },
      { text: '11月份费用', value: 'november' },
      { text: '12月份费用', value: 'december' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '发布起始时间',
      value: 'updateTime',
      interval: true,
    }
    this.pushParams = {
      name: 'electronic-chart-detail',
    }
  },
  data() {
    return {
      selected: false,
      loading1: false,
      searchObj: {},
    }
  },

  methods: {
    editElectronicchart() {
      this.updateTaskPromptMassage(this.selected.id)
      this.$router.push(
        `/maritime-affairs/electronic-chart/${this.selected.id}`,
      )
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    async calculate() {
      this.loading1 = true
      if (!(await this.$dialog.msgbox.confirm('确定计算此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/electronic-chart/calculate',
        {
          id: this.selected.id,
        },
        false,
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
    async delElectronicchart() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/electronic-chart/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
