<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        电子海图/书订购记录-{{ isEdit ? '编辑' : '新建' }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.chartName"
                  label="海图名称"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-dict-select
                  v-model="formData.chartType"
                  label="海图类型"
                  dict-type="sea_echart_type"
                  :rules="[rules.required]"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.cost"
                  label="费用"
                  outlined
                  dense
                  type="number"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="formData.currency"
                  :items="currencyInfo"
                  item-text="ccyName"
                  item-value="id"
                  label="币别"
                  dense
                  outlined
                  :rules="[rules.required]"
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  :rules="[rules.required]"
                  label="订购时间"
                  outlined
                  v-model="formData.purchaseTime"
                  :max-date="maxDate"
                  :min-date="minDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  :rules="[rules.required]"
                  label="有效期"
                  v-model="formData.expiryTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="3" md="6">
                <v-textarea
                  v-model="formData.scope"
                  dense
                  outlined
                  rows="1"
                  label="购图范围"
                  :rules="[rules.required]"
                  auto-grow
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.remark"
                  outlined
                  dense
                  label="备注"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-card-text>
                  <v-attach-list
                    :attachments="formData.attachmentRecords"
                    @change="(ids) => (formData.attachmentIds = ids)"
                  ></v-attach-list>
                </v-card-text>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'electronic-chart-order',
  mixins: [currencyHelper],
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    year: String,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      purchaseMenu: false,
      expiryMenu: false,
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
      minDate: '',
      maxDate: '',
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = { ...this.initialData, attachmentRecords: [] }
    },
    year(val) {
      if (val) this.loadYearSetting()
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/electronic-chart/record/update'
        : '/business/seaAffairs/electronic-chart/record/save'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        chartName: '无名称',
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
    async loadYearSetting() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/eChartSetting/record',
        { year: this.year },
      )
      this.minDate = data.quarterFirstStart
      this.maxDate =
        data.quarterFourthEnd ||
        data.quarterThirdEnd ||
        data.quarterSecondEnd ||
        data.quarterFirstEnd
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
