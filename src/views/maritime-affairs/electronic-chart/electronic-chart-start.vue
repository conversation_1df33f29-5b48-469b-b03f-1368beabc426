<template>
  <v-container fluid>
    <v-card>
      <v-card-title>电子海图-新增</v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="4">
                <v-autocomplete
                  label="年度"
                  v-model="formData.purchaseYear"
                  :rules="[rules.year]"
                  :items="years"
                  outlined
                  dense
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" md="4">
                <v-ship-select
                  v-model="formData.shipId"
                  required
                  ref="shipselect"
                  use-id
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="经办人"
                  v-model="formData.handler"
                  required
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['电子海图新增:新增']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  新增
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
export default {
  name: 'electronic-chart-create',
  data() {
    return {
      formData: {
        purchaseYear: '',
        shipId: '',
        handler: this.$local.data.get('userInfo').nickName,
      },
      rules: {
        required: (value) => !!value || '必填项',
        year: (value) => {
          if (!value) {
            return '必填项'
          }
          if (value.length !== 4) {
            return '请输入正确的年份'
          }
          return true
        },
      },
      years: [],
    }
  },

  methods: {
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const shipName = this.$refs.shipselect?.list?.find(
        (item) => item.dictValue === this.formData.shipId,
      ).dictLabel
      const { data } = await this.postAsync(
        '/business/seaAffairs/electronic-chart/save',
        { ...this.formData, shipName },
      )
      if (data) {
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.push({
          name: 'electronic-chart-detail',
          params: { id: data },
        })
      }
    },
    async loadYear() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/eChartSetting/page',
        { current: 1, size: 99, sort: 'year', order: 'desc' },
      )
      this.years = data.records.map((y) => {
        return y.year
      })
    },
  },

  mounted() {
    this.loadYear()
    this.formData.shipId = this.$route.query.shipId
  },
}
</script>

<style></style>
