<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['电子海图:编辑']"
      :title="`电子海图/书-${shipName} ${echart.purchaseYear}年度`"
      backRouteName="electronic-chart-list"
      :tooltip="`电子海图/书-${shipName} ${echart.purchaseYear}年度`"
      :subtitles="subtitles"
      @save="save"
    >
      <template #基本信息>
        <v-container fluid>
          <v-form>
            <v-row>
              <v-col cols="12" sm="6" md="2">
                <v-text-field
                  dense
                  label="经办人"
                  v-model="echart.handler"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="4">
                <v-text-field
                  dense
                  label="备注"
                  v-model="echart.remark"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
          <v-row>
            <v-col cols="12">
              <div class="subtitle-1 font-weight-black">
                全年费用：{{ echart.totalCost }}
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                1月份费用：{{ echart.january }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                2月份费用：{{ echart.february }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                3月份费用：{{ echart.march }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                4月份费用：{{ echart.april }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                5月份费用：{{ echart.may }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                6月份费用：{{ echart.june }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                7月份费用：{{ echart.july }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                8月份费用：{{ echart.auguest }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                9月份费用：{{ echart.september }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                10月份费用：{{ echart.october }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                11月份费用：{{ echart.november }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                12月份费用：{{ echart.december }}
              </div>
            </v-col>
          </v-row>
        </v-container>
      </template>

      <template #电子海图-书订购记录按钮>
        <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click.stop="createRecord"
          v-permission="['电子海图订购记录:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editRecord"
          v-permission="['电子海图订购记录:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delRecord"
          v-permission="['电子海图订购记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #电子海图-书订购记录>
        <v-card-text>
          <v-table-list-new
            :headers="headers"
            :items="echart.eChartRecords"
            v-model="selected"
            @dbclick="editRecord"
          >
            <template v-slot:[`item.chartType`]="{ item }">
              {{ getLabel(item.chartType) }}
            </template>
          </v-table-list-new>
          <v-divider></v-divider>
        </v-card-text>
      </template>
      <v-attach-list
        class="mt-2"
        :attachments="echart.attachmentRecords"
        @change="changeAttachment"
      ></v-attach-list>
    </v-detail-view>
    <electronic-chart-order
      v-model="dialog"
      :initial-data="initialData"
      @success="success"
      :year="echart.purchaseYear"
    ></electronic-chart-order>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import electronicChartOrder from '@/views/maritime-affairs/electronic-chart/components/electronic-chart-order'
export default {
  name: 'electronic-chart-detail',
  components: {
    electronicChartOrder,
  },
  mixins: [dictHelper],
  created() {
    this.headers = [
      // { text: '海图名称', value: 'chartName' },
      { text: '海图/书类型', value: 'chartType' },
      { text: '购图范围', value: 'scope' },
      { text: '费用', value: 'cost' },
      { text: '币别', value: 'currencyName' },
      { text: '有效期', value: 'expiryTime' },
      { text: '订购时间', value: 'purchaseTime' },
      { text: '附件', value: 'attachmentRecords' },
      { text: '备注', value: 'remark' },
    ]
    this.subtitles = ['基本信息', '电子海图-书订购记录']
  },
  data() {
    return {
      echart: {
        attachmentRecords: [],
        eChartRecords: [],
        firstQuarterCost: 0,
        secondQuarterCost: 0,
        thirdQuarterCost: 0,
        firthQuarterCost: 0,
        handler: '',
        purchaseYear: '1970',
        remark: '',
        shipName: '',
      },
      shipName: '',
      shipCode: '',
      selectedRecord: [],
      echartType: [],
      attachmentIds: [],
      initialData: {},
      dialog: false,
      selected: false,
    }
  },

  computed: {
    // selected() {
    //   return this.selectedRecord && this.selectedRecord.length !== 0
    //     ? this.selectedRecord[0]
    //     : false
    // },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.attachmentIds = attachmentIds
    },
    async getElectronicChart() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/electronic-chart/record/${this.$route.params.id}`,
      )
      this.updateTaskPromptMassage(this.$route.params.id)
      this.echart = data
      let ship = await this.getAsync('/business/common/ship/simpleInfoById/', {
        id: data.shipId,
      })
      this.shipCode = ship.data?.shipCode
      this.shipName = ship.data?.chShipName || '船舶删除'
      this.$store.commit('updateViewTagsTooltip', {
        tooltip: `${this.shipName} ${data?.purchaseYear}-年度`,
        nowFullPath: this.$route.path,
      })
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    getLabel(value) {
      return this.echartType.find((item) => item.dictValue === value)?.dictLabel
    },

    async createRecord() {
      this.initialData = {
        electronicChartId: this.$route.params.id,
      }
      this.dialog = true
      // await this.success()
    },
    async editRecord() {
      this.initialData = {
        ...this.selected,
      }
      this.dialog = true
      // await this.success()
    },
    async delRecord() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/electronic-chart/record/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.success()
    },
    async save() {
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/electronic-chart/update',
        {
          ...this.echart,
          attachmentIds: this.attachmentIds,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('保存成功')
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.push({
          name: 'electronic-chart-list',
          query: {
            reload: true,
          },
        })
      }
    },
    async success() {
      this.selected = false
      await this.getElectronicChart()
    },
  },

  async mounted() {
    await this.getElectronicChart()
    this.echartType = await this.getDictByType('sea_echart_type')
  },
}
</script>

<style></style>
