<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新建恶劣天气
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-select
                  dictType="bad_weather_type"
                  v-model="formData.weatherType"
                  label="恶劣天气类型"
                  :rules="[rules.required]"
                  required
                  dense
                  :items="badWeatherTypes"
                ></v-select>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <v-text-field
                  v-model="formData.weatherKey"
                  dense
                  label="恶劣天气编号"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import badWeatherTypes from './badWeatherTypes'
export default {
  name: 'bad-weather-new',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/seaAffairs/badWeather/save'
      const { errorRaw, data } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success', data)
      }
    },
  },
  //   {
  // 			"cssClass":null,
  // 			"dictLabel":"台风",
  // 			"dictValue":"0",
  // 			"listClass":null
  // 		},
  // 		{
  // 			"cssClass":null,
  // 			"dictLabel":"雾，霾",
  // 			"dictValue":"1",
  // 			"listClass":null
  // 		},
  // 		{
  // 			"cssClass":null,
  // 			"dictLabel":"能见度不良",
  // 			"dictValue":"2",
  // 			"listClass":null
  // 		},
  // 		{
  // 			"cssClass":null,
  // 			"dictLabel":"冷高压",
  // 			"dictValue":"3",
  // 			"listClass":null
  // 		},
  // 		{
  // 			"cssClass":null,
  // 			"dictLabel":"温带气旋",
  // 			"dictValue":"4",
  // 			"listClass":null
  // 		}
  created() {
    this.badWeatherTypes = badWeatherTypes
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
