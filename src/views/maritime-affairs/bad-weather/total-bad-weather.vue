<template>
  <v-container fluid>
    <v-card>
      <v-card-title>按年度统计</v-card-title>
      <v-container fluid>
        <v-radio-group v-model="totalWay" row style="width: 100%">
          <v-row>
            <v-col md="4" cols="12">
              <v-radio value="year">
                <template v-slot:label>
                  <strong>按年度统计</strong>
                  <v-text-field
                    class="mx-3"
                    ref="year"
                    v-model="year"
                    label="年份"
                  ></v-text-field>
                </template>
              </v-radio>
            </v-col>
            <v-col md="4" cols="12">
              <v-radio value="time">
                <template v-slot:label>
                  <strong>按时间统计</strong>
                  <v-menu
                    v-model="datesMenu"
                    :close-on-content-click="false"
                    :nudge-right="40"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        style="width: 350px"
                        ref="dates"
                        :value="dateRangeText"
                        label="时间范围"
                        class="mx-3"
                        append-icon="mdi-calendar"
                        outlined
                        dense
                        readonly
                        clearable
                        @click:clear="dates = []"
                        v-bind="attrs"
                        v-on="on"
                      ></v-text-field>
                    </template>
                    <vc-date-picker
                      v-model="dates"
                      mode="date"
                      is-range
                    ></vc-date-picker>
                  </v-menu>
                </template>
              </v-radio>
            </v-col>
            <v-col md="4" cols="12">
              <v-radio value="weatherKey">
                <template v-slot:label>
                  <strong class="mr-3">按恶劣天气编号统计</strong>
                  <v-dialog-select
                    label="恶劣天气编号"
                    item-text="weatherKey"
                    item-value="weatherKey"
                    v-model="weatherKey"
                    :search-dicts="searchDicts"
                    :search-date="searchDate"
                    :headers="headers"
                    req-url="/business/seaAffairs/badWeather/page"
                  ></v-dialog-select>
                </template>
              </v-radio>
            </v-col>
          </v-row>
        </v-radio-group>
      </v-container>
      <v-card-actions>
        <v-btn block dark color="blue darken-1" @click="search">查询</v-btn>
      </v-card-actions>
    </v-card>
    <v-card class="mt-2">
      <v-card-title>查询结果</v-card-title>
      <v-card-text>
        <v-container fluid>
          <v-row>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                累计停航时间：{{
                  `${parseInt(result.accOfSusTime / 24)}天${
                    result.accOfSusTime % 24
                  }小时`
                }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                累计绕航里程时间：{{ `${result.accOfDeviation}海里` }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                共影响船舶艘次：{{ result.numberOfAffected }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                恶劣天气次数：{{ result.numberOfBadWeather }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                恶劣天气类型:{{ result.weatherType }}
              </div>
            </v-col>
          </v-row>
          <v-divider></v-divider>
          <v-data-table
            hide-default-footer
            class="use-divider"
            dense
            :headers="recordHeaders"
            :items="result.statisticOfAffectedShip"
          ></v-data-table>
          <v-divider></v-divider>
        </v-container>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
export default {
  name: 'total-bad-weather',
  created() {
    this.searchDicts = [
      {
        dicType: 'bad_weather_type',
        label: '恶劣天气类型',
        key: 'weatherType',
      },
    ]
    this.headers = [
      { text: '恶劣天气编号', value: 'weatherKey' },
      { text: '恶劣天气类型', value: 'weatherType' },
      { text: '中文名称', value: 'zhName' },
      { text: '英文名称', value: 'enName' },
      { text: '起编日期', value: 'startDate' },
    ]
    this.recordHeaders = [
      { text: '', value: '', width: '1px', sortable: false },
      { text: '船名', value: 'shipName' },
      { text: '恶劣天气名称', value: 'weatherName' },
      { text: '抛锚时间', value: 'anchorDownTime' },
      { text: '起锚时间', value: 'anchorUpTime' },
      { text: '开始时间', value: 'fromTime' },
      { text: '结束时间', value: 'toTime' },
      { text: '停航时间（小时）', value: 'suspensionTime' },
      { text: '绕航里程（海里）', value: 'deviation' },
      { text: '年度', value: 'year' },
    ]
    this.searchDate = {
      label: '起编日期',
      value: 'startDate',
    }
  },
  data: () => ({
    totalWay: 'year',
    datesMenu: false,
    year: '',
    dates: [],
    weatherKey: '',
    result: {
      accOfSusTime: '',
      numberOfAffected: '',
      numberOfBadWeather: '',
      statisticOfAffectedShip: [],
    },
  }),

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
  },

  methods: {
    async search() {
      if (this.totalWay === 'year') {
        const { data } = await this.postAsync(
          '/business/seaAffairs/badWeatherAffectedShipRecord/getStatistic',
          {
            year: this.year,
          },
        )
        this.result = data
      } else if (this.totalWay === 'time') {
        const { data } = await this.postAsync(
          '/business/seaAffairs/badWeatherAffectedShipRecord/getStatistic',
          {
            fromTime: this.dates?.start.toISOString().split('T')[0],
            toTime: this.dates?.end.toISOString().split('T')[0],
          },
        )
        this.result = data
      } else if (this.totalWay === 'weatherKey') {
        const { data } = await this.postAsync(
          '/business/seaAffairs/badWeatherAffectedShipRecord/getStatistic',
          {
            badWeatherKey: this.weatherKey,
          },
        )
        this.result = data
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
