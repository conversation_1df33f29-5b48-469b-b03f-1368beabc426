<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['恶劣天气编号:编辑']"
      :title="`恶劣天气编号-${formData.weatherKey}`"
      backRouteName="bad-weather-list"
      :subtitles="[]"
      :tooltip="`恶劣天气编号-${formData.weatherKey || '新增'}`"
      @save="save"
    >
      <v-card-title class="h6 mt-1">基本信息</v-card-title>
      <v-card-text class="pt-0">
        <v-form ref="form">
          <v-row>
            <v-col cols="12" sm="6" md="2">
              <v-select
                readonly
                dictType="bad_weather_type"
                v-model="formData.weatherType"
                label="恶劣天气类型"
                :rules="[rules.required]"
                required
                dense
                :items="badWeatherTypes"
              ></v-select>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                readonly
                v-model="formData.weatherKey"
                dense
                label="恶劣天气编号"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2" v-if="formData.weatherType == 0">
              <v-text-field
                v-model="formData.zhName"
                dense
                label="中文名称"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2" v-if="formData.weatherType == 0">
              <v-text-field
                v-model="formData.enName"
                dense
                label="英文名称"
                :rules="[rules.required]"
                required
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-date-time-picker
                label="起编时间"
                v-model="formData.startDate"
                dense
                :rules="[rules.required]"
              ></v-date-time-picker>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-date-time-picker
                label="停编时间"
                v-model="formData.endDate"
                dense
              ></v-date-time-picker>
            </v-col>
            <v-col cols="12" sm="6" md="2" v-if="formData.weatherType == 0">
              <v-text-field
                v-model="formData.genAltitudeLongtitude"
                dense
                label="生成经纬度"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2" v-if="formData.weatherType == 0">
              <v-text-field
                v-model="formData.disAltitudeLongtitude"
                dense
                label="消失经纬度"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                v-model="formData.influenceArea"
                dense
                label="当前影响区域"
                required
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2" v-if="formData.weatherType == 0">
              <v-date-time-picker
                v-model="formData.landDate"
                label="登陆时间"
                dense
              ></v-date-time-picker>
            </v-col>
            <v-col cols="12" sm="6" md="2" v-if="formData.weatherType == 0">
              <v-text-field
                v-model="formData.landLocation"
                dense
                label="登录地点"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                v-model="formData.remark"
                dense
                label="备注"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                v-model="formData.totalTime"
                dense
                label="总时"
                hint="填写停编时间并保存后自动计算"
                readonly
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                v-model="formData.maxWindPower"
                dense
                label="最大风力"
                type="number"
                hint="填写追踪详情后可自动计算"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                v-model="formData.maxWindSpeed"
                dense
                type="number"
                label="最大风速"
                hint="填写追踪详情后可自动计算"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                v-model="formData.maxWaveHeight"
                dense
                type="number"
                label="最大浪高"
                hint="填写追踪详情后可自动计算"
              ></v-text-field>
            </v-col>
            <v-col
              v-if="['3', '4'].includes(formData.weatherType)"
              cols="12"
              sm="6"
              md="2"
            >
              <v-text-field
                v-model="formData.centerAirPressure"
                dense
                type="number"
                label="中心气压"
                hint="填写追踪详情后可自动计算"
              ></v-text-field>
            </v-col>
            <v-col
              cols="12"
              sm="6"
              md="2"
              v-if="['1', '2'].includes(formData.weatherType)"
            >
              <v-text-field
                v-model="formData.minVisibility"
                dense
                :rules="[rules.number]"
                label="最低能见度"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2" v-if="formData.weatherType == 0">
              <v-text-field
                v-model="formData.level12Last"
                dense
                label="12级历时"
              ></v-text-field>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-title class="h6 mt-1">
        恶劣天气追踪
        <v-spacer></v-spacer>
        <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click.stop="createBadweatherrecord"
          v-permission="['恶劣天气追踪:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editBadweatherrecord"
          v-permission="['恶劣天气追踪:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delBadweatherrecord"
          v-permission="['恶劣天气追踪:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-divider></v-divider>
        <v-table-list
          :headers="headers"
          :items="BadWeatherRecords"
          v-model="selected"
        ></v-table-list>
        <v-row></v-row>
      </v-card-text>
      <v-dialog
        attach="#mask"
        max-width="1000"
        v-model="flag"
        hide-overlay
        persistent
        style="z-index: 2"
      >
        <v-card>
          <v-card-title>
            恶劣天气追踪-{{ recordEdit ? '编辑' : '新建' }}
            <v-spacer></v-spacer>
            <v-icon color="red" class="close" @click="closerecordedit">
              mdi-close-circle-outline
            </v-icon>
          </v-card-title>
          <v-card-text>
            <v-form ref="recordform">
              <v-container fluid>
                <v-row>
                  <v-col cols="12" md="3">
                    <v-date-time-picker
                      v-model="recordformData.trackTime"
                      label="追踪时间（北京时间）"
                      required
                      :rules="[rules.required]"
                    ></v-date-time-picker>
                  </v-col>
                  <v-col cols="12" md="3">
                    <v-text-field
                      v-model="recordformData.centerAirPressure"
                      label="中心气压（HPA）"
                      required
                      type="number"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3">
                    <v-text-field
                      v-model="recordformData.centerLocation"
                      label="中心位置"
                      required
                      :rules="[rules.required]"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3">
                    <v-text-field
                      v-model="recordformData.currentInfluenceArea"
                      label="当前影响区域"
                      required
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3" v-if="formData.weatherType == 0">
                    <v-text-field
                      v-model="recordformData.currentMovementDirection"
                      label="当前移动方向"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3">
                    <v-text-field
                      v-model="recordformData.currentMovementSpeed"
                      label="当前移动速度(kt)	"
                      type="number"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3">
                    <v-text-field
                      v-model="recordformData.currentVisibility"
                      type="number"
                      label="当前能见度(公里)"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3">
                    <v-text-field
                      v-model="recordformData.currentWaveHeight"
                      type="number"
                      label="当前浪高"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3">
                    <v-text-field
                      v-model="recordformData.currentWindPower"
                      type="number"
                      label="当前风力"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3">
                    <v-text-field
                      v-model="recordformData.currentWindSpeed"
                      type="number"
                      label="当前风速(kt)"
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12">
                    <v-btn
                      outlined
                      tile
                      color="success"
                      class="mx-1"
                      @click="saverecord"
                      block
                      v-permission="['恶劣天气追踪:编辑']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      {{ isEdit ? '保存' : '创建' }}
                    </v-btn>
                  </v-col>
                </v-row>
              </v-container>
            </v-form>
          </v-card-text>
        </v-card>
      </v-dialog>

      <v-divider class="mt-2"></v-divider>
      <v-card-text>
        <v-attach-list
          class="mt-2"
          :attachments="attachmentRecords"
          @change="changeAttachment"
          ship-code="0"
        ></v-attach-list>
      </v-card-text>
    </v-detail-view>
  </v-container>
</template>
<script>
import vDateTimePicker from '@/components/v-date-time-picker.vue'
import badWeatherTypes from './private/badWeatherTypes'
export default {
  components: { vDateTimePicker },
  name: 'bad-weather-detail',
  data() {
    return {
      selected: false,
      startDateMenu: false,
      endDateMenu: false,
      attachmentRecords: [],
      formData: {
        attachmentIds: [],
        disAltitudeLongtitude: '',
        enName: '',
        endDate: '',
        genAltitudeLongtitude: '',
        influenceArea: '',
        landDate: '',
        landLocation: '',
        level12Last: 0,
        maxWaveHeight: 0,
        maxWindPower: 0,
        maxWindSpeed: 0,
        minVisibility: 0,
        remark: '',
        startDate: '',
        weatherKey: '',
        weatherType: 1,
        zhName: '',
      },
      recordformData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || !v || '请输入数字',
      },
      headers: [
        { text: '中心气压', value: 'centerAirPressure' },
        { text: '中心位置', value: 'centerLocation' },
        { text: '追踪时间', value: 'trackTime' },
        { text: '当前影响区域', value: 'currentInfluenceArea' },
        { text: '当前移动速度', value: 'currentMovementSpeed' },
        { text: '当前浪高', value: 'currentWaveHeight' },
        { text: '当前风力', value: 'currentWindPower' },
        { text: '当前风速', value: 'currentWindSpeed' },
        { text: '当前能见度', value: 'currentVisibility' },
      ],
      BadWeatherRecords: [],
      //
      flag: false,
      watchFlag: false,
      recordEdit: false,
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async getBadweather() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/seaAffairs/badWeather/detailByWeatherId/${this.$route.params.id}`,
      )
      this.BadWeatherRecords = data.list
      this.formData = data
      this.attachmentRecords = data.attachmentRecords
      if (this.formData.weatherType == 0)
        this.headers.push({
          text: '当前移动方向',
          value: 'currentMovementDirection',
        })
    },
    async getBadweatherRecords() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/badWeather/detailByWeatherId/${this.$route.params.id}`,
      )
      this.BadWeatherRecords = data.list
      this.attachmentRecords = data.attachmentRecords
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      //此处给的对应的编辑对应url接口
      const url = this.isEdit
        ? '/business/seaAffairs/badWeather/update'
        : '/business/seaAffairs/badWeather/save'
      // this.isEdit && delete this.formData.weatherKey
      const { errorRaw } = await this.postAsync(url, this.formData)
      if (!errorRaw) {
        this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        goBack()
      }
    },
    async delBadweatherrecord() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/badWeather/record/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      //将这个表格数据重新加载
      await this.getBadweather()
      this.selected = false
    },
    //跳转界面,无需在跳转
    createBadweatherrecord() {
      this.flag = true
      this.watchFlag = true
      this.recordEdit = false
      this.recordformData.weatherKey = this.formData.weatherKey
    },
    editBadweatherrecord() {
      /*this.$router.push({
        path: `/maritime-affairs/bad-weather-record/${this.selected.id}`,
        query: {
          badWeather: this.$route.params.id,
        },
      })*/
      this.recordformData = { ...this.selected }
      this.flag = true
      this.watchFlag = true
      this.recordEdit = true
    },
    async saverecord() {
      if (!this.$refs.recordform.validate()) {
        return
      }
      //此处给的对应的编辑对应url接口
      const url = this.recordEdit
        ? '/business/seaAffairs/badWeather/record/update'
        : '/business/seaAffairs/badWeather/record/save'
      const { errorRaw } = await this.postAsync(url, this.recordformData)
      if (!errorRaw) {
        this.$dialog.message.success(this.recordEdit ? '保存成功' : '创建成功')
        this.recordformData = {}
        this.$refs.recordform.resetValidation()
        await this.getBadweatherRecords()
      }
      this.watchFlag = false
      this.flag = false
    },
    //点击icon图标，使该card不显示
    async closerecordedit() {
      this.flag = false
      this.watchFlag = false
      this.recordformData = {}
      this.$refs.recordform.resetValidation()
    },
  },
  created() {
    this.getBadweather()
    this.badWeatherTypes = badWeatherTypes
    //this.getBadweatherrecord()
  },
  activated() {
    this.flag = this.watchFlag
  },
}
</script>
<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
