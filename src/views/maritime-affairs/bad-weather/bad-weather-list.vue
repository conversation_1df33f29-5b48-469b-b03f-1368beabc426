<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      table-name="恶劣天气编号"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      req-url="/business/seaAffairs/badWeather/page"
      :fix-header="false"
      :search-remain="searchObj"
      :push-params="{ name: 'bad-weather-detail' }"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.zhName"
            label="恶劣天气中文名称"
            outlined
            dense
            append-icon="mdi-magnify"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.weatherType"
            label="恶劣天气类型"
            outlined
            dense
            :items="badWeatherTypes"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="dialog = true"
          v-permission="['恶劣天气编号:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delBadweather"
          v-permission="['恶劣天气编号:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <!-- eslint-disable vue/no-use-v-if-with-v-for -->
      <template v-slot:[`item.weatherType`]="{ item }">
        <div
          v-for="i in badWeatherTypes"
          :key="i.value"
          v-if="i.value == item.weatherType"
        >
          {{ i.text }}
        </div>
      </template>
    </v-table-searchable>
    <bad-weather-new v-model="dialog" @success="success"></bad-weather-new>
  </v-container>
</template>
<script>
import badWeatherNew from './private/bad-weather-new.vue'
import badWeatherTypes from './private/badWeatherTypes'

export default {
  components: { badWeatherNew },
  name: 'bad-weather-list',
  created() {
    //以恶劣天气类型做查询
    this.searchDicts = [
      {
        //dicType是表明字典里的类型
        dicType: 'bad_weather_type',
        label: '恶劣天气类型',
        //key是接口给的参数名
        key: 'weatherType',
      },
    ]
    this.headers = [
      //恶劣天气列表所有的表头
      { text: '恶劣天气编号', value: 'weatherKey' },
      { text: '恶劣天气类型', value: 'weatherType' },
      { text: '中文名称', value: 'zhName' },
      { text: '英文名称', value: 'enName' },
      { text: '起编日期', value: 'startDate' },
      { text: '停编日期', value: 'endDate' },
      { text: '总时（小时）', value: 'totalTime', sortable: true },
      { text: '影响区域', value: 'influenceArea' },
    ]
    //以时间做查询
    this.searchDate = {
      label: '起编日期',
      value: 'startDate',
    }
    this.badWeatherTypes = badWeatherTypes
  },
  data: () => ({
    selected: false,
    searchObj: { zhName: '' },
    dialog: false,
  }),

  methods: {
    editBadweather() {
      this.$router.push(`/maritime-affairs/bad-weather/${this.selected.id}`)
    },
    async delBadweather() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/badWeather/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
    success(id) {
      this.$router.push({ name: 'bad-weather-detail', params: { id } })
    },
  },

  mounted() {},
}
</script>

<style></style>
