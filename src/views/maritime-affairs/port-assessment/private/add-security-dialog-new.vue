<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新建港口评估
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :rules="[rules.required]"
                  v-model="formData.shipCode"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <vs-date-picker
                  label="港口"
                  :rules="[rules.required]"
                  v-model="formData.port"
                  @change="changeTime"
                  outlined
                  dense
                ></vs-date-picker> -->
                <v-text-field
                  :rules="[rules.required]"
                  v-model="formData.route"
                  label="航线"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <vs-date-picker
                  label="港口"
                  :rules="[rules.required]"
                  v-model="formData.port"
                  @change="changeTime"
                  outlined
                  dense
                ></vs-date-picker> -->
                <v-text-field
                  :rules="[rules.required]"
                  v-model="formData.port"
                  label="港口"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="评估日期"
                  :rules="[rules.required]"
                  v-model="formData.fillingDate"
                  @change="changeTime"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-select
                  label="检查类型"
                  v-model="formData.inspectionType"
                  :rules="[rules.required]"
                  :items="insTypes"
                  outlined
                  readonly="true"
                  dense
                ></v-select>
              </v-col> -->
            </v-row>
            <!-- <v-row>
              <v-col cols="12" md="3">
                <v-select
                  v-if="formData.inspectionType === '2'"
                  label="风险等级"
                  v-model="formData.riskLevel"
                  :rules="[rules.required]"
                  :items="风险等级"
                  outlined
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-if="formData.inspectionType === '2'"
                  label="下次窗口日期"
                  v-model="formData.nextInspectionTime"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
            </v-row> -->
            <v-row>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  创建
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'add-security-dialog-new',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({ inspectionType: '0' }),
    },
    date1: {
      type: Date,
      default: null,
    },
  },
  created() {
    this.insTypes = [
      { text: '登轮检查', value: '0' },
      { text: '船舶自查', value: '1' },
      { text: 'PSC检查', value: '2' },
      { text: 'FSC检查', value: '3' },
    ]
    this.风险等级 = [
      { text: '低风险', value: '0' },
      { text: '标准风险', value: '1' },
      { text: '高风险', value: '2' },
    ]
  },
  data() {
    return {
      dialog: false,
      formData: {
        inspectionType: '0',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
    // date1(val) {
    //   // this.dialog = val
    //   console.log(val)
    //   // this.$refs?.form?.resetValidation()
    //   this.formData.nextInspectionTime = val
    // },
    'formData.inspectionTime'(val) {
      // console.log(val)
      /** 
      if (this.formData.inspectionType == '2') {
        let date_1 = new Date(val)
        // console.log(date_1)
        let date_2 = date_1.setMonth(date_1.getMonth() + 0)
        // console.log(date_2)
        if (!!this.formData.riskLevel && this.formData.riskLevel == '0') {
          date_2 = date_1.setMonth(date_1.getMonth() + 9)
        } else if (
          !!this.formData.riskLevel &&
          this.formData.riskLevel == '1'
        ) {
          date_2 = date_1.setMonth(date_1.getMonth() + 5)
        } else if (
          !!this.formData.riskLevel &&
          this.formData.riskLevel == '2'
        ) {
          date_2 = date_1.setMonth(date_1.getMonth() + 2)
        }
        let date_s = new Date(date_2)
        // console.log(date_s)
        //获取yyyy-mm-dd格式日期：date_s.toISOString().substr(0, 10)
        this.formData.nextInspectionTime = date_s.toISOString().substr(0, 10)
      }
      */
      this.changeTime(val, this.formData.riskLevel)
    },
    'formData.riskLevel'(val) {
      // console.log(val)
      /** 
      if (this.formData.inspectionType == '2') {
        let date_1 = new Date(this.formData.inspectionTime)
        // console.log(date_1)
        let date_2 = date_1.setMonth(date_1.getMonth() + 0)
        // console.log(date_2)
        if (!!val && val == '0') {
          date_2 = date_1.setMonth(date_1.getMonth() + 9)
        } else if (!!val && val == '1') {
          date_2 = date_1.setMonth(date_1.getMonth() + 5)
        } else if (!!val && val == '2') {
          date_2 = date_1.setMonth(date_1.getMonth() + 2)
        }
        let date_s = new Date(date_2)
        // console.log(date_s)
        //获取yyyy-mm-dd格式日期：date_s.toISOString().substr(0, 10)
        this.formData.nextInspectionTime = date_s.toISOString().substr(0, 10)
        
      }
      */
      this.changeTime(this.formData.inspectionTime, val)
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    changeTime(inspectionTime, riskLevel) {
      if (!inspectionTime || inspectionTime == '') return
      if (this.formData.inspectionType == '2') {
        let date_1 = new Date(inspectionTime)
        // console.log(date_1)
        let date_2 = date_1.setMonth(date_1.getMonth() + 0)
        // console.log(date_2)
        if (!!riskLevel && riskLevel == '0') {
          date_2 = date_1.setMonth(date_1.getMonth() + 9)
        } else if (!!riskLevel && riskLevel == '1') {
          date_2 = date_1.setMonth(date_1.getMonth() + 5)
        } else if (!!riskLevel && riskLevel == '2') {
          date_2 = date_1.setMonth(date_1.getMonth() + 2)
        }
        let date_s = new Date(date_2)
        // console.log(date_s)
        //获取yyyy-mm-dd格式日期：date_s.toISOString().substr(0, 10)
        this.formData.nextInspectionTime = date_s.toISOString().substr(0, 10)
      }
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/seaAffairs/PortAssessment/save'
      const { errorRaw, data } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success', data)
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
