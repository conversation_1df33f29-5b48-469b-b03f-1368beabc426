<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶事故记录:编辑']"
      :title="`船舶事故详情-${
        ['', '未提交', '审批中', '审批通过', '驳回'][detail.status]
      }`"
      tooltip="船舶事故"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:船舶基本信息>
        <v-container fluid>
          <v-row>
            <v-col cols="2">
              中文船名:{{ detail.shipBaseMixOutputDTO.chShipName }}
            </v-col>
            <v-col cols="2">
              总吨 t:{{ detail.shipBaseMixOutputDTO.grossTonnage }}
            </v-col>
            <v-col cols="2">
              净吨 t:{{ detail.shipBaseMixOutputDTO.netTonnage }}
            </v-col>
            <v-col cols="2">
              建造日期:{{ detail.shipBaseMixOutputDTO.keelLaidDate }}
            </v-col>
            <v-col cols="2">
              型宽 m:{{ detail.shipBaseMixOutputDTO.breadth }}
            </v-col>
            <v-col cols="2">总长 m:{{ detail.shipBaseMixOutputDTO.loa }}</v-col>
            <v-col cols="2">
              船籍港:{{ detail.shipBaseMixOutputDTO.flagPort }}
            </v-col>
          </v-row>
        </v-container>
      </template>
      <template v-slot:事故相关数据>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                md="3"
                v-for="(h, i) in 事故相关数据字段"
                :key="i"
                class="py-0"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :rules="[rules.required]"
                  :label="h.label"
                  dense
                  outlined
                ></v-text-field>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                  :label="h.label"
                ></vs-date-picker>
                <v-date-time-picker
                  v-else-if="h.type === 'time'"
                  v-model="detail[h.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                  :label="h.label"
                ></v-date-time-picker>
              </v-col>
            </v-row>
            <v-row>
              <v-col
                class="py-0"
                cols="12"
                md="2"
                v-for="(h, i) in 船舶人员字段"
                :key="i"
              >
                <v-text-field
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template v-slot:事故损失及性质>
        <v-form :readonly="detail.status === '3'" ref="bform">
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-for="(h, i) in 事故损失及性质字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  dense
                  outlined
                  :label="h.label"
                ></v-text-field>
                <v-text-field
                  v-if="h.type === 'number'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  outlined
                  type="number"
                ></v-text-field>
                <v-radio-group
                  class="mt-0 pt-0"
                  v-if="h.type === 'boolean'"
                  :label="h.label"
                  v-model="detail[h.value]"
                  row
                  dense
                >
                  <v-radio label="是" :value="true"></v-radio>
                  <v-radio label="否" :value="false"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-select
                  v-model="detail.shipwreckOrTotalLoss"
                  label="沉船或全损"
                  :items="['沉船', '全损', '无']"
                  dense
                  outlined
                ></v-select>
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-select
                  v-model="detail.levelAccident"
                  outlined
                  label="事故等级"
                  :items="[
                    '小事故',
                    '一般事故',
                    '大事故',
                    '重大事故',
                    '特大事故',
                  ]"
                  dense
                ></v-select>
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-dict-select
                  dict-type="accident_nature_type"
                  label="事故性质"
                  outlined
                  v-model="detail.natureAccident"
                  dense
                ></v-dict-select>
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-select
                  label="事故类型"
                  outlined
                  v-model="detail.accidentType"
                  :items="accidentTypes"
                  dense
                  readonly
                ></v-select>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <template v-for="(h, i) in 长文本字段">
                <v-col
                  :key="h.label"
                  class="text-right body-2 caption"
                  cols="1"
                >
                  {{ h.label }}
                </v-col>
                <v-col :key="i" cols="11">
                  <v-textarea
                    row-height="19"
                    class="ml-1"
                    v-model="detail[h.value]"
                    dense
                    rows="1"
                    filled
                    auto-grow
                  ></v-textarea>
                </v-col>
              </template>
            </v-row>
            <v-row>
              <v-col cols="12">
                <v-attach-list
                  title="关联证据文件"
                  :disabled="detail.status === '3'"
                  :attachments="attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="detail.shipCode"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template v-if="detail.status !== '3'" #人员伤亡情况按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCas"
          v-permission="['人员伤亡:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedCas"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateCas"
          v-permission="['人员伤亡:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <!-- <v-btn
          :disabled="!selectedCas"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCas"
          v-permission="['人员伤亡:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template v-slot:人员伤亡情况>
        <v-table-list
          v-model="selectedCas"
          :headers="casHeaders"
          :items="casualties"
        ></v-table-list>
      </template>
      <template v-slot:船舶报表>
        <v-table-list :headers="reportHeaders" :items="reports">
          <template v-slot:[`item.reportName`]="{ item }">
            <router-link
              :to="{
                name:
                  item.type === 'a'
                    ? 'accident-report-detail'
                    : item.type === 'n'
                    ? 'dept-report-info-detail2'
                    : 'casualties-report-detail',
                params: { id: item.id },
              }"
            >
              {{ item.reportName }}
            </router-link>
          </template>
        </v-table-list>
      </template>
      <template v-slot:船舶报表按钮>
        <v-btn
          :disabled="
            (!!accidentReportId && accidentReport.status !== '4') ||
            !detail.accidentType
          "
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="addAccidentReport"
          v-permission="['人员伤亡:海损机损事故报告']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          海损机损事故报告
        </v-btn>
        <v-btn
          :disabled="!!casualtiesReportId && casualtiesReport.status !== '4'"
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="addCasualtiesReport"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          人员伤亡报告
        </v-btn>
        <!-- TODO:未完成 -->
        <!-- <v-btn
          :disabled="
            !!nonstandartReportId && accidentReport.status !== '已驳回'
          "
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="addNonstandartReport"
          v-permission="['人员伤亡:不符合报告']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          不符合报告
        </v-btn> -->
        <v-btn
          :disabled="
            !!nonstandartReportId && accidentReport.status !== '已驳回'
          "
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="addNonstandartReport"
          v-permission="['人员伤亡:不符合报告']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          不符合报告
        </v-btn>
      </template>
      <template v-if="!detail.isClosed" #保险理赔项目按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createIns"
          v-permission="['保险理赔项目:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedIns"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateIns"
          v-permission="['保险理赔项目:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <!-- <v-btn
          :disabled="!selectedCas"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCas"
          v-permission="['保险理赔项目:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template #保险理赔项目>
        <v-table-list
          v-model="selectedIns"
          :headers="insHeaders"
          :items="insurances"
        ></v-table-list>
      </template>
    </v-detail-view>
    <cas-dialog
      @success="loadCasualty"
      v-model="dialog1"
      :initialData="initCas"
    ></cas-dialog>
    <ins-dialog
      @success="loadInsurance"
      v-model="dialog2"
      :initialData="initIns"
    ></ins-dialog>
  </v-container>
</template>
<script>
import casDialog from './private/cas-dialog.vue'
import dictHelper from '@/mixin/dictHelper'
import InsDialog from './private/ins-dialog.vue'
export default {
  components: { casDialog, InsDialog },
  mixins: [dictHelper],
  name: 'accident-detail',
  created() {
    this.backRouteName = 'accident-list'
    this.subtitles = [
      '船舶基本信息',
      '事故相关数据',
      '事故损失及性质',
      '人员伤亡情况',
      '船舶报表',
      '费用项目',
      '保险理赔项目',
    ]
    this.事故相关数据字段 = [
      { label: '出发港口', value: 'departurePort', type: 'string' },
      { label: '出发日期', value: 'departureDate', type: 'date' },
      { label: '目的港口', value: 'destinationPort', type: 'string' },
      { label: '抵达日期', value: 'destinationDate', type: 'date' },
      { label: '事故时水尺（米）', value: 'accidentDraft', type: 'string' },
      { label: '载货情况[吨]', value: 'cargoCondition', type: 'string' },
      { label: '事故发生时间（地方时间）', value: 'localTime', type: 'time' },
      { label: '事发天气状况', value: 'weatherCondition', type: 'string' },
      { label: '事故地点或经纬度', value: 'place', type: 'string' },
    ]
    this.船舶人员字段 = [
      { label: '船长', value: 'captain', type: 'string' },
      { label: '总船长', value: 'chiefCaptain', type: 'string' },
      { label: '轮机长', value: 'chiefEngineer', type: 'string' },
      { label: '大副', value: 'firstMate', type: 'string' },
      { label: '二副', value: 'secondMate', type: 'string' },
      { label: '三副', value: 'thirdMate', type: 'string' },
      { label: '海务主管', value: 'marineSupervisor', type: 'string' },
      { label: '主要监督员', value: 'mainSupervisor', require: false },
      { label: '现场见证人', value: 'witness', require: false },
    ]
    this.事故损失及性质字段 = [
      { label: '死亡人数', value: 'deathDoll', type: 'number' },
      { label: '受伤人数', value: 'injuryDoll', type: 'number' },
      { label: '发生污染', value: 'occurPollution', type: 'boolean' },
      { label: '事故状况', value: 'conditions', type: 'string' },
      { label: '船期损失（小时）', value: 'scheduleLoss', type: 'number' },
      { label: '经济损失', value: 'economicLoss', type: 'number' },
    ]
    this.长文本字段 = [
      { label: '事故损失情况简述', value: 'briefDescription' },
      { label: '事故经过概述', value: 'overview' },
      { label: '事故原因分析', value: 'causeAnalysis' },
      { label: '事故教训', value: 'lesson' },
      { label: '纠正预防措施', value: 'measures' },
      { label: '对事故责任人的处理意见或建议', value: 'suggestions' },
      { label: '措施落实情况或其他需要说明的情况', value: 'otherSituations' },
    ]
    this.casHeaders = [
      { text: '人员名称', value: 'crewName' },
      { text: '年龄', value: 'age' },
      { text: '身份证', value: 'identityCard' },
      { text: '职位', value: 'position' },
      { text: '伤亡类型', value: 'casualtyType' },
      { text: '死亡原因', value: 'reason' },
    ]
    this.reportHeaders = [
      { text: '报表名称', value: 'reportName' },
      { text: '上传人', value: 'poster' },
      { text: '上传时间', value: 'postTime' },
      { text: '状态', value: 'status' },
    ]
    this.insHeaders = [
      { text: '编号', value: 'number' },
      { text: '项目名称', value: 'itemName' },
      { text: '费用', value: 'expense' },
      { text: '备注', value: 'remark' },
    ]
    // 海务、机务、通导、i办、船员
    this.accidentTypes = [
      { text: '海务', value: '海务' },
      { text: '机务', value: '机务' },
      { text: '通导', value: '通导' },
      { text: 'i办', value: 'i办' },
      { text: '船员', value: '船员' },
    ]
  },
  computed: {
    // TODO:待不符合报告添加完毕，提交时将进行校验，未填写的不符合报告将不会被提交
    canSubmit() {
      // return (
      //   (!!this.nonstandartReportId &&
      //     !!this.accidentReportId &&
      //     !this.detail.auditParams) ||
      //   !!this.detail.auditParams?.isReject
      // )
      return (
        ((!!this.nonstandartReportId &&
          !!this.accidentReportId &&
          !this.detail.auditParams) ||
          this.detail.auditParams.taskId) &&
        this.detail.status == 2
      )
    },
    accidentReportTempId() {
      return this.reportTypeMap?.find((i) => i.dictLabel === 'accidentReport')
        ?.dictValue
    },
    nonstandartReportTempId() {
      return this.reportTypeMap?.find(
        (i) => i.dictLabel === 'nonstandartReport',
      )?.dictValue
    },
    accidentReportId() {
      return (
        this.detail.accidentReportId ||
        this.$store.state.reportParams.businessParams.find(
          (b) => b.businessType === 'shipAccident',
        )?.reportId
      )
    },
    // TODO:待修改，可暂时废弃
    nonstandartReportId() {
      return (
        this.detail.nonstandartReportId ||
        this.$store.state.reportParams.businessParams.find(
          (b) => b.templateId === 'nonstandartReport',
          //this.nonstandartReportTempId,
        )?.reportId
      )
    },

    casualtiesReportId() {
      return (
        this.detail.casualtiesReportId ||
        this.$store.state.reportParams.businessParams.find(
          (b) => b.businessType === 'accidentCasuality',
        )?.reportId
      )
    },
    reports() {
      let reports = []
      if ('id' in this.accidentReport) reports.push(this.accidentReport)
      if ('id' in this.nonstandartReport) reports.push(this.nonstandartReport)
      if ('id' in this.casualtiesReport) reports.push(this.casualtiesReport)
      return reports
    },
  },
  watch: {
    accidentReportId(value) {
      if (value) {
        this.detail.accidentReportId = value
        this.loadReportInfo(value, 'a')
      }
    },
    nonstandartReportId(value) {
      if (value) {
        this.detail.nonstandartReportId = value
        this.loadReportInfo(value, 'n')
      }
    },
    casualtiesReportId(value) {
      if (value) {
        this.detail.casualtiesReportId = value
        this.loadReportInfo(value, 'c')
      }
    },
  },
  data() {
    return {
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      detail: {
        shipBaseMixOutputDTO: {},
        isClosed: false,
        accidentReportId: '',
        nonstandartReportId: '',
        casualtiesReportId: '',
        status: 0,
      },
      attachmentRecords: [],
      reportTypeMap: [],
      casualties: [],
      insurances: [],
      selectedCas: false,
      selectedIns: false,
      initCas: {},
      initIns: {},
      dialog1: false,
      dialog2: false,
      accidentReport: {},
      nonstandartReport: {},
      casualtiesReport: {},
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate() || !this.$refs.bform.validate()) {
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/AccidentRecord/record/update',
        this.detail,
      )
      if (errorRaw) false
      if (notMove) return this.detail.id
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/AccidentRecord/record/submit',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/record/getById/${this.$route.params.id}`,
      )
      this.detail = { ...data, casualtiesReportId: '' }
      this.$refs.form.resetValidation()
      this.attachmentRecords = data.attachmentRecords
      await this.loadCasualty()
      await this.loadInsurance()
      this.reportTypeMap = await this.getDictByType('report_type_mapping')
      // if (data.accidentReportId)
      //   await this.loadReportInfo(data.accidentReportId, 'a')
    },
    async loadCasualty() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/casualties/getByAccId/${this.$route.params.id}`,
        { size: 99, current: 1 },
      )
      this.casualties = data.records
    },
    async loadInsurance() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/insurance/getByAccId/${this.$route.params.id}`,
        { size: 99, current: 1 },
      )
      this.insurances = data.records
    },

    async createCas() {
      this.initCas = {}
      this.dialog1 = true
    },
    async updateCas() {
      this.initCas = this.selectedCas
      this.dialog1 = true
    },
    async createIns() {
      this.initIns = {}
      this.dialog2 = true
    },
    async updateIns() {
      this.initIns = this.selectedIns
      this.dialog2 = true
    },
    // async delCas() {},
    addAccidentReport() {
      this.$store.commit('emitBussiness', {
        businessType: 'shipAccident',
        businessId: this.detail.id,
        otherParams: {
          shipCode: this.detail.shipBaseMixOutputDTO.shipCode,
          // type: this.detail.accidentType,
          shipFlag:
            this.detail.shipBaseMixOutputDTO.flagPort +
            ',' +
            this.detail.shipBaseMixOutputDTO.flagState,
          shipName: this.detail.shipBaseMixOutputDTO.chShipName,
          accidentType: this.detail.accidentType,
        },
      })
      this.$router.push({
        name: 'accident-report-detail',
        params: {
          id: 'new',
          businessId1: this.detail.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },
    // TODO: 未完成
    // addNonstandartReport() {
    //   this.$store.commit('emitBussiness', {
    //     businessType: 'nonstandardAccident',
    //     businessId: this.detail.id,
    //     templateId: this.nonstandartReportTempId,
    //   })
    //   this.$router.push({
    //     name: 'report-emit-detail',
    //     params: { id: this.nonstandartReportTempId },
    //   })
    // },
    addNonstandartReport() {
      this.$store.commit('emitBussiness', {
        businessType: 'shipAccident_Non',
        businessId: this.detail.id,
        templateId: this.nonstandartReportTempId,
        otherParams: {
          shipCode: this.detail.shipBaseMixOutputDTO.shipCode,
          // type: this.detail.accidentType,
          shipFlag:
            this.detail.shipBaseMixOutputDTO.flagPort +
            ',' +
            this.detail.shipBaseMixOutputDTO.flagState,
          shipName: this.detail.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        //'report-emit-detail',
        name: 'dept-report-info-detail2',
        params: {
          id: this.nonstandartReportTempId,
          businessId1: this.detail.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },

    addCasualtiesReport() {
      this.$store.commit('emitBussiness', {
        businessType: 'accidentCasuality',
        businessId: this.detail.id,
        otherParams: {
          shipCode: this.detail.shipBaseMixOutputDTO.shipCode,
          type: this.detail.accidentType,
          shipFlag:
            this.detail.shipBaseMixOutputDTO.flagPort +
            ',' +
            this.detail.shipBaseMixOutputDTO.flagState,
          shipName: this.detail.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        name: 'casualties-report-detail',
        params: {
          id: 'new',
          businessId1: this.detail.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },

    async loadReportInfo(reportId, type) {
      // const { data } = await this.getAsync(
      //   '/business/seaAffairs/deptReport/getReportDetailById',
      //   { reportId },
      // )
      // const { deptReport } = data
      // deptReport['status'] = ['', '草稿', '审批中', '已审批', '已驳回'][
      //   deptReport.status
      // ]
      // if (type === 'a') this.accidentReport = deptReport
      // if (type === 'n') this.nonstandartReport = deptReport
      if (type === 'a') {
        const { data } = await this.getAsync(
          '/business/ismAffairs/ismAccidentBrief/getDetailById',
          { id: reportId },
        )
        this.accidentReport = {
          id: data.id,
          reportName: `${data.type}事故报告`,
          poster: data.handlerNickName,
          postTime: data.formDate,
          status: ['', '草稿', '审批中', '已审批', '已驳回'][data.status],
          type,
        }
      } else if (type === 'n') {
        // TODO: 不符合报告修改
        const { data } = await this.getAsync(
          // '/business/ismAffairs/ismNonstandardAccidentBrief/getDetailById',
          '/business/seaAffairs/dept-report-non-compliance/getReportDetailById',
          { reportId: reportId },
        )
        console.log(data)
        this.nonstandartReport = {
          id: data.id,
          reportName: `事故不符合报告`,
          poster: data.deptReportNonCompliance.poster,
          postTime: data.deptReportNonCompliance.postTime,
          status: ['', '草稿', '审批中', '已审批', '已驳回'][
            data.deptReportNonCompliance.status
          ],
          type,
        }
      } else if (type === 'c') {
        const { data } = await this.getAsync(
          '/business/ismAffairs/ismAccidentCasualties/getDetailById',
          { id: reportId },
        )
        this.casualtiesReport = {
          id: data.id,
          reportName: '伤亡人员情况',
          poster: data.handlerNickName,
          postTime: data.formDate,
          status: ['', '草稿', '审批中', '已审批', '已驳回'][data.status],
          type,
        }
      }
    },
    async loadAccident() {
      const { data } = await this.getAsync(
        '/business/ismAffairs/ismAccidentBrief/getRecordsByBusinessId',
        { businessId: this.$route.params.id },
      )
      this.detail.accidentReportId = data[0]?.id
    },
    async loadCasualtyReport() {
      const { data } = await this.getAsync(
        '/business/ismAffairs/ismAccidentCasualties/getRecordsByBusinessId',
        { businessId: this.$route.params.id },
      )
      this.detail.casualtiesReportId = data[0]?.id
    },
    // TODO: 不符合报告的加载
    async loadNonstandartReport() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/dept-report-non-compliance/getRecordsByBusinessId',
        { businessId: this.$route.params.id },
      )
      this.detail.nonstandartReportId = data[0]?.id
      console.log(this.detail.nonstandartReportId)
    },
  },

  async mounted() {
    await this.loadDetail()
    this.loadAccident()
    this.loadCasualtyReport()
    this.loadNonstandartReport()
  },

  beforeDestroy() {
    this.$store.commit('removeBussinessParam', this.detail.id)
  },
}
</script>

<style></style>
