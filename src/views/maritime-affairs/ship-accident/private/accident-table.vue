<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      use-ship
      use-status
      :search-remain="searchObj"
      outlined
    ></v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'accident-list-dialog',
  props: {
    searchObj: {
      type: Object,
      default: () => ({ natureAccident: '' }),
    },
  },
  created() {
    this.tableName = '船舶事故'
    this.reqUrl = '/business/seaAffairs/AccidentRecord/record/list'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '船长', value: 'captain' },
      { text: '轮机长', value: 'chiefEngineer' },
      { text: '死亡人数', value: 'deathDoll' },
      { text: '受伤人数', value: 'injuryDoll' },
      { text: '发生污染', value: 'occurPollution' },
      { text: '沉船或全损', value: 'shipwreckOrTotalLoss' },
      { text: '事故等级', value: 'levelAccident' },
      { text: '结案', value: 'isClosed' },
      { text: '审批状态', value: 'status' },
    ]
    this.searchDate = {
      interval: true,
      value: 'beginDate',
    }
    this.pushParams = { name: 'accident-detail' }
  },

  data() {
    return {
      selected: false,
      dialog: false,
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
