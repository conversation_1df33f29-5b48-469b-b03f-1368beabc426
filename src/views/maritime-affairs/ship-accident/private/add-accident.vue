<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新增事故
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row justify="center">
              <v-col cols="4">
                <v-ship-select
                  label="事故船舶"
                  v-model="shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="4">
                <v-date-time-picker
                  label="事故时间"
                  v-model="localTime"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></v-date-time-picker>
              </v-col>
              <v-col cols="4">
                <v-select
                  label="事故类型"
                  outlined
                  v-model="accidentType"
                  :items="accidentTypes"
                  dense
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'add-accident',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    // 海务、机务、通导、i办、船员
    this.accidentTypes = [
      { text: '海务', value: '海务' },
      { text: '机务', value: '机务' },
      { text: '通导', value: '通导' },
      { text: 'i办', value: 'i办' },
      { text: '船员', value: '船员' },
    ]
  },
  data() {
    return {
      dialog: false,
      formData: {},
      shipCode: '',
      localTime: '',
      accidentType: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/seaAffairs/AccidentRecord/record/insert'
      const { errorRaw, data } = await this.postAsync(url, {
        shipCode: this.shipCode,
        localTime: this.localTime,
        attachmentIds: [],
        accidentType: this.accidentType,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success', data)
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
