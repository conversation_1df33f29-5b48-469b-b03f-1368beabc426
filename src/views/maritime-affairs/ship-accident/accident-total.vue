<template>
  <v-container fluid>
    <v-card>
      <v-card-title>按年度统计</v-card-title>
      <v-container fluid>
        <v-radio-group v-model="totalWay" row style="width: 100%">
          <v-row>
            <v-col md="8" cols="12">
              <v-radio value="time">
                <template v-slot:label>
                  <strong>按时间统计</strong>
                  <v-row>
                    <v-col class="ml-1" cols="5">
                      <vs-date-picker
                        label="开始时间"
                        dense
                        outlined
                        v-model="fromTime"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="5">
                      <vs-date-picker
                        label="结束时间"
                        dense
                        outlined
                        v-model="toTime"
                      ></vs-date-picker>
                    </v-col>
                  </v-row>
                </template>
              </v-radio>
            </v-col>
            <v-col md="4" cols="12">
              <v-radio value="type">
                <template v-slot:label>
                  <strong class="mr-3">按事故性质统计</strong>
                  <v-dict-select
                    dict-type="accident_nature_type"
                    label="事故性质"
                    v-model="type"
                    dense
                  ></v-dict-select>
                </template>
              </v-radio>
            </v-col>
          </v-row>
        </v-radio-group>
      </v-container>
      <v-card-actions>
        <v-btn block dark color="blue darken-1" @click="search">查询</v-btn>
      </v-card-actions>
    </v-card>
    <v-card class="mt-2">
      <v-card-title>查询结果(单位:艘次)</v-card-title>
      <v-card-text>
        <v-container fluid>
          <v-divider></v-divider>
          <v-data-table
            hide-default-footer
            class="use-divider"
            dense
            :headers="headers"
            :items="result"
          >
            <template
              v-for="h in headers"
              v-slot:[`item.${h.value}`]="{ item }"
            >
              <v-btn
                v-if="totalWay === 'time'"
                @click="openDialog(h.originValue)"
                color="blue"
                :key="h.value"
                text
                small
              >
                {{ item[h.value] }}
              </v-btn>
              <v-btn
                v-else-if="totalWay === 'type' && h.value === 'totalNum'"
                @click="openDialog(item.year)"
                color="blue"
                :key="h.value"
                text
                small
              >
                {{ item[h.value] }}
              </v-btn>
              <div v-else :key="h.value">{{ item[h.value] }}</div>
            </template>
          </v-data-table>
          <v-divider></v-divider>
        </v-container>
      </v-card-text>
    </v-card>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="auto"
      height="400"
      v-model="dialog"
      absolute="true"
    >
      <v-card>
        <v-card-title>
          <!-- 船舶事故 -->
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <div class="dialgDiv">
          <accident-table
            :searchObj="searchObj"
            :title="false"
            :table-name="false"
          ></accident-table>
        </div>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import accidentTable from './private/accident-table.vue'
export default {
  components: { accidentTable },
  name: 'accident-total',
  mixins: [dictHelper],
  created() {
    this.searchDate = {
      label: '起编日期',
      value: 'startDate',
    }
  },
  data() {
    return {
      totalWay: '',
      datesMenu: false,
      year: new Date(Date.now()).getFullYear(),
      dates: [],
      headers: [],
      type: '',
      result: [],
      fromTime: '',
      toTime: '',
      searchObj: {},
      dialog: false,
    }
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
  },

  methods: {
    closeForm() {
      // this.$emit('change', false)
      this.dialog = false
    },
    async search() {
      // if (this.totalWay === 'year') {
      //   const { data } = await this.getAsync(
      //     '/business/seaAffairs/AccidentRecord/statistic/getStatisticByTime',
      //     {
      //       year: this.year,
      //     },
      //   )
      //   this.headers = this.typeHeaders
      //   this.result = data
      // } else
      if (this.totalWay === 'time') {
        const { data } = await this.getAsync(
          '/business/seaAffairs/AccidentRecord/statistic/getStatisticByTime',
          {
            fromTime: this.fromTime,
            toTime: this.toTime,
          },
        )
        this.searchObj = {
          fromTime: this.fromTime,
          toTime: this.toTime,
          natureAccident: '',
        }
        this.headers = this.typeHeaders
        this.result = data
      } else if (this.totalWay === 'type') {
        const { data } = await this.getAsync(
          '/business/seaAffairs/AccidentRecord/statistic/getStatisticByType',
          {
            type: this.type,
          },
        )
        this.searchObj = {
          natureAccident: this.type,
        }
        this.headers = [
          { text: '事故总艘次', value: 'totalNum' },
          { text: '年份', value: 'year' },
        ]
        this.result = data
      }
    },
    openDialog(item) {
      if (this.totalWay === 'time') {
        this.$set(this.searchObj, 'natureAccident', item)
      } else if (this.totalWay === 'type') {
        this.$set(this.searchObj, 'year', item)
      }
      this.$nextTick(() => {
        this.dialog = true
      })
    },
  },

  async mounted() {
    const types = await this.getDictByType('accident_nature_type')
    this.typeHeaders = types.map((i) => ({
      text: i.dictLabel,
      value: i.dictLabel,
      originValue: i.dictValue,
      sortable: false,
    }))
    this.typeHeaders.push({
      text: '事故总艘次',
      value: '事故总艘次',
      originValue: '',
      sortable: false,
    })
  },
}
</script>

<style>
.dialogDiv {
  min-height: 300px;
  height: 400px;
  max-height: 450px;
  overflow: auto;
}
</style>
