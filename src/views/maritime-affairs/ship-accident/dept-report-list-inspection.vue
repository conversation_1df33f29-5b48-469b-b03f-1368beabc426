<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            outlined
            label="状态"
            dense
            :items="statusMap"
            clearable
          ></v-select>
        </v-col> -->
      </template>
      <template #btns>
        <v-btn
          v-permission="['港口国/船旗国检查报告:新增']"
          outlined
          color="success"
          class="mx-1"
          @click="addNonstandartReport"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAcc"
          v-permission="['港口国/船旗国检查报告:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipInfo`]="{ item }">
        {{ item.shipInfo ? item.shipInfo.chShipName : '船舶名称' }}
      </template>
      <template v-slot:[`item.type`]="{ item }">
        {{ ['PSC', 'FSC'][item.type] }}
      </template>
      <!-- <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{
            item.status == 2
              ? `待[${item.businessStatus}]审批`
              : statuses[item.status]
          }}
        </v-chip>
      </template> -->
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'dept-report-list-inspection',
  created() {
    this.tableName = '港口国/船旗国检查报告'
    this.reqUrl = '/business/seaAffairs/DeptReportInspection/recordPage'
    this.headers = [
      { text: '编号', value: 'reportNumber' },
      { text: '船舶名称', value: 'shipInfo' },
      { text: '受检港口', value: 'inspectionPort' },
      { text: '检查类别', value: 'type' },
      { text: '检查日期', value: 'inspectionDate' },
      // {
      //   text: '符合规定情况、事故、险情的简要描述',
      //   value: 'reportDescription',
      // },
      // {
      //   text: '符合规定情况、事故、险情的简要描述报告日期',
      //   value: 'reportDate',
      // },
      // {
      //   text: '符合规定情况、事故、险情的简要描述报告报告人',
      //   value: 'reporter',
      // },
      // {
      //   text: '分析不符合规定情况、事故、险情产生的原因',
      //   value: 'reportCauses',
      // },
      { text: '创建日期', value: 'createTime' },
      { text: '更新日期', value: 'updateTime' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'dept-report-inspection-detail' }
    //dept-report-info-detail//dept-report-detail
    this.statuses = ['', '草稿', '待审批', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '待审批', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
    ]
    this.类型 = [
      { text: 'PSC', value: 0 },
      { text: 'FSC', value: 1 },
      // { text: '季度检查Quarterly check', value: 2 },
      // { text: '险情Risks', value: 3 },
      // { text: '船舶自查Ship self-check', value: 4 },
      // { text: '外审External audit', value: 5 },
      // { text: '事故Accidents', value: 6 },
      // { text: '其他Other', value: 7 },
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: {
        businessType: 'shipAccident',
        formCode: '47-090100-1',
      },
    }
  },

  methods: {
    addNonstandartReport() {
      // this.$store.commit('emitBussiness', {
      //   businessType: 'shipAccident',
      //   businessId: this.detail.id,
      //   templateId: this.nonstandartReportTempId,
      // })
      this.$router.push({
        name: 'dept-report-inspection-detail',
        //'report-emit-detail',
        // params: { id: this.nonstandartReportTempId },
        params: { id: 'new' },
      })
    },
    async delAcc() {
      if (!this.selected.status || this.selected.status > 1) {
        this.$dialog.message.error(`只能删除草稿状态的记录！`)
        return
      }

      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/DeptReportInspection/deleteById',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
