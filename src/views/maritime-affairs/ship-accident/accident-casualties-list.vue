<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns></template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'accident-casualties-list',
  created() {
    this.tableName = '人员伤亡报表'
    this.reqUrl = '/business/ismAffairs/ismAccidentCasualties/recordPage'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船名', value: 'shipInfo' },
      { text: '报表类型', value: 'type' },
      { text: '经办人', value: 'dept' },
      { text: '部门', value: 'handlerNickName' },
      { text: '填表日期', value: 'formDate' },
      { text: '备注', value: 'remark' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '填表日期',
      value: 'formDate',
    }
    this.pushParams = { name: 'casualties-report-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {},
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
