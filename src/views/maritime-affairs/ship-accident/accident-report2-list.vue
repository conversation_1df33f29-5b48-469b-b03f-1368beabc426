<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            outlined
            label="状态"
            dense
            :items="statusMap"
            clearable
          ></v-select>
        </v-col> -->
      </template>
      <template #btns></template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// businessStatus	业务状态	string
// dept	部门：轮机部/甲板部	string
// formDate	填表日期	string
// handler	经办人	string
// handlerNickName	经办人名称	string
// id	主键	string
// remark	备注	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// status	流程状态	string
// type	报表类型：海损/机损	string
export default {
  name: 'accident-report2-list',
  created() {
    this.tableName = '海损机损事故报告审批'
    this.reqUrl = '/business/ismAffairs/ismAccidentBrief/recordPage'
    this.headers = [
      { text: '船名', value: 'shipInfo' },
      { text: '报表类型', value: 'type' },
      { text: '经办人', value: 'dept' },
      { text: '部门', value: 'handlerNickName' },
      { text: '填表日期', value: 'formDate' },
      { text: '备注', value: 'remark' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'accident-report-detail' }
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
    ]
    this.searchDate = {
      label: '填表日期',
      value: 'formDate',
    }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        businessType: 'shipAccident',
        formCode: '48-090101-1',
      },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/AccidentRecord/record/deleteById',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
