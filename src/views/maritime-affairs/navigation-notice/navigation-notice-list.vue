<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/maritime-affairs/book-data/navigation-notice/detail/new"
          v-permission="['航海通告:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delNavigation"
          v-permission="['航海通告:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'navigation-notice-list',
  created() {
    this.tableName = '航海通告'
    this.reqUrl = '/business/seaAffairs/navigation-notice/page'
    this.searchDicts = [
      {
        dicType: 'navigation_notice_type',
        label: '通告名称', //通告期数 ->通告名称
        key: 'type',
      },
    ]
    this.headers = [
      { text: '年份', value: 'code', hideShip: true }, //编号 ->年份
      { text: '通告期数', value: 'name' }, //通告名称 ->通告期数
      { text: '通告名称', value: 'type' }, //通告期数 ->通告名称
      { text: '更新日期', value: 'updateDate' },
      { text: '经办人', value: 'handler' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '更新日期',
      value: 'updateDate',
    }
    this.pushParams = {
      name: 'navigation-notice-detail',
    }
  },
  data: () => ({
    selected: undefined,
  }),

  methods: {
    linkDownload(url) {
      window.open(url, '_blank')
    },
    editNavigation() {
      this.$router.push(
        `/maritime-affairs/navigation-notice/${this.selected.id}`,
      )
    },
    async delNavigation() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '//business/seaAffairs/navigation-notice/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
