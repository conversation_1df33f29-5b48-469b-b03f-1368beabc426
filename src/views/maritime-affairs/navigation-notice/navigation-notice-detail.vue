<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['航海通告:编辑']"
      :title="`航海通告-${isEdit ? notice.code : '新增'}`"
      backRouteName="navigation-notice-list"
      :tooltip="isEdit ? notice.code : '新增'"
      :subtitles="[]"
      @save="save"
    >
      <v-card-text>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col v-ship cols="12" md="6">
                <v-text-field
                  v-model="notice.code"
                  label="年份"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="notice.name"
                  label="通告期数"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-dict-select
                  dictType="navigation_notice_type"
                  label="通告名称"
                  v-model="notice.type"
                ></v-dict-select>
              </v-col>
              <!-- <v-col cols="12" md="6">
                <v-text-field
                  v-model="notice.illustration"
                  label="通告说明"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="6">
                <v-menu
                  v-model="dateMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="notice.updateDate"
                      label="更新日期"
                      append-icon="mdi-calendar"
                      readonly
                      clearable
                      outlined
                      dense
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="notice.updateDate"
                    @input="dateMenu = false"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="notice.handler"
                  label="经办人"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="6">
                <v-text-field
                  v-model="notice.remark"
                  label="备注"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="attachmentRecords"
                  @change="changeAttachment"
                  ship-code="0"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'navigation-notice-detail',
  data() {
    return {
      dateMenu: false,
      attachmentRecords: [],
      notice: {
        code: '',
        content: '',
        name: '',
        updateDate: new Date(Date.now()).toISOString().substr(0, 10),
        illustration: '',
        handler: this.$local.data.get('userInfo').nickName,
        type: '',
        handle: this.$local.data.get('userInfo').nickName,
        businessType: '0',
        attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
    }
  },
  computed: {
    // 获取当前路由的参数
    isEdit() {
      return this.$route.params.id !== 'new'
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.notice.attachmentIds = attachmentIds
    },

    async getNavigation() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/seaAffairs/navigation-notice/record/${this.$route.params.id}`,
      )
      this.attachmentRecords = data.attachmentRecords
      this.notice = data
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/navigation-notice/update'
        : '/business/seaAffairs/navigation-notice/save'
      const { errorRaw } = await this.postAsync(url, this.notice)
      if (!errorRaw) {
        this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        goBack()
      }
    },
  },
  created() {
    this.getNavigation()
  },
}
</script>

<style></style>
