<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }} {{ tableName }}
        <v-spacer></v-spacer>
        <v-switch
          class="mx-1"
          v-model="isExcel"
          :label="isExcel ? 'Excel' : '手动录入'"
        ></v-switch>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text v-if="isExcel">
        <div class="pb-0 mb-1">excel导入</div>
        <v-row>
          <v-col cols="12" md="2">
            <v-dict-select
              label="图类型"
              dictType="inventory_chart_type"
              v-model="excel.type"
              required
              :rules="[rules.required]"
            ></v-dict-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-import-btn
              v-show="excel.type"
              import-url="/business/seaAffairs/shipMaterials/drawInit"
              :other-params="excel"
              @importSuccess="onImportSuccess"
            ></v-import-btn>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-text v-else>
        <div class="pb-0 mb-1">手动录入</div>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2" v-for="(h, i) in formHeaders" :key="i">
                <v-dict-select
                  v-if="h.value === 'type'"
                  label="图类型"
                  dictType="inventory_chart_type"
                  v-model="form[h.value]"
                  required
                  :rules="[rules.required]"
                ></v-dict-select>
                <v-text-field
                  v-else-if="h.value === 'editionDate'"
                  v-model="form.editionDate"
                  label="出版日期"
                  :rules="[rules.required]"
                  required
                ></v-text-field>

                <v-text-field
                  v-else
                  v-model="form[h.value]"
                  :label="h.text"
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['本轮资料清单-图:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      :search-remain="searchObj"
      :single-select="false"
      @dbclick="editChart"
      use-ship
      use-ship-id
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            v-model="searchObj.chartNo"
            label="图号"
            outlined
            dense
            append-icon="mdi-magnify"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            clearable
            v-model="searchObj.newEditionFlag"
            dense
            outlined
            :items="items"
            label="是否过期"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="!haveSelect"
          @click="apply"
          v-permission="['本轮资料清单-图:申请更新']"
        >
          <v-icon left>mdi-update</v-icon>
          申请更新
        </v-btn>
        <v-btn
          :loading="checking"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="check"
          v-permission="['本轮资料清单-图:手动检测']"
        >
          <v-icon left>mdi-file-refresh-outline</v-icon>
          手动检测
        </v-btn>
        <v-btn
          :disabled="selected.length !== 1"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delChart"
          v-permission="['本轮资料清单-图:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit && selected.length === 0"
          @click="formShow = true"
          v-permission="['本轮资料清单-图:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
      </template>
      <template v-slot:[`item.newEditionFlag`]="{ item }">
        <v-chip small color="success" v-if="item.newEditionFlag">新版</v-chip>
        <v-chip small color="warning" v-else-if="item.newEditionFlag === false">
          已过期
        </v-chip>
        <v-chip small v-else>未知</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'sup-chart-list',
  created() {
    this.tableName = '主管船资料清单-图'
    this.reqUrl = '/business/seaAffairs/shipMaterials/pageDraw'
    this.searchDicts = [
      {
        dicType: 'inventory_chart_type',
        label: '类型',
        key: 'type',
      },
    ]
    this.headers = [
      { text: '编号', value: 'no' },
      { text: '图号', value: 'drawNo' },
      { text: '图名', value: 'drawName' },
      { text: '类型', value: 'type' },
      { text: '出版日期', value: 'editionDate' },
      { text: '是否新版', value: 'newEditionFlag' },
      { text: '新版日期', value: 'newEditionDate' },
      { text: '备注', value: 'remark' },
    ]
    this.formHeaders = [
      { text: '图号', value: 'drawNo' },
      { text: '图名', value: 'drawName' },
      { text: '类型', value: 'type' },
      { text: '出版日期', value: 'editionDate' },
      { text: '备注', value: 'remark' },
    ]
  },

  data() {
    return {
      selected: [],
      searchObj: { shipId: this.$local.data.get('userInfo').shipId },
      items: [
        { text: '是', value: false },
        { text: '否', value: true },
      ],
      formShow: false,
      isExcel: true,
      isEdit: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        yyyymm: (v) =>
          /^\d{4}-\d{2}$/.test(v) || '请输入正确的年月格式如2022-02',
      },
      form: {},
      excel: {
        shipId: this.$local.data.get('userInfo').shipId,
        type: '',
      },
      checking: false,
    }
  },

  computed: {
    haveSelect() {
      return this.selected && this.selected.length > 0
    },
  },
  methods: {
    async apply() {
      const { selected } = this
      if (!this.haveSelect) {
        this.$dialog.message.error('请至少选择一条记录')
        return
      }
      if (
        selected.filter(
          (item) => item.newEditionFlag || item.newEditionFlag === null,
        ).length > 0
      ) {
        this.$dialog.message.error('只能选择已过期的记录')
        return
      }
      const applyList = selected.map((item) => {
        return {
          recordId: item.id,
          shipId: this.$local.data.get('userInfo').shipId,
          type: 1,
        }
      })
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/shipMaterials/writeRecord',
        applyList,
      )
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
      } else {
        this.$dialog.message.success('添加成功,请注意检查更新')
        this.selected = []
      }
    },
    async onImportSuccess() {
      await this.$refs.table.loadTableData()
      this.$dialog.message.success('导入成功，请注意检查更新')
      this.closeForm()
    },
    async check() {
      this.checking = true
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/shipMaterials/checkEditionForChart',
        {
          shipId: this.$local.data.get('userInfo').shipId,
        },
      )
      if (!errorRaw) this.$dialog.message.success('检查成功')
      await this.$refs.table.loadTableData()
      this.checking = false
    },

    async delChart() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/shipMaterials/deleteInventoryForChart/${this.selected[0].id}`,
        {},
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },

    async editChart() {
      this.form = { ...this.selected[0] }
      this.isExcel = false
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/shipMaterials/updateInventoryForChart'
        : '/business/seaAffairs/shipMaterials/insertInventoryForChart'
      const { errorRaw } = await this.postAsync(reqUrl, {
        ...this.form,
        shipId: this.$local.data.get('userInfo').shipId,
      })
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.form = {}
      this.isEdit = false
      this.selected = []
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    closeForm() {
      !this.isExcel && this.$refs.form.reset()
      this.form = {}
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style scoped></style>
