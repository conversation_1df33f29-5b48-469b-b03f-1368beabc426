<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        船保部更新包上传-{{ isEdit ? '编辑' : '新建' }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.name"
                  label="更新包名称"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.introduction"
                  label="更新包说明"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.handler"
                  label="经办人"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="formData.attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'updatebag-upload',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    updateBag: {
      type: Object,
      default: () => ({
        name: '',
        introduction: '',
        handler: '',
        type: '1',
        attachmentRecords: [],
      }),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {
        attachmentRecords: [],
      },
      attachmentIds: [],
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.isEdit
        ? {
            ...this.updateBag,
            attachmentRecords: this.updateBag?.attachmentRecords
              ? [this.updateBag.attachmentRecords]
              : [],
          }
        : this.updateBag
    },
  },
  computed: {
    isEdit() {
      return this.updateBag?.id
    },
    attachmentId() {
      return this.attachmentIds.length > 0 ? this.attachmentIds[0] : null
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.attachmentIds = attachmentIds
    },

    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.attachmentIds.length > 1) {
        this.$dialog.message.error('更新包仅能上传一个附件，请删除多余后再上传')
        return
      }
      if (
        this.attachmentIds.length === 0 &&
        !(await this.$dialog.msgbox.confirm('您当前未上传附件，确定提交吗'))
      )
        return
      const url = this.isEdit
        ? '/business/seaAffairs/sidPackage/update'
        : '/business/seaAffairs/sidPackage/addSidPackage'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        attachmentId: this.attachmentId,
        type: 1,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
