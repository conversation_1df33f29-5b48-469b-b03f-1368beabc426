<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        船保部基础包
        <v-spacer></v-spacer>
        <v-btn v-permission="['船保部基础包:编辑']" color="primary" fab small>
          <v-icon @click="close" v-if="isEditing">mdi-close</v-icon>
          <v-icon @click="edit" v-else>mdi-pencil</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="baseBag.name"
                  label="基础包名称"
                  :disabled="!isEditing"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="baseBag.introduction"
                  label="基础包说明"
                  :disabled="!isEditing"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="baseBag.handler"
                  label="经办人"
                  :disabled="!isEditing"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="baseBag.attachmentRecords"
                  @change="changebaseBageattach"
                  :disabled="!isEditing"
                  ship-code="0"
                ></v-attach-list>
              </v-col>
              <v-col v-show="isEditing" cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['船保部更新包:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      class="mt-2"
      ref="table"
      table-name="船保部更新包"
      v-model="selected"
      :headers="headers"
      req-url="/business/seaAffairs/sidPackage/list"
      :fix-header="false"
      :search-remain="searchRemain"
      :push-params="{ name: 'updatebag-upload' }"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="createUpdatebag"
          v-permission="['船保部更新包:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editUpdatebag"
          v-permission="['船保部更新包:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delUpdatebag"
          v-permission="['船保部更新包:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.url`]="{ item }">
        <v-chip
          @click.stop="linkDownload(item.attachmentRecords.url)"
          small
          color="primary"
          v-if="item.attachmentRecords && item.attachmentRecords.url"
        >
          <v-icon>mdi-file-document-box-outline</v-icon>
          <span>{{ item.attachmentRecords.name }}</span>
        </v-chip>
        <v-chip small v-else>暂无附件</v-chip>
      </template>
    </v-table-searchable>
    <updatebag-upload
      v-model="updateDialog"
      :update-bag="updateBag"
      @success="updateSuccess"
    ></updatebag-upload>
  </v-container>
</template>
<script>
import updatebagUpload from '@/views/maritime-affairs/bag/components/updatebag-upload'
export default {
  name: 'bag-view',
  components: {
    updatebagUpload,
  },
  data() {
    return {
      selected: undefined,
      formData: {
        code: '',
        content: '',
        name: '',
        introduction: '',
        handler: '',
        url: '',
        attachmentId: [],
      },
      baseBag: {},
      editbaseBag: {},
      isEditing: false,
      updateDialog: false,
      updateBag: {},
      attachmentIds: [],
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
      searchRemain: {
        type: 1,
      },
    }
  },
  created() {
    this.headers = [
      { text: '更新包名称', value: 'name' },
      { text: '更新包说明', value: 'introduction' },
      { text: '经办人', value: 'handler' },
      { text: '更新包附件', value: 'url', sortable: false },
    ]
    this.searchDicts = []
  },

  methods: {
    linkDownload(url) {
      window.open(url, '_blank') // 新窗口打开外链接
    },

    editbag() {
      this.$router.push(
        `/business/seaAffairs/sidPackage/record/${this.selected.id}`,
      )
    },

    createUpdatebag() {
      this.updateBag = {
        name: '',
        introduction: '',
        handler: this.$local.data.get('userInfo').nickName,
        url: '',
        type: '1',
        attachmentRecords: [],
      }
      this.updateDialog = true
    },
    editUpdatebag() {
      this.updateBag = this.selected
      this.updateDialog = true
    },
    async updateSuccess() {
      await this.$refs.table.loadTableData()
    },

    close() {
      this.isEditing = false
      this.baseBag = this.editbaseBag
    },

    edit() {
      this.isEditing = true
      this.editbaseBag = this.baseBag
      this.baseBag = {}
      this.$refs.form.resetValidation()
      this.baseBag.handler = this.$local.data.get('userInfo').nickName
    },

    changebaseBageattach(attachmentIds) {
      this.attachmentIds = attachmentIds
    },
    async delUpdatebag() {
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/sidPackage/deleteBatch',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.attachmentIds.length > 1) {
        this.$dialog.message.error('基础包仅能上传一个附件，请删除多余后再上传')
        return
      }
      if (
        this.attachmentIds.length === 0 &&
        !(await this.$dialog.msgbox.confirm('您当前未上传附件，确定提交吗'))
      )
        return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/sidPackage/update',
        {
          ...this.baseBag,
          type: '0',
          attachmentId:
            this.attachmentIds.length > 0 ? this.attachmentIds[0] : null,
          id: this.editbaseBag.id,
        },
      )

      if (!errorRaw) {
        this.isEditing = false
        await this.loadBasicBag()
      }
    },

    async loadBasicBag() {
      const { data } = await this.postAsync(
        '/business/seaAffairs/sidPackage/baseRecord',
        {},
      )

      this.baseBag = {
        ...data,
        attachmentRecords: data.attachmentRecords?.id
          ? [data.attachmentRecords]
          : [],
      }
    },
  },

  mounted() {
    this.loadBasicBag()
  },
}
</script>

<style></style>
