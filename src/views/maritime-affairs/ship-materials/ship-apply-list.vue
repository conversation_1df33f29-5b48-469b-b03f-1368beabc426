<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      use-ship
      use-ship-id
    >
      <template #btns></template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === '0'" small>未提交</v-chip>
        <v-chip v-else-if="item.status === '1'" color="primary" small>
          待审批
        </v-chip>
        <v-chip v-else-if="item.status === '2'" color="success" small>
          已查阅
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'ship-apply-list',
  created() {
    this.tableName = '本轮资料申请单'
    this.reqUrl = '/business/seaAffairs/shipMaterials/listApply'
    this.searchDicts = [
      {
        dicType: 'materials_update_apply_status',
        label: '申请单状态',
        key: 'status',
      },
    ]
    this.headers = [
      { text: '申请单id', value: 'id' },
      { text: '船舶', value: 'shipInfo' },
      { text: '船长姓名', value: 'captain' },
      { text: '经办人姓名', value: 'handler' },
      { text: '备注', value: 'remark' },
      { text: '申请时间', value: 'applyDate' },
      { text: '申请单状态', value: 'status' },
    ]
    this.fuzzyLabel = '船长'
    this.pushParams = { name: 'ship-apply-detail' }
  },

  data() {
    return {
      selected: false,
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
