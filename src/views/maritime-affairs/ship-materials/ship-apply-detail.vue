<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="primary white--text py-1 my-0">
        本轮资料清单申请-{{ $route.params.id }}
        <v-spacer></v-spacer>
        <v-btn
          @click="submit"
          depressed
          small
          v-if="detailData.status === '0'"
          v-permission="['本轮资料申请单:保存并提交']"
        >
          保存并提交
        </v-btn>
        <v-btn @click="save" depressed small class="mx-1">保存</v-btn>
      </v-card-title>
      <v-card-title class="subtitle-1 mt-1">申请单信息</v-card-title>
      <v-card-text class="pt-0">
        <v-form>
          <v-row>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                dense
                label="船舶"
                v-model="detailData.shipId"
                readonly
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                dense
                label="船长姓名"
                v-model="detailData.captain"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                dense
                label="经办人"
                v-model="detailData.handler"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                dense
                label="申请时间"
                v-model="detailData.applyDate"
                readonly
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-text-field
                dense
                label="备注"
                v-model="detailData.remark"
              ></v-text-field>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-title class="subtitle-1 mt-1">
        申请单明细
        <v-spacer></v-spacer>
        <v-btn
          v-if="detailData.status === '2' && false"
          :disabled="selects.length === 0"
          outlined
          :hidden="true"
          tile
          color="success"
          class="mx-1"
          @click="reciveRecord('已更新')"
          v-permission="['申请单明细:更新']"
        >
          <v-icon left>mdi-check</v-icon>
          更新
        </v-btn>
        <v-btn
          v-if="detailData.status === '1'"
          :disabled="selects.length === 0"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="approveRecord('同意')"
          v-permission="['申请单明细:同意']"
        >
          <v-icon left>mdi-check</v-icon>
          同意
        </v-btn>
        <v-btn
          v-if="detailData.status === '1'"
          :disabled="selects.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="approveRecord('拒绝')"
          v-permission="['申请单明细:驳回']"
        >
          <v-icon left>mdi-close</v-icon>
          拒绝
        </v-btn>
        <v-btn
          v-if="detailData.status === '0'"
          :disabled="selects.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delRecord"
          v-permission="['申请单明细:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-data-table
          hide-default-footer
          disable-pagination
          :items="detailData.recordListOutputDTOList"
          :headers="headers"
          show-select
          dense
          v-model="selects"
        >
          <template v-slot:[`item.type`]="{ item }">
            <div v-if="item.type === '0'">书</div>
            <div
              v-else-if="
                item.type === '10' ||
                item.type === '11' ||
                item.type === '12' ||
                item.type === '13'
              "
            >
              书（新申请）
            </div>
            <div
              v-else-if="
                item.type === '20' ||
                item.type === '21' ||
                item.type === '22' ||
                item.type === '23'
              "
            >
              图（新申请）
            </div>
            <div v-else>图</div>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
export default {
  name: 'ship-apply-detail',
  created() {
    this.headers = [
      { text: '编号（图号/书号）', value: 'code' },
      { text: '名称（图名/书名）', value: 'name' },
      { text: '出版日期或版本', value: 'edition' },
      { text: '新版版本', value: 'newEdition' },
      { text: '类型', value: 'type' },
      { text: '审批状态', value: 'approveStatus' },
      { text: '更新状态', value: 'updateStatus' },
    ]
  },
  data() {
    return {
      detailData: {
        applyDate: '',
        captain: '',
        handler: '',
        recordListOutputDTOList: [],
        remark: '',
        shipId: '',
        status: '',
      },
      selects: [],
    }
  },

  computed: {
    canEdit: function () {
      return this.detailData.status === '0'
    },
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/shipMaterials/listUpdateApply/${this.$route.params.id}`,
      )
      this.updateTaskPromptMassage(this.$route.params.id)
      this.detailData = data
      if (this.detailData.status === '0')
        this.detailData.handler = this.$local.data.get('userInfo').nickName
      this.$store.commit('updateViewTagsTooltip', {
        tooltip: this.$route.params.id,
        nowFullPath: this.$route.path,
      })
    },

    async reloadRecord() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/shipMaterials/listUpdateApply/${this.$route.params.id}`,
      )
      this.selects = []
      this.detailData.recordListOutputDTOList = data.recordListOutputDTOList
      this.detailData.status = data.status
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },

    async save() {
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/shipMaterials/updateApply',
        { ...this.detailData, id: this.$route.params.id },
      )
      if (!errorRaw) {
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.push({
          path: '/maritime-affairs/ship-materials/apply-list',
          query: {
            reload: true,
          },
        })
      }
    },

    async submit() {
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/shipMaterials/updateApply',
        { ...this.detailData, id: this.$route.params.id, status: '1' },
      )
      if (!errorRaw) {
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.push({
          path: '/maritime-affairs/ship-materials/apply-list',
          query: {
            reload: true,
          },
        })
      }
    },

    async delRecord() {
      if (!(await this.$dialog.msgbox.confirm('确定删除选择记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/shipMaterials/deleteBatchRecord',
        this.selects.map((item) => item.id),
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.reloadRecord()
      }
    },

    async approveRecord(flag) {
      if (this.selects.some((item) => item.approveStatus !== '未查阅')) {
        if (
          !(await this.$dialog.msgbox.confirm(
            '您选择的记录中有已处理的，确定处理将会覆盖状态',
          ))
        )
          return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/shipMaterials/updateStatusforUpdateInventory',
        this.selects.map((item) => {
          return {
            id: item.id,
            approveStatus: flag,
          }
        }),
      )
      if (!errorRaw) {
        this.$dialog.message.success('处理完成')
        await this.reloadRecord()
      }
    },

    async reciveRecord(flag) {
      if (this.selects.some((item) => item.updateStatus !== '未更新')) {
        if (
          !(await this.$dialog.msgbox.confirm(
            '您选择的记录中有已处理的，确定处理将会覆盖状态',
          ))
        )
          return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/shipMaterials/updateStatusforUpdateInventory',
        this.selects.map((item) => {
          return {
            id: item.id,
            updateStatus: flag,
          }
        }),
      )
      if (!errorRaw) {
        this.$dialog.message.success('更新成功')
        await this.reloadRecord()
      }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
