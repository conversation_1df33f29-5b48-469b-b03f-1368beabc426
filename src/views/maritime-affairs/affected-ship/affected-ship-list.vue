<template>
  <v-container fluid>
    <!-- <v-table-searchable
      ref="table"
      table-name="受影响船舶"
      v-model="selected"
      :headers="headers"
      req-url="/business/seaAffairs/badWeatherAffectedShipRecord/recordPage"
      :fix-header="false"
      :push-params="pushParams"
      :search-dicts="searchDicts"
      :search-remain="searchObj"
      use-ship
      use-ship-id
    > -->
    <v-table-searchable
      ref="table"
      table-name="受影响船舶"
      v-model="selected"
      :headers="headers"
      req-url="/business/seaAffairs/badWeatherAffectedShipRecord/recordPage"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-ship-id
    >
      <template #searchflieds>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.weatherKey"
            label="恶劣天气编号"
            outlined
            dense
            append-icon="mdi-magnify"
          ></v-text-field>
        </v-col> -->
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.weatherKey"
            label="年度"
            outlined
            dense
            append-icon="mdi-magnify"
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/maritime-affairs/bad-weather/affected-ship/new"
          v-permission="['受影响船舶:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="createSailRecord"
          v-permission="['受影响船舶:新增船舶动态']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增船舶动态
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAffectship"
          v-permission="['受影响船舶:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.zhName`]="{ item }">
        {{ item.shipInfoDO && item.shipInfoDO.chShipName }}
      </template>
    </v-table-searchable>
    <sail-detail
      v-model="sailDialog"
      :sail-record="sailDetail"
      @success="success"
    ></sail-detail>
  </v-container>
</template>
<script>
import sailDetail from '@/views/maritime-affairs/affected-ship/components/sail-detail'
export default {
  name: 'affected-ship-list',
  components: { sailDetail },
  created() {
    this.searchDicts = [
      {
        //dicType是表明字典里的类型
        dicType: 'bad_weather_type',
        label: '恶劣天气类型',
        //key是接口给的参数名
        key: 'weatherType',
      },
    ]
    this.headers = [
      { text: '船舶名称', value: 'zhName' },
      // { text: '恶劣天气编号', value: 'weatherKey' },
      { text: '年度', value: 'year' },
      { text: '受恶劣天气影响次数', value: 'affectNum' },
      { text: '停航时间（小时）', value: 'suspensionTotalTime' },
      { text: '绕航里程（海里）', value: 'deviationTotal' },
      // { text: '恶劣天气类型', value: 'weatherType', sortable: true },
    ]
    this.fuzzyLabel = '恶劣天气编号'
    this.pushParams = {
      name: 'affected-ship-detail',
    }
  },
  data: () => ({
    selected: undefined,
    searchObj: {},
    sailDetail: {},
    sailDialog: false,
  }),

  methods: {
    editAffectship() {
      this.updateTaskPromptMassage(this.selected.id)
      this.$router.push(`/maritime-affairs/affected-ship/${this.selected.id}`)
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    async delAffectship() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/badWeatherAffectedShipRecord/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
    createSailRecord() {
      this.sailDetail = {
        weatherShipId: 'newbyship',
      }
      this.sailDialog = true
    },
    async success() {
      await this.$refs.table.loadTableData()
      this.selected = undefined
    },
  },

  mounted() {},
}
</script>

<style></style>
