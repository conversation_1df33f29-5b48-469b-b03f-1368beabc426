<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['受影响船舶:编辑']"
      :title="`受影响船舶详情-${shipInfoDO.chShipName}`"
      :tooltip="`${shipInfoDO.chShipName}-${weatherKey}`"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      :can-save="false"
    >
      <template #基本信息>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="3">
              <v-ship-select
                :clearable="false"
                readonly
                v-model="shipId"
                use-id
              ></v-ship-select>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                dense
                outlined
                readonly
                v-model="detail.year"
                label="年度"
              ></v-text-field>
            </v-col>
            <!-- <v-col cols="12" md="3">
              <v-text-field
                dense
                outlined
                readonly
                v-model="detail.weatherKey"
                label="恶劣天气编号"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-select
                v-model="detail.weatherType"
                label="恶劣天气类型"
                required
                dense
                :items="badWeatherTypes"
                outlined
                readonly
              ></v-select>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                dense
                outlined
                readonly
                v-model="detail.zhName"
                label="恶劣天气名称"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                dense
                outlined
                readonly
                v-model="detail.influenceArea"
                label="影响区域"
              ></v-text-field>
            </v-col> -->
          </v-row>
        </v-card-text>
      </template>
      <template #在港船舶动态按钮>
        <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click.stop="createPortRecord"
          v-permission="['在港船舶动态:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedPortRecord"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editPortRecord"
          v-permission="['在港船舶动态:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedPortRecord"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delRecord(0)"
          v-permission="['在港船舶动态:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!selectedPortRecord"
          outlined
          small
          tile
          color="primary"
          class="mx-1"
          @click.stop="openGmDialog(0)"
          v-permission="['在港船舶动态:填写指导意见']"
        >
          <v-icon left>mdi-pen</v-icon>
          填写指导意见
        </v-btn>
      </template>
      <template #在港船舶动态>
        <v-card-text>
          <v-table-list-new
            :headers="portHeaders"
            :items="portList"
            v-model="selectedPortRecord"
          ></v-table-list-new>
        </v-card-text>
      </template>
      <template #航行船舶动态按钮>
        <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click.stop="createSailRecord"
          v-permission="['航行船舶动态:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedSailRecord"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editSailRecord"
          v-permission="['航行船舶动态:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedSailRecord"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delRecord(1)"
          v-permission="['航行船舶动态:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!selectedSailRecord"
          outlined
          small
          tile
          color="primary"
          class="mx-1"
          @click.stop="openGmDialog(1)"
          v-permission="['航行船舶动态:填写指导意见']"
        >
          <v-icon left>mdi-pen</v-icon>
          填写指导意见
        </v-btn>
      </template>
      <template #航行船舶动态>
        <v-card-text>
          <v-table-list
            :headers="sailHeaders"
            :items="sailList"
            :search-dicts="searchDicts"
            v-model="selectedSailRecord"
            @dbclick="editSailRecord"
          >
            <!-- <template v-slot:[`item.weatherType`]="{ item }">
              <div
                v-for="i in badWeatherTypes"
                :key="i.value"
                v-if="i.value == item.weatherType"
              >
                {{ i.text }}
              </div>
              <v-dict-select
                dict-type="bad_weather_type"
                v-model="item.weatherType"
                readoly
              ></v-dict-select>
            </template> -->
          </v-table-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <port-detail
      v-model="portDialog"
      :port-record="portDetail"
      @success="success"
    ></port-detail>
    <sail-detail
      v-model="sailDialog"
      :sail-record="sailDetail"
      @success="success"
    ></sail-detail>
    <v-dialog persistent hide-overlay v-model="gmDialog" width="1000">
      <v-card>
        <v-card-text class="pt-6">
          <v-textarea
            rows="23"
            outlined
            label="岸端指导与措施"
            v-model="guidanceAndMeasures"
          ></v-textarea>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click.stop="gmDialog = false">关闭</v-btn>
          <v-btn color="blue darken-1" text @click.stop="submitGm">提交</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import portDetail from '@/views/maritime-affairs/affected-ship/components/port-detail'
import sailDetail from '@/views/maritime-affairs/affected-ship/components/sail-detail'
import badWeatherTypes from '../bad-weather/private/badWeatherTypes'
export default {
  name: 'affected-ship-detail',
  components: {
    portDetail,
    sailDetail,
  },
  created() {
    // this.subtitles = ['基本信息', '在港船舶动态', '航行船舶动态']
    this.subtitles = ['基本信息', '航行船舶动态']
    this.backRouteName = 'affected-ship-list'
    this.portHeaders = [
      { text: '港口', value: 'shipLocation' },
      { text: '录入时间', value: 'entryTime' },
      { text: '船舶动态', value: 'shipDynamicsType' },
      { text: '驶往港口', value: 'destinationPort' },
      { text: '指导意见', value: 'guidance' },
      { text: '船方采取措施', value: 'measures' },
    ]
    this.sailHeaders = [
      { text: '录入时间', value: 'entryTime' },
      { text: '恶劣天气编号', value: 'weatherKey' },
      { text: '恶劣天气类型', value: 'weatherType' },
      { text: '恶劣天气名称', value: 'weatherName' },
      { text: '船舶位置', value: 'shipLocation' },
      { text: '航向', value: 'shipCourse' },
      { text: '航速', value: 'shipSpeed' },
      { text: '出发港', value: 'departurePort' },
      { text: '目的港', value: 'destinationPort' },
      { text: '抛锚/漂航/影响开始时间', value: 'anchorDownTime' },
      { text: '起锚/漂航/影响结束时间', value: 'anchorUpTime' },
      { text: '停航/漂航/影响时间（小时）', value: 'suspensionTime' },
      { text: '绕航里程（海里）', value: 'deviation' },
      // { text: '漂航时间', value: 'driftTime' },
      // { text: '停租时间', value: 'downtime' },
      // { text: '停租原因', value: 'rentSuspensionReason' },
      { text: '指导意见', value: 'guidance' },
      { text: '船方采取措施', value: 'measures' },
    ]
    this.badWeatherTypes = badWeatherTypes
    this.searchDicts = [
      {
        dicType: 'bad_weather_type',
        label: '恶劣天气类型',
        key: 'weatherType',
      },
    ]
  },
  data() {
    return {
      shipId: '',
      shipInfoDO: {},
      weatherKey: '',
      detail: {},
      selectedPortRecord: false,
      selectedSailRecord: false,
      sailList: [],
      portList: [],
      portDetail: {},
      sailDetail: {},
      portDialog: false,
      sailDialog: false,
      guidanceAndMeasures: '',
      gmDialog: false,
      gmId: '',
    }
  },
  computed: {
    // 获取当前路由的参数
    isEdit() {
      return this.$route.params.id
    },
  },
  methods: {
    async getAffectedship() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/seaAffairs/badWeatherAffectedShipRecord/detailPage/${this.$route.params.id}`,
      )
      this.updateTaskPromptMassage(this.$route.params.id)
      this.shipId = data.shipInfoDO.id
      this.shipInfoDO = data.shipInfoDO
      this.detail = data
      this.sailList =
        data.dynamicDetailList?.filter((item) => item.dynamicType === '1') || []
      this.portList =
        data.dynamicDetailList?.filter((item) => item.dynamicType === '0') || []
      // this.$store.commit('updateViewTagsTooltip', {
      //   tooltip: data?.code,
      //   nowFullPath: this.$route.path,
      // })
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    getLabel(value) {
      return this.echartType.find((item) => item.dictValue === value)?.dictLabel
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/badWeatherAffectedShipRecord/updatedShipDynamicRecordDetail'
        : '/business/seaAffairs/badWeatherAffectedShipRecord/submitDynamicRecords'
      const { errorRaw } = await this.postAsync(url, this.formData)
      if (!errorRaw) {
        this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        goBack()
      }
    },
    createPortRecord() {
      this.portDetail = {
        shipLocation: '',
        shipDynamicsType: '',
        destinationPort: '',
        weatherShipId: this.$route.params.id,
      }
      this.portDialog = true
    },
    editPortRecord() {
      this.portDetail = {
        ...this.selectedPortRecord,
        weatherShipId: this.$route.params.id,
      }
      this.portDialog = true
    },

    createSailRecord() {
      this.sailDetail = {
        weatherShipId: this.$route.params.id,
      }
      this.sailDialog = true
    },
    editSailRecord() {
      this.sailDetail = {
        ...this.selectedSailRecord,
        weatherShipId: this.$route.params.id,
      }
      this.sailDialog = true
    },
    async delRecord(recordType) {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/badWeatherAffectedShipRecord/deleteDetail',
        {
          id:
            recordType === 0
              ? this.selectedPortRecord.id
              : this.selectedSailRecord.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.success()
    },
    async success() {
      await this.getAffectedship()
      this.selectedPortRecord = false
      this.selectedSailRecord = false
    },
    openGmDialog(gmType) {
      this.gmType = gmType
      this.gmDialog = true
    },
    async submitGm() {
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/badWeatherAffectedShipRecord/updateProposalAndMeasure',
        {
          id:
            this.gmType === 0
              ? this.selectedPortRecord.id
              : this.selectedSailRecord.id,
          guidance: this.guidanceAndMeasures,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('提交成功')
        this.gmDialog = false
        this.guidanceAndMeasures = ''
        await this.success()
      }
    },
  },

  mounted() {
    this.getAffectedship()
  },
}
</script>

<style></style>
