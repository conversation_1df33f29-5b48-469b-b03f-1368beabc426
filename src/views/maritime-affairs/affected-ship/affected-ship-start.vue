<template>
  <v-container fluid>
    <v-card>
      <v-card-title>受影响船舶-新建</v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <!-- <v-col cols="12" md="6">
                <v-dialog-select
                  label="恶劣天气编号"
                  item-text="weatherKey"
                  item-value="weatherKey"
                  v-model="formData.weatherKey"
                  :search-dicts="searchDicts"
                  :search-date="searchDate"
                  :headers="headers"
                  req-url="/business/seaAffairs/badWeather/page"
                  :rules="[rules.required]"
                ></v-dialog-select>
              </v-col> -->
              <v-col cols="12" md="6">
                <v-ship-select
                  v-model="formData.shipId"
                  required
                  use-id
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  dense
                  outlined
                  v-model="formData.year"
                  label="年度"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['受影响船舶新建:新增']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  新增
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
export default {
  name: 'affected-ship-start',

  data() {
    return {
      dateMenu: false,
      formData: {
        weatherKey: '',
        shipId: this.$local.data.get('userInfo').isShipSyS
          ? this.$local.data.get('userInfo').shipId
          : '',
      },
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
    }
  },
  computed: {
    // 获取当前路由的参数
    isEdit() {
      return this.$route.params.id
    },
    isShip() {
      //console.log(this.$local.data.get('userInfo').isShipSyS)//船端时返回true
      return this.$local.data.get('userInfo').isShipSyS
    },
  },
  created() {
    this.searchDicts = [
      {
        dicType: 'bad_weather_type',
        label: '恶劣天气类型',
        key: 'weatherType',
      },
    ]
    this.headers = [
      { text: '恶劣天气编号', value: 'weatherKey' },
      { text: '恶劣天气类型', value: 'weatherType' },
      { text: '中文名称', value: 'zhName' },
      { text: '英文名称', value: 'enName' },
      { text: '起编日期', value: 'startDate' },
    ]
    //以时间做查询
    this.searchDate = {
      label: '起编日期',
      value: 'startDate',
    }
  },
  methods: {
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const { data } = await this.postAsync(
        '/business/seaAffairs/badWeatherAffectedShipRecord/initiate',
        this.formData,
      )
      if (data) {
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.push({
          name: 'affected-ship-detail',
          params: { id: data },
        })
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
