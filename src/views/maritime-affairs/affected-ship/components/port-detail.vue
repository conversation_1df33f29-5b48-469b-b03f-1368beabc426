<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        在港船舶记录-{{ isEdit ? '编辑' : '新建' }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.shipLocation"
                  label="港口"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.shipDynamicsType"
                  label="船舶动态"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.destinationPort"
                  label="驶往港口"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  v-model="formData.measures"
                  label="船端采取措施"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'port-detail-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    portRecord: {
      type: Object,
      default: () => ({
        shipLocation: '',
        shipDynamicsType: '',
        destinationPort: '',
      }),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.portRecord
    },
  },
  computed: {
    isEdit() {
      return this.portRecord?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/badWeatherAffectedShipRecord/updatedShipDynamicRecordDetail'
        : '/business/seaAffairs/badWeatherAffectedShipRecord/submitDynamicRecords'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        entryTime: new Date(Date.now()).toISOString().substr(0, 10),
        dynamicType: '0',
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped></style>
