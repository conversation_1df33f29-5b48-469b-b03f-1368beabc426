<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        航行船舶动态-{{ isEdit ? '编辑' : '新建' }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-dialog-select
                  label="恶劣天气编号"
                  item-text="weatherKey"
                  item-value="weatherKey"
                  req-url="/business/seaAffairs/badWeather/page"
                  v-model="formData.weatherKey"
                  :init-selected="processObj"
                  :search-dicts="searchDicts"
                  :search-date="searchDate"
                  :headers="headers"
                  :rules="[rules.required]"
                ></v-dialog-select>
              </v-col>
              <!-- <v-text-field
                  v-model="formData.weatherType"
                  label="恶劣天气类型"
                  readonly
                ></v-text-field> -->
              <!-- <v-select
                  dictType="bad_weather_type"
                  v-model="formData.weatherType"
                  label="恶劣天气类型"
                  dense
                ></v-select> -->
              <!-- <v-col cols="12" md="3">
                <v-dict-select
                  dict-type="bad_weather_type"
                  v-model="formData.weatherType"
                  label="恶劣天气类型"
                ></v-dict-select>
              </v-col> -->
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.weatherName"
                  label="恶劣天气名称"
                  readonly
                ></v-text-field>
              </v-col> -->
              <v-col v-for="h in sailHeaders" :key="h.value" cols="12" md="3">
                <v-date-time-picker
                  v-if="h.type === 'time'"
                  v-model="formData[h.value]"
                  :label="h.text"
                ></v-date-time-picker>
                <v-text-field
                  v-else-if="h.type === 'int'"
                  type="number"
                  v-model="formData[h.value]"
                  :rules="[rules.required]"
                  :label="h.text"
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="formData[h.value]"
                  :label="h.text"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  v-model="formData.measures"
                  label="船端采取措施"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
// import badWeatherTypes from './private/badWeatherTypes'
export default {
  name: 'sail-detail-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    sailRecord: Object,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
      processObj: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.formData = this.sailRecord
      this.processObj = {
        weatherKey: this.formData.weatherKey,
        // name: this.formData.weatherName,
      }
      this.$refs?.form?.resetValidation()
    },
  },
  computed: {
    isEdit() {
      return this.sailRecord?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/badWeatherAffectedShipRecord/updatedShipDynamicRecordDetail'
        : '/business/seaAffairs/badWeatherAffectedShipRecord/submitDynamicRecords'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        entryTime: new Date(Date.now()).toISOString().substr(0, 10),
        dynamicType: '1',
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
  created() {
    this.sailHeaders = [
      // { text: '恶劣天气类型', value: 'weatherKey' },
      // { text: '恶劣天气名称', value: 'weatherKey' },
      { text: '船舶位置', value: 'shipLocation' },
      { text: '航向', value: 'shipCourse' },
      { text: '航速', value: 'shipSpeed', type: 'int' },
      { text: '出发港', value: 'departurePort' },
      { text: '目的港', value: 'destinationPort' },
      // { text: '漂航时间', value: 'driftTime', type: 'int' },
      { text: '抛锚/漂航/影响开始时间', value: 'anchorDownTime', type: 'time' },
      { text: '起锚/漂航/影响结束时间', value: 'anchorUpTime', type: 'time' },
      { text: '绕航里程（海里）', value: 'deviation', type: 'int' },
      // { text: '停租时间', value: 'downtime', type: 'int' },
      // { text: '停租原因', value: 'rentSuspensionReason' },
    ]
    // this.badWeatherTypes = badWeatherTypes
    this.searchDicts = [
      {
        dicType: 'bad_weather_type',
        label: '恶劣天气类型',
        key: 'weatherType',
      },
    ]
    this.headers = [
      { text: '恶劣天气编号', value: 'weatherKey' },
      { text: '恶劣天气类型', value: 'weatherType' },
      { text: '中文名称', value: 'zhName' },
      { text: '英文名称', value: 'enName' },
      { text: '起编日期', value: 'startDate' },
    ]
    //以时间做查询
    this.searchDate = {
      label: '起编日期',
      value: 'startDate',
    }
  },
}
</script>

<style></style>
