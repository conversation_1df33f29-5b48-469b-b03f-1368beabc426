<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}文件修改&推送
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="ship.company"
                  label="公司名"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="ship.dept"
                  label="部门"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="2" v-if="!this.isEdit">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="ship.shipAll"
                  label="全部船舶"
                  color="success"
                ></v-switch>
              </v-col>
              <v-col cols="12" :md="[this.isEdit ? '2' : '12']">
                <v-ship-select
                  v-model="ship.shipCode"
                  :rules="!this.shipAll ? [rules.required] : []"
                  :required="!this.shipAll"
                  :multiple="!this.isEdit"
                ></v-ship-select>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="ship.year"
                  label="年度"
                  :rules="[rules.year]"
                  dense
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  useToday
                  v-model="ship.uploadDate"
                  label="实际上传日期"
                  dense
                  required
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="ship.handler"
                  label="经办人"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="ship.remark"
                  label="备注"
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12">
                <v-attach-list
                  :attachments="ship.attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="ship.shipCode"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['公司文件推送:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      use-ship
      :search-remain="searchObj"
      @dbclick="editShip"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.history"
            label="历史数据"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createTable"
          v-permission="['公司文件推送:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delShip"
          v-permission="['公司文件推送:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.quarter`]="{ item }">
        <div v-if="item.halfYearType === '0'">第一季度</div>
        <div v-if="item.halfYearType === '1'">第二季度</div>
        <div v-if="item.halfYearType === '2'">第三季度</div>
        <div v-if="item.halfYearType === '3'">第四季度</div>
      </template>
      <template v-slot:[`item.shipConfirm`]="{ item }">
        <v-btn
          v-if="item.shipConfirm !== null"
          x-small
          color="primary"
          elevation="0"
          @click="confirmInfom(item)"
          v-permission="['公司文件推送:编辑']"
        >
          {{ item.shipConfirm == '1' ? '已确认' : '尚未确认' }}
        </v-btn>
      </template>
    </v-table-searchable>
    <v-dialog v-model="dialog" width="500" hide-overlay>
      <v-card>
        <v-card-title>船端反馈填写</v-card-title>
        <v-card-text>
          <v-form>
            <v-textarea
              label="反馈信息"
              v-model="confirmDTO.shipFeedback"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-btn
            @click="upData"
            outlined
            tile
            color="success"
            class="mx-1"
            block
            v-permission="['公司文件推送:提交']"
          >
            提交
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
export default {
  name: 'ship-companydocuments',
  attachmentRecords: [],
  created() {
    this.tableName = '文件修改&推送'
    this.reqUrl = '/business/seaAffairs/emanage/companyfile/page'
    this.headers = [
      { text: '公司名', value: 'company' },
      { text: '部门', value: 'dept' },
      { text: '船名', value: 'cnShipName' },
      { text: '年度', value: 'year' },
      { text: '实际上传日期', value: 'uploadDate' },
      { text: '经办人', value: 'handler' },
      { text: '船端确认标记', value: 'shipConfirm' },
      { text: '船端反馈内容', value: 'shipFeedback' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords', sortable: false },
    ]
    this.items = [
      { text: '第一季度', value: '0' },
      { text: '第二季度', value: '1' },
      { text: '第三季度', value: '2' },
      { text: '第四季度', value: '3' },
    ]
  },

  data() {
    return {
      selected: false,
      shipAll: false,
      ship: {
        company: '',
        dept: '',
        shipCode: '',
        year: '',
        quarter: '',
        uploadDate: '',
        handler: this.$local.data.get('userInfo').nickName,
        remark: '',
        attachmentIds: [],
        attachmentRecords: [],
      },
      searchObj: {
        history: false,
      },

      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        time: (v) =>
          /^\d{4}-\d{2}-\d{2}$/.test(v) || '请输入正确的年月日格式如2022-02-02',
        year: (v) => /^\d{4}$/.test(v) || '请输入正确的年格式如2022',
      },
      isEdit: false,
      loading: false,
      dialog: false,
      formShow: false,
      confirmDTO: {},
    }
  },
  watch: {
    // open(val) {
    //   this.dialog = val
    //   this.$refs?.form?.resetValidation()
    //   this.formData = this.initialData
    // },
    // date1(val) {
    //   // this.dialog = val
    //   console.log(val)
    //   // this.$refs?.form?.resetValidation()
    //   this.formData.nextInspectionTime = val
    // },
    // 'formData.inspectionTime'(val) {
    //   // console.log(val)
    //   /**
    //   if (this.formData.inspectionType == '2') {
    //     let date_1 = new Date(val)
    //     // console.log(date_1)
    //     let date_2 = date_1.setMonth(date_1.getMonth() + 0)
    //     // console.log(date_2)
    //     if (!!this.formData.riskLevel && this.formData.riskLevel == '0') {
    //       date_2 = date_1.setMonth(date_1.getMonth() + 9)
    //     } else if (
    //       !!this.formData.riskLevel &&
    //       this.formData.riskLevel == '1'
    //     ) {
    //       date_2 = date_1.setMonth(date_1.getMonth() + 5)
    //     } else if (
    //       !!this.formData.riskLevel &&
    //       this.formData.riskLevel == '2'
    //     ) {
    //       date_2 = date_1.setMonth(date_1.getMonth() + 2)
    //     }
    //     let date_s = new Date(date_2)
    //     // console.log(date_s)
    //     //获取yyyy-mm-dd格式日期：date_s.toISOString().substr(0, 10)
    //     this.formData.nextInspectionTime = date_s.toISOString().substr(0, 10)
    //   }
    //   */
    //   this.changeTime(val, this.formData.riskLevel)
    // },
    'ship.shipAll'(val) {
      console.log(val)
      /** 
      if (this.formData.inspectionType == '2') {
        let date_1 = new Date(this.formData.inspectionTime)
        // console.log(date_1)
        let date_2 = date_1.setMonth(date_1.getMonth() + 0)
        // console.log(date_2)
        if (!!val && val == '0') {
          date_2 = date_1.setMonth(date_1.getMonth() + 9)
        } else if (!!val && val == '1') {
          date_2 = date_1.setMonth(date_1.getMonth() + 5)
        } else if (!!val && val == '2') {
          date_2 = date_1.setMonth(date_1.getMonth() + 2)
        }
        let date_s = new Date(date_2)
        // console.log(date_s)
        //获取yyyy-mm-dd格式日期：date_s.toISOString().substr(0, 10)
        this.formData.nextInspectionTime = date_s.toISOString().substr(0, 10)
        
      }
      */
      this.shipAll = val
      console.log(this.shipAll)
      // this.changeTime(this.formData.inspectionTime, val)
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.ship.attachmentIds = attachmentIds
    },

    async delShip() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/emanage/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
    createTable() {
      this.ship = {
        company: '',
        dept: '',
        shipCode: '',
        // 当前年份
        year: new Date().getFullYear(),
        quarter: '',
        uploadDate: '',
        handler: this.$local.data.get('userInfo').nickName,
        remark: '',
        attachmentIds: [],
        attachmentRecords: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editShip() {
      this.ship = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      this.updateTaskPromptMassage(this.selected.id)
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/emanage/update'
        : '/business/seaAffairs/emanage/save'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.ship, businessType: '4' },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.ship = {
        company: '',
        dept: '',
        shipCode: '',
        year: '',
        quarter: '',
        uploadDate: '',
        handler: '',
        remark: '',
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },

    confirmInfom(item) {
      if (item.shipConfirm == 1) {
        this.$dialog.message.error(`已确认，船端反馈无法修改`)
        // if (!this.$dialog.message.confirm(`已确认，是否需要重复确认？`)) return
        return
      }
      this.dialog = true
      this.confirmDTO = JSON.parse(JSON.stringify(item))
    },

    async upData() {
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/emanage/shipConfirm`,
        { ...this.confirmDTO, shipConfirm: 1 },
      )
      if (errowRaw) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      this.dialog = false
      this.selected = false
      this.$dialog.message.success(`确认成功`)
      this.$refs.table.loadTableData()
      await this.getChildInfo()
    },

    closeForm() {
      this.$refs.form.reset()
      this.ship = {
        company: '',
        dept: '',
        shipCode: '',
        year: '',
        quarter: '',
        uploadDate: '',
        handler: '',
        remark: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
