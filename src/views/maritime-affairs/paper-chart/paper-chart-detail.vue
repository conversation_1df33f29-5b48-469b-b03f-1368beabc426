<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['纸版海图:编辑']"
      :title="`纸版海图-订购及发船-${shipName} ${pchart.purchaseYear}年度`"
      backRouteName="paper-chart-list"
      :tooltip="`纸版海图-订购及发船-${shipName} ${pchart.purchaseYear}年度`"
      :subtitles="subtitles"
      @save="save"
    >
      <template #基本信息>
        <v-container fluid>
          <v-form>
            <v-row>
              <v-col cols="12" sm="6" md="2">
                <v-text-field
                  dense
                  outlined
                  label="经办人"
                  v-model="pchart.handler"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="4">
                <v-text-field
                  dense
                  outlined
                  label="备注"
                  v-model="pchart.remark"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
          <v-row>
            <v-col cols="12">
              <div class="subtitle-1 font-weight-black">
                全年费用：{{ pchart.totalCost }}
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                1月份费用：{{ pchart.january }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                2月份费用：{{ pchart.february }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                3月份费用：{{ pchart.march }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                4月份费用：{{ pchart.april }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                5月份费用：{{ pchart.may }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                6月份费用：{{ pchart.june }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                7月份费用：{{ pchart.july }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                8月份费用：{{ pchart.auguest }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                9月份费用：{{ pchart.september }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                10月份费用：{{ pchart.october }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                11月份费用：{{ pchart.november }}
              </div>
            </v-col>
            <v-col cols="3">
              <div class="subtitle-1 font-weight-black">
                12月份费用：{{ pchart.december }}
              </div>
            </v-col>
          </v-row>
        </v-container>
      </template>

      <template #纸版海图-书订购记录按钮>
        <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click.stop="createRecord"
          v-permission="['纸版海图订购记录:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editRecord"
          v-permission="['纸版海图订购记录:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delRecord"
          v-permission="['纸版海图订购记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #纸版海图-书订购记录>
        <v-card-text>
          <v-table-list-new
            ref="table"
            :headers="headers"
            :items="pchart.pChartRecords"
            v-model="selected"
            @dbclick="editRecord"
          >
            <template v-slot:[`item.chartType`]="{ item }">
              {{ getLabel(item.chartType) }}
            </template>
            <template v-slot:[`item.supplier`]="{ item }">
              {{ getLabel1(item.supplier) }}
            </template>
            <template v-slot:[`item.shipConfirm`]="{ item }">
              <v-btn
                v-if="item.shipConfirm !== null"
                x-small
                color="primary"
                elevation="0"
                @click="confirmInfomnew(item)"
                v-permission="['纸版海图订购记录:编辑']"
              >
                {{ item.shipConfirm == 0 ? '尚未确认' : '已确认' }}
              </v-btn>
            </template>
            <template v-slot:[`item.directorConfirm`]="{ item }">
              <v-btn
                x-small
                color="primary"
                elevation="0"
                v-if="item.directorConfirm == 0"
                @click="updirectorConfirmnew(item)"
                v-permission="['纸版海图订购记录:尚未确定']"
              >
                尚未确定
              </v-btn>
              <v-btn
                x-small
                color="primary"
                elevation="0"
                v-else
                v-permission="['纸版海图订购记录:已确定']"
              >
                已确定
              </v-btn>
            </template>
          </v-table-list-new>
          <v-divider></v-divider>
        </v-card-text>
      </template>
      <v-attach-list
        class="mt-2"
        :attachments="pchart.attachmentRecords"
        @change="changeAttachment"
      ></v-attach-list>
    </v-detail-view>
    <paper-chart-order
      v-model="dialog"
      :initial-data="initialData"
      @success="success"
      :year="pchart.purchaseYear"
    ></paper-chart-order>
    <paper-chart-ship-confirm-dialog
      v-model="dialogFeedback"
      :initialData="confirmDTO"
      @confimeSuccess="confimeSuccess"
    ></paper-chart-ship-confirm-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
// import handleResizeColumn from '@/mixin/handleResizeColumn'
import paperChartOrder from '@/views/maritime-affairs/paper-chart/components/paper-chart-order'
import paperChartShipConfirmDialog from '@/views/maritime-affairs/paper-chart/components/paper-chart-ship-confirm-dialog.vue'
export default {
  name: 'paper-chart-detail',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    paperChartOrder,
    paperChartShipConfirmDialog,
  },
  mixins: [dictHelper],
  // mixins: [dictHelper, handleResizeColumn],
  created() {
    this.costHeader = [
      { text: '1月份费用', value: 'january' },
      { text: '2月份费用', value: 'february' },
      { text: '3月份费用', value: 'march' },
      { text: '4月份费用', value: 'april' },
      { text: '5月份费用', value: 'may' },
      { text: '6月份费用', value: 'june' },
      { text: '7月份费用', value: 'july' },
      { text: '8月份费用', value: 'auguest' },
      { text: '9月份费用', value: 'september' },
      { text: '10月份费用', value: 'october' },
      { text: '11月份费用', value: 'november' },
      { text: '12月份费用', value: 'december' },
    ]
    this.headers = [
      { text: '海图图书', value: 'chartName', width: 50 },
      // { text: '海图图书', value: 'chartType', width: 30 },
      { text: '资料内容', value: 'materialContent', width: 350 },
      { text: '供应商', value: 'supplier', width: 50 },
      { text: '费用', value: 'cost', width: 50 },
      { text: '币别', value: 'currencyName', width: 50 },
      { text: '发往何地', value: 'scope', width: 100 },
      // { text: '有效期', value: 'expiryTime' },
      { text: '订购时间', value: 'purchaseTime', width: 50 },
      { text: '附件', value: 'attachmentRecords', width: 50 },
      { text: '船端是否确认', value: 'shipConfirm' },
      { text: '船端反馈内容', value: 'shipFeedback' },
      { text: '船端确认信息', value: 'shipConfirmDesc' },
      { text: '主管是否确认', value: 'directorConfirm' },
      { text: '主管确认信息', value: 'directorConfirmDesc' },
      { text: '备注', value: 'remark', width: 300 },
    ]
    this.subtitles = ['基本信息', '纸版海图-书订购记录']
  },
  data() {
    return {
      pchart: {
        attachmentRecords: [],
        pchartRecords: [],
        firstQuarterCost: 0,
        secondQuarterCost: 0,
        thirdQuarterCost: 0,
        firthQuarterCost: 0,
        handler: '',
        purchaseYear: '1970',
        remark: '',
        shipName: '',
      },
      shipName: '',
      shipCode: '',
      selectedRecord: [],
      pchartType: [],
      psupplierType: [],
      attachmentIds: [],
      initialData: {},
      confirmDTO: {},
      dialog: false,
      dialogFeedback: false,
      selected: false,
    }
  },

  computed: {
    // selected() {
    //   return this.selectedRecord && this.selectedRecord.length !== 0
    //     ? this.selectedRecord[0]
    //     : false
    // },
    isShip() {
      //console.log(this.$local.data.get('userInfo').isShipSyS)//船端时返回true
      return this.$local.data.get('userInfo').isShipSyS
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.attachmentIds = attachmentIds
    },
    async getPaperChart() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/paper-chart/record/${this.$route.params.id}`,
      )
      this.updateTaskPromptMassage(this.$route.params.id)
      this.pchart = data
      let ship = await this.getAsync('/business/common/ship/simpleInfoById/', {
        id: data.shipId,
      })
      this.shipCode = ship.data?.shipCode
      this.shipName = ship.data?.chShipName || '船舶删除'
      this.$store.commit('updateViewTagsTooltip', {
        tooltip: `${this.shipName} ${data?.purchaseYear}-年度`,
        nowFullPath: this.$route.path,
      })
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    confirmInfom(item) {
      if (item.directorConfirm == 1) {
        this.$dialog.message.error(`主管已确认，船端反馈无法修改`)
        return
      }
      this.dialog = true
      this.confirmDTO = item
    },
    confirmInfomnew(item) {
      if (!this.isShip) {
        console.log('船端确认岸端无法操作')
        return
      }
      if (!item.id) {
        this.$dialog.message.error('该条记录还未提交，暂不可确认')
        return
      }
      if (item.directorConfirm == 1) {
        this.$dialog.message.error(`主管已确认，船端反馈无法修改`)
        return
      }
      this.confirmDTO = { ...item }
      this.dialogFeedback = true
    },
    async confimeSuccess() {
      await this.getPaperChart()
    },
    async updirectorConfirm(item) {
      if (item.shipConfirm == 0) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/paper-chart/directorConfirm`,
        { ...item, directorConfirm: 1 },
      )
      if (errowRaw) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      this.$dialog.message.success(`确认成功`)
      await this.getPaperChart()
    },
    async updirectorConfirmnew(item) {
      if (!item.id) {
        this.$dialog.message.error('该条记录还未提交，暂不可确认')
        return
      }
      if (item.shipConfirm == 0) {
        this.$dialog.message.error(`确认失败，请重试`)
        return
      }
      const { errowRaw } = await this.postAsync(
        `/business/seaAffairs/paper-chart/directorConfirm`,
        { ...item, directorConfirm: 1 },
      )
      if (errowRaw) {
        return
      }
      this.$dialog.message.success(`确认成功`)
      await this.getPaperChart()
    },
    getLabel(value) {
      return this.pchartType.find((item) => item.dictValue === value)?.dictLabel
    },
    getLabel1(value) {
      return this.psupplierType.find((item) => item.dictValue === value)
        ?.dictLabel
    },

    async createRecord() {
      this.initialData = {
        paperChartId: this.$route.params.id,
      }
      this.dialog = true
      // await this.success()
    },
    async editRecord() {
      this.initialData = {
        ...this.selected,
      }
      this.dialog = true
      // await this.success()
    },
    async delRecord() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/paper-chart/record/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.success()
    },
    async save() {
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/paper-chart/update',
        {
          ...this.pchart,
          attachmentIds: this.attachmentIds,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('保存成功')
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.push({
          name: 'paper-chart-list',
          query: {
            reload: true,
          },
        })
      }
    },
    async success() {
      this.selected = false
      await this.getPaperChart()
    },
  },
  // handleResizeColumn(w, col) {
  //   col.with = w
  // },
  async mounted() {
    await this.getPaperChart()
    this.pchartType = await this.getDictByType('sea_pchart_type')
    this.psupplierType = await this.getDictByType('sea_psupplier_type')
  },
}
// function handleResizeColumn(w, col) {
//   col.with = w
// }
</script>

<style></style>
