<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/maritime-affairs/book-data/paper-chart-season/new"
          v-permission="['电子海图-季度设置:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="deleChartSetting"
          v-permission="['电子海图-季度设置:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'paper-chart-season-list',
  created() {
    this.tableName = '电子海图季度'
    this.reqUrl = '/business/seaAffairs/eChartSetting/page'
    this.headers = [
      { text: '年度', value: 'year' },
      { text: '一季度起始时间', value: 'quarterFirstStart' },
      { text: '一季度结束时间', value: 'quarterFirstEnd' },
      { text: '二季度起始时间', value: 'quarterSecondStart' },
      { text: '二季度结束时间', value: 'quarterSecondEnd' },
      { text: '三季度起始时间', value: 'quarterThirdStart' },
      { text: '三季度结束时间', value: 'quarterThirdEnd' },
      { text: '四季度起始时间', value: 'quarterFourthStart' },
      { text: '四季度结束时间', value: 'quarterFourthEnd' },
    ]
    this.pushParams = {
      name: 'paper-chart-season-detail',
    }
    // this.searchDate = {
    //   label: '年度',
    //   value: 'year',
    // }
  },

  data() {
    return {
      selected: undefined,
    }
  },

  methods: {
    // editeChartSetting() {
    //   this.$router.push(
    //     `/maritime-affairs/paper-chart-eChartSetting/${this.selected.id}`,
    //   )
    // },
    async deleChartSetting() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/eChartSetting/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
