<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        各检查项统计(次数)
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="loadStatistic"
        >
          <v-icon left>mdi-magnify-expand</v-icon>
          搜索
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-row>
          <v-col cols="12" sm="6" md="2">
            <v-text-field
              outlined
              dense
              label="年份"
              clearable
              v-model="searchObj.year"
            ></v-text-field>
          </v-col>
          <v-col cols="12" sm="6" md="4">
            <v-menu
              v-model="datesMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  ref="dates"
                  :value="dateRangeText"
                  label="时间范围"
                  append-icon="mdi-calendar"
                  outlined
                  dense
                  readonly
                  clearable
                  @click:clear="dates = []"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <vc-date-picker
                v-model="dates"
                mode="date"
                is-range
              ></vc-date-picker>
            </v-menu>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-data-table
        :headers="headers"
        :items="list"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
      ></v-data-table>
    </v-card>
  </v-container>
</template>
<script>
// boardingInspection	登轮检查次数	integer(int32)
// defectNumOfBoardingInspection	登轮检查缺陷项数量	integer(int32)
// defectNumOfFSCInspection	FSC检查缺陷项数量	integer(int32)
// defectNumOfPSCInspection	PSC检查缺陷项数量	integer(int32)
// defectNumOfSelfInspection	船舶自查缺陷项数量	integer(int32)
// fscinspection		integer(int32)
// irregularInspection	不定期检查次数	integer(int32)
// proposalNumOfSelfInspection	船舶自查建议项数量	integer(int32)
// proposalNumofBoardingInspeciton	登轮检查建议项数量	integer(int32)
// pscinspection		integer(int32)
// selfInspection	船舶自查次数	integer(int32)
// shipCnName	中文船名	string
// shipEnName	英文船名	string
export default {
  name: 'security-check-statistic',
  created() {
    this.headers = [
      { text: '', value: 'a', width: '0', sortable: false },
      { text: '中文船名', value: 'shipCnName' },
      { text: '英文船名', value: 'shipEnName' },
      { text: 'FSC检查', value: 'fSCInspection' },
      { text: 'FSC缺陷项', value: 'defectNumOfFSCInspection' },
      { text: 'PSC检查', value: 'pSCInspection' },
      { text: 'PSC缺陷项', value: 'defectNumOfPSCInspection' },
      { text: '船舶自查', value: 'selfInspection' },
      { text: '自查缺陷项', value: '' },
      { text: '自查建议项', value: 'proposalNumOfSelfInspection' },
      { text: '登轮检查', value: 'boardingInspection' },
      { text: '登轮缺陷项', value: 'defectNumOfBoardingInspection' },
      { text: '登轮建议项', value: 'proposalNumofBoardingInspeciton' },
    ]
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
  },

  data() {
    return {
      selected: false,
      searchObj: {
        year: new Date().getFullYear(),
      },
      dates: [],
      datesMenu: false,
      list: [],
    }
  },

  methods: {
    async loadStatistic() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/securityCheck/statistic/checkTimesAndQuestionNumber',
        {
          ...this.searchObj,
          fromTime: this.dates?.start?.toISOString()?.split('T')[0] || null,
          toTime: this.dates?.end?.toISOString()?.split('T')[0] || null,
        },
      )
      this.list = data
    },
  },

  mounted() {
    this.loadStatistic()
  },
}
</script>

<style></style>
