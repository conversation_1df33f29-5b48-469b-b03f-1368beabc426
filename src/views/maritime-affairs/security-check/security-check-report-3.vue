<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            outlined
            label="状态"
            dense
            :items="statusMap"
            clearable
          ></v-select>
        </v-col> -->
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessType"
            outlined
            label="业务类型"
            dense
            :items="businessTypeMap"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-col v-if="false">
          <v-btn
            v-permission="['不符合报告:新增']"
            hideBtn="true"
            outlined
            color="success"
            class="mx-1"
            @click="addNonstandartReport"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            :disabled="!selected"
            hidden="true"
            outlined
            tile
            color="error"
            class="mx-1"
            @click="delAcc"
            v-permission="['不符合报告:删除']"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-col>
      </template>
      <template v-slot:[`item.shipInfoDO`]="{ item }">
        {{ item.shipInfoDO ? item.shipInfoDO.chShipName : '船舶名称' }}
      </template>
      <template v-slot:[`item.type`]="{ item }">
        {{
          [
            '港口国检查PSC',
            '船旗国检查FSC',
            '季度检查Quarterly check',
            '险情Risks',
            '船舶自查Ship self-check',
            '外审External audit',
            '事故Accidents',
            '其他Other',
          ][item.type]
        }}
      </template>
      <!-- <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{
            item.status == 2
              ? `待[${item.businessStatus}]审批`
              : statuses[item.status]
          }}
        </v-chip>
      </template> -->
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'security-check-report-3',
  created() {
    this.tableName = '安全检查-不符合报告new'
    this.reqUrl = '/business/seaAffairs/dept-report-non-compliance/page'
    this.headers = [
      { text: '编号', value: 'reportNumber' },
      { text: '船名', value: 'shipInfoDO' },
      { text: '经理/船长', value: 'managerMaster' },
      { text: '类型', value: 'type' },
      { text: '报告日期', value: 'createDate' },
      // {
      //   text: '符合规定情况、事故、险情的简要描述',
      //   value: 'reportDescription',
      // },
      // {
      //   text: '符合规定情况、事故、险情的简要描述报告日期',
      //   value: 'reportDate',
      // },
      // {
      //   text: '符合规定情况、事故、险情的简要描述报告报告人',
      //   value: 'reporter',
      // },
      // {
      //   text: '分析不符合规定情况、事故、险情产生的原因',
      //   value: 'reportCauses',
      // },
      { text: '创建日期', value: 'createTime' },
      { text: '更新日期', value: 'updateTime' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'dept-report-info-detail1',
      params: { businessType: 'securityCheck_Non' },
    }
    //dept-report-info-detail//dept-report-detail
    this.statuses = ['', '草稿', '待审批', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
    this.businessTypeMap = [
      { text: '不符合报告', value: 'nonConformanceReportNew' },
      { text: '安全检查', value: 'securityCheck_Non' },
      { text: '安全检查-险情', value: 'securityCheck_Non_3' },
      { text: '船舶事故', value: 'shipAccident_Non' },
    ]
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '待审批', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
    ]
    this.类型 = [
      { text: '港口国检查PSC', value: 0 },
      { text: '船旗国检查FSC', value: 1 },
      { text: '季度检查Quarterly check', value: 2 },
      { text: '险情Risks', value: 3 },
      { text: '船舶自查Ship self-check', value: 4 },
      { text: '外审External audit', value: 5 },
      { text: '事故Accidents', value: 6 },
      { text: '其他Other', value: 7 },
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: {
        businessType: 'securityCheck_Non',
        formCode: '47-090100-1',
      },
    }
  },

  methods: {
    addNonstandartReport() {
      // this.$store.commit('emitBussiness', {
      //   businessType: 'shipAccident',
      //   businessId: this.detail.id,
      //   templateId: this.nonstandartReportTempId,
      // })
      this.$router.push({
        name: 'dept-report-info-detail1',
        //'report-emit-detail',
        // params: { id: this.nonstandartReportTempId },
        params: { id: 'new' },
      })
    },
    async delAcc() {
      if (!this.selected.status || this.selected.status > 1) {
        this.$dialog.message.error(`只能删除草稿状态的记录！`)
        return
      }

      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/dept-report-non-compliance/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
