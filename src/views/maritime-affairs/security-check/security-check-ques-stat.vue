<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        检查缺陷/建议项细节
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="loadStatistic"
        >
          <v-icon left>mdi-magnify-expand</v-icon>
          搜索
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-row>
          <v-col cols="12" sm="6" md="2">
            <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
          </v-col>
          <v-col cols="12" sm="6" md="4">
            <v-menu
              v-model="datesMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  ref="dates"
                  :value="dateRangeText"
                  label="时间范围"
                  append-icon="mdi-calendar"
                  outlined
                  dense
                  readonly
                  clearable
                  @click:clear="dates = []"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <vc-date-picker
                v-model="dates"
                mode="date"
                is-range
              ></vc-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-text-field
              outlined
              dense
              label="年份"
              clearable
              v-model="searchObj.year"
            ></v-text-field>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-select
              outlined
              dense
              label="检查类型"
              :items="insTypes"
              v-model="searchObj.inspectionType"
            ></v-select>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-data-table
        :headers="searchObj.inspectionType == '2' ? psc_headers : headers"
        :items="list"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
        show-expand
      >
        <template v-slot:[`item.inspectionType`]="{ item }">
          {{
            ['登轮检查', '船舶自查', 'PSC检查', 'FSC检查'][item.inspectionType]
          }}
        </template>
        <template v-slot:[`item.riskLevel`]="{ item }">
          {{ ['低风险', '标准风险', '高风险'][item.riskLevel] }}
        </template>
        <template v-slot:expanded-item="{ headers, item }">
          <!-- <v-container>
            <v-row>
              <v-col class="justify-center">

              </v-col>
            </v-row>
          </v-container> -->
          <td :colspan="headers.length">
            <v-card class="d-flex justify-center my-2" flat tile>
              <v-sheet>
                <v-divider></v-divider>
                <v-data-table
                  class="my-2"
                  :headers="defctHeaders"
                  :items="item.defectDetailList"
                  hide-default-footer
                  disable-pagination
                  dense
                >
                  <template v-slot:[`item.defectType`]="{ item }">
                    {{ ['无缺陷', '缺陷项', '建议项'][item.defectType] }}
                  </template>
                </v-data-table>
                <v-divider></v-divider>
              </v-sheet>
            </v-card>
          </td>
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>
<script>
// defectDetailList	缺陷统计详情列表	array	DefectStatisticDetail
// defectType	缺陷类型	string
// description	问题描述	string
// defectItemMap	问题项Map	object
// inspectionDate	检查日期	string(date-time)
// inspectionPort	检查港口	string
// inspectionType	检查类型	string
// shipChName	中文船名	string
// shipEnName
export default {
  name: 'security-check-ques-stat',
  created() {
    this.headers = [
      { text: '', value: 'a', width: '0', sortable: false },
      { text: '中文船名', value: 'shipChName' },
      { text: '检查港口', value: 'inspectionPort' },
      { text: '检查日期', value: 'inspectionDate' },
      // { text: '下次窗口日期', value: 'nextInspectionDate' },
      // { text: '风险等级', value: 'riskLevel' },
      { text: '检查类型', value: 'inspectionType' },
      { text: '', value: 'data-table-expand' },
    ]
    this.psc_headers = [
      { text: '', value: 'a', width: '0', sortable: false },
      { text: '中文船名', value: 'shipChName' },
      { text: '检查港口', value: 'inspectionPort' },
      { text: '检查日期', value: 'inspectionDate' },
      { text: '下次窗口日期', value: 'nextInspectionDate' },
      { text: '风险等级', value: 'riskLevel' },
      { text: '检查类型', value: 'inspectionType' },
      { text: '', value: 'data-table-expand' },
    ]
    this.insTypes = [
      { text: '登轮检查', value: '0' },
      { text: '船舶自查', value: '1' },
      { text: 'PSC检查', value: '2' },
      { text: 'FSC检查', value: '3' },
    ]
    this.defctHeaders = [
      { text: '缺陷类型', value: 'defectType' },
      { text: '问题描述', value: 'description' },
    ]
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
  },

  data() {
    return {
      selected: false,
      searchObj: {
        year: new Date().getFullYear(),
      },
      dates: [],
      datesMenu: false,
      list: [],
    }
  },

  methods: {
    async loadStatistic() {
      const { data } = await this.postAsync(
        '/business/seaAffairs/securityCheck/statistic/statisticDetail',
        {
          ...this.searchObj,
          fromTime: this.dates?.start?.toISOString()?.split('T')[0] || null,
          toTime: this.dates?.end?.toISOString()?.split('T')[0] || null,
        },
      )
      this.list = data.map((s, i) => ({ id: i, ...s }))
    },

    getChartOptions(map) {
      const labels = Object.keys(map)
      // const data = Object.values(map)
      return {
        // chart: {
        //   // id: 'vuechart-example',
        //   type: 'donut',
        // },
        labels,
      }
    },

    getChartData(map) {
      return Object.values(map)
    },
  },

  mounted() {
    this.loadStatistic()
  },
}
</script>

<style></style>
