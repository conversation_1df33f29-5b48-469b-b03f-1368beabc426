<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船况评估报告:编辑']"
      :title="`船况评估报告`"
      tooltip="船况评估报告"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        !detail.auditParams ||
        detail.auditParams.taskId ||
        detail.status === '1' ||
        detail.status === '4'
      "
      :can-save="detail.status === '1' || detail.status === '4'"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <!--  -->
      <template v-slot:custombtns>
        <v-btn
          v-if="!isShip && detail.supConfirm == 0"
          width="90"
          tile
          @click="savePrompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['船况评估报告:主管提交']"
        >
          确认
        </v-btn>
        <!-- <v-btn
          v-if="isShip"
          width="90"
          tile
          @click="savePrompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['货物积载信息:船端提交']"
        >
          提交并通知岸端
        </v-btn> -->
      </template>
      <template v-if="detail.status == 3" v-slot:titlebtns>
        <v-btn
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn>
        <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['船况评估报告:下载部门报表']"
        >
          报表导出
        </v-btn>
      </template>
      <template #基本信息>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="4">
                <v-ship-select
                  v-model="detail.shipInfo.shipCode"
                  readonly
                ></v-ship-select>
              </v-col>
              <v-col
                class="py-1"
                cols="12"
                md="4"
                v-for="(h, i) in 基本信息字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  :readonly="h.value === 'marineSupervisor'"
                  dense
                  outlined
                ></v-text-field>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  dense
                  :label="h.label"
                  outlined
                  :readonly="
                    (detail.inspectionType === '2' &&
                      h.value === 'nextInspectionTime') ||
                    h.value === 'inspectionTime'
                  "
                  :hidden="
                    !detail.inspectionType != '2' &&
                    (h.value === 'nextInspectionTime' ||
                      h.value === 'marineReviewDate')
                  "
                ></vs-date-picker>
                <v-select
                  v-else-if="h.type === 'select'"
                  :label="h.label"
                  v-model="detail[h.value]"
                  :items="insTypes"
                  readonly
                  outlined
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="12">
                <v-text-field
                  outlined
                  v-model="detail.title"
                  label="标题"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #船况评估报告信息>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-container fluid v-if="detail.position === '0'">
              <v-row v-for="(h, i) in 船况评估报告信息字段" :key="i">
                <v-col cols="12" class="py-0">
                  <span style="font-size: 16px">{{ h.label }}</span>
                  <!-- <span style="color: red; font-size: 14px">
                    {{ h.desc }}
                  </span> -->
                </v-col>
                <v-col cols="12" class="py-0">
                  <span style="font-size: 14px">
                    {{ h.desc }}
                  </span>
                </v-col>
                <v-col cols="12" class="py-0">
                  <span style="color: red; font-size: 14px">
                    {{ h.desc1 }}
                  </span>
                </v-col>
                <v-col class="py-1" cols="12" md="12">
                  <v-text-field
                    v-if="h.type === 'string'"
                    v-model="detail[h.value]"
                    :label="h.label"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    outlined
                  ></v-text-field>
                  <v-textarea
                    v-if="h.type === 'stringarea'"
                    v-model="detail[h.value]"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    rows="3"
                    auto-grow
                    outlined
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
            <v-container fluid v-else-if="detail.position === '1'">
              <v-row v-for="(h, i) in 船况评估报告信息大副字段" :key="i">
                <v-col cols="12" class="py-0">
                  <span style="font-size: 16px">{{ h.label }}</span>
                  <!-- <span style="color: red; font-size: 14px">
                    {{ h.desc }}
                  </span> -->
                </v-col>
                <v-col cols="12" class="py-0">
                  <span style="font-size: 14px">
                    {{ h.desc }}
                  </span>
                </v-col>
                <v-col cols="12" class="py-0">
                  <span style="color: red; font-size: 14px">
                    {{ h.desc1 }}
                  </span>
                </v-col>
                <v-col class="py-1" cols="12" md="12">
                  <v-text-field
                    v-if="h.type === 'string'"
                    v-model="detail[h.value]"
                    :label="h.label"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    outlined
                  ></v-text-field>
                  <v-textarea
                    v-if="h.type === 'stringarea'"
                    v-model="detail[h.value]"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    rows="3"
                    auto-grow
                    outlined
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
            <v-container fluid v-else-if="detail.position === '2'">
              <v-row v-for="(h, i) in 船况评估报告信息轮机长字段" :key="i">
                <v-col cols="12" class="py-0">
                  <span style="font-size: 16px">{{ h.label }}</span>
                  <!-- <span style="color: red; font-size: 14px">
                    {{ h.desc }}
                  </span> -->
                </v-col>
                <v-col cols="12" class="py-0">
                  <span style="font-size: 14px">
                    {{ h.desc }}
                  </span>
                </v-col>
                <v-col cols="12" class="py-0">
                  <span style="color: red; font-size: 14px">
                    {{ h.desc1 }}
                  </span>
                </v-col>
                <v-col class="py-1" cols="12" md="12">
                  <v-text-field
                    v-if="h.type === 'string'"
                    v-model="detail[h.value]"
                    :label="h.label"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    outlined
                  ></v-text-field>
                  <v-textarea
                    v-if="h.type === 'stringarea'"
                    v-model="detail[h.value]"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    rows="3"
                    auto-grow
                    outlined
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
          </v-container>
        </v-form>
      </template>
      <template #船端反馈>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="12">
                <v-textarea
                  label="船端反馈"
                  dense
                  outlined
                  :readonly="!isShip"
                  v-model="detail.shipFeedback"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <!-- <template #主管建议> -->
      <template #岸基答复>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="12">
                <v-textarea
                  label="岸基答复"
                  :readonly="isShip || detail.status != '2'"
                  dense
                  outlined
                  v-model="detail.supSuggest"
                  rows="3"
                  auto-grow
                ></v-textarea>
              </v-col>
            </v-row>
            <!-- <v-row>
              <v-col class="py-1" cols="12" md="4">-->
            <!-- <v-switch
                  class="mt-1"
                  dense
                  v-model="detail.supStatus"
                  :label="`${detail.supStatus ? '确认' : '退回'}`"
                  :readonly="isShip"
                  color="success"
                ></v-switch> -->
            <!-- <v-btn
                  v-if="detail.status === '2'"
                  outlined
                  tile
                  @click="savePrompt1(backRouteName)"
                  color="success"
                  class="mx-1"
                  v-permission="['港口评估:船端提交']"
                  block
                >
                  退回
                </v-btn>
              </v-col>
            </v-row> -->
          </v-container>
        </v-form>
      </template>
      <v-card-text>
        <v-attach-list
          :title="`附件列表（上传问题照片等附件）`"
          :attachments="detail.attachments"
          @change="changeAttachment"
        ></v-attach-list>
      </v-card-text>
    </v-detail-view>
    <v-dialog v-model="attachmentDialog" max-width="700" hide-overlay>
      <v-card>
        <v-card-title class="text-h5">
          附件列表
          <!-- <span style="color: red; font-size: 14px">上传问题照片等附件</span> -->
        </v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import routerControl from '@/mixin/routerControl'
export default {
  // components: {
  //   addQuestionItemNew,
  //   addWorkItemNew,
  //   addCrewItemNew,
  //   addTrackItemNew,
  //   addOtherItemNew,
  // },
  mixins: [dictHelper, routerControl],
  name: 'ship-condition-assessment-report-detail',
  created() {
    this.backRouteName = 'ship-condition-assessment-report'
    // this.subtitles = [
    //   '填写进度',
    //   '船舶基本信息',
    //   '安全检查',
    //   '检查相关内容',
    //   '确认进度',
    // ]

    this.基本信息字段 = [
      // { label: '航线', value: 'route', type: 'string' },
      { label: '标题', value: 'title', type: 'string', readonly: false },
      { label: '经办人', value: 'handler', type: 'string', readonly: true },
      { label: '岗位', value: 'position', type: 'select', readonly: true },
      { label: '接班日期', value: 'reportTime', type: 'date', readonly: false },
      // { label: '评估日期', value: 'fillingDate', type: 'date' },
    ]
    this.insTypes = [
      { text: '船长', value: '0' },
      { text: '大副', value: '1' },
      { text: '轮机长', value: '2' },
      // { text: 'FSC检查', value: '3' },
    ]
    this.船况评估报告信息字段 = [
      {
        label: '1、全船维护保养状况及存在问题',
        desc: `1（甲板机械特别是锚设备系固装置、船体、货舱、舱盖板及附属装置、消防救生、机舱机器设备、驾驶台通导设备等）`,
        desc1: `评价标准：是否影响安全、是否会被滞留、是否影响日常使用？`,
        value: 'statusAndIssue',
        type: 'stringarea',
      },
      {
        label: '2、船员适任情况',
        desc: '（驾驶台值班、机舱值班、尽职尽责、业务能力、服从管理、身体和心理健康、有无过度娱乐/过度饮酒情况等）',
        desc1: `评价标准：是否能完成日常工作，是否影响自身、船舶或者集体安全，应知应会等职责业务是否会在检查时被滞留、是否影响船舶团结？`,
        value: 'suitabilityAndCompetence',
        type: 'stringarea',
      },
      {
        label: '3、预防船舶重大事故执行情况',
        desc: '（防碰撞、防货物倒塌落海、防丢锚丢链、防油舱泄露、防货舱进水、防火灾、防人员伤亡），要求船长亲自抓、落实公司对应的相关发文、掌握相关设备和操作的真实情况',
        desc1: `评价标准：是否影响安全、是否会被滞留、是否影响日常使用？`,
        value: 'implementationStatus',
        type: 'stringarea',
      },
      {
        label: '4、需公司提供的支持或建议', //'横摇周期：rolling period（秒）',
        desc1: '是否有遗留的严重问题需要解决？',
        value: 'supportOrRecommendation',
        type: 'stringarea',
      },
      // { label: '靠离泊', value: 'berthDisembark' },
      {
        label: '5、其他方面需要报告的内容', //'弯距bending moment（%）',
        desc1: '证书、体系运行管理状况等',
        value: 'otherAspectRequireReport',
        type: 'stringarea',
      },
    ]
    this.船况评估报告信息大副字段 = [
      {
        label: '1、甲板部维护保养的状况及存在问题',
        desc: `（锚及系固装置、消防救生设备、船体、货舱及舱盖附属设备、货舱污水井测量等）`,
        value: 'statusAndIssue',
        type: 'stringarea',
      },
      {
        label: '2、甲板部船员适任情况',
        desc: '（驾驶台值班、尽职尽责、业务能力、服从管理、身体和心理健康等）',
        value: 'suitabilityAndCompetence',
        type: 'stringarea',
      },
      {
        label: '3、预防船舶重大事故执行情况',
        desc: '（防碰撞、防货物倒塌落海、防丢锚丢链、防油舱泄露、防货舱进水、防火灾、防人员伤亡）',
        desc1: '要求认真落实公司发文、掌握相关设备和操作的真实情况',
        value: 'implementationStatus',
        type: 'stringarea',
      },
      {
        label: '4、需公司提供的支持或建议', //'横摇周期：rolling period（秒）',
        desc1: '是否有遗留的严重问题需要解决？',
        value: 'supportOrRecommendation',
        type: 'stringarea',
      },
      // { label: '靠离泊', value: 'berthDisembark' },
      {
        label: '5、其他方面需要报告的内容', //'弯距bending moment（%）',
        desc: `1.是否了解本轮稳性手册以及最小稳性要求？
2.是否了解本轮系固手册装载要求（海侧及3/4层高装载重量）？
3.垃圾记录簿、压载水记录簿和其他相关记载的状况
`,
        value: 'otherAspectRequireReport',
        type: 'stringarea',
      },
    ]
    this.船况评估报告信息轮机长字段 = [
      {
        label: '1、机电设备的状况及存在问题',
        desc1: `重点描述存在问题`,
        value: 'statusAndIssue',
        type: 'stringarea',
      },
      {
        label: '2、所有油舱油柜使用情况、保养情况描述',
        value: 'maintenanceStatusDescription',
        type: 'stringarea',
      },
      {
        label: '3、各类记录簿检查情况描述',
        desc: '（油类记录簿、生活污水、臭氧物质、NOx参数记录簿等）',
        value: 'inspectionStatusDescription',
        type: 'stringarea',
      },
      {
        label: '4、轮机部船员适任情况、团队精神', //'横摇周期：rolling period（秒）',
        desc1: '（不胜任船员名单，如有）',
        value: 'suitabilityAndCompetence',
        type: 'stringarea',
      },
      // { label: '靠离泊', value: 'berthDisembark' },
      {
        label: '5、安全管理体系在部门的总体运行情况', //'弯距bending moment（%）',
        value: 'implementationStatus',
        type: 'stringarea',
      },
      {
        label: '6、需公司提供的支持或建议', //'剪力：shear force（%）',
        value: 'supportOrRecommendation',
        type: 'stringarea',
      },
      {
        label: '7、其他方面需要报告的内容', //'本港装船货重（mt）：local loading',
        desc1:
          '（如：备件、物料等供应数量质量，燃油、滑油账面库存数量与实际是否一致）',
        value: 'otherAspectRequireReport',
        type: 'stringarea',
      },
    ]
    this.attachmentHeader = [
      { text: '名称', value: 'name' },
      { text: '大小(kb)', value: 'fileSize' },
      { text: '上传时间', value: 'createTime' },
      { text: '上传人', value: 'userName' },
    ]
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = [
      'info',
      'info',
      'info',
      'info',
      'warning',
      'warning',
      'warning',
      'warning',
      'warning',
      'success',
      'error',
    ]
  },
  computed: {
    // TODO:待不符合报告添加完毕，提交时将进行校验，未填写的不符合报告将不会被提交
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    isFilled() {
      return this.fillList.filter((i) => i.status == 1).length === 0
    },
    canEdit() {
      return ['1', '4'].includes(this.detail.status)
    },
    isShip() {
      //console.log(this.$local.data.get('userInfo').isShipSyS)//船端时返回true
      return this.$local.data.get('userInfo').isShipSyS
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/ismShipConditionAssessmentReport/exportById?id=${this.detail.id}`
        : ''
      //''
    },
  },
  watch: {
    load() {
      console.log('open ...')
    },
  },
  data() {
    return {
      subtitles: [
        // '填写进度',
        // '船舶基本信息',
        '基本信息',
        // '安全检查',
        '船况评估报告信息',
        '船端反馈',
        '岸基答复',
        // '主管建议',
        // '需跟踪事项',
        // '其他工作',
        // '确认进度',
      ],
      detail: { status: 0, inspectionType: 0, auditParams: '' },
      fillList: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      // checkList: [],
      // workList: [],
      // crewList: [],
      // trackList: [],
      // otherList: [],
      // securityReport: {},
      // selectedQues: false,
      // selectedWork: false,
      // selectedCrew: false,
      // selectedTrack: false,
      // selectedOther: false,
      dialog: false,
      // dialogWork: false,
      // dialogCrew: false,
      // dialogTrack: false,
      // dialogOther: false,
      initialData: {},
      // initialWorkData: {},
      // initialCrewData: {},
      // initialTrackData: {},
      // initialOtherData: {},
      attachmentDialog: false,
      attachments: [],
      supSuggestOld: '',
    }
  },

  methods: {
    async save(goBack, notMove = false) {
      // this.savePrompt(goBack, notMove)
      if (!this.$refs.form.validate()) {
        return
      }
      this.detail.shipCode = this.detail.shipInfo.shipCode
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/ismShipConditionAssessmentReport/updateDetail',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) goBack()
      goBack()
    },
    async savePrompt(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      this.detail.shipCode = this.detail.shipInfo.shipCode
      this.detail.supStatus = true //岸端默认直接确认
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/ismShipConditionAssessmentReport/savePrompt',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) this.closeAndTo(this.backRouteName) //this.closeAndTo(this.backRouteName)
      //this.closeAndTo(this.backRouteName)
      // goBack() //this.closeAndTo(this.backRouteName)
      this.closeAndTo(this.backRouteName)
    },
    async savePrompt1(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      this.detail.shipCode = this.detail.shipInfo.shipCode
      if (this.detail.supSuggest == '') {
        this.$dialog.message.error(`请填写主管建议`)
        return
      }
      this.detail.supStatus = false //岸端退回
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/ismShipConditionAssessmentReport/savePrompt',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) this.closeAndTo(this.backRouteName) //this.closeAndTo(this.backRouteName)
      //this.closeAndTo(this.backRouteName)
      // goBack() //this.closeAndTo(this.backRouteName)
      this.closeAndTo(this.backRouteName)
    },
    async submit(goBack) {
      // if (!this.$refs.form.validate()) {
      //   return
      // }
      // const { errorRaw } = await this.postAsync(
      //   '/business/seaAffairs/ismShipConditionAssessmentReport/savePrompt',
      //   this.detail,
      // )
      // if (!errorRaw) goBack()
      // //this.closeAndTo(this.backRouteName)
      // goBack()

      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (
        (this.detail.status == '1' || this.detail.status == '4') &&
        !this.isFilled &&
        !(await this.$dialog.msgbox.confirm(
          '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return
      console.log(this.detail.supSuggest)
      console.log(this.supSuggestOld)
      if (
        this.detail.status == '2' &&
        this.detail.supSuggest == this.supSuggestOld &&
        !(await this.$dialog.msgbox.confirm(
          '未填写岸基答复，确定提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return
      if (
        this.detail.status == '2' &&
        this.detail.supSuggest != this.supSuggestOld
      )
        this.detail.supSuggest = this.detail.supSuggest + '\r\n'
      const data = await this.save(goBack, true)
      if (!data) return false
      // const test = true //测试
      // if (test) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/ismShipConditionAssessmentReport/process/submit',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/ismShipConditionAssessmentReport/record/${this.$route.params.id}`,
      )
      this.detail = data

      this.attachmentRecords = data.attachmentRecords
      this.supSuggestOld = this.detail.supSuggest
      if (this.canEdit)
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船况评估报告信息',
          // '船端反馈',
          '岸基答复',
          // '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      else if (this.detail.status === '2')
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船况评估报告信息',
          // '船端反馈',
          '岸基答复',
          // '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      else
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船况评估报告信息',
          // '船端反馈',
          '岸基答复',
          // '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      // this.updateTaskPromptMassage(this.$route.params.id)
      // await this.loadCheckList()
      // await this.loadWorkList()
      // await this.loadCrewList()
      // await this.loadTrackList()
      // await this.loadOtherList()
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
  },

  mounted() {
    this.loadDetail()
    this.updateTaskPromptMassage(this.$route.params.id)
  },
  beforeDestroy() {
    this.$store.commit('removeBussinessParam', this.detail.id)
  },
}
</script>

<style>
.scroll-content1 {
  /* position: sticky;
  top: 0; */
  height: 400px;
  overflow-y: auto;
}
</style>
