<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form :readonly="formData.status === '3'" ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="formData.shipCode"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.title"
                  label="标题"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  v-model="formData.reportTime"
                  label="报告时间"
                  :rules="[rules.required]"
                  required
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.handler"
                  label="经办人"
                  :rules="[rules.required]"
                  required
                  readonly
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  v-model="formData.remark"
                  label="备注"
                  dense
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="formData.attachments"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
              <v-col cols="8" v-if="isEdit">
                <v-textarea
                  outlined
                  v-model="formData.supSuggest"
                  label="主管建议"
                  :readonly="isShip"
                  dense
                ></v-textarea>
              </v-col>
              <v-col
                cols="4"
                v-if="
                  !isShip &&
                  (formData.status === '1' ||
                    formData.status === '2' ||
                    formData.status === '4')
                "
              >
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="saveBack"
                  block
                  v-permission="['船况评估报告:退回']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  退回
                </v-btn>
              </v-col>
              <v-col cols="12" v-if="isEdit">
                <v-attach-list
                  title="主管附件"
                  :attachments="formData.supAttachments"
                  :readonly="isShip"
                  @change="changeAttachment2"
                ></v-attach-list>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  :disabled="formData.status === '3'"
                  block
                  v-permission="['船舶述职报告:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{
                    isEdit && isShip
                      ? '修改'
                      : isEdit && !isShip
                      ? '确认'
                      : '新增'
                  }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.history"
            label="历史数据"
            color="success"
          ></v-switch>
        </v-col> -->
      </template>
      <template #btns>
        <!-- <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['船舶述职报告:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn> -->
        <v-btn
          outlined
          color="success"
          class="mx-1"
          @click="dialog = true"
          v-permission="['船舶述职报告:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!canDel"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['船舶述职报告:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{
            item.status == 3
              ? `${item.supConfirmPerson}` + statuses[item.status]
              : statuses[item.status]
          }}
        </v-chip>
      </template>
    </v-table-searchable>
    <add-ship-work-performance-report-dialog-new
      @success="success"
      v-model="dialog"
    ></add-ship-work-performance-report-dialog-new>
  </v-container>
</template>
<script>
import addShipWorkPerformanceReportDialogNew from './private/add-ship-work-performance-report-dialog-new.vue'
export default {
  components: { addShipWorkPerformanceReportDialogNew },
  name: 'ship-work-performance-report',
  created() {
    this.tableName = '船舶述职报告'
    this.reqUrl = '/business/seaAffairs/ismShipWorkPerformanceReport/page'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '经办人', value: 'handler' },
      { text: '报告标题', value: 'title' },
      { text: '报告时间', value: 'reportTime' },
      { text: '附件', value: 'attachments', sortable: false },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.pushParams = { name: 'ship-work-performance-report-detail' }
    this.fuzzyLabel = '模糊查询'
    this.searchDate = {
      label: '演习时间',
      value: 'testTime',
    }
    this.cycleTypes = [
      { text: '三个月一次', value: '0' },
      { text: '一年一次', value: '1' },
    ]
    this.statuses = ['草稿', '草稿', '待确认', '已确认', '已退回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      searchObj: { cycleType: '2', history: false },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      dialog: false,
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    changeAttachment2(attachmentIds) {
      this.formData.supAttachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ismShipWorkPerformanceReport/deleteRecord',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async success(id) {
      this.$router.push({
        name: 'ship-work-performance-report-detail',
        params: { id },
      })
      await this.$refs.table.loadTableData()
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        supAttachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected,
        shipCode: this.selected.shipInfo.shipCode,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      this.updateTaskPromptMassage(this.selected.id)
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },

    async save() {
      if (!this.$refs.form.validate()) return
      if (this.isEdit && !this.isShip) this.formData.supStatus = true //岸端默认直接确认
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShipWorkPerformanceReport/updateRecord'
        : '/business/seaAffairs/ismShipWorkPerformanceReport/saveRecord'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData, cycleType: '2' },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },
    async saveBack() {
      //退回
      if (!this.$refs.form.validate()) return
      if (this.isEdit && !this.isShip) this.formData.supStatus = false //岸端默认直接确认
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShipWorkPerformanceReport/updateRecord'
        : '/business/seaAffairs/ismShipWorkPerformanceReport/saveRecord'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData, cycleType: '2' },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },
  computed: {
    isShip() {
      //console.log(this.$local.data.get('userInfo').isShipSyS)//船端时返回true
      return this.$local.data.get('userInfo').isShipSyS
    },
    canDel() {
      return (
        this.selected != false &&
        (this.selected.status == 1 || this.selected.status == 4)
        // this.selected.length > 0 &&
        // this.selected.every(
        //   (item) =>
        //     // item.dataSource == '单次预算' &&
        //     item.status == 1 || item.status == 4,
        //   //  &&
        //   // item.applyUser == this.$local.data.get('userInfo').id,
        // )
      )
    },
  },
  mounted() {},
}
</script>

<style></style>
