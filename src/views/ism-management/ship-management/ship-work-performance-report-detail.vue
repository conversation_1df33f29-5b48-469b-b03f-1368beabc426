<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶述职报告:编辑']"
      :title="`船舶述职报告`"
      tooltip="船舶述职报告"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        !(
          !detail.auditParams ||
          detail.auditParams.taskId ||
          detail.status === '1' ||
          detail.status === '4'
        )
      "
      :can-save="detail.status === '1' || detail.status === '4'"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <!--  -->
      <template v-slot:custombtns>
        <v-btn
          v-if="!isShip && detail.supStatus == 0"
          width="90"
          tile
          @click="savePrompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['船舶述职报告:主管提交']"
        >
          确认
        </v-btn>
        <!-- <v-btn
          v-if="isShip"
          width="90"
          tile
          @click="savePrompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['货物积载信息:船端提交']"
        >
          提交并通知岸端
        </v-btn> -->
      </template>
      <template v-if="detail.status == 3" v-slot:titlebtns>
        <v-btn
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn>
        <!-- <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['船舶检查:下载部门报表']"
        >
          报表导出
        </v-btn> -->
      </template>
      <template #基本信息>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="4">
                <v-ship-select
                  v-model="detail.shipInfo.shipCode"
                  readonly
                ></v-ship-select>
              </v-col>
              <v-col
                class="py-1"
                cols="12"
                md="4"
                v-for="(h, i) in 基本信息字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  :readonly="h.value === 'marineSupervisor'"
                  dense
                  outlined
                ></v-text-field>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  dense
                  :label="h.label"
                  outlined
                  :readonly="
                    (detail.inspectionType === '2' &&
                      h.value === 'nextInspectionTime') ||
                    h.value === 'inspectionTime'
                  "
                  :hidden="
                    !detail.inspectionType != '2' &&
                    (h.value === 'nextInspectionTime' ||
                      h.value === 'marineReviewDate')
                  "
                ></vs-date-picker>
                <v-select
                  v-else-if="h.type === 'select'"
                  :label="h.label"
                  v-model="detail[h.value]"
                  :items="insTypes"
                  readonly
                  outlined
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="12">
                <v-text-field
                  outlined
                  v-model="detail.title"
                  label="标题"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #船舶述职报告信息>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-container fluid v-if="detail.position === '0'">
              <v-row>
                <v-col
                  class="py-1"
                  cols="12"
                  md="12"
                  v-for="(h, i) in 船舶述职报告信息字段"
                  :key="i"
                >
                  <v-text-field
                    v-if="h.type === 'string'"
                    v-model="detail[h.value]"
                    :label="h.label"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    outlined
                  ></v-text-field>
                  <v-textarea
                    v-if="h.type === 'stringarea'"
                    v-model="detail[h.value]"
                    :label="h.label"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    rows="3"
                    auto-grow
                    outlined
                  ></v-textarea>
                  <v-text-field
                    v-else-if="h.type === 'number'"
                    v-model="detail[h.value]"
                    type="number"
                    :label="h.label"
                    dense
                    outlined
                  ></v-text-field>
                  <v-switch
                    v-else-if="h.type === 'switch1'"
                    class="mt-1"
                    dense
                    v-model="detail[h.value]"
                    :label="`${detail[h.value] ? '是' : '否'}` + h.label"
                    color="success"
                  ></v-switch>
                  <v-switch
                    v-else-if="h.type === 'switch11'"
                    class="mt-1"
                    dense
                    v-model="detail[h.value]"
                    :label="h.label + `${detail[h.value] ? ':是' : ':否'}`"
                    color="success"
                  ></v-switch>
                  <v-switch
                    v-else-if="h.type === 'switch2'"
                    class="mt-1"
                    dense
                    v-model="detail[h.value]"
                    :label="`${detail[h.value] ? '有' : '无'}` + h.label"
                    color="success"
                  ></v-switch>
                  <v-switch
                    v-else-if="h.type === 'switch22'"
                    class="mt-1"
                    dense
                    v-model="detail[h.value]"
                    :label="h.label + `${detail[h.value] ? ':有' : ':无'}`"
                    color="success"
                  ></v-switch>
                  <vs-date-picker
                    v-else-if="h.type === 'date'"
                    v-model="detail[h.value]"
                    dense
                    :label="h.label"
                    outlined
                    :readonly="
                      (detail.inspectionType === '2' &&
                        h.value === 'nextInspectionTime') ||
                      h.value === 'inspectionTime'
                    "
                    :hidden="
                      !detail.inspectionType != '2' &&
                      (h.value === 'nextInspectionTime' ||
                        h.value === 'marineReviewDate')
                    "
                  ></vs-date-picker>
                </v-col>
              </v-row>
            </v-container>
            <v-container fluid v-if="detail.position === '2'">
              <v-row>
                <v-col
                  class="py-1"
                  cols="12"
                  md="12"
                  v-for="(h, i) in 船舶述职报告信息轮机长字段"
                  :key="i"
                >
                  <v-text-field
                    v-if="h.type === 'string'"
                    v-model="detail[h.value]"
                    :label="h.label"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    outlined
                  ></v-text-field>
                  <v-textarea
                    v-if="h.type === 'stringarea'"
                    v-model="detail[h.value]"
                    :label="h.label"
                    :readonly="h.value === 'marineSupervisor'"
                    dense
                    rows="3"
                    auto-grow
                    outlined
                  ></v-textarea>
                  <v-text-field
                    v-else-if="h.type === 'number'"
                    v-model="detail[h.value]"
                    type="number"
                    :label="h.label"
                    dense
                    outlined
                  ></v-text-field>
                  <v-switch
                    v-else-if="h.type === 'switch1'"
                    class="mt-1"
                    dense
                    v-model="detail[h.value]"
                    :label="`${detail[h.value] ? '是' : '否'}` + h.label"
                    color="success"
                  ></v-switch>
                  <v-switch
                    v-else-if="h.type === 'switch11'"
                    class="mt-1"
                    dense
                    v-model="detail[h.value]"
                    :label="h.label + `${detail[h.value] ? ':是' : ':否'}`"
                    color="success"
                  ></v-switch>
                  <v-switch
                    v-else-if="h.type === 'switch2'"
                    class="mt-1"
                    dense
                    v-model="detail[h.value]"
                    :label="`${detail[h.value] ? '有' : '无'}` + h.label"
                    color="success"
                  ></v-switch>
                  <v-switch
                    v-else-if="h.type === 'switch22'"
                    class="mt-1"
                    dense
                    v-model="detail[h.value]"
                    :label="h.label + `${detail[h.value] ? ':有' : ':无'}`"
                    color="success"
                  ></v-switch>
                  <vs-date-picker
                    v-else-if="h.type === 'date'"
                    v-model="detail[h.value]"
                    dense
                    :label="h.label"
                    outlined
                    :readonly="
                      (detail.inspectionType === '2' &&
                        h.value === 'nextInspectionTime') ||
                      h.value === 'inspectionTime'
                    "
                    :hidden="
                      !detail.inspectionType != '2' &&
                      (h.value === 'nextInspectionTime' ||
                        h.value === 'marineReviewDate')
                    "
                  ></vs-date-picker>
                </v-col>
              </v-row>
            </v-container>
          </v-container>
        </v-form>
      </template>
      <template #船端反馈>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="12">
                <v-textarea
                  label="船端反馈"
                  dense
                  outlined
                  :readonly="!isShip"
                  v-model="detail.shipFeedback"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #主管建议>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="12">
                <v-textarea
                  label="主管建议"
                  :readonly="isShip"
                  dense
                  outlined
                  v-model="detail.supSuggest"
                ></v-textarea>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="py-1" cols="12" md="4">
                <!-- <v-switch
                  class="mt-1"
                  dense
                  v-model="detail.supStatus"
                  :label="`${detail.supStatus ? '确认' : '退回'}`"
                  :readonly="isShip"
                  color="success"
                ></v-switch> -->
                <v-btn
                  v-if="detail.status === '2'"
                  outlined
                  tile
                  @click="savePrompt1(backRouteName)"
                  color="success"
                  class="mx-1"
                  v-permission="['船舶述职报告:退回']"
                  block
                >
                  退回
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <v-card-text>
        <v-attach-list
          :attachments="detail.attachments"
          @change="changeAttachment"
        ></v-attach-list>
      </v-card-text>
    </v-detail-view>
    <v-dialog v-model="attachmentDialog" max-width="700" hide-overlay>
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import routerControl from '@/mixin/routerControl'
export default {
  // components: {
  //   addQuestionItemNew,
  //   addWorkItemNew,
  //   addCrewItemNew,
  //   addTrackItemNew,
  //   addOtherItemNew,
  // },
  mixins: [dictHelper, routerControl],
  name: 'ship-work-performance-report-detail',
  created() {
    this.backRouteName = 'ship-work-performance-report'
    // this.subtitles = [
    //   '填写进度',
    //   '船舶基本信息',
    //   '安全检查',
    //   '检查相关内容',
    //   '确认进度',
    // ]

    this.基本信息字段 = [
      // { label: '航线', value: 'route', type: 'string' },
      // { label: '港口', value: 'port', type: 'string' },
      { label: '姓名', value: 'handler', type: 'string' },
      { label: '岗位', value: 'position', type: 'select', readonly: true },
      { label: '上船时间', value: 'boardTime', type: 'date' },
      { label: '下船时间', value: 'departTime', type: 'date' },
      { text: '报告时间', value: 'reportTime', type: 'date' },
    ]
    this.insTypes = [
      { text: '船长', value: '0' },
      { text: '大副', value: '1' },
      { text: '轮机长', value: '2' },
      // { text: 'FSC检查', value: '3' },
    ]
    this.船舶述职报告信息字段 = [
      {
        label: '一、航线港口特点（航线/港口需特别应注意事项）',
        value: 'routePortCharacteristic',
        type: 'stringarea',
      },
      {
        label: '二、航行安全管理',
        value: 'navigationSafetyManagement',
        type: 'stringarea',
      },
      {
        label: '三、外部检查及防污染（PSC/FSC/外审及垃圾、油类记录簿记录）', //'横摇周期：rolling period（秒）',
        value: 'externalInspectionAndPollutionPrevention',
        type: 'stringarea',
      },
      // { label: '靠离泊', value: 'berthDisembark' },
      {
        label: '四．人员管理', //'弯距bending moment（%）',
        value: 'staffManagement',
        type: 'stringarea',
      },
      {
        label: '五、应急演习（按公约要求执行，包括船岸联合演习）', //'货重cargo weight right or not是/否准确',
        value: 'emergencyExercise',
        type: 'stringarea',
      },
      {
        label: '六、证书及重要文件管理（船舶、船员证书、重要文件及公司发文）',
        value: 'certificateFileManagement',
        type: 'stringarea',
      },
      {
        label: '七、船东、船管、租家沟通、配合（包括节能减排、成本控制等）',
        value: 'communicationCooperation',
        type: 'stringarea',
      },
      {
        label: '八、体系运行（体系、保安、能效、劳工）',
        value: 'systemOperation',
        type: 'stringarea',
      },
      {
        label: '九、维护保养（船体、设备等）',
        value: 'maintenance',
        type: 'stringarea',
      },
      {
        label: '十、管理总结（总结管理经验、存在问题、合理化的建议）',
        value: 'managementSummary',
        type: 'stringarea',
      },
    ]
    this.船舶述职报告信息轮机长字段 = [
      {
        label:
          '一、航线港口特点（航线/安全管理（人员、设备、操作、机损事故等）---20分',
        value: 'routePortCharacteristic',
        type: 'stringarea',
      },
      {
        label: '二、检查及防污染（船旗国和港口国检查及污染事故）---20分',
        value: 'externalInspectionAndPollutionPrevention',
        type: 'stringarea',
      },
      {
        label:
          '三、船技管理（机电设备运行概况、保养和管理、故障解决、任期内造水机使用情况及造水总数量）--20分', //'横摇周期：rolling period（秒）',
        value: 'maintenance',
        type: 'stringarea',
      },
      // { label: '靠离泊', value: 'berthDisembark' },
      {
        label: '四．体系运行（体系运行的有效性、内外审情况及缺陷纠正等）--10分', //'弯距bending moment（%）',
        value: 'systemOperation',
        type: 'stringarea',
      },
      {
        label: '五、日常管理（部门配合、制度执行、沟通、成本控制、培训）-10分', //'货重cargo weight right or not是/否准确',
        value: 'communicationCooperation',
        type: 'stringarea',
      },
      {
        label: '六、管理总结（总结管理经验、存在问题、合理化的建议）--20分',
        value: 'managementSummary',
        type: 'stringarea',
      },
      {
        label: '七、任期内重要故障的处理并加以描述，可配上图片（加分项20分）',
        value: 'navigationSafetyManagement',
        type: 'stringarea',
      },
    ]
    this.attachmentHeader = [
      { text: '名称', value: 'name' },
      { text: '大小(kb)', value: 'fileSize' },
      { text: '上传时间', value: 'createTime' },
      { text: '上传人', value: 'userName' },
    ]
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = [
      'info',
      'info',
      'info',
      'info',
      'warning',
      'warning',
      'warning',
      'warning',
      'warning',
      'success',
      'error',
    ]
  },
  computed: {
    // TODO:待不符合报告添加完毕，提交时将进行校验，未填写的不符合报告将不会被提交
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    isFilled() {
      return this.fillList.filter((i) => i.status == 1).length === 0
    },
    canEdit() {
      return ['1', '4'].includes(this.detail.status)
    },
    isShip() {
      //console.log(this.$local.data.get('userInfo').isShipSyS)//船端时返回true
      return this.$local.data.get('userInfo').isShipSyS
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/ismShipWorkPerformanceReport/exportById?id=${this.detail.id}`
        : ''
      //''
    },
  },
  watch: {
    load() {
      console.log('open ...')
    },
  },
  data() {
    return {
      subtitles: [
        // '填写进度',
        // '船舶基本信息',
        '基本信息',
        // '安全检查',
        '船舶述职报告信息',
        '船端反馈',
        '主管建议',
        // '需跟踪事项',
        // '其他工作',
        // '确认进度',
      ],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      detail: { status: 0, inspectionType: 0, auditParams: '' },
      fillList: [],
      // checkList: [],
      // workList: [],
      // crewList: [],
      // trackList: [],
      // otherList: [],
      // securityReport: {},
      // selectedQues: false,
      // selectedWork: false,
      // selectedCrew: false,
      // selectedTrack: false,
      // selectedOther: false,
      dialog: false,
      // dialogWork: false,
      // dialogCrew: false,
      // dialogTrack: false,
      // dialogOther: false,
      initialData: {},
      // initialWorkData: {},
      // initialCrewData: {},
      // initialTrackData: {},
      // initialOtherData: {},
      attachmentDialog: false,
      attachments: [],
    }
  },

  methods: {
    async save(goBack, notMove = false) {
      this.savePrompt(goBack, notMove)
      // if (!this.$refs.form.validate()) {
      //   return
      // }
      // const { errorRaw } = await this.postAsync(
      //   '/business/seaAffairs/PortAssessment/save',
      //   this.detail,
      // )
      // if (notMove) return this.detail.id
      // if (!errorRaw) goBack()
      goBack()
    },
    async savePrompt(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      this.detail.shipCode = this.detail.shipInfo.shipCode
      this.detail.supStatus = false
      if (!this.$local.data.get('userInfo').isShipSyS) {
        this.detail.supStatus = true
      }
      //岸端默认直接确认
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/ismShipWorkPerformanceReport/updateRecord',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) this.closeAndTo(this.backRouteName) //this.closeAndTo(this.backRouteName)
      //this.closeAndTo(this.backRouteName)
      // goBack() //this.closeAndTo(this.backRouteName)
      this.closeAndTo(this.backRouteName)
    },
    async savePrompt1(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      this.detail.shipCode = this.detail.shipInfo.shipCode
      if (this.detail.supSuggest == '') {
        this.$dialog.message.error(`请填写主管建议`)
        return
      }
      this.detail.supStatus = false //岸端退回
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/ismShipWorkPerformanceReport/updateRecord',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) this.closeAndTo(this.backRouteName) //this.closeAndTo(this.backRouteName)
      //this.closeAndTo(this.backRouteName)
      // goBack() //this.closeAndTo(this.backRouteName)
      this.closeAndTo(this.backRouteName)
    },
    async submit(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      this.detail.shipCode = this.detail.shipInfo.shipCode
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/ismShipWorkPerformanceReport/savePrompt',
        this.detail,
      )
      if (!errorRaw) goBack()
      //this.closeAndTo(this.backRouteName)
      goBack()

      // if (!(this.$refs?.aform?.validate() ?? true)) return
      // if (
      //   (this.detail.status == '1' || this.detail.status == '4') &&
      //   !this.isFilled &&
      //   !(await this.$dialog.msgbox.confirm(
      //     '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
      //   ))
      // )
      //   return

      // const data = await this.save(goBack, true)
      // if (!data) return false
      // if (!this.detail.auditParams) {
      //   const { errorRaw } = await this.getAsync(
      //     '/business/seaAffairs/PortAssessment/process/submit',
      //     { id: data },
      //   )
      //   if (!errorRaw) goBack()
      // } else {
      //   const error = await this.$refs.audit.submit()
      //   if (!error) goBack()
      // }
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/ismShipWorkPerformanceReport/record/${this.$route.params.id}`,
      )
      this.detail = data

      this.attachmentRecords = data.attachmentRecords
      if (this.canEdit)
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船舶述职报告信息',
          '船端反馈',
          '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      else if (this.detail.status === '2')
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船舶述职报告信息',
          '船端反馈',
          '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      else
        this.subtitles = [
          '基本信息',
          // '安全检查',
          '船舶述职报告信息',
          '船端反馈',
          '主管建议',
          // '需跟踪事项',
          // '其他工作',
        ]
      // this.updateTaskPromptMassage(this.$route.params.id)
      // await this.loadCheckList()
      // await this.loadWorkList()
      // await this.loadCrewList()
      // await this.loadTrackList()
      // await this.loadOtherList()
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
  },

  mounted() {
    this.loadDetail()
    this.updateTaskPromptMassage(this.$route.params.id)
  },
  beforeDestroy() {
    this.$store.commit('removeBussinessParam', this.detail.id)
  },
}
</script>

<style>
.scroll-content1 {
  /* position: sticky;
  top: 0; */
  height: 400px;
  overflow-y: auto;
}
</style>
