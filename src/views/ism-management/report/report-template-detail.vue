<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['报表模板库:编辑']"
      :title="`报表模板${isEdit ? '编辑' : '新增'}`"
      :tooltip="report.name || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template v-slot:报表模板基础信息>
        <v-card-text>
          <v-form ref="form">
            <v-container fluid class="py-0 px-0">
              <v-row>
                <v-col v-if="isEdit" cols="6" class="body-1">
                  当前模板：
                  <a
                    v-if="report.attachmentRecord.url"
                    :href="report.attachmentRecord.url"
                  >
                    {{ report.attachmentRecord.name }}
                  </a>
                  <div style="display: inline-block" v-else>暂无模板</div>
                </v-col>
                <v-col cols="12" md="6">
                  <v-file-input
                    outlined
                    :label="isEdit ? '更换模板' : '报表模板'"
                    dense
                    truncate-length="20"
                    @change="changeFile"
                    :rules="isEdit ? [] : [rules.required]"
                    ref="file"
                  ></v-file-input>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="report.name"
                    label="报表模板名称"
                    dense
                    outlined
                    :rules="[rules.required]"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                    v-model="report.formCode"
                    label="报表模板编号"
                    outlined
                    dense
                    :rules="[rules.required]"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                    v-model="report.version"
                    label="版本号"
                    outlined
                    dense
                    :rules="[rules.required]"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3">
                  <v-select
                    v-model="report.type"
                    label="报表模板类型"
                    outlined
                    dense
                    :items="types"
                    :rules="[rules.required]"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="3">
                  <v-select
                    v-model="report.period"
                    label="周期"
                    outlined
                    dense
                    :items="periods"
                    :rules="[rules.required]"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="3">
                  <v-select
                    v-model="report.docProperty"
                    label="文件属性"
                    outlined
                    dense
                    :items="docProperties"
                    :rules="[rules.required]"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="3">
                  <v-select
                    v-model="report.status"
                    label="状态"
                    :items="statuses"
                    outlined
                    dense
                  ></v-select>
                </v-col>
                <v-col cols="12" md="3">
                  <v-dialog-select
                    v-model="report.processPdfKey"
                    label="流程定义"
                    :headers="processHeaders"
                    item-text="name"
                    item-value="key"
                    :init-selected="processObj"
                    req-url="/flow/model/page"
                    @clear="delete report.processPdfKey"
                    :search-remain="processSearch"
                  >
                    <template v-slot:searchflieds>
                      <v-col cols="12" sm="6" md="3">
                        <v-text-field
                          v-model="processSearch.name"
                          outlined
                          label="流程定义名称"
                          dense
                          clearable
                        ></v-text-field>
                      </v-col>
                    </template>
                  </v-dialog-select>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                    v-model="report.code"
                    label="功能编码"
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                    v-model="report.sort"
                    outlined
                    dense
                    label="排序字段，越大越靠前，默认为0"
                    type="number"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    outlined
                    v-model="report.remark"
                    label="备注"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </template>
      <template v-if="isEdit" v-slot:报表模板映射信息按钮>
        <v-btn
          @click="createMap"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['报表模板映射信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editMap"
          v-permission="['报表模板映射信息:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delMap"
          v-permission="['报表模板映射信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:报表模板映射信息>
        <v-table-list
          v-if="isEdit"
          v-model="selected"
          :headers="mapHeaders"
          :items="mapItems"
        >
          <template v-slot:[`item.approverType`]="{ item }">
            {{ approverTypes[item.approverType] }}
          </template>
          <template v-slot:[`item.mappingType`]="{ item }">
            {{ mapTypes[item.mappingType] }}
          </template>
        </v-table-list>
        <v-card-text v-else>
          请先填写其他信息并保存,方可创建映射字段
        </v-card-text>
      </template>
    </v-detail-view>
    <report-map
      v-model="dialog"
      :initial-data="mapData"
      @success="success"
    ></report-map>
  </v-container>
</template>
<script>
import reportMap from '@/views/ism-management/report/components/report-map'
export default {
  name: 'report-template-detail',
  components: {
    reportMap,
  },
  created() {
    this.backRouteName = 'report-template-list'
    this.subtitles = ['报表模板基础信息', '报表模板映射信息']
    this.processHeaders = [
      { text: '流程定义名称', value: 'name' },
      { text: '流程分类', value: 'category' },
    ]
    this.types = [
      { text: 'SMS', value: 0 },
      { text: 'MLC', value: 1 },
      { text: '保安', value: 2 },
      { text: '专用', value: 3 },
    ]
    this.statuses = [
      { text: '禁用(业务导出)', value: '2' },
      { text: '过期', value: '1', disabled: true },
      { text: '最新', value: '0', disabled: true },
    ]
    this.mapHeaders = [
      { text: '审批人类型', value: 'approverType' },
      { text: '审批人', value: 'approverName' },
      { text: '映射类型', value: 'mappingType' },
      { text: '映射内容', value: 'field' },
      { text: '映射编码', value: 'mappingCode' },
    ]
    this.mapTypes = ['电子签名', '内容']
    this.approverTypes = ['用户', '角色']
    this.periods = [
      '需要时/及时',
      '到港前',
      '开航前',
      '每港',
      '每港/每周',
      '每年',
      '每半年',
      '每季度',
      '每月',
      '每周',
    ]
    this.docProperties = [
      { text: '船端', value: true },
      { text: '岸端', value: false },
    ]
  },
  data() {
    return {
      report: {
        formCode: '',
        attachmentRecord: {
          name: '',
          url: '',
        },
        sort: 0,
      },
      rules: {
        required: (v) => !!v || v === 0 || v === false || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      processSearch: {},
      mapItems: [],
      selected: false,
      dialog: false,
      mapData: {},
      processObj: {},
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
  },

  methods: {
    async save(goBack) {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ism/formLib/update'
        : '/business/seaAffairs/ism/formLib/save'
      const { errorRaw } = await this.postAsync(reqUrl, {
        ...this.report,
        status: 0,
      })
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`保存成功`)
      if (this.isEdit) {
        goBack()
      } else {
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.replace({
          name: 'report-template-detail',
          params: { id: this.report.formCode },
        })
      }
    },
    async changeFile(file) {
      if (!file) return
      let formData = new FormData()
      formData.append('file', file)
      if (this.reportEmit) formData.append('shipCode', this.reportEmit.shipCode)
      else formData.append('shipCode', '')
      const { data } = await this.postAsync('/system/file/upload', formData)
      if (data) {
        this.report.attachmentId = data.id
      }
      this.isSelecting = false
    },
    async loadReportTemplate() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/seaAffairs/ism/formLib/recordByCode/${this.$route.params.id}`,
      )
      this.report = data
      this.processObj = {
        key: data.processPdfKey,
        name: data.processName,
      }
      this.report.type = this.types.find(
        (item) => item.text === data.type,
      ).value
      const res = await this.getAsync(
        '/business/seaAffairs/templateMapping/getByMappingByFormCode',
        { formCode: data.formCode },
      )
      this.mapItems = res.data
    },
    createMap() {
      this.mapData = {
        formCode: this.report.formCode,
      }
      this.dialog = true
    },
    editMap() {
      this.mapData = { ...this.selected }
      this.dialog = true
    },
    async delMap() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/templateMapping/deleteMappingSetting',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.success()
    },
    async success() {
      this.selected = false
      await this.loadReportTemplate()
    },
  },

  mounted() {
    this.loadReportTemplate()
  },
}
</script>

<style></style>
