<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['港口国/船旗国检查报告:编辑']"
      :title="`港口国/船旗国检查报告-${
        isEdit ? deptReportInspection.reportNumber : '新增'
      }`"
      :tooltip="isEdit ? deptReportInspection.reportNumber : '新增'"
      :backRouteName="backRouteName"
      :can-submit="
        (!deptReportInspection.auditParams ||
          deptReportInspection.auditParams.taskId) &&
        (deptReportInspection.status == 2 ||
          deptReportInspection.status == 1 ||
          deptReportInspection.status == 4)
      "
      :can-save="isSave"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
    >
      <template v-if="deptReportInspection.status == 3" v-slot:titlebtns>
        <v-btn
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn>
        <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['不符合报告:下载部门报表']"
        >
          下载部门报表
        </v-btn>
      </template>
      <template
        v-if="!!auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="form">
            <v-audit ref="audit" :auditParams="auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  label="船舶名称MV"
                  v-model="deptReportInspection.shipInfoDO.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="编号Report number"
                  v-model="deptReportInspection.reportNumber"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="受检港口Port of Inspection"
                  v-model="deptReportInspection.inspectionPort"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="检察官员Inspection Officer"
                  v-model="deptReportInspection.inspectionOfficer"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  label="类  型 Type"
                  v-model="deptReportInspection.type"
                  outlined
                  dense
                ></v-text-field> -->
                <v-select
                  :readonly="isEdit"
                  label="检查类别Types of Inspection"
                  v-model="deptReportInspection.type"
                  outlined
                  dense
                  :items="[
                    { text: 'PSC', value: '0' },
                    { text: 'FSC', value: '1' },
                  ]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="isEdit"
                  label="检查日期Date of Inspection"
                  v-model="deptReportInspection.inspectionDate"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="检查发现的缺陷项Deficiencies  found during inspection"
                  rows="3"
                  dense
                  v-model="deptReportInspection.inspectionDeficiencies"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="发生缺陷原因的简要说明A brief description of the causes of the deficiencies"
                  rows="3"
                  dense
                  v-model="deptReportInspection.causesDescription"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="本船已采取的纠正措施Corrective measures taken by the ship"
                  rows="3"
                  dense
                  v-model="deptReportInspection.measuresTaken"
                ></v-textarea>
              </v-col>
              <!-- <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="预防再次发生该类情况的措施"
                  rows="3"
                  dense
                  v-model="deptReportInspection.preventSituation"
                ></v-textarea>
              </v-col> -->
            </v-row>
          </v-form>
          <v-row>
            <v-col cols="12">
              <v-attach-list
                :attachments="deptReportInspection.attachmentRecords"
                :readonly="isEdit"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-container>
        <!-- <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-attach-list
                :disabled="deptReportInspection.status === '3'"
                :attachments="deptReportInspection.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-card-text> -->
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'dept-report-inspection-detail',
  mixins: [routerControl],
  components: {},
  created() {
    this.backRouteName = 'dept-report-list-inspection'
  },
  data() {
    return {
      subtitles: ['基本信息'],
      // deptReportInspection: {
      //   // shipCode: '',
      //   reportNumber: '',
      //   managerMaster: '',
      //   createDate: '',
      //   createTime: '',
      //   inchargeMaster: '',
      //   type: '',
      //   reporter: '',
      //   shipInfoDO: { shipCode: '' },
      //   reportDate: '',
      //   reportDescription: '',
      //   reportCauses: '',
      //   attachmentRecords: [],
      // },
      deptReportInspection: {
        attachmentRecords: [],
        businessId: '',
        businessStatus: '',
        createDate: '',
        createTime: '',
        delFlag: false,
        id: '',
        causesDescription: '',
        inspectionDate: '',
        inspectionDeficiencies: '',
        inspectionPort: '',
        inspectionOfficer: '',
        processInstanceId: '',
        measuresTaken: '',
        postTime: '',
        reporter: '',
        reportNumber: '',
        // reporter: '',
        reviewRelevant: '',
        poster: this.$local.data.get('userInfo').id,
        shipInfoDO: {
          // chShipName: '',
          // enShipName: '',
          // id: '',
          shipCode: '',
        },
        status: null,
        type: '',
        updateTime: '',
      },
      auditParams: {},
      needFields: [],
      mapping: {},
      // pdfFile: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      status: '0',
      loading: false,
      sloading: false,
    }
  },

  watch: {
    '$store.state.reportParams.businessParams': {
      handler: function (val) {
        if (this.isEdit) return
        const businessParam = val.find(
          (item) =>
            item.businessType === 'securityCheck_Insp' &&
            item.businessId === this.$route.params.businessId1,
        )
        if (!!businessParam && businessParam.businessId) {
          console.log(
            businessParam.businessType + ' ' + businessParam.businessId,
          )
          console.log(businessParam)
          this.backRouteName = 'security-check-detail'

          this.businessParam = businessParam
          // this.detail.type = businessParam.otherParams.type
          this.deptReportInspection.businessId = businessParam.businessId
          this.deptReportInspection.shipInfoDO.shipCode =
            businessParam.otherParams.shipCode
          this.deptReportInspection.type =
            businessParam.otherParams.type == '3' ? '1' : '0'
          // this.detail.seaJson = { ...businessParam.otherParams }
          // this.detail.shipJson = { ...businessParam.otherParams }
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id == 'new'
        ? false
        : this.deptReportInspection.status == null ||
          this.deptReportInspection.status == '' ||
          this.deptReportInspection.status == '1' ||
          this.deptReportInspection.status == '4'
        ? false
        : true
    },
    isSave() {
      // console.log(this.$route.params.id)
      // console.log(this.deptReportInspection)
      // console.log(this.deptReportInspection.status)
      return this.$route.params.id == 'new'
        ? true
        : this.deptReportInspection.status == '0' ||
          this.deptReportInspection.status == '1' ||
          this.deptReportInspection.status == '4' ||
          this.deptReportInspection.status == '' ||
          this.deptReportInspection.status == null
        ? true
        : false
    },
    canSubmit() {
      return (
        !this.deptReportInspection.auditParams ||
        !!this.deptReportInspection.auditParams?.isReject
      )
    },
    isComplete() {
      let hideBtn =
        !!this.deptReportInspection.status &&
        this.deptReportInspection.status == 3
          ? true
          : false
      return hideBtn
    },
    downloadUrl() {
      return this.deptReportInspection.status === '3'
        ? `/api/business/seaAffairs/DeptReportInspection/exportById?id=${this.deptReportInspection.id}`
        : ''
    },
  },

  methods: {
    // changeAttachment(attachmentIds) {
    //   this.deptReportInspection.attachmentRecords = attachmentIds
    // },
    changeAttachment(attachmentIds) {
      this.attachmentIds = attachmentIds
    },
    async save(goBack) {
      this.loading = true
      console.log('save 1...')
      // if (!!this.businessParam && this.businessParam.businessId) {
      //   this.backRouteName = 'security-check-detail'
      // }
      console.log('this.backRouteName...' + this.backRouteName)
      // if (this.$refs.audit && !this.$refs.form.validate()) {
      //   return
      // }
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.isComplete) {
        this.$dialog.message.error(`已审批工作无法保存！`)
        return
      }
      console.log('save 2...')
      const t = true
      if (t) {
        console.log('save...')
        // 无业务伴随，正常提交
        // if (!this.businessParam) {
        if (
          !this.deptReportInspection.id ||
          this.deptReportInspection.id == 'new'
        ) {
          console.log('save createWithBusiness...1')
          const { data } = await this.postAsync(
            '/business/seaAffairs/DeptReportInspection/saveOrUpdateRecord',
            // '/business/seaAffairs/dept-report-non-compliance/createWithBusiness',
            {
              ...this.deptReportInspection,
              shipCode: this.deptReportInspection.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              attachmentIds: this.attachmentIds,
            },
          )
          // if (data) this.closeAndTo(this.backRouteName, {}, {})
          if (data) {
            this.$store.commit('setReportId', {
              ...this.businessParam,
              reportId: data,
            })
            this.closeAndTo(this.backRouteName, {}, {})
          }
        } else {
          // 有业务伴随，提交业务
          console.log('save createWithBusiness...2')
          const { data } = await this.postAsync(
            '/business/seaAffairs/DeptReportInspection/saveOrUpdateRecord',
            // '/business/seaAffairs/dept-report-non-compliance/update',
            {
              ...this.deptReportInspection,
              shipCode: this.deptReportInspection.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              // ...this.businessParam,
              // isSyncProcess: false,
              attachmentIds: this.attachmentIds,
            },
          )
          if (data) {
            this.$store.commit('setReportId', {
              ...this.businessParam,
              reportId: data,
            })
            this.closeAndTo(this.backRouteName, {}, {})
          }
        }
      }
      // 审批流提交
      // const error = await this.$refs.audit?.submit()
      // if (error) {
      //   return
      // }
      goBack()
      this.loading = false
    },
    async submit(goBack) {
      this.loading = true
      console.log('submit 1...')
      if (this.$refs.audit && !this.$refs.form.validate()) {
        return
      }
      if (!this.$refs.form.validate()) {
        return
      }
      console.log('submit 2...')
      const t = true
      if (t) {
        console.log('submit...')
        // 无业务伴随，正常提交
        // if (!this.businessParam) {
        if (
          !this.deptReportInspection.id ||
          this.deptReportInspection.id == 'new'
        ) {
          console.log('save createAndSubmit...')
          const { data } = await this.postAsync(
            '/business/seaAffairs/DeptReportInspection/submitById',
            { id: this.$route.params.id + '' },
            // {
            //   ...this.deptReportInspection,
            //   shipCode: this.deptReportInspection.shipInfoDO.shipCode,
            //   // formCode: this.reportInfo.formCode,
            //   attachmentIds: this.attachmentIds,
            // },
          )
          if (data) this.closeAndTo(this.backRouteName, {}, {})
        } else {
          // 有业务伴随，提交业务
          if (!this.deptReportInspection.auditParams) {
            console.log('save createWithBusiness...')
            const { data } = await this.getAsync(
              '/business/seaAffairs/DeptReportInspection/submitById',
              // '/business/seaAffairs/dept-report-non-compliance/update',
              { id: this.$route.params.id + '' },
              // {
              //   ...this.deptReportInspection,
              //   shipCode: this.deptReportInspection.shipInfoDO.shipCode,
              //   // formCode: this.reportInfo.formCode,
              //   // ...this.businessParam,
              //   // isSyncProcess: false,
              //   attachmentIds: this.attachmentIds,
              // },
            )
            if (data) {
              this.closeAndTo(this.backRouteName, {}, {})
            }
          }
        }
      }
      // 有映射字段需填写,且当前状态为审批中，更新字段
      if (this.needFields.length !== 0 && this.status === '2') {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            // deptReportId: this.deptReportInspection.id,
            processInstanceId:
              this.deptReportInspection.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent: this.mapping[f.mappingCode],
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) {
          return
        }
      }
      // 审批流提交
      const error = await this.$refs.audit?.submit()
      if (error) {
        return
      }
      this.loading = true
      goBack()
    },
    async loadDeptReportInfo() {
      console.log(this.$route.params.id)
      const { data } = await this.getAsync(
        // '/business/seaAffairs/deptReport/getReportDetailById',
        '/business/seaAffairs/DeptReportInspection/getDetailById',
        { id: this.$route.params.id },
      )
      console.log('data 1')
      if (data) {
        console.log(data)
        // this.deptReportInspection = data.deptReportInspection
        this.deptReportInspection = data
        this.deptReportInspection.shipInfoDO = data.shipInfo
        // {
        //   ...data,
        //   // shipCode: data.deptReportInspection.shipInfoDO.shipCode,
        // }
        console.log('data 12')
        if (data.status) this.status = data.status
      }
      console.log(this.deptReportInspection)

      console.log('data 2')

      if (data && data.auditParams) {
        this.auditParams = data.auditParams
      }
      console.log('data 3')
      if (this.status === '2') this.subtitles = ['基本信息']
      console.log('data 4')
    },
    async loadNeedFields() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedField',
        { deptReportId: this.$route.params.id },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          this.mapping[t.mappingCode] = '' || this.mappingRecords[t.field]
        }
      }
    },
  },

  async mounted() {
    await this.loadDeptReportInfo()
    // await this.loadNeedFields()
  },
}
</script>

<style scoped></style>
