<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['报告信息:编辑']"
      :title="`报告信息-${isEdit ? formData.shipBase.chShipName : '新增'}`"
      backRouteName="accident-report-list"
      :subtitles="subTitles"
      :tooltip="isEdit ? formData.shipBase.chShipName : '新增'"
      @save="save"
    >
      <template v-if="auditParams.processInstanceId" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="form">
            <v-audit ref="audit" :auditParams="auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:基础信息>
        <v-form ref="form">
          <v-card-text>
            <v-row>
              <v-col
                v-for="t in basicTextFields"
                :key="t.value"
                cols="12"
                md="4"
                class="py-0"
              >
                <v-text-field
                  v-if="t.value === 'shipCode'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-text-field>
                <v-date-time-picker
                  :items="日期"
                  v-else-if="t.value === 'classification'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  :rules="[rules.required]"
                  outlined
                ></v-date-time-picker>
                <v-select
                  :items="类型"
                  v-else-if="t.value === 'flagPort'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
                <v-select
                  :items="船旗国选项"
                  v-else-if="t.value === 'flagState'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
                <v-select
                  :items="是否二手船选项"
                  v-else-if="t.value === 'secondHand'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
                <v-select
                  :items="是否自引船舶选项"
                  v-else-if="t.value === 'selfPilot'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
                <v-select
                  :items="船舶状态选项"
                  v-else-if="t.value === 'shipStatus'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
                <v-dict-select
                  dict-type="series_ship"
                  v-else-if="t.value === 'seriesShip'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                ></v-dict-select>
                <v-text-field
                  v-else
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <template v-for="(h, i) in basicIntgerTextFields">
                <v-col :key="i" cols="12">
                  <v-textarea
                    :label="h.label"
                    v-model="formData.shipBase[h.value]"
                    dense
                    rows="3"
                    outlined
                    auto-grow
                  ></v-textarea>
                </v-col>
              </template>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'dept-report-info-detail',
  data() {
    return {
      formData: {
        shipBase: {
          chShipName: '',
        },
        shipImo: {},
        shipManage: {},
        shipParams: {},
      },
      auditParams: {},
      rules: {
        required: (v) => !!v || v == 0 || '必填项不能为空',
        yyyymmdd: (v) => /^\d{4}-\d{2}-\d{2}$/.test(v) || '请输入yyyy-mm格式',
      },
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
  },

  methods: {
    async save(goBack) {
      if (this.$refs.audit && !this.$refs.form.validate()) {
        return
      }
      if (!this.$refs.form.validate() || !this.$refs.form1.validate()) {
        return
      }
      const 基础信息链接 = this.isEdit
        ? '/business/common/ship/base/update'
        : '/business/common/ship/base/save'
      // const 船舶参数链接 = this.isEdit
      //   ? '/business/common/ship/params/update'
      //   : '/business/common/ship/params/save'
      // const 通导信息链接 = this.isEdit
      //   ? '/business/common/ship/imo/update'
      //   : '/business/common/ship/imo/save'
      // const 管理信息链接 = this.isEdit
      //   ? '/business/common/ship/management/update'
      //   : '/business/common/ship/management/save'
      let shipDataReqs = []
      shipDataReqs.push(this.postAsync(基础信息链接, this.formData.shipBase))
      // shipDataReqs.push(
      //   this.postAsync(通导信息链接, {
      //     shipCode: this.formData.shipBase.shipCode,
      //     ...this.formData.shipImo,
      //   }),
      // )
      // shipDataReqs.push(
      //   this.postAsync(船舶参数链接, {
      //     shipCode: this.formData.shipBase.shipCode,
      //     ...this.formData.shipParams,
      //   }),
      // )
      // shipDataReqs.push(
      //   this.postAsync(管理信息链接, {
      //     shipCode: this.formData.shipBase.shipCode,
      //     ...this.formData.shipManage,
      //   }),
      // )
      const results = await Promise.all(shipDataReqs)
      if (results.every((item) => !item.errorRaw)) {
        goBack()
      }
    },
    async loadShipIfo() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync('/business/common/ship/detailInfo', {
        code: this.$route.params.id,
      })
      this.formData.shipBase = data.shipBase || {}
      this.formData.shipImo = data.shipImo || {}
      this.formData.shipManage = data.shipManage || {}
      this.formData.shipParams = data.shipParams || {}
    },
  },
  created() {
    // this.subTitles = ['基础信息', '通导信息', '船舶参数', '管理信息']
    this.subTitles = ['基础信息']
    this.basicTextFields = [
      { label: '公司部门/船名', value: 'chShipName' },
      { label: '经理/船长', value: 'enShipName' },
      { label: '船舶编号', value: 'shipCode' },
      { label: '日期', value: 'classification' },
      { label: '类型', value: 'flagPort' },
      // { label: '不符合、事故、险情简要描述', value: 'flagState' },
      // { label: '不符合、事故、险情产生的原因', value: 'insuranceCompany' },
      // { label: '报告人', value: 'sapCenterCode' },
      // { label: '报告日期', value: 'sapCode' },
      // { label: '相关人员的评审', value: 'seriesShip' },
      // { label: '已采取纠正措施及完成纠正情况的简要描述', value: 'shipType' },
      // { label: '预防再次发生该类情况的措施', value: 'shipStatus' },
      // { label: '验证意见', value: 'secondHand' },
      // { label: '自引', value: 'selfPilot' },
    ]
    this.basicIntgerTextFields = [
      { label: '不符合、事故、险情简要描述', value: 'maximumSafeManning' },
      { label: '不符合、事故、险情产生的原因', value: 'minimumSafeManning' },
      // { label: '预防再次发生该类情况的措施', value: 'standardSafeManning' },
      // { label: '验证意见', value: 'paymentCompany' },
    ]
    this.ImoTextFields = [
      // { label: '船舶呼号', value: 'callSign' },
      // { label: 'cno', value: 'cno' },
      // { label: 'FBB电话', value: 'fbbNo' },
      // { label: 'FO电话', value: 'foNo' },
      // { label: '国外手机号码', value: 'foreignTelephoneNo' },
      // { label: 'IMO编号', value: 'imoNumber' },
      // { label: 'IRRIDIUM电话', value: 'imo' },
      // { label: 'LRIT号码', value: 'irridiumNo' },
      // { label: '1749邮箱', value: 'lritNo' },
      // { label: 'RYDEX邮箱', value: 'mail1749' },
      // { label: '海上移动业务识别码', value: 'mailRydex' },
      // { label: '船舶登记号', value: 'mmsi' },
      // { label: '其他卫通电话', value: 'officialNumber' },
      // { label: 'SSAS号码', value: 'ssasNo' },
      // { label: 'VSAT电话', value: 'vsatNo' },
    ]
    this.shipParamsTextFields = [
      // { label: '航区装载限制', value: 'areaLoadingRestrictions' },
      // { label: '造船厂', value: 'builderCompany' },
      // { label: '交船日期', value: 'deliveredDate' },
      // { label: '船壳编号', value: 'hullNo' },
      // { label: '建造日期', value: 'keelLaidDate' },
      // { label: '下水日期', value: 'launchedDate' },
      // { label: '船龄', value: 'shipAge' },
    ]
    this.shipParamsIntgerTextFields = [
      // { label: '总吨', value: 'grossTonnage' },
      // { label: '净吨', value: 'netTonnage' },
      // { label: '总载重吨位', value: 'gdwt' },
      // { label: '净载重吨位 t', value: 'dwct' },
      // { label: '燃油日消耗', value: 'fuelOilConsumption' },
      // { label: 'TEU数量', value: 'teu' },
      // { label: '厘米吃水吨数', value: 'tpc' },
      // { label: '冷箱插座数量', value: 'reeferContainerSocket' },
      // { label: '型宽 m', value: 'breadth' },
      // { label: '满载吨位 t', value: 'deadWeight' },
      // { label: '型深 m', value: 'depth' },
      // { label: '最高航速 kn', value: 'designSpeed' },
      // { label: '总长 m', value: 'loa' },
    ]
    this.类型 = [
      '港口国检查PSC',
      '船旗国检查FSC',
      '季度检查Quarterly check',
      '险情Risks',
      '船舶自查Ship self-check',
      '外审External audit',
      '事故Accidents',
      '其他Other',
    ]
    this.系列船舶选项 = ['集装箱', '散货船']
    this.船旗国选项 = ['香港', '巴拿马', '中国']
    this.船级社选项 = ['HK', 'DNV', 'CCS', 'LR', 'BV', 'ABS']
    this.付款公司选项 = ['3000', '3800', '8903', '3402']
    this.船舶状态选项 = [
      { text: '运营', value: 0 },
      { text: '坞修', value: 1 },
      { text: '停航', value: 2 },
      { text: '退役', value: 3 },
      { text: '卖船', value: 4 },
    ]
    this.是否二手船选项 = [
      { text: '否', value: 0 },
      { text: '是', value: 1 },
    ]
    this.是否自引船舶选项 = [
      { text: '否', value: 0 },
      { text: '是', value: 1 },
    ]
  },
  mounted() {
    this.loadShipIfo()
  },
}
</script>

<style></style>
