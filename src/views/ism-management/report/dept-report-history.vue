<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :push-params="pushParams"
      fuzzy-label="报表名称"
    >
      <template #searchflieds></template>
      <template v-slot:[`item.shipInfoDO`]="{ item }">
        {{ item.shipInfoDO ? item.shipInfoDO.chShipName : '岸端报表' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'dept-report-history',
  created() {
    this.tableName = '报表记录库'
    this.reqUrl = '/business/seaAffairs/deptReport/historyProcessPage'
    this.pushParams = { name: 'dept-report-detail' }
    this.headers = [
      { text: '报表任务名称', value: 'reportName' },
      { text: '船名', value: 'shipInfoDO' },
      { text: '发起人', value: 'poster' },
      { text: '年度', value: 'year' },
      { text: '季度', value: 'season' },
      { text: '发起时间', value: 'postTime' },
      { text: '备注', value: 'remark' },
    ]
    this.searchDate = {
      label: '发起时间',
      interval: true,
    }
  },

  data() {
    return {
      selected: false,
    }
  },

  methods: {
    async delDeptReport() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/circular/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
