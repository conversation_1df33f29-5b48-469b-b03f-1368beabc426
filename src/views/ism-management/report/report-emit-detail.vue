<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['报表发起中心:编辑']"
      :title="`${reportInfo.name}-发起`"
      :tooltip="reportInfo.name"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <!-- <v-btn width="80" tile @click="save" color="success" small class="mx-1">
          保存
        </v-btn> -->
        <v-btn
          width="80"
          tile
          @click="submit"
          color="success"
          small
          class="mx-1"
        >
          {{ !!businessParam ? '保存返回业务' : '保存并提交' }}
        </v-btn>
      </template>
      <template v-slot:报表基础信息>
        <v-card-text>
          <v-row>
            <v-col cols="3">报表编号：{{ reportInfo.formCode }}</v-col>
            <v-col cols="3">报表名称：{{ reportInfo.name }}</v-col>
            <v-col cols="3">报表类型：{{ reportInfo.type }}</v-col>
            <v-col cols="3">报表版本：{{ reportInfo.version }}</v-col>
            <v-col cols="3">报表上传人：{{ reportInfo.uploadUser }}</v-col>
            <v-col cols="3">模板备注：{{ reportInfo.remark }}</v-col>
            <v-col cols="6">
              报表模板：
              <a v-if="reportTemplate.url" :href="reportTemplate.url">
                {{ reportTemplate.name }}
              </a>
              <div style="display: inline-block" v-else>暂无模板</div>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <template v-slot:填写报表>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-file-input
                  outlined
                  label="报表文件"
                  dense
                  truncate-length="20"
                  @change="changeFile"
                  :rules="[rules.required]"
                ></v-file-input>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="reportEmit.reportName"
                  label="报表文件名称"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="reportEmit.shipCode"
                  label="报表所属船舶"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="reportEmit.year"
                  label="报表年度"
                  dense
                  :rules="[rules.required, rules.number]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="reportEmit.season"
                  label="报表季度"
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-dialog-select
                  label="经办人"
                  item-text="nickName"
                  item-value="id"
                  :init-selected="initUser"
                  v-model="reportEmit.poster"
                  :headers="userHeaders"
                  :rules="[rules.required]"
                  req-url="/system/user/page"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field label="用户名"></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="reportEmit.remark"
                  label="备注"
                  outlined
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
const routeNameBussinessTypeMap = {
  shipAccident: 'accident-detail',
  securityCheck: 'security-check-detail',
  shoreDocTrainForTrainer: 'shore-train-list',
  shoreDocTrainForManager: 'shore-train-list',
}
export default {
  mixins: [routerControl],
  name: 'report-emit-detail',
  created() {
    this.title = '报表模板发起'
    this.backRouteName = 'report-emit-list'
    this.subtitles = ['报表基础信息', '填写报表']
    this.userHeaders = [
      { text: '用户名', value: 'nickName' },
      { text: '部门名称', value: 'deptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
  },
  computed: {
    businessParam() {
      return this.$store.state.reportParams.businessParams.find(
        (i) => i.templateId === this.$route.params.id,
      )
    },
  },
  data() {
    return {
      reportInfo: {
        formCode: '',
        name: '',
        type: '',
        version: '',
        uploadUser: '',
        status: '',
      },
      reportTemplate: {
        name: '',
        url: '',
      },
      reportEmit: {
        poster: this.$local.data.get('userInfo').id,
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      initUser: {
        id: this.$local.data.get('userInfo').id,
        nickName: this.$local.data.get('userInfo').nickName,
      },
    }
  },

  methods: {
    async loadReportInfo() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/ism/formLib/record/${this.$route.params.id}`,
      )
      this.reportInfo = data
      if (data.attachmentRecord) {
        this.reportTemplate.name = data.attachmentRecord.name
        this.reportTemplate.url = `/api/system/file/download?fileName=${encodeURIComponent(
          data.attachmentRecord.name,
        )}&filePath=${data.attachmentRecord.filePath}`
      }
      // this.reportTypeMap = await this.getDictByType('report_type_mapping')
    },
    async changeFile(file) {
      if (!file) return
      this.isSelecting = 'primary'
      let formData = new FormData()
      formData.append('file', file)
      formData.append('shipCode', this.reportEmit.shipCode)
      const { data } = await this.postAsync('/system/file/upload', formData)
      if (data) {
        this.reportEmit['attachmentIds'] = [data.id]
      }
      this.isSelecting = false
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      // 无业务伴随，正常提交
      // if (!this.businessParam) {
      //   const { data } = await this.postAsync(
      //     '/business/seaAffairs/deptReport/createAndSubmit',
      //     { ...this.reportEmit, formCode: this.reportInfo.formCode },
      //   )
      //   if (data) this.closeAndTo(this.backRouteName, {}, {})
      // } else {
      // 有业务伴随，提交业务
      const { data } = await this.postAsync(
        '/business/seaAffairs/deptReport/createWithBusiness',
        {
          ...this.reportEmit,
          formCode: this.reportInfo.formCode,
          ...this.businessParam,
          isSyncProcess: false,
        },
      )
      if (data) {
        const businessType = this.businessParam.businessType
        const id = this.businessParam.businessId
        this.$store.commit('setReportId', {
          ...this.businessParam,
          reportId: data,
        })
        this.closeAndTo(routeNameBussinessTypeMap[businessType], { id })
        // }
      }

      // if (data) {
      //   console.log('asd')
      //   const { errorRaw } = await this.getAsync(
      //     '/business/seaAffairs/templateMapping/executeMapping',
      //     { deptReportId: data },
      //   )
      //   if (!errorRaw) goBack()
      // }
    },
    async submit() {
      if (!this.$refs.form.validate()) {
        return
      }
      // 无业务伴随，正常提交
      if (!this.businessParam) {
        const { data } = await this.postAsync(
          '/business/seaAffairs/deptReport/createAndSubmit',
          { ...this.reportEmit, formCode: this.reportInfo.formCode },
        )
        if (data) this.closeAndTo(this.backRouteName, {}, {})
      } else {
        // 有业务伴随，提交业务
        const { data } = await this.postAsync(
          '/business/seaAffairs/deptReport/createWithBusiness',
          {
            ...this.reportEmit,
            formCode: this.reportInfo.formCode,
            ...this.businessParam,
            isSyncProcess: false,
          },
        )
        if (data) {
          const businessType = this.businessParam.businessType
          const id = this.businessParam.businessId
          this.$store.commit('setReportId', {
            ...this.businessParam,
            reportId: data,
          })
          this.closeAndTo(routeNameBussinessTypeMap[businessType], { id })
        }
      }

      // if (data) {
      //   console.log('asd')
      //   const { errorRaw } = await this.getAsync(
      //     '/business/seaAffairs/templateMapping/executeMapping',
      //     { deptReportId: data },
      //   )
      //   if (!errorRaw) goBack()
      // }
    },
  },

  mounted() {
    this.loadReportInfo()
  },
}
</script>

<style></style>
