<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      item-key="formCode"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <v-select
            v-model="searchObj.type"
            outlined
            label="报表模板类型"
            dense
            :items="types"
            clearable
          ></v-select>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="3">
          <v-select
            v-model="searchObj.status"
            outlined
            label="模板状态"
            dense
            :items="statuses"
            clearable
          ></v-select>
        </v-col> -->
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.name"
            outlined
            label="报表模板名称"
            dense
            clearable
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/ism-management/ship-report/tempelate/new"
          v-permission="['报表模板库:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delReport"
          v-permission="['报表模板库:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.attachmentRecord`]="{ item }">
        <div class="text-truncate" style="max-width: 150px">
          <v-chip
            v-if="item.attachmentRecord"
            class="primary"
            small
            target="_blank"
            :href="`/api/system/file/download?fileName=${encodeURIComponent(
              item.attachmentRecord.name,
            )}&filePath=${item.attachmentRecord.filePath}`"
            elevation="0"
          >
            {{ item.attachmentRecord.name || '未知文件' }}
          </v-chip>
          <v-chip v-else small>暂无模板</v-chip>
        </div>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'report-template-list',
  created() {
    this.tableName = '报表模板库'
    this.reqUrl = '/business/seaAffairs/ism/formLib/page'
    this.headers = [
      { text: '模板名称', value: 'name' },
      { text: '模板编号', value: 'formCode' },
      { text: '模板类型', value: 'type' },
      { text: '报表周期', value: 'period' },
      // { text: '模板状态', value: 'status' },
      // { text: '文件类型', value: 'attachmentType' },
      { text: '上传时间', value: 'updateTime' },
      { text: '上传人', value: 'uploadUser' },
      { text: '版本', value: 'version' },
      { text: '模板文件', value: 'attachmentRecord' },
      { text: '备注', value: 'remark' },
    ]
    this.pushParams = { name: 'report-template-detail' }
    this.types = [
      { text: 'SMS', value: 0 },
      { text: 'MLC', value: 1 },
      { text: '保安', value: 2 },
      { text: '专用', value: 3 },
    ]
    this.statuses = [
      { text: '过期', value: '1' },
      { text: '最新', value: '0' },
    ]
    this.fileType = ['word', 'excel', 'pdf']
  },

  data() {
    return {
      selected: false,
      searchObj: {},
      report: {},
      formShow: false,
      isEdit: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      processSearch: {},
      isSelecting: false,
    }
  },

  methods: {
    async delReport() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ism/formLib/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
