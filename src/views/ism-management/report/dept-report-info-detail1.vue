<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶事故-不符合报告:编辑']"
      :title="`不符合报告${
        deptReportNonCompliance.businessType == 'securityCheck_Non_3'
          ? '（险情）'
          : ''
      }-${isEdit ? deptReportNonCompliance.reportNumber : '新增'}`"
      :tooltip="isEdit ? deptReportNonCompliance.reportNumber : '新增'"
      :backRouteName="isbackRouteName"
      :can-submit="canSubmit"
      :can-save="isSave"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
    >
      <template
        v-if="
          deptReportNonCompliance.status === 3 ||
          deptReportNonCompliance.status === '3'
        "
        v-slot:custombtns
      >
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['不符合报告:下载部门报表']"
        >
          下载部门报表
        </v-btn>
      </template>
      <template
        v-if="!!auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="form">
            <v-audit ref="audit" :auditParams="auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  label="公司部门/船名Company Department/MV"
                  v-model="deptReportNonCompliance.shipInfoDO.shipCode"
                  outlined
                  dense
                ></v-text-field> -->
                <v-ship-select
                  :readonly="isEdit"
                  label="公司部门/船名Company Department/MV"
                  v-model="deptReportNonCompliance.shipInfoDO.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="编号Report number"
                  v-model="deptReportNonCompliance.reportNumber"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="经理/船长Manager/Master"
                  v-model="deptReportNonCompliance.managerMaster"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  label="类  型 Type"
                  v-model="deptReportNonCompliance.type"
                  outlined
                  dense
                ></v-text-field> -->
                <v-select
                  :readonly="
                    isEdit ||
                    deptReportNonCompliance.businessType ==
                      'securityCheck_Non_3'
                  "
                  label="类  型 Type"
                  v-model="deptReportNonCompliance.type"
                  :rules="[rules.required]"
                  outlined
                  dense
                  :items="[
                    { text: '港口国检查PSC', value: '0' },
                    { text: '船旗国检查FSC', value: '1' },
                    { text: '季度检查Quarterly check', value: '2' },
                    { text: '险情Risks', value: '3' },
                    { text: '船舶自查Ship self-check', value: '4' },
                    { text: '外审External audit', value: '5' },
                    { text: '事故Accidents', value: '6' },
                    { text: '其他Other', value: '7' },
                  ]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="isEdit"
                  label="日期Date"
                  v-model="deptReportNonCompliance.createDate"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="不符合规定情况、事故、险情的简要描述"
                  rows="3"
                  :rules="
                    deptReportNonCompliance.businessType ==
                    'securityCheck_Non_3'
                      ? [rules.required]
                      : []
                  "
                  dense
                  v-model="deptReportNonCompliance.reportDescription"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="报告人Reporter"
                  v-model="deptReportNonCompliance.reporter"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="isEdit"
                  label="报告日期Date"
                  v-model="deptReportNonCompliance.reportDate"
                  outlined
                  dense
                ></vs-date-picker>
                <!-- <v-text-field
                  label="报告日期Date"
                  v-model="deptReportNonCompliance.reportDate"
                  outlined
                  dense
                ></v-text-field> -->
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="分析不符合规定情况、事故、险情产生的原因"
                  rows="3"
                  dense
                  v-model="deptReportNonCompliance.reportCauses"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                相关人员的评审Review of relevant personnel：
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="1  严重程度Degree of severity"
                  rows="3"
                  dense
                  v-model="deptReportNonCompliance.severityDegree"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="2  影响范围Scope of influence"
                  rows="3"
                  dense
                  v-model="deptReportNonCompliance.influenceScope"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="3  处置方式Methods of disposing"
                  rows="3"
                  dense
                  v-model="deptReportNonCompliance.disposingMethods"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="4  拟纠正的期限The time limit for the proposed correction"
                  rows="3"
                  dense
                  v-model="deptReportNonCompliance.correctionLimit"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="5  责任人Person in charge"
                  rows="3"
                  dense
                  v-model="deptReportNonCompliance.inchargePerson"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="9"></v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="主管部门/船长Department in charge/Master"
                  v-model="deptReportNonCompliance.inchargeMaster"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="已采取纠正措施及完成纠正情况的简要描述"
                  rows="3"
                  dense
                  v-model="deptReportNonCompliance.acorrectiveTaken"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="9"></v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="主管部门/船长Department in charge/Master"
                  v-model="deptReportNonCompliance.inchargeMaster1"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="预防再次发生该类情况的措施"
                  rows="3"
                  dense
                  v-model="deptReportNonCompliance.preventSituation"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="9"></v-col>
              <v-col cols="12" md="3" align-self="end">
                <v-text-field
                  :readonly="isEdit"
                  label="主管部门/船长Department in charge/Master"
                  v-model="deptReportNonCompliance.inchargeMaster2"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
          <v-row>
            <v-col cols="12">
              <v-attach-list
                :attachments="deptReportNonCompliance.attachmentRecords"
                :readonly="isEdit"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-container>
        <!-- <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-attach-list
                :disabled="deptReportNonCompliance.status === '3'"
                :attachments="deptReportNonCompliance.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-card-text> -->
      </template>
      <template v-slot:映射内容>
        <v-card-text>
          <v-row>
            <v-col v-for="t in contents" :key="t.id" cols="12" md="4">
              <v-text-field
                v-model="mapping[t.mappingCode]"
                :label="t.field"
              ></v-text-field>
            </v-col>
            <v-col v-if="!!signNatures.length" cols="12">
              <div>
                当前节点存在电子签名，审批结果为通过，包含：
                <b v-for="t in signNatures" :key="t.id">
                  {{ t.field }}
                </b>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'dept-report-info-detail1',
  mixins: [routerControl],
  components: {},
  created() {
    // this.backRouteName = 'accident-report-list-new'
    // this.subtitles = ['基础信息', '映射内容']
  },
  data() {
    return {
      backRouteName: 'accident-report-list-new',
      subtitles: ['基本信息', '映射内容'],
      // deptReportNonCompliance: {
      //   // shipCode: '',
      //   reportNumber: '',
      //   managerMaster: '',
      //   createDate: '',
      //   createTime: '',
      //   inchargeMaster: '',
      //   type: '',
      //   reporter: '',
      //   shipInfoDO: { shipCode: '' },
      //   reportDate: '',
      //   reportDescription: '',
      //   reportCauses: '',
      //   attachmentRecords: [],
      // },
      deptReportNonCompliance: {
        attachmentRecords: [],
        businessId: '',
        businessType: '',
        businessStatus: '',
        createDate: '',
        createTime: '',
        delFlag: false,
        id: '',
        inchargeMaster: '',
        managerMaster: '',
        preventSituation: '',
        acorrectiveTaken: '',
        processInstanceId: '',
        reportCauses: '',
        reportDate: '',
        reportDescription: '',
        reportNumber: '',
        reporter: '',
        reviewRelevant: '',
        poster: this.$local.data.get('userInfo').id,
        shipInfoDO: {
          // chShipName: '',
          // enShipName: '',
          // id: '',
          shipCode: '',
        },
        status: null,
        type: '',
        updateTime: '',
      },
      auditParams: {},
      needFields: [],
      mapping: {},
      mappingRecords: {},
      // pdfFile: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      status: '0',
      loading: false,
      sloading: false,
    }
  },

  watch: {
    // $route() {
    //   console.log(this.$route)
    // },
    '$store.state.reportParams.businessParams': {
      handler: function (val) {
        if (this.$route.params.id == 'new') {
          this.deptReportNonCompliance.businessType = 'nonConformanceReportNew'
          // this.backRouteName = 'security-check-report-3'
        } else if (this.$route.params.id == 'securityCheck_Non_3_new') {
          this.deptReportNonCompliance.type = '3'
          this.deptReportNonCompliance.businessType = 'securityCheck_Non_3'
          this.backRouteName = 'security-check-report-3'
        }
        if (this.isEdit) return
        // console.log(val)
        const businessParam = val.find(
          (item) =>
            // (!item.reportId || item.reportId === '') &&
            (item.businessType === 'securityCheck_Non' ||
              item.businessType === 'shipAccident_Non') &&
            item.businessId === this.$route.params.businessId1, //验证当前最新传入的业务id，多业务id时精准匹配验证
        )
        // if (!!businessParam && businessParam.businessType)
        if (!!businessParam && businessParam.businessId) {
          // console.log(
          //   businessParam.businessType + ' ' + businessParam.businessId,
          // )
          if (businessParam.businessType === 'securityCheck_Non')
            this.backRouteName = 'security-check-report-3'
          else this.backRouteName = 'accident-report-list-new'
          console.log(
            businessParam.businessType + ' ' + businessParam.businessId,
          )

          this.businessParam = businessParam
          this.deptReportNonCompliance.type = businessParam.otherParams.type
          this.deptReportNonCompliance.businessId = businessParam.businessId
          this.deptReportNonCompliance.businessType = businessParam.businessType
          this.deptReportNonCompliance.shipInfoDO.shipCode =
            businessParam.otherParams.shipCode
          // this.detail.seaJson = { ...businessParam.otherParams }
          // this.detail.shipJson = { ...businessParam.otherParams }
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    isbackRouteName() {
      //根据传过来的类型调整返回列表
      //   if (this.$route.params.businessType === 'securityCheck_Non')
      //     this.backRouteName = 'security-check-report-3'
      //   else this.backRouteName = 'accident-report-list-new'
      return this.$route.params.businessType === 'securityCheck_Non' ||
        this.$route.params.businessType === 'securityCheck_Non_3'
        ? 'security-check-report-3'
        : 'accident-report-list-new'
    },
    isEdit() {
      return this.$route.params.id == 'new'
        ? false
        : this.deptReportNonCompliance.status == null ||
          this.deptReportNonCompliance.status == '' ||
          this.deptReportNonCompliance.status == '1' ||
          this.deptReportNonCompliance.status == '4'
        ? false
        : true
    },
    isSave() {
      return this.$route.params.id == 'new'
        ? true
        : this.deptReportNonCompliance.status == '0' ||
          this.deptReportNonCompliance.status == '1' ||
          this.deptReportNonCompliance.status == '' ||
          this.deptReportNonCompliance.status == '4' ||
          this.deptReportNonCompliance.status == null
        ? true
        : false
    },
    canSubmit() {
      return (
        this.$route.params.id == 'new' ||
        ((!this.deptReportNonCompliance.auditParams ||
          !!this.deptReportNonCompliance.auditParams?.isReject) &&
          this.deptReportNonCompliance.status == 2) ||
        (this.deptReportNonCompliance.businessType == 'securityCheck_Non_3' &&
          this.deptReportNonCompliance.status != '3') ||
        this.deptReportNonCompliance.status == 4
      )
    },
    isComplete() {
      let hideBtn =
        !!this.deptReportNonCompliance.status &&
        this.deptReportNonCompliance.status == 3
          ? true
          : false
      return hideBtn
    },
    signNatures() {
      return this.needFields.filter((i) => i.mappingType === '0')
    },
    contents() {
      return this.needFields.filter((i) => i.mappingType === '1')
    },
    downloadUrl() {
      return this.deptReportNonCompliance.status === '3'
        ? `/api/business/seaAffairs/dept-report-non-compliance/exportById?id=${this.deptReportNonCompliance.id}`
        : `/api/business/seaAffairs/dept-report-non-compliance/exportById?id=${this.deptReportNonCompliance.id}`
    },
  },

  methods: {
    // changeAttachment(attachmentIds) {
    //   this.deptReportNonCompliance.attachmentRecords = attachmentIds
    // },
    changeAttachment(attachmentIds) {
      this.attachmentIds = attachmentIds
    },
    // async save(goBack) {
    async save() {
      this.loading = true
      // console.log('save 1...')
      // if (this.$refs.audit && !this.$refs.form.validate()) {
      //   return
      // }
      // if (!(this.$refs?.form?.validate() ?? true)) return

      if (!this.$refs.form.validate()) {
        return
      }
      if (this.isComplete) {
        this.$dialog.message.error(`已审批工作无法保存！`)
        return
      }
      // console.log('save 2...')
      const t = true
      if (t) {
        // console.log('save...')
        // console.log(this.businessParam)
        // 无业务伴随，正常提交
        if (!this.businessParam) {
          // if (
          //   !this.deptReportNonCompliance.id ||
          //   this.deptReportNonCompliance.id == 'new'
          // ) {
          // console.log('save createWithBusiness...1')
          const { data } = await this.postAsync(
            '/business/seaAffairs/dept-report-non-compliance/createWithBusiness',
            {
              ...this.deptReportNonCompliance,
              shipCode: this.deptReportNonCompliance.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              attachmentIds: this.attachmentIds,
            },
          )
          if (data) this.closeAndTo(this.backRouteName, {}, {})
        } else {
          // 有业务伴随，提交业务
          // console.log('save createWithBusiness...2')
          const { data } = await this.postAsync(
            '/business/seaAffairs/dept-report-non-compliance/createWithBusiness',
            // '/business/seaAffairs/dept-report-non-compliance/update',
            {
              ...this.deptReportNonCompliance,
              shipCode: this.deptReportNonCompliance.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              // ...this.businessParam,
              // isSyncProcess: false,
              attachmentIds: this.attachmentIds,
            },
          )
          if (!data) return
          // if (notMove) return data
          // console.log('this.businessParam.businessId')
          if (this.businessParam.businessId) {
            // console.log(this.businessParam.businessId)
            this.$store.commit('setReportId', {
              ...this.businessParam,
              reportId: data,
            })
            this.closeAndTo('accident-detail', {
              id: this.businessParam.businessId,
            })
          } else {
            // goBack(this.backRouteName)
            this.closeAndTo(this.backRouteName, {}, {})
          }
          if (data) {
            this.closeAndTo(this.backRouteName, {}, {})
          }
        }
      }
      // 审批流提交
      // const error = await this.$refs.audit?.submit()
      // if (error) {
      //   return
      // }
      // goBack(this.backRouteName)
      this.closeAndTo(this.backRouteName, {}, {})
      this.loading = false
    },
    //  async submit(goBack) {
    async submit() {
      this.loading = true
      // console.log('submit 1...')
      if (this.$refs.audit && !this.$refs.form.validate()) {
        return
      }
      if (!this.$refs.form.validate()) {
        return
      }
      // console.log('submit 2...')
      const t = true
      if (t) {
        // console.log('submit...')
        // 无业务伴随，正常提交
        // if (!this.businessParam) {
        if (
          !this.deptReportNonCompliance.id ||
          this.deptReportNonCompliance.id == 'new'
        ) {
          // console.log('save createAndSubmit...')
          const { data } = await this.postAsync(
            '/business/seaAffairs/dept-report-non-compliance/createAndSubmit',
            {
              ...this.deptReportNonCompliance,
              shipCode: this.deptReportNonCompliance.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              attachmentIds: this.attachmentIds,
            },
          )
          if (data) this.closeAndTo(this.backRouteName, {}, {})
        } else {
          // 有业务伴随，提交业务
          if (!this.deptReportNonCompliance.auditParams) {
            // console.log('save createWithBusiness...')
            const { data } = await this.postAsync(
              '/business/seaAffairs/dept-report-non-compliance/createAndSubmit',
              // '/business/seaAffairs/dept-report-non-compliance/update',
              {
                ...this.deptReportNonCompliance,
                shipCode: this.deptReportNonCompliance.shipInfoDO.shipCode,
                // formCode: this.reportInfo.formCode,
                // ...this.businessParam,
                // isSyncProcess: false,
                attachmentIds: this.attachmentIds,
              },
            )
            if (data) {
              this.closeAndTo(this.backRouteName, {}, {})
            }
          }
        }
      }
      // 有映射字段需填写,且当前状态为审批中，更新字段
      if (this.needFields.length !== 0 && this.status === '2') {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            processInstanceId:
              this.deptReportNonCompliance.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent: this.mapping[f.mappingCode],
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) {
          return
        }
      }
      // 审批流提交
      const error = await this.$refs.audit?.submit()
      if (error) {
        return
      }
      this.loading = true
      // goBack(this.backRouteName)
      this.closeAndTo(this.backRouteName, {}, {})
    },
    // async mapField() {
    //   let mappingDetails = []
    //   for (let f of this.needFields) {
    //     mappingDetails.push({
    //       processInstanceId:
    //         this.deptReportNonCompliance.auditParams.processInstanceId,
    //       mappingCode: f.mappingCode,
    //       mappingContent: this.mapping[f.mappingCode],
    //       mappingType: f.mappingType,
    //     })
    //   }
    //   let { errorRaw } = await this.postAsync(
    //     '/business/seaAffairs/templateMapping/saveMappingDetail',
    //     mappingDetails,
    //   )
    //   return errorRaw
    // },
    async loadDeptReportInfo() {
      // console.log(this.$route.params.id)
      // console.log(this.$route.params.businessType)
      // if (this.$route.params.businessType === 'securityCheck_Non')
      //   this.backRouteName = 'security-check-report-3'
      // else this.backRouteName = 'accident-report-list-new'
      // console.log(this.backRouteName)
      const { data } = await this.getAsync(
        // '/business/seaAffairs/deptReport/getReportDetailById',
        '/business/seaAffairs/dept-report-non-compliance/getReportDetailById',
        { reportId: this.$route.params.id },
      )
      // console.log('data 1')
      if (data.deptReportNonCompliance) {
        // this.deptReportNonCompliance = data.deptReportNonCompliance
        this.deptReportNonCompliance = {
          ...data.deptReportNonCompliance,
          // shipCode: data.deptReportNonCompliance.shipInfoDO.shipCode,
        }
        // console.log('data 12')
        if (data.deptReportNonCompliance.status)
          this.status = data.deptReportNonCompliance.status
        // console.log(data.deptReportNonCompliance.businessType)
        // if (data.deptReportNonCompliance.businessType === 'securityCheck')
        //   this.backRouteName = 'security-check-report-3'
        // else this.backRouteName = 'accident-report-list-new'
      }
      // console.log('data 2')

      if (data.auditParams) {
        this.auditParams = data.auditParams
        // this.deptReportNonCompliance.auditParams = data.auditParams
      }
      // console.log('data 3')
      // if (this.status === '2') this.subtitles = ['基本信息', '映射内容']
      // console.log('data 4')
      await this.loadNeedFields()
    },
    // async loadNeedFields() {
    //   const { data } = await this.getAsync(
    //     '/business/seaAffairs/templateMapping/getReportNeedField',
    //     { deptReportId: this.$route.params.id },
    //   )
    //   this.needFields = data || []
    //   // 初始化签名字段的用户id
    //   for (const t of this.needFields) {
    //     if (t.mappingType === '0') {
    //       this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
    //     } else {
    //       this.mapping[t.mappingCode] = '' || this.mappingRecords[t.field]
    //     }
    //   }
    // },
    async loadNeedFields() {
      console.log('1')
      console.log(this.deptReportNonCompliance)
      console.log(this.deptReportNonCompliance?.auditParams)
      console.log(this.deptReportNonCompliance?.auditParams?.processInstanceId)
      console.log('2')
      console.log(this.auditParams)
      console.log(this.deptReportNonCompliance?.auditParams)
      console.log(this.auditParams?.processInstanceId)
      if (!this.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        {
          processInstanceId: this.auditParams.processInstanceId,
        },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          if (t.mappingCode.indexOf('date') != -1)
            this.mapping[t.mappingCode] = new Date(
              Date.now() - new Date().getTimezoneOffset() * 60000,
            )
              .toISOString()
              .substr(0, 10)
          else
            this.mapping[t.mappingCode] =
              this.mappingRecords[t.mappingCode] || ''
        }
      }
    },
  },

  async mounted() {
    await this.loadDeptReportInfo()
  },
}
</script>

<style scoped></style>
