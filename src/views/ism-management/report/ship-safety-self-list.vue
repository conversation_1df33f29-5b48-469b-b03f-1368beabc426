<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :search-date="searchDate"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="shipParams"
      :push-params="pushParams"
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="shipParams.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-ship-select
            ref="select"
            v-model="shipParams.shipCode"
            :use-name="useName"
          ></v-ship-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :to="{ name: 'ship-safety-self-detail', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['船舶安全自查结果汇总:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status != 1"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['船舶安全自查结果汇总:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipInfo`]="{ item }">
        {{ item.shipInfo.chShipName }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'ship-safety-self-list',
  created() {
    this.tableName = '船舶安全自查结果汇总申请'
    this.reqUrl = '/business/seaAffairs/shipReport/ship-safety-self/page'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '所属船舶', value: 'shipInfo' },
      //{ text: '船舶编码', value: 'shipCode' },
      { text: '申请编号', value: 'applicationNo' },
      { text: '日期', value: 'time' },
      { text: '创建时间', value: 'createTime' },
      { text: '更新时间', value: 'updateTime' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '创建时间期间',
      interval: true,
    }
    this.pushParams = { name: 'ship-safety-self-detail' }
  },

  data() {
    return {
      selected: false,
      useName: false,
      shipParams: {
        shipCode: '',
        department: '',
      },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/shipReport/ship-safety-self/record/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
