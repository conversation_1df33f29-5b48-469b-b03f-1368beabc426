<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶安全检查表:编辑']"
      :title="`船舶安全检查表-${
        isEdit ? securityCheckReport.reportNumber : '新增'
      }`"
      :tooltip="isEdit ? securityCheckReport.reportNumber : '新增'"
      :backRouteName="backRouteName"
      :can-submit="
        (!securityCheckReport.auditParams ||
          securityCheckReport.auditParams.taskId) &&
        securityCheckReport.status == 2
      "
      :can-save="isSave"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
    >
      <template v-if="securityCheckReport.status == 3" v-slot:titlebtns>
        <v-btn
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn>
      </template>
      <template
        v-if="!!auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="form">
            <v-audit ref="audit" :auditParams="auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  label="船舶名称MV"
                  v-model="securityCheckReport.shipInfoDO.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <!--<v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="编号Report number"
                  v-model="securityCheckReport.reportNumber"
                  outlined
                  dense
                ></v-text-field>
              </v-col>-->
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="港口Port"
                  v-model="securityCheckReport.checkPort"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="检察人员Inspection Officer"
                  v-model="securityCheckReport.inspector"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="isEdit"
                  label="检查时间Date of Inspection"
                  v-model="securityCheckReport.inspectionTime"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="航次Voyage No."
                  v-model="securityCheckReport.voyage"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="船长Master"
                  v-model="securityCheckReport.captain"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="大副Chief Officer"
                  v-model="securityCheckReport.firstMate"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="轮机长Chief Engineer"
                  v-model="securityCheckReport.cheifEngineer"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  label="类  型 Type"
                  v-model="securityCheckReport.type"
                  outlined
                  dense
                ></v-text-field> 
                <v-select
                  :readonly="isEdit"
                  label="检查类别Types of Inspection"
                  v-model="securityCheckReport.type"
                  outlined
                  dense
                  :items="[
                    { text: 'PSC', value: '0' },
                    { text: 'FSC', value: '1' },
                  ]"
                ></v-select>-->
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="主要缺陷和建议Major deficiencies and suggestions"
                  rows="3"
                  dense
                  v-model="securityCheckReport.deficiencySuggestion"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="纠正期限Correction deadline"
                  rows="3"
                  dense
                  v-model="securityCheckReport.correctionDeadline"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="验证意见Verification comments"
                  rows="3"
                  dense
                  v-model="securityCheckReport.verificationComment"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="综合结论Overall Conclusion"
                  rows="3"
                  dense
                  v-model="securityCheckReport.overallConclusion"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
          <v-row>
            <v-col cols="12">
              <v-attach-list
                :attachments="securityCheckReport.attachmentRecords"
                :readonly="isEdit"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-container>
        <!-- <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-attach-list
                :disabled="securityCheckReport.status === '3'"
                :attachments="securityCheckReport.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-card-text> -->
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'dept-report-security-check',
  mixins: [routerControl],
  components: {},
  created() {
    this.backRouteName = 'security-check-report-1new'
  },
  data() {
    return {
      subtitles: ['基本信息'],
      // securityCheckReport: {
      //   // shipCode: '',
      //   reportNumber: '',
      //   managerMaster: '',
      //   createDate: '',
      //   createTime: '',
      //   inchargeMaster: '',
      //   type: '',
      //   reporter: '',
      //   shipInfoDO: { shipCode: '' },
      //   reportDate: '',
      //   reportDescription: '',
      //   reportCauses: '',
      //   attachmentRecords: [],
      // },
      securityCheckReport: {
        attachmentRecords: [],
        businessId: '',
        businessStatus: '',
        createDate: '',
        createTime: '',
        delFlag: false,
        id: '',
        checkPort: '',
        inspectionTime: '',
        inspector: '',
        captain: '',
        cheifEngineer: '',
        firstMate: '',
        voyage: '',
        deficiencySuggestion: '',
        processInstanceId: '',
        correctionDeadline: '',
        postTime: '',
        overallConclusion: '',
        verificationComment: '',
        // reporter: '',
        poster: this.$local.data.get('userInfo').id,
        shipInfoDO: {
          // chShipName: '',
          // enShipName: '',
          // id: '',
          shipCode: '',
        },
        status: null,
        type: '',
        updateTime: '',
      },
      auditParams: {},
      needFields: [],
      mapping: {},
      // pdfFile: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      status: '0',
      loading: false,
      sloading: false,
    }
  },

  watch: {
    '$store.state.reportParams.businessParams': {
      handler: function (val) {
        if (this.isEdit) return
        const businessParam = val.find(
          (item) =>
            item.businessType === 'securityCheck_Report' &&
            item.businessId === this.$route.params.businessId1,
        )
        if (!!businessParam && businessParam.businessId) {
          console.log(
            businessParam.businessType + ' ' + businessParam.businessId,
          )
          console.log(businessParam)
          this.backRouteName = 'security-check-detail'

          this.businessParam = businessParam
          // this.detail.type = businessParam.otherParams.type
          this.securityCheckReport.businessId = businessParam.businessId
          this.securityCheckReport.shipInfoDO.shipCode =
            businessParam.otherParams.shipCode
          this.securityCheckReport.shipInfoDO.shipCode =
            businessParam.otherParams.shipCode
          this.securityCheckReport.shipInfoDO.shipCode =
            businessParam.otherParams.shipCode
          this.securityCheckReport.checkPort =
            businessParam.otherParams.checkPort
          this.securityCheckReport.inspectionTime =
            businessParam.otherParams.inspectionTime
          this.securityCheckReport.inspector =
            businessParam.otherParams.inspector
          this.securityCheckReport.captain = businessParam.otherParams.captain
          this.securityCheckReport.cheifEngineer =
            businessParam.otherParams.cheifEngineer
          this.securityCheckReport.firstMate =
            businessParam.otherParams.firstMate
          // this.securityCheckReport.captain = businessParam.otherParams.captain
          // this.securityCheckReport.type =
          //   businessParam.otherParams.type == '3' ? '1' : '0'
          // this.detail.seaJson = { ...businessParam.otherParams }
          // this.detail.shipJson = { ...businessParam.otherParams }
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id == 'new'
        ? false
        : this.securityCheckReport.status == null ||
          this.securityCheckReport.status == '' ||
          this.securityCheckReport.status == '1' ||
          this.securityCheckReport.status == '4'
        ? false
        : true
    },
    isSave() {
      return this.$route.params.id == 'new'
        ? true
        : this.securityCheckReport.status == '0' ||
          this.securityCheckReport.status == '1' ||
          this.securityCheckReport.status == '' ||
          this.securityCheckReport.status == null
        ? true
        : false
    },
    canSubmit() {
      return (
        !this.securityCheckReport.auditParams ||
        !!this.securityCheckReport.auditParams?.isReject
      )
    },
    isComplete() {
      let hideBtn =
        !!this.securityCheckReport.status &&
        this.securityCheckReport.status == 3
          ? true
          : false
      return hideBtn
    },
  },

  methods: {
    // changeAttachment(attachmentIds) {
    //   this.securityCheckReport.attachmentRecords = attachmentIds
    // },
    changeAttachment(attachmentIds) {
      this.attachmentIds = attachmentIds
    },
    async save(goBack) {
      this.loading = true
      console.log('save 1...')
      // if (!!this.businessParam && this.businessParam.businessId) {
      //   this.backRouteName = 'security-check-detail'
      // }
      console.log('this.backRouteName...' + this.backRouteName)
      // if (this.$refs.audit && !this.$refs.form.validate()) {
      //   return
      // }
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.isComplete) {
        this.$dialog.message.error(`已审批工作无法保存！`)
        return
      }
      console.log('save 2...')
      const t = true
      if (t) {
        console.log('save...')
        // 无业务伴随，正常提交
        // if (!this.businessParam) {
        if (
          !this.securityCheckReport.id ||
          this.securityCheckReport.id == 'new'
        ) {
          console.log('save createWithBusiness...1')
          const { data } = await this.postAsync(
            '/business/seaAffairs/SecurityCheckReport/saveOrUpdateRecord',
            // '/business/seaAffairs/dept-report-non-compliance/createWithBusiness',
            {
              ...this.securityCheckReport,
              shipCode: this.securityCheckReport.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              attachmentIds: this.attachmentIds,
            },
          )
          // if (data) this.closeAndTo(this.backRouteName, {}, {})
          if (data) {
            this.$store.commit('setReportId', {
              ...this.businessParam,
              reportId: data,
            })
            this.closeAndTo(this.backRouteName, {}, {})
          }
        } else {
          // 有业务伴随，提交业务
          console.log('save createWithBusiness...2')
          const { data } = await this.postAsync(
            '/business/seaAffairs/SecurityCheckReport/saveOrUpdateRecord',
            // '/business/seaAffairs/dept-report-non-compliance/update',
            {
              ...this.securityCheckReport,
              shipCode: this.securityCheckReport.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              // ...this.businessParam,
              // isSyncProcess: false,
              attachmentIds: this.attachmentIds,
            },
          )
          if (data) {
            this.$store.commit('setReportId', {
              ...this.businessParam,
              reportId: data,
            })
            this.closeAndTo(this.backRouteName, {}, {})
          }
        }
      }
      // 审批流提交
      // const error = await this.$refs.audit?.submit()
      // if (error) {
      //   return
      // }
      goBack()
      this.loading = false
    },
    async submit(goBack) {
      this.loading = true
      console.log('submit 1...')
      if (this.$refs.audit && !this.$refs.form.validate()) {
        return
      }
      if (!this.$refs.form.validate()) {
        return
      }
      console.log('submit 2...')
      const t = true
      if (t) {
        console.log('submit...')
        // 无业务伴随，正常提交
        // if (!this.businessParam) {
        if (
          !this.securityCheckReport.id ||
          this.securityCheckReport.id == 'new'
        ) {
          console.log('save createAndSubmit...')
          const { data } = await this.postAsync(
            '/business/seaAffairs/SecurityCheckReport/submitById',
            { id: this.$route.params.id + '' },
            // {
            //   ...this.securityCheckReport,
            //   shipCode: this.securityCheckReport.shipInfoDO.shipCode,
            //   // formCode: this.reportInfo.formCode,
            //   attachmentIds: this.attachmentIds,
            // },
          )
          if (data) this.closeAndTo(this.backRouteName, {}, {})
        } else {
          // 有业务伴随，提交业务
          if (!this.securityCheckReport.auditParams) {
            console.log('save createWithBusiness...')
            const { data } = await this.getAsync(
              '/business/seaAffairs/SecurityCheckReport/submitById',
              // '/business/seaAffairs/dept-report-non-compliance/update',
              { id: this.$route.params.id + '' },
              // {
              //   ...this.securityCheckReport,
              //   shipCode: this.securityCheckReport.shipInfoDO.shipCode,
              //   // formCode: this.reportInfo.formCode,
              //   // ...this.businessParam,
              //   // isSyncProcess: false,
              //   attachmentIds: this.attachmentIds,
              // },
            )
            if (data) {
              this.closeAndTo(this.backRouteName, {}, {})
            }
          }
        }
      }
      // 有映射字段需填写,且当前状态为审批中，更新字段
      if (this.needFields.length !== 0 && this.status === '2') {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            // deptReportId: this.securityCheckReport.id,
            processInstanceId:
              this.securityCheckReport.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent: this.mapping[f.mappingCode],
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) {
          return
        }
      }
      // 审批流提交
      const error = await this.$refs.audit?.submit()
      if (error) {
        return
      }
      this.loading = true
      goBack()
    },
    async loadDeptReportInfo() {
      console.log(this.$route.params.id)
      const { data } = await this.getAsync(
        // '/business/seaAffairs/deptReport/getReportDetailById',
        '/business/seaAffairs/SecurityCheckReport/getDetailById',
        { id: this.$route.params.id },
      )
      console.log('data 1')
      if (data) {
        console.log(data)
        // this.securityCheckReport = data.securityCheckReport
        this.securityCheckReport = data
        this.securityCheckReport.shipInfoDO = data.shipInfo
        // {
        //   ...data,
        //   // shipCode: data.securityCheckReport.shipInfoDO.shipCode,
        // }
        console.log('data 12')
        if (data.status) this.status = data.status
      }
      console.log(this.securityCheckReport)

      console.log('data 2')

      if (data && data.auditParams) {
        this.auditParams = data.auditParams
      }
      console.log('data 3')
      if (this.status === '2') this.subtitles = ['基本信息']
      console.log('data 4')
    },
    async loadNeedFields() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedField',
        { deptReportId: this.$route.params.id },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          this.mapping[t.mappingCode] = '' || this.mappingRecords[t.field]
        }
      }
    },
  },

  async mounted() {
    await this.loadDeptReportInfo()
    // await this.loadNeedFields()
  },
}
</script>

<style scoped></style>
