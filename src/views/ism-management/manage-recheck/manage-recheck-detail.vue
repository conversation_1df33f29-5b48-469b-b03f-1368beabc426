<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['管理复查报表:编辑']"
      title="管理复查报告"
      :tooltip="isEdit ? detail.id : '新增'"
      :backRouteName="backRouteName"
      :can-submit="
        !detail.auditParams ||
        detail.auditParams.taskId ||
        detail.status === 1 ||
        detail.status === 4
      "
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['管理复查报表:下载部门报表']"
        >
          下载部门报表
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-card-text>
        </v-form>
      </template>
      <template #基本信息>
        <v-container fluid>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.formDate"
                  required
                  dense
                  use-today
                  label="填报时间"
                  readonly
                  outlined
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-if="isEdit"
                  v-model="detail.handlerName"
                  outlined
                  label="处理人"
                  dense
                  readonly
                ></v-text-field>
                <v-handler
                  v-else
                  v-model="detail.handler"
                  use-current
                  readonly
                  outlined
                  :rules="[rules.required]"
                ></v-handler>
              </v-col>
            </v-row>
            <v-row>
              <v-col
                class="py-0"
                v-for="h in 短文本"
                :key="h.value"
                cols="12"
                md="6"
              >
                <vs-date-picker
                  v-if="h.type == 'date'"
                  v-model="detail.dataJson[h.value]"
                  :label="h.text"
                  outlined
                  dense
                ></vs-date-picker>
                <v-text-field
                  v-else
                  v-model="detail.dataJson[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col class="py-0" v-for="h in 长文本" :key="h.value" cols="12">
                <v-textarea
                  v-model="detail.dataJson[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
          <v-card-text>
            <v-attach-list
              :attachments="detail.attachmentRecords"
              @change="(ids) => (detail.attachmentIds = ids)"
              :ship-code="detail.shipCode"
            ></v-attach-list>
          </v-card-text>
        </v-container>
      </template>
      <template v-slot:映射内容>
        <v-card-text>
          <v-row>
            <v-col v-for="t in contents" :key="t.id" cols="12" md="4">
              <v-text-field
                v-model="mapping[t.mappingCode]"
                :label="t.field"
              ></v-text-field>
            </v-col>
            <v-col v-if="!!signNatures.length" cols="12">
              <div>
                当前节点存在电子签名，审批结果为通过，包含：
                <b v-for="t in signNatures" :key="t.id">
                  {{ t.field }}
                </b>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
// conclusionOfReview	复查结论	string
// participants	复查会议人员	string
// purposeOfReview	复查目的	string
// reviewContents	复查内容	string
// scopeOfReview	复查范围	string
// shipName	船舶名称	string
// time	时间	string
export default {
  name: 'manage-recheck-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'manage-recheck-list'
    this.短文本 = [
      { text: '复查会议人员', value: 'participants' },
      { text: '时间', value: 'time', type: 'date' },
    ]
    this.长文本 = [
      { text: '复查目的', value: 'purposeOfReview' },
      { text: '复查范围', value: 'scopeOfReview' },
      { text: '复查内容', value: 'reviewContents' },
      { text: '复查结论', value: 'conclusionOfReview' },
    ]
  },
  data() {
    return {
      detail: { dataJson: {} },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      businessParam: {},
      subtitles: ['基本信息', '映射内容'],
      needFields: [],
      mapping: {},
      mappingRecords: {},
    }
  },

  watch: {
    'detail.shipCode'(value) {
      //判断是否选择其他类型，选择其他类型可填写其他类型
      //此监听只在打开页面时调用一次，后续修改不触发
      this.$nextTick(() => {
        console.log('shipCode')
      })
      console.log('value ' + value)
      this.detail.dataJson['shipName'] = value
      console.log('dataJson shipName value ' + this.detail.dataJson['shipName'])
      // if (value.indexOf('12') != -1) {
      //   console.log('value1 ' + value)
      //   // this.isOther = false
      // } else {
      //   // this.isOther = true
      //   this.detail.natureAccidentOther = ''
      // }
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    signNatures() {
      return this.needFields.filter((i) => i.mappingType === '0')
    },
    contents() {
      return this.needFields.filter((i) => i.mappingType === '1')
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/ismAffairs/ismManageRecheck/exportById?id=${this.detail.id}`
        : ''
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return
      const { data } = await this.postAsync(
        '/business/ismAffairs/ismManageRecheck/saveOrUpdateRecord',
        this.detail,
      )
      if (!data) return
      if (notMove) return data
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/ismAffairs/ismManageRecheck/submitById',
          { id: data },
        )
        if (!errorRaw) {
          goBack()
        }
      } else {
        await this.mapField()
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadNeedFields() {
      if (!this.detail?.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        { processInstanceId: this.detail.auditParams.processInstanceId },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          if (t.mappingCode.indexOf('date') != -1)
            this.mapping[t.mappingCode] = new Date(
              Date.now() - new Date().getTimezoneOffset() * 60000,
            )
              .toISOString()
              .substr(0, 10)
          else
            this.mapping[t.mappingCode] =
              this.mappingRecords[t.mappingCode] || ''
        }
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/ismAffairs/ismManageRecheck/getDetailById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
      this.mappingRecords = data.mappingRecords
    },

    async mapField() {
      let mappingDetails = []
      for (let f of this.needFields) {
        mappingDetails.push({
          processInstanceId: this.detail.auditParams.processInstanceId,
          mappingCode: f.mappingCode,
          mappingContent: this.mapping[f.mappingCode],
          mappingType: f.mappingType,
        })
      }
      let { errorRaw } = await this.postAsync(
        '/business/seaAffairs/templateMapping/saveMappingDetail',
        mappingDetails,
      )
      return errorRaw
    },
  },

  async mounted() {
    await this.loadDetail()
    await this.loadNeedFields()
  },
}
</script>

<style></style>
