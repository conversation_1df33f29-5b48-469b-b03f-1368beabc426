<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      use-ship
      use-status
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.history"
            label="历史数据"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          outlined
          color="info"
          :loading="loading1"
          class="mx-1"
          @click="updateExportForm"
          v-permission="['管理复查报表:更新部门报表']"
        >
          <v-icon left>mdi-wrench</v-icon>
          更新部门报表
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'manage-recheck-detail', params: { id: 'new' } }"
          v-permission="['管理复查报表:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['管理复查报表:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'manage-recheck-list',
  created() {
    this.tableName = '管理复查报告'
    this.reqUrl = '/business/ismAffairs/ismManageRecheck/recordPage'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船名', value: 'shipInfo' },
      // { text: '报表类型', value: 'type' },
      { text: '填报时间', value: 'formDate' },
      { text: '处理人', value: 'handlerName' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '填表日期',
      value: 'formDate',
    }
    this.pushParams = { name: 'manage-recheck-detail' }
  },

  data() {
    return {
      selected: false,
      loading1: false,
      searchObj: {
        history: false,
      },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/ismAffairs/ismManageRecheck/deleteById',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async updateExportForm() {
      if (this.selected.status !== '3') {
        this.$dialog.message.error(`只可选择已审批的记录`)
        return
      }
      this.loading1 = true
      const { errorRaw } = await this.getAsync(
        '/business/ismAffairs/ismManageRecheck/updateExportForm',
        {
          id: this.selected.id,
        },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
