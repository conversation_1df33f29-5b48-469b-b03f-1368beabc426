<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['事故简况报表:编辑']"
      :title="`事故简况报表`"
      :tooltip="isEdit ? detail.type : '新增'"
      :backRouteName="backRouteName"
      :can-submit="canSubmit"
      :can-save="isSave"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['事故简况报表:下载部门报表']"
        >
          下载部门报表
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-card-text>
        </v-form>
      </template>
      <template #基本信息>
        <v-container fluid>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  label="类型"
                  v-model="detail.type"
                  required
                  dense
                  outlined
                  :rules="[rules.required]"
                  :items="['机损', '海损']"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  label="部门"
                  v-model="detail.dept"
                  required
                  dense
                  outlined
                  :rules="[rules.required]"
                  :items="accidentTypes"
                  readonly
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.formDate"
                  required
                  dense
                  use-today
                  label="填报时间"
                  readonly
                  outlined
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-if="isEdit"
                  v-model="detail.handlerNickName"
                  outlined
                  label="经办人"
                  dense
                  readonly
                ></v-text-field>
                <v-handler
                  v-else
                  v-model="detail.handler"
                  use-current
                  readonly
                  outlined
                  :rules="[rules.required]"
                ></v-handler>
              </v-col>
            </v-row>
            <v-row v-if="detail.type === '机损'">
              <v-col
                class="py-0"
                v-for="h in 机损短文本"
                :key="h.value"
                cols="12"
                md="3"
              >
                <vs-date-picker
                  v-if="h.type == 'date'"
                  v-model="detail.shipJson[h.value]"
                  :label="h.text"
                  outlined
                  dense
                ></vs-date-picker>
                <v-text-field
                  v-else
                  v-model="detail.shipJson[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col
                class="py-0"
                v-for="h in 机损长文本"
                :key="h.value"
                cols="12"
              >
                <v-textarea
                  v-model="detail.shipJson[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-textarea>
              </v-col>
            </v-row>
            <v-row v-else>
              <v-col
                class="py-0"
                v-for="h in 海损短文本"
                :key="h.value"
                cols="12"
                md="3"
              >
                <vs-date-picker
                  v-if="h.type == 'date'"
                  v-model="detail.seaJson[h.value]"
                  :label="h.text"
                  outlined
                  dense
                ></vs-date-picker>
                <v-text-field
                  v-else
                  v-model="detail.seaJson[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col
                class="py-0"
                v-for="h in 海损长文本"
                :key="h.value"
                cols="12"
              >
                <v-textarea
                  v-model="detail.seaJson[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
          <v-card-text>
            <v-attach-list
              :attachments="detail.attachmentRecords"
              @change="(ids) => (detail.attachmentIds = ids)"
              :ship-code="detail.shipCode"
            ></v-attach-list>
          </v-card-text>
        </v-container>
      </template>
      <template v-slot:映射内容>
        <v-card-text>
          <v-row>
            <v-col v-for="t in contents" :key="t.id" cols="12" md="4">
              <v-text-field
                v-model="mapping[t.mappingCode]"
                :label="t.field"
              ></v-text-field>
            </v-col>
            <v-col v-if="!!signNatures.length" cols="12">
              <div>
                当前节点存在电子签名，审批结果为通过，包含：
                <b v-for="t in signNatures" :key="t.id">
                  {{ t.field }}
                </b>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
// analysisOfCause	原因分析		false
// string
// cargo	载货		false
// string
// casualty	伤亡情况		false
// string
// conditionOfOpposite	对方船舶情况		false
// string
// description	事故简况		false
// string
// insurer	保险人		false
// string
// location	地点		false
// string
// portOfDeparture	出发港口		false
// string
// portOfDestination	目的港口		false
// string
// preventionMeasure	预防措施		false
// string
// seaCondition	气象海况		false
// string
// shipFlag	船旗		false
// string
// shipName	船舶名称		false
// string
// takenMeasure	已采取的措施		false
// string
// time	时间		false
// string

// accidentType	事故种类		false
// string
// analysisOfCause	原因分析		false
// string
// cost	费用		false
// string
// description	事故简况		false
// string
// location	地点		false
// string
// measure	预防或措施		false
// string
// operTime	营运时间		false
// string
// scopeOfDamage	受损范围		false
// string
// shipName	船舶名称		false
// string
// time	时间		false
// string
export default {
  name: 'accident-report-detail-new',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'accident-report2-list-new'
    this.海损短文本 = [
      //   { text: '船舶名称', value: 'shipName',type:'dias' },
      { text: '船旗', value: 'shipFlag' },
      { text: '时间', value: 'time', type: 'date' },
      { text: '保险人', value: 'insurer' },
      { text: '地点', value: 'location' },
      { text: '出发港口', value: 'portOfDeparture' },
      { text: '目的港口', value: 'portOfDestination' },
    ]
    this.海损长文本 = [
      { text: '原因分析', value: 'analysisOfCause' },
      { text: '伤亡情况', value: 'casualty' },
      { text: '对方船舶情况', value: 'conditionOfOpposite' },
      { text: '事故简况', value: 'description' },
      { text: '预防措施', value: 'preventionMeasure' },
      { text: '气象海况', value: 'seaCondition' },
      { text: '已采取的措施', value: 'takenMeasure' },
    ]
    this.机损短文本 = [
      { text: '事故种类', value: 'accidentType' },
      { text: '时间', value: 'time', type: 'date' },
      { text: '营运时间', value: 'operTime', type: 'date' },
      { text: '地点', value: 'location' },
      { text: '费用', value: 'cost' },
    ]
    this.机损长文本 = [
      { text: '原因分析', value: 'analysisOfCause' },
      { text: '受损范围', value: 'scopeOfDamage' },
      { text: '预防或措施', value: 'measure' },
      { text: '事故简况', value: 'description' },
    ]
    this.accidentTypes = [
      { text: '海务', value: '海务' },
      { text: '机务', value: '机务' },
      { text: '通导', value: '通导' },
      { text: 'i办', value: 'i办' },
      { text: '船员', value: '船员' },
    ]
  },
  data() {
    return {
      detail: { seaJson: {}, shipJson: {} },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      businessParam: {},
      subtitles: ['基本信息', '映射内容'],
      needFields: [],
      mapping: {},
      mappingRecords: {},
    }
  },

  watch: {
    '$store.state.reportParams.businessParams': {
      handler: function (val) {
        if (this.isEdit) return
        const businessParam = val.find(
          (item) =>
            item.businessType === 'shipAccident' &&
            item.businessId === this.$route.params.businessId1, //验证当前最新传入的业务id，多业务id时精准匹配验证,
        )
        if (businessParam.businessId) {
          this.businessParam = businessParam
          this.detail.type = businessParam.otherParams.type
          this.detail.businessId = businessParam.businessId
          this.detail.shipCode = businessParam.otherParams.shipCode
          this.detail.dept = businessParam.otherParams.accidentType
          this.detail.seaJson = { ...businessParam.otherParams }
          this.detail.shipJson = { ...businessParam.otherParams }
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    isSave() {
      // console.log(this.$route.params.id)
      // console.log(this.deptReportInspection)
      // console.log(this.deptReportInspection.status)
      return this.$route.params.id == 'new'
        ? true
        : this.detail.status == '0' ||
          this.detail.status == '1' ||
          this.detail.status == '' ||
          this.detail.status == null
        ? true
        : false
    },
    canSubmit() {
      // return (
      //   (!this.detail.auditParams || !!this.detail.auditParams?.isReject) &&
      //   this.detail.status == 2
      // )
      return (
        (!this.detail.auditParams || this.detail.auditParams.taskId) &&
        this.detail.status == 2
      )
    },
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    signNatures() {
      return this.needFields.filter((i) => i.mappingType === '0')
    },
    contents() {
      return this.needFields.filter((i) => i.mappingType === '1')
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/ismAffairs/ismAccidentBrief/exportById?id=${this.detail.id}`
        : ''
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return
      const { data } = await this.postAsync(
        '/business/ismAffairs/ismAccidentBrief/saveOrUpdateRecord',
        this.detail,
      )
      if (!data) return
      if (notMove) return data
      if (this.businessParam.businessId) {
        this.$store.commit('setReportId', {
          ...this.businessParam,
          reportId: data,
        })
        this.closeAndTo('accident-detail', {
          id: this.businessParam.businessId,
        })
      } else {
        goBack()
      }
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/ismAffairs/ismAccidentBrief/submitById',
          { id: data },
        )
        if (!errorRaw) {
          if (this.businessParam.businessId) {
            this.$store.commit('setReportId', {
              ...this.businessParam,
              reportId: data,
            })
            this.closeAndTo('accident-detail', {
              id: this.businessParam.businessId,
            })
          } else {
            goBack()
          }
        }
      } else {
        await this.mapField()
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadNeedFields() {
      if (!this.detail?.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        { processInstanceId: this.detail.auditParams.processInstanceId },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          this.mapping[t.mappingCode] = this.mappingRecords[t.mappingCode] || ''
        }
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/ismAffairs/ismAccidentBrief/getDetailById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
      this.mappingRecords = data.mappingRecords
    },

    async mapField() {
      let mappingDetails = []
      for (let f of this.needFields) {
        mappingDetails.push({
          processInstanceId: this.detail.auditParams.processInstanceId,
          mappingCode: f.mappingCode,
          mappingContent: this.mapping[f.mappingCode],
          mappingType: f.mappingType,
        })
      }
      let { errorRaw } = await this.postAsync(
        '/business/seaAffairs/templateMapping/saveMappingDetail',
        mappingDetails,
      )
      return errorRaw
    },
  },

  async mounted() {
    await this.loadDetail()
    await this.loadNeedFields()
  },
}
</script>

<style></style>
