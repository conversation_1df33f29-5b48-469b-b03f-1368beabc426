<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['事故与险情记录:编辑']"
      :title="`船舶事故详情-${
        ['', '未提交', '审批中', '审批通过', '驳回'][detail.status]
      }`"
      tooltip="船舶事故"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-save="isSave"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['事故与险情记录:下载部门报表']"
        >
          下载部门报表
        </v-btn>
        <v-btn
          width="180"
          tile
          :href="downloadUrl1"
          color="info"
          small
          class="mx-1"
          v-permission="['事故与险情记录:下载部门报表(案件进展更新版)']"
        >
          部门报表(案件进展更新版)
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipBaseMixOutputDTO.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:船舶基本信息>
        <v-container fluid>
          <v-row>
            <v-col cols="2">
              中文船名:{{ detail.shipBaseMixOutputDTO.chShipName }}
            </v-col>
            <v-col cols="2">
              总吨 t:{{ detail.shipBaseMixOutputDTO.grossTonnage }}
            </v-col>
            <v-col cols="2">
              净吨 t:{{ detail.shipBaseMixOutputDTO.netTonnage }}
            </v-col>
            <v-col cols="2">
              建造日期:{{ detail.shipBaseMixOutputDTO.keelLaidDate }}
            </v-col>
            <v-col cols="2">
              型宽 m:{{ detail.shipBaseMixOutputDTO.breadth }}
            </v-col>
            <v-col cols="2">总长 m:{{ detail.shipBaseMixOutputDTO.loa }}</v-col>
            <v-col cols="2">
              船籍港:{{ detail.shipBaseMixOutputDTO.flagPort }}
            </v-col>
          </v-row>
        </v-container>
      </template>
      <template v-slot:事故相关数据>
        <v-form
          :readonly="detail.status === '3' || detail.status === '2'"
          ref="form"
        >
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                md="3"
                v-for="(h, i) in 事故相关数据字段"
                :key="i"
                class="py-0"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :rules="h.value === 'accidentNumber' ? [] : [rules.required]"
                  :label="h.label"
                  :readonly="h.value === 'accidentNumber'"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-if="h.type === 'number'"
                  v-model="detail[h.value]"
                  :rules="[rules.required]"
                  :label="h.label"
                  dense
                  :readonly="detail.status === '3'"
                  outlined
                  type="number"
                ></v-text-field>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                  :label="h.label"
                ></vs-date-picker>
                <v-date-time-picker
                  v-else-if="h.type === 'time'"
                  v-model="detail[h.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                  :label="h.label"
                ></v-date-time-picker>
              </v-col>
            </v-row>
            <v-row>
              <v-col
                class="py-0"
                cols="12"
                md="2"
                v-for="(h, i) in 船舶人员字段"
                :key="i"
              >
                <v-text-field
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template v-slot:事故损失及性质>
        <v-form ref="bform">
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-for="(h, i) in 事故损失及性质字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  dense
                  :readonly="detail.status === '3'"
                  outlined
                  :label="h.label"
                ></v-text-field>
                <v-text-field
                  v-if="h.type === 'number'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  :readonly="detail.status === '3'"
                  outlined
                  type="number"
                ></v-text-field>
                <v-radio-group
                  class="mt-0 pt-0"
                  v-if="h.type === 'boolean'"
                  :label="h.label"
                  v-model="detail[h.value]"
                  :readonly="detail.status === '3'"
                  row
                  dense
                >
                  <v-radio label="是" :value="true"></v-radio>
                  <v-radio label="否" :value="false"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-select
                  v-model="detail.shipwreckOrTotalLoss"
                  label="沉船或全损"
                  :items="['沉船', '全损', '无']"
                  :readonly="detail.status === '3'"
                  dense
                  outlined
                ></v-select>
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-select
                  v-model="detail.levelAccident"
                  outlined
                  :readonly="detail.status === '3'"
                  label="事故等级"
                  :items="[
                    '微小事故',
                    '小事故',
                    '一般事故',
                    '较大事故',
                    '重大事故',
                    '特大事故',
                  ]"
                  dense
                ></v-select>
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-dict-select
                  v-if="update"
                  dict-type="accident_nature_type"
                  label="事故类型"
                  outlined
                  collapse-tags
                  :readonly="detail.status === '3'"
                  :rules="[rules.required]"
                  multiple
                  v-model="detail.natureAccident"
                  @change="(val) => $emit('input', String(val))"
                  dense
                ></v-dict-select>
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-text-field
                  v-model="detail.natureAccidentOther"
                  dense
                  :readonly="detail.status === '3' || isOther"
                  outlined
                  label="其他事故类型（请填写）"
                ></v-text-field>
                <!-- <v-checkbox
                  class="mt-0"
                  dict-type="accident_nature_type"
                  v-model="detail.natureAccident"
                  label="人员伤亡"
                  value="1"
                ></v-checkbox>
                <v-checkbox
                  class="mt-0"
                  dict-type="accident_nature_type"
                  v-model="detail.natureAccident"
                  label="污染"
                  value="2"
                ></v-checkbox>
                <v-checkbox
                  class="mt-0"
                  dict-type="accident_nature_type"
                  v-model="detail.natureAccident"
                  label="船舶碰撞"
                  value="3"
                ></v-checkbox> -->
              </v-col>
              <v-col class="py-0" md="3" cols="12">
                <v-select
                  label="岸基责任人"
                  outlined
                  v-model="detail.accidentType"
                  :items="accidentTypes"
                  dense
                  :readonly="detail.status === '3'"
                ></v-select>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <template v-for="(h, i) in 长文本字段">
                <v-col
                  :key="h.label"
                  class="text-right body-2 caption"
                  cols="1"
                >
                  {{ h.label }}
                </v-col>
                <v-col :key="i" cols="11">
                  <v-textarea
                    v-if="h.value != 'caseProgress'"
                    row-height="19"
                    class="ml-1"
                    v-model="detail[h.value]"
                    dense
                    rows="1"
                    filled
                    :readonly="detail.status === '3'"
                    auto-grow
                  ></v-textarea>
                  <v-textarea
                    row-height="19"
                    v-if="h.value === 'caseProgress'"
                    class="ml-1"
                    v-model="detail[h.value]"
                    dense
                    rows="1"
                    filled
                    :readonly="detail.isClosed"
                    auto-grow
                  ></v-textarea>
                </v-col>
              </template>
            </v-row>
            <v-row>
              <v-col cols="12">
                <v-attach-list
                  title="关联证据文件"
                  :disabled="detail.status === '3'"
                  :attachments="attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="detail.shipCode"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template v-if="detail.status !== '3'" #人员伤亡情况按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCas"
          v-permission="['事故与险情人员伤亡:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedCas"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateCas"
          v-permission="['事故与险情人员伤亡:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <!-- <v-btn
          :disabled="!selectedCas"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCas"
          v-permission="['人员伤亡:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template v-slot:人员伤亡情况>
        <v-table-list
          v-model="selectedCas"
          :headers="casHeaders"
          :items="casualties"
        ></v-table-list>
      </template>
      <template v-slot:船舶报表>
        <v-table-list :headers="reportHeaders" :items="reports">
          <template v-slot:[`item.reportName`]="{ item }">
            <router-link
              :to="{
                name:
                  item.type === 'a'
                    ? 'accident-report-detail-new'
                    : item.type === 'n'
                    ? 'dept-report-info-detail2-new'
                    : 'casualties-report-detail-new',
                params: { id: item.id },
              }"
            >
              {{ item.reportName }}
            </router-link>
          </template>
        </v-table-list>
      </template>
      <template v-slot:船舶报表按钮>
        <!-- <v-btn
          :disabled="
            (!!accidentReportId && accidentReport.status !== '4') ||
            !detail.accidentType
          "
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="addAccidentReport"
          v-permission="['事故与险情人员伤亡:海损机损事故报告']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          海损机损事故报告
        </v-btn>
        <v-btn
          :disabled="!!casualtiesReportId && casualtiesReport.status !== '4'"
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="addCasualtiesReport"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          人员伤亡报告
        </v-btn> -->
        <!-- TODO:未完成 -->
        <!-- <v-btn
          :disabled="
            !!nonstandartReportId && accidentReport.status !== '已驳回'
          "
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="addNonstandartReport"
          v-permission="['人员伤亡:不符合报告']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          不符合报告
        </v-btn> 
        <v-btn
          :disabled="
            !!nonstandartReportId && accidentReport.status !== '已驳回'
          "
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="addNonstandartReport"
          v-permission="['事故与险情记录:不符合报告']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          不符合报告
        </v-btn>-->
      </template>
      <template v-if="!detail.isClosed" #保险理赔项目按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createIns"
          v-permission="['事故与险情保险理赔项目:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedIns"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateIns"
          v-permission="['事故与险情保险理赔项目:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <!-- <v-btn
          :disabled="!selectedCas"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCas"
          v-permission="['保险理赔项目:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template #保险理赔项目>
        <v-table-list
          v-model="selectedIns"
          :headers="insHeaders"
          :items="insurances"
        ></v-table-list>
      </template>
      <template v-if="!detail.isClosed" #案件进展按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createIns1"
          v-permission="['事故与险情保险理赔项目:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedIns1"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateIns1"
          v-permission="['事故与险情保险理赔项目:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <!-- <v-btn
          :disabled="!selectedCas"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCas"
          v-permission="['保险理赔项目:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template #案件进展>
        <v-table-list
          v-model="selectedIns1"
          :headers="insHeaders1"
          :items="insurances"
        ></v-table-list>
      </template>
      <template v-if="!detail.isClosed" #事实陈述按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createFactStatement"
          v-permission="['事故与险情事实陈述:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedFactStatement"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateFactStatement"
          v-permission="['事故与险情事实陈述:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="
            !selectedFactStatement ||
            detail.status === '3' ||
            detail.status === '2'
          "
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delFactStatement"
          v-permission="['事故与险情事实陈述:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #事实陈述>
        <v-table-list
          v-model="selectedFactStatement"
          :headers="factStatementHeaders"
          :items="factStatement"
        >
          <!-- <template v-slot:[`item.attachmentIds`]="{ item }">
            <v-btn
              @click.stop="openAttachmentDialog(item.attachmentIds)"
              dark
              x-small
              color="primary"
              elevation="0"
            >
              {{ item.attachmentIds.length }}
            </v-btn>
          </template> -->
        </v-table-list>
      </template>
      <template v-if="!detail.isClosed" #事故或事件处理记录按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createAccidentHandle"
          v-permission="['事故与险情事故或事件处理记录:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedAccidentHandle"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateAccidentHandle"
          v-permission="['事故与险情事故或事件处理记录:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="
            !selectedAccidentHandle ||
            detail.status === '3' ||
            detail.status === '2'
          "
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delAccidentHandle"
          v-permission="['事故与险情事故或事件处理记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #事故或事件处理记录>
        <v-table-list
          v-model="selectedAccidentHandle"
          :headers="accidentHandleHeaders"
          :items="accidentHandle"
        ></v-table-list>
      </template>
    </v-detail-view>
    <cas-dialog
      @success="loadCasualty"
      v-model="dialog1"
      :initialData="initCas"
    ></cas-dialog>
    <ins-dialog
      @success="loadInsurance"
      v-model="dialog2"
      :initialData="initIns"
    ></ins-dialog>
    <case-progress-dialog
      @success="loadInsurance"
      v-model="dialog3"
      :initialData="initIns1"
    ></case-progress-dialog>
    <fact-statement-dialog
      @success="loadFactStatement"
      v-model="dialog4"
      :initialData="initFactStatement"
    ></fact-statement-dialog>
    <accident-handle-dialog
      @success="loadAccidentHandle"
      v-model="dialog5"
      :initialData="initAccidentHandle"
    ></accident-handle-dialog>
    <!-- <v-dialog v-model="attachmentDialog" max-width="700" hide-overlay>
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog> -->
  </v-container>
</template>
<script>
import casDialog from './private/cas-dialog.vue'
import dictHelper from '@/mixin/dictHelper'
import InsDialog from './private/ins-dialog.vue'
import CaseProgressDialog from './private/case-progress-dialog.vue'
import FactStatementDialog from './private/fact-statement-dialog.vue'
import AccidentHandleDialog from './private/accident-handle-dialog.vue'
export default {
  components: {
    casDialog,
    InsDialog,
    CaseProgressDialog,
    FactStatementDialog,
    AccidentHandleDialog,
  },
  mixins: [dictHelper],
  name: 'accident-detail-new',
  created() {
    this.backRouteName = 'accident-list-new'
    this.subtitles = [
      '船舶基本信息',
      '事故相关数据',
      '事故损失及性质',
      '事实陈述',
      '事故或事件处理记录',
      '人员伤亡情况',
      '船舶报表',
      '费用项目',
      '保险理赔项目',
      // '案件进展',
    ]
    this.事故相关数据字段 = [
      { label: '事故编号', value: 'accidentNumber', type: 'string' },
      { label: '报告人', value: 'reporter', type: 'string' },
      { label: '报告时间', value: 'reportDate', type: 'date' },
      { label: '事故名称', value: 'accidentName', type: 'string' },
      { label: '船舶管理责任人', value: 'inChargePerson', type: 'string' },
      { label: '事故受害方', value: 'injuredParty', type: 'string' },
      {
        label: '推断事故责任方',
        value: 'presumedResponsibleParty',
        type: 'string',
      },
      { label: '出发港口', value: 'departurePort', type: 'string' },
      { label: '出发日期', value: 'departureDate', type: 'date' },
      { label: '目的港口', value: 'destinationPort', type: 'string' },
      { label: '抵达日期', value: 'destinationDate', type: 'date' },
      { label: '事故时水尺（米）', value: 'accidentDraft', type: 'string' },
      { label: '载货情况[吨]', value: 'cargoCondition', type: 'number' },
      { label: '事故发生时间（地方时间）', value: 'localTime', type: 'time' },
      { label: '事发天气状况', value: 'weatherCondition', type: 'string' },
      { label: '事故地点或经纬度', value: 'place', type: 'string' },
    ]
    this.船舶人员字段 = [
      { label: '船长', value: 'captain', type: 'string' },
      { label: '总船长', value: 'chiefCaptain', type: 'string' },
      { label: '轮机长', value: 'chiefEngineer', type: 'string' },
      { label: '大副', value: 'firstMate', type: 'string' },
      { label: '二副', value: 'secondMate', type: 'string' },
      { label: '三副', value: 'thirdMate', type: 'string' },
      { label: '海务主管', value: 'marineSupervisor', type: 'string' },
      { label: '主要监督员', value: 'mainSupervisor', require: false },
      { label: '现场见证人', value: 'witness', require: false },
    ]
    this.事故损失及性质字段 = [
      { label: '死亡人数', value: 'deathDoll', type: 'number' },
      { label: '受伤人数', value: 'injuryDoll', type: 'number' },
      { label: '发生污染', value: 'occurPollution', type: 'boolean' },
      // { label: '保险承保案件', value: 'insureCase', type: 'boolean' },
      { label: '事故状况', value: 'conditions', type: 'string' },
      { label: '船期损失（小时）', value: 'scheduleLoss', type: 'number' },
      { label: '经济损失（RMB）', value: 'economicLoss', type: 'number' },
    ]
    this.长文本字段 = [
      { label: '事故简况', value: 'briefDescription' },
      { label: '原因分析', value: 'causeAnalysis' },
      { label: '初步损失（含对方损失）', value: 'preliminaryLoss' },
      { label: '案件进展', value: 'caseProgress' },
      // { label: '事故经过概述', value: 'overview' },
      { label: '已采取的措施', value: 'alreadyTakenMeasure' },
      { label: '预防措施', value: 'measures' },
      // { label: '对事故责任人的处理意见或建议', value: 'suggestions' },
      // { label: '措施落实情况或其他需要说明的情况', value: 'otherSituations' },
      // { label: '事故教训', value: 'lesson' },
    ]
    this.casHeaders = [
      { text: '人员名称', value: 'crewName' },
      { text: '年龄', value: 'age' },
      { text: '身份证', value: 'identityCard' },
      { text: '职位', value: 'position' },
      { text: '伤亡类型', value: 'casualtyType' },
      { text: '死亡原因', value: 'reason' },
    ]
    this.reportHeaders = [
      { text: '报表名称', value: 'reportName' },
      { text: '上传人', value: 'poster' },
      { text: '上传时间', value: 'postTime' },
      { text: '状态', value: 'status' },
    ]
    this.insHeaders = [
      { text: '编号', value: 'number' },
      { text: '项目名称', value: 'itemName' },
      { text: '费用', value: 'expense' },
      { text: '备注', value: 'remark' },
    ]
    this.insHeaders1 = [
      { text: '编号', value: 'number' },
      { text: '项目名称', value: 'itemName' },
      // { text: '费用', value: 'expense' },
      { text: '备注', value: 'remark' },
    ]
    this.factStatementHeaders = [
      { text: '填写人', value: 'fillPerson' },
      { text: '岸基主管', value: 'shoreSupervisor' },
      { text: '相关人员', value: 'relevantPerson' },
      { text: '事由', value: 'subject' },
      { text: '事实陈述内容', value: 'content' },
      { text: '报告人', value: 'reporter' },
      { text: '见证人', value: 'witness' },
      { text: '日期', value: 'fillTime' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.accidentHandleHeaders = [
      { text: '日期', value: 'localTime' },
      { text: '地点', value: 'place' },
      { text: '参加人员', value: 'participant' },
      { text: '性质', value: 'nature' },
      { text: '情况概要', value: 'situationSummary' },
      { text: '时间节点及应急处理', value: 'timeEmergency' },
      { text: '风险评估', value: 'riskAssessment' },
      { text: '原因分析', value: 'causeAnalysis' },
      { text: '纠正预防措施', value: 'correctiveAction' },
    ]
    // 海务、机务、通导、i办、船员
    this.accidentTypes = [
      { text: '海务主管', value: '海务' },
      { text: '机务主管', value: '机务' },
      { text: '通导主管', value: '通导' },
      { text: 'i办主管', value: 'i办' },
      { text: '船员管理主管', value: '船员' },
    ]
  },
  computed: {
    // TODO:待不符合报告添加完毕，提交时将进行校验，未填写的不符合报告将不会被提交
    canSubmit() {
      // return (
      //   (!!this.nonstandartReportId &&
      //     !!this.accidentReportId &&
      //     !this.detail.auditParams) ||
      //   !!this.detail.auditParams?.isReject
      // )
      return (
        ((!!this.nonstandartReportId &&
          // !!this.accidentReportId &&
          !this.detail.auditParams) ||
          this.detail.auditParams.taskId) &&
        this.detail.status == 2
      )
    },
    isSave() {
      // console.log(this.$route.params.id)
      // console.log(this.deptReportInspection)
      // console.log(this.deptReportInspection.status)
      return this.$route.params.id == 'new'
        ? true
        : this.detail.status == '0' ||
          this.detail.status == '1' ||
          this.detail.status == '4' ||
          this.detail.status == '' ||
          this.detail.status == null
        ? true
        : false
    },
    accidentReportTempId() {
      return this.reportTypeMap?.find((i) => i.dictLabel === 'accidentReport')
        ?.dictValue
    },
    nonstandartReportTempId() {
      return this.reportTypeMap?.find(
        (i) => i.dictLabel === 'nonstandartReport',
      )?.dictValue
    },
    accidentReportId() {
      return (
        this.detail.accidentReportId ||
        this.$store.state.reportParams.businessParams.find(
          (b) => b.businessType === 'shipAccident',
        )?.reportId
      )
    },
    // TODO:待修改，可暂时废弃
    nonstandartReportId() {
      return (
        this.detail.nonstandartReportId ||
        this.$store.state.reportParams.businessParams.find(
          (b) => b.templateId === 'nonstandartReport',
          //this.nonstandartReportTempId,
        )?.reportId
      )
    },

    casualtiesReportId() {
      return (
        this.detail.casualtiesReportId ||
        this.$store.state.reportParams.businessParams.find(
          (b) => b.businessType === 'accidentCasuality',
        )?.reportId
      )
    },
    reports() {
      let reports = []
      if ('id' in this.accidentReport) reports.push(this.accidentReport)
      if ('id' in this.nonstandartReport) reports.push(this.nonstandartReport)
      if ('id' in this.casualtiesReport) reports.push(this.casualtiesReport)
      return reports
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/AccidentRecord/exportById?id=${this.detail.id}&type=1`
        : '' //`/api/business/seaAffairs/AccidentRecord/exportById?id=${this.detail.id}&type=1`
    },
    downloadUrl1() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/AccidentRecord/exportById?id=${this.detail.id}&type=2`
        : '' //`/api/business/seaAffairs/AccidentRecord/exportById?id=${this.detail.id}&type=2`
    },
  },
  watch: {
    accidentReportId(value) {
      if (value) {
        this.detail.accidentReportId = value
        this.loadReportInfo(value, 'a')
      }
    },
    nonstandartReportId(value) {
      if (value) {
        this.detail.nonstandartReportId = value
        this.loadReportInfo(value, 'n')
      }
    },
    casualtiesReportId(value) {
      if (value) {
        this.detail.casualtiesReportId = value
        this.loadReportInfo(value, 'c')
      }
    },
    'control.multiple'() {
      this.update = false
      setTimeout(() => {
        this.update = true
      })
    },
    'detail.natureAccident'(value) {
      //判断是否选择其他类型，选择其他类型可填写其他类型
      console.log('value ' + value)
      if (value.indexOf('12') != -1) {
        console.log('value1 ' + value)
        this.isOther = false
      } else {
        this.isOther = true
        this.detail.natureAccidentOther = ''
      }
    },
  },
  data() {
    return {
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      detail: {
        shipBaseMixOutputDTO: {},
        isClosed: false,
        accidentReportId: '',
        nonstandartReportId: '',
        casualtiesReportId: '',
        status: 0,
      },
      attachmentRecords: [],
      // attachmentDialog: false,
      reportTypeMap: [],
      casualties: [],
      insurances: [],
      insurances1: [],
      factStatement: [],
      accidentHandle: [],
      selectedCas: false,
      selectedIns: false,
      selectedIns1: false,
      selectedFactStatement: false,
      selectedAccidentHandle: false,
      initCas: {},
      initIns: {},
      initIns1: {},
      initFactStatement: {},
      initAccidentHandle: {},
      dialog1: false,
      dialog2: false,
      dialog3: false,
      dialog4: false,
      dialog5: false,
      accidentReport: {},
      nonstandartReport: {},
      casualtiesReport: {},
      isOther: true,
      update: true,
    }
  },

  methods: {
    // selectValue(event) {
    //   // this.detail.natureAccident = JSON.parse(this.detail.natureAccident) //转换多选项处理
    //   console.log('select ' + event.selectValue)
    //   console.log('select ' + this.detail.natureAccident + event.selectValue)
    // },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate() || !this.$refs.bform.validate()) {
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/AccidentRecord/record/update',
        this.detail,
      )
      if (errorRaw) false
      if (notMove) return this.detail.id
      goBack()
    },
    // openAttachmentDialog(attachmentRecords) {
    //   this.attachments = attachmentRecords
    //   this.attachmentDialog = true
    // },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (
        !this.isOther &&
        (!this.detail.natureAccidentOther ||
          this.detail.natureAccidentOther == '')
      )
        return
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/AccidentRecord/record/submit',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else if (this.detail.auditParams && this.detail.status === '4') {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/AccidentRecord/record/submit',
          { id: data },
        )
        if (!errorRaw) goBack()
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/record/getById/${this.$route.params.id}`,
      )
      this.detail = { ...data, casualtiesReportId: '' }
      this.detail.natureAccident = JSON.parse(this.detail.natureAccident) //转换多选项处理
      this.$refs.form.resetValidation()
      this.attachmentRecords = data.attachmentRecords
      await this.loadCasualty()
      await this.loadInsurance()
      this.reportTypeMap = await this.getDictByType('report_type_mapping')
      // if (data.accidentReportId)
      //   await this.loadReportInfo(data.accidentReportId, 'a')
    },
    async loadCasualty() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/casualties/getByAccId/${this.$route.params.id}`,
        { size: 99, current: 1 },
      )
      this.casualties = data.records
    },
    async loadInsurance() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/insurance/getByAccId/${this.$route.params.id}`,
        { size: 99, current: 1 },
      )
      this.insurances = data.records
    },
    async loadInsurance1() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/insurance/getByAccId/${this.$route.params.id}`,
        { size: 99, current: 1 },
      )
      this.insurances1 = data.records
    },
    async loadFactStatement() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/AccidentFactStatement/getByAccId/${this.$route.params.id}`,
        { size: 99, current: 1 },
      )
      this.factStatement = data.records
    },
    async loadAccidentHandle() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/AccidentRecord/AccidentHandle/getByAccId/${this.$route.params.id}`,
        { size: 99, current: 1 },
      )
      this.accidentHandle = data.records
    },
    async createCas() {
      this.initCas = {}
      this.dialog1 = true
    },
    async updateCas() {
      this.initCas = this.selectedCas
      this.dialog1 = true
    },
    async createIns() {
      this.initIns = {}
      this.dialog2 = true
    },
    async createIns1() {
      this.initIns1 = {}
      this.dialog3 = true
    },
    async updateIns() {
      this.initIns = this.selectedIns
      this.dialog2 = true
    },
    async updateIns1() {
      this.initIns1 = this.selectedIns1
      this.dialog3 = true
    },
    async createFactStatement() {
      this.initFactStatement = {}
      this.dialog4 = true
    },
    async updateFactStatement() {
      this.initFactStatement = this.selectedFactStatement
      this.dialog4 = true
    },
    async delFactStatement() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/AccidentRecord/AccidentFactStatement/deleteAccidentFactStatementBatch',
        [this.selectedFactStatement.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadFactStatement()
        this.selectedFactStatement = false
      }
    },
    async createAccidentHandle() {
      this.initAccidentHandle = {}
      this.dialog5 = true
    },
    async updateAccidentHandle() {
      this.initAccidentHandle = this.selectedAccidentHandle
      this.dialog5 = true
    },
    async delAccidentHandle() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/AccidentRecord/AccidentHandle/deleteAccidentHandleBatch',
        [this.selectedAccidentHandle.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadAccidentHandle()
        this.selectedAccidentHandle = false
      }
    },
    // async delCas() {},
    addAccidentReport() {
      this.$store.commit('emitBussiness', {
        businessType: 'shipAccident',
        businessId: this.detail.id,
        otherParams: {
          shipCode: this.detail.shipBaseMixOutputDTO.shipCode,
          // type: this.detail.accidentType,
          shipFlag:
            this.detail.shipBaseMixOutputDTO.flagPort +
            ',' +
            this.detail.shipBaseMixOutputDTO.flagState,
          shipName: this.detail.shipBaseMixOutputDTO.chShipName,
          accidentType: this.detail.accidentType,
        },
      })
      this.$router.push({
        name: 'accident-report-detail-new',
        params: {
          id: 'new',
          businessId1: this.detail.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },
    // TODO: 未完成
    // addNonstandartReport() {
    //   this.$store.commit('emitBussiness', {
    //     businessType: 'nonstandardAccident',
    //     businessId: this.detail.id,
    //     templateId: this.nonstandartReportTempId,
    //   })
    //   this.$router.push({
    //     name: 'report-emit-detail',
    //     params: { id: this.nonstandartReportTempId },
    //   })
    // },
    addNonstandartReport() {
      this.$store.commit('emitBussiness', {
        businessType: 'shipAccident_Non',
        businessId: this.detail.id,
        templateId: this.nonstandartReportTempId,
        otherParams: {
          shipCode: this.detail.shipBaseMixOutputDTO.shipCode,
          // type: this.detail.accidentType,
          shipFlag:
            this.detail.shipBaseMixOutputDTO.flagPort +
            ',' +
            this.detail.shipBaseMixOutputDTO.flagState,
          shipName: this.detail.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        //'report-emit-detail',
        name: 'dept-report-info-detail2-new',
        params: {
          id: this.nonstandartReportTempId,
          businessId1: this.detail.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },

    addCasualtiesReport() {
      this.$store.commit('emitBussiness', {
        businessType: 'accidentCasuality',
        businessId: this.detail.id,
        otherParams: {
          shipCode: this.detail.shipBaseMixOutputDTO.shipCode,
          type: this.detail.accidentType,
          shipFlag:
            this.detail.shipBaseMixOutputDTO.flagPort +
            ',' +
            this.detail.shipBaseMixOutputDTO.flagState,
          shipName: this.detail.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        name: 'casualties-report-detail-new',
        params: {
          id: 'new',
          businessId1: this.detail.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },

    async loadReportInfo(reportId, type) {
      // const { data } = await this.getAsync(
      //   '/business/seaAffairs/deptReport/getReportDetailById',
      //   { reportId },
      // )
      // const { deptReport } = data
      // deptReport['status'] = ['', '草稿', '审批中', '已审批', '已驳回'][
      //   deptReport.status
      // ]
      // if (type === 'a') this.accidentReport = deptReport
      // if (type === 'n') this.nonstandartReport = deptReport
      if (type === 'a') {
        const { data } = await this.getAsync(
          '/business/ismAffairs/ismAccidentBrief/getDetailById',
          { id: reportId },
        )
        this.accidentReport = {
          id: data.id,
          reportName: `${data.type}事故报告`,
          poster: data.handlerNickName,
          postTime: data.formDate,
          status: ['', '草稿', '审批中', '已审批', '已驳回'][data.status],
          type,
        }
      } else if (type === 'n') {
        // TODO: 不符合报告修改
        const { data } = await this.getAsync(
          // '/business/ismAffairs/ismNonstandardAccidentBrief/getDetailById',
          '/business/seaAffairs/dept-report-non-compliance/getReportDetailById',
          { reportId: reportId },
        )
        console.log(data)
        this.nonstandartReport = {
          id: data.id,
          reportName: `事故不符合报告`,
          poster: data.deptReportNonCompliance.poster,
          postTime: data.deptReportNonCompliance.postTime,
          status: ['', '草稿', '审批中', '已审批', '已驳回'][
            data.deptReportNonCompliance.status
          ],
          type,
        }
      } else if (type === 'c') {
        const { data } = await this.getAsync(
          '/business/ismAffairs/ismAccidentCasualties/getDetailById',
          { id: reportId },
        )
        this.casualtiesReport = {
          id: data.id,
          reportName: '伤亡人员情况',
          poster: data.handlerNickName,
          postTime: data.formDate,
          status: ['', '草稿', '审批中', '已审批', '已驳回'][data.status],
          type,
        }
      }
    },
    async loadAccident() {
      const { data } = await this.getAsync(
        '/business/ismAffairs/ismAccidentBrief/getRecordsByBusinessId',
        { businessId: this.$route.params.id },
      )
      this.detail.accidentReportId = data[0]?.id
    },
    async loadCasualtyReport() {
      const { data } = await this.getAsync(
        '/business/ismAffairs/ismAccidentCasualties/getRecordsByBusinessId',
        { businessId: this.$route.params.id },
      )
      this.detail.casualtiesReportId = data[0]?.id
    },
    // TODO: 不符合报告的加载
    async loadNonstandartReport() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/dept-report-non-compliance/getRecordsByBusinessId',
        { businessId: this.$route.params.id },
      )
      this.detail.nonstandartReportId = data[0]?.id
      console.log(this.detail.nonstandartReportId)
    },
  },

  async mounted() {
    await this.loadDetail()
    this.loadAccident()
    this.loadCasualtyReport()
    this.loadNonstandartReport()
    this.loadFactStatement()
    this.loadAccidentHandle()
  },

  beforeDestroy() {
    this.$store.commit('removeBussinessParam', this.detail.id)
  },
}
</script>

<style></style>
