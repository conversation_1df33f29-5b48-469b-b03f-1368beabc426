<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            outlined
            label="状态"
            dense
            :items="statusMap"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns></template>
      <template v-slot:[`item.shipInfoDO`]="{ item }">
        {{ item.shipInfoDO ? item.shipInfoDO.chShipName : '岸端报表' }}
      </template>
      <template v-slot:[`item.type`]>事故</template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{
            item.status == 2
              ? `待[${item.businessStatus}]审批`
              : statuses[item.status]
          }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'accident-report-list-new',
  created() {
    this.tableName = '事故与险情-不符合报告审批'
    this.reqUrl = '/business/seaAffairs/deptReport/page'
    this.headers = [
      { text: '报表任务名称', value: 'reportName' },
      { text: '船名', value: 'shipInfoDO' },
      { text: '发起人', value: 'poster' },
      { text: '年度', value: 'year' },
      { text: '季度', value: 'season' },
      { text: '发起时间', value: 'postTime' },
      { text: '备注', value: 'remark' },
      { text: '类型', value: 'type' },
      { text: '审批状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'dept-report-detail-new' }
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: {
        businessType: 'shipAccident',
        formCode: '47-090100-1',
      },
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
