<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      use-ship
      use-status
      :search-remain="searchObj"
      :search-dicts="searchDicts"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-dict-select
            dict-type="accident_nature_type"
            label="事故性质"
            v-model="searchObj.natureAccident"
            dense
          ></v-dict-select>
        </v-col> -->
      </template>
      <template #btns>
        <v-btn
          :disabled="selected.status != 3 || selected.isClosed"
          outlined
          color="primary"
          class="mx-1"
          @click="closeAcc"
          v-permission="['事故与险情记录:结案']"
        >
          <v-icon left>mdi-check-underline</v-icon>
          结案
        </v-btn>
        <v-btn
          outlined
          color="success"
          class="mx-1"
          @click="dialog = true"
          v-permission="['事故与险情记录:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAcc"
          v-permission="['事故与险情记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.occurPollution`]="{ item }">
        {{ item.occurPollution ? '是' : '否' }}
      </template>
      <!-- <template v-slot:[`item.insureCase`]="{ item }">
        {{ item.insureCase ? '是' : '否' }}
      </template> -->
      <template v-slot:[`item.isClosed`]="{ item }">
        {{ item.isClosed ? '是' : '否' }}
      </template>
    </v-table-searchable>
    <add-accident @success="success" v-model="dialog"></add-accident>
  </v-container>
</template>
<script>
import addAccident from './private/add-accident.vue'
export default {
  components: { addAccident },
  name: 'accident-list-new',
  created() {
    this.tableName = '船舶事故管理'
    this.reqUrl = '/business/seaAffairs/AccidentRecord/record/list'
    this.headers = [
      { text: '事故编号', value: 'accidentNumber' },
      { text: '船舶', value: 'shipInfo' },
      { text: '事故发生时间（地方时间）', value: 'localTime' },
      { text: '船长', value: 'captain' },
      { text: '轮机长', value: 'chiefEngineer' },
      { text: '死亡人数', value: 'deathDoll' },
      { text: '受伤人数', value: 'injuryDoll' },
      { text: '发生污染', value: 'occurPollution' },
      // { text: '保险承保案件', value: 'insureCase' },
      { text: '沉船或全损', value: 'shipwreckOrTotalLoss' },
      { text: '事故类型', value: 'natureAccidentName' },
      // { text: '事故性质', value: 'natureAccident' },
      { text: '事故等级', value: 'levelAccident' },
      { text: '结案', value: 'isClosed' },
      { text: '审批状态', value: 'status' },
    ]
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
    this.searchDate = {
      interval: true,
      value: 'beginDate',
    }
    this.pushParams = { name: 'accident-detail-new' }
    this.searchDicts = [
      {
        dicType: 'accident_nature_type',
        label: '事故类型',
        key: 'natureAccident',
        multiple: 'true',
      },
    ]
  },

  data() {
    return {
      selected: false,
      dialog: false,
      searchObj: {},
    }
  },

  methods: {
    async delAcc() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/AccidentRecord/record/deleteById',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
    success(id) {
      this.$router.push({ name: 'accident-detail-new', params: { id } })
    },
    async closeAcc() {
      if (!(await this.$dialog.msgbox.confirm('确定结案此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/AccidentRecord/record/finish',
        {
          id: this.selected.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`结案成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
