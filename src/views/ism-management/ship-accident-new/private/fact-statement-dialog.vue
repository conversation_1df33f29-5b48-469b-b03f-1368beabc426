<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        事实陈述
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="填写人"
                  dense
                  outlined
                  :rules="[rules.required]"
                  v-model="formData.fillPerson"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="岸基主管"
                  dense
                  outlined
                  :rules="[rules.required]"
                  v-model="formData.shoreSupervisor"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="相关人员"
                  dense
                  outlined
                  v-model="formData.relevantPerson"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="事由"
                  dense
                  outlined
                  :rules="[rules.required]"
                  v-model="formData.subject"
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  label="费用"
                  type="number"
                  :rules="[rules.required]"
                  v-model="formData.expense"
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="12">
                <v-textarea
                  label="事实陈述内容"
                  dense
                  outlined
                  v-model="formData.content"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  label="报告人"
                  dense
                  outlined
                  v-model="formData.reporter"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  label="报告人职务"
                  dense
                  outlined
                  v-model="formData.reporterPost"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  label="见证人"
                  dense
                  outlined
                  v-model="formData.witness"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  label="见证人职务"
                  dense
                  outlined
                  v-model="formData.witnessPost"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-date-time-picker
                  label="日期"
                  v-model="formData.fillTime"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></v-date-time-picker>
              </v-col>
              <!-- <v-row> -->
              <v-col cols="12">
                <v-attach-list
                  title="附件"
                  :attachments="formData.attachmentRecords"
                  @change="changeAttachment1"
                ></v-attach-list>
              </v-col>
              <!-- </v-row> -->

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'fact-statement-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v == 0 || '必填项不能为空',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = { ...this.initialData }
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    changeAttachment1(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/AccidentRecord/AccidentFactStatement/update'
        : '/business/seaAffairs/AccidentRecord/AccidentFactStatement/insert'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        accidentId: this.$route.params.id,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
