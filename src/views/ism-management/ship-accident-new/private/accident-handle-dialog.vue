<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        事故/事件处理记录
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-date-time-picker
                  label="日期和时间"
                  v-model="formData.localTime"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></v-date-time-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="地点"
                  dense
                  outlined
                  :rules="[rules.required]"
                  v-model="formData.place"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="参加人员"
                  dense
                  outlined
                  :rules="[rules.required]"
                  v-model="formData.participant"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="性质"
                  dense
                  outlined
                  :rules="[rules.required]"
                  v-model="formData.nature"
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  label="费用"
                  type="number"
                  :rules="[rules.required]"
                  v-model="formData.expense"
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="12">
                <!-- <v-text-field
                  label="情况概要"
                  v-model="formData.situationSummary"
                ></v-text-field> -->
                <v-textarea
                  label="情况概要"
                  dense
                  outlined
                  v-model="formData.situationSummary"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="时间节点及应急处理"
                  dense
                  outlined
                  v-model="formData.timeEmergency"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="风险评估"
                  dense
                  outlined
                  v-model="formData.riskAssessment"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="原因分析"
                  dense
                  outlined
                  v-model="formData.causeAnalysis"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="纠正预防措施"
                  dense
                  outlined
                  v-model="formData.correctiveAction"
                ></v-textarea>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'accident-handle-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v == 0 || '必填项不能为空',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = { ...this.initialData }
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/AccidentRecord/AccidentHandle/update'
        : '/business/seaAffairs/AccidentRecord/AccidentHandle/insert'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        accidentId: this.$route.params.id,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
