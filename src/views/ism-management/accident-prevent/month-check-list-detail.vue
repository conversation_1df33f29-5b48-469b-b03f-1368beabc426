<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['月度检查清单:编辑']"
      :title="titleName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="update"
      :can-submit="
        !detailInfo.auditParams ||
        detailInfo.auditParams.taskId ||
        detailInfo.status === '1' ||
        detailInfo.status === '4'
      "
      @submit="submit"
    >
      <template v-if="detailInfo.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detailInfo.auditParams"
              :shipCode="detailInfo.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:custombtns>
        <v-btn
          width="70"
          tile
          :loading="loading3"
          @click="update"
          color="success"
          small
          class="mx-1"
          v-permission="['月度检查清单:保存']"
        >
          保存
        </v-btn>
      </template>
      <template v-slot:清单信息>
        <v-form ref="form2">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="4">
                <v-text-field
                  label="清单名称"
                  readonly
                  outlined
                  dense
                  v-model="detailInfo.name"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-select
                  v-model="detailInfo.type"
                  label="检查类型"
                  :items="typeDict"
                  :item-text="'typeText'"
                  :item-value="'id'"
                  outlined
                  readonly
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="7">
                <v-textarea
                  label="备注"
                  readonly
                  required
                  outlined
                  dense
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:检查项目>
        <v-data-table
          class="use-divider"
          show-select
          :headers="headers"
          :items="detailInfo.details"
          dense
          single-select
          item-key="id"
          v-model="selected"
          :items-per-page="5"
        >
          <template v-slot:[`item.type`]="{ item }">
            {{ getText(item.type) }}
          </template>
        </v-data-table>
        <v-attach-list
          class="mt-2"
          :attachments="detailInfo.attachmentRecords"
          @change="changeAttachment"
          :disabled="!canEdit()"
        ></v-attach-list>
      </template>
      <template v-slot:检查项目按钮 v-if="!newCard">
        <v-btn
          :disabled="!selected.length"
          outlined
          small
          tile
          color="info"
          class="mx-1"
          :loading="loading1"
          @click="openForm"
          v-permission="['月度检查清单:修改内容']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
      </template>
    </v-detail-view>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="800"
      persistent
      v-model="editDialog"
    >
      <v-card>
        <v-card-title>
          编辑
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="6">
                  <vs-date-picker
                    label="检查时间"
                    required
                    :rules="[rules.required]"
                    v-model="formData.checkTime"
                    dense
                    outlined
                  ></vs-date-picker>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-textarea
                    label="检查情况"
                    outlined
                    dense
                    v-model="formData.checkResult"
                  ></v-textarea>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    :loading="loading2"
                    class="mx-1"
                    @click="edit"
                    block
                  >
                    <v-icon left>mdi-pencil</v-icon>
                    保存
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import routerControl from '@/mixin/routerControl'
export default {
  name: 'month-check-list-detail',
  mixins: [dictHelper, routerControl],
  created() {
    this.backRouteName = 'month-check-list'
    this.subtitles = ['清单信息', '检查项目']
    this.newCard = this.$route.params.id
    this.headers = [
      { text: '检查内容', value: 'content' },
      { text: '检查类型', value: 'type' },
      { text: '检查人', value: 'checker' },
      { text: '船方验证人', value: 'shipChecker' },
      {
        text: '岸基验证人',
        value: 'bankChecker',
        class: 'string[]',
        sortable: false,
      },
      { text: '检查情况', value: 'checkResult' },
      { text: '检查时间', value: 'checkTime' },
      { text: '填写人', value: 'handlerName' },
    ]
    this.initSelected = {}
  },
  data() {
    return {
      detailInfo: {},
      searchRemain: {},
      selected: false,
      formData: {},
      editDialog: false,
      backRouteName: 'month-check-list',
      sloading: false,
      loading1: false,
      loading2: false,
      loading3: false,
      typeDict: [],
      titleName: '检查清单详情',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  computed: {
    newCard: function () {
      return this.detailInfo.status === '2' || this.detailInfo.status === '3'
    },
  },
  methods: {
    getText(type) {
      let v = this.typeDict.find((t) => t.id === type)
        ? this.typeDict.find((t) => t.id === type).typeText
        : ''
      return v
    },
    canEdit() {
      console.log(
        'disable',
        this.detailInfo.status == '4' || this.detailInfo.status == '1',
      )
      return this.detailInfo.status == '4' || this.detailInfo.status == '1'
    },
    async update() {
      if (!this.canEdit()) {
        this.$dialog.message.error('仅草稿、已驳回状态可修改')
        return
      }
      this.loading3 = true
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/monthCheckList/update',
        this.detailInfo,
      )
      this.loading3 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.detailInfo = {}
      this.closeAndTo(this.backRouteName)
    },
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (
        !(await this.$dialog.msgbox.confirm(
          '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return

      if (!this.detailInfo.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/monthCheckList/process/submit',
          { id: this.detailInfo.id },
        )
        if (!errorRaw) {
          this.$dialog.message.success('操作成功')
          goBack()
        }
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async getDetailInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/seaAffairs/monthCheckList/total`,
          { id: this.$route.params.id },
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        // console.log('detailInfo', this.detailInfo)
        this.titleName = this.detailInfo.name
      }
    },
    openForm() {
      this.loading1 = true
      this.editDialog = true
      this.formData.id = this.selected[0].id
    },
    closeForm() {
      this.loading1 = false
      this.editDialog = false
      this.formData = {}
    },
    async edit() {
      if (!this.canEdit()) {
        this.$dialog.message.error('仅草稿、已驳回状态可修改')
        return
      }
      this.loading2 = true
      console.log('formData', this.formData)
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/monthCheckList/detail/update`,
        this.formData,
      )
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.closeForm()
      await this.getDetailInfo()
    },
    async getTypeDict() {
      const { data, errorRaw } = await this.getAsync(
        '/business/seaAffairs/inspectType/selectList',
      )
      if (errorRaw) {
        return
      }
      this.typeDict = data
      // console.log('data', data)
    },
  },

  async mounted() {
    await this.getDetailInfo()
    await this.getTypeDict()
  },
}
</script>

<style></style>
