<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :single-select="false"
      :searchRemain="searchRemain"
      :fix-header="false"
      :push-params="pushParams"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select
            clearable
            v-model="searchRemain.shipCode"
          ></v-ship-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            label="年度"
            outlined
            clearable
            dense
            v-model="searchRemain.year"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            label="月份"
            outlined
            clearable
            dense
            v-model="searchRemain.month"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.type"
            label="检查类型"
            :items="typeDict"
            :item-text="'typeText'"
            :item-value="'id'"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.status"
            label="状态"
            :items="statusMap"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          :loading="loading1"
          color="info"
          class="mx-1"
          @click="initList"
          v-permission="['月度检查清单:初始化本月清单']"
        >
          <v-icon left>mdi-arrow-top-right</v-icon>
          初始化本月清单
        </v-btn>
        <!--        <v-btn-->
        <!--          :disabled="!selected"-->
        <!--          outlined-->
        <!--          :loading="loading2"-->
        <!--          color="gray"-->
        <!--          class="mx-1"-->
        <!--          @click="submit"-->
        <!--          v-permission="['月度检查清单:提交审批']"-->
        <!--        >-->
        <!--          <v-icon left>mdi-arrow-bottom-left</v-icon>-->
        <!--          提交审批-->
        <!--        </v-btn>-->
        <v-btn
          :disabled="!canSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="submitApply"
          v-permission="['月度检查清单:审批通过']"
        >
          <v-icon left>mdi-send</v-icon>
          审批通过
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['月度检查清单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.type`]="{ item }">
        {{ getText(item.type) }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === '1'" small dark color="gray">草稿</v-chip>
        <v-chip v-else-if="item.status === '2'" small dark color="info">
          待{{ item.businessStatus }}审批
        </v-chip>
        <v-chip v-else-if="item.status === '3'" small dark color="success">
          已审核
        </v-chip>
        <v-chip v-else-if="item.status === '4'" small dark color="error">
          已驳回
        </v-chip>
      </template>
    </v-table-searchable>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="800"
      persistent
      v-model="copyDialog"
    >
      <v-card>
        <v-card-title>
          复制清单模板
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    label="名称"
                    required
                    :rules="[rules.required]"
                    outlined
                    dense
                    v-model="copyData.name"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-textarea
                    label="备注"
                    outlined
                    dense
                    v-model="copyData.remark"
                  ></v-textarea>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="copy"
                    block
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    复制
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import moment from 'moment'
export default {
  name: 'month-check-list',
  created() {
    this.tableName = '防止重大事故检查清单'
    this.reqUrl = '/business/seaAffairs/monthCheckList/page'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '清单名称', value: 'name' },
      { text: '年份', value: 'year' },
      { text: '月度', value: 'month' },
      { text: '类型', value: 'type' },
      { text: '操作人', value: 'handlerName' },
      { text: '状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'month-check-list-detail',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {
        year: moment().year(),
        month: moment().month() + 1,
      },
      copyData: {},
      attachmentDialog: false,
      copyDialog: false,
      formData: {},
      isEdit: false,
      loading: false,
      loading1: false,
      loading2: false,
      formShow: false,
      typeDict: [],
      statusMap: [
        { text: '草稿', value: 1 },
        { text: '审核中', value: 2 },
        { text: '已审核', value: 3 },
        { text: '已驳回', value: 4 },
      ],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  computed: {
    canSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) => item?.auditParams?.taskId && item.status == 2,
        )
      )
    },
  },

  methods: {
    async submitApply() {
      if (!(await this.$dialog.msgbox.confirm('确定审批通过所选记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/batchCompleteTaskAndCommentAndSetVar',
        this.selected.map((item) => ({
          adopt: true,
          comment: '',
          params: {},
          taskId: item.auditParams.taskId,
        })),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
    async initList() {
      if (!(await this.$dialog.msgbox.confirm('确定初始化本月清单？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/monthCheckList/init',
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async submit() {
      if (!(this.selected.status == '1' || this.selected.status == '4')) {
        this.$dialog.message.error('仅草稿状态和已驳回状态可提交审核')
        return
      }
      if (
        !(await this.$dialog.msgbox.confirm(
          '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return
      this.loading2 = true
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/monthCheckList/process/submit',
        { id: this.selected.id },
      )
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    getText(type) {
      let v = this.typeDict.find((t) => t.id === type)
        ? this.typeDict.find((t) => t.id === type).typeText
        : ''
      return v
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      let idList = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/monthCheckList/deleteBatch',
        idList,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async deliver() {
      if (!(await this.$dialog.msgbox.confirm('确定发布此模板？'))) return
      this.loading1 = true
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/monthCheckList/deliver',
        { id: this.selected.id },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    openCopy() {
      this.copyDialog = true
    },
    async copy() {
      this.copyData.id = this.selected.id
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/monthCheckList/copy',
        this.copyData,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      this.closeForm()
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {}
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },
    closeForm() {
      this.copyDialog = false
      this.copyData = {}
    },
    async getTypeDict() {
      const { data, errorRaw } = await this.getAsync(
        '/business/seaAffairs/inspectType/selectList',
      )
      if (errorRaw) {
        return
      }
      this.typeDict = data
      // console.log('data', data)
    },
    async creatPlan() {
      if (this.selected.status !== '2') {
        this.$dialog.message.error(`请选择已发布的年度模板`)
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定以此模板生成年度计划？')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/monthCheckList/createPlan',
        { id: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error(`操作失败，请重试`)
        return
      }
      this.$dialog.message.success(`操作成功`)
      this.selected = false
      await this.$router.push({
        name: 'annual-train-ship-plan',
        query: {
          reload: true,
        },
      })
    },
  },

  async mounted() {
    await this.getTypeDict()
  },
}
</script>

<style></style>
