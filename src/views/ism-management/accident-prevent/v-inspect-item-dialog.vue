<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新增模板内容
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :fuzzy-label="fuzzyLabel"
          req-url="/business/seaAffairs/inspectItem/page"
          :search-remain="searchRemain"
          item-key="id"
          :single-select="false"
          @dbclick="confirm"
        >
          <template #searchflieds>
            <slot name="searchflieds">
              <!-- 剩余参数插槽 -->
            </slot>
          </template>
          <template v-slot:[`item.type`]="{ item }">
            {{ getText(item.type) }}
          </template>
          <template v-slot:[`item.bankChecker`]="{ item }">
            {{ listToString(item.bankChecker) }}
          </template>
          <template v-slot:[`item.checker`]="{ item }">
            {{ listToString(item.checker) }}
          </template>
          <template v-slot:[`item.shipChecker`]="{ item }">
            {{ listToString(item.shipChecker) }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-inspect-item-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.headers = [
      { text: '检查内容', value: 'content' },
      { text: '检查类型', value: 'type' },
      { text: '检查人', value: 'checker' },
      { text: '船方验证人', value: 'shipChecker' },
      { text: '岸基验证人', value: 'bankChecker' },
    ]
    this.fuzzyLabel = ''
  },
  data() {
    return {
      dialog: false,
      formData: {},
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    listToString(value) {
      return value.join(', ')
    },
    getText(type) {
      let v = this.typeDict.find((t) => t.id === type)
        ? this.typeDict.find((t) => t.id === type).typeText
        : ''
      return v
    },
    async getTypeDict() {
      const { data, errorRaw } = await this.getAsync(
        '/business/seaAffairs/inspectType/selectList',
      )
      if (errorRaw) {
        return
      }
      this.typeDict = data
      // console.log('data', data)
    },
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      this.$emit('change', false)
      this.$emit('success', this.selected)
    },
  },
  async mounted() {
    await this.getTypeDict()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
