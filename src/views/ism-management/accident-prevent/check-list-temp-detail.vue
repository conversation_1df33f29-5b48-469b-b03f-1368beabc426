<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['检查清单模板:编辑']"
      :title="titleName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="false"
      @save="save"
    >
      <template v-slot:模板信息>
        <v-form ref="form2">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="模板名称"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                  v-model="detailInfo.name"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-select
                  v-model="detailInfo.type"
                  label="检查类型"
                  :rules="[rules.required]"
                  :items="typeDict"
                  :item-text="'typeText'"
                  :item-value="'id'"
                  outlined
                  required
                  dense
                ></v-select>
              </v-col>
              <!--              <v-col cols="12" md="2">-->
              <!--                <v-text-field-->
              <!--                  label="操作人"-->
              <!--                  required-->
              <!--                  outlined-->
              <!--                  dense-->
              <!--                  v-model="detailInfo.handlerName"-->
              <!--                ></v-text-field>-->
              <!--              </v-col>-->
              <v-col cols="12" md="7">
                <v-textarea
                  label="备注"
                  required
                  outlined
                  dense
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:模板内容>
        <v-data-table
          class="use-divider"
          show-select
          :headers="headers"
          :items="detailInfo.details"
          dense
          item-key="id"
          v-model="selected"
          :items-per-page="5"
        >
          <template v-slot:[`item.type`]="{ item }">
            {{ getText(item.type) }}
          </template>
          <template v-slot:[`item.bankChecker`]="{ item }">
            {{ listToString(item.bankChecker) }}
          </template>
          <template v-slot:[`item.checker`]="{ item }">
            {{ listToString(item.checker) }}
          </template>
          <template v-slot:[`item.shipChecker`]="{ item }">
            {{ listToString(item.shipChecker) }}
          </template>
        </v-data-table>
      </template>
      <template v-slot:模板内容按钮 v-if="!newCard">
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :loading="sloading"
          small
          @click="openForm"
          v-permission="['检查清单模板:新增内容']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected.length"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          :loading="loading1"
          @click="delAudit"
          v-permission="['检查清单模板:删除内容']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <v-inspect-item-dialog
      v-model="dialog1"
      @change="change"
      @success="success"
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="检查内容"
            outlined
            dense
            clearable
            v-model="searchRemain.content"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="4">
          <v-select
            label="检查类型"
            outlined
            dense
            :items="typeDict"
            clearable
            :item-text="'typeText'"
            :item-value="'id'"
            v-model="searchRemain.type"
          ></v-select>
        </v-col>
      </template>
    </v-inspect-item-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import VInspectItemDialog from '@/views/ism-management/accident-prevent/v-inspect-item-dialog.vue'

export default {
  name: 'check-list-temp-detail',
  components: { VInspectItemDialog },
  mixins: [dictHelper],
  created() {
    this.backRouteName = 'check-list-temp'
    this.subtitles = ['模板信息', '模板内容']
    this.newCard = this.$route.params.id
    this.headers = [
      { text: '检查内容', value: 'content' },
      { text: '检查类型', value: 'type' },
      { text: '检查人', value: 'checker' },
      { text: '船方验证人', value: 'shipChecker' },
      {
        text: '岸基验证人',
        value: 'bankChecker',
      },
    ]
    this.initSelected = {}
  },
  data() {
    return {
      detailInfo: {},
      searchRemain: {},
      selected: [],
      dialog1: false,
      sloading: false,
      loading1: false,
      typeDict: [],
      titleName: '新增检查清单模板',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  computed: {
    newCard: function () {
      return this.$route.params.id === `new` || this.detailInfo.status === '2'
    },
  },
  methods: {
    listToString(value) {
      console.log(value)
      console.log(value.join(', '))
      return value.join(', ')
    },
    getText(type) {
      let v = this.typeDict.find((t) => t.id === type)
        ? this.typeDict.find((t) => t.id === type).typeText
        : ''
      return v
    },
    async save(goback) {
      if (!this.$refs.form2.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      if (this.detailInfo.status === '2') {
        this.$dialog.message.error('已发布，无法修改')
        return
      }
      const url =
        this.$route.params.id === `new`
          ? '/business/seaAffairs/checkListTemp/save'
          : '/business/seaAffairs/checkListTemp/update'
      const { errorRaw } = await this.postAsync(url, this.detailInfo)
      if (errorRaw) {
        return
      }
      this.detailInfo = {}
      this.$dialog.message.success('操作成功')
      goback()
    },
    async getDetailInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/seaAffairs/checkListTemp/detailTempId/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        // console.log('detailInfo', this.detailInfo)
        this.titleName = this.detailInfo.name
      }
    },
    openForm() {
      this.sloading = true
      this.dialog1 = true
    },
    change(val) {
      this.dialog1 = val
      this.sloading = false
    },
    async success(val) {
      console.log(val)
      let matchDetail = {
        id: this.$route.params.id,
        details: val,
      }
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/checkListTemp/detail/saveDetails`,
        matchDetail,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.sloading = false
      this.selected = []
      await this.getDetailInfo()
    },
    async getTypeDict() {
      const { data, errorRaw } = await this.getAsync(
        '/business/seaAffairs/inspectType/selectList',
      )
      if (errorRaw) {
        return
      }
      this.typeDict = data
      // console.log('data', data)
    },
    async delAudit() {
      this.loading1 = true
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) {
        this.loading1 = false
        return
      }
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除'))) {
        this.loading1 = false
        return
      }
      let idList = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/checkListTemp/detail/deleteBatch`,
        idList,
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功！')
      await this.getDetailInfo()
      this.selected = []
    },
  },

  async mounted() {
    await this.getTypeDict()
    await this.getDetailInfo()
  },
}
</script>

<style></style>
