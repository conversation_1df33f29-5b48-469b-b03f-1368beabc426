<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model="formData.typeText"
                    label="检查类型"
                    :rules="[rules.required]"
                    outlined
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['检查类型:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            label="检查类型"
            outlined
            clearable
            dense
            v-model="searchRemain.content"
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['检查类型:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['检查类型:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'inspect-type',
  created() {
    this.tableName = '检查类型'
    this.reqUrl = '/business/seaAffairs/inspectType/page'
    this.headers = [
      { text: '检查类型', value: 'typeText' },
      { text: '操作人', value: 'handlerName' },
      { text: '操作时间', value: 'updateTime' },
    ]
    this.fuzzyLabel = ''
    this.typeDict = [
      { text: '船舶碰撞', value: '0' },
      { text: '甲板火灾', value: '1' },
      { text: '轮机火灾', value: '2' },
      { text: '锚装置', value: '3' },
      { text: '油舱泄露', value: '4' },
      { text: '货物事故', value: '5' },
      { text: '货物进水', value: '6' },
      { text: '舵机故障', value: '7' },
      { text: '舷梯与组合梯', value: '8' },
    ]
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      attachmentDialog: false,
      formData: {},
      typeDict: [],
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/inspectType/delete',
        { id: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/inspectType/update'
        : '/business/seaAffairs/inspectType/save'
      const { errorRaw } = await this.postAsync(reqUrl, { ...this.formData })
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      this.closeForm()
      await this.$refs.table.loadTableData()
    },
    createItem() {
      this.formData = {}
      this.formShow = true
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.isEdit = true
    },
    closeForm() {
      this.formShow = false
      this.formData = {}
      this.isEdit = false
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
