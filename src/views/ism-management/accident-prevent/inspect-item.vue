<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.type"
                    label="检查类型"
                    :rules="[rules.required]"
                    :items="typeDict"
                    :item-text="'typeText'"
                    :item-value="'id'"
                    outlined
                    required
                    dense
                  ></v-select>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="formData.content"
                    label="检查内容"
                    :rules="[rules.required]"
                    outlined
                    required
                    dense
                  ></v-textarea>
                </v-col>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.checker"
                    label="检查人"
                    :rules="[rules.required]"
                    :items="shipStationList"
                    :multiple="true"
                    :item-text="'dictLabel'"
                    :item-value="'dictValue'"
                    clearable
                    outlined
                    required
                    dense
                  ></v-select>
                </v-col>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.shipChecker"
                    label="船端验证人"
                    :rules="[rules.required]"
                    :items="shipStationList"
                    :item-text="'dictLabel'"
                    :item-value="'dictValue'"
                    :multiple="true"
                    clearable
                    outlined
                    required
                    dense
                  ></v-select>
                </v-col>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.bankChecker"
                    label="岸端验证人"
                    :rules="[rules.required]"
                    :items="bankStationList"
                    :multiple="true"
                    :item-text="'dictLabel'"
                    :item-value="'dictValue'"
                    clearable
                    outlined
                    required
                    dense
                  ></v-select>
                </v-col>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['检查项目库:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            label="检查内容"
            outlined
            clearable
            dense
            v-model="searchRemain.content"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.type"
            label="检查类型"
            :items="typeDict"
            :item-text="'typeText'"
            :item-value="'id'"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['检查项目库:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!--        <v-import-btn-->
        <!--          :import-url="`/business/seaAffairs/inspectItem/importExcel`"-->
        <!--          @importSuccess="importSuccess"-->
        <!--          :buttonLabel="'导入培训项目'"-->
        <!--          v-permission="['培训项目库:导入培训项目']"-->
        <!--        ></v-import-btn>-->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['检查项目库:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.type`]="{ item }">
        {{ getText(item.type) }}
      </template>
      <template v-slot:[`item.bankChecker`]="{ item }">
        {{ listToString(item.bankChecker) }}
      </template>
      <template v-slot:[`item.checker`]="{ item }">
        {{ listToString(item.checker) }}
      </template>
      <template v-slot:[`item.shipChecker`]="{ item }">
        {{ listToString(item.shipChecker) }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import { cacheGetDefault } from '@/util/cache'

export default {
  name: 'inspect-item',
  components: {},
  created() {
    this.tableName = '检查项目库'
    this.reqUrl = '/business/seaAffairs/inspectItem/page'
    this.headers = [
      { text: '检查内容', value: 'content' },
      { text: '检查类型', value: 'type' },
      { text: '检查人', value: 'checker' },
      { text: '船方验证人', value: 'shipChecker' },
      {
        text: '岸基验证人',
        value: 'bankChecker',
      },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      attachmentDialog: false,
      formData: {},
      typeDict: [],
      shipStationList: [],
      bankStationList: [],
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    getText(type) {
      let v = this.typeDict.find((t) => t.id === type)
        ? this.typeDict.find((t) => t.id === type).typeText
        : ''
      return v
    },
    listToString(value) {
      console.log(value)
      console.log(value.join(', '))
      return value.join(', ')
    },
    async getStationList() {
      let that = this
      let data = await cacheGetDefault('ship-station', async () => {
        const { data, errorRaw } = await that.getAsync(
          '/business/crew/infra/positionList',
          {},
          false,
        )
        console.log('data', data)
        if (errorRaw) {
          that.$dialog.message.error('船上岗位列表获取失败，请重试')
          return null
        }
        if (data.length === 0) {
          that.$dialog.message.error('船上岗位为空，部分功能受损')
        }
        return data
      })
      this.shipStationList = data.map((item) => {
        return {
          dictValue: item.positionName,
          dictLabel: item.positionName,
        }
      })
    },
    async getBankStationList() {
      const { data, errorRaw } = await this.getAsync(
        '/system/role/listBankRole',
      )
      if (errorRaw) {
        return
      }
      this.bankStationList = data.map((item) => {
        return {
          dictValue: item.roleName,
          dictLabel: item.roleName,
        }
      })
      // console.log('data', data)
    },
    async getTypeDict() {
      const { data, errorRaw } = await this.getAsync(
        '/business/seaAffairs/inspectType/selectList',
      )
      if (errorRaw) {
        return
      }
      this.typeDict = data
      // console.log('data', data)
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/inspectItem/delete',
        { id: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async save() {
      console.log('formData', this.formData)
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/inspectItem/update'
        : '/business/seaAffairs/inspectItem/save'
      const { errorRaw } = await this.postAsync(reqUrl, { ...this.formData })
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      this.closeForm()
      await this.$refs.table.loadTableData()
    },
    createItem() {
      this.formData = {}
      this.formShow = true
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.isEdit = true
    },
    closeForm() {
      this.formShow = false
      this.formData = {}
      this.isEdit = false
      this.selected = false
    },
  },

  mounted() {
    this.getTypeDict()
    this.getStationList()
    this.getBankStationList()
  },
}
</script>

<style></style>
