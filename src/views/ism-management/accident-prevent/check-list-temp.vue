<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      :push-params="pushParams"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <!--        <v-col cols="12" sm="6" md="2">-->
        <!--          <v-text-field-->
        <!--            label="年度"-->
        <!--            outlined-->
        <!--            clearable-->
        <!--            dense-->
        <!--            v-model="searchRemain.year"-->
        <!--          ></v-text-field>-->
        <!--        </v-col>-->
        <v-col cols="12" md="2">
          <v-text-field
            label="名称"
            outlined
            clearable
            dense
            v-model="searchRemain.name"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.type"
            label="检查类型"
            :items="typeDict"
            :item-text="'typeText'"
            :item-value="'id'"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.status"
            label="状态"
            :items="statusMap"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          color="success"
          class="mx-1"
          to="/ism-management/accident-prevent/check-list-temp-detail/new"
          v-permission="['检查清单模板:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          :loading="loading3"
          outlined
          color="info"
          class="mx-1"
          @click="reset"
          v-permission="['检查清单模板:重置']"
        >
          <v-icon left>mdi-hammer-wrench</v-icon>
          重置
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          color="info"
          class="mx-1"
          @click="openCopy"
          v-permission="['检查清单模板:复制']"
        >
          <v-icon left>mdi-file-document-multiple</v-icon>
          复制
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          :loading="loading1"
          color="info"
          class="mx-1"
          @click="deliver"
          v-permission="['检查清单模板:发布']"
        >
          <v-icon left>mdi-arrow-top-right</v-icon>
          发布
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          :loading="loading2"
          tile
          color="gray"
          class="mx-1"
          @click="cancel"
          v-permission="['检查清单模板:发布']"
        >
          <v-icon left>mdi-arrow-bottom-left</v-icon>
          取消发布
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['检查清单模板:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.type`]="{ item }">
        {{ getText(item.type) }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === '0'" small dark color="gray">
          编辑中
        </v-chip>
        <v-chip v-else-if="item.status === '1'" small dark color="info">
          草稿
        </v-chip>
        <v-chip v-else-if="item.status === '2'" small dark color="success">
          已发布
        </v-chip>
      </template>
    </v-table-searchable>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="800"
      persistent
      v-model="copyDialog"
    >
      <v-card>
        <v-card-title>
          复制清单模板
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    label="名称"
                    required
                    :rules="[rules.required]"
                    outlined
                    dense
                    v-model="copyData.name"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-textarea
                    label="备注"
                    outlined
                    dense
                    v-model="copyData.remark"
                  ></v-textarea>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="copy"
                    block
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    复制
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import moment from 'moment'
export default {
  name: 'check-list-temp',
  created() {
    this.tableName = '检查清单模板'
    this.reqUrl = '/business/seaAffairs/checkListTemp/page'
    this.headers = [
      { text: '名称', value: 'name' },
      { text: '类型', value: 'type' },
      { text: '操作人', value: 'handlerName' },
      { text: '状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'check-list-temp-detail',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {
        year: moment().year(),
      },
      copyData: {},
      attachmentDialog: false,
      copyDialog: false,
      formData: {},
      isEdit: false,
      loading: false,
      loading1: false,
      loading2: false,
      loading3: false,
      formShow: false,
      typeDict: [],
      statusMap: [
        { text: '编辑中', value: 0 },
        { text: '草稿', value: 1 },
        { text: '已发布', value: 2 },
      ],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    getText(type) {
      let v = this.typeDict.find((t) => t.id === type)
        ? this.typeDict.find((t) => t.id === type).typeText
        : ''
      return v
    },
    async reset() {
      if (this.selected.status !== '2') {
        this.$dialog.message.error(`请选择已发布的模板`)
      }
      if (!(await this.$dialog.msgbox.confirm('确定重置此模板？'))) return
      this.loading3 = true
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/monthCheckList/reset',
        { id: this.selected.id },
      )
      this.loading3 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
    },
    async cancel() {
      if (!(await this.$dialog.msgbox.confirm('确定取消发布此模板？'))) return
      this.loading2 = true
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/checkListTemp/cancel',
        { id: this.selected.id },
      )
      this.loading2 = false
      if (errorRaw) {
        // this.$dialog.message.error(`操作失败，请重试`)
        return
      }
      this.$dialog.message.success(`操作成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/checkListTemp/delete',
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async deliver() {
      if (!(await this.$dialog.msgbox.confirm('确定发布此模板？'))) return
      this.loading1 = true
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/checkListTemp/deliver',
        { id: this.selected.id },
      )
      this.loading1 = false
      if (errorRaw) {
        // this.$dialog.message.error(`操作失败，请重试`)
        return
      }
      this.$dialog.message.success(`操作成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    openCopy() {
      this.copyDialog = true
    },
    async copy() {
      this.copyData.id = this.selected.id
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/checkListTemp/copy',
        this.copyData,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      this.closeForm()
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {}
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },
    closeForm() {
      this.copyDialog = false
      this.copyData = {}
    },
    async getTypeDict() {
      const { data, errorRaw } = await this.getAsync(
        '/business/seaAffairs/inspectType/selectList',
      )
      if (errorRaw) {
        return
      }
      this.typeDict = data
      // console.log('data', data)
    },
    async creatPlan() {
      if (this.selected.status !== '2') {
        this.$dialog.message.error(`请选择已发布的年度模板`)
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定以此模板生成年度计划？')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/checkListTemp/createPlan',
        { id: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error(`操作失败，请重试`)
        return
      }
      this.$dialog.message.success(`操作成功`)
      this.selected = false
      await this.$router.push({
        name: 'annual-train-ship-plan',
        query: {
          reload: true,
        },
      })
    },
  },

  async mounted() {
    await this.getTypeDict()
  },
}
</script>

<style></style>
