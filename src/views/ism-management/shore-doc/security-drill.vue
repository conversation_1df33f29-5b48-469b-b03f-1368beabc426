<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="formData.shipCode"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  v-model="formData.testTime"
                  label="演习时间"
                  :rules="[rules.required]"
                  required
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.handler"
                  label="经办人"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  v-model="formData.remark"
                  label="备注"
                  dense
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="formData.attachments"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['保安联合演习:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      use-ship
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.history"
            label="历史数据"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['保安联合演习:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['保安联合演习:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'security-drill',
  created() {
    this.tableName = '船岸保安联合演习记录'
    this.reqUrl = '/business/seaAffairs/ismShoreDocument/ssasRecordPage'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '经办人', value: 'handler' },
      { text: '演习时间', value: 'testTime' },
      { text: '下次演习时间', value: 'nextTestTime' },
      { text: '附件', value: 'attachments', sortable: false },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊查询'
    this.searchDate = {
      label: '演习时间',
      value: 'testTime',
    }
    this.cycleTypes = [
      { text: '三个月一次', value: '0' },
      { text: '一年一次', value: '1' },
    ]
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      searchObj: { cycleType: '2', history: false },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ismShoreDocument/deleteSSASRecord',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected,
        shipCode: this.selected.shipInfo.shipCode,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      this.updateTaskPromptMassage(this.selected.id)
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShoreDocument/updateSSASRecord'
        : '/business/seaAffairs/ismShoreDocument/saveSSASRecord'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData, cycleType: '2' },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
