<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>

        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.year"
                    label="年度"
                    :rules="[rules.required, rules.yyyy]"
                    required
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    v-model="formData.auditType"
                    label="审核类型"
                    :rules="[rules.required]"
                    required
                    outlined
                    dense
                    :items="auditTypes"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="3">
                  <v-dict-select
                    dict-type="ship_management_company"
                    label="船管公司"
                    v-model="formData.manageCompany"
                    dense
                    outlined
                    :rules="[rules.required]"
                  ></v-dict-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.handler"
                    label="递交人"
                    :rules="[rules.required]"
                    outlined
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    v-model="formData.recordDate"
                    outlined
                    label="日期"
                    :rules="[rules.required]"
                    required
                    dense
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="formData.ncRecord"
                    outlined
                    label="NC记录"
                  ></v-textarea>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="formData.obRecord"
                    outlined
                    label="OB记录"
                  ></v-textarea>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.remark"
                    label="备注"
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-attach-list
                    :attachments="formData.attachments"
                    @change="changeAttachment"
                  ></v-attach-list>
                </v-col>

                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['公司审核:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            outlined
            v-model="searchObj.auditType"
            label="审核类型"
            dense
            :items="auditTypes"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="3">
          <v-dict-select
            dict-type="ship_management_company"
            label="船管公司"
            v-model="searchObj.manageCompany"
            dense
            outlined
          ></v-dict-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['公司审核:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['公司审核:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.auditType`]="{ item }">
        {{ ['内审', '外审'][item.auditType] }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'company-audit',
  created() {
    this.tableName = '公司审核'
    this.reqUrl = '/business/seaAffairs/ismShoreDocument/companyAuditPage'
    this.headers = [
      { text: '年度', value: 'year' },
      { text: '审核类型', value: 'auditType' },
      { text: '船管公司', value: 'manageCompany' },
      { text: '递交人', value: 'handler' },
      { text: '日期', value: 'recordDate' },
      { text: 'NC', value: 'ncRecord' },
      { text: 'OB', value: 'obRecord' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachments', sortable: false },
    ]
    this.fuzzyLabel = '模糊查询'
    this.searchDate = {
      label: '日期',
      value: 'recordDate',
    }
    this.auditTypes = [
      { text: '内审', value: '0' },
      { text: '外审', value: '1' },
    ]
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        yyyy: (v) => /^\d{4}$/.test(v) || '年度格式不正确',
      },
      searchObj: {},
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ismShoreDocument/deleteCompanyAudit',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShoreDocument/updateCompanyAudit'
        : '/business/seaAffairs/ismShoreDocument/saveCompanyAudit'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
