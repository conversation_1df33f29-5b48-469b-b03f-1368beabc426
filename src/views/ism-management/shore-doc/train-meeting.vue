<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>

        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="会议时间"
                    outlined
                    required
                    dense
                    :rules="[rules.required]"
                    v-model="formData.meetingTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="3">
                  <v-dict-select
                    dict-type="ship_management_company"
                    label="船管公司"
                    v-model="formData.manageCompany"
                    dense
                    outlined
                    :rules="[rules.required]"
                  ></v-dict-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.dept"
                    label="部门"
                    :rules="[rules.required]"
                    required
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    outlined
                    v-model="formData.handler"
                    label="递交人"
                    :rules="[rules.required]"
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    outlined
                    v-model="formData.type"
                    label="培训类型"
                    :rules="[rules.required]"
                    required
                    dense
                    :items="types"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.remark"
                    label="备注"
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-attach-list
                    :attachments="formData.attachments"
                    @change="changeAttachment"
                  ></v-attach-list>
                </v-col>

                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['公司例会-培训:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
      :search-remain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="3">
          <v-dict-select
            dict-type="ship_management_company"
            label="船管公司"
            v-model="searchRemain.manageCompany"
            dense
            clearable
            outlined
          ></v-dict-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            v-model="searchRemain.type"
            label="培训类型"
            clearable
            required
            dense
            :items="types"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['公司例会-培训:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['公司例会-培训:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.type`]="{ item }">
        <span v-if="item.type === '1'">内训</span>
        <span v-if="item.type === '2'">外训</span>
        <span v-if="item.type === '3'">公司例会</span>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'train-meeting',
  created() {
    this.tableName = '安全例会/培训'
    this.reqUrl = '/business/seaAffairs/ismShoreDocument/meetingTrainingPage'
    this.headers = [
      { text: '培训类型', value: 'type' },
      { text: '会议时间', value: 'meetingTime' },
      { text: '船管公司', value: 'manageCompany' },
      { text: '部门', value: 'dept' },
      { text: '递交人', value: 'handler' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachments', sortable: false },
    ]
    this.fuzzyLabel = '模糊查询'
    this.types = [
      { text: '内训', value: 1 },
      { text: '外训', value: 2 },
      { text: '公司例会', value: 3 },
    ]
  },

  data() {
    return {
      searchRemain: {},
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        month: (v) => (v > 0 && v < 13) || '月份必须在1-12之间',
        int: (v) => /^\d+$/.test(v) || '必须为整数',
        yyyy: (v) => /^\d{4}$/.test(v) || '必须为4位整数',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ismShoreDocument/deleteMeetingTraining',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShoreDocument/updateMeetingTraining'
        : '/business/seaAffairs/ismShoreDocument/saveMeetingTraining'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
