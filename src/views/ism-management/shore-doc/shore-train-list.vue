<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="3">
                  <v-dict-select
                    dict-type="ship_management_company"
                    label="船管公司"
                    v-model="formData.manageCompany"
                    dense
                    outlined
                    :rules="[rules.required]"
                  ></v-dict-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    :readonly="isEdit"
                    v-model="formData.docType"
                    label="文件类型"
                    :rules="[rules.required]"
                    required
                    dense
                    outlined
                    :items="docTypes"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="日期"
                    outlined
                    required
                    dense
                    :rules="[rules.required]"
                    v-model="formData.checkTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.itemName"
                    label="项目名称"
                    :rules="[rules.required]"
                    required
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.handler"
                    label="经办人"
                    :rules="[rules.required]"
                    required
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    outlined
                    v-model="formData.remark"
                    label="备注"
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-attach-list
                    :attachments="formData.attachmentRecords"
                    @change="changeAttachment"
                    :title="'附件'"
                  ></v-attach-list>
                </v-col>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['岸基培训:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" md="3">
          <v-dict-select
            dict-type="ship_management_company"
            label="船管公司"
            v-model="searchRemain.manageCompany"
            dense
            outlined
          ></v-dict-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.docType"
            label="文件类型"
            dense
            outlined
            :items="docTypes"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            v-model="searchRemain.handler"
            label="经办人"
            :rules="[rules.required]"
            required
            outlined
            dense
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['岸基培训:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['岸基培训:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.systemReportName`]="{ item }">
        <router-link
          :to="{
            name: 'dept-report-detail',
            params: { id: item.systemReportId },
          }"
        >
          {{ item.systemReportName }}
        </router-link>
      </template>
      <template v-slot:[`item.docType`]="{ item }">
        <span v-if="item.docType === '1'">有效性评价</span>
        <span v-if="item.docType === '2'">管理复查</span>
      </template>
      <!--      <template v-slot:[`item.status`]="{ item }">-->
      <!--        {{ ['', '草稿', '审批中', '已审批', '已驳回'][item.status] }}-->
      <!--      </template>-->
    </v-table-searchable>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'shore-train-list',
  components: {},
  mixins: [dictHelper],
  created() {
    this.tableName = '公司有效性评价/管理复查'
    this.reqUrl = '/business/seaAffairs/ismShoreDocument/trainPage'
    this.headers = [
      { text: '公司名称', value: 'manageCompany' },
      { text: '文件类型', value: 'docType' },
      { text: '日期', value: 'checkTime' },
      { text: '经办人', value: 'handler' },
      { text: '项目名称', value: 'itemName' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachments', sortable: false },
    ]
    this.fuzzyLabel = '模糊查询'
    this.docTypes = [
      { text: '有效性评价', value: '1' },
      { text: '管理复查', value: '2' },
    ]
  },
  computed: {},

  data() {
    return {
      selected: false,
      searchRemain: {},
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      trainerRepTempId: '',
      managerRepTempId: '',
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },

    createRep(type) {
      this.$store.commit('emitBussiness', {
        businessType: type
          ? 'shoreDocTrainForManager'
          : 'shoreDocTrainForTrainer',
        businessId: this.formData.vid,
        templateId: type ? this.managerRepTempId : this.trainerRepTempId,
      })
      this.$router.push({
        name: 'report-emit-detail',
        params: { id: type ? this.managerRepTempId : this.trainerRepTempId },
      })
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ismShoreDocument/deleteTrainRecord',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        vid: Math.floor(Math.random() * 1000 + 1),
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected, vid: this.selected.id }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShoreDocument/updateTrainRecord'
        : '/business/seaAffairs/ismShoreDocument/saveTrainRecord'
      const { errorRaw, data } = await this.postAsync(
        reqUrl,
        { ...this.formData, systemReportId: this.reportId },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      if (!this.isEdit)
        await this.getAsync(
          `/business/seaAffairs/ismShoreDocument/submitTrainRecord/${data}`,
        )
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$store.commit('removeBussinessParam', this.formData.vid)
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },

    async loadReportTempId() {
      const reportTypeMap = await this.getDictByType('report_type_mapping')
      this.trainerRepTempId = reportTypeMap.find(
        (i) => i.dictLabel === 'shoreDocTrainForTrainer',
      )?.dictValue
      this.managerRepTempId = reportTypeMap.find(
        (i) => i.dictLabel === 'shoreDocTrainForManager',
      )?.dictValue
    },
  },

  mounted() {
    this.loadReportTempId()
  },
}
</script>

<style></style>
