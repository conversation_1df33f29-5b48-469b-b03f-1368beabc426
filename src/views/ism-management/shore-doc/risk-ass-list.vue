<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>

        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.riskItem"
                    label="风险项目名称"
                    :rules="[rules.required]"
                    outlined
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.dept"
                    label="部门"
                    :rules="[rules.required]"
                    outlined
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3">
                  <v-dict-select
                    dict-type="ship_management_company"
                    label="船管公司"
                    v-model="formData.manageCompany"
                    dense
                    outlined
                    :rules="[rules.required]"
                  ></v-dict-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.handler"
                    label="经办人"
                    :rules="[rules.required]"
                    outlined
                    dense
                    required
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    useToday
                    v-model="formData.assDate"
                    label="风险评估日期"
                    outlined
                    dense
                    required
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.remark"
                    outlined
                    label="备注"
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <v-attach-list
                    :attachments="formData.planAttachments"
                    @change="changeAttachment($event, 'planAttachmentIds')"
                    title="评估计划附件"
                  ></v-attach-list>
                </v-col>
                <v-col cols="12" md="6">
                  <v-attach-list
                    :attachments="formData.regonizeAttachments"
                    @change="changeAttachment($event, 'regonizeAttachmentIds')"
                    title="识别评价附件"
                  ></v-attach-list>
                </v-col>
                <v-col cols="12" md="6">
                  <v-attach-list
                    :attachments="formData.reportAttachments"
                    @change="changeAttachment($event, 'reportAttachmentIds')"
                    title="评估报告附件"
                  ></v-attach-list>
                </v-col>
                <v-col cols="12" md="6">
                  <v-attach-list
                    :attachments="formData.otherAttachments"
                    @change="changeAttachment($event, 'otherAttachmentIds')"
                    title="其他附件"
                  ></v-attach-list>
                </v-col>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['岸基风险评估:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" md="3">
          <v-dict-select
            dict-type="ship_management_company"
            label="船管公司"
            v-model="searchRemain.manageCompany"
            dense
            outlined
          ></v-dict-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['岸基风险评估:新建']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['岸基风险评估:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.planAttachments`]="{ item }">
        <v-btn
          @click.stop="openAttachmentDialog(item.planAttachments)"
          dark
          x-small
          color="primary"
          elevation="0"
        >
          {{ item.planAttachments.length }}
        </v-btn>
      </template>
      <template v-slot:[`item.regonizeAttachments`]="{ item }">
        <v-btn
          @click.stop="openAttachmentDialog(item.regonizeAttachments)"
          dark
          x-small
          color="primary"
          elevation="0"
        >
          {{ item.regonizeAttachments.length }}
        </v-btn>
      </template>
      <template v-slot:[`item.reportAttachments`]="{ item }">
        <v-btn
          @click.stop="openAttachmentDialog(item.reportAttachments)"
          dark
          x-small
          color="primary"
          elevation="0"
        >
          {{ item.reportAttachments.length }}
        </v-btn>
      </template>
      <template v-slot:[`item.otherAttachments`]="{ item }">
        <v-btn
          @click.stop="openAttachmentDialog(item.otherAttachments)"
          dark
          x-small
          color="primary"
          elevation="0"
        >
          {{ item.otherAttachments.length }}
        </v-btn>
      </template>
    </v-table-searchable>
    <v-dialog
      v-model="attachmentDialog"
      max-width="700"
      hide-overlay
      attach="#mask"
    >
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
export default {
  name: 'risk-ass-list',
  created() {
    this.tableName = '岸基风险评估'
    this.reqUrl = '/business/seaAffairs/ismShoreDocument/riskassPage'
    this.headers = [
      { text: '风险项目名称', value: 'riskItem' },
      { text: '船管公司', value: 'manageCompany' },
      { text: '部门', value: 'dept' },
      { text: '经办人', value: 'handler' },
      { text: '风险评估日期', value: 'assDate' },
      { text: '备注', value: 'remark' },
      { text: '评估计划附件', value: 'planAttachments', sortable: false },
      { text: '识别评价附件', value: 'regonizeAttachments', sortable: false },
      { text: '评估报告附件', value: 'reportAttachments', sortable: false },
      { text: '其他附件', value: 'otherAttachments', sortable: false },
    ]
    this.fuzzyLabel = '模糊查询'
    this.searchDate = {
      label: '评估日期',
      value: 'assDate',
    }
    this.attachments = []
    this.attachmentHeader = [
      { text: '名称', value: 'name' },
      { text: '大小(kb)', value: 'fileSize' },
      { text: '上传时间', value: 'createTime' },
      { text: '上传人', value: 'userName' },
    ]
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      attachmentDialog: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds, type) {
      this.formData[type] = attachmentIds
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ismShoreDocument/deleteRiskass',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        reportAttachmentIds: [],
        otherAttachmentIds: [],
        planAttachmentIds: [],
        regonizeAttachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShoreDocument/updateRiskass'
        : '/business/seaAffairs/ismShoreDocument/saveRiskass'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        reportAttachmentIds: [],
        otherAttachmentIds: [],
        planAttachmentIds: [],
        regonizeAttachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
