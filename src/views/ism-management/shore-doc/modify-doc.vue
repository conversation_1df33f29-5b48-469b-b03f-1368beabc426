<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>

        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="3">
                  <v-dict-select
                    dict-type="ship_management_company"
                    label="船管公司"
                    v-model="formData.manageCompany"
                    dense
                    outlined
                    :rules="[rules.required]"
                  ></v-dict-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.documentName"
                    label="文件名称"
                    :rules="[rules.required]"
                    outlined
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.documentNo"
                    label="文件编号"
                    outlined
                    :rules="[rules.required]"
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.recordTitle"
                    label="记录标题"
                    outlined
                    :rules="[rules.required]"
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    v-model="formData.effectiveDate"
                    label="生效日期"
                    outlined
                    :rules="[rules.required]"
                    required
                    dense
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.handler"
                    label="递交人"
                    outlined
                    :rules="[rules.required]"
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    v-model="formData.modifyDate"
                    label="修改日期"
                    outlined
                    :rules="[rules.required]"
                    required
                    dense
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12">
                  <v-attach-list
                    :attachments="formData.attachments"
                    @change="changeAttachment"
                  ></v-attach-list>
                </v-col>

                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['岸基文件修改:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" md="3">
          <v-dict-select
            dict-type="ship_management_company"
            label="船管公司"
            v-model="searchRemain.manageCompany"
            dense
            outlined
          ></v-dict-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['岸基文件修改:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['岸基文件修改:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'modify-doc',
  created() {
    this.tableName = '岸基文件修改'
    this.reqUrl = '/business/seaAffairs/ismShoreDocument/modifyDocPage'
    this.headers = [
      { text: '船管公司', value: 'manageCompany' },
      { text: '文件名称', value: 'documentName' },
      { text: '文件编号', value: 'documentNo' },
      { text: '记录标题', value: 'recordTitle' },
      { text: '生效日期', value: 'effectiveDate' },
      { text: '递交人', value: 'handler' },
      { text: '修改日期', value: 'modifyDate' },
      { text: '附件', value: 'attachments', sortable: false },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊查询'
  },

  data() {
    return {
      selected: false,
      formData: {},
      searchRemain: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ismShoreDocument/deleteModifyDoc',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShoreDocument/updateModifyDoc'
        : '/business/seaAffairs/ismShoreDocument/saveModifyDoc'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
