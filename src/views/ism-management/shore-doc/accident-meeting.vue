<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>

        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="2">
                  <v-ship-select
                    v-model="formData.shipCode"
                    :rules="[rules.required]"
                    required
                    dense
                  ></v-ship-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.year"
                    label="年度"
                    outlined
                    :rules="[rules.required, rules.year]"
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3">
                  <v-dict-select
                    dict-type="ship_management_company"
                    label="船管公司"
                    v-model="formData.manageCompany"
                    dense
                    outlined
                    :rules="[rules.required]"
                  ></v-dict-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.handler"
                    label="递交人"
                    :rules="[rules.required]"
                    outlined
                    required
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    v-model="formData.remark"
                    label="备注"
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <v-textarea
                    outlined
                    v-model="formData.accidentSummary"
                    label="事故概要"
                    dense
                    rows="2"
                  ></v-textarea>
                </v-col>
                <v-col cols="12" md="6">
                  <v-textarea
                    outlined
                    v-model="formData.causeAnalysis"
                    label="原因分析"
                    dense
                    rows="2"
                  ></v-textarea>
                </v-col>
                <v-col cols="12" md="6">
                  <v-textarea
                    outlined
                    v-model="formData.correctivePreventiveMeasures"
                    label="预防纠正措施"
                    dense
                    rows="2"
                  ></v-textarea>
                </v-col>
                <v-col cols="12" md="6">
                  <v-textarea
                    outlined
                    v-model="formData.emergencyPlan"
                    label="应急预案"
                    dense
                    rows="2"
                  ></v-textarea>
                </v-col>
                <v-col cols="12" md="6">
                  <v-textarea
                    outlined
                    v-model="formData.riskAssessment"
                    label="风险评估"
                    dense
                    rows="2"
                  ></v-textarea>
                </v-col>
                <v-col cols="12" md="6">
                  <v-textarea
                    outlined
                    v-model="formData.solution"
                    label="解决方案"
                    dense
                    rows="2"
                  ></v-textarea>
                </v-col>
                <v-col cols="12">
                  <v-attach-list
                    :attachments="formData.attachments"
                    @change="changeAttachment"
                  ></v-attach-list>
                </v-col>

                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['船舶事故会议纪要:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      use-ship
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" md="3">
          <v-dict-select
            dict-type="ship_management_company"
            label="船管公司"
            v-model="searchObj.manageCompany"
            dense
            outlined
          ></v-dict-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['船舶事故会议纪要:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['船舶事故会议纪要:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="!selected"
          :href="exportUrl"
          v-permission="['船舶事故会议纪要:导出PDF']"
        >
          <v-icon left>mdi-file-pdf-box</v-icon>
          导出PDF
        </v-btn>
      </template>
      <template v-slot:[`item.shipInfo`]="{ item }">
        {{ item.shipInfo.chShipName }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'accident-meeting',
  created() {
    this.tableName = '船舶事故会议纪要'
    this.reqUrl = '/business/seaAffairs/ismShoreDocument/accidentMeetingPage'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '年度', value: 'year' },
      { text: '船管公司', value: 'manageCompany' },
      { text: '事故概要', value: 'accidentSummary' },
      { text: '原因分析', value: 'causeAnalysis' },
      { text: '预防纠正措施', value: 'correctivePreventiveMeasures' },
      { text: '应急预案', value: 'emergencyPlan' },
      { text: '风险评估', value: 'riskAssessment' },
      { text: '解决方案', value: 'solution' },
      { text: '递交人', value: 'handler' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachments', sortable: false },
    ]
    this.fuzzyLabel = '模糊查询'
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        year: (v) => /^\d{4}$/.test(v) || '必须为4位整数',
      },
      searchObj: {
        // remindType: this.$route.query?.remindType ?? '',
      },
    }
  },

  computed: {
    exportUrl() {
      return `/api/business/seaAffairs/ismShoreDocument/exportAccidentMeeting?id=${this.selected.id}`
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/ismShoreDocument/deleteAccidentMeeting',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected,
        shipCode: this.selected.shipInfo.shipCode,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      this.updateTaskPromptMassage(this.selected.id)
    },

    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/ismShoreDocument/updateAccidentMeeting'
        : '/business/seaAffairs/ismShoreDocument/saveAccidentMeeting'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
