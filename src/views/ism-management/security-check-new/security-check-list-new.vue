<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            clearable
            v-model="searchObj.inspectionType"
            label="检查类型"
            outlined
            dense
            :items="insTypes"
          ></v-select>
        </v-col> -->
      </template>
      <template #btns>
        <!-- <v-btn
          outlined
          color="success"
          class="mx-1"
          @click="addNonstandartReport"
          v-permission="['安全检查:新增险情']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增险情项目
        </v-btn> -->
        <v-btn
          outlined
          color="success"
          class="mx-1"
          @click="dialog = true"
          v-permission="['船舶检查:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          outlined
          class="mx-1"
          @click="finish"
          v-permission="['船舶检查:结案']"
          :disabled="selected.status !== '3' || selected.isClosed === true"
        >
          <v-icon left>mdi-contain-end</v-icon>
          结案
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delSecCheck"
          v-permission="['船舶检查:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.inspectionType`]="{ item }">
        {{
          ['登轮检查', '船舶自查', 'PSC检查', 'FSC检查'][item.inspectionType]
        }}
      </template>
      <template v-slot:[`item.isClosed`]="{ item }">
        <v-chip small :color="closeColors[item.isClosed ? 1 : 0]" :dark="true">
          {{ item.isClosed ? '是' : '否' }}
        </v-chip>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{
            item.status == 2
              ? `待[${item.businessStatus}]审批`
              : statuses[item.status]
          }}
        </v-chip>
      </template>
    </v-table-searchable>
    <add-security-dialog-new
      @success="success"
      v-model="dialog"
    ></add-security-dialog-new>
  </v-container>
</template>
<script>
import addSecurityDialogNew from './private/add-security-dialog-new.vue'
// attachmentRecords	附件列表，限制最多5个文件	array	CommonAttachment
// bonus	检查考评奖金额	number
// captain	船长	string
// cheifEngineer	轮机长	string
// cnShipPort	中文船名	string
// enShipName	英文船名	string
// id	物理主键	string
// inspectionTime	检查时间	string
// inspectionType	检查类型	string
// inspector	检查人员	string
// isClosed	是否结案	boolean
// marineSupervisor	海务主管
export default {
  components: { addSecurityDialogNew },
  name: 'security-check-list-new',
  created() {
    this.tableName = '安全检查记录'
    this.reqUrl = '/business/seaAffairs/securityCheck/record/page'
    this.headers = [
      { text: '中文船名', value: 'cnShipName' },
      { text: '船长', value: 'captain' },
      { text: '轮机长', value: 'cheifEngineer' },
      { text: '海务主管', value: 'marineSupervisor' },
      { text: '检查时间', value: 'inspectionTime' },
      { text: '受检港口', value: 'checkPort' },
      { text: '检查人员', value: 'inspector' },
      { text: '检查类型', value: 'inspectionType' },
      { text: '是否结案', value: 'isClosed' },
      { text: '审批状态', value: 'status' },
      // { text: '附件', value: 'attachmentRecords' },
    ]
    this.insTypes = [
      { text: '登轮检查', value: '0' },
      { text: '船舶自查', value: '1' },
      { text: 'PSC检查', value: '2' },
      { text: 'FSC检查', value: '3' },
    ]
    this.pushParams = { name: 'security-check-detail-new' }
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '检查日期',
      interval: true,
    }
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
    this.closeColors = ['warning', 'success', 'warning']
  },

  data() {
    return {
      selected: false,
      searchObj: {},
      formData: {},
      dialog: false,
    }
  },

  methods: {
    // async save() {
    //   if (!this.$refs.form.validate()) {
    //     return
    //   }
    //   this.formData = {}
    //   const url = '/business/seaAffairs/securityCheck/record/saveRecord'
    //   const { errorRaw, data } = await this.postAsync(url, {
    //     ...this.formData,
    //   })
    //   if (!errorRaw) {
    //     this.$emit('change', false)
    //     this.$emit('success', data)
    //   }
    // },
    success(id) {
      this.$router.push({ name: 'security-check-detail-new', params: { id } })
    },
    async delSecCheck() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/securityCheck/record/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },

    async finish() {
      if (!(await this.$dialog.msgbox.confirm('确定结案此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/securityCheck/record/finish',
        {
          id: this.selected.id,
          // isClosed: true,
        },
      )
      if (errorRaw) return
      this.$dialog.message.success(`结案成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    addNonstandartReport() {
      // this.$store.commit('emitBussiness', {
      //   businessType: 'shipAccident',
      //   businessId: this.detail.id,
      //   templateId: this.nonstandartReportTempId,
      // })
      this.$router.push({
        name: 'dept-report-info-detail1-new',
        //'report-emit-detail',
        // params: { id: this.nonstandartReportTempId },
        params: { id: 'securityCheck_Non_3_new' },
      })
    },
  },

  mounted() {},
}
</script>

<style></style>
