<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
    @close="cancelDialog"
    :beforeClose="onBeforeClose"
  >
    <v-card>
      <v-card-title>
        新增问题项
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  label="主要问题简述Summary of problems"
                  dense
                  outlined
                  v-model="formData.problemDescription"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-dict-select
                  label="整改期限"
                  dict-type="security_measures"
                  :rules="[rules.required]"
                  v-model="formData.measures"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="整改责任人"
                  dense
                  outlined
                  v-model="formData.personLiable"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  label="问题类别"
                  v-model="formData.questionType"
                  outlined
                  :rules="[rules.required]"
                  dense
                  :items="questionTypes"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dict-select
                  dense
                  :dict-type="this.dict_type"
                  label="项目类别"
                  v-model="formData.itemType"
                  :rules="[rules.required]"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-checkbox
                  class="mt-0"
                  :readonly="formData.questionType === '1'"
                  v-model="formData.isCorrected"
                  :label="`已纠正: ${formData.isCorrected ? '是' : '否'}`"
                ></v-checkbox>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="完成日期Completion date"
                  v-model="formData.completionDate"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  label="船管验证人Verifier(旧)"
                  dense
                  outlined
                  readonly
                  v-model="formData.verifier"
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-select
                  label="船管验证人Verifier"
                  v-model="formData.verifierNew"
                  outlined
                  :rules="[rules.required]"
                  dense
                  :items="verifierNews"
                ></v-select>
                <!-- <v-text-field
                  label="船管验证人Verifier"
                  dense
                  outlined
                  v-model="formData.verifierNew"
                ></v-text-field> -->
              </v-col>
              <v-col cols="12" md="3">
                <v-checkbox
                  class="mt-0"
                  v-model="formData.verifierStatus"
                  :label="`已验证: ${formData.verifierStatus ? '是' : '否'}`"
                ></v-checkbox>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="岸基验证日期Date"
                  v-model="formData.operationDate"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="备注Remark"
                  dense
                  outlined
                  v-model="formData.remark"
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="12">
                <v-textarea
                  label="备注Remark"
                  dense
                  rows="3"
                  outlined
                  v-model="formData.remark"
                ></v-textarea>
              </v-col> -->
              <!-- <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col> -->
            </v-row>
            <v-row>
              <v-col cols="12">
                <v-attach-list
                  title="问题照片"
                  :attachments="formData.problemPhotos"
                  @change="changeAttachment1"
                  :ship-code="formData.shipCode"
                ></v-attach-list>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12">
                <v-attach-list
                  title="纠正证据"
                  :attachments="formData.correctEvidences"
                  @change="changeAttachment"
                  :ship-code="formData.shipCode"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          v-if="formData.questionType === '1'"
          depressed
          :disabled="!!nonstandartReportId"
          @click="addNonstandart"
        >
          {{ !!nonstandartReportId ? '已填加' : '添加不符合报告' }}
        </v-btn>
        <v-btn depressed color="primary" @click="save">确定</v-btn>
        <v-btn depressed color="primary" @click="saveBack">验证提醒</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'add-question-item-new',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
    // this.formData = {}
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({
        isCorrected: false,
        // problemPhotos: [],
        // correctEvidences: [],
      }),
    },
    nonstandartReportTempId: String,
    checkType: [String, Number],
  },
  created() {
    this.questionTypes = [
      { text: '无缺陷', value: '0' },
      { text: '缺陷项', value: '1' },
      { text: '建议项', value: '2' },
    ]
    this.verifierNews = [
      { text: '海务主管', value: '0' },
      { text: '机务主管', value: '1' },
      // { text: '建议项', value: '2' },
    ]
  },
  data() {
    return {
      dialog: false,
      dict_type: 'security_ques_types',
      formData: {
        isCorrected: false,
        // problemPhotos: [],
        // correctEvidences: [],
        // attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      questionId: '',
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      // this.$refs?.form?.formData?.reset()
      this.$refs?.form?.resetValidation()
      this.questionTypes = [
        { text: '无缺陷', value: '0' },
        { text: '缺陷项', value: '1' },
      ]
      // <!--"`${this.checkType === '1' ? 'security_ques_types_psc' : 'security_ques_types'}`"-->
      if ('2' == this.checkType) this.dict_type = 'security_ques_types_psc'
      if (['0', '1'].includes(this.checkType))
        this.questionTypes.push({ text: '建议项', value: '2' })
      this.$nextTick(() => {
        this.formData = { ...this.initialData }
        this.questionId =
          this.formData.id || Math.floor(Math.random() * 1000 + 1)
        this.updateTaskPromptMassage(this.$route.params.id)
      })
    },
    // close() {
    //   console.log('close 1') //提示无效
    //   this.questionId = ''
    // },
    // closed() {
    //   console.log('close 2') //提示无效
    //   this.questionId = ''
    // },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
    nonstandartReportId() {
      return (
        this.$store.state.reportParams.businessParams.find(
          (b) => b.id === this.questionId,
        )?.reportId || this.formData.systemReportId
      )
    },
    addnonstandartReportId() {
      return (
        this.$store.state.reportParams.businessParams.find(
          (b) => b.id === this.questionId,
        )?.reportId && !this.formData.systemReportId
      )
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.correctEvidenceIds = attachmentIds
    },
    changeAttachment1(attachmentIds) {
      this.formData.problemPhotoIds = attachmentIds
    },
    closeForm() {
      if (this.formData.questionType == 1 && !this.nonstandartReportId) {
        //this.$dialog.message.warning('请填写不符合报告')
        this.$dialog.message.warning('未添加不符合报告请点击确认按钮')
        return
      }
      if (this.formData.questionType == 1 && this.addnonstandartReportId) {
        //this.$dialog.message.warning('请填写不符合报告')
        this.$dialog.message.warning('请点击确认按钮保存记录！')
        return
      }
      // console.log('closeForm 1') //提示有效
      this.questionId = ''
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      // if (this.formData.questionType == 1 && !this.nonstandartReportId) {
      //   this.$dialog.message.warning('请填写不符合报告')
      //   return
      // }
      const url = '/business/seaAffairs/securityCheck/detail/addQuestion'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        securityCheckId: this.$route.params.id,
        systemReportId: this.nonstandartReportId,
      })
      if (!errorRaw) {
        this.questionId = ''
        this.$emit('change', false)
        this.$emit('success')
        // this.$refs.form.reset()
      }
      // this.$refs.form.reset()
    },
    async saveBack() {
      if (!this.$refs.form.validate()) {
        return
      }
      if (
        !(await this.$dialog.msgbox.confirm(
          '验证提醒会向岸基主管发送邮件提醒，请确认是否需要提醒？',
        ))
      )
        return
      this.formData.validate
      // if (this.formData.questionType == 1 && !this.nonstandartReportId) {
      //   this.$dialog.message.warning('请填写不符合报告')
      //   return
      // }
      const url = '/business/seaAffairs/securityCheck/detail/backQuestion'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        securityCheckId: this.$route.params.id,
        systemReportId: this.nonstandartReportId,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        // this.$refs.form.reset()
      }
      // this.$refs.form.reset()
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/updateByConfirmPerosn',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    // addNonstandart() {
    //   this.$store.commit('emitBussiness', {
    //     businessType: 'securityCheck',
    //     businessId: this.$route.params.id,
    //     templateId: this.nonstandartReportTempId,
    //     id: this.questionId,
    //   })
    //   this.$router.push({
    //     name: 'report-emit-detail',
    //     params: { id: this.nonstandartReportTempId },
    //   })
    // },
    addNonstandart() {
      console.log(this.initialData.shipCode)
      this.$store.commit('emitBussiness', {
        businessType: 'securityCheck_Non',
        businessId: this.$route.params.id,
        templateId: this.nonstandartReportTempId,
        id: this.questionId,
        otherParams: {
          shipCode: this.initialData.shipCode,
          // type: this.detail.accidentType,
          // shipFlag:
          //   this.initialData.shipBaseMixOutputDTO.flagPort +
          //   ',' +
          //   this.initialData.shipBaseMixOutputDTO.flagState,
          // shipName: this.initialData.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        //'report-emit-detail',
        name: 'dept-report-info-detail1-new',
        params: {
          id: this.nonstandartReportTempId,
          businessId1: this.$route.params.id, //不符合报告中与businessId验证获取当前触发的业务关系
        },
      })
    },
    async cancelDialog() {
      console.log('关闭按钮触发。。。')
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
    },
    // 取消默认关闭弹框事件
    onBeforeClose(action, done) {
      console.log('关闭按钮触发2。。。')
      return done(false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
