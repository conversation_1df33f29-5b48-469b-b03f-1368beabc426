<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶检查:编辑']"
      :title="`安全检查-${
        ['', '未提交', '审批中', '审批通过', '驳回'][detail.status]
      }`"
      tooltip="船舶安全检查"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        detail.status === '2' &&
        (!detail.auditParams || detail.auditParams.taskId)
      "
      :can-save="isSave"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:custombtns>
        <v-btn
          v-if="!isShip && (detail.status == 1 || detail.status == 4)"
          width="90"
          tile
          @click="saveprompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['船舶检查:保存并通知船端']"
        >
          保存并通知船端
        </v-btn>
        <v-btn
          v-if="isShip && (detail.status == 1 || detail.status == 4)"
          width="90"
          tile
          @click="saveprompt(backRouteName)"
          color="success"
          small
          class="mx-1"
          v-permission="['船舶检查:保存并通知岸端']"
        >
          保存并通知岸端
        </v-btn>
        <v-btn
          v-if="detail.status == 1 || detail.status == 4"
          width="90"
          tile
          @click="submit"
          color="success"
          small
          class="mx-1"
          v-permission="['船舶检查:保存并提交']"
        >
          保存并提交
        </v-btn>
        <!-- </template>
      <template v-if="detail.status == 3" v-slot:titlebtns> -->
        <!-- <v-btn
          v-if="detail.status == 3"
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn> -->
        <v-btn
          v-if="detail.status == 3"
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['船舶检查:下载部门报表']"
        >
          总表导出
        </v-btn>
        <v-btn
          v-if="detail.status == 3"
          width="90"
          tile
          :href="downloadUrl1"
          color="info"
          small
          class="mx-1"
          v-permission="['船舶检查:下载部门报表']"
        >
          船东报表导出
        </v-btn>
        <v-btn
          v-if="detail.status == 3"
          width="90"
          tile
          :href="downloadUrl2"
          color="info"
          small
          class="mx-1"
          v-permission="['船舶检查:下载部门报表']"
        >
          船管报表导出
        </v-btn>
      </template>
      <template #船舶基本信息>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="detail.shipCode"
                  readonly
                ></v-ship-select>
              </v-col>
              <v-col
                cols="12"
                md="2"
                v-for="(h, i) in 船舶基本信息字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <!-- <template #检查相关内容按钮>
        <v-col
          v-if="detail.inspectionType === '0' || detail.inspectionType === '1'"
        >
          <v-btn
            outlined
            tile
            :disabled="!!securityCheckReportId"
            small
            color="success"
            class="mx-1"
            @click.stop="createSecNew"
            v-permission="['安全检查:添加安全检查部门报表']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            添加安全检查部门报表
          </v-btn>
        </v-col>
        <v-col
          v-if="detail.inspectionType === '2' || detail.inspectionType === '3'"
        >
          <v-btn
            outlined
            tile
            :disabled="!!securityCheckReportId"
            small
            color="success"
            class="mx-1"
            @click.stop="createInsp"
            v-permission="['安全检查:添加安全检查部门报表']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            添加港口国/船旗国检查报告
          </v-btn>
        </v-col>
      </template> -->
      <template #检查相关内容>
        <v-form :readonly="detail.status === '3'" ref="form">
          <v-container fluid>
            <!-- <v-row v-if="!!securityReport.reportName">
              <v-col cols="12" md="3">
                <b>报表名称:</b>
                <router-link
                  :to="{
                    name:
                      detail.inspectionType === '0' ||
                      detail.inspectionType === '1'
                        ? 'dept-report-security-check' //'dept-report-detail'
                        : 'dept-report-inspection-detail',
                    params: { id: detail.systemReportId },
                  }"
                >
                  {{ securityReport.reportName }}
                </router-link>
              </v-col>
              <v-col cols="12" md="3">
                <b>上传人:</b>
                {{ securityReport.poster }}
              </v-col>
              <v-col cols="12" md="3">
                <b>上传时间:</b>
                {{ securityReport.postTime }}
              </v-col>
              <v-col cols="12" md="3">
                <b>状态:</b>
                {{ securityReport.status }}
              </v-col>
            </v-row>
            <v-row justify="center" v-else>
              <v-col
                v-if="
                  detail.inspectionType === '0' || detail.inspectionType === '1'
                "
                cols="2"
                class="subtitle-2 font-weight-light"
              >
                安全检查部门报表:暂未提交
              </v-col>
              <v-col
                v-if="
                  detail.inspectionType === '2' || detail.inspectionType === '3'
                "
                cols="2"
                class="subtitle-2 font-weight-light"
              >
                港口国/船旗国检查报告:暂未提交
              </v-col>
            </v-row> -->
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col class="py-1" cols="12" md="2">
                <v-select
                  readonly
                  label="检查类型"
                  v-model="detail.inspectionType"
                  :items="insTypes"
                  outlined
                  :rules="[rules.required]"
                  dense
                ></v-select>
              </v-col>
              <v-col
                cols="12"
                md="2"
                class="py-1"
                v-if="detail.inspectionType === '2'"
              >
                <v-select
                  readonly
                  label="风险等级"
                  v-model="detail.riskLevel"
                  :items="风险等级"
                  outlined
                  dense
                ></v-select>
              </v-col>
              <v-col
                class="py-1"
                cols="12"
                md="2"
                v-for="(h, i) in 检查相关内容字段"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  outlined
                ></v-text-field>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  dense
                  :label="h.label"
                  outlined
                  :readonly="
                    (detail.inspectionType === '2' &&
                      h.value === 'nextInspectionTime') ||
                    h.value === 'inspectionTime'
                  "
                  :hidden="
                    !detail.inspectionType != '2' &&
                    (h.value === 'nextInspectionTime' ||
                      h.value === 'marineReviewDate')
                  "
                ></vs-date-picker>
                <v-dict-select
                  v-else-if="h.value === 'dispatchCompany'"
                  dict-type="ship_crew_conpany"
                  :label="h.label"
                  v-model="detail.dispatchCompany"
                  dense
                  outlined
                ></v-dict-select>
                <v-text-field
                  v-else-if="
                    h.value === 'bonus' &&
                    ['2', '3'].includes(detail.inspectionType)
                  "
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <template v-for="(h, i) in 长文本字段">
                <v-col
                  v-if="
                    h.value !== 'bonusReply' ||
                    ['2', '3'].includes(detail.inspectionType)
                  "
                  :key="h.label"
                  class="text-right body-2 caption"
                  cols="1"
                >
                  {{ h.label }}
                </v-col>
                <v-col
                  v-if="
                    h.value !== 'bonusReply' ||
                    ['2', '3'].includes(detail.inspectionType)
                  "
                  :key="i"
                  cols="11"
                >
                  <v-textarea
                    row-height="19"
                    class="ml-1"
                    v-model="detail[h.value]"
                    dense
                    rows="5"
                    filled
                    auto-grow
                  ></v-textarea>
                </v-col>
              </template>
            </v-row>
            <v-row>
              <v-col
                class="py-1"
                cols="12"
                md="2"
                v-for="(h, i) in 检查相关内容字段2"
                :key="i"
              >
                <v-text-field
                  v-if="h.type === 'string'"
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  outlined
                ></v-text-field>
                <vs-date-picker
                  v-else-if="h.type === 'date'"
                  v-model="detail[h.value]"
                  dense
                  :label="h.label"
                  outlined
                  :readonly="
                    (detail.inspectionType === '2' &&
                      h.value === 'nextInspectionTime') ||
                    h.value === 'inspectionTime'
                  "
                  :hidden="
                    !detail.inspectionType != '2' &&
                    (h.value === 'nextInspectionTime' ||
                      h.value === 'marineReviewDate')
                  "
                ></vs-date-picker>
                <v-text-field
                  v-else-if="
                    h.value === 'bonus' &&
                    ['2', '3'].includes(detail.inspectionType)
                  "
                  v-model="detail[h.value]"
                  :label="h.label"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #问题项按钮>
        <v-btn
          v-if="!(detail.inspectionType != '1' && isShip)"
          @click="createQues"
          outlined
          :disabled="!canEdit"
          :hidden="detail.inspectionType != '1' && isShip"
          tile
          small
          color="success"
          class="mx-1"
          v-permission="['问题项:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedQues"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editQues"
          v-permission="['问题项:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedQues"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editMesure"
          v-permission="['问题项:采取措施']"
        >
          <v-icon left>mdi-pencil</v-icon>
          采取措施
        </v-btn>
        <v-btn
          :disabled="!selectedQues || !canEdit"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delQues"
          v-permission="['问题项:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #问题项>
        <v-table-list-new
          v-model="selectedQues"
          :headers="questionHeaders"
          :items="questionList"
          :search-dicts="searchDicts"
          @dbclick="editQues"
        >
          <template v-slot:[`item.questionType`]="{ item }">
            {{ ['无缺陷', '缺陷项', '建议项'][item.questionType] }}
          </template>
          <template v-slot:[`item.problemPhotoIds`]="{ item }">
            <v-btn
              @click.stop="openAttachmentDialog(item.problemPhotos)"
              dark
              x-small
              color="primary"
              elevation="0"
            >
              {{ item.problemPhotos.length }}
            </v-btn>
          </template>
          <template v-slot:[`item.correctEvidenceIds`]="{ item }">
            <v-btn
              @click.stop="openAttachmentDialog(item.correctEvidences)"
              dark
              x-small
              color="primary"
              elevation="0"
            >
              {{ item.correctEvidences.length }}
            </v-btn>
          </template>
          <template v-slot:[`item.verifierStatus`]="{ item }">
            {{ item.verifierStatus ? '是' : '否' }}
          </template>
          <template v-slot:[`item.isCorrected`]="{ item }">
            {{ item.isCorrected ? '是' : '否' }}
          </template>
          <template v-slot:[`item.reportStatus`]="{ item }">
            {{ ['', '草稿', '审批中', '已审批', '已驳回'][item.reportStatus] }}
          </template>
          <template v-slot:[`item.reportName`]="{ item }">
            <router-link
              :to="{
                name: 'dept-report-info-detail1-new', //'dept-report-detail',
                params: { id: item.systemReportId || '2' },
              }"
            >
              {{ !!item.systemReportId ? '不符合报告-' : '' }}
              {{ item.reportName }}
            </router-link>
          </template>
        </v-table-list-new>
      </template>
      <template #填写进度>
        <v-table-list
          v-model="selectedQues"
          :headers="massageHeaders"
          :items="fillList"
          :search-dicts="searchDicts"
        >
          <template v-slot:[`item.businessName`]="{ item }">
            <router-link
              :to="{
                name: item.route,
                params: { id: item.businessId },
                //this.$router.resolve({
                //  name: item.processDefinitionName,
                //}).route.meta.permissionId,
                //'dept-report-detail',
                //params: { id: item.systemReportId },
              }"
            >
              {{ item.businessName }}
            </router-link>
          </template>
          <template v-slot:[`item.status`]="{ item }">
            <v-chip small :color="statusColors[item.status]" :dark="true">
              {{
                item.status == 1 || (item.status == 0 && !item.isShip)
                  ? `待填写`
                  : '已填写'
              }}
            </v-chip>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <add-question-item-new
      v-model="dialog"
      :initialData="initialData"
      @success="questionSuccess"
      :nonstandartReportTempId="nonstandartReportTempId"
      :check-type="detail.inspectionType"
    ></add-question-item-new>
    <ship-measure-dialog-new
      v-model="dialog2"
      :initialData="selectedQues || {}"
      @success="questionSuccess"
    ></ship-measure-dialog-new>
    <v-dialog v-model="attachmentDialog" max-width="700" hide-overlay>
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
// bonus	检查考评奖金额	number
// bonusReply	检查考评奖批复	string
// briefComments	简要评语	string
// businessStatus	业务状态	string
// captain	船长	string
// captainAutograph	船长签名	string
// checkPort	受检港口	string
// cheifEngineer	轮机长	string
// dpReview	DP审核意见	string
// dpReviewDate	DP审核日期	string(date-time)
// firstMate	大副	string
// id	物理主键	string
// inspectionProcess	检查经过	string
// inspectionTime	检查时间	string(date-time)
// inspectionType	检查类型	string
// inspector	检查人员	string
// inspectorAutograph	检查人员签名	string
// isClosed	是否结案	boolean
// marineReviewDate	海务主管审核日期	string(date-time)
// marineSupervisor	海务主管	string
// marineSupervisorAutograph	海务主管签名	string
// secondMate	二副	string
// shipCode	船舶代码	string
// status	流程状态	string
// supervisorReview	主管审核意见	string
// systemReportId	安全检查表id(部门报表id)	string
// thridMate	三副	string

// correctEvidenceIds	纠正证据（附件列表）	string
// id	物理主键	string
// isCorrected	是否纠正	boolean
// itemType	项目类别	string
// measures	措施	string
// number	序号	string
// personLiable	责任人	string
// problemDescription	问题描述	string
// questionType	问题类别(非字典) 0-无缺陷 1-缺陷项 2-建议项	string
// reportName	报表名称	string
// reportStatus	报表流程状态	string
// securityCheckId	安全检查id	string
// systemReportId	不符合报告id	string
import dictHelper from '@/mixin/dictHelper'
import addQuestionItemNew from './private/add-question-item-new.vue'
import ShipMeasureDialogNew from './private/ship-measure-dialog-new.vue'
export default {
  components: { addQuestionItemNew, ShipMeasureDialogNew },
  mixins: [dictHelper],
  name: 'security-check-detail-new',
  created() {
    this.backRouteName = 'security-check-list-new'
    // this.subtitles = [
    //   '填写进度',
    //   '船舶基本信息',
    //   '问题项',
    //   '检查相关内容',
    //   '确认进度',
    // ]
    this.船舶基本信息字段 = [
      { label: '建造时间', value: 'buildDate', type: 'string' },
      { label: '船长', value: 'captain', type: 'string' },
      { label: '轮机长', value: 'cheifEngineer', type: 'string' },
      { label: '大副', value: 'firstMate', type: 'string' },
      { label: '二副', value: 'secondMate', type: 'string' },
      { label: '三副', value: 'thridMate', type: 'string' },
      { label: '海务主管', value: 'marineSupervisor', type: 'string' },
      { label: '机务主管', value: 'techManager', type: 'string' },
      { label: 'I办主管', value: 'ismManager', type: 'string' },
      { label: '安技主管', value: 'ostManager', type: 'string' },
      { label: '通导主管', value: 'cnitManager', type: 'string' },
      // { label: '航线', value: 'route', type: 'string' },
    ]
    this.massageHeaders = [
      // { text: '业务类型', value: 'businessName' },
      // { text: '船舶', value: 'shipName' },
      { text: '填写人', value: 'inputPerson' },
      { text: '备注信息', value: 'businessDesc' },
      { text: '填写状态', value: 'status' },
      { text: '时间', value: 'inputDate' },
    ]
    this.检查相关内容字段 = [
      { label: '受检港口', value: 'checkPort', type: 'string' },
      { label: '派员公司', value: 'dispatchCompany', type: 'select' },
      { label: '检查人员', value: 'inspector', type: 'string' },
      { label: '检查时间', value: 'inspectionTime', type: 'date' },
      // { label: '下次窗口日期', value: 'nextInspectionTime', type: 'date' },
      // { label: '海务主管审核日期', value: 'marineReviewDate', type: 'date' },
      // { label: 'DP审核日期', value: 'dpReviewDate', type: 'date' },
      // 仅为PSC/FSC检查时有
      // { label: 'PSC/FCS检查考评奖金额', value: 'bonus', type: 'number' },
      // { label: '审核人', value: 'reviewedBy', type: 'string' },
      // { label: '船长签名', value: 'captainAutograph', type: 'string' },
      // { label: '检查人员签名', value: 'inspectorAutograph', type: 'string' },
      // { label: '检查人员审核日期', value: 'inspectorDate', type: 'date' },
      // {
      //   label: '海务主管签名',
      //   value: 'marineSupervisorAutograph',
      //   type: 'string',
      // },
    ]
    this.检查相关内容字段2 = [
      { label: '检查人员签名', value: 'inspectorAutograph', type: 'string' },
      { label: '检查人员审核日期', value: 'inspectorDate', type: 'date' },
    ]
    this.长文本字段 = [
      {
        label:
          '船舶检查情况综合描述Comprehensive description of vessel inspection（包括人员方面including personnel）',
        value: 'inspectionProcess',
      },
      // { label: '简要评语', value: 'briefComments' },
      // { label: '主管审核意见', value: 'supervisorReview' },
      // { label: 'DP审核意见', value: 'dpReview' },
      // { label: '检查考评奖批复', value: 'bonusReply' },
    ]
    this.questionHeaders = [
      { text: '序号', value: 'number' },
      { text: '主要问题简述Summary of problems', value: 'problemDescription' },
      // { text: '项目类别', value: 'itemType' },
      { text: '问题类别', value: 'questionType' },
      { text: '是否已验证', value: 'verifierStatus' },
      { text: '整改期限', value: 'measures' },
      { text: '整改责任人', value: 'personLiable' },
      { text: '已采取措施', value: 'measuresDone' },
      {
        text: '完成日期Completion date',
        value: 'completionDate',
        type: 'date',
      },
      { text: '船管验证人Verifier', value: 'verifier' },
      { text: '日期Date', value: 'operationDate', type: 'date' },
      // { text: '是否纠正', value: 'isCorrected' },
      { text: '问题照片', value: 'problemPhotoIds' },
      { text: '纠正证据', value: 'correctEvidenceIds' },
      { text: '不符合报表', value: 'reportName' },
      { text: '报表状态', value: 'reportStatus' },
    ]
    this.reportHeaders = [
      { text: '报表名称', value: 'reportName' },
      { text: '上传人', value: 'poster' },
      { text: '上传时间', value: 'postTime' },
      { text: '状态', value: 'status' },
    ]
    this.insTypes = [
      { text: '登轮检查', value: '0' },
      { text: '船舶自查', value: '1' },
      { text: 'PSC检查', value: '2' },
      { text: 'FSC检查', value: '3' },
    ]
    this.风险等级 = [
      { text: '低风险', value: '0' },
      { text: '标准风险', value: '1' },
      { text: '高风险', value: '2' },
    ]
    // this.searchDicts = [
    //   { dicType: 'security_measures', key: 'measures' },
    //   { dicType: 'security_ques_types', key: 'itemType' },
    // ]
    this.attachmentHeader = [
      { text: '名称', value: 'name' },
      { text: '大小(kb)', value: 'fileSize' },
      { text: '上传时间', value: 'createTime' },
      { text: '上传人', value: 'userName' },
    ]
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = [
      'info',
      'info',
      'info',
      'info',
      'warning',
      'warning',
      'warning',
      'warning',
      'warning',
      'success',
      'error',
    ]
  },
  computed: {
    // TODO:待不符合报告添加完毕，提交时将进行校验，未填写的不符合报告将不会被提交
    canSubmit() {
      return (
        (!!this.securityCheckReportId &&
          this.questionList.filter((i) => i.questionType == 1 && !i.reportName)
            .length === 0 &&
          !this.detail.auditParams) ||
        !!this.detail.auditParams?.isReject
      )
    },
    isSave() {
      return this.$route.params.id == 'new'
        ? true
        : this.detail.status == '0' ||
          this.detail.status == '1' ||
          this.detail.status == '' ||
          this.detail.status == '4' ||
          this.detail.status == null
        ? true
        : false
    },
    isFilled() {
      return this.fillList.filter((i) => i.status == 1).length === 0
    },
    canEdit() {
      return ['1', '4'].includes(this.detail.status)
    },
    isShip() {
      //console.log(this.$local.data.get('userInfo').isShipSyS)//船端时返回true
      return this.$local.data.get('userInfo').isShipSyS
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/securityCheck/exportById?id=${this.detail.id}`
        : ''
      //''
    },
    downloadUrl1() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/securityCheck/exportById?id=${this.detail.id}&type=1`
        : ''
      //''
    },
    downloadUrl2() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/securityCheck/exportById?id=${this.detail.id}&type=2`
        : ''
      //''
    },
    nonstandartReportTempId() {
      return this.reportTypeMap?.find(
        (i) => i.dictLabel === 'nonstandartReport',
      )?.dictValue
    },
    securityCheckReportTempId() {
      return this.reportTypeMap?.find((i) => i.dictLabel === 'securityCheck')
        ?.dictValue
    },
    securityCheckReportId() {
      return (
        this.detail.systemReportId ||
        this.$store.state.reportParams.businessParams.find(
          (b) => b.templateId === this.securityCheckReportTempId,
        )?.reportId
      )
    },
    // securityCheckReportId1() {
    //   return (
    //     this.detail.systemReportId ||
    //     this.$store.state.reportParams.businessParams.find(
    //       (b) => b.templateId === this.securityCheckReportTempId,
    //     )?.reportId
    //   )
    // },
  },
  watch: {
    load() {
      console.log('open ...')
      if ('2' == this.detail.inspectionType)
        this.searchDicts = [
          { dicType: 'security_measures', key: 'measures' },
          // { dicType: 'security_ques_types_psc', key: 'itemType' },
        ]
    },
    securityCheckReportId(value) {
      if (value) {
        this.detail.systemReportId = value
        if (
          this.detail.inspectionType === '0' ||
          this.detail.inspectionType === '1'
        ) {
          this.loadReportInfo(value, 's')
        } else if (
          this.detail.inspectionType === '2' ||
          this.detail.inspectionType === '3'
        ) {
          this.loadReportInfo(value, 'i')
        }
      }
    },
    // inspectionReportId(value) {
    //   if (value) {
    //     this.detail.systemReportId = value
    //     this.loadReportInfo(value, 'i')
    //   }
    // },
    // 'detail.shipCode'(val) {
    //   if (val) {
    //     this.loadShipInfo(val)
    //   }
    // },
  },
  data() {
    return {
      subtitles: [
        '填写进度',
        '船舶基本信息',
        '检查相关内容',
        '问题项',
        // '确认进度',
      ],
      detail: { status: 0, inspectionType: 0, auditParams: '' },
      fillList: [],
      questionList: [],
      reportTypeMap: [],
      securityReport: {},
      selectedQues: false,
      dialog: false,
      dialog2: false,
      initialData: {},
      attachmentDialog: false,
      attachments: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchDicts: [
        { dicType: 'security_measures', key: 'measures' },
        // { dicType: 'security_ques_types_psc', key: 'itemType' },
      ],
    }
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/securityCheck/detail/updateDetail',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) goBack()
      goBack()
    },
    async saveprompt(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/securityCheck/detail/updateDetailPrompt',
        this.detail,
      )
      if (notMove) return this.detail.id
      if (!errorRaw) {
        this.$dialog.message.success(`保存并通知船端保存成功，已推送船端提醒`)
        // this.updateTaskPromptMassage(this.detail.id)
        goBack()
      }
      goBack()
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      // if (!this.securityCheckReportId) {
      //   this.$dialog.message.error('请先上传安全检查报告')
      //   return
      // }
      if (
        (this.detail.status == '1' || this.detail.status == '4') &&
        !this.isFilled &&
        !(await this.$dialog.msgbox.confirm(
          '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return
      // if (
      //   !(
      //     this.questionList.filter((i) => i.questionType == 1 && !i.reportName)
      //       .length === 0
      //   )
      // ) {
      //   await this.$dialog.msgbox.confirm(
      //     '有问题项（缺陷项）未提交不符合报告，请确认所有问题项已录入完成后再提交！',
      //   )
      //   return
      // }
      if (
        !(
          this.questionList.filter(
            (i) => i.questionType == 1 && !i.systemReportId,
          ).length === 0
        )
      ) {
        await this.$dialog.msgbox.confirm(
          '有问题项（缺陷项）未提交不符合报告，请确认所有问题项已录入完成后再提交！',
        )
        return
      }
      // if (
      //   !(
      //     this.questionList.filter(
      //       (i) => !i.verifier || i.verifier == null || i.verifier.trim() == '',
      //     ).length === 0
      //   )
      // ) {
      //   await this.$dialog.msgbox.confirm(
      //     '有问题项未填写验证人，请确认所有问题项已录入验证人后再提交！',
      //   )
      //   return
      // }
      if (!(this.questionList.filter((i) => !i.verifierStatus).length === 0)) {
        await this.$dialog.msgbox.confirm(
          '有问题项未验证完成，请确认所有问题项已验证完成后再提交！',
        )
        return
      }

      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/securityCheck/process/submit',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/securityCheck/record/${this.$route.params.id}`,
      )
      this.detail = data
      // if ('2' == this.detail.inspectionType)
      //   this.searchDicts = [
      //     { dicType: 'security_measures', key: 'measures' },
      //     { dicType: 'security_ques_types_psc', key: 'itemType' },
      //   ]

      this.attachmentRecords = data.attachmentRecords
      this.reportTypeMap = await this.getDictByType('report_type_mapping')
      if (this.canEdit)
        this.subtitles = [
          '填写进度',
          '船舶基本信息',
          '检查相关内容',
          '问题项',
          // '确认进度',
        ]
      else if (this.detail.status === '2')
        this.subtitles = [
          '填写进度',
          '船舶基本信息',
          '检查相关内容',
          '问题项',
          // '确认进度',
        ]
      else
        this.subtitles = [
          '填写进度',
          '船舶基本信息',
          '检查相关内容',
          '问题项',
          // '确认进度',
        ]

      await this.loadQuestionList()
      await this.loadFillList()
    },
    async loadReportInfo(reportId, type) {
      console.log(reportId)
      if (type === 's') {
        // const { data } = await this.getAsync(
        //   '/business/seaAffairs/deptReport/getReportDetailById',
        //   { reportId },
        // )
        // const { deptReport } = data
        // deptReport['status'] = ['', '草稿', '审批中', '已审批', '已驳回'][
        //   deptReport.status
        // ]
        // this.securityReport = deptReport

        const { data } = await this.getAsync(
          '/business/seaAffairs/SecurityCheckReport/getDetailById',
          // '/business/seaAffairs/deptReport/getReportDetailById',
          { id: reportId },
        )
        console.log(data)
        // const { deptReport } = data
        // deptReport['status'] = ['', '草稿', '审批中', '已审批', '已驳回'][
        //   deptReport.status
        // ]
        const deptReport = {
          id: data.id,
          reportName: data.checkPort + ' ' + data.inspectionTime,
          poster: data.posterName,
          postTime: data.createTime,
          status: ['', '草稿', '审批中', '已审批', '已驳回'][data.status],
          // type: data.type,
        }
        this.securityReport = deptReport
      } else if (type === 'i') {
        const { data } = await this.getAsync(
          '/business/seaAffairs/DeptReportInspection/getDetailById',
          // '/business/seaAffairs/deptReport/getReportDetailById',
          { id: reportId },
        )
        console.log(data)
        // const { deptReport } = data
        // deptReport['status'] = ['', '草稿', '审批中', '已审批', '已驳回'][
        //   deptReport.status
        // ]
        const deptReport = {
          id: data.id,
          reportName: data.reportNumber,
          poster: data.poster,
          postTime: data.createTime,
          status: ['', '草稿', '审批中', '已审批', '已驳回'][data.status],
          type: data.type,
        }
        this.securityReport = deptReport
      }
    },
    createSec() {
      this.$store.commit('emitBussiness', {
        businessType: 'securityCheck',
        businessId: this.detail.id,
        templateId: this.securityCheckReportTempId,
      })
      this.$router.push({
        name: 'report-emit-detail',
        params: { id: this.securityCheckReportTempId },
      })
    },
    createSecNew() {
      this.$store.commit('emitBussiness', {
        businessType: 'securityCheck_Report',
        businessId: this.detail.id,
        templateId: this.securityCheckReportTempId,
        otherParams: {
          shipCode: this.detail.shipCode,
          captain: this.detail.captain,
          cheifEngineer: this.detail.cheifEngineer,
          firstMate: this.detail.firstMate,
          checkPort: this.detail.checkPort,
          inspector: this.detail.inspector,
          inspectionTime: this.detail.inspectionTime,
          // shipFlag:
          //   this.initialData.shipBaseMixOutputDTO.flagPort +
          //   ',' +
          //   this.initialData.shipBaseMixOutputDTO.flagState,
          // shipName: this.initialData.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        name: 'dept-report-security-check-new',
        params: {
          id: this.securityCheckReportTempId,
          businessId1: this.$route.params.id,
        },
      })
    },
    createInsp() {
      this.$store.commit('emitBussiness', {
        businessType: 'securityCheck_Insp',
        businessId: this.detail.id,
        templateId: this.securityCheckReportTempId,
        otherParams: {
          shipCode: this.detail.shipCode,
          type: this.detail.inspectionType,
          // shipFlag:
          //   this.initialData.shipBaseMixOutputDTO.flagPort +
          //   ',' +
          //   this.initialData.shipBaseMixOutputDTO.flagState,
          // shipName: this.initialData.shipBaseMixOutputDTO.chShipName,
        },
      })
      this.$router.push({
        name: 'dept-report-inspection-detail-new',
        params: {
          id: this.securityCheckReportTempId,
          businessId1: this.$route.params.id,
        },
      })
    },
    createQues() {
      this.initialData = {
        shipCode: this.detail.shipCode,
        problemPhotos: [],
        correctEvidences: [],
      }
      this.dialog = true
    },
    editQues() {
      this.initialData = {
        ...this.selectedQues,
        shipCode: this.detail.shipCode,
      }
      this.dialog = true
    },
    editMesure() {
      this.dialog2 = true
    },
    async delQues() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/securityCheck/detail/deleteQuestion',
        [this.selectedQues.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadQuestionList()
        this.selectedQues = false
      }
    },
    async loadQuestionList() {
      const { data } = await this.getAsync(
        '/business/seaAffairs/securityCheck/detail/questionList',
        { id: this.detail.id },
      )
      this.questionList = data
    },
    async loadFillList() {
      const { data } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/pagebyquery',
        { businessId: this.detail.id, status: '-3' },
      )
      this.fillList = data
    },
    async questionSuccess() {
      await this.loadQuestionList()
      this.selectedQues = false
      this.initialData = {}
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },

    async loadShipInfo(shipCode) {
      const { data } = await this.getAsync(
        '/business/crew/osmOnShipCrew/common/crew/listByShipCodeAndPosition',
        { shipCode },
      )
      if (data.length === 0) {
        this.$dialog.message.warning('当前船舶没有船员信息，请先添加船员信息')
        return
      }
      this.detail.captain = data.find(
        (i) => i.actualPosition === '船长',
      )?.chName
      this.detail.cheifEngineer = data.find(
        (i) => i.actualPosition === '轮机长',
      )?.chName
      this.detail.firstMate = data.find(
        (i) => i.actualPosition === '大副',
      )?.chName
      this.detail.secondMate = data.find(
        (i) => i.actualPosition === '二副',
      )?.chName
      this.detail.thirdMate = data.find(
        (i) => i.actualPosition === '三副',
      )?.chName
    },
  },

  mounted() {
    this.loadDetail()
  },
  beforeDestroy() {
    this.$store.commit('removeBussinessParam', this.detail.id)
  },
}
</script>

<style></style>
