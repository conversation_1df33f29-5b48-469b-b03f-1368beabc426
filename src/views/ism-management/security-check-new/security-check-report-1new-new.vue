<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            outlined
            label="状态"
            dense
            :items="statusMap"
            clearable
          ></v-select>
        </v-col> -->
      </template>
      <template #btns></template>
      <template v-slot:[`item.shipInfo`]="{ item }">
        {{ item.shipInfo ? item.shipInfo.chShipName : '岸端报表' }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{
            item.status == 2
              ? `待[${item.businessStatus}]审批`
              : statuses[item.status]
          }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'security-check-report-1new-new',
  created() {
    this.tableName = '安全检查报表new'
    // this.reqUrl = '/business/seaAffairs/deptReport/page'
    this.reqUrl = '/business/seaAffairs/SecurityCheckReport/recordPage'
    this.headers = [
      { text: '船名', value: 'shipInfo' },
      { text: '航次', value: 'voyage' },
      { text: '港口', value: 'checkPort' },
      { text: '检查时间', value: 'inspectionTime' },
      { text: '主要缺陷和建议', value: 'deficiencySuggestion' },
      { text: '发起人', value: 'posterName' },
      { text: '发起时间', value: 'createTime' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'dept-report-security-check' }
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: {
        businessType: 'securityCheck',
        formCode: '86-120101-1',
      },
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
