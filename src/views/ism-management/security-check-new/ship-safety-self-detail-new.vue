<template>
  <v-container fluid>
    <v-detail-view
      :title="`船舶自查-${isEdit ? detail.applicationNo : '新增'}`"
      :tooltip="isEdit ? detail.applicationNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        !detail.auditParams ||
        detail.auditParams.taskId ||
        detail.status === 1 ||
        detail.status === 4
      "
      @save="save"
      @submit="submit"
      v-permission="['船舶自查:编辑']"
    >
      <template v-if="detail.status == 3" v-slot:titlebtns>
        <!-- 返回方法可用 -->
        <v-btn
          width="90"
          tile
          :to="{
            name: 'ship-safety-self-list-new',
          }"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn>
        <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['船舶自查:下载部门报表']"
        >
          下载部门报表
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基础信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3" class="py-0">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <vs-date-picker
                  label="日期"
                  :readonly="isEdit"
                  v-model="detail.time"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <!-- <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="岸基责任人"
                  outlined
                  v-model="detail.approveDepartment"
                  :items="approveDepartments"
                  dense
                  :rules="[rules.required]"
                ></v-select>
              </v-col> -->
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="船长-自查结果描述"
                  v-model="detail.captain"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="大副-自查结果描述"
                  v-model="detail.firstMate"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="二副-自查结果描述"
                  v-model="detail.secondMate"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="三副-自查结果描述"
                  v-model="detail.thirdMate"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="轮机长-自查结果描述"
                  v-model="detail.chiefEngineer"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="大管轮-自查结果描述"
                  v-model="detail.largeWheelPipe"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="二管轮-自查结果描述"
                  v-model="detail.twoWheelPipe"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="三管轮-自查结果描述"
                  v-model="detail.threeWheelPipe"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="电子电气员-自查结果描述"
                  v-model="detail.electronicElectrician"
                  :rules="[rules.required]"
                  height="100px"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-card-text>
      </template>
      <!-- <template v-slot:映射内容>
        <v-card-text>
          <v-row>
            <v-col v-for="t in contents" :key="t.id" cols="12" md="4">
              <v-text-field
                v-model="mapping[t.mappingCode]"
                :label="t.field"
              ></v-text-field>
            </v-col>
            <v-col v-if="!!signNatures.length" cols="12">
              <div>
                当前节点存在电子签名，审批结果为通过，包含：
                <b v-for="t in signNatures" :key="t.id">
                  {{ t.field }}
                </b>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </template> -->
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'ship-safety-self-detail-new',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'ship-safety-self-list-new'
    // this.subtitles = ['基础信息', '映射内容']
    this.subtitles = ['基础信息']
    this.approveDepartments = [
      { text: '海务主管', value: '海务' },
      { text: '机务主管', value: '机务' },
      { text: '通导主管', value: '通导' },
      { text: 'i办主管', value: 'i办' },
      { text: '船员管理主管', value: '船员' },
    ]
  },
  computed: {
    isEdit() {
      return this.$route.params.id == 'new'
        ? false
        : this.detail.status == '1' || this.detail.status == '4'
        ? false
        : true
    },
    isSave() {
      return this.$route.params.id == 'new'
        ? true
        : this.detail.status == '2' || this.detail.status == '3'
        ? true
        : false
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    signNatures() {
      return this.needFields.filter((i) => i.mappingType === '0')
    },
    contents() {
      return this.needFields.filter((i) => i.mappingType === '1')
    },
    downloadUrl() {
      return this.detail.status === '3'
        ? `/api/business/seaAffairs/shipReport/ship-safety-self/record/exportById?id=${this.detail.id}`
        : ''
    },
  },
  data() {
    return {
      detail: {
        applicationNo: '',
        shipInfo: {
          shipCode: '',
        },
        attachmentIds: [],
      },
      mapping: {},
      needFields: [],
      rules: {
        required: (v) => !!v || v == '0' || '必填项不能为空',
      },
    }
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw, data } = await this.postAsync(
        '/business/seaAffairs/shipReport/ship-safety-self/record/saveOrUpdate',
        { ...this.detail },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            processInstanceId: this.detail.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent: this.mapping[f.mappingCode],
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) return
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/seaAffairs/shipReport/ship-safety-self/record/submitById',
            { id: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },
    //审批流异常处理测试方法（后台审批流启动时缺少shipcode）
    async submit1(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            processInstanceId: this.detail.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent: this.mapping[f.mappingCode],
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) return
        if (this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/seaAffairs/shipReport/ship-safety-self/record/submitById',
            { id: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },
    async loadNeedFields() {
      if (!this.detail?.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        { processInstanceId: this.detail.auditParams.processInstanceId },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          if (t.mappingCode.indexOf('date') != -1)
            this.mapping[t.mappingCode] = new Date(
              Date.now() - new Date().getTimezoneOffset() * 60000,
            )
              .toISOString()
              .substr(0, 10)
          else
            this.mapping[t.mappingCode] =
              this.mappingRecords[t.mappingCode] || ''
        }
      }
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async loadDetail() {
      this.$refs.form.validate()
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/shipReport/ship-safety-self/record/detailById',
        { id: this.$route.params.id },
      )
      this.detail = { ...data, shipCode: data.shipInfo.shipCode }
      // await this.loadNeedFields()
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
