<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['事故与险情险情报告:编辑']"
      :title="`险情报告${
        deptReportNearMiss.businessType == 'securityCheck_Non_3'
          ? '（险情）'
          : '险情'
      }-${isEdit ? deptReportNearMiss.reportNumber : '新增'}`"
      :tooltip="isEdit ? deptReportNearMiss.reportNumber : '新增'"
      :backRouteName="isbackRouteName"
      :can-submit="canSubmit"
      :can-save="isSave"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
    >
      <template
        v-if="
          deptReportNearMiss.status === 3 || deptReportNearMiss.status === '3'
        "
        v-slot:custombtns
      >
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          :href="downloadUrl"
          color="info"
          small
          class="mx-1"
          v-permission="['事故与险情险情报告:下载部门报表']"
        >
          下载部门报表
        </v-btn>
      </template>
      <template
        v-if="!!auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="auditParams"
              :shipCode="deptReportNearMiss.shipInfoDO.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #验证意见>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <!-- <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="!isSub"
                  label="纠正情况验证Verification of correction"
                  rows="3"
                  :rules="
                    deptReportNearMiss.status === '2' && isSub
                      ? [rules.required]
                      : []
                  "
                  dense
                  v-model="deptReportNearMiss.correctionVerification"
                ></v-textarea>
              </v-col> -->
              <v-col cols="12" md="12">
                <v-dict-select
                  dict-type="root_cause_category"
                  label="根本原因类别（可多选）Category of root cause（Multiple choice）"
                  outlined
                  collapse-tags
                  :required="isSub"
                  :rules="[rules.required]"
                  :readonly="!isSub"
                  multiple
                  v-model="deptReportNearMiss.rootCauseCategory"
                  @change="(val) => $emit('input', String(val))"
                  dense
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :required="isSub"
                  :rules="[rules.required]"
                  :readonly="!isSub"
                  label="针对客观因素的改进措施Improvement measures for objective factors"
                  rows="3"
                  dense
                  v-model="deptReportNearMiss.objectiveFactorImprovementMeasure"
                ></v-textarea>
              </v-col>
              <v-col class="py-0" md="12" cols="12">
                <v-dict-select
                  dict-type="management_factor_measure"
                  label="针对安全管理的改进措施（可多选）Improvement measures for management factors（Multiple choice）"
                  outlined
                  :required="isSub"
                  collapse-tags
                  :readonly="!isSub"
                  :rules="[rules.required]"
                  multiple
                  v-model="
                    deptReportNearMiss.managementFactorImprovementMeasure
                  "
                  @change="(val) => $emit('input', String(val))"
                  dense
                ></v-dict-select>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  label="公司部门/船名Company Department/MV"
                  v-model="deptReportNearMiss.shipInfoDO.shipCode"
                  outlined
                  dense
                ></v-text-field> -->
                <v-ship-select
                  :readonly="isEdit"
                  label="公司部门/船名Company Department/MV"
                  v-model="deptReportNearMiss.shipInfoDO.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="编号Report number"
                  v-model="deptReportNearMiss.reportNumber"
                  outlined
                  :rules="[rules.required]"
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="报告人及职务Reporter & Rank"
                  v-model="deptReportNearMiss.reporter"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="4">
                <v-select
                  label="岸基责任人"
                  outlined
                  v-model="deptReportNearMiss.approveDepartment"
                  :items="approveDepartments"
                  dense
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
              <!-- <v-col cols="12" md="3"> -->
              <!-- <v-text-field
                  label="类  型 Type"
                  v-model="deptReportNearMiss.type"
                  outlined
                  dense
                ></v-text-field> -->
              <!-- <v-select
                  :readonly="
                    isEdit ||
                    deptReportNearMiss.businessType ==
                      'securityCheck_Non_3'
                  "
                  label="类  型 Type"
                  v-model="deptReportNearMiss.type"
                  outlined
                  :rules="[rules.required]"
                  dense
                  :items="[
                    { text: '港口国检查PSC', value: '0' },
                    { text: '船旗国检查FSC', value: '1' },
                    { text: '季度检查Quarterly check', value: '2' },
                    { text: '险情Risks', value: '3' },
                    { text: '船舶自查Ship self-check', value: '4' },
                    { text: '外审External audit', value: '5' },
                    { text: '事故Accidents', value: '6' },
                    { text: '其他Other', value: '7' },
                  ]"
                ></v-select>
              </v-col> -->
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="isEdit"
                  label="日期Date"
                  v-model="deptReportNearMiss.createDate"
                  outlined
                  :rules="[rules.required]"
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="险情的简要描述（包括发生时间、地点、人员）A brief description of risks (including time, place and personnel)"
                  rows="3"
                  :rules="[rules.required]"
                  dense
                  v-model="deptReportNearMiss.reportDescription"
                ></v-textarea>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="报告人Reporter"
                  v-model="deptReportNearMiss.reporter"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="isEdit"
                  label="报告日期Date"
                  v-model="deptReportNearMiss.reportDate"
                  outlined
                  dense
                ></vs-date-picker> -->
              <!-- </v-col> -->
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="分析险情产生的原因（直接原因/根本原因） Analyze the causes of risks (Direct cause / Root cause )"
                  rows="3"
                  dense
                  v-model="deptReportNearMiss.reportCauses"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="潜在危险：Potential hazard"
                  rows="3"
                  dense
                  v-model="deptReportNearMiss.potentialHazard"
                ></v-textarea>
              </v-col>
              <!-- <v-col cols="12" md="12">岸基分析Shore-based analysis：</v-col> -->
              <!-- <v-col cols="12" md="12">
                <v-dict-select
                  dict-type="root_cause_category"
                  label="根本原因类别Category of root cause"
                  outlined
                  collapse-tags
                  :readonly="!isSub"
                  :rules="
                    deptReportNearMiss.status === '2' && isSub
                      ? [rules.required]
                      : []
                  "
                  multiple
                  v-model="deptReportNearMiss.rootCauseCategory"
                  @change="(val) => $emit('input', String(val))"
                  dense
                ></v-dict-select>
              </v-col> -->
              <!-- <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="!isSub"
                  :rules="
                    deptReportNearMiss.status === '2' && isSub
                      ? [rules.required]
                      : []
                  "
                  label="针对客观因素的改进措施"
                  rows="3"
                  dense
                  v-model="deptReportNearMiss.acorrectiveTaken"
                ></v-textarea>
              </v-col> -->
              <!-- <v-col cols="12" md="9"></v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="isEdit"
                  label="主管部门/船长Department in charge/Master"
                  v-model="deptReportNearMiss.inchargeMaster1"
                  outlined
                  dense
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  :readonly="isEdit"
                  label="纠正措施/预防措施 Measures to corrective action / preventive action"
                  rows="3"
                  dense
                  v-model="deptReportNearMiss.preventSituation"
                ></v-textarea>
              </v-col>
              <!-- <v-col cols="12" md="9"></v-col>
              <v-col cols="12" md="3" align-self="end">
                <v-text-field
                  :readonly="isEdit"
                  label="主管部门/船长Department in charge/Master"
                  v-model="deptReportNearMiss.inchargeMaster2"
                  outlined
                  dense
                ></v-text-field> -->
              <!-- </v-col> -->
            </v-row>
          </v-form>
          <v-row>
            <v-col cols="12">
              <v-attach-list
                :attachments="deptReportNearMiss.attachmentRecords"
                :readonly="isEdit"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-container>
        <!-- <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-attach-list
                :disabled="deptReportNearMiss.status === '3'"
                :attachments="deptReportNearMiss.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-card-text> -->
      </template>
      <template v-slot:映射内容>
        <v-card-text>
          <v-row>
            <v-col v-for="t in contents" :key="t.id" cols="12" md="4">
              <v-text-field
                v-model="mapping[t.mappingCode]"
                :label="t.field"
              ></v-text-field>
            </v-col>
            <v-col v-if="!!signNatures.length" cols="12">
              <div>
                当前节点存在电子签名，审批结果为通过，包含：
                <b v-for="t in signNatures" :key="t.id">
                  {{ t.field }}
                </b>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'dept-report-info-near-miss-detail',
  mixins: [routerControl],
  components: {},
  created() {
    // this.backRouteName = 'accident-report-list-new'
    // this.subtitles = ['基础信息', '映射内容']
    this.approveDepartments = [
      { text: '海务主管', value: '海务' },
      { text: '机务主管', value: '机务' },
      { text: '通导主管', value: '通导' },
      { text: 'i办主管', value: 'i办' },
      { text: '船员管理主管', value: '船员' },
    ]
  },
  data() {
    return {
      backRouteName: 'accident-report-near-miss-list',
      // subtitles: ['验证意见', '基本信息', '映射内容'],
      subtitles: ['基本信息', '映射内容'],
      // deptReportNearMiss: {
      //   // shipCode: '',
      //   reportNumber: '',
      //   managerMaster: '',
      //   createDate: '',
      //   createTime: '',
      //   inchargeMaster: '',
      //   type: '',
      //   reporter: '',
      //   shipInfoDO: { shipCode: '' },
      //   reportDate: '',
      //   reportDescription: '',
      //   reportCauses: '',
      //   attachmentRecords: [],
      // },
      deptReportNearMiss: {
        attachmentRecords: [],
        businessId: '',
        businessType: '',
        businessStatus: '',
        createDate: '',
        createTime: '',
        delFlag: false,
        id: '',
        inchargeMaster: '',
        managerMaster: '',
        preventSituation: '',
        acorrectiveTaken: '',
        processInstanceId: '',
        reportCauses: '',
        reportDate: '',
        reportDescription: '',
        reportNumber: '',
        reporter: '',
        reviewRelevant: '',
        poster: this.$local.data.get('userInfo').id,
        shipInfoDO: {
          // chShipName: '',
          // enShipName: '',
          // id: '',
          shipCode: '',
        },
        status: null,
        type: '',
        updateTime: '',
      },
      auditParams: {},
      needFields: [],
      mapping: {},
      mappingRecords: {},
      // pdfFile: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      status: '0',
      loading: false,
      sloading: false,
    }
  },

  watch: {
    // $route() {
    //   console.log(this.$route)
    // },
    '$store.state.reportParams.businessParams': {
      handler: function (val) {
        if (this.$route.params.id == 'new') {
          this.deptReportNearMiss.businessType = 'nonConformanceReportNew'
          // this.backRouteName = 'security-check-report-3'
        } else if (this.$route.params.id == 'securityCheck_Non_3_new') {
          this.deptReportNearMiss.type = '3'
          this.deptReportNearMiss.businessType = 'securityCheck_Non_3'
          this.backRouteName = 'accident-report-near-miss-list'
        }
        if (this.isEdit) return
        // console.log(val)
        const businessParam = val.find(
          (item) =>
            // (!item.reportId || item.reportId === '') &&
            (item.businessType === 'securityCheck_Non' ||
              item.businessType === 'shipAccident_Non') &&
            item.businessId === this.$route.params.businessId1, //验证当前最新传入的业务id，多业务id时精准匹配验证
        )
        // if (!!businessParam && businessParam.businessType)
        if (!!businessParam && businessParam.businessId) {
          // console.log(
          //   businessParam.businessType + ' ' + businessParam.businessId,
          // )
          if (businessParam.businessType === 'securityCheck_Non')
            this.backRouteName = 'accident-report-near-miss-list'
          else this.backRouteName = 'accident-report-near-miss-list'
          console.log(
            businessParam.businessType + ' ' + businessParam.businessId,
          )

          this.businessParam = businessParam
          this.deptReportNearMiss.type = businessParam.otherParams.type
          this.deptReportNearMiss.businessId = businessParam.businessId
          this.deptReportNearMiss.businessType = businessParam.businessType
          this.deptReportNearMiss.shipInfoDO.shipCode =
            businessParam.otherParams.shipCode
          // this.detail.seaJson = { ...businessParam.otherParams }
          // this.detail.shipJson = { ...businessParam.otherParams }
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    isbackRouteName() {
      //根据传过来的类型调整返回列表
      //   if (this.$route.params.businessType === 'securityCheck_Non')
      //     this.backRouteName = 'security-check-report-3'
      //   else this.backRouteName = 'accident-report-list-new'
      return this.$route.params.businessType === 'securityCheck_Non' ||
        this.$route.params.businessType === 'securityCheck_Non_3'
        ? 'accident-report-near-miss-list'
        : 'accident-report-near-miss-list'
    },
    isEdit() {
      return this.$route.params.id == 'new'
        ? false
        : this.deptReportNearMiss.status == null ||
          this.deptReportNearMiss.status == '' ||
          this.deptReportNearMiss.status == '1' ||
          this.deptReportNearMiss.status == '4'
        ? false
        : true
    },
    isSave() {
      return this.$route.params.id == 'new'
        ? true
        : this.deptReportNearMiss.status == '0' ||
          this.deptReportNearMiss.status == '1' ||
          this.deptReportNearMiss.status == '' ||
          this.deptReportNearMiss.status == '4' ||
          this.deptReportNearMiss.status == null
        ? true
        : false
    },
    isSub() {
      //是否主管审批，主卦审批是填写验证意见内容
      return this.deptReportNearMiss.businessStatus == '业务主管' ||
        this.deptReportNearMiss.businessStatus == '海务主管' ||
        this.deptReportNearMiss.businessStatus == '安全培训主管' ||
        this.deptReportNearMiss.businessStatus == '机务主管' ||
        this.deptReportNearMiss.businessStatus == '通导主管' ||
        this.deptReportNearMiss.businessStatus == 'I办主管'
        ? true
        : false
    },
    canSubmit() {
      return (
        this.$route.params.id == 'new' ||
        ((!this.deptReportNearMiss.auditParams ||
          !!this.deptReportNearMiss.auditParams?.isReject) &&
          this.deptReportNearMiss.status == 2) ||
        (this.deptReportNearMiss.businessType == 'securityCheck_Non_3' &&
          this.deptReportNearMiss.status != '3') ||
        this.deptReportNearMiss.status == 4
      )
    },
    isComplete() {
      let hideBtn =
        !!this.deptReportNearMiss.status && this.deptReportNearMiss.status == 3
          ? true
          : false
      return hideBtn
    },
    signNatures() {
      return this.needFields.filter((i) => i.mappingType === '0')
    },
    contents() {
      return this.needFields.filter((i) => i.mappingType === '1')
    },
    downloadUrl() {
      return this.deptReportNearMiss.status === '3'
        ? `/api/business/seaAffairs/dept-report-near-miss/exportById?id=${this.deptReportNearMiss.id}`
        : ''
    },
  },

  methods: {
    // changeAttachment(attachmentIds) {
    //   this.deptReportNearMiss.attachmentRecords = attachmentIds
    // },
    changeAttachment(attachmentIds) {
      this.attachmentIds = attachmentIds
    },
    // async save(goBack) {
    async save() {
      this.loading = true
      // console.log('save 1...')
      // if (this.$refs.audit && !this.$refs.form.validate()) {
      //   return
      // }
      // if (!(this.$refs?.form?.validate() ?? true)) return

      if (!this.$refs.form.validate()) {
        return
      }
      if (this.isComplete) {
        this.$dialog.message.error(`已审批工作无法保存！`)
        return
      }
      // console.log('save 2...')
      const t = true
      if (t) {
        // console.log('save...')
        // console.log(this.businessParam)
        // 无业务伴随，正常提交
        if (!this.businessParam) {
          // if (
          //   !this.deptReportNearMiss.id ||
          //   this.deptReportNearMiss.id == 'new'
          // ) {
          // console.log('save createWithBusiness...1')
          const { data } = await this.postAsync(
            '/business/seaAffairs/dept-report-near-miss/createWithBusiness',
            {
              ...this.deptReportNearMiss,
              shipCode: this.deptReportNearMiss.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              attachmentIds: this.attachmentIds,
            },
          )
          if (data) this.closeAndTo(this.backRouteName, {}, {})
        } else {
          // 有业务伴随，提交业务
          // console.log('save createWithBusiness...2')
          const { data } = await this.postAsync(
            '/business/seaAffairs/dept-report-near-miss/createWithBusiness',
            // '/business/seaAffairs/dept-report-near-miss/update',
            {
              ...this.deptReportNearMiss,
              shipCode: this.deptReportNearMiss.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              // ...this.businessParam,
              // isSyncProcess: false,
              attachmentIds: this.attachmentIds,
            },
          )
          if (!data) return
          // if (notMove) return data
          // console.log('this.businessParam.businessId')
          if (this.businessParam.businessId) {
            // console.log(this.businessParam.businessId)
            this.$store.commit('setReportId', {
              ...this.businessParam,
              reportId: data,
            })
            this.closeAndTo('accident-detail-new', {
              id: this.businessParam.businessId,
            })
          } else {
            // goBack(this.backRouteName)
            this.closeAndTo(this.backRouteName, {}, {})
          }
          if (data) {
            this.closeAndTo(this.backRouteName, {}, {})
          }
        }
      }
      // 审批流提交
      // const error = await this.$refs.audit?.submit()
      // if (error) {
      //   return
      // }
      // goBack(this.backRouteName)
      this.closeAndTo(this.backRouteName, {}, {})
      this.loading = false
    },
    //  async submit(goBack) {
    async submit() {
      this.loading = true
      // console.log('submit 1...')
      if (this.$refs?.audit && !this.$refs?.aform?.validate()) {
        return
      }
      if (!(this.$refs?.aform?.validate() ?? true)) {
        return
      }
      // if (!this.$refs.form.validate()) {
      //   return
      // }
      // console.log('submit 2...')
      if (
        this.isSub &&
        this.deptReportNearMiss.status == 2 &&
        this.$refs?.audit?.adopt &&
        this.deptReportNearMiss.rootCauseCategory == ''
      ) {
        this.$dialog.message.error(
          `请填写根本原因类别（可多选）Category of root cause（Multiple choice）`,
        )
        return
      }
      if (
        this.isSub &&
        this.deptReportNearMiss.status == 2 &&
        this.$refs?.audit?.adopt &&
        this.deptReportNearMiss.objectiveFactorImprovementMeasure == ''
      ) {
        this.$dialog.message.error(
          `请填写针对客观因素的改进措施Improvement measures for objective factors`,
        )
        return
      }
      if (
        this.deptReportNearMiss.status == 2 &&
        this.isSub &&
        this.$refs?.audit?.adopt &&
        this.deptReportNearMiss.managementFactorImprovementMeasure == ''
      ) {
        this.$dialog.message.error(
          `请填写针对安全管理的改进措施Improvement measures for management factors`,
        )
        return
      }
      const t = true
      // console.log('submit 2...')
      // if (t) return
      if (t) {
        // console.log('submit...')
        // 无业务伴随，正常提交
        // if (!this.businessParam) {
        if (
          !this.deptReportNearMiss.id ||
          this.deptReportNearMiss.id == 'new'
        ) {
          // console.log('save createAndSubmit...')
          const { data } = await this.postAsync(
            '/business/seaAffairs/dept-report-near-miss/createAndSubmit',
            {
              ...this.deptReportNearMiss,
              shipCode: this.deptReportNearMiss.shipInfoDO.shipCode,
              // formCode: this.reportInfo.formCode,
              attachmentIds: this.attachmentIds,
            },
          )
          if (data) this.closeAndTo(this.backRouteName, {}, {})
        } else {
          // 有业务伴随，提交业务
          if (!this.deptReportNearMiss.auditParams) {
            // console.log('save createWithBusiness...')
            const { data } = await this.postAsync(
              '/business/seaAffairs/dept-report-near-miss/createAndSubmit',
              // '/business/seaAffairs/dept-report-near-miss/update',
              {
                ...this.deptReportNearMiss,
                shipCode: this.deptReportNearMiss.shipInfoDO.shipCode,
                // formCode: this.reportInfo.formCode,
                // ...this.businessParam,
                // isSyncProcess: false,
                attachmentIds: this.attachmentIds,
              },
            )
            if (data) {
              this.closeAndTo(this.backRouteName, {}, {})
            }
          }
        }
      }
      // 有映射字段需填写,且当前状态为审批中，更新字段
      if (this.needFields.length !== 0 && this.status === '2') {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            processInstanceId:
              // this.deptReportNearMiss.auditParams.processInstanceId,
              this.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent: this.mapping[f.mappingCode],
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) {
          return
        }
      }
      // 审批流提交
      const error = await this.$refs.audit?.submit()
      if (error) {
        return
      }
      this.loading = true
      // goBack(this.backRouteName)
      this.closeAndTo(this.backRouteName, {}, {})
    },
    // async mapField() {
    //   let mappingDetails = []
    //   for (let f of this.needFields) {
    //     mappingDetails.push({
    //       processInstanceId:
    //         this.deptReportNearMiss.auditParams.processInstanceId,
    //       mappingCode: f.mappingCode,
    //       mappingContent: this.mapping[f.mappingCode],
    //       mappingType: f.mappingType,
    //     })
    //   }
    //   let { errorRaw } = await this.postAsync(
    //     '/business/seaAffairs/templateMapping/saveMappingDetail',
    //     mappingDetails,
    //   )
    //   return errorRaw
    // },
    async loadDeptReportInfo() {
      // console.log(this.$route.params.id)
      // console.log(this.$route.params.businessType)
      // if (this.$route.params.businessType === 'securityCheck_Non')
      //   this.backRouteName = 'security-check-report-3'
      // else this.backRouteName = 'accident-report-list-new'
      // console.log(this.backRouteName)
      const { data } = await this.getAsync(
        // '/business/seaAffairs/deptReport/getReportDetailById',
        '/business/seaAffairs/dept-report-near-miss/getReportDetailById',
        { reportId: this.$route.params.id },
      )
      // console.log('data 1')
      if (data.deptReportNearMiss) {
        // this.deptReportNearMiss = data.deptReportNearMiss
        this.deptReportNearMiss = {
          ...data.deptReportNearMiss,
          // shipCode: data.deptReportNearMiss.shipInfoDO.shipCode,
        }
        if (
          this.deptReportNearMiss.rootCauseCategory != null &&
          this.deptReportNearMiss.rootCauseCategory != ''
        )
          this.deptReportNearMiss.rootCauseCategory = JSON.parse(
            this.deptReportNearMiss.rootCauseCategory,
          ) //转换多选项处理
        if (
          this.deptReportNearMiss.managementFactorImprovementMeasure != null &&
          this.deptReportNearMiss.managementFactorImprovementMeasure != ''
        )
          this.deptReportNearMiss.managementFactorImprovementMeasure =
            JSON.parse(
              this.deptReportNearMiss.managementFactorImprovementMeasure,
            ) //转换多选项处理
        // console.log('data 12')
        if (data.deptReportNearMiss.status)
          this.status = data.deptReportNearMiss.status
        // console.log(data.deptReportNearMiss.businessType)
        // if (data.deptReportNearMiss.businessType === 'securityCheck')
        //   this.backRouteName = 'security-check-report-3'
        // else this.backRouteName = 'accident-report-list-new'
      }
      // console.log('data 2')

      if (data.auditParams) {
        this.auditParams = data.auditParams
        // this.deptReportNearMiss.auditParams = data.auditParams
      }
      if (this.status > 1) this.subtitles = ['验证意见', '基本信息']
      //['验证意见', '基本信息', '映射内容']
      // else if (this.detail.status === '2')
      //   this.subtitles = [
      //     '填写进度',
      //     '船舶基本信息',
      //     '检查相关内容',
      //     '问题项',
      //     // '确认进度',
      //   ]
      else this.subtitles = ['基本信息']
      // console.log('data 3')
      // if (this.status === '2') this.subtitles = ['基本信息', '映射内容']
      // console.log('data 4')
      // await this.loadNeedFields()
    },
    // async loadNeedFields() {
    //   const { data } = await this.getAsync(
    //     '/business/seaAffairs/templateMapping/getReportNeedField',
    //     { deptReportId: this.$route.params.id },
    //   )
    //   this.needFields = data || []
    //   // 初始化签名字段的用户id
    //   for (const t of this.needFields) {
    //     if (t.mappingType === '0') {
    //       this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
    //     } else {
    //       this.mapping[t.mappingCode] = '' || this.mappingRecords[t.field]
    //     }
    //   }
    // },
    async loadNeedFields() {
      console.log('1')
      console.log(this.deptReportNearMiss)
      console.log(this.deptReportNearMiss?.auditParams)
      console.log(this.deptReportNearMiss?.auditParams?.processInstanceId)
      console.log('2')
      console.log(this.auditParams)
      console.log(this.deptReportNearMiss?.auditParams)
      console.log(this.auditParams?.processInstanceId)
      if (!this.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        {
          processInstanceId: this.auditParams.processInstanceId,
        },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id 测试
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          if (t.mappingCode.indexOf('date') != -1)
            this.mapping[t.mappingCode] = new Date(
              Date.now() - new Date().getTimezoneOffset() * 60000,
            )
              .toISOString()
              .substr(0, 10)
          else
            this.mapping[t.mappingCode] =
              this.mappingRecords[t.mappingCode] || ''
        }
      }
    },
  },

  async mounted() {
    await this.loadDeptReportInfo()
  },
}
</script>

<style scoped></style>
