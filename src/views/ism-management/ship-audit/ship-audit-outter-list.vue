<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      :push-params="pushParams"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.certificateType"
            label="证书类型"
            outlined
            dense
            :items="['ISSC', 'MLC', 'SMC']"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.ismManager"
            label="I办主管"
            outlined
            dense
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-dict-select
            dict-type="classification_society"
            label="船级社"
            v-model="searchObj.classification"
            dense
            outlined
            clearable
          ></v-dict-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          to="/ism-management/ship-audit/outter/create"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['船舶外审:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增船舶
        </v-btn>
        <!-- 导入按钮仅限测试环境数据导入使用 -->
        <!-- <v-import-btn
          import-url="/business/seaAffairs/outAudit/excelImport"
          @importSuccess="importSuccess"
          v-permission="['中版书:导入EXCEL']"
        >
          导入EXCEL
        </v-import-btn> -->

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船舶外审:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'ship-audit-outter-list',
  created() {
    this.tableName = '船舶外审'
    this.reqUrl = '/business/seaAffairs/outAudit/page'
    this.headers = [
      { text: '船舶', value: 'shipName' },
      { text: '船级社', value: 'shipClassification' },
      { text: '证书类型', value: 'certificateType' },
      { text: '证书到期日期', value: 'certificateInvalidDate' },
      { text: '窗口日期', value: 'planAuditDate' },
      { text: '上次实际审核日期', value: 'lastActualAuditDate' },
      { text: '审核地点(上次)', value: 'lastAuditPlace' },
      { text: '审核类型', value: 'status' },
      { text: 'I办主管', value: 'ismManager' },
      { text: '海务主管', value: 'marManager' },
      { text: '机务主管', value: 'techManager' },
      { text: '通导主管', value: 'cnitManager' },
      { text: '调配主管', value: 'deplManager' },
      { text: '证书编号', value: 'certificateCode' },
      { text: '证书签发日期', value: 'certificateIssueDate' },
    ]
    this.pushParams = {
      name: 'ship-audit-outter-detail',
    }
  },
  watch: {
    $route() {
      // this.remindType = this.$route.query.remindType
      this.searchObj.remindType = this.$route.query?.remindType ?? ''
      // this.tableName =
      //   this.searchObj.remindType == '0'
      //     ? '船舶内审（2个月内到期）'
      //     : this.searchObj.remindType == '1'
      //     ? '船舶内审（1个月内到期）'
      //     : '船舶内审'
      this.$refs.table.loadTableData()
    },
    // 'searchObj.remindType'(val) {
    //   if (val) {
    //     console.log('remindType is ' + this.searchObj.remindType)
    //     this.$refs.table.loadTableData()
    //   }
    // },
  },
  data() {
    return {
      selected: false,
      searchObj: {
        remindType: this.$route.query?.remindType ?? '',
      },
    }
  },

  methods: {
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/outAudit/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
