<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      use-ship
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.year"
            label="年份"
            outlined
            dense
            append-icon="mdi-magnify"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.ismManager"
            label="I办主管"
            outlined
            dense
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-dict-select
            dict-type="classification_society"
            label="船级社"
            v-model="searchObj.classification"
            dense
            outlined
            clearable
          ></v-dict-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          to="/ism-management/ship-audit/inner/create"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['船舶内审:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增船舶
        </v-btn>
        <!-- 导入按钮仅限测试环境数据导入使用 -->
        <!-- <v-import-btn
          import-url="/business/seaAffairs/innerAudit/excelImport"
          @importSuccess="importSuccess"
          v-permission="['中版书:导入EXCEL']"
        >
          导入EXCEL
        </v-import-btn> -->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船舶内审:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.planAuditDate`]="{ item }">
        <!-- 超期或者还差一个月(30天)超期就变红
              还差一个月(30天)进入窗口就变黄
          -->
        <v-chip
          small
          :color="
            !!item.planAuditDate &&
            item.planAuditDate != '' &&
            !!item.expirationDate &&
            item.expirationDate != '' &&
            new Date(new Date().setDate(today.getDate() + 30)) <
              new Date(item.expirationDate)
              ? new Date(new Date().setDate(today.getDate() + 30)) <
                new Date(item.planAuditDate)
                ? 'success'
                : 'warning'
              : 'error'
          "
        >
          {{ item.planAuditDate }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
const today = new Date(Date.now())
export default {
  name: 'ship-audit-inner-list',
  created() {
    //定义在created里的数据是不可变的
    //如果需要变动要删除created里面定义的tableName ，转移到data里
    // this.tableName = '船舶内审'
    this.reqUrl = '/business/seaAffairs/innerAudit/page'
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '船级社', value: 'shipClassification' },
      // { text: '年度', value: 'year' },
      { text: '到期日期', value: 'expirationDate' },
      { text: '计划审核日期', value: 'planAuditDate' },
      // { text: '上次计划审核日期', value: 'lastPlanAuditDate' },
      // { text: '实际审核日期（最新）', value: 'lastActualAuditDate' },
      { text: '实际审核日期（最新）', value: 'lastAuditDate' },
      { text: 'I办主管', value: 'ismManager' },
      { text: '海务主管', value: 'marManager' },
      { text: '机务主管', value: 'techManager' },
      { text: '通导主管', value: 'cnitManager' },
      { text: '调配主管', value: 'deplManager' },
    ]
    this.pushParams = { name: 'ship-audit-inner-detail' }
    this.today = today
  },
  watch: {
    $route() {
      // this.remindType = this.$route.query.remindType
      this.searchObj.remindType = this.$route.query?.remindType ?? ''
      this.tableName =
        this.searchObj.remindType == '0'
          ? '船舶内审（2个月内到期）'
          : this.searchObj.remindType == '1'
          ? '船舶内审（1个月内到期）'
          : '船舶内审'
      this.$refs.table.loadTableData()
    },
    // 'searchObj.remindType'(val) {
    //   if (val) {
    //     console.log('remindType is ' + this.searchObj.remindType)
    //     this.$refs.table.loadTableData()
    //   }
    // },
  },
  data() {
    return {
      tableName: '船舶内审',
      selected: false,
      searchObj: {
        remindType: this.$route.query?.remindType ?? '',
      },
    }
  },

  methods: {
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/innerAudit/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
