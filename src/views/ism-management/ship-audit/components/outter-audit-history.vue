<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    max-width="1100"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        实际审核记录
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <!-- <v-select
                  :items="auditTypes"
                  v-model="formData.status"
                  :disabled="isEdit"
                  label="审核类型"
                ></v-select> -->
                <v-select
                  :items="auditTypes"
                  v-model="formData.status"
                  disabled
                  label="审核类型"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="formData.actualAuditDate"
                  label="实际审核日期"
                  use-today
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.place"
                  label="审核地点"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :rules="[rules.required]"
                  v-model="formData.handler"
                  label="经办人"
                ></v-text-field>
              </v-col>
              <v-col v-if="isChangeEdit" cols="12" md="3">
                <v-text-field
                  v-model="formData.certificateCode"
                  label="证书编码"
                  :readonly="isChangeEdit"
                ></v-text-field>
              </v-col>
              <v-col v-if="isChangeEdit" cols="12" md="3">
                <v-select
                  v-model="formData.certificateKind"
                  label="证书性质"
                  :items="certificateKinds"
                  :disabled="isChangeEdit"
                ></v-select>
              </v-col>
              <v-col v-if="isChangeEdit" cols="12" md="3">
                <vs-date-picker
                  v-model="formData.certificateIssueDate"
                  label="证书签发日期"
                  :disabled="isChangeEdit"
                ></vs-date-picker>
              </v-col>
              <v-col v-if="isChangeEdit" cols="12" md="3">
                <vs-date-picker
                  v-model="formData.certificateInvalidDate"
                  label="证书到期日期"
                  :disabled="isChangeEdit"
                ></vs-date-picker>
              </v-col>
              <v-col v-if="isChangeEdit" cols="12" md="3">
                <v-text-field
                  v-model="formData.newCertificateCode"
                  :rules="isChangeEdit ? [rules.required] : null"
                  label="新证书编码"
                ></v-text-field>
              </v-col>
              <v-col v-if="isChangeEdit" cols="12" md="3">
                <v-select
                  v-model="formData.newCertificateKind"
                  label="新证书性质"
                  :rules="isChangeEdit ? [rules.required] : null"
                  :items="certificateKinds"
                ></v-select>
              </v-col>
              <v-col v-if="isChangeEdit" cols="12" md="3">
                <vs-date-picker
                  v-model="formData.newCertificateIssueDate"
                  label="新证书签发日期"
                  :rules="isChangeEdit ? [rules.required] : null"
                ></vs-date-picker>
              </v-col>
              <v-col v-if="isChangeEdit" cols="12" md="3">
                <vs-date-picker
                  v-model="formData.newCertificateInvalidDate"
                  label="新证书到期日期"
                  :rules="isChangeEdit ? [rules.required] : null"
                ></vs-date-picker>
              </v-col>

              <v-col cols="12">
                <v-textarea
                  v-model="formData.remark"
                  outlined
                  label="备注"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="formData.attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'outter-audit-history',
  model: { prop: 'open', event: 'change' },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    outterAudit: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v == '0' || '必填项不能为空',
      },
    }
  },
  watch: {
    open(val) {
      this.toggleDialog(val)
    },
  },
  computed: {
    isEdit() {
      return !!this.outterAudit?.id
    },
    isChangeEdit() {
      return (
        this.outterAudit.status === 'CHANGE_AUDIT' ||
        this.outterAudit.status === 'FIRST_AUDIT'
      )
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async toggleDialog(val) {
      this.formData = this.outterAudit
      this.formData.status = this.auditTypes.find((item) => {
        return item.text === this.formData.status
      })?.value
      this.dialog = val
      await this.$nextTick()
      this.$refs?.form?.resetValidation()
    },
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/outAudit/updateHistory'
        : '/business/seaAffairs/outAudit/add'
      const { errorRaw } = await this.postAsync(url, this.formData)
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
  created() {
    this.auditTypes = [
      { text: '临时审核', value: 'TEMP_AUDIT' },
      { text: '初次审核', value: 'FIRST_AUDIT' },
      { text: '中间审核', value: 'MID_AUDIT' },
      { text: '换证审核', value: 'CHANGE_AUDIT' },
      { text: '附加审核', value: 'ADDITION_AUDIT' },
    ]
    this.certificateKinds = [
      { text: '长期', value: 0 },
      { text: '短期', value: 1 },
    ]
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
