<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    max-width="1100"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        实际审核记录
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="formData.actualAuditDate"
                  label="实际审核日期"
                  :disabled="isEdit"
                  use-today
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.handler"
                  label="经办人"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.year"
                  :disabled="isEdit"
                  label="年度"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="formData.remark"
                  outlined
                  label="备注"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="formData.attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'inner-audit-history',
  model: { prop: 'open', event: 'change' },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    innerAudit: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v == '0' || '必填项不能为空',
      },
    }
  },
  watch: {
    open(val) {
      this.toggleDialog(val)
    },
    'innerAudit.actualAuditDate'(val) {
      this.formData.year = val?.split('-')[0]
    },
  },
  computed: {
    isEdit() {
      return !!this.innerAudit?.id
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async toggleDialog(val) {
      this.formData = this.innerAudit
      this.dialog = val
      await this.$nextTick()
      this.$refs?.form?.resetValidation()
    },
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/seaAffairs/innerAudit/updateHistory'
        : '/business/seaAffairs/innerAudit/add'
      const { errorRaw } = await this.postAsync(url, this.formData)
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
