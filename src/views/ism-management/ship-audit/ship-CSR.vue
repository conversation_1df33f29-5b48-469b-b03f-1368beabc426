<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}CSR
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="ship.shipCode"
                  :rules="[rules.required]"
                  required
                ></v-ship-select>
              </v-col>

              <!-- <v-col cols="12" md="2">
                <v-text-field
                  v-model="ship.year"
                  label="年度"
                  :rules="[rules.year]"
                  dense
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  v-model="ship.halfYearType"
                  label="类型"
                  :items="items"
                  :rules="[rules.required]"
                  dense
                  required
                ></v-select>
              </v-col> -->
              <v-col cols="12" md="2">
                <vs-date-picker
                  v-model="ship.certificateIssuanceDate"
                  label="发证日期"
                  :rules="[rules.time]"
                  dense
                  required
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  v-model="ship.certificateUpdateDate"
                  label="更新日期"
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="ship.handler"
                  label="经办人"
                  :rules="[rules.required]"
                  dense
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  dense
                  v-model="ship.remark"
                  label="备注"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12">
                <v-attach-list
                  :attachments="ship.attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="ship.shipCode"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['CSR:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      :search-remain="searchObj"
      use-ship
      @dbclick="editShip"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.history"
            label="历史数据"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="creatTable"
          v-permission="['CSR:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delShip"
          v-permission="['CSR:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.halfYearType`]="{ item }">
        <div v-if="item.halfYearType == '0'">上半年</div>
        <div v-if="item.halfYearType == '1'">下半年</div>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'ship-CSR',
  attachmentRecords: [],
  created() {
    this.tableName = 'CSR'
    this.reqUrl = '/business/seaAffairs/SspDmlcCsr/sspdmlccsrcsr/page'
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '船旗国', value: 'flagState' },
      { text: '船级社', value: 'shipClassification' },
      { text: '发证日期', value: 'certificateIssuanceDate' },
      { text: '更新日期', value: 'certificateUpdateDate' },
      { text: 'I办主管', value: 'ismManager' },
      { text: '经办人', value: 'handler' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords', sortable: false },
    ]
    this.items = [
      { text: '上半年', value: 0 },
      { text: '下半年', value: 1 },
    ]
  },

  data() {
    return {
      selected: false,
      ship: {
        shipCode: '',
        certificateIssuanceDate: '',
        certificateUpdateDate: '',
        ismManager: '',
        handler: '',
        remark: '',
        attachmentIds: [],
        attachmentRecords: [],
      },
      searchObj: {
        history: false,
      },

      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        time: (v) =>
          /^\d{4}-\d{2}-\d{2}$/.test(v) || '请输入正确的年月日格式如2022-02-02',
        year: (v) => /^\d{4}$/.test(v) || '请输入正确的年格式如2022',
      },
      isEdit: false,
      loading: false,
      formShow: false,
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.ship.attachmentIds = attachmentIds
    },

    async delShip() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/SspDmlcCsr/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
    creatTable() {
      this.ship = {
        shipCode: '',
        certificateIssuanceDate: '',
        certificateUpdateDate: '',
        ismManager: '',
        handler: this.$local.data.get('userInfo').nickName,
        remark: '',
        attachmentIds: [],
        attachmentRecords: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },

    async editShip() {
      this.ship = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      this.updateTaskPromptMassage(this.selected.id)
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/SspDmlcCsr/update'
        : '/business/seaAffairs/SspDmlcCsr/save'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.ship, type: 'CSR' },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.ship = {
        shipCode: '',
        certificateIssuanceDate: '',
        certificateUpdateDate: '',
        ismManager: '',
        handler: '',
        remark: '',
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },

    closeForm() {
      this.$refs.form.reset()
      this.ship = {
        shipCode: '',
        certificateIssuanceDate: '',
        certificateUpdateDate: '',
        ismManager: '',
        handler: '',
        remark: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
