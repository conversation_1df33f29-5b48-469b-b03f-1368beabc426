<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶外审:编辑']"
      :title="`${auditInfo.shipName}-船舶外审`"
      backRouteName="ship-audit-outter-list"
      :subtitles="subtitles"
      :tooltip="`${auditInfo.shipName}-船舶外审`"
      @save="save"
    >
      <template #当前审核记录-基本信息按钮>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="createAudit(false)"
          v-permission="['船舶外审:生成审核记录']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          生成审核记录
        </v-btn>
      </template>
      <template #当前审核记录-基本信息>
        <v-card-text class="pt-0 subtitle-1">
          <v-row>
            <v-col cols="12" sm="6" md="3">
              船名：{{ auditInfo.shipName }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              I办主管：{{ auditInfo.ismManager }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              证书编号：{{ auditInfo.certificateCode }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              窗口日期：{{ auditInfo.planAuditDate }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              证书到期日期：{{ auditInfo.certificateInvalidDate }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              审核类型：{{ auditInfo.status }}
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <template #历史外审记录按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createAudit(true)"
          v-permission="['历史外审记录:添加附加审核']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          添加附加审核
        </v-btn>
        <v-btn
          :disabled="!selectedHistory"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateAudit"
          v-permission="['历史外审记录:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedHistory"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['历史外审记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #历史外审记录>
        <v-card-text>
          <v-table-list
            :headers="auditHistoryHeaders"
            :items="auditInfo.list"
            v-model="selectedHistory"
          >
            <template v-slot:[`item.certificateKind`]="{ item }">
              {{ certificateKinds[item.certificateKind] }}
            </template>
          </v-table-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <outter-audit-history
      v-model="dialog"
      @success="success"
      :outter-audit="outterAudit"
    ></outter-audit-history>
  </v-container>
</template>
<script>
import outterAuditHistory from '@/views/ism-management/ship-audit/components/outter-audit-history'
export default {
  name: 'ship-audit-outter-detail',
  components: {
    outterAuditHistory,
  },
  data() {
    return {
      auditInfo: {
        actualAuditDate: '',
        lastPlanAuditDate: '',
        planAuditDate: '',
        shipClassification: '',
        certificateCode: '',
        shipName: '',
        certificateInvalidDate: '',
        certificateType: '',
        certificateKind: '',
        shipCode: '',
        status: '',
        list: [],
      },
      selectedHistory: false,
      dialog: false,
      outterAudit: {},
    }
  },

  methods: {
    async loadOutterAudit() {
      console.log(this.$route.params.id + ` this.$route.params.id`)
      const { data } = await this.getAsync(
        `/business/seaAffairs/outAudit/detail/${this.$route.params.id}`,
      )
      if (data) {
        // console.log(data)
        this.auditInfo = data
        // this.updateTaskPromptMassage(this.auditInfo.id)
        this.updateTaskPromptMassage(this.$route.params.id)
      }
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    async save(goBack) {
      goBack()
    },
    createAudit(attach = false) {
      this.outterAudit = {
        shipCode: this.auditInfo.shipCode,
        certificateCode: this.auditInfo.certificateCode,
        certificateInvalidDate: this.auditInfo.certificateInvalidDate,
        certificateIssueDate: this.auditInfo.certificateIssueDate,
        actualAuditDate: this.auditInfo.certificateIssueDate,
        certificateType: this.auditInfo.certificateType,
        certificateKind: this.auditInfo.certificateKind,
        newCertificateIssueDate: this.auditInfo.newCertificateIssueDate,
        newCertificateKind: this.auditInfo.newCertificateKind,
        newCertificateInvalidDate: this.auditInfo.newCertificateInvalidDate,
        status: attach ? '附加审核' : this.auditInfo.status,
        handler: this.$local.data.get('userInfo').nickName,
        attachmentRecords: [],
        attachmentIds: [],
      }
      this.dialog = true
    },
    updateAudit() {
      this.outterAudit = { ...this.selectedHistory }
      this.dialog = true
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/outAudit/deleteHistory',
        { id: this.selectedHistory.id },
        false,
      )
      if (errorRaw) {
        // console.log(errorRaw)
        if (errorRaw.msg) this.$dialog.message.error(errorRaw.msg)
        else this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.loadOutterAudit()
      this.selectedHistory = false
    },
    async success() {
      this.selectedHistory = false
      await this.loadOutterAudit()
    },
  },

  mounted() {
    this.loadOutterAudit()
  },
  created() {
    this.auditHistoryHeaders = [
      { text: '证书类型', value: 'certificateType' },
      { text: '证书编号', value: 'certificateCode' },
      { text: '证书性质', value: 'certificateKind' },
      { text: '签发日期', value: 'certificateIssueDate' },
      { text: '窗口日期', value: 'planAuditDate' },
      { text: '实际审核时间', value: 'actualAuditDate' },
      { text: '审核地点', value: 'place' },
      { text: '证书到期日期', value: 'certificateInvalidDate' },
      { text: '审核类型', value: 'status' },
      { text: '经办人', value: 'handler' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.subtitles = ['当前审核记录-基本信息', '历史外审记录']
    this.certificateKinds = ['长期', '短期']
  },
}
</script>

<style></style>
