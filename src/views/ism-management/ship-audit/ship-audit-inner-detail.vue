<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶内审:编辑']"
      :title="`${auditInfo.shipName}-船舶内审`"
      backRouteName="ship-audit-inner-list"
      :subtitles="subtitles"
      :tooltip="`${auditInfo.shipName}-船舶内审`"
      @save="save"
    >
      <template #基本信息>
        <v-card-text class="pt-0 subtitle-1">
          <v-row>
            <v-col cols="12" sm="6" md="3">
              船名：{{ auditInfo.shipName }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              船级社：{{ auditInfo.shipClassification }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              I办主管：{{ auditInfo.ismManager }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              实际审核日期（最新）：{{ auditInfo.lastAuditDate }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              计划审核日期：{{ auditInfo.planAuditDate }}
            </v-col>
            <v-col cols="12" sm="6" md="3">
              到期日期：{{ auditInfo.expirationDate }}
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <template #历史内审记录按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createAudit"
          v-permission="['历史内审记录:新建']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedHistory"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateAudit"
          v-permission="['历史内审记录:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedHistory"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['历史内审记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #历史内审记录>
        <v-card-text>
          <v-table-list
            :headers="auditHistoryHeaders"
            :items="auditInfo.list"
            v-model="selectedHistory"
          ></v-table-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <inner-audit-history
      v-model="dialog"
      @success="success"
      :inner-audit="innerAudit"
    ></inner-audit-history>
  </v-container>
</template>
<script>
import innerAuditHistory from '@/views/ism-management/ship-audit/components/inner-audit-history'
export default {
  name: 'ship-audit-inner-detail',
  components: {
    innerAuditHistory,
  },
  data() {
    return {
      auditInfo: {
        actualAuditDate: '',
        lastPlanAuditDate: '',
        lastAuditDate: '',
        planAuditDate: '',
        shipClassification: '',
        expirationDate: '',
        shipName: '',
        shipCode: '',
        year: '',
        list: [],
      },
      selectedHistory: false,
      dialog: false,
      innerAudit: {},
    }
  },

  methods: {
    async loadInnerAudit() {
      const { data } = await this.getAsync(
        `/business/seaAffairs/innerAudit/detail/${this.$route.params.id}`,
      )
      if (data) {
        this.auditInfo = data
        this.updateTaskPromptMassage(this.auditInfo.id)
      }
    },
    async updateTaskPromptMassage(id) {
      // if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/taskpromptmassage/update',
        { businessId: id },
        false,
      )
      if (errorRaw) {
        // this.$dialog.message.error(`船岸交互信息提醒保存失败，请重试`)
        console.log(id + `船岸交互信息提醒保存失败`)
        return
      }
      // this.$dialog.message.success(`船岸交互信息提醒保存成功`)
      console.log(id + `船岸交互信息提醒保存成功`)
      // await this.$refs.table.loadTableData()
      // this.closeForm()
    },
    async save(goBack) {
      goBack()
    },
    createAudit() {
      this.innerAudit = {
        shipCode: this.auditInfo.shipCode,
        handler: this.$local.data.get('userInfo').nickName,
        attachmentRecords: [],
      }
      this.dialog = true
    },
    updateAudit() {
      this.innerAudit = { ...this.selectedHistory }
      this.dialog = true
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/innerAudit/deleteHistory',
        { id: this.selectedHistory.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.success()
    },
    async success() {
      this.selectedHistory = false
      await this.loadInnerAudit()
    },
  },

  mounted() {
    this.loadInnerAudit()
  },
  created() {
    this.auditHistoryHeaders = [
      { text: '实际审核日期', value: 'actualAuditDate' },
      { text: '经办人', value: 'handler' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.subtitles = ['基本信息', '历史内审记录']
  },
}
</script>

<style></style>
