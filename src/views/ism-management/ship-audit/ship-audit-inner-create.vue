<template>
  <v-container fluid>
    <v-card>
      <v-card-title>增加船舶内审记录</v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :rules="[rules.required]"
                  v-model="formData.shipCode"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="I办主管"
                  :rules="[rules.required]"
                  v-model="formData.ismManager"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="经办人"
                  :rules="[rules.required]"
                  v-model="formData.handler"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="formData.actualAuditDate"
                  use-today
                  label="实际审核日期"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.year"
                  label="年度"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="备注"
                  v-model="formData.remark"
                  dense
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="attachments"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['船舶内审:创建']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  创建
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'ship-audit-inner-create',
  mixins: [routerControl],
  data() {
    return {
      formData: {
        handler: this.$local.data.get('userInfo').nickName,
        actualAuditDate: '',
        attachmentIds: [],
        year: '',
      },
      dateMenu: false,
      rules: {
        required: (v) => !!v || v == '0' || '必填项不能为空',
      },
    }
  },

  watch: {
    'formData.actualAuditDate'(val) {
      if (val) {
        this.formData.year = val.split('-')[0]
      }
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const { data } = await this.postAsync(
        '/business/seaAffairs/innerAudit/init',
        this.formData,
      )
      if (data) {
        this.closeAndTo('ship-audit-inner-detail', { id: data }, {})
      }
    },
  },

  mounted() {},
  created() {
    this.attachments = []
  },
}
</script>

<style></style>
