<template>
  <v-container fluid>
    <v-card>
      <v-card-title>新加船舶外审-临时审核</v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :rules="[rules.required]"
                  v-model="formData.shipCode"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="formData.actualAuditDate"
                  label="实际审核日期"
                  use-today
                  :rules="[rules.required]"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.handler"
                  label="经办人"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="I办主管"
                  :rules="[rules.required]"
                  v-model="formData.ismManager"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.certificateCode"
                  label="证书编号"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="formData.certificateKind"
                  :items="certificateKinds"
                  label="证书性质"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="formData.certificateType"
                  :items="certificateTypes"
                  label="证书类型"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="formData.certificateIssueDate"
                  label="签发日期"
                  use-today
                  outlined
                  dense
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="formData.certificateInvalidDate"
                  label="到期日期"
                  use-today
                  outlined
                  dense
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.place"
                  label="审核地点"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="备注"
                  v-model="formData.remark"
                  dense
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="attachments"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['船舶外审:创建']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  创建
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'ship-audit-outter-create',
  mixins: [routerControl],
  data() {
    return {
      formData: {
        handler: this.$local.data.get('userInfo').nickName,
        actualAuditDate: '',
        attachmentIds: [],
        status: 'TEMP_AUDIT',
      },
      rules: {
        required: (v) => !!v || v == '0' || '必填项不能为空',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async save() {
      if (!this.$refs.form.validate()) {
        console.log('ddd')
        return
      }
      const { data } = await this.postAsync(
        '/business/seaAffairs/outAudit/init',
        this.formData,
      )
      // const { errorRaw } = await this.postAsync(//打印提示优化（尝试）
      //   '/business/seaAffairs/outAudit/init',
      //   this.formData,
      //   false,
      // )
      if (data) {
        this.closeAndTo('ship-audit-outter-detail', { id: data }, {})
      }
      // if (errorRaw) {//打印提示优化（尝试）
      //   console.log(errorRaw)
      //   if (errorRaw.data)
      //     this.$dialog.message.error(errorRaw.msg + ':' + errorRaw.data)
      //   else this.$dialog.message.error(errorRaw.msg)
      //   return
      // }
    },
  },

  mounted() {},
  created() {
    this.certificateTypes = ['ISSC', 'MLC', 'SMC']
    this.certificateKinds = [
      { text: '长期', value: '0' },
      { text: '短期', value: '1' },
    ]
    this.attachments = []
  },
}
</script>

<style></style>
