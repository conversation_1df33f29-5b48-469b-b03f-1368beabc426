<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['海员就业协议:编辑']"
      :title="`海员就业协议${
        ['', '-未提交', '-审批中', '-审批通过', '-驳回'][detailInfo.status]
      }`"
      tooltip="海员就业协议"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-save="isSave"
      :can-submit="
        !detailInfo.auditParams ||
        detailInfo.auditParams.taskId ||
        detailInfo.status === '1' ||
        detailInfo.status === '4'
      "
      @save="save"
      @submit="submit"
    >
      <template v-if="detailInfo.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detailInfo.auditParams"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-form
          :readonly="detailInfo.status === '2' || detailInfo.status === '3'"
          ref="form"
        >
          <v-container fluid>
            <v-divider class="my-4"></v-divider>
            <v-row>
              <v-col cols="12" md="2">
                <v-dialog-select
                  v-model="detailInfo.creUserId"
                  item-value="creId"
                  item-text="creName"
                  req-url="/business/crew/osmOnShipCrew/forShipPage"
                  :rules="[rules.required]"
                  label="船员姓名"
                  :headers="creHeaders"
                  :init-selected="initSelected"
                  :search-remain="creSearchRemain"
                  @select="selectCrew"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        v-model="creSearchRemain.name"
                      ></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="detailInfo.position"
                  label="职务"
                  required
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="detailInfo.shipName"
                  label="船舶名称"
                  required
                  outlined
                  :rules="[rules.required]"
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="上船时间"
                  required
                  outlined
                  dense
                  :rules="[rules.required]"
                  v-model="detailInfo.onBoardTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="协议生效时间"
                  required
                  outlined
                  dense
                  :rules="[rules.required]"
                  v-model="detailInfo.beginTime"
                ></vs-date-picker>
              </v-col>
            </v-row>
            <v-attach-list
              :attachments="detailInfo.attachmentRecords"
              :disabled="
                !detailInfo.status ||
                detailInfo.status === '3' ||
                detailInfo.status === '2'
              "
              @change="changeAttachment"
            ></v-attach-list>
          </v-container>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  components: {},
  mixins: [dictHelper],
  name: 'agreement-detailInfo',
  created() {
    this.backRouteName = 'agreement-list'
    this.creHeaders = [
      { text: '船员姓名', value: 'creName' },
      { text: '实际职务', value: 'post' },
      { text: '上船时间', value: 'onBoardTime' },
    ]
    this.initSelected = {}
    this.attachmentHeader = [
      { text: '名称', value: 'name' },
      { text: '大小(kb)', value: 'fileSize' },
      { text: '上传时间', value: 'createTime' },
      { text: '上传人', value: 'userName' },
    ]
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
  },
  data() {
    return {
      subtitles: ['基本信息'],
      detailInfo: { status: '0' },
      initSelected: {},
      creSearchRemain: {},
      initialData: {},
      attachmentDialog: false,
      attachments: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    selectCrew(val) {
      console.log('val', val)
      this.detailInfo.creName = val.creName
      this.detailInfo.creUserId = val.creId
      this.detailInfo.position = val.post
      this.detailInfo.shipName = val.shipName
      this.detailInfo.shipCode = val.shipCode
      this.detailInfo.onBoardTime = val.onBoardTime
      this.detailInfo.beginTime = val.onBoardTime
    },
    async getDetailInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/seaAffairs/crewWorkAgreement/getOne`,
          { id: this.$route.params.id },
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        this.initSelected = {
          creId: this.detailInfo.creUserId,
          creName: this.detailInfo.creName,
        }
      }
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      const url =
        this.$route.params.id === `new`
          ? '/business/seaAffairs/crewWorkAgreement/save'
          : '/business/seaAffairs/crewWorkAgreement/update'
      const { errorRaw, data } = await this.postAsync(url, this.detailInfo)
      if (notMove) return data
      if (!errorRaw) goBack()
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (
        (this.detailInfo.status == '1' || this.detailInfo.status == '4') &&
        !this.isFilled &&
        !(await this.$dialog.msgbox.confirm(
          '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return

      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detailInfo.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/crewWorkAgreement/process/submit',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
  },
  mounted() {
    this.getDetailInfo()
  },
}
</script>

<style>
.scroll-content1 {
  /* position: sticky; */
  height: 400px;
  overflow-y: auto;
}
</style>
