<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      :single-select="false"
      use-status
      use-ship
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          color="success"
          class="mx-1"
          to="/ism-management/crew-management/crew-work-agreement/agreement-detail/new"
          v-permission="['海员就业协议:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!canDel"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delApply"
          v-permission="['海员就业协议:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!canSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="submitApply"
          v-permission="['海员就业协议:审批通过']"
        >
          <v-icon left>mdi-send</v-icon>
          审批通过
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{
            item.status == 2
              ? `待[${item.businessStatus}]审批`
              : statuses[item.status]
          }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  components: {},
  name: 'agreement-list',
  created() {
    this.tableName = '海员就业协议'
    this.reqUrl = '/business/seaAffairs/crewWorkAgreement/page'
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '船员名称', value: 'creName' },
      { text: '操作人', value: 'handlerName' },
      { text: '上船时间', value: 'onBoardTime' },
      { text: '协议生效时间', value: 'beginTime' },
      { text: '审批状态', value: 'status' },
      { text: '附件', value: 'attachmentRecords', sortable: false },
    ]
    this.pushParams = { name: 'agreement-detail' }
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '上船时间',
      interval: true,
    }
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
  },

  data() {
    return {
      selected: [],
      searchObj: {},
      loading: false,
      formData: {},
      dialog: false,
    }
  },
  computed: {
    canDel() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            // item.dataSource == '单次预算' &&
            item.status == 1 || item.status == 4,
          //  &&
          // item.applyUser == this.$local.data.get('userInfo').id,
        )
      )
    },
    canSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) => item?.auditParams?.taskId && item.status == 2,
        )
      )
    },
  },
  methods: {
    success(id) {
      this.$router.push({
        name: 'agreement-detail',
        params: { id },
      })
    },
    async delApply() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/crewWorkAgreement/deleteBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async submitApply() {
      if (!(await this.$dialog.msgbox.confirm('确定审批通过所选记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/batchCompleteTaskAndCommentAndSetVar',
        this.selected.map((item) => ({
          adopt: true,
          comment: '',
          params: {},
          taskId: item.auditParams.taskId,
        })),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`提交成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
  },

  mounted() {},
}
</script>

<style></style>
