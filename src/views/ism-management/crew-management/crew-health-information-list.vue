<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      :single-select="false"
      use-status
      use-ship
    >
      <template #searchflieds>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col> -->
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            clearable
            v-model="searchObj.inspectionType"
            label="检查类型"
            outlined
            dense
            :items="insTypes"
          ></v-select>
        </v-col> -->
      </template>
      <template #btns>
        <v-btn
          outlined
          color="success"
          class="mx-1"
          @click="dialog = true"
          v-permission="['船员健康记录:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!canDel"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delApply"
          v-permission="['船员健康记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <!-- <v-btn
          :disabled="!canSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="submitApply"
          v-permission="['船员健康记录:审批通过']"
        >
          <v-icon left>mdi-send</v-icon>
          审批通过
        </v-btn> -->
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{
            item.status == 2
              ? `待[${item.businessStatus}]审批`
              : statuses[item.status]
          }}
        </v-chip>
      </template>
    </v-table-searchable>
    <add-security-dialog-new
      @success="success"
      v-model="dialog"
    ></add-security-dialog-new>
  </v-container>
</template>
<script>
import addSecurityDialogNew from './private/add-crew-health-information-dialog-new.vue'
// attachmentRecords	附件列表，限制最多5个文件	array	CommonAttachment
// bonus	检查考评奖金额	number
// captain	船长	string
// cheifEngineer	轮机长	string
// cnShipPort	中文船名	string
// enShipName	英文船名	string
// id	物理主键	string
// inspectionTime	检查时间	string
// inspectionType	检查类型	string
// inspector	检查人员	string
// isClosed	是否结案	boolean
// marineSupervisor	海务主管
export default {
  components: { addSecurityDialogNew },
  name: 'crew-health-information-form-list',
  created() {
    this.tableName = '船员健康记录'
    this.reqUrl = '/business/seaAffairs/CrewHealthInformationForm/record/page'
    this.headers = [
      // { text: '海务主管', value: 'marineSupervisor' },
      { text: '船名', value: 'shipInfo' },
      { text: '月度', value: 'yearMonthTime' },
      { text: '创建时间', value: 'createTime' },
      { text: '更新时间', value: 'updateTime' },
      { text: '审批状态', value: 'status' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.insTypes = [
      { text: '登轮检查', value: '0' },
      { text: '船舶自查', value: '1' },
      { text: 'PSC检查', value: '2' },
      { text: 'FSC检查', value: '3' },
    ]
    this.pushParams = { name: 'crew-health-information-form-detail' }
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '日期',
      interval: true,
    }
    this.statuses = ['', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', 'info', 'warning', 'success', 'error']
  },

  data() {
    return {
      selected: [],
      searchObj: {},
      loading: false,
      formData: {},
      dialog: false,
    }
  },
  computed: {
    canDel() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            // item.dataSource == '单次预算' &&
            item.status == 1 || item.status == 4,
          //  &&
          // item.applyUser == this.$local.data.get('userInfo').id,
        )
      )
    },
    // canCopy() {
    //   return (
    //     this.selected.length > 0 &&
    //     this.selected.every((item) => item.dataSource == '单次预算')
    //   )
    // },
    canSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) => item?.auditParams?.taskId && item.status == 2,
        )
      )
    },
    // canSubmit2() {
    //   return (
    //     this.selected.length > 0 &&
    //     this.selected.every(
    //       (item) =>
    //         ((item?.status == '1' &&
    //           item?.businessStatus != '待实际申请人确认') ||
    //           item?.status == '4') &&
    //         item.dataSource == '单次预算' &&
    //         item.applyUser == this.$local.data.get('userInfo').id,
    //     )
    //   )
    // },
    // canSubmit3() {
    //   return (
    //     this.selected.length > 0 &&
    //     this.selected.every(
    //       (item) =>
    //         item?.status == '3' &&
    //         (item?.businessStatus == '审批通过' ||
    //           item?.businessStatus == 'OA立项审批通过') &&
    //         (item.dataSource == '单次预算' ||
    //           // item.dataSource == '航修修理单' ||
    //           item.dataSource == '批量预算'),
    //     )
    //   )
    // },
    // canSubmit4() {
    //   return (
    //     this.selected.length > 0 &&
    //     this.selected.every(
    //       (item) =>
    //         item?.status == '3' &&
    //         item?.budgetTime &&
    //         item?.businessStatus == '凭证SAP执行成功',
    //     )
    //   )
    // },
    // canSubmit5() {
    //   // return (
    //   //   this.selected.length > 0 &&
    //   //   this.selected.every(
    //   //     (item) =>
    //   //       item?.status == '3' &&
    //   //       item?.budgetTime &&
    //   //       (item?.businessStatus == '凭证SAP执行成功' ||
    //   //         item?.businessStatus == '预算已发送'),
    //   //   )
    //   // )
    //   return (
    //     this.selected.length > 0 &&
    //     this.selected.every(
    //       (item) =>
    //         item?.status == '3' &&
    //         item.applyUser == this.$local.data.get('userInfo').id,
    //     )
    //   )
    //   // return (
    //   //   this.selected.length > 0 &&
    //   //   this.selected.every(
    //   //     (item) =>
    //   //       item?.status == '3' &&
    //   //       item.applyUser == this.$local.data.get('userInfo').id &&
    //   //       (item?.businessStatus == '待发送OA立项' ||
    //   //         item?.businessStatus == 'OA立项审批失败'),
    //   //   )
    //   // )
    // },
    // canSubmit55() {
    //   return (
    //     this.selected.length > 0 &&
    //     this.selected.every(
    //       (item) =>
    //         item?.status == '3' && item?.businessStatus == '待单船财务确认废弃',
    //     )
    //   )
    // },
    // canSubmitOA() {
    //   return (
    //     this.selected.length > 0 &&
    //     this.selected.every(
    //       (item) =>
    //         item?.status == '3' &&
    //         item?.needSendOa == 1 &&
    //         (item?.businessStatus == '待发送OA立项' ||
    //           item?.businessStatus == 'OA立项审批失败'),
    //     )
    //   )
    // },
    // dateRangeText() {
    //   return this.dates1?.start && this.dates1?.end
    //     ? `${this.dates1.start.toLocaleDateString()} 至 ${this.dates1?.end.toLocaleDateString()}`
    //     : ''
    // },
    // dateRangeText2() {
    //   return this.dates2?.start && this.dates2?.end
    //     ? `${this.dates2.start.toLocaleDateString()} 至 ${this.dates2?.end.toLocaleDateString()}`
    //     : ''
    // },
  },
  methods: {
    // async save() {
    //   if (!this.$refs.form.validate()) {
    //     return
    //   }
    //   this.formData = {}
    //   const url = '/business/seaAffairs/securityCheck/record/saveRecord'
    //   const { errorRaw, data } = await this.postAsync(url, {
    //     ...this.formData,
    //   })
    //   if (!errorRaw) {
    //     this.$emit('change', false)
    //     this.$emit('success', data)
    //   }
    // },
    success(id) {
      this.$router.push({
        name: 'crew-health-information-form-detail',
        params: { id },
      })
    },
    async delApply() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/CrewHealthInformationForm/record/deleteBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async delSecCheck() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/CrewHealthInformationForm/record/delete',
        {
          id: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = []
    },
    async submitApply() {
      if (!(await this.$dialog.msgbox.confirm('确定审批通过所选记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/batchCompleteTaskAndCommentAndSetVar',
        this.selected.map((item) => ({
          adopt: true,
          comment: '',
          params: {},
          taskId: item.auditParams.taskId,
        })),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`提交成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
  },

  mounted() {},
}
</script>

<style></style>
