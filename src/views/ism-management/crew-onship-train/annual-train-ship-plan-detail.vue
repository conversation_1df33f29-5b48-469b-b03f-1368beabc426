<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['年度培训计划:编辑']"
      :title="titleName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      :can-save="detailInfo.status === '1' || detailInfo.status === '4'"
      :can-submit="
        !detailInfo.auditParams ||
        detailInfo.auditParams.taskId ||
        detailInfo.status === '1' ||
        detailInfo.status === '4'
      "
      @submit="submit"
    >
      <template v-if="detailInfo.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detailInfo.auditParams"
              :shipCode="detailInfo.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:年度计划信息>
        <v-form ref="form2">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="年度"
                  outlined
                  readonly
                  dense
                  v-model="detailInfo.year"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="计划名称"
                  readonly
                  outlined
                  dense
                  v-model="detailInfo.name"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="类型"
                  outlined
                  dense
                  :items="typeDict"
                  readonly
                  v-model="detailInfo.type"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="操作人"
                  required
                  outlined
                  dense
                  v-model="detailInfo.handlerName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-textarea
                  label="备注"
                  outlined
                  readonly
                  dense
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:年度计划内容>
        <v-data-table
          show-select
          :headers="headers"
          :items="detailInfo.details"
          dense
          :single-select="false"
          item-key="id"
          v-model="selected"
          :items-per-page="5"
        >
          <template v-slot:[`item.type`]="{ item }">
            <span v-if="item.type === '1'">保安演习/演练</span>
            <span v-if="item.type === '2'">应急演习</span>
            <span v-if="item.type === '3'">ISM/SMS培训</span>
            <span v-if="item.type === '4'">保安培训</span>
          </template>
        </v-data-table>
      </template>
      <template v-slot:年度计划内容按钮 v-if="!newCard">
        <!--        <v-btn-->
        <!--          outlined-->
        <!--          tile-->
        <!--          color="success"-->
        <!--          class="mx-1"-->
        <!--          :loading="sloading"-->
        <!--          small-->
        <!--          @click="openForm"-->
        <!--          v-permission="['年度培训计划:新增']"-->
        <!--        >-->
        <!--          <v-icon left>mdi-plus-circle</v-icon>-->
        <!--          新增-->
        <!--        </v-btn>-->
        <v-btn
          small
          :disabled="!selected.length"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="openEdit"
          v-permission="['年度培训计划:修改内容']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected.length"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          :loading="loading1"
          @click="delAudit"
          v-permission="['年度培训计划:删除内容']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <ship-plan-month-choose-dialog
      @success="success"
      v-model="editDialog"
      :initialData="selected"
    ></ship-plan-month-choose-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import ShipPlanMonthChooseDialog from '@/views/ism-management/crew-onship-train/ship-plan-month-choose-dialog.vue'
export default {
  name: 'annual-train-ship-plan-detail',
  components: { ShipPlanMonthChooseDialog },
  mixins: [dictHelper],
  created() {
    this.backRouteName = 'annual-train-ship-plan'
    this.subtitles = ['年度计划信息', '年度计划内容']
    this.newCard = this.$route.params.id
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目类型', value: 'type' },
      { text: '相关人员', value: 'stakeholder' },
      // { text: '备注', value: 'remark' },
      { text: '实施月份', value: 'months', sortable: false },
    ]
    this.initSelected = {}
  },
  data() {
    return {
      detailInfo: {},
      searchRemain: {},
      selected: [],
      dialog1: false,
      editDialog: false,
      sloading: false,
      loading1: false,
      typeDict: [
        { text: '保安演习/演练', value: '1' },
        { text: '应急演习', value: '2' },
        { text: 'ISM/SMS培训', value: '3' },
        { text: '保安培训', value: '4' },
      ],
      titleName: '船年度培训计划详情',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  computed: {
    newCard: {
      get() {
        return this.$route.params.id === `new` || !this.canEdit()
      },
      set() {},
    },
  },
  methods: {
    openEdit() {
      // console.log('opendEdit!!')
      this.editDialog = true
      // console.log('editDialog', this.editDialog)
    },
    async save(goback, notMove = false) {
      if (!this.$refs.form2.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      if (!this.canEdit()) {
        this.$dialog.message.error('仅草稿、已驳回状态可修改')
        return
      }
      const url = '/business/seaAffairs/annualShipPlan/update'
      const { errorRaw } = await this.postAsync(url, this.detailInfo)
      if (notMove) return this.detailInfo.id
      if (errorRaw) {
        return
      }
      this.detailInfo = {}
      this.$dialog.message.success('操作成功')
      goback()
    },
    async getDetailInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/seaAffairs/annualShipPlan/total/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        // console.log('detailInfo', this.detailInfo)
        this.titleName = this.detailInfo.name
      }
    },
    openForm() {
      this.sloading = true
      this.dialog1 = true
    },
    change(val) {
      this.dialog1 = val
      this.sloading = false
    },
    async success() {
      this.sloading = false
      this.editDialog = false
      this.selected = []
      await this.getDetailInfo()
    },
    canEdit() {
      return this.detailInfo.status === '1' || this.detailInfo.status === '4'
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      // console.log(this.detailInfo)
      // if (!this.canEdit()) {
      //   this.$dialog.message.error('仅草稿、已驳回状态可提交')
      //   return
      // }
      if (
        !(await this.$dialog.msgbox.confirm(
          '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return

      if (!this.detailInfo.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/annualShipPlan/process/submit',
          { id: this.detailInfo.id },
        )
        if (!errorRaw) {
          this.$dialog.message.success('操作成功')
          goBack()
        }
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async delAudit() {
      this.loading1 = true
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) {
        this.loading1 = false
        return
      }
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除'))) {
        this.loading1 = false
        return
      }
      let idList = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/annualShipPlanDetail/deleteBatch`,
        idList,
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功！')
      await this.getDetailInfo()
      this.selected = []
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
