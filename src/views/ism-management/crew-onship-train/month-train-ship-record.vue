<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select
            clearable
            v-model="searchRemain.shipCode"
          ></v-ship-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            label="年度"
            outlined
            clearable
            dense
            v-model="searchRemain.year"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="月份"
            outlined
            clearable
            dense
            v-model="searchRemain.month"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.type"
            label="类型"
            :items="typeDict"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.status"
            label="状态"
            :items="statusMap"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="openForm"
          v-permission="['月度培训记录:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          outlined
          tile
          color="info"
          :loading="loading1"
          class="mx-1"
          @click="createRecord"
          v-permission="['月度培训记录:初始化本月记录']"
        >
          <v-icon left>mdi-chart-donut</v-icon>
          初始化本月记录
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="confirmRecord"
          v-permission="['月度培训记录:确认']"
        >
          <v-icon left>mdi-emoticon-outline</v-icon>
          确认
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="drawBack"
          v-permission="['月度培训记录:退回']"
        >
          <v-icon left>mdi-arrow-u-right-bottom</v-icon>
          退回
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['月度培训记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          outlined
          :disabled="!selected"
          tile
          color="info"
          class="mx-1"
          @click="exportTrain"
          v-permission="['月度培训记录:导出月培训记录表']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          导出记录表
        </v-btn>
      </template>
      <template v-slot:[`item.type`]="{ item }">
        <span v-if="item.type === '1'">保安演习/演练</span>
        <span v-if="item.type === '2'">应急演习</span>
        <span v-if="item.type === '3'">ISM/SMS培训</span>
        <span v-if="item.type === '4'">保安培训</span>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === '1'" small dark color="gray">
          未进行
        </v-chip>
        <v-chip v-else-if="item.status === '0'" small dark color="#6D4C41">
          草稿
        </v-chip>
        <v-chip v-else-if="item.status === '2'" small dark color="info">
          进行中
        </v-chip>
        <v-chip v-else-if="item.status === '3'" small dark color="#FFAB00">
          已完成
        </v-chip>
        <v-chip v-else-if="item.status === '4'" small dark color="success">
          {{ item.confirmName }}已确认
        </v-chip>
        <v-chip v-else-if="item.status === '5'" small dark color="error">
          已退回
        </v-chip>
      </template>
    </v-table-searchable>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="dialog"
    >
      <v-card>
        <v-card-title>
          新增培训/演习记录
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="3">
                  <v-dialog-select
                    label="所属年度培训计划"
                    dense
                    outlined
                    table-name="年度培训计划"
                    :headers="creHeaders1"
                    :reqUrl="`/business/seaAffairs/annualShipPlan/pageForMonth`"
                    itemText="name"
                    itemValue="id"
                    @select="planSelect"
                    :search-remain="planSearchRemain"
                    v-model="formData.planId"
                  >
                    <template #searchflieds>
                      <v-col cols="12" sm="6" md="3">
                        <v-text-field
                          label="名称"
                          outlined
                          clearable
                          dense
                          v-model="planSearchRemain.name"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="年度"
                          outlined
                          clearable
                          dense
                          v-model="planSearchRemain.year"
                        ></v-text-field>
                      </v-col>
                    </template>
                    <template v-slot:[`item.type`]="{ item }">
                      <span v-if="item.type === '1'">保安演习/演练</span>
                      <span v-if="item.type === '2'">应急演习</span>
                      <span v-if="item.type === '3'">ISM/SMS培训</span>
                      <span v-if="item.type === '4'">保安培训</span>
                    </template>
                  </v-dialog-select>
                </v-col>
                <v-col cols="12" md="3">
                  <v-dialog-select
                    label="项目名称"
                    dense
                    outlined
                    table-name="项目选择"
                    :headers="creHeaders"
                    :reqUrl="`/business/seaAffairs/crewShipTrainItem/page`"
                    itemText="itemName"
                    itemValue="id"
                    @select="select"
                    :search-remain="diaSearchRemain"
                    v-model="formData.itemId"
                  >
                    <template #searchflieds>
                      <v-col cols="12" sm="6" md="3">
                        <v-text-field
                          label="项目名称"
                          outlined
                          clearable
                          dense
                          v-model="diaSearchRemain.itemName"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          v-model="diaSearchRemain.type"
                          label="项目类型"
                          :items="typeDict"
                          outlined
                          clearable
                          dense
                        ></v-select>
                      </v-col>
                    </template>
                  </v-dialog-select>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                    label="年度"
                    v-model="formData.year"
                    :rules="[rules.required]"
                    required
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3">
                  <v-text-field
                    label="月份"
                    v-model="formData.month"
                    :rules="[rules.required]"
                    required
                    outlined
                    dense
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save()"
                    block
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    保存
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import moment from 'moment'
export default {
  name: 'month-train-ship-record',
  created() {
    this.tableName = '月度培训/演习记录'
    this.reqUrl = '/business/seaAffairs/mTrainShipR/page'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '年度', value: 'year' },
      { text: '月份', value: 'month' },
      { text: '项目名称', value: 'itemName' },
      { text: '类型', value: 'type' },
      { text: '状态', value: 'status' },
      { text: '附件', value: 'attachments', sortable: false },
    ]
    this.creHeaders = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目类型', value: 'type' },
      { text: '相关人员', value: 'stakeholder' },
      { text: '备注', value: 'remark' },
    ]
    this.creHeaders1 = [
      { text: '船舶名称', value: 'shipName' },
      { text: '年度', value: 'year' },
      { text: '名称', value: 'name' },
      { text: '类型', value: 'type' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'month-train-ship-record-detail',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {
        year: moment().year(),
        month: moment().month() + 1,
      },
      diaSearchRemain: {},
      planSearchRemain: {},
      attachmentDialog: false,
      formData: {},
      isEdit: false,
      loading: false,
      loading1: false,
      dialog: false,
      formShow: false,
      typeDict: [
        { text: '保安演习/演练', value: 1 },
        { text: '应急演习', value: 2 },
        { text: 'ISM/SMS培训', value: 3 },
        { text: '保安培训', value: 4 },
      ],
      statusMap: [
        { text: '草稿', value: 0 },
        { text: '未进行', value: 1 },
        { text: '进行中', value: 2 },
        { text: '已完成', value: 3 },
        { text: '已确认', value: 4 },
        { text: '已退回', value: 5 },
      ],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    async delItem() {
      if (this.selected.status === '4') {
        this.$dialog.message.error('培训记录已确认不可删除')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/mTrainShipR/delete',
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    select(val) {
      this.formData.itemName = val.itemName
      this.formData.type = val.type
      this.formData.stakeholder = val.stakeholder
      this.formData.remark = val.remark
      // console.log('formData------------->')
      // console.log(this.formData)
    },
    planSelect(val) {
      this.formData.year = val.year
    },
    async createRecord() {
      if (!(await this.$dialog.msgbox.confirm('是否确认初始化本月记录？')))
        return
      this.loading1 = true
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/mTrainShipR/init',
        { year: moment().year(), month: moment().month() + 1 },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      await this.$refs.table.loadTableData()
    },
    async save() {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/mTrainShipR/save',
        this.formData,
      )
      if (errorRaw) {
        return
      }
      this.formData = {}
      this.$dialog.message.success('操作成功')
      this.closeForm()
      await this.$refs.table.loadTableData()
    },
    openForm() {
      this.dialog = true
    },
    closeForm() {
      this.dialog = false
      this.formData = {}
    },
    async confirmRecord() {
      if (this.selected.status !== '3') {
        this.$dialog.message.error(`仅已完成状态记录才可确认`)
        return
      }
      if (!(await this.$dialog.msgbox.confirm('请确认信息是否正确？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/mTrainShipR/confirm',
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      await this.$refs.table.loadTableData()
    },
    async drawBack() {
      if (this.selected.status !== '3') {
        this.$dialog.message.error(`仅已完成状态记录才可退回`)
        return
      }
      if (!(await this.$dialog.msgbox.confirm('是否确认退回？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/mTrainShipR/drawBack',
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      await this.$refs.table.loadTableData()
    },
    exportTrain() {
      if (this.selected.status !== '4') {
        this.$dialog.message.error(`仅可导出已确认的记录`)
        return
      }
      const baseUrl = '/api/business/seaAffairs/mTrainShipR/exportTrain'
      const downloadUrl = `${baseUrl}?id=${encodeURIComponent(
        this.selected.id,
      )}`
      console.log('url', downloadUrl)
      window.location.href = downloadUrl
    },
  },

  mounted() {},
}
</script>

<style></style>
