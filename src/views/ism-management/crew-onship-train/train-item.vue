<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }}{{ tableName }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0" fluid>
              <v-row>
                <v-col cols="12" md="2">
                  <v-select
                    v-model="formData.type"
                    label="项目类型"
                    :rules="[rules.required]"
                    :items="typeDict"
                    outlined
                    required
                    dense
                  ></v-select>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="formData.itemName"
                    label="项目名称"
                    :rules="[rules.required]"
                    outlined
                    required
                    dense
                  ></v-textarea>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="formData.stakeholder"
                    label="相关人员"
                    :rules="[rules.required]"
                    outlined
                    dense
                    required
                  ></v-textarea>
                </v-col>
                <!--                <v-col cols="12" md="4">-->
                <!--                  <v-textarea-->
                <!--                    v-model="formData.remark"-->
                <!--                    outlined-->
                <!--                    label="备注"-->
                <!--                    rows="2"-->
                <!--                    dense-->
                <!--                  ></v-textarea>-->
                <!--                </v-col>-->
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    v-permission="['培训项目库:编辑']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    {{ isEdit ? '修改' : '新增' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            label="项目名称"
            outlined
            clearable
            dense
            v-model="searchRemain.itemName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.type"
            label="项目类型"
            :items="typeDict"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!--        <v-import-btn-->
        <!--          :import-url="`/business/seaAffairs/crewShipTrainItem/importExcel`"-->
        <!--          @importSuccess="importSuccess"-->
        <!--          :buttonLabel="'导入培训项目'"-->
        <!--          v-permission="['培训项目库:导入培训项目']"-->
        <!--        ></v-import-btn>-->
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['培训项目库:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['培训项目库:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.type`]="{ item }">
        <span v-if="item.type === '1'">保安演习/演练</span>
        <span v-if="item.type === '2'">应急演习</span>
        <span v-if="item.type === '3'">ISM/SMS培训</span>
        <span v-if="item.type === '4'">保安培训</span>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'train-item',
  created() {
    this.tableName = '培训/演习项目库'
    this.reqUrl = '/business/seaAffairs/crewShipTrainItem/page'
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目类型', value: 'type' },
      { text: '相关人员', value: 'stakeholder' },
      // { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      attachmentDialog: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      typeDict: [
        { text: '保安演习/演练', value: '1' },
        { text: '应急演习', value: '2' },
        { text: 'ISM/SMS培训', value: '3' },
        { text: '保安培训', value: '4' },
      ],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/crewShipTrainItem/delete',
        { id: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/seaAffairs/crewShipTrainItem/update'
        : '/business/seaAffairs/crewShipTrainItem/save'
      const { errorRaw } = await this.postAsync(reqUrl, { ...this.formData })
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      this.closeForm()
      await this.$refs.table.loadTableData()
    },
    createItem() {
      this.formData = {}
      this.formShow = true
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.isEdit = true
    },
    closeForm() {
      this.formShow = false
      this.formData = {}
      this.isEdit = false
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
