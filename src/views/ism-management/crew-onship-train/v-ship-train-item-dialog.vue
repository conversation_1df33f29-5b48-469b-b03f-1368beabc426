<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新增模板内容
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :fuzzy-label="fuzzyLabel"
          req-url="/business/seaAffairs/crewShipTrainItem/page"
          :search-remain="searchRemain"
          item-key="id"
          :single-select="false"
          @dbclick="confirm"
        >
          <template #searchflieds>
            <slot name="searchflieds">
              <!-- 剩余参数插槽 -->
            </slot>
          </template>
          <template v-slot:[`item.type`]="{ item }">
            <span v-if="item.type === '1'">保安演习/演练</span>
            <span v-if="item.type === '2'">应急演习</span>
            <span v-if="item.type === '3'">ISM/SMS培训</span>
            <span v-if="item.type === '4'">保安培训</span>
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-ship-train-item-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目类型', value: 'type' },
      { text: '相关人员', value: 'stakeholder' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
  },
  data() {
    return {
      dialog: false,
      formData: {},
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      this.$emit('change', false)
      this.$emit('success', this.selected)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
