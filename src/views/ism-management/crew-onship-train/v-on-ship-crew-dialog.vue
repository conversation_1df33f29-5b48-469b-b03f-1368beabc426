<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新增参与船员
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :fuzzy-label="fuzzyLabel"
          :headers="headers"
          req-url="/business/crew/osmOnShipCrew/page"
          :search-remain="searchRemain"
          :single-select="false"
          @dbclick="confirm"
        >
          <template #searchflieds>
            <slot name="searchflieds">
              <!-- 剩余参数插槽 -->
            </slot>
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-on-ship-crew-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '岗位名称', value: 'post' },
      { text: '类型', value: 'crePropertyFeature' },
    ]
    this.fuzzyLabel = ''
  },
  data() {
    return {
      dialog: false,
      formData: {},
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      let selected1 = []
      this.selected.forEach((e) => {
        let data = {
          userId: e.creId,
          name: e.creName,
          post: e.post,
          crePropertyFeature: e.crePropertyFeature,
        }
        selected1.push(data)
      })
      this.$emit('change', false)
      this.$emit('success', selected1)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
