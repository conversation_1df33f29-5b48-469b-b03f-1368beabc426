<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['年度培训计划模板:编辑']"
      :title="titleName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="false"
      @save="save"
    >
      <template v-slot:模板信息>
        <v-form ref="form2">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  required
                  :rules="[rules.required]"
                  label="年度"
                  outlined
                  dense
                  v-model="detailInfo.year"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="模板名称"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                  v-model="detailInfo.name"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  required
                  label="类型"
                  outlined
                  dense
                  :items="typeDict"
                  :rules="[rules.required]"
                  v-model="detailInfo.type"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  v-model="detailInfo.company"
                  label="船管公司"
                  :items="shipCompanys"
                  outlined
                  required
                  :rules="[rules.required]"
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="7">
                <v-textarea
                  label="备注"
                  required
                  outlined
                  dense
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:模板内容>
        <v-data-table
          show-select
          :headers="headers"
          :items="detailInfo.details"
          dense
          item-key="id"
          v-model="selected"
          :items-per-page="5"
        >
          <template v-slot:[`item.type`]="{ item }">
            <span v-if="item.type === '1'">保安演习/演练</span>
            <span v-if="item.type === '2'">应急演习</span>
            <span v-if="item.type === '3'">ISM/SMS培训</span>
            <span v-if="item.type === '4'">保安培训</span>
          </template>
        </v-data-table>
      </template>
      <template v-slot:模板内容按钮 v-if="!newCard">
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :loading="sloading"
          small
          @click="openForm"
          v-permission="['年度培训计划模板:新增内容']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!--        <v-btn-->
        <!--          small-->
        <!--          :disabled="!selected.length"-->
        <!--          outlined-->
        <!--          tile-->
        <!--          color="warning"-->
        <!--          class="mx-1"-->
        <!--          @click="edit"-->
        <!--          v-permission="['年度培训计划模板:修改']"-->
        <!--        >-->
        <!--          <v-icon left>mdi-pencil</v-icon>-->
        <!--          修改-->
        <!--        </v-btn>-->
        <v-btn
          :disabled="!selected.length"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          :loading="loading1"
          @click="delAudit"
          v-permission="['年度培训计划模板:删除内容']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <v-ship-train-item-dialog
      v-model="dialog1"
      @change="change"
      @success="success"
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="项目名称"
            outlined
            dense
            clearable
            v-model="searchRemain.itemName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="4">
          <v-select
            label="项目类型"
            outlined
            dense
            :items="typeDict"
            clearable
            v-model="searchRemain.type"
          ></v-select>
        </v-col>
      </template>
    </v-ship-train-item-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import VShipTrainItemDialog from '@/views/ism-management/crew-onship-train/v-ship-train-item-dialog.vue'
export default {
  name: 'annual-train-plan-temp-detail',
  components: { VShipTrainItemDialog },
  mixins: [dictHelper],
  created() {
    this.backRouteName = 'annual-train-plan-temp'
    this.subtitles = ['模板信息', '模板内容']
    this.newCard = this.$route.params.id
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目类型', value: 'type' },
      { text: '相关人员', value: 'stakeholder' },
      // { text: '备注', value: 'remark' },
    ]
    this.initSelected = {}
  },
  data() {
    return {
      detailInfo: {},
      searchRemain: {},
      selected: [],
      dialog1: false,
      sloading: false,
      loading1: false,
      typeDict: [
        { text: '保安演习/演练', value: '1' },
        { text: '应急演习', value: '2' },
        { text: 'ISM/SMS培训', value: '3' },
        { text: '保安培训', value: '4' },
      ],
      shipCompanys: [
        { text: '山东船管', value: '0' },
        { text: '上海船管', value: '1' },
      ],
      titleName: '新增年度培训/演习计划模板',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  computed: {
    newCard: function () {
      return this.$route.params.id === `new` || this.detailInfo.status === '2'
    },
  },
  methods: {
    async save(goback) {
      if (!this.$refs.form2.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      if (this.detailInfo.status === '2') {
        this.$dialog.message.error('已发布，无法修改')
        return
      }
      const url =
        this.$route.params.id === `new`
          ? '/business/seaAffairs/annualTrainPlanTemp/save'
          : '/business/seaAffairs/annualTrainPlanTemp/update'
      const { errorRaw } = await this.postAsync(url, this.detailInfo)
      if (errorRaw) {
        return
      }
      this.detailInfo = {}
      this.$dialog.message.success('操作成功')
      goback()
    },
    async getDetailInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/seaAffairs/annualTrainPlanTemp/detailTempId/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        // console.log('detailInfo', this.detailInfo)
        this.titleName = this.detailInfo.name
      }
    },
    openForm() {
      this.sloading = true
      this.dialog1 = true
    },
    change(val) {
      this.dialog1 = val
      this.sloading = false
    },
    async success(val) {
      console.log(val)
      let matchDetail = {
        id: this.$route.params.id,
        details: val,
      }
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/trainPlanTempDetail/saveDetails`,
        matchDetail,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.sloading = false
      this.selected = []
      await this.getDetailInfo()
    },
    // edit() {
    //   return
    // },
    async delAudit() {
      this.loading1 = true
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) {
        this.loading1 = false
        return
      }
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除'))) {
        this.loading1 = false
        return
      }
      let idList = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/trainPlanTempDetail/deleteBatch`,
        idList,
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功！')
      await this.getDetailInfo()
      this.selected = []
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
