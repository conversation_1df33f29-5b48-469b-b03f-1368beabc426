<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['月度培训记录:编辑']"
      :title="titleName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      :can-save="canEdit()"
      @submit="submit"
      :can-submit="canEdit()"
    >
      <template v-if="detailInfo.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detailInfo.auditParams"
              :shipCode="detailInfo.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:培训项目>
        <v-form ref="form2">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="年度"
                  outlined
                  readonly
                  dense
                  v-model="detailInfo.year"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="月份"
                  outlined
                  readonly
                  dense
                  v-model="detailInfo.month"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="项目名称"
                  readonly
                  outlined
                  dense
                  v-model="detailInfo.itemName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="相关人员"
                  readonly
                  outlined
                  dense
                  v-model="detailInfo.stakeholder"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="类型"
                  outlined
                  readonly
                  dense
                  :items="typeDict"
                  v-model="detailInfo.type"
                ></v-select>
              </v-col>
              <!--              <v-col cols="12" md="6">-->
              <!--                <v-textarea-->
              <!--                  label="备注"-->
              <!--                  outlined-->
              <!--                  dense-->
              <!--                  readonly-->
              <!--                  v-model="detailInfo.remark"-->
              <!--                ></v-textarea>-->
              <!--              </v-col>-->
            </v-row>
            <v-row>
              <v-col cols="12" md="1">
                <v-btn
                  :disabled="detailInfo.status !== '3'"
                  outlined
                  tile
                  color="primary"
                  class="mx-1"
                  :loading="loading2"
                  @click="confirmRecord"
                  v-permission="['月度培训记录:确认']"
                >
                  <v-icon left>mdi-emoticon-outline</v-icon>
                  确认
                </v-btn>
              </v-col>
              <v-col cols="12" md="2">
                <v-btn
                  :disabled="detailInfo.status !== '3'"
                  outlined
                  tile
                  color="info"
                  class="mx-1"
                  :loading="loading3"
                  @click="drawBack"
                  v-permission="['月度培训记录:退回']"
                >
                  <v-icon left>mdi-arrow-u-right-bottom</v-icon>
                  退回
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:培训内容>
        <v-form ref="form2">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="培训人"
                  required
                  :rules="[rules.required]"
                  outlined
                  dense
                  v-model="detailInfo.trainerName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="地点"
                  required
                  :rules="[rules.required]"
                  outlined
                  dense
                  v-model="detailInfo.place"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="完成时间"
                  required
                  v-model="detailInfo.completedTime"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="培训内容"
                  required
                  :rules="[rules.required]"
                  outlined
                  dense
                  v-model="detailInfo.trainContent"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="培训效果评价"
                  outlined
                  required
                  :rules="[rules.required]"
                  dense
                  v-model="detailInfo.valuation"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="detailInfo.attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:参与船员>
        <v-data-table
          show-select
          :headers="headers"
          :items="crews"
          dense
          :single-select="false"
          item-key="id"
          v-model="selected"
          :items-per-page="5"
        >
          <template v-slot:[`item.type`]="{ item }">
            <span v-if="item.type === '1'">保安演习/演练</span>
            <span v-if="item.type === '2'">应急演习</span>
            <span v-if="item.type === '3'">ISM/SMS培训</span>
            <span v-if="item.type === '4'">保安培训</span>
          </template>
        </v-data-table>
      </template>
      <template v-slot:参与船员按钮 v-if="!newCard">
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          :loading="sloading"
          @click="openEdit"
          v-permission="['月度培训记录:新增船员']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected.length"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          :loading="loading1"
          @click="delAudit"
          v-permission="['月度培训记录:删除船员']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <v-on-ship-crew-dialog
      v-model="dialog1"
      @change="change"
      @success="success"
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="名称"
            outlined
            dense
            clearable
            v-model="searchRemain.name"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="4">
          <v-text-field
            label="职位"
            outlined
            dense
            clearable
            v-model="searchRemain.post"
          ></v-text-field>
        </v-col>
      </template>
    </v-on-ship-crew-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import VOnShipCrewDialog from '@/views/ism-management/crew-onship-train/v-on-ship-crew-dialog.vue'
import routerControl from '@/mixin/routerControl'
export default {
  name: 'month-train-ship-record-detail',
  components: { VOnShipCrewDialog },
  mixins: [dictHelper, routerControl],
  created() {
    this.backRouteName = 'month-train-ship-record'
    this.subtitles = ['培训项目', '培训内容', '参与船员']
    this.newCard = this.$route.params.id
    this.headers = [
      { text: '船员姓名', value: 'name' },
      { text: '岗位名称', value: 'post' },
      { text: '类型', value: 'crePropertyFeature' },
    ]
    this.initSelected = {}
    this.creHeaders = [
      { text: '船员姓名', value: 'creName' },
      { text: '岗位名称', value: 'post' },
      { text: '类型', value: 'crePropertyFeature' },
    ]
  },
  data() {
    return {
      detailInfo: {},
      searchRemain: {
        shipCode: this.$local.data.get('userInfo').shipCode,
      },
      backRouteName: 'month-train-ship-record',
      formData: {},
      selected: [],
      crews: [],
      dialog1: false,
      editDialog: false,
      sloading: false,
      loading1: false,
      loading2: false,
      loading3: false,
      typeDict: [
        { text: '保安演习/演练', value: '1' },
        { text: '应急演习', value: '2' },
        { text: 'ISM/SMS培训', value: '3' },
        { text: '保安培训', value: '4' },
      ],
      // statusDict: [
      //   { text: '进行中', value: '2' },
      //   { text: '已完成', value: '3' },
      // ],
      titleName: '船月度培训记录详情',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  computed: {
    newCard: {
      get() {
        return this.$route.params.id === `new` || !this.canEdit()
      },
      set() {},
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    openEdit() {
      // console.log('opendEdit!!')
      this.dialog1 = true
      // console.log('editDialog', this.editDialog)
    },
    closeForm() {
      this.dialog1 = false
    },
    select(val) {
      this.formData.post = val.post
      this.formData.name = val.creName
      // console.log('formData------------->')
      // console.log(this.formData)
    },
    async save(goBack) {
      if (!this.$refs.form2.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/mTrainShipR/update',
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = {}
      this.$dialog.message.success('操作成功')
      goBack()
      // console.log('1233112')
    },
    async getCrews() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/seaAffairs/mTrainShipRDetail/parent`,
          { id: this.$route.params.id },
        )
        if (errorRaw) {
          return
        }
        this.crews = data
      }
    },
    async getDetailInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/seaAffairs/mTrainShipR/detail/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        // console.log('detailInfo', this.detailInfo)
        this.titleName = this.detailInfo.name
      }
    },
    async saveDetail() {
      this.formData.parentId = this.$route.params.id
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/mTrainShipRDetail/save',
        this.formData,
      )
      if (errorRaw) {
        return
      }
      this.closeForm()
      this.formData = {}
      this.$dialog.message.success('操作成功')
      await this.getCrews()
    },
    change(val) {
      this.dialog1 = val
      this.sloading = false
    },
    async success(val) {
      console.log(val)
      let matchDetail = {
        id: this.$route.params.id,
        details: val,
      }
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/mTrainShipRDetail/saveBatch`,
        matchDetail,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.sloading = false
      this.selected = []
      await this.getCrews()
    },
    canEdit() {
      return this.detailInfo.status !== '4' && this.detailInfo.status !== '3'
    },
    async drawBack() {
      if (this.detailInfo.status !== '3') {
        this.$dialog.message.error(`仅已完成状态记录才可退回`)
        return
      }
      this.loading3 = true
      if (!(await this.$dialog.msgbox.confirm('是否确认退回？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/mTrainShipR/drawBack',
        { id: this.detailInfo.id },
      )
      this.loading3 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.detailInfo = {}
      this.closeAndTo(this.backRouteName)
    },
    async confirmRecord() {
      if (this.detailInfo.status !== '3') {
        this.$dialog.message.error(`仅已完成状态记录才可确认`)
        return
      }
      this.loading2 = true
      if (!(await this.$dialog.msgbox.confirm('请确认信息是否正确？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/seaAffairs/mTrainShipR/confirm',
        { id: this.detailInfo.id },
      )
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.detailInfo = {}
      this.closeAndTo(this.backRouteName)
    },
    async delAudit() {
      this.loading1 = true
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) {
        this.loading1 = false
        return
      }
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除'))) {
        this.loading1 = false
        return
      }
      // console.log('selected', this.selected)
      let idList = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/seaAffairs/mTrainShipRDetail/deleteBatch`,
        idList,
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功！')
      await this.getCrews()
      this.selected = []
    },
    async submit(goBack) {
      if (!this.$refs.form2.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/mTrainShipR/submit',
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.detailInfo = {}
      goBack()
      console.log('1233112')
    },
  },

  async mounted() {
    await this.getDetailInfo()
    await this.getCrews()
  },
}
</script>

<style></style>
