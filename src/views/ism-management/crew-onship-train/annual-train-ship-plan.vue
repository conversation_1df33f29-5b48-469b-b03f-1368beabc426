<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      :single-select="false"
      :push-params="pushParams"
      @dbclick="editItem"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select
            clearable
            v-model="searchRemain.shipCode"
          ></v-ship-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            label="年度"
            outlined
            clearable
            dense
            v-model="searchRemain.year"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="名称"
            outlined
            clearable
            dense
            v-model="searchRemain.name"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.type"
            label="项目类型"
            :items="typeDict"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.status"
            label="状态"
            :items="statusMap"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="exportPlan"
          v-permission="['年度培训计划:导出计划表']"
        >
          <v-icon left>mdi-equalizer</v-icon>
          导出计划表
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          :loading="loading1"
          tile
          color="info"
          class="mx-1"
          @click="submit"
          v-permission="['年度培训计划:提交审核']"
        >
          <v-icon left>mdi-arrow-up-circle-outline</v-icon>
          提交审核
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['年度培训计划:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!canSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="submitApply"
          v-permission="['年度培训计划:审批通过']"
        >
          <v-icon left>mdi-send</v-icon>
          审批通过
        </v-btn>
      </template>
      <template v-slot:[`item.type`]="{ item }">
        <span v-if="item.type === '1'">保安演习/演练</span>
        <span v-if="item.type === '2'">应急演习</span>
        <span v-if="item.type === '3'">ISM/SMS培训</span>
        <span v-if="item.type === '4'">保安培训</span>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === '1'" small dark color="gray">草稿</v-chip>
        <v-chip v-else-if="item.status === '2'" small dark color="info">
          待{{ item.businessStatus }}审批
        </v-chip>
        <v-chip v-else-if="item.status === '3'" small dark color="success">
          已审核
        </v-chip>
        <v-chip v-else-if="item.status === '4'" small dark color="error">
          已驳回
        </v-chip>
      </template>
      <template v-slot:[`item.liveFlag`]="{ item }">
        <v-chip v-if="item.liveFlag" small dark color="success">生效中</v-chip>
        <v-chip v-else small dark color="gray">未生效</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import moment from 'moment'
export default {
  name: 'annual-train-ship-plan',
  created() {
    this.tableName = '年度培训/演习计划'
    this.reqUrl = '/business/seaAffairs/annualShipPlan/page'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '年度', value: 'year' },
      { text: '名称', value: 'name' },
      { text: '类型', value: 'type' },
      { text: '状态', value: 'status' },
      { text: '生效状态', value: 'liveFlag' },
      { text: '创建时间', value: 'createTime' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'annual-train-ship-plan-detail',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {
        year: moment().year(),
      },
      attachmentDialog: false,
      formData: {},
      isEdit: false,
      loading: false,
      loading1: false,
      formShow: false,
      typeDict: [
        { text: '保安演习/演练', value: 1 },
        { text: '应急演习', value: 2 },
        { text: 'ISM/SMS培训', value: 3 },
        { text: '保安培训', value: 4 },
      ],
      statusMap: [
        { text: '草稿', value: 1 },
        { text: '审核中', value: 2 },
        { text: '已审核', value: 3 },
        { text: '已驳回', value: 4 },
      ],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },
  computed: {
    canSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) => item?.auditParams?.taskId && item.status == 2,
        )
      )
    },
  },
  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除所选记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/annualShipPlan/deleteBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async exportPlan() {
      // await this.getBlobDownload(
      //   '/business/seaAffairs/annualShipPlan/exportPlan',
      //   { planId: this.selected.id },
      // )
      if (this.selected.length > 1) {
        this.$dialog.message.error('一次只可选择一项记录')
        return
      }
      if (!(this.selected[0].status === '3')) {
        this.$dialog.message.error('仅审核完成才可导出')
        return
      }
      const baseUrl = '/api/business/seaAffairs/annualShipPlan/exportPlan'
      const downloadUrl = `${baseUrl}?planId=${encodeURIComponent(
        this.selected[0].id,
      )}`
      console.log('url', downloadUrl)
      window.location.href = downloadUrl
    },
    async submit() {
      if (this.selected.length > 1) {
        this.$dialog.message.error('一次只可选择一项记录')
        return
      }
      if (!(this.selected[0].status == '1' || this.selected[0].status == '4')) {
        this.$dialog.message.error('仅草稿状态和已驳回状态可提交审核')
        return
      }
      if (
        !(await this.$dialog.msgbox.confirm(
          '请确认是否填写完成，确定发起提交审批？\n\r<br>提交后将无法再填写修改',
        ))
      )
        return
      this.loading1 = true
      if (!this.selected[0].auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/seaAffairs/annualShipPlan/process/submit',
          { id: this.selected[0].id },
        )
        this.loading1 = false
        if (errorRaw) {
          return
        }
        this.$dialog.message.success('操作成功')
        await this.$refs.table.loadTableData()
        return
      } else {
        return
      }
    },
    async submitApply() {
      if (!(await this.$dialog.msgbox.confirm('确定审批通过所选记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/batchCompleteTaskAndCommentAndSetVar',
        this.selected.map((item) => ({
          adopt: true,
          comment: '',
          params: {},
          taskId: item.auditParams.taskId,
        })),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`操作成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
    createItem() {
      this.formData = {}
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected[0] }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },
  },

  mounted() {},
}
</script>

<style></style>
