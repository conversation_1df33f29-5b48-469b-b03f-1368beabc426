<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        实施月份修改
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-select
                  v-model="formData.months"
                  :items="monthList"
                  label="月份"
                  multiple
                  dense
                  outlined
                >
                  <template v-slot:prepend-item>
                    <v-list-item @click="toggle">
                      <v-list-item-title v-text="'所有'"></v-list-item-title>
                      <template v-slot:prepend>
                        <v-checkbox-btn
                          :color="
                            chooseSomelMonth ? 'indigo-darken-4' : undefined
                          "
                          :indeterminate="chooseSomelMonth && !chooseAllMonth"
                          :model-value="chooseAllMonth"
                        ></v-checkbox-btn>
                      </template>
                    </v-list-item>
                    <v-divider class="mt-2"></v-divider>
                  </template>
                </v-select>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'ship-plan-month-choose-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Array,
      default: () => [],
    },
  },
  created() {},
  data() {
    return {
      dialog: false,
      formData: {
        months: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      typeDict: [
        { text: '保安演习/演练', value: '1' },
        { text: '应急演习', value: '2' },
        { text: 'ISM/SMS培训', value: '3' },
        { text: '保安培训', value: '4' },
      ],
      monthList: [
        { text: '1', value: '1' },
        { text: '2', value: '2' },
        { text: '3', value: '3' },
        { text: '4', value: '4' },
        { text: '5', value: '5' },
        { text: '6', value: '6' },
        { text: '7', value: '7' },
        { text: '8', value: '8' },
        { text: '9', value: '9' },
        { text: '10', value: '10' },
        { text: '11', value: '11' },
        { text: '12', value: '12' },
      ],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      console.log(this.initialData)
      let tempData = JSON.parse(JSON.stringify(this.initialData))
      this.formData.idList = tempData.map((ele) => ele.id)
      // console.log('formData', this.formData)
    },
  },
  computed: {
    chooseAllMonth() {
      return this.formData.months.length === this.monthList.length
    },
    chooseSomelMonth() {
      return this.formData.months.length > 0
    },
  },
  methods: {
    toggle() {
      if (!this.formData.months) {
        this.formData.months = this.monthList.map((ele) => ele.value)
        return
      }
      if (this.chooseAllMonth) {
        this.formData.months = []
      } else {
        this.formData.months = this.monthList.map((ele) => ele.value)
      }
    },
    closeForm() {
      this.formData = {
        months: [],
      }
      console.log('closeForm1')
      this.$emit('change', false)
      console.log('closeForm2')
      this.$emit('success')
    },
    async save() {
      console.log('months', this.formData.months)
      const { errorRaw } = await this.postAsync(
        '/business/seaAffairs/annualShipPlanDetail/update',
        this.formData,
      )
      if (errorRaw) {
        return
      }
      this.closeForm()
      this.$dialog.message.success('操作成功')
      // this.formData = {}
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
