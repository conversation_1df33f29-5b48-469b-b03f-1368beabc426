<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['供应商准入:编辑']"
      :title="`供应商准入/退出/评级/慎用-${
        isEdit ? detail.applicationNo : '新增'
      }`"
      :tooltip="isEdit ? detail.applicationNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <v-btn
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: detail.systemReportId },
          }"
          color="info"
          small
          class="mx-1"
          v-permission="['备件申请:查看部门报表']"
        >
          查看部门报表
        </v-btn> -->
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <v-card-text>
        <v-expansion-panels multiple accordion v-model="panel" focusable>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              申请单类型
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form">
                  <v-row>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.applyType"
                        :items="[
                          { text: '准入', value: 1 },
                          { text: '退出', value: 2 },
                          { text: '评级', value: 3 },
                          { text: '慎用', value: 4 },
                        ]"
                        label="准入/退出/评级/慎用"
                        dense
                        :rules="[rules.required]"
                        readonly
                        disabled
                        required
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" class="py-0" v-if="detail.applyType != 1">
                      <v-textarea
                        v-model="detail.remark"
                        :label="detail.applyType == 2 ? '退出说明' : '评级说明'"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.requiredAlways]"
                        height="50px"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              准入推荐信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form">
                  <v-row>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="推荐人"
                        v-model="supplierInOutItem.recommendPerson"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <vs-date-picker
                        readonly
                        outlined
                        dense
                        v-model="supplierInOutItem.recommendDate"
                        use-today
                        label="推荐时间"
                        :rules="[rules.required]"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="准入小组名单"
                        v-model="supplierInOutItem.recommendGroup"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        disabled
                        required
                        outlined
                      ></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="detail.applyPurpose"
                  label="申请目的"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col> -->
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="supplierInOutItem.reason"
                        label="准入理由"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.required]"
                        height="50px"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="supplierInOutItem.remark"
                        label="采购主管备注"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.required]"
                        height="50px"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="supplierInOutItem.productIntro"
                        label="规模和产品介绍"
                        dense
                        outlined
                        :rules="[rules.required]"
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="supplierInOutItem.personnelSize"
                        label="人员规模情况介绍"
                        dense
                        outlined
                        :rules="[rules.required]"
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="supplierInOutItem.marketContent"
                        label="市场占有率介绍情况"
                        dense
                        outlined
                        :rules="[rules.required]"
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              上传营业执照附件文档
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-attach-list
                  :attachments="detail.attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="detail.shipCode"
                ></v-attach-list>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              上传承诺函、合同等相关附件文档
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-attach-list
                  :attachments="detail.attachmentRecords2"
                  @change="changeAttachment2"
                  :ship-code="detail.shipCode"
                ></v-attach-list>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              基础信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form2">
                  <v-row>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="supplierInOutItem.supplierType"
                        multiple
                        :items="[
                          { text: '船员', value: '船员' },
                          { text: '备件', value: '备件' },
                          { text: '物料', value: '物料' },
                          { text: '滑油', value: '滑油' },
                          { text: '化学品', value: '化学品' },
                          { text: '缆绳', value: '缆绳' },
                          { text: '绑扎件', value: '绑扎件' },
                          { text: '油漆', value: '油漆' },
                          { text: '锚、锚链', value: '锚、锚链' },
                          { text: '消防救生检验', value: '消防救生检验' },
                          { text: '海图', value: '海图' },
                          { text: '通导', value: '通导' },
                          { text: '坞修', value: '坞修' },
                          { text: '航修', value: '航修' },
                          { text: '固定资产', value: '固定资产' },
                          { text: '年度协议', value: '年度协议' },
                          { text: '大宗采购', value: '大宗采购' },
                          { text: '大宗采购', value: '大宗采购' },
                        ]"
                        label="供应商类型"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="供应商名称"
                        v-model="supplierInOutItem.supplierNameCn"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="供应商英文名称"
                        v-model="supplierInOutItem.supplierNameEn"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="注册国家/地区"
                        v-model="supplierInOutItem.country"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="经营所在城市"
                        v-model="supplierInOutItem.city"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="联系人"
                        v-model="supplierInOutItem.contacts"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="手机"
                        v-model="supplierInOutItem.phoneNumber"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-combobox
                        v-model="supplierInOutItem.emailList"
                        label="邮箱"
                        multiple
                        outlined
                        small-chips
                        dense
                        hint="输入邮箱后回车即保存"
                        :rules="[rules.required]"
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-combobox>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="supplierInOutItem.signContract"
                        :items="[
                          { text: '是', value: 1 },
                          { text: '否', value: 0 },
                        ]"
                        label="是否签订合同"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        :disabled="detail.applyType != 1"
                        :readonly="isEdit"
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              规模及产品信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-row>
                  <v-col cols="12" md="3" class="py-0">
                    <v-text-field
                      label="供应商生产部门人数"
                      v-model="supplierInOutItem.prodeptNum"
                      dense
                      :rules="[rules.required]"
                      required
                      type="number"
                      outlined
                      :readonly="isEdit"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3" class="py-0">
                    <v-text-field
                      label="供应商销售部门人数"
                      v-model="supplierInOutItem.saledeptNum"
                      dense
                      :rules="[rules.required]"
                      required
                      type="number"
                      outlined
                      :readonly="isEdit"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="3" class="py-0">
                    <v-text-field
                      label="去年销售额"
                      v-model="supplierInOutItem.lastYearSales"
                      dense
                      :rules="[rules.required]"
                      type="number"
                      required
                      outlined
                      :readonly="isEdit"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" class="py-0">
                    <v-textarea
                      label="产品介绍"
                      v-model="supplierInOutItem.productIntroduction"
                      dense
                      :rules="[rules.required]"
                      required
                      outlined
                      height="50px"
                      :readonly="isEdit"
                    ></v-textarea>
                  </v-col>
                  <v-col cols="12" class="py-0">
                    <v-textarea
                      label="产品优势"
                      v-model="supplierInOutItem.productAdvantages"
                      dense
                      :rules="[rules.required]"
                      required
                      outlined
                      height="50px"
                      :readonly="isEdit"
                    ></v-textarea>
                  </v-col>
                  <v-col cols="12" class="py-0">
                    <v-textarea
                      label="供应商主要客户（除海丰外）"
                      v-model="supplierInOutItem.clients"
                      dense
                      :rules="[rules.required]"
                      required
                      outlined
                      height="50px"
                      :readonly="isEdit"
                    ></v-textarea>
                  </v-col>
                  <v-col cols="12" class="py-0">
                    <v-textarea
                      label="供应商市场占有率"
                      v-model="supplierInOutItem.marketShare"
                      dense
                      :rules="[rules.required]"
                      required
                      outlined
                      height="50px"
                      :readonly="isEdit"
                    ></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              结算信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-card-title v-if="detail.applyType == 1">
                  <div></div>
                  <v-spacer></v-spacer>
                  <template>
                    <v-btn
                      v-if="!isEdit"
                      outlined
                      small
                      tile
                      color="success"
                      class="mx-1"
                      :disabled="!payCom"
                      @click="addSupplierBank"
                      v-permission="['供应商准入:新增银行信息']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增银行信息
                    </v-btn>
                    <!-- <v-btn
                      outlined
                      small
                      tile
                      color="success"
                      :disabled="!bank"
                      class="mx-1"
                      @click="editSupplierBank"
                      v-permission="['供应商准入:编辑银行信息']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      编辑银行信息
                    </v-btn> -->
                    <v-btn
                      v-if="!isEdit"
                      outlined
                      small
                      tile
                      color="success"
                      class="mx-1"
                      @click="addPayCom"
                      v-permission="['供应商准入:新增结算信息']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增
                    </v-btn>
                    <v-btn
                      v-if="!isEdit"
                      :disabled="!payCom"
                      small
                      outlined
                      tile
                      color="warning"
                      class="mx-1"
                      @click="editSupplierP"
                      v-permission="['供应商准入:修改结算信息']"
                    >
                      <v-icon left>mdi-pencil</v-icon>
                      修改
                    </v-btn>
                    <v-btn
                      v-if="!isEdit"
                      :disabled="!payCom"
                      small
                      outlined
                      tile
                      color="error"
                      class="mx-1"
                      @click="delSupplierP"
                      v-permission="['供应商准入:删除结算信息']"
                    >
                      <v-icon left>mdi-delete-empty</v-icon>
                      删除
                    </v-btn>
                  </template>
                </v-card-title>
                <template>
                  <v-data-table
                    :headers="payComHeaders"
                    :items="supplierPurchaserList"
                    v-model="payComs"
                    single-select
                    disable-pagination
                    hide-default-footer
                    show-select
                    dense
                    class="use-divider"
                    show-expand
                    :expanded.sync="expanded"
                  >
                    <template v-slot:[`item.status`]="{ item }">
                      {{ ['有效', '暂停整顿', '冻结', '黑名单'][item.status] }}
                    </template>
                    <template v-slot:expanded-item="{ headers, item }">
                      <td :colspan="headers.length">
                        <v-list class="my-2" dense>
                          <v-list-item-group v-model="bank" color="primary">
                            <v-list-item-subtitle
                              class="d-flex justify-space-between"
                              v-for="b in item.supplierBankListOutputDTOS"
                              :key="b.id"
                              :value="b"
                            >
                              <div>银行名称:{{ b.bank }}</div>
                              <div>银行账户:{{ b.account }}</div>
                              <div>
                                <!-- 币别:{{ currencyInfo[b.currencyType].ccyCode }} -->
                                币别:{{
                                  currencyInfo.find(
                                    (i) => i.id === b.currencyType,
                                  ).ccyCode
                                }}
                              </div>
                              <!-- {{ b.ccyCode }} -->
                              <!-- <v-row>
                                <v-col cols="12" md="4">
                                  开户账号
                                  <v-text-field
                                    v-model="b.bank"
                                    outlined
                                    disabled
                                    readonly
                                    dense
                                  ></v-text-field>
                                </v-col>
                                <v-col cols="12" md="4">
                                  开户行
                                  <v-text-field
                                    v-model="b.account"
                                    outlined
                                    disabled
                                    readonly
                                    dense
                                  ></v-text-field>
                                </v-col>
                                <v-col cols="12" md="4">
                                  币种
                                  <v-select
                                    v-model="b.currencyType"
                                    outlined
                                    required
                                    disabled
                                    readonly
                                    dense
                                    item-text="ccyName"
                                    item-value="id"
                                    :items="currencyInfo"
                                  ></v-select>
                                </v-col>
                              </v-row> -->
                            </v-list-item-subtitle>
                          </v-list-item-group>
                        </v-list>
                      </td>
                    </template>
                  </v-data-table>
                </template>
                <!-- <v-table-list
                  ref="table"
                  v-model="selected"
                  :headers="headers"
                  :items="detailInfo.familyMember"
                  item-key="name"
                ></v-table-list> -->
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel
            v-if="detail.applyType != 2 && detail.applyType != 4"
          >
            <v-expansion-panel-header style="color: #3399cc">
              评级打分
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-simple-table class="use-divider" ref="table">
                  <template v-slot:default>
                    <thead>
                      <tr>
                        <th colspan="1" class="text-center">20分</th>
                        <th colspan="1" class="text-center">20分</th>
                        <th colspan="1" class="text-center">20分</th>
                        <th colspan="1" class="text-center">20分</th>
                        <th colspan="1" class="text-center">20分</th>
                        <th colspan="1" class="text-center"></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <th colspan="1" class="text-center">
                          供应商生产部门人数/供应商销售部门人数
                        </th>
                        <!-- <th colspan="1" class="text-center">
                          供应商销售部门人数
                        </th> -->
                        <th colspan="1" class="text-center">去年销售额</th>
                        <th colspan="1" class="text-center">
                          产品介绍/产品优势
                        </th>
                        <!-- <th colspan="1" class="text-center">产品优势</th> -->
                        <th colspan="1" class="text-center">
                          供应商主要客户（除海丰外）
                        </th>
                        <th colspan="1" class="text-center">
                          供应商市场占有率
                        </th>
                        <th colspan="1" class="text-center">总计</th>
                      </tr>
                      <tr
                        v-for="item in detail.supplierInOutScoreModifyDTOList"
                        :key="item.id"
                      >
                        <td>
                          <!-- 供应商生产部门人数 -->
                          <v-text-field
                            v-if="item.addFlag"
                            class="shrink"
                            v-model="item.prodeptNum"
                            @change="
                              () =>
                                (item.prodeptNum = Math.round(item.prodeptNum))
                            "
                            type="number"
                            :max="20"
                            dense
                            :rules="[rules.required, rules.notAboveTwenty]"
                            single-line
                          ></v-text-field>
                          <v-text-field
                            v-if="!item.addFlag"
                            class="shrink"
                            v-model="item.prodeptNum"
                            @change="
                              () =>
                                (item.prodeptNum = Math.round(item.prodeptNum))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            readonly
                            disabled
                            dense
                            single-line
                          ></v-text-field>
                        </td>
                        <!-- 供应商销售部门人数 -->
                        <!-- <td>
                          <v-text-field
                            v-if="item.addFlag"
                            class="shrink"
                            v-model="item.saledeptNum"
                            @change="
                              () =>
                                (item.saledeptNum = Math.round(
                                  item.saledeptNum,
                                ))
                            "
                            type="number"
                            dense
                            single-line
                          ></v-text-field>
                          <v-text-field
                            v-if="!item.addFlag"
                            class="shrink"
                            v-model="item.saledeptNum"
                            @change="
                              () =>
                                (item.saledeptNum = Math.round(
                                  item.saledeptNum,
                                ))
                            "
                            type="number"
                            readonly
                            disabled
                            dense
                            single-line
                          ></v-text-field>
                        </td> -->
                        <td>
                          <!--  去年销售额-->
                          <v-text-field
                            v-if="item.addFlag"
                            class="shrink"
                            v-model="item.lastYearSales"
                            @change="
                              () =>
                                (item.lastYearSales = Math.round(
                                  item.lastYearSales,
                                ))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            dense
                            single-line
                          ></v-text-field>
                          <v-text-field
                            v-if="!item.addFlag"
                            class="shrink"
                            v-model="item.lastYearSales"
                            @change="
                              () =>
                                (item.lastYearSales = Math.round(
                                  item.lastYearSales,
                                ))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            readonly
                            disabled
                            dense
                            single-line
                          ></v-text-field>
                        </td>
                        <td>
                          <!-- 产品介绍 -->
                          <v-text-field
                            v-if="item.addFlag"
                            class="shrink"
                            v-model="item.productIntroduction"
                            @change="
                              () =>
                                (item.productIntroduction = Math.round(
                                  item.productIntroduction,
                                ))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            dense
                            single-line
                          ></v-text-field>
                          <v-text-field
                            v-if="!item.addFlag"
                            class="shrink"
                            v-model="item.productIntroduction"
                            @change="
                              () =>
                                (item.productIntroduction = Math.round(
                                  item.productIntroduction,
                                ))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            readonly
                            disabled
                            dense
                            single-line
                          ></v-text-field>
                        </td>
                        <!-- 产品优势 -->
                        <!-- <td>
                          <v-text-field
                            v-if="item.addFlag"
                            class="shrink"
                            v-model="item.productAdvantages"
                            @change="
                              () =>
                                (item.productAdvantages = Math.round(
                                  item.productAdvantages,
                                ))
                            "
                            type="number"
                            dense
                            single-line
                          ></v-text-field>
                          <v-text-field
                            v-if="!item.addFlag"
                            class="shrink"
                            v-model="item.productAdvantages"
                            @change="
                              () =>
                                (item.productAdvantages = Math.round(
                                  item.productAdvantages,
                                ))
                            "
                            type="number"
                            readonly
                            disabled
                            dense
                            single-line
                          ></v-text-field>
                        </td> -->
                        <td>
                          <!--供应商主要客户（除海丰外）  -->
                          <v-text-field
                            v-if="item.addFlag"
                            class="shrink"
                            v-model="item.clients"
                            @change="
                              () => (item.clients = Math.round(item.clients))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            dense
                            single-line
                          ></v-text-field>
                          <v-text-field
                            v-if="!item.addFlag"
                            class="shrink"
                            v-model="item.clients"
                            @change="
                              () => (item.clients = Math.round(item.clients))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            readonly
                            disabled
                            dense
                            single-line
                          ></v-text-field>
                        </td>
                        <td>
                          <!-- 供应商市场占有率 -->
                          <v-text-field
                            v-if="item.addFlag"
                            class="shrink"
                            v-model="item.marketShare"
                            @change="
                              () =>
                                (item.marketShare = Math.round(
                                  item.marketShare,
                                ))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            dense
                            single-line
                          ></v-text-field>
                          <v-text-field
                            v-if="!item.addFlag"
                            class="shrink"
                            v-model="item.marketShare"
                            @change="
                              () =>
                                (item.marketShare = Math.round(
                                  item.marketShare,
                                ))
                            "
                            type="number"
                            :rules="[rules.required, rules.notAboveTwenty]"
                            readonly
                            disabled
                            dense
                            single-line
                          ></v-text-field>
                        </td>
                        <td>
                          {{
                            item.prodeptNum +
                            item.saledeptNum +
                            item.lastYearSales +
                            item.productIntroduction +
                            item.productAdvantages +
                            item.clients +
                            item.marketShare
                          }}
                        </td>
                      </tr>
                    </tbody>
                  </template>
                </v-simple-table>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-card-text>
    </v-detail-view>
    <bank-dialog
      :initialData="form1"
      v-model="formShow1"
      @success="addBank"
    ></bank-dialog>
    <pay-com-dialog
      :initialData="form2"
      v-model="formShow2"
      @success="addCom"
    ></pay-com-dialog>
  </v-container>
</template>
<script>
import bankDialog from './private/bank-dialog.vue'
import PayComDialog from './private/pay-com-dialog.vue'
import currencyHelper from '@/mixin/currencyHelper'
export default {
  components: { bankDialog, PayComDialog },
  name: 'supplier-in-detail',
  mixins: [currencyHelper],
  created() {
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.backRouteName = 'supplier-in-list'
    this.subtitles = [
      //   '准入推荐信息',
      //   '基础信息',
      //   '规模及产品信息',
      //   '结算信息',
      //   '评级打分',
    ]
    this.payComHeaders = [
      { text: '', value: 'data-table-expand' },
      { text: '付款公司', value: 'paymentCompany' },
      { text: '有效期-开始', value: 'beginDate' },
      { text: '赊销期（天）', value: 'creditDate' },
      { text: '有效期-结束', value: 'endDate' },
      { text: '联系人姓名', value: 'managerName' },
      { text: '联系人电话', value: 'managerPhone' },
      { text: '评级打分', value: 'score' },
      { text: '状态', value: 'status' },
      { text: '供应商类型', value: 'supplierType' },
    ]
  },
  computed: {
    isEdit() {
      return (
        this.$route.params.id !== 'new' &&
        this.detail.status !== '1' &&
        this.detail.status !== '4'
      )
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
    payCom() {
      return this.payComs.length > 0 ? this.payComs[0] : false
    },
  },
  data() {
    return {
      detail: {
        applicationNo: '',
        attachmentIds: [],
      },
      select: false,
      dialog: false,
      searchObj: {},
      rules: {
        required: (v) => {
          if (this.detail.applyType == 1 || this.detail.applyType == 3) {
            return !!v || v === false || v === 0 || '必填项不能为空'
          }
          return true
        },
        requiredAlways: (v) =>
          !!v || v === false || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        notAboveTwenty: (v) => parseInt(v) <= 20 || '数字不能超过20',
      },
      panel: [0, 1, 2, 3, 4, 5, 6, 7], //根据需要调整panel数组中的值，以控制哪些面板是展开的
      form1: {},
      formShow1: false,
      form2: {},
      formShow2: false,
      supplierPurchaserList: [],
      payComs: [],
      bank: false,
      supplierInOutScoreModifyDTOList: [],
      supplierInOutItem: { recommendGroup: '程军剑、李瑞民、张海玉、纪文光' },
      saveStatus: true,
    }
  },
  watch: {},

  methods: {
    // async save(goBack) {
    //   goBack()
    // },
    // async submit(goBack) {
    //   goBack()
    // },

    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    changeAttachment2(attachmentIds) {
      this.detail.attachmentIds2 = attachmentIds
    },
    editSupplierP() {
      this.form2 = { ...this.payCom }
      this.formShow2 = true
    },
    addPayCom() {
      this.form2 = { supplierId: this.$route.params.id }
      this.formShow2 = true
    },
    addCom(newCom) {
      console.log(newCom)
      if (
        this.supplierPurchaserList.some(
          (s) => s.paymentCompany === newCom.paymentCompany,
        )
      ) {
        this.$dialog.message.error('付款公司重复')
        return
      }
      this.supplierPurchaserList.push(newCom)
      console.log(this.supplierPurchaserList)
    },
    addBank(newBank) {
      console.log(newBank)
      // if (
      //   this.supplierPurchaserList.some(
      //     (s) => s.paymentCompany === newBank.paymentCompany,
      //   )
      // ) {
      //   this.supplierPurchaserList.supplierBankListOutputDTOS.push(newBank)
      // }
      // 假设this.supplierPurchaserList是已经定义好的数组
      this.supplierPurchaserList.forEach((s) => {
        if (s.paymentCompany === newBank.paymentCompany) {
          // 检查对象a是否包含supplierBankListOutputDTOS数组
          if (!s.supplierBankListOutputDTOS) {
            // 如果不包含，赋值一个空数组
            s.supplierBankListOutputDTOS = []
          }
          s.supplierBankListOutputDTOS.push(newBank)
        }
      })
      console.log('1213ad', this.supplierPurchaserList)
    },
    // delSupplierP() {
    //   this.supplierPurchaserList = this.supplierPurchaserList.filter(
    //     (s) => !(s.id === this.payCom.id),
    //   )
    // },
    editSupplierBank() {
      this.form1 = { ...this.bank }
      this.formShow1 = true
    },
    addSupplierBank() {
      this.form1 = {
        supplierId: this.$route.params.id,
        purchaserId: this.payCom.id,
        paymentCompany: this.payCom.paymentCompany,
      }
      this.formShow1 = true
    },
    getCompWithOperation() {
      const ids = this.supplierPurchaserList.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.supplierPurchaserList
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.supplierPurchaserList.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      others.forEach((s) => {
        if ('supplierBankListOutputDTOS' in s) {
          // s包含supplierBankListOutputDTOS属性
          s.supplierBankListOutputDTOS = s.supplierBankListOutputDTOS.map(
            (i) => {
              return { ...i, operationType: i.id ? 2 : 1 }
            },
          )
        } else {
          // s不包含supplierBankListOutputDTOS属性
          this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
        }
        // s.supplierBankListOutputDTOS = s.supplierBankListOutputDTOS.map((i) => {
        //   return { ...i, operationType: i.id ? 2 : 1 }
        // })
      })
      return [...delList, ...others]
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return false
      }
      if (this.supplierPurchaserList.length === 0) {
        this.$dialog.message.warning('请填写结算信息')
        return false
      }
      const detailList = this.getCompWithOperation()
      this.saveStatus = true
      detailList.forEach((s) => {
        if ('supplierBankListOutputDTOS' in s) {
          // s包含supplierBankListOutputDTOS属性
          if (s.supplierBankListOutputDTOS.length <= 0) {
            this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
            this.saveStatus = false
          }
        } else {
          // s不包含supplierBankListOutputDTOS属性
          this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
          this.saveStatus = false
        }
      })
      if (!this.saveStatus) {
        return
      }
      // const supplierInOutScoreModifyDTOList =
      //   this.supplierInOutScoreModifyDTOList
      this.supplierInOutScoreModifyDTOList.forEach((item) => {
        item.total =
          item.prodeptNum +
          item.saledeptNum +
          item.lastYearSales +
          item.productIntroduction +
          item.productAdvantages +
          item.clients +
          item.marketShare
      })
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/supplierInOut/supplierInOutSaveOrUpdate',
        {
          ...this.detail,
          supplierInOutItem: this.supplierInOutItem,
          supplierPurchaserList: [...detailList],
          supplierInOutScoreModifyDTOList: [
            ...this.supplierInOutScoreModifyDTOList,
          ],
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/supplierInOut/supplierInOutSubmit',
            { applyId: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/supplierInOut/supplierInOutDetailById',
        { applyId: this.$route.params.id },
      )
      this.detail = { ...data }
      this.supplierInOutItem = data.supplierInOutItem
      this.supplierInOutScoreModifyDTOList = [
        ...data.supplierInOutScoreModifyDTOList,
      ]
      this.supplierPurchaserList = data.supplierPurchaserList.map((s) => {
        return { ...s, vid: s.id, operationType: 0 }
      })
      this.supplierPurchaserList.forEach((s) => {
        s.supplierBankListOutputDTOS.forEach((a) => {
          a.operationType = 0
        })
      })
      if (
        this.detail.applyType != 2 &&
        this.detail.status == 2 &&
        this.detail.applyType != 4
      ) {
        this.panel = [0, 5, 7]
        this.$dialog.message.error('请在评级打分模块填写评分后再提交！')
      }
    },
    async loadDetailByType() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/supplierInOut/supplierInOutDetailByApplyType',
        {
          applyType: this.$route.params.applyType,
          supplierId: this.$route.params.supplierId,
        },
      )
      this.detail = { ...data }
      this.supplierInOutItem = data.supplierInOutItem
      this.supplierInOutScoreModifyDTOList = [
        ...data.supplierInOutScoreModifyDTOList,
      ]
      this.supplierPurchaserList = data.supplierPurchaserList.map((s) => {
        return { ...s, vid: s.id, operationType: 0 }
      })
      this.supplierPurchaserList.forEach((s) => {
        s.supplierBankListOutputDTOS.forEach((a) => {
          a.operationType = 0
        })
      })
      this.supplierInOutItem.recommendGroup = '程军剑、李瑞民、张海玉、纪文光'
    },
  },

  mounted() {
    if (this.$route.params.applyType != undefined) {
      // console.log(1)
      this.detail.applyType = this.$route.params.applyType
      // 退出
      if (this.detail.applyType != 1) {
        this.loadDetailByType()
      }
    }
    if (this.detail.applyType == 2) {
      this.panel = [0, 1, 2, 3, 4, 5, 6]
    }
    this.loadDetail()
  },
}
</script>

<style></style>
