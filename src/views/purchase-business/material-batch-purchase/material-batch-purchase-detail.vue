<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['物料专项集采:编辑']"
      :title="`物料专项集采-${isEdit ? detail.orderNo : '新增'}`"
      :tooltip="isEdit ? detail.orderNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      :can-save="!isEdit"
    >
      <template v-slot:custombtns>
        <v-btn
          :loading="loading"
          v-if="!isEdit"
          tile
          color="error"
          small
          class="mx-1"
          @click="submit"
          v-permission="['物料专项集采:提交确认(采购)']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          提交至各主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="
            detail.businessStatus == '待采购主管确认' ||
            detail.businessStatus == '超预算待确认'
          "
          tile
          color="error"
          small
          class="mx-1"
          @click="genOrder"
          v-permission="['物料专项集采:生成各船订单']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成各船订单
        </v-btn>
      </template>

      <template v-slot:基本信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.applyDate"
                  label="申请日期"
                  use-today
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  readonly
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <v-handler-ship-code
                  :disabled="isEdit"
                  label="申请人"
                  :use-current="!isEdit"
                  v-model="detail.applicantId"
                  :rules="[rules.required]"
                  :init-user="initHandler"
                  :shipCode="detail.shipCode"
                  @selectUser="(user) => (detail.applicantName = user.nickName)"
                ></v-handler-ship-code>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-select
                  :disabled="isEdit"
                  v-model="detail.type"
                  :items="[
                    { text: '机务', value: '机务' },
                    { text: '通导', value: '通导' },
                  ]"
                  label="集采类型"
                  dense
                  readonly
                  required
                  outlined
                ></v-select>
              </v-col> -->
              <!-- :readonly="detail.applyType" -->
              <v-col cols="12" md="3">
                <v-select
                  :disabled="isEdit"
                  v-model="detail.applyType"
                  :items="applyTypeItems"
                  label="申请类型"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-station-jw
                  v-model="detail.applicantPost"
                  :disabled="isEdit"
                  :rules="[rules.required]"
                ></v-ship-station-jw>
              </v-col>
              <v-col md="3" cols="12">
                <!--                   :search-dicts="searchDicts" -->
                <v-dialog-select
                  :disabled="!detail.applyType"
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="detail.costSubjectId"
                  :init-selected="initSubject"
                  :search-remain="searchObj"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  :readonly="isEdit"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="申请单号"
                  dense
                  v-model="detail.orderNo"
                  outlined
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.supplyItem"
                  label="供应项目"
                  dense
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.proposedDate"
                  label="供货日期"
                  dense
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  required
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :readonly="detail.paymentCompany"
                  :items="付款公司选项"
                  label="付款公司"
                  v-model="detail.paymentCompany"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-supply-select2
                  :readonly="isEdit"
                  v-model="detail.supplierId"
                  :payment-company="detail.paymentCompany"
                  @select="
                    (item) => {
                      currency = item.currency
                    }
                  "
                  :init-selected="initSupply"
                  :rules="[rules.required]"
                ></v-supply-select2>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-if="isEdit"
                  outlined
                  dense
                  v-model="detail.ccyCode"
                  :rules="[rules.required]"
                  readonly
                  label="币种"
                ></v-text-field>
                <v-select
                  v-else
                  v-model="detail.currencyObj"
                  :items="currency"
                  item-text="ccyCode"
                  return-object
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  label="币种"
                  outlined
                  dense
                  :disabled="currency.length === 0"
                  @change="
                    (item) => {
                      currencyId = item.currencyType
                      detail.bankDesc = item.bank + item.account
                    }
                  "
                ></v-select>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  readonly
                  label="供应商"
                  dense
                  v-model="supplierName"
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.currencyId"
                  :items="currencys"
                  item-text="ccyCode"
                  item-value="currencyType"
                  label="币种"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-select>
              </v-col> -->
              <!-- :rules="[rules.required]" -->
              <v-col cols="12" md="3">
                <v-text-field
                  v-if="isJPY"
                  disabled
                  @change="() => (detail.money = Math.round(detail.money))"
                  v-model="detail.money"
                  label="采购总金额"
                  type="number"
                  dense
                  :rules="[isJPY ? rules.numberRule2 : rules.numberRule1]"
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="detail.money"
                  label="采购总金额"
                  type="number"
                  dense
                  disabled
                  :rules="[isJPY ? rules.numberRule2 : rules.numberRule1]"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.moneyUsd"
                  label="采购总金额折算美金"
                  dense
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3" v-if="isEdit">
                <v-text-field
                  v-if="isJPY"
                  disabled
                  @change="() => (detail.money = Math.round(detail.money))"
                  v-model="detail.moneyConfirm"
                  label="确认总金额"
                  type="number"
                  dense
                  :rules="[isJPY ? rules.numberRule2 : rules.numberRule1]"
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="detail.moneyConfirm"
                  label="确认总金额"
                  type="number"
                  dense
                  disabled
                  :rules="[isJPY ? rules.numberRule2 : rules.numberRule1]"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="isEdit">
                <v-text-field
                  v-model="detail.moneyConfirmUsd"
                  label="确认总金额折算美金"
                  dense
                  readonly
                  outlined
                ></v-text-field>
              </v-col> -->
              <v-col cols="12">
                <v-textarea
                  v-model="detail.applyRemark"
                  :rules="[rules.required]"
                  label="备注"
                  outlined
                  dense
                  :readonly="isEdit"
                ></v-textarea>
              </v-col>
              <!-- <v-col class="py-0" cols="12" v-if="detail.paymentCompany">
                <v-ship-select-pay
                  v-model="detail.shipCodes"
                  :payCompony="detail.paymentCompany"
                  multiple
                  ref="shipSelectPay"
                ></v-ship-select-pay>
              </v-col> -->
              <!-- <v-col class="py-0" cols="12">
                <v-dict-select
                  v-model="detail.standardType"
                  label="备件标准化分类"
                  clearable
                  dense
                  outlined
                  dict-type="component_standard_ype"
                ></v-dict-select>
              </v-col> -->
              <v-col cols="12">
                <v-attach-list
                  title="附件"
                  :attachments="detail.attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
            </v-row>
            <v-row v-if="false">
              <v-col class="py-0" cols="12">
                <v-ship-select
                  v-model="detail.shipCodes"
                  multiple
                ></v-ship-select>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #物料明细按钮>
        <!-- <template
        v-if="!detail.status || detail.status == '1' || detail.status == '4'"
        #待选供应商按钮
      > -->
        <!-- <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click="confirm"
          v-permission="['备件集采:生成明细']"
        >
          <v-icon left>mdi-check</v-icon>
          生成明细
        </v-btn> -->
        <v-col cols="12" md="2">
          <v-file-input
            v-if="!isEdit"
            small
            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
            label="导入EXCEL"
            v-model="file"
            v-permission="['物料专项集采:导入明细']"
          ></v-file-input>
        </v-col>
        <v-btn
          v-if="!isEdit"
          small
          tile
          color="error"
          class="mx-1"
          @click="dowExcel"
          v-permission="['物料专项集采:下载明细']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          下载明细
        </v-btn>
        <v-btn
          :disabled="
            !detail.costSubjectId || !detail.currencyId || !detail.proposedDate
          "
          v-if="!isEdit"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['物料专项集采明细:选择物料']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择物料
        </v-btn>
        <v-btn
          :disabled="!select"
          v-if="!isEdit"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCom"
          v-permission="['物料专项集采明细:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          v-if="detail.businessStatus == '待各主管确认'"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="confirmDetails"
          v-permission="['物料专项集采明细:一键确认']"
        >
          <v-icon left>mdi-content-save-alert</v-icon>
          一键确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == '待各主管确认'"
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click="saveConfirmDetails"
          v-permission="['物料专项集采明细:提交确认情况']"
        >
          <v-icon left>mdi-content-save-check-outline</v-icon>
          提交确认情况
        </v-btn>
      </template>
      <template #物料明细>
        <v-card-text>
          <v-table-list
            :headers="!isEdit ? pendingHeaders : pendingHeadersConfirm"
            :items="components"
            v-model="select"
            item-key="componentId"
          >
            <template v-slot:[`item.shipInfo`]="{ item }">
              {{ item.shipInfo.chShipName }}
            </template>
            <template v-slot:[`item.confirmNumber`]="{ item }">
              <v-text-field
                v-model="item.confirmNumber"
                label="确认数量"
                type="number"
                single-line
                dense
                :disabled="item.OldConfirm"
                :rules="[rules.required, rules.int, rules.aboveZero]"
              ></v-text-field>
            </template>
            <template v-slot:[`item.applyNumber`]="{ item }">
              <v-text-field
                v-model="item.applyNumber"
                label="申请数量"
                type="number"
                single-line
                dense
                :disabled="isEdit"
                :rules="[rules.required, rules.int, rules.aboveZero]"
              ></v-text-field>
            </template>
            <template v-slot:[`item.price`]="{ item }">
              <v-text-field
                v-if="isJPY"
                :disabled="isEdit"
                @change="() => (item.price = Math.round(item.price))"
                v-model="item.price"
                label="单价"
                type="number"
                single-line
                :rules="[rules.int]"
                :readonly="item.haveMainPrice"
                dense
              ></v-text-field>
              <v-text-field
                v-else-if="!isJPY"
                :disabled="isEdit"
                v-model="item.price"
                label="单价"
                type="number"
                single-line
                :readonly="item.haveMainPrice"
                :rules="[rules.decimal4]"
                dense
              ></v-text-field>
              <div v-else>{{ item.price }}</div>
            </template>
            <template v-slot:[`item.totalPrice`]="{ item }">
              {{
                isJPY
                  ? Math.round(item.price * item.applyNumber)
                  : Math.round(item.price * item.applyNumber * 100) / 100
              }}
            </template>
            <template v-slot:[`item.totalPriceConfirm`]="{ item }">
              {{
                isJPY
                  ? Math.round(item.price * item.confirmNumber)
                  : Math.round(item.price * item.confirmNumber * 100) / 100
              }}
            </template>
            <template v-slot:[`item.confirmOption`]="{ item }">
              <!-- <v-switch
                v-model="item.isConfirm"
                dense
                color="success"
                :readonly="detail.businessStatus != '待各主管确认'"
              ></v-switch> -->
              <!-- @change="trackingStateChanged($event, item)" -->
              <v-switch
                v-model="item.isConfirm"
                :readonly="detail.businessStatus != '待各主管确认'"
                dense
                :disabled="item.OldConfirm"
                color="success"
              ></v-switch>
            </template>
            <template v-slot:[`item.orderNo`]="{ item }">
              <v-btn
                v-if="item.orderId && item.orderNo"
                :to="{
                  name: 'materials-order-detail',
                  params: { id: item.orderId },
                }"
                color="primary"
                text
              >
                <sapn>
                  {{ item.orderNo }}:{{ businessStatusMap[item.orderStatus] }}
                </sapn>
              </v-btn>
            </template>
          </v-table-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <material-part-select
      v-model="dialog"
      :searchRemain="searchComponentObj"
      :subject-id="detail.costSubjectId"
      :subject-type="subjectType"
      :currency-id="detail.currencyId"
      :proposed-date="detail.proposedDate"
      :components.sync="components"
      :shipCodes="detail.shipCodes"
    ></material-part-select>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import MaterialPartSelect from './private/material-part-select.vue'
import routerControl from '@/mixin/routerControl'
export default {
  components: {
    MaterialPartSelect,
  },
  name: 'material-batch-purchase-detail',
  mixins: [currencyHelper, routerControl],
  created() {
    // this.backRouteName =
    //   this.detail.type == '机务'
    //     ? 'component-batch-purchase-list'
    //     : 'component-batch-purchase-listTD'
    this.backRouteName = 'material-batch-purchase-list'
    this.subtitles = ['基本信息', '物料明细']
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.付款公司选项 = [
      { text: '3000', value: '3000' },
      { text: '8903', value: '8903' },
      { text: '3402', value: '3402' },
    ]
    this.applyTypeItems = [
      { text: '常规', value: 1 },
      { text: '紧急', value: 2 },
      { text: '坞修', value: 3 },
      // { text: '固定资产', value: 4 },
      // { text: '通导固定资产', value: 5 },
    ]
    this.applyTypeItemsTD = [
      // { text: '常规', value: 1 },
      // { text: '紧急', value: 2 },
      // { text: '坞修', value: 3 },
      // { text: '固定资产', value: 4 },
      { text: '通导固定资产', value: 5 },
    ]
    this.pendingHeaders = [
      { text: '船舶名称', value: 'shipInfo' },
      { text: '物料名称', value: 'componentEname' },
      { text: 'IMPA', value: 'equipmentThirdCname' },
      { text: '物料描述', value: 'componentParameter' },
      { text: '物料编码', value: 'componentNumber' },
      { text: '单位', value: 'componentUnit' },
      { text: '采购数量', value: 'applyNumber' },
      { text: '单价', value: 'price' },
      { text: '申请总价', value: 'totalPrice' },
      { text: '机务主管', value: 'manager' },
      { text: '备注', value: 'remark' },
    ]
    this.pendingHeadersConfirm = [
      { text: '船舶名称', value: 'shipInfo' },
      { text: '物料名称', value: 'componentEname' },
      { text: 'IMPA', value: 'equipmentThirdCname' },
      { text: '物料描述', value: 'componentParameter' },
      { text: '物料编码', value: 'componentNumber' },
      { text: '单位', value: 'componentUnit' },
      { text: '采购数量', value: 'applyNumber' },
      { text: '到货数量', value: 'warehouseNum' },
      { text: '单价', value: 'price' },
      { text: '采购总价', value: 'totalPrice' },
      { text: '机务主管', value: 'manager' },
      { text: '实际确认人', value: 'handlerName' },
      { text: '确认', value: 'confirmOption' },
      { text: '备注', value: 'remark' },
      { text: '订单号', value: 'orderNo' },
      { text: '超预算说明', value: 'budgetRemark' },

      // { text: '船舶名称', value: 'shipInfo' },
      // { text: '子设备', value: 'equipmentThirdCname' },
      // { text: '备件名', value: 'componentEname' },
      // { text: '备件号', value: 'componentNumber' },
      // // { text: '备件SAPCODE', value: 'sapComponentNumberNew' },
      // // { text: '单位', value: 'componentUnit' },
      // { text: '采购数量', value: 'applyNumber' },
      // // { text: '确认数量', value: 'confirmNumber' },
      // { text: '单价', value: 'price' },
      // { text: '采购总价', value: 'totalPrice' },
      // // { text: '确认总价', value: 'totalPriceConfirm' },
      // { text: '机务主管', value: 'manager' },
      // { text: '实际确认人', value: 'handlerName' },
      // { text: '确认', value: 'confirmOption' },
      // { text: '备注', value: 'remark' },
    ]
    this.businessStatusMap = {
      0: '费用项目未提交', //未做凭证
      1: '发票未提交', //已做凭证
      10: '发票未提交', //未提交
      11: '发票审批中', //凭证审批中
      12: '发票审批退回', //审批失败
      13: '发票已审批', //审批通过
      21: '发票财务未确认-业务', //映射错误
      22: '发票财务未确认-业务', //报文错误
      24: '发票财务确认中', //已发送SAP
      26: '发票财务已确认', //SAP执行成功
      27: '发票财务未确认-SAP', //SAP执行失败
      30: '付款审批审批中', //付款审批中
      31: '付款审批退回', //付款审批未通过
      32: '付款审批已完成', //付款审批通过
      33: '已付款', //已付款
      34: '付款审批未提交', //付款审批未提交
      99: '发票待实际申请人确认',
      999: '待供应商报价',
      1000: '待机务/通导主管确认',
      100991: '供应商修改订单待确认',
      1001: '待供应商发货', //待供应商确认发货
      10011: '机务主管已确认，待OA审批通过',
      10099: '机务主管已确认,超二级科目预算审批中',
      100999: '机务主管已确认,超二级科目预算审批失败',
      1002: '供应商已发货', //已发货
      1003: '船端入库未提交', //已到货
      1008: '船端入库审批中', //入库中
      1004: '船端已入库', //入库完成
      10044: '已上传发票', //入库完成
      1005: '付款中',
      1006: '已付款',
      1007: '作废',
      1009: '重新定标',
    }
    // this.searchDicts = [
    //   {
    //     dicType: 'cost_subject_type',
    //     label: '业务分类',
    //     key: 'subjectType',
    //   },
    // ]
  },
  data() {
    return {
      detail: { currencyObj: {}, attachmentIds: [], orderType: '02' },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        numberRule1: (v) => {
          // 检查v是否为数字,如果是,返回验证是否保留两位小数的结果,否则返回错误信息
          if (!isNaN(parseFloat(v)) && isFinite(v)) {
            // 使用正则表达式来检查是否有超过两位小数
            if (/^-?\d+(\.\d{1,2})?$/.test(v)) {
              return true
            } else {
              return '输入的数字必须保留两位小数'
            }
          } else {
            return '请输入数字'
          }
        },
        numberRule2: (v) => {
          // 检查v是否为数字，如果是，返回转换为整数的值，否则返回错误信息
          if (!isNaN(parseFloat(v))) {
            return parseInt(v) == v || '输入的数字必须为整数'
          } else {
            return '请输入数字'
          }
        },
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
        decimal4: (v) =>
          /^\d+(\.\d{1,4})?$/.test(v) || '必须为整数或最多四位小数',
        decimalTwoPlacesMaxOne: (v) =>
          (/^\d{0,1}(\.\d{2})?$/.test(v) && parseFloat(v) <= 1) ||
          '必须为两位小数且值不大于1',
      },
      searchObj: { subjectType: '', materialsApply: '物料申请' },
      searchComponentObj: {},
      currency: [],
      initSupply: {},
      initHandler: false,
      components: [],
      delList: [],
      dialog: false,
      select: false,
      initSubject: {},
      selectList: [],
      loading: false,
      file: null,
    }
  },
  watch: {
    components: {
      handler(val) {
        console.log(val)
        var totalPrice = 0
        var finalTotalPrice = 0
        val.forEach((item) => {
          totalPrice =
            Number(totalPrice) + Number(item.price) * Number(item.applyNumber)
          finalTotalPrice =
            Number(finalTotalPrice) +
            Number(item.price) * Number(item.confirmNumber)
        })
        this.detail.money = totalPrice.toFixed(2)
        this.detail.moneyUsd = (totalPrice * this.rate).toFixed(2)
        this.detail.moneyConfirm = finalTotalPrice.toFixed(2)
        this.detail.moneyConfirmUsd = (finalTotalPrice * this.rate).toFixed(2)
      },
      deep: true,
    },
    'detail.currencyObj'(val) {
      if (val) {
        // console.log(val)
        // console.log(this.detail)
        this.detail.currencyId = this.detail.currencyObj.ccyCode
        // console.log(this.detail)
      }
    },
    'detail.costSubjectId'(_, oldVal) {
      if (!oldVal) return
      this.clearComponents()
    },
    // 'detail.applyType'(_, oldVal) {
    //   //  { text: '常规', value: 1 },
    //   // { text: '紧急', value: 2 },
    //   // { text: '坞修', value: 3 },
    //   // { text: '固定资产', value: 4 },
    //   // { text: '通导固定资产', value: 5 },
    //   console.log(_)
    //   console.log(oldVal)
    //   if (oldVal) this.clearEngineType()
    //   if (_) {
    //     // 费用科目查询
    //     if (_ == 1 || _ == 2 || _ == 3) {
    //       this.searchObj = { subjectType: '备件费' }
    //     } else if (_ == 4) {
    //       this.searchObj = { subjectType: '固定资产费用' }
    //     } else if (_ == 5) {
    //       console.log(2222222222222222)
    //       this.searchObj = { subjectType: '通导设备固定资产' }
    //     }
    //     console.log(this.searchObj)
    //   }
    // },
    // 'detail.applyType'(val) {
    //   if (val) {
    //     if (val == 3) {
    //       this.detail.isDockRepair = true
    //     } else if (val == 1) {
    //       this.detail.isDockRepair = false
    //     } else if (val == 2) {
    //       this.detail.isDockRepair = false
    //     }
    //     if (!this.isEdit) {
    //       this.detail.initSubject = {}
    //       this.detail.costSubjectId = ''
    //     }
    //   }
    // },
    file(val) {
      if (val) {
        this.importExcel()
      }
    },
    subjectType: {
      handler(val) {
        this.searchObj.subjectType = val
      },
      immediate: true,
    },
    // 'detail.paymentCompany'(val) {
    //   if (val) this.$refs.shipSelectPay.beforeMount()
    // },
  },
  computed: {
    // isJPY() {
    //   return (
    //     this.detail.currencyObj.ccyCode === 'JPY' ||
    //     this.detail.currencyObj.ccyCode === '日元' ||
    //     this.detail.ccyCode === 'JPY' ||
    //     this.detail.ccyCode === '日元'
    //   )
    // },
    isEdit() {
      return (
        this.$route.params.id !== 'new' &&
        this.detail.businessStatus !== '未提交'
        // this.$route.params.id !== 'new' &&
        // this.detail.businessStatus !== '未提交'
      )
    },
    canEditApplyNumber() {
      return (
        this.$local.data.get('userInfo').roleName.includes('采购主管') &&
        this.detail.businessStatus == '待各主管确认'
        // this.$route.params.id !== 'new' &&
        // this.detail.businessStatus !== '未提交'
      )
    },
    isJPY() {
      return (
        this.detail.currencyObj.ccyCode === 'JPY' ||
        this.detail.currencyObj.ccyCode === '日元' ||
        this.detail.ccyCode === 'JPY' ||
        this.detail.ccyCode === '日元'
      )
      // const JPYID = this.currencyInfo.find((i) => i.ccyCode === 'JPY')?.id
      // console.log('1231231313', this.detail.currencyId === JPYID)
      // return this.detail.currencyId === JPYID
    },
    rate() {
      return this.currencyInfo.find(
        (i) => i.ccyCode === this.detail.currencyObj?.ccyCode,
      )?.rateToMain
    },
    subjectType() {
      if (this.detail.applyType == 3) {
        return '坞修费'
      } else if (this.detail.applyType == 2) {
        return '应急物料费'
      } else if (this.detail.applyType == 1) {
        return '招标物料费'
      } else {
        return ''
      }
    },
  },
  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return false
      }
      if (this.components.length === 0) {
        this.$dialog.message.warning('请添加物料')
        return false
      }
      const detailList = this.getCompWithOperation()
      this.loading = true
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/componentBatchPurchase/componentBatchPurchaseSaveOrUpdate',
        {
          ...this.detail,
          detailList,
        },
      )
      this.loading = false
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      this.detail.businessStatus = '待各主管确认'
      const data = await this.save(goBack, true)
      if (!data) return
      this.loading = false
      // this.closeAndTo(this.backRouteName)
      this.closeAndTo(this.backRouteName)
    },
    createCom() {
      this.searchComponentObj = {
        stopUse: false,
        paymentCompany: this.detail.paymentCompany,
        costSubjectId: this.detail.costSubjectId,
        managerType: this.detail.applyType == 5 ? '通导' : '机务',
      }
      this.dialog = true
    },
    async delCom() {
      // this.delList.push({ ...this.select, operationType: 3 })
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.components = this.components.filter(
        (i) => i.componentId !== this.select.componentId,
      )
      this.select = false
    },
    getCompWithOperation() {
      const ids = this.components.map((i) => i.id)
      const delList =
        this.$route.params.id != 'new'
          ? this.detail.detailList
              .filter((i) => !ids.includes(i.id))
              .map((i) => {
                return { ...i, operationType: 3 }
              })
          : []
      const others = this.components.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/componentBatchPurchase/componentBatchPurchaseDetailById',
        { applyId: this.$route.params.id },
      )
      this.detail = { ...data }
      this.components = [...data.detailList]
      this.components.forEach((item) => {
        item.OldConfirm = item.isConfirm
        // item.componentId = item.componentId + item.shipCode
      })
      this.initHandler = { id: data.applicantId, nickName: data.applicantName }
      this.initSupply = {
        id: data.supplierId,
        name: data.supplierName,
      }
      this.initSubject = {
        subjectName: data.subjectName,
        id: data.costSubjectId,
      }
      this.detail.currencyObj = {
        ccyName: data.ccyName,
        ccyCode: data.ccyCode,
        id: data.currencyId,
      }
      // console.log(this.initSupply)
      // console.log(this.initSubject)
    },
    confirmDetails() {
      this.components.forEach((item) => {
        item.isConfirm = true
      })
    },
    async saveConfirmDetails() {
      const detailList = this.getCompWithOperation()
      var hasNotConfirm = false
      // var numberNot0 = false
      detailList.forEach((item) => {
        if (!item.isConfirm) {
          hasNotConfirm = true
          // if (
          //   !this.$dialog.msgbox.confirm(
          //     '明细第' + index + '项未确认，是否确认提交？',
          //   )
          // )
          //   return
        }
        // if (item.confirmNumber <= 0) {
        //   numberNot0 = true
        // }
      })
      if (hasNotConfirm) {
        this.$dialog.message.error('存在未确认的物料,提交失败！')
        // this.$dialog.msgbox.confirm('请全部确认后再提交！')return
        // if (!this.$dialog.msgbox.confirm('存在未确认的备件，否确认提交？'))
        //   return false
      }
      // if (numberNot0) {
      //   this.$dialog.message.error('确认数量是0,提交失败！')
      //   // this.$dialog.msgbox.confirm('请全部确认后再提交！')return
      //   // if (!this.$dialog.msgbox.confirm('确认数量是0，否确认提交？'))
      //   //   return false
      // }
      // if (!hasNotConfirm && !numberNot0) {
      if (!hasNotConfirm) {
        this.loading = true
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/componentBatchPurchase/saveConfirmDetails',
          detailList,
        )
        this.loading = false
        if (errorRaw) return false
        this.closeAndTo(this.backRouteName)
      }
    },
    async genOrder() {
      this.loading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/componentBatchPurchase/componentBatchGenOrder',
        { applyId: this.$route.params.id },
      )
      this.loading = false
      if (errorRaw) return false
      this.closeAndTo(this.backRouteName)
    },
    clearComponents() {
      this.$dialog.message.info('由于费用科目变更,自动清空物料')
      this.components = []
    },
    async dowExcel() {
      if (this.components.length === 0) {
        this.$dialog.message.warning('请添加物料')
        return false
      }
      const detailList = this.getCompWithOperation()
      this.loading = true
      const { errorRaw } = await this.blobDownload(
        '/business/shipAffairs/componentBatchPurchase/dowExcelMaterial',
        {
          // ...this.detail,
          detailList,
        },
        '物料集采明细.xlsx',
      )
      this.loading = false
      // if (errorRaw) this.$dialog.message.error(errorRaw)
      // // const items = this.components.map((i) => i.materialId)
      // const { errorRaw } = await this.blobDownload(
      //   '/business/shipAffairs/batchCostApply/dowExcel',
      //   {
      //     paymentCompany: this.detail.paymentCompany,
      //     userId: this.detail.initApply,
      //   },
      //   '批量预算导入模板.xlsx',
      // )
      if (errorRaw) this.$dialog.message.error(errorRaw)
    },
    async importExcel() {
      let formData = new FormData()
      formData.append('file', this.file)
      const detailList = this.getCompWithOperation()
      let componentBatchPurchaseModifyDTO = {
        detailList: detailList,
      }
      formData.append(
        'componentBatchPurchaseModifyDTO',
        new Blob([JSON.stringify(componentBatchPurchaseModifyDTO)], {
          type: 'application/json',
        }),
      )
      const { data } = await this.postAsync(
        '/business/shipAffairs/componentBatchPurchase/importExcelMaterial',
        formData,
      )
      if (data) {
        this.components.forEach((component) => {
          const dataItem = data.find(
            (item) => item.componentId === component.componentId,
          )
          if (dataItem) {
            component.applyNumber = dataItem.applyNumber
            component.price = dataItem.price
            component.remark = dataItem.remark
          }
        })
        var totalPrice = 0
        var finalTotalPrice = 0
        this.components.forEach((item) => {
          totalPrice =
            Number(totalPrice) + Number(item.price) * Number(item.applyNumber)
          finalTotalPrice =
            Number(finalTotalPrice) +
            Number(item.price) * Number(item.confirmNumber)
        })
        this.detail.money = totalPrice.toFixed(2)
        this.detail.moneyUsd = (totalPrice * this.rate).toFixed(2)
        this.detail.moneyConfirm = finalTotalPrice.toFixed(2)
        this.detail.moneyConfirmUsd = (finalTotalPrice * this.rate).toFixed(2)
        // this.components = data
        // this.components = this.components.map((i) => {
        //   return {
        //     ...i,
        //     componentId: i.id,
        //     shipCode: i.shipInfo.shipCode,
        //     id: i.vid,
        //     applyNumber: i.applyNumber || 0,
        //     price: i.price || 0,
        //     componentNumber: i.componentNumber,
        //     equipmentThirdCname: i.equipmentThirdCname,
        //     sapComponentNumberNew: i.sapComponentNumberNew,
        //     manager: i.manager,
        //     remark: i.remarkk || '',
        //   }
        // })
      }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style>
/* 隐藏所有数字输入框的增减按钮 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
/* Firefox 兼容 */
/* input[type='number'] {
  -moz-appearance: textfield;
} */
</style>
