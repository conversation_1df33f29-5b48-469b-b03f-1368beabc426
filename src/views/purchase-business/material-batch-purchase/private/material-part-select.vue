<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        物料选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-title>
        <v-row>
          <v-col class="py-0" cols="12">
            <v-ship-select v-model="shipCodeList" multiple></v-ship-select>
          </v-col>
        </v-row>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          fuzzy-label="名称/编码/描述/IMPA"
        >
          <template #searchflieds>
            <v-col cols="3">
              <v-select
                v-model="searchObj.type"
                :items="materialTypes"
                label="物料分类"
                outlined
                dense
                clearable
              ></v-select>
            </v-col>
          </template>
          <template #btns></template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          生成各船明细
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
// materialCategory	物料分类	string
// materialCnName	物料中文名称	string
// materialCode	物料编码	string
// materialDescription	物料描述	string
// materialEnName	物料英文名称	string
// materialId	物料id	string
// materialModel	物料型号	string
// materialSpec	物料规格	string
// materialUnit	物料单位	string
export default {
  name: 'materials-part-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/shipAffairs/MaterialInfo/listPrice'
    this.headers = [
      { text: '物料名称', value: 'nameCn' },
      { text: 'IMPA', value: 'nameEn' },
      { text: '物料编码', value: 'code' },
      { text: '物料描述', value: 'description' },
      { text: '物料协议价', value: 'price' },
      // { text: '物料型号', value: 'model' },
      // { text: '物料规格', value: 'specs' },
      { text: '物料单位', value: 'unit' },
    ]
    this.fuzzyLabel = '名称/编码/描述/IMPA'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    components: Array,
    subjectId: String,
    subjectType: String,
    shipCodes: Array,
    currencyId: String,
    proposedDate: Date,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      loading: false,
      searchObj: { subjectId: '', stopUse: false },
      selected: [],
      materialTypes: [],
      shipCodeList: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    subjectId: {
      handler(val) {
        if (this.subjectType === '坞修费') {
          this.searchObj = {
            dockSubjectId: val,
            subjectId: '',
            stopUse: false,
          }
        } else if (this.subjectType === '招标物料费') {
          this.searchObj = {
            dockSubjectId: '',
            subjectId: val,
            stopUse: false,
          }
        } else {
          this.searchObj = {
            dockSubjectId: '',
            subjectId: '',
            stopUse: false,
          }
        }
        this.loadMaterialTypes()
      },
      immediate: true,
    },
    // searchRemain(val) {
    //   this.searchObj = val
    // },
    components: {
      handler(val) {
        const seenIds = new Set() // 用于去重的 Set
        const processedComponentsA = val
          .map((component) => {
            // 创建组件的副本
            const newComponent = { ...component }
            // 更新 componentId，去除最后四位
            newComponent.componentId = newComponent.componentId.slice(0, -4)

            // 检查更新后的 componentId 是否已经存在
            if (!seenIds.has(newComponent.componentId)) {
              // 如果不存在，则添加到 Set 中，并保留该组件对象
              seenIds.add(newComponent.componentId)
              return newComponent
            } else {
              // 如果存在（理论上不应该进入这里，因为我们正在映射原始数组，但为了完整性而包含）
              // 可以选择返回 null 或不返回任何东西（但 map 总是会返回与原始数组相同长度的数组）
              // 在这个例子中，我们假设不应该有重复的处理后的 componentId，所以实际上不会执行到这里
              // 如果确实需要处理这种情况，你可能需要采用不同的逻辑结构，比如 for 循环
              return null // 但注意，这会导致 processedComponents 数组中有 null 值
            }
          })
          .filter((component) => component !== null) // 移除任何 null 值（如果有的话）

        console.log('processedComponentsA:', processedComponentsA)
        // 注意：由于我们假设每个原始组件在去除最后四位 componentId 后都是唯一的，
        // 因此上面的 else 分支实际上不会执行。如果实际情况不是这样，你需要重新考虑去重逻辑。

        // const tempIds = val.map((component) => {
        //   return component.componentId.slice(0, -4)
        // })
        // tempIds.forEach((item) => {

        // })
        // const processedComponentIds = [...new Set(tempIds)]
        this.selected = processedComponentsA.map((i) => {
          return {
            ...i,
            vid: i.id,
            id: i.componentId,
            remarkk: i.remark,
          }
        })
        // this.selected = val.map((i) => {
        //   return {
        //     ...i,
        //     vid: i.id,
        //     id: i.componentId.slice(0, -4),
        //     remarkk: i.remark,
        //   }
        // })
      },
      deep: true,
    },
    currencyId(val) {
      this.searchObj.currencyId = val
    },
    proposedDate(val) {
      this.searchObj.date = val
    },
    shipCodes(val) {
      this.shipCodeList = val
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async confirm() {
      console.log('confirm', this.selected)
      if (this.shipCodeList.length <= 0) {
        this.$dialog.message.error('请选择船舶，操作失败！')
        return
      }
      const components = []
      for (const key of this.shipCodeList) {
        const { data } = await this.getAsync(
          '/business/shipAffairs/componentBatchPurchase/getBaseByShipCode',
          { shipCode: key },
        )

        const components1 = this.selected.map((i) => {
          const comp = {
            ...i,
            componentId: i.id + key,
            id: i.vid,
            applyNumber: i.applyNumber || 0,
            price: i.price || 0,
            componentEname: i.nameCn, // 名称
            equipmentThirdCname: i.nameEn, // IMPA
            componentParameter: i.description, // 描述
            componentNumber: i.code, // 物料编码
            componentUnit: i.unit, // 单位
            haveMainPrice: i.haveMainPrice,
            remark: i.remarkk || '',
            shipInfo: data.shipInfo,
            manager: data.manager,
            shipCode: key,
          }
          return comp
        })
        components1.forEach((item) => {
          components.push(item)
        })
      }

      this.$emit('update:components', components)
      this.$emit('update:shipCodes', this.shipCodeList)
      this.$emit('change', false)
    },
    async loadMaterialTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        {
          page: 1,
          size: 1000,
          ...this.searchObj,
        },
      )
      this.materialTypes = data.records.map((i) => {
        return {
          text: i.cateName,
          value: i.id,
        }
      })
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
