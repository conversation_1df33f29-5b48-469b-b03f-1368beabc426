<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['供应商评审合同详细信息:编辑']"
      :title="`供应商评审合同详细信息-${
        isEdit ? detail.applicationNo : '新增'
      }`"
      :tooltip="isEdit ? detail.applicationNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-save="!isEdit"
      @save="save"
    >
      <!-- <template v-slot:titlebtns>
        <v-btn
          v-if="false"
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn>
        <v-btn
          v-if="!isEdit"
          width="60"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          :loading="loading"
        >
          保存
        </v-btn>
      </template> -->
      <template v-slot:custombtns>
        <!-- <v-btn
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: detail.systemReportId },
          }"
          color="info"
          small
          class="mx-1"
          v-permission="['备件申请:查看部门报表']"
        >
          查看部门报表
        </v-btn> -->
        <v-btn
          v-if="!isEdit"
          width="100"
          tile
          @click="sendOA"
          color="success"
          small
          class="mx-1"
          v-permission="['供应商评审合同详细信息:发送OA评审']"
          :loading="sendloading"
        >
          发送OA评审
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <v-card-text>
        <v-expansion-panels multiple accordion v-model="panel" focusable>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              基本信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form">
                  <v-row>
                    <v-col cols="12" md="6" class="py-0">
                      <v-select
                        v-model="detail.ctype"
                        :items="[
                          { text: '新增供应商评审', value: '1' },
                          { text: '续签供应商评审（年度评审）', value: '2' },
                          { text: '变更银行结算信息', value: '3' },
                          { text: '加入黑名单', value: '4' },
                        ]"
                        label="评审类型"
                        dense
                        :rules="[rules.required]"
                        :readonly="true"
                        required
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-select
                        v-model="detail.mtype"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '2' },
                        ]"
                        label="是否批量评审"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        required
                        outlined
                      ></v-select>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="6" class="py-0">
                      <v-select
                        v-model="detail.supplierType"
                        :items="[
                          { text: '-', value: '1' },
                          { text: '通用-其他管理服务类供应商', value: '1099' },
                          { text: '船东-船舶物料', value: '2011' },
                          { text: '船东-船舶维修服务', value: '2013' },
                          { text: '船东-船舶坞修厂商', value: '2014' },
                          { text: '船东-油漆供应商', value: '2015' },
                          { text: '船东-船舶通讯导航', value: '2016' },
                          { text: '船东-船员外包公司', value: '2017' },
                          { text: '船东-备件供应商', value: '2018' },
                          { text: '船东-滑油供应商', value: '2019' },
                        ]"
                        label="供应商类型"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        required
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <!-- 财务核算统驭科目为OA财务审批时选项，待确认是否需要在船管系统中展示 -->
                      <v-select
                        v-model="detail.controlAccountType"
                        :items="[
                          { text: '-', value: '-' },
                          { text: '应付账款', value: '**********' },
                          { text: '其他应付款-供应商', value: '**********' },
                          {
                            text: '长期应付款-长期租赁负债-房屋建筑物',
                            value: '**********',
                          },
                        ]"
                        label="财务核算统驭科目"
                        dense
                        required
                        outlined
                      ></v-select>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <v-select
                        v-model="detail.multiOffice"
                        multiple
                        :items="[
                          {
                            text: 'SITC SHIPOWNING GROUP COMPANY LIMITED---3000',
                            value: '3000',
                          },
                          {
                            text: '山东海丰航运有限公司---8903',
                            value: '8903',
                          },
                        ]"
                        label="评审所属公司"
                        dense
                        @change="(val) => $emit('input', String(val))"
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        required
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-text-field
                        label="合同名称"
                        v-model="detail.contractName"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="12" class="py-0">
                      <v-textarea
                        label="供应商基本信息及采购业务简要说明"
                        v-model="detail.supplierInfo"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              签约信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
              <v-card-text>
                <v-form ref="form1">
                  <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
                  <!-- <v-row>
                    <v-col cols="12" md="3" class="py-0"></v-col>
                    <v-col cols="12" md="4" class="py-0">他方签约公司</v-col>
                    <v-col cols="12" md="5" class="py-0">我方签约公司</v-col>
                  </v-row> -->
                  <v-row>
                    <!-- <v-col cols="12" md="3" class="py-0">
                      <span style="font-size: 18px">签署协议</span>
                    </v-col> -->
                    <v-col
                      v-if="detail.ctype == '1' || detail.ctype == '2'"
                      cols="12"
                      md="9"
                      class="py-0"
                    >
                      <!-- &nbsp;&nbsp;&nbsp;&nbsp; -->
                      <v-radio-group
                        :rules="[rules.radio, rules.required]"
                        ref="radio"
                        label=" 签署协议"
                        required
                        class="pb-0"
                        v-model="detail.supplierAssessItemOutputDTO.isSign"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="2"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.isSign"
                        :items="[
                          { text: '是', value: 1 },
                          { text: '否', value: 2 },
                        ]"
                        label="签署协议"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        required
                        outlined
                      ></v-select> -->
                    </v-col>
                    <!-- <v-col cols="12" class="py-0" v-if="detail.applyType != 1">
                      <v-textarea
                        v-model="detail.remark"
                        :label="detail.applyType == 2 ? '退出说明' : '评级说明'"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.required]"
                        height="50px"
                      ></v-textarea>
                    </v-col> -->
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label="外部个人供应商"
                        class="pb-0"
                        v-model="
                          detail.supplierAssessItemOutputDTO.isPersonalSupplier
                        "
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="2"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.personalSupplier"
                        :items="[
                          { text: '是', value: 1 },
                          { text: '否', value: 2 },
                        ]"
                        label="外部个人供应商"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        required
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col v-if="detail.ctype == '3'" cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label="变更内容"
                        class="pb-0"
                        v-model="
                          detail.supplierAssessItemOutputDTO.purchaseType
                        "
                        row
                      >
                        <v-radio label="变更结算账户" value="1"></v-radio>
                        <v-radio label="变更收款人" value="2"></v-radio>
                        <v-radio label="其他" value="3"></v-radio>
                      </v-radio-group>
                    </v-col>
                    <v-col v-if="detail.ctype == '3'" cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label="是否与供应商电话确认"
                        class="pb-0"
                        v-model="detail.isTeletocus"
                        row
                      >
                        <v-radio label="是" value="0"></v-radio>
                        <v-radio label="否" value="1"></v-radio>
                      </v-radio-group>
                    </v-col>
                    <v-col v-if="detail.ctype == '4'" cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label="黑名单级别"
                        class="pb-0"
                        v-model="detail.supplierAssessItemOutputDTO.blackType"
                        row
                      >
                        <v-radio label="冻结交易" value="1"></v-radio>
                        <v-radio label="特别关注" value="2"></v-radio>
                      </v-radio-group>
                    </v-col>
                    <!-- <v-col
                      v-if="detail.ctype == '1' || detail.ctype == '2'"
                      cols="12"
                      class="py-0"
                    >
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label="供应商类型细类"
                        class="pb-0"
                        v-model="
                          detail.supplierAssessItemOutputDTO.purchaseType
                        "
                        row
                      >
                        <v-radio label="船舶购置" value="S1"></v-radio>
                        <v-radio label="集装箱购置" value="S4"></v-radio>
                        <v-radio label="土地购置" value="S3"></v-radio>
                        <v-radio label="房产购置" value="S2"></v-radio>
                        <v-radio label="其他" value="S5"></v-radio>
                      </v-radio-group>
                    </v-col> -->
                    <v-col v-if="detail.ctype == '4'" cols="12" class="py-0">
                      <v-textarea
                        label="“加入黑名单”原因说明"
                        v-model="detail.supplierAssessItemOutputDTO.blackNote"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-textarea>
                    </v-col>
                    <!-- <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.applyType"
                        :items="[
                          { text: '是', value: 1 },
                          { text: '否', value: 2 },
                        ]"
                        label="是否批量评审"
                        dense
                        :rules="[rules.required]"
                        readonly
                        required
                        outlined
                      ></v-select>
                    </v-col> -->
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <v-text-field
                        label="合同名称（详细信息）"
                        v-model="
                          detail.supplierAssessItemOutputDTO.contractNameInItems
                        "
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        label="供应商基本信息（详细信息）"
                        v-model="
                          detail.supplierAssessItemOutputDTO.supplierInfoInItems
                        "
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-textarea>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">他方签约公司</span>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <!-- <vs-date-picker
                        :readonly="isEdit"
                        outlined
                        dense
                        v-model="detail.projectName"
                        use-today
                        label="公司注册全称"
                        :rules="[rules.required]"
                      ></vs-date-picker> -->
                      <v-text-field
                        label="公司注册全称(他方签约公司)"
                        v-model="detail.supplierAssessItemOutputDTO.projectName"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <!-- <v-col cols="12" md="6" class="py-0"></v-col> -->
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="供应商税号(他方签约公司)"
                        v-model="
                          detail.supplierAssessItemOutputDTO.supplierTaxcode
                        "
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="detail.applyPurpose"
                  label="申请目的"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col> -->
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.supplierCode
                        "
                        label="供应商业务系统编码(他方签约公司)"
                        :readonly="isEdit"
                        dense
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.projectNameSap
                        "
                        label="SAP代码(他方签约公司)"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="
                          this.detail.ctype != '1' ? [rules.required] : []
                        "
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0"></v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.bcountrySap"
                        :items="countrys"
                        label="注册国家地区代码(他方签约公司)"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.bcountryName
                        "
                        label="注册国家地区名称(他方签约公司)"
                        dense
                        outlined
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">我方签约公司</span>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <!-- <vs-date-picker
                        :readonly="isEdit"
                        outlined
                        dense
                        v-model="detail.projectName"
                        use-today
                        label="公司注册全称"
                        :rules="[rules.required]"
                      ></vs-date-picker> -->
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.myCompany"
                        :items="[
                          {
                            text: 'SITC SHIPOWNING GROUP COMPANY LIMITED',
                            value: '3000',
                          },
                          {
                            text: '山东海丰航运有限公司',
                            value: '8903',
                          },
                        ]"
                        label="公司注册全称(我方签约公司)"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0"></v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.myCompanySap
                        "
                        label="SAP代码(我方签约公司)"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.required]"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0"></v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.aCountrySap"
                        :items="countrys"
                        label="注册国家地区代码(他方签约公司)"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.aCountryName
                        "
                        label="注册国家地区名称(我方签约公司)"
                        dense
                        outlined
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row v-if="detail.ctype == '1' || detail.ctype == '2'">
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">合同标的金额</span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="合同标的金额"
                        v-model="detail.supplierAssessItemOutputDTO.value"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.waers"
                        :items="currs"
                        label="币别"
                        dense
                        clearable
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="单价"
                        v-model="detail.supplierAssessItemOutputDTO.price"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="数量"
                        v-model="detail.supplierAssessItemOutputDTO.num"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel v-if="detail.ctype == '1' || detail.ctype == '2'">
            <v-expansion-panel-header style="color: #3399cc">
              评估信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
              <v-card-text>
                <v-form ref="form2">
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label=" 关连交易及关连方认定结果"
                        class="pb-0"
                        v-model="detail.supplierAssessItemOutputDTO.relation"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="2"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.relation"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        label="关连交易及关连方认定结果"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">
                        履约能力评估类型（请填写评估结果说明、扫描件上传证明文件）
                      </span>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="
                          detail.supplierAssessItemOutputDTO.supplierNote1
                        "
                        label="公司注册证明(提供有效期内证照扫描件)"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="
                          detail.supplierAssessItemOutputDTO.supplierNote2
                        "
                        label="经营资质(提供与业务内容相匹配的经营资质或经营许可)"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="
                          detail.supplierAssessItemOutputDTO.supplierNote3
                        "
                        label="信用调查(行业口碑、审计报告、财务报表或其他公开信息)"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="
                          detail.supplierAssessItemOutputDTO.supplierNote4
                        "
                        label="询价与遴选(须提供多家供应商作为遴选对象或多家询价)"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel
            v-if="
              detail.ctype == '1' || detail.ctype == '2' || detail.ctype == '3'
            "
          >
            <v-expansion-panel-header style="color: #3399cc">
              结算信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
              <v-card-text>
                <v-form ref="form3">
                  <v-row>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.paymentDay"
                        :items="[
                          { text: '-', value: '-' },
                          { text: '立即到期', value: 'Z001' },
                          { text: '10天到期', value: 'Z010' },
                          {
                            text: '15天到期',
                            value: 'Z015',
                          },
                          {
                            text: '20天到期',
                            value: 'Z020',
                          },
                          {
                            text: '25天到期',
                            value: 'Z025',
                          },
                          {
                            text: '30天到期',
                            value: 'Z002',
                          },
                          {
                            text: '35天到期',
                            value: 'Z035',
                          },
                          {
                            text: '40天到期',
                            value: 'Z040',
                          },
                          {
                            text: '45天到期',
                            value: 'Z045',
                          },
                          {
                            text: '50天到期',
                            value: 'Z050',
                          },
                          {
                            text: '55天到期',
                            value: 'Z055',
                          },
                          {
                            text: '60天到期',
                            value: 'Z003',
                          },
                          {
                            text: '65天到期',
                            value: 'Z065',
                          },
                          {
                            text: '70天到期',
                            value: 'Z070',
                          },
                          {
                            text: '75天到期',
                            value: 'Z075',
                          },
                          {
                            text: '80天到期',
                            value: 'Z080',
                          },
                          {
                            text: '90天到期',
                            value: 'Z004',
                          },
                          {
                            text: '105天到期',
                            value: 'Z105',
                          },
                          {
                            text: '120天到期',
                            value: 'Z120',
                          },
                          {
                            text: '270天到期',
                            value: 'Z270',
                          },
                        ]"
                        label="赊销条件"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.supplierAssessItemOutputDTO.paymentDays"
                        label="赊销条件描述"
                        :readonly="isEdit"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-text-field
                        v-model="detail.supplierAssessItemOutputDTO.beneficiary"
                        label="收款人名称"
                        :readonly="isEdit"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="detail.supplierAssessItemOutputDTO.benefiBank"
                        label="	收款人开户行"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                        "
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="detail.supplierAssessItemOutputDTO.benefiBank1"
                        label="	收款人开户行1"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                        "
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.benefiAccount
                        "
                        label="收款人账号"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.benefiAccount1
                        "
                        label="收款人账号1"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.accountCurr"
                        :items="currs"
                        label="币别"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                        "
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="币别名称"
                        v-model="
                          detail.supplierAssessItemOutputDTO.accountCurrName
                        "
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                        "
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="
                          detail.supplierAssessItemOutputDTO.accountCurr1
                        "
                        :items="currs"
                        label="币别1"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                        "
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="币别名称1"
                        v-model="
                          detail.supplierAssessItemOutputDTO.accountCurrName1
                        "
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                        "
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        label="收款人开户SWIFT代码"
                        v-model="detail.supplierAssessItemOutputDTO.bankSwift"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        label="收款人开户SWIFT代码1"
                        v-model="detail.supplierAssessItemOutputDTO.bankSwift1"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankCountrySap
                        "
                        :items="countrys"
                        label="收款人开户国家代码"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                        "
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="收款人开户国家"
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankCountryName
                        "
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          )
                        "
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankCountrySap1
                        "
                        :items="countrys"
                        label="收款人开户国家代码1"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                        "
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="收款人开户国家1"
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankCountryName1
                        "
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          )
                        "
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankAreaName
                        "
                        label="收款人开户地区/省"
                        :readonly="isEdit"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          ) &&
                          (this.detail.supplierAssessItemOutputDTO
                            .bankCountrySap == '中国' ||
                            this.detail.supplierAssessItemOutputDTO
                              .bankCountrySap == '越南')
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          ) &&
                          (this.detail.supplierAssessItemOutputDTO
                            .bankCountrySap == '中国' ||
                            this.detail.supplierAssessItemOutputDTO
                              .bankCountrySap == '越南')
                        "
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankAreaName1
                        "
                        label="收款人开户地区/省1"
                        :readonly="isEdit"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          ) &&
                          (this.detail.supplierAssessItemOutputDTO
                            .bankCountrySap1 == '中国' ||
                            this.detail.supplierAssessItemOutputDTO
                              .bankCountrySap1 == '越南')
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          ) &&
                          (this.detail.supplierAssessItemOutputDTO
                            .bankCountrySap1 == '中国' ||
                            this.detail.supplierAssessItemOutputDTO
                              .bankCountrySap1 == '越南')
                        "
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankCityName
                        "
                        label="收款人开户城市"
                        :readonly="isEdit"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          ) &&
                          (this.detail.supplierAssessItemOutputDTO
                            .bankCountrySap == '中国' ||
                            this.detail.supplierAssessItemOutputDTO
                              .bankCountrySap == '越南')
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          ) &&
                          (this.detail.supplierAssessItemOutputDTO
                            .bankCountrySap == '中国' ||
                            this.detail.supplierAssessItemOutputDTO
                              .bankCountrySap == '越南')
                        "
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankCityName1
                        "
                        label="收款人开户城市1"
                        :readonly="isEdit"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          ) &&
                          (this.detail.supplierAssessItemOutputDTO
                            .bankCountrySap1 == '中国' ||
                            this.detail.supplierAssessItemOutputDTO
                              .bankCountrySap1 == '越南')
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          ) &&
                          (this.detail.supplierAssessItemOutputDTO
                            .bankCountrySap1 == '中国' ||
                            this.detail.supplierAssessItemOutputDTO
                              .bankCountrySap1 == '越南')
                        "
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankStreetName
                        "
                        label="收款人开户银行地址"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.bankStreetName1
                        "
                        label="收款人开户银行地址1"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col
                      v-if="
                        detail.supplierAssessItemOutputDTO.bankCountrySap ==
                          '中国' ||
                        detail.supplierAssessItemOutputDTO.bankCountrySap ==
                          '越南'
                      "
                      cols="12"
                      md="6"
                      class="py-0"
                    >
                      <v-text-field
                        v-model="detail.supplierAssessItemOutputDTO.bankCnaps"
                        label="	收款人开户银行代码"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col
                      v-if="
                        detail.supplierAssessItemOutputDTO.bankCountrySap1 ==
                          '中国' ||
                        detail.supplierAssessItemOutputDTO.bankCountrySap1 ==
                          '越南'
                      "
                      cols="12"
                      md="6"
                      class="py-0"
                    >
                      <v-text-field
                        v-model="detail.supplierAssessItemOutputDTO.bankCnaps1"
                        label="	收款人开户银行代码1"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col
                      v-if="
                        detail.supplierAssessItemOutputDTO.accountCurr ==
                        detail.supplierAssessItemOutputDTO.accountCurr1
                      "
                      cols="12"
                      md="6"
                      class="py-0"
                    >
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.usageDescription
                        "
                        label="	帐户用途说明"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          ) &&
                          this.detail.supplierAssessItemOutputDTO.accountCurr ==
                            this.detail.supplierAssessItemOutputDTO.accountCurr1
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount == ''
                          ) &&
                          this.detail.supplierAssessItemOutputDTO.accountCurr ==
                            this.detail.supplierAssessItemOutputDTO.accountCurr1
                        "
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col
                      v-if="
                        detail.supplierAssessItemOutputDTO.accountCurr ==
                        detail.supplierAssessItemOutputDTO.accountCurr1
                      "
                      cols="12"
                      md="6"
                      class="py-0"
                    >
                      <v-text-field
                        v-model="
                          detail.supplierAssessItemOutputDTO.usageDescription1
                        "
                        label="	帐户用途说明1"
                        dense
                        :rules="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          ) &&
                          this.detail.supplierAssessItemOutputDTO.accountCurr ==
                            this.detail.supplierAssessItemOutputDTO.accountCurr1
                            ? [rules.required]
                            : []
                        "
                        :required="
                          !(
                            !this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 ||
                            this.detail.supplierAssessItemOutputDTO
                              .benefiAccount1 == ''
                          ) &&
                          this.detail.supplierAssessItemOutputDTO.accountCurr ==
                            this.detail.supplierAssessItemOutputDTO.accountCurr1
                        "
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.supplierAssessItemOutputDTO.clause"
                        label="结算条件简要描述"
                        dense
                        :rules="[rules.required]"
                        required
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col
                      v-if="
                        (!this.detail.supplierAssessItemOutputDTO
                          .benefiAccount ||
                          detail.supplierAssessItemOutputDTO.benefiAccount ==
                            '') &&
                        (!this.detail.supplierAssessItemOutputDTO
                          .benefiAccount1 ||
                          detail.supplierAssessItemOutputDTO.benefiAccount1 ==
                            '')
                      "
                      cols="12"
                      class="py-0"
                    >
                      <v-textarea
                        label="未维护结算信息的原因"
                        v-model="
                          detail.supplierAssessItemOutputDTO.nosignreason
                        "
                        dense
                        :rules="
                          (!this.detail.supplierAssessItemOutputDTO
                            .benefiAccount ||
                            detail.supplierAssessItemOutputDTO.benefiAccount ==
                              '') &&
                          (!this.detail.supplierAssessItemOutputDTO
                            .benefiAccount1 ||
                            detail.supplierAssessItemOutputDTO.benefiAccount1 ==
                              '')
                            ? [rules.required]
                            : []
                        "
                        required
                        height="50px"
                        :readonly="isEdit"
                        outlined
                      ></v-textarea>
                    </v-col>
                    <v-col
                      v-if="
                        (!(
                          !detail.supplierAssessItemOutputDTO.benefiAccount ||
                          detail.supplierAssessItemOutputDTO.benefiAccount == ''
                        ) &&
                          detail.supplierAssessItemOutputDTO.bcountrySap !=
                            detail.supplierAssessItemOutputDTO
                              .bankCountrySap) ||
                        (!(
                          !detail.supplierAssessItemOutputDTO.benefiAccount1 ||
                          detail.supplierAssessItemOutputDTO.benefiAccount1 ==
                            ''
                        ) &&
                          detail.supplierAssessItemOutputDTO.bcountrySap !=
                            detail.supplierAssessItemOutputDTO.bankCountrySap1)
                      "
                      cols="12"
                      class="py-0"
                    >
                      <v-textarea
                        label="收款人注册地和其开户行注册地不一致原因"
                        v-model="
                          detail.supplierAssessItemOutputDTO.countrySapReason
                        "
                        dense
                        :rules="[rules.required]"
                        required
                        height="50px"
                        :readonly="isEdit"
                        outlined
                      ></v-textarea>
                    </v-col>
                    <v-col
                      v-if="
                        detail.supplierAssessItemOutputDTO.projectName !=
                        detail.supplierAssessItemOutputDTO.beneficiary
                      "
                      cols="12"
                      class="py-0"
                    >
                      <v-textarea
                        label="供应商名称和收款人名称不一致原因"
                        v-model="detail.supplierAssessItemOutputDTO.nameReason"
                        dense
                        :rules="[rules.required]"
                        required
                        height="50px"
                        :readonly="isEdit"
                        outlined
                      ></v-textarea>
                    </v-col>
                  </v-row>
                  <v-row v-if="detail.ctype == '1' || detail.ctype == '2'">
                    <v-col cols="12" md="6" class="py-0">
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.payer"
                        :items="[
                          {
                            text: 'SITC SHIPOWNING GROUP COMPANY LIMITED',
                            value: '3000',
                          },
                          {
                            text: '山东海丰航运有限公司',
                            value: '8903',
                          },
                        ]"
                        label="付款人名称"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="付款人SAP代码"
                        v-model="detail.supplierAssessItemOutputDTO.sapcode"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label=" 是否分期付款"
                        class="pb-0"
                        v-model="detail.supplierAssessItemOutputDTO.paymentType"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="2"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.paymentType"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '2' },
                        ]"
                        label="是否分期付款"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col
                      v-if="
                        detail.supplierAssessItemOutputDTO.paymentType == '11'
                      "
                      cols="12"
                      md="3"
                      class="py-0"
                    >
                      <v-text-field
                        label="总计金额"
                        v-model="detail.total"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col
                      v-if="
                        detail.supplierAssessItemOutputDTO.paymentType == '11'
                      "
                      cols="12"
                      md="3"
                      class="py-0"
                    >
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.totalType"
                        :items="currs"
                        label="币别"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
              <v-card-text
                v-if="detail.supplierAssessItemOutputDTO.paymentType == '11'"
              >
                <v-card-title>
                  <div></div>
                  <v-spacer></v-spacer>
                  <template>
                    <!-- <v-btn
                      v-if="!isEdit"
                      outlined
                      small
                      tile
                      color="success"
                      class="mx-1"
                      @click="addSupplierP"
                      v-permission="['供应商评审:新增分期']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增
                    </v-btn> -->
                    <!-- <v-btn
                      v-if="!isEdit"
                      small
                      :to="{
                        name: 'supplier-assess-detail-item',
                        params: { id: 'new', buniessId: this.$route.params.id },
                      }"
                      outlined
                      tile
                      color="success"
                      class="mx-1"
                      v-permission="['供应商评审:新增合同']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增
                    </v-btn> -->
                    <!-- <v-btn
                      outlined
                      small
                      tile
                      color="success"
                      :disabled="!bank"
                      class="mx-1"
                      @click="editSupplierBank"
                      v-permission="['供应商准入:编辑银行信息']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      编辑银行信息
                    </v-btn> -->
                    <v-btn
                      v-if="!isEdit"
                      outlined
                      small
                      tile
                      color="success"
                      class="mx-1"
                      @click="addPayCom"
                      v-permission="['供应商评审:新增结算信息']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增分期付款
                    </v-btn>
                    <v-btn
                      v-if="!isEdit"
                      small
                      outlined
                      tile
                      :disabled="!payComs"
                      color="warning"
                      class="mx-1"
                      @click="editSupplierP"
                      v-permission="['供应商评审:修改合同']"
                    >
                      <v-icon left>mdi-pencil</v-icon>
                      修改
                    </v-btn>
                    <v-btn
                      v-if="!isEdit"
                      small
                      outlined
                      tile
                      :disabled="!payComs"
                      color="error"
                      class="mx-1"
                      @click="delSupplierP"
                      v-permission="['供应商评审:删除结算信息']"
                    >
                      <v-icon left>mdi-delete-empty</v-icon>
                      删除
                    </v-btn>
                  </template>
                </v-card-title>
                <template>
                  <v-table-list
                    :headers="payComHeaders"
                    title="分期付款"
                    :items="supplierAssessItemPayList"
                    v-model="payComs"
                    :single-select="true"
                    @dbclick="editSupplierP"
                  >
                    <template v-slot:[`item.status`]="{ item }">
                      {{ ['有效', '暂停整顿', '冻结', '黑名单'][item.status] }}
                    </template>
                    <template v-slot:[`item.paymentType`]="{ item }">
                      {{ ['-', '是', '否'][item.paymentType] }}
                    </template>
                    <template v-slot:[`item.isSign`]="{ item }">
                      {{ ['-', '是', '否'][item.isSign] }}
                    </template>
                    <template v-slot:[`item.havaBillInformation`]="{ item }">
                      {{ ['-', '是', '否'][item.havaBillInformation] }}
                    </template>
                    <template v-slot:expanded-item="{ headers, item }">
                      <td :colspan="headers.length">
                        <v-list class="my-2" dense>
                          <v-list-item-group v-model="bank" color="primary">
                            <v-list-item-subtitle
                              class="d-flex justify-space-between"
                              v-for="b in item.supplierBankListOutputDTOS"
                              :key="b.id"
                              :value="b"
                            >
                              <div>银行名称:{{ b.bank }}</div>
                              <div>银行账户:{{ b.account }}</div>
                              <div>
                                <!-- 币别:{{ currencyInfo[b.currencyType].ccyCode }} -->
                                币别:{{
                                  currencyInfo.find(
                                    (i) => i.id === b.currencyType,
                                  ).ccyCode
                                }}
                              </div>
                              <!-- {{ b.ccyCode }} -->
                              <!-- <v-row>
                                <v-col cols="12" md="4">
                                  开户账号
                                  <v-text-field
                                    v-model="b.bank"
                                    outlined
                                    disabled
                                    readonly
                                    dense
                                  ></v-text-field>
                                </v-col>
                                <v-col cols="12" md="4">
                                  开户行
                                  <v-text-field
                                    v-model="b.account"
                                    outlined
                                    disabled
                                    readonly
                                    dense
                                  ></v-text-field>
                                </v-col>
                                <v-col cols="12" md="4">
                                  币种
                                  <v-select
                                    v-model="b.currencyType"
                                    outlined
                                    required
                                    disabled
                                    readonly
                                    dense
                                    item-text="ccyName"
                                    item-value="id"
                                    :items="currencyInfo"
                                  ></v-select>
                                </v-col>
                              </v-row> -->
                            </v-list-item-subtitle>
                          </v-list-item-group>
                        </v-list>
                      </td>
                    </template>
                  </v-table-list>
                </template>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel
            v-if="
              (detail.ctype == '1' || detail.ctype == '2') &&
              detail.supplierAssessItemOutputDTO.isSign != '2'
            "
          >
            <v-expansion-panel-header style="color: #3399cc">
              有效期信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form4">
                  <v-row>
                    <v-col cols="12" md="3" class="py-0">
                      <vs-date-picker
                        outlined
                        dense
                        :readonly="isEdit"
                        :rules="[rules.required]"
                        required
                        v-model="detail.supplierAssessItemOutputDTO.beginDate"
                        label="合同生效日期"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.supplierAssessItemOutputDTO.beginTime"
                        label="合同生效日期及条件"
                        :readonly="isEdit"
                        dense
                        outlined
                        height="50px"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <vs-date-picker
                        outlined
                        dense
                        :readonly="isEdit"
                        v-model="detail.supplierAssessItemOutputDTO.overDate"
                        label="合同终止日期"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.supplierAssessItemOutputDTO.overTime"
                        label="	合同终止日期及条件"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.supplierAssessItemOutputDTO.continot"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '2' },
                        ]"
                        label="是否自动延续"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.supplierAssessItemOutputDTO.isContinu"
                        label="延续条件"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <!-- <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              附件
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-attach-list
                  :attachments="detail.attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="detail.shipCode"
                ></v-attach-list>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel> -->
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              附件
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-attach-list
                  :attachments="detail.attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              评审合同附件
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-attach-list
                  :attachments="detail.attachmentRecords2"
                  @change="changeAttachment2"
                ></v-attach-list>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-card-text>
    </v-detail-view>
    <!-- <bank-dialog
      :initialData="form1"
      v-model="formShow1"
      @success="addBank"
    ></bank-dialog> -->
    <pay-installment‌-dialog
      :initialData="form2"
      v-model="formShow2"
      @success="addCom"
    ></pay-installment‌-dialog>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
// import bankDialog from '../private/bank-dialog.vue'
import PayInstallment‌Dialog from './private/pay-installment‌-dialog.vue'
import currencyHelper from '@/mixin/currencyHelper'
export default {
  components: { PayInstallment‌Dialog },
  name: 'supplier-assess-detail',
  mixins: [routerControl, currencyHelper],
  created() {
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.backRouteName = 'supplier-assess-list'
    // this.buniessId = this.$route.params.buniessId
    // this.status = this.$route.params.buniessStatus
    this.subtitles = [
      //   '准入推荐信息',
      //   '基础信息',
      //   '规模及产品信息',
      //   '结算信息',
      //   '评级打分',
    ]
    this.payComHeaders = [
      // { text: '', value: 'data-table-expand' },
      { text: '金额', value: 'amount' },
      { text: '币别', value: 'currency' },
      { text: '分期付款条件', value: 'term' },
      // { text: '有效期-结束', value: 'endDate' },
      // { text: '联系人姓名', value: 'managerName' },
      // { text: '联系人电话', value: 'managerPhone' },
      // { text: '评级打分', value: 'score' },
      // { text: '状态', value: 'status' },
      // { text: '供应商类型', value: 'supplierType' },
    ]
    this.currs = [
      // { text: '', value: 'data-table-expand' },
      { text: 'CNY', value: '人民币' },
      { text: 'HKD', value: '港币' },
      { text: 'TWD', value: '新台币' },
      { text: 'USD', value: '美元' },
      { text: 'EUR', value: '欧元' },
      { text: 'JPY', value: '日元' },
      { text: 'KRW', value: '韩币' },
      { text: 'VND', value: '越南盾' },
      { text: 'THB', value: '泰铢' },
      { text: 'IDR', value: '印尼盾' },
      { text: 'ZAR', value: '南非兰得' },
      { text: 'MMK', value: '缅甸币' },
      { text: 'INR', value: '印度比索' },
      { text: 'BDT', value: '孟加拉塔卡' },
      { text: 'GBP', value: '英镑' },
      { text: 'BND', value: '文莱元' },
      { text: 'PHP', value: '菲律宾比索' },
      { text: 'SGD', value: '新加坡元' },
      { text: 'MYR', value: '马来西亚元' },
    ]
    this.countrys = [
      // { text: '', value: 'data-table-expand' },
      { text: 'CN', value: '中国' },
      { text: 'HK ', value: '中国香港' },
      { text: 'ID', value: '印度尼西亚' },
      { text: 'JP', value: '日本' },
      { text: 'KH', value: '柬埔寨' },
      { text: 'KR', value: '韩国' },
      { text: 'MY', value: '马来西亚' },
      { text: 'PH', value: '菲律宾' },
      { text: 'SG', value: '新加坡' },
      { text: 'TH', value: '泰国' },
      { text: 'TW', value: '台湾' },
      { text: 'VN', value: '越南' },
      { text: 'AD', value: '安道尔共和国' },
      { text: 'AE', value: '阿联酋' },
      { text: 'AF', value: '阿富汗' },
      { text: 'AG', value: '安提瓜和巴布达' },
      { text: 'AI', value: '安圭拉岛' },
      { text: 'AL', value: '阿尔巴尼亚' },
      { text: 'AM', value: '亚美尼亚' },
      { text: 'AN', value: '荷属安的列斯岛' },
      { text: 'AO', value: '安哥拉' },
      { text: 'AQ', value: '南极洲' },
      { text: 'AR', value: '阿根廷' },
      { text: 'AS', value: '萨摩亚, 美国' },
      { text: 'AT', value: '奥地利' },
      { text: 'AU', value: '澳大利亚' },
      { text: 'AW', value: '阿鲁巴' },
      { text: 'AZ', value: '阿塞拜疆' },
      { text: 'BA', value: '波黑' },
      { text: 'BB', value: '巴巴多斯' },
      { text: 'BD', value: '孟加拉国' },
      { text: 'BE', value: '比利时' },
      { text: 'BF', value: '布基纳法索' },
      { text: 'BG', value: '保加利亚' },
      { text: 'BH', value: '巴林' },
      { text: 'BI', value: '布隆迪' },
      { text: 'BJ', value: '贝宁' },
      { text: 'BL', value: '蓝色' },
      { text: 'BM', value: '百慕大群岛' },
      { text: 'BN', value: '文莱达鲁萨兰' },
      { text: 'BO', value: '玻利维亚' },
      { text: 'BR', value: '巴西' },
      { text: 'BS', value: '巴哈马(群岛)' },
      { text: 'BT', value: '不丹' },
      { text: 'BV', value: '布维群岛' },
      { text: 'BW', value: '博茨瓦纳' },
      { text: 'BY', value: '白俄罗斯' },
      { text: 'BZ', value: '伯利兹' },
      { text: 'CA', value: '加拿大' },
      { text: 'CC', value: '库科纳群岛' },
      { text: 'CD', value: '刚果民主共和国' },
      { text: 'CF', value: '中非共和国' },
      { text: 'CG', value: '刚果共和国' },
      { text: 'CH', value: '瑞士' },
      { text: 'CI', value: '象牙海岸' },
      { text: 'CK', value: '科克群岛' },
      { text: 'CL', value: '智利' },
      { text: 'CM', value: '喀麦隆' },
      { text: 'CO', value: '哥伦比亚' },
      { text: 'CR', value: '哥斯答黎加' },
      { text: 'CS', value: '塞尔维亚/Monten' },
      { text: 'CU', value: '古巴' },
      { text: 'CV', value: '佛得角群岛' },
      { text: 'CX', value: '圣诞岛' },
      { text: 'CY', value: '塞浦路斯' },
      { text: 'CZ', value: '捷克共和国' },
      { text: 'GY', value: '圭亚那' },
      { text: 'HM', value: '荷德/马克多纳岛' },
      { text: 'HN', value: '洪都拉斯' },
      { text: 'HR', value: '克罗地亚' },
      { text: 'HT', value: '海地' },
      { text: 'HU', value: '匈牙利' },
      { text: 'IE', value: '爱尔兰' },
      { text: 'IL', value: '以色列' },
      { text: 'IN', value: '印度' },
      { text: 'IO', value: '英属印度洋区' },
      { text: 'IQ', value: '伊拉克' },
      { text: 'IR', value: '伊朗' },
      { text: 'IS', value: '冰岛' },
      { text: 'IT', value: '意大利' },
      { text: 'JM', value: '牙买加' },
      { text: 'JO', value: '约旦' },
      { text: 'KE', value: '肯尼亚' },
      { text: 'KG', value: '吉尔吉斯斯坦' },
      { text: 'KI', value: '基里巴斯' },
      { text: 'KM', value: '科摩罗群岛' },
      { text: 'KN', value: '圣基茨和那维斯' },
      { text: 'KP', value: '北朝鲜' },
      { text: 'KW', value: '科威特' },
      { text: 'KY', value: '开曼群岛' },
      { text: 'KZ', value: '哈萨克斯坦' },
      { text: 'LA', value: '老挝' },
      { text: 'LB', value: '黎巴嫩' },
      { text: 'LC', value: '圣路西亚' },
      { text: 'LI', value: '列支敦士登' },
      { text: 'LK', value: '斯里兰卡' },
      { text: 'LR', value: '利比里亚' },
      { text: 'LS', value: '莱索托' },
      { text: 'LT', value: '立陶宛' },
      { text: 'LU', value: '卢森堡' },
      { text: 'LV', value: '拉脱维亚' },
      { text: 'LY', value: '利比亚' },
      { text: 'MA', value: '摩洛哥' },
      { text: 'MC', value: '摩纳哥' },
      { text: 'MD', value: '摩尔多瓦' },
      { text: 'MG', value: '马达加斯加' },
      { text: 'MH', value: '马绍尔群岛' },
      { text: 'MK', value: '马其顿' },
      { text: 'ML', value: '马里' },
      { text: 'MM', value: '缅甸' },
      { text: 'MN', value: '蒙古' },
      { text: 'MO', value: '中国澳门' },
      { text: 'MP', value: '北马里亚纳群岛' },
      { text: 'MQ', value: '马提尼克' },
      { text: 'MR', value: '毛里塔尼亚' },
      { text: 'MS', value: '蒙塞拉特岛' },
      { text: 'MT', value: '马耳他' },
      { text: 'MU', value: '毛里求斯' },
      { text: 'MV', value: '马尔代夫' },
      { text: 'MW', value: '马拉维' },
      { text: 'MX', value: '墨西哥' },
      { text: 'MZ', value: '莫桑比克' },
      { text: 'NA', value: '纳米比亚' },
      { text: 'NC', value: '新喀里多尼亚' },
      { text: 'NE', value: '尼日尔' },
      { text: 'NF', value: '诺福克岛' },
      { text: 'NG', value: '尼日利亚' },
      { text: 'NI', value: '尼加拉瓜' },
      { text: 'NL', value: '荷 兰' },
      { text: 'NO', value: '挪威' },
      { text: 'NP', value: '尼泊尔' },
      { text: 'NR', value: '瑙鲁' },
      { text: 'DE', value: '德国' },
      { text: 'NT', value: 'NATO' },
      { text: 'DJ', value: '吉布提' },
      { text: 'NU', value: '纽埃群岛' },
      { text: 'DK', value: '丹麦' },
      { text: 'NZ', value: '新西兰' },
      { text: 'DM', value: '多米尼加' },
      { text: 'OM', value: '阿曼' },
      { text: 'DO', value: '多米尼加共和国' },
      { text: 'OR', value: '橙色' },
      { text: 'DZ', value: '阿尔及利亚' },
      { text: 'PA', value: '巴拿马' },
      { text: 'EC', value: '厄瓜多尔' },
      { text: 'PE', value: '秘鲁' },
      { text: 'EE', value: '爱沙尼亚' },
      { text: 'PF', value: '法属波利尼西亚' },
      { text: 'EG', value: '埃及' },
      { text: 'PG', value: '巴布亚新几内亚' },
      { text: 'EH', value: '撒哈拉西部' },
      { text: 'PK', value: '巴基斯坦' },
      { text: 'ER', value: '埃立特里亚' },
      { text: 'PL', value: '波兰' },
      { text: 'ES', value: '西班牙' },
      { text: 'PM', value: 'St.Pier,Miquel.' },
      { text: 'ET', value: '埃塞俄比亚' },
      { text: 'PN', value: '皮特肯岛' },
      { text: 'EU', value: '欧盟' },
      { text: 'PR', value: '波多黎哥' },
      { text: 'FI', value: '芬兰' },
      { text: 'PS', value: '巴基斯坦' },
      { text: 'FJ', value: '斐济' },
      { text: 'PT', value: '葡萄牙' },
      { text: 'FK', value: '福克兰群岛' },
      { text: 'PW', value: '帕劳' },
      { text: 'FM', value: '密克罗尼西亚' },
      { text: 'PY', value: '巴拉圭' },
      { text: 'FO', value: '法罗群岛' },
      { text: 'QA', value: '卡塔尔' },
      { text: 'FR', value: '法国' },
      { text: 'RE', value: '统一' },
      { text: 'GA', value: '加蓬' },
      { text: 'RO', value: '罗马尼亚' },
      { text: 'GB', value: '英国' },
      { text: 'RU', value: '俄罗斯联邦' },
      { text: 'GD', value: '格林纳达' },
      { text: 'RW', value: '卢旺达' },
      { text: 'GE', value: '乔治亚' },
      { text: 'SA', value: '沙特阿拉伯' },
      { text: 'GF', value: '法属圭亚那' },
      { text: 'SB', value: '所罗门群岛' },
      { text: 'GH', value: '加纳' },
      { text: 'SC', value: '塞舌尔群岛' },
      { text: 'GI', value: '直布罗陀' },
      { text: 'SD', value: '苏丹' },
      { text: 'GL', value: '格陵兰岛' },
      { text: 'SE', value: '瑞典' },
      { text: 'GM', value: '冈比亚' },
      { text: 'SH', value: '圣赫勒拿岛' },
      { text: 'GN', value: '几内亚' },
      { text: 'SI', value: '斯洛文尼亚' },
      { text: 'GP', value: '瓜达洛普' },
      { text: 'SJ', value: '皮特肯岛' },
      { text: 'GQ', value: '赤道几内亚' },
      { text: 'SK', value: '斯洛伐克' },
      { text: 'GR', value: '希腊' },
      { text: 'SL', value: '塞拉利昂' },
      { text: 'GS', value: '南三维治岛' },
      { text: 'SG', value: '新加坡' },
      { text: 'GT', value: '危地马拉' },
      { text: 'GU', value: '关岛' },
      { text: 'GW', value: '几内亚比绍' },
      { text: 'SG', value: '新加坡' },
      { text: 'VA', value: '梵蒂冈城' },
      { text: 'VC', value: '新加坡' },
      { text: 'VG', value: '英属维尔京群岛' },
      { text: 'VI', value: '美属维尔京群岛' },
      { text: 'VU', value: '瓦努阿图' },
      { text: 'WF', value: '瓦利斯/富图纳岛' },
      { text: 'WS', value: '萨摩亚' },
      { text: 'YE', value: '也门' },
      { text: 'YT', value: '马约特岛' },
      { text: 'ZA', value: '南非' },
      { text: 'ZM', value: '赞比亚' },
      { text: 'ZW', value: '津巴布韦' },
      { text: 'SM', value: '圣马力诺' },
      { text: 'SN', value: '塞内加尔' },
      { text: 'SO', value: '索马里' },
      { text: 'SR', value: '苏里南' },
      { text: 'ST', value: '圣多马/普林西比' },
      { text: 'SV', value: '萨尔瓦多' },
      { text: 'SY', value: '叙利亚' },
      { text: 'SZ', value: '斯威士兰' },
      { text: 'TC', value: '特克斯凯科斯' },
      { text: 'TD', value: '乍得' },
      { text: 'TF', value: '法国南部地区' },
      { text: 'TG', value: '多哥' },
      { text: 'TJ', value: '塔吉克斯坦' },
      { text: 'TK', value: '托客劳群岛' },
      { text: 'TL', value: '东帝汶岛' },
      { text: 'TM', value: '土库曼斯坦' },
      { text: 'TN', value: '突尼斯' },
      { text: 'TO', value: '汤加' },
      { text: 'TP', value: '东帝汶岛' },
      { text: 'TR', value: '土耳其' },
      { text: 'TT', value: '特立尼达/多巴哥' },
      { text: 'TV', value: '图瓦卢' },
      { text: 'TZ', value: '坦桑尼亚' },
      { text: 'UA', value: '乌克兰' },
      { text: 'UG', value: '乌干达' },
      { text: 'UM', value: '小奥特兰群岛' },
      { text: 'UN', value: '联合国' },
      { text: 'US', value: '美国' },
      { text: 'UY', value: '乌拉圭' },
      { text: 'UZ', value: '乌兹别克斯坦' },
    ]
  },
  computed: {
    // isEdit() {
    //   return (
    //     this.$route.params.id !== 'new' &&
    //     this.detail.status !== null &&
    //     this.detail.status !== '' &&
    //     this.detail.status !== '0' &&
    //     this.detail.status !== '1' &&
    //     this.detail.status !== '4'
    //   )
    // },
    isEdit() {
      return (
        this.$route.params.id !== 'new' &&
        this.detail.businessStatus !== null &&
        this.detail.businessStatus !== '' &&
        this.detail.businessStatus !== '草稿' &&
        this.detail.businessStatus !== '退回（一阶段）' &&
        this.detail.businessStatus !== '退回（二阶段）' &&
        this.detail.businessStatus !== '补充合同附件'
      )
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
    payCom() {
      return this.payComs.length > 0 ? this.payComs : false
    },
  },
  data() {
    return {
      detail: {
        applicationNo: '',
        attachmentIds: [],
      },
      select: false,
      dialog: false,
      searchObj: {},
      rules: {
        required: (v) => !!v || v === false || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        notAboveTwenty: (v) => parseInt(v) <= 20 || '数字不能超过20',
      },
      panel: [0, 1, 2, 3, 4, 5, 6, 7], //根据需要调整panel数组中的值，以控制哪些面板是展开的
      form1: {},
      formShow1: false,
      form2: {},
      formShow2: false,
      supplierPurchaserList: [],
      supplierAssessItemPayList: [],
      payComs: false,
      bank: false,
      isNew: false,
      supplierInOutScoreModifyDTOList: [],
      supplierInOutItem: { recommendGroup: '程军剑、李瑞民、张海玉、纪文光' },
      saveStatus: true,
      sendloading: false,
    }
  },
  watch: {
    // '$store.state.reportParams.businessParams': {
    //   handler: function (val) {
    //     console.log(val)
    //     // if (this.isEdit) return
    //     const businessParam = val.find(
    //       (item) =>
    //         item.businessType === 'supplierAssess_item' &&
    //         item.businessId === this.$route.params.buniessId &&
    //         item.id === this.$route.params.buniessId,
    //     )
    //     if (!!businessParam && businessParam.businessId) {
    //       console.log(
    //         businessParam.businessType + ' ' + businessParam.businessId,
    //       )
    //       console.log(businessParam)
    //       // this.backRouteName = 'security-check-detail-new'
    //       this.businessParam = businessParam
    //       // this.detail.type = businessParam.otherParams.type
    //       // this.deptReportInspection.businessId = businessParam.businessId
    //       // this.deptReportInspection.shipInfoDO.shipCode =
    //       //   businessParam.otherParams.shipCode
    //       // this.deptReportInspection.type =
    //       //   businessParam.otherParams.type == '3' ? '1' : '0'
    //       // this.detail.seaJson = { ...businessParam.otherParams }
    //       // this.detail.shipJson = { ...businessParam.otherParams }
    //     }
    //   },
    //   deep: true,
    //   immediate: true,
    // },
    // 'detail.paymentType': {
    //   handler: function (val) {
    //     console.log(val)
    //     if ('1' == val) this.loadItemPayList()
    //   },
    // },
    'detail.supplierAssessItemOutputDTO.myCompany': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.myCompany')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.myCompanySap = val
      },
    },
    'detail.supplierAssessItemOutputDTO.payer': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.payer')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.sapcode = val
      },
    },
    'detail.supplierAssessItemOutputDTO.bankCountrySap': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.bankCountrySap')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.bankCountryName = val
      },
    },
    'detail.supplierAssessItemOutputDTO.bankCountrySap1': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.bankCountrySap1')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.bankCountryName1 = val
      },
    },
    'detail.supplierAssessItemOutputDTO.accountCurr': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.accountCurr')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.accountCurrName = val
      },
    },
    'detail.supplierAssessItemOutputDTO.accountCurr1': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.accountCurr1')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.accountCurrName1 = val
      },
    },
    'detail.supplierAssessItemOutputDTO.aCountrySap': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.aCountrySap')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.aCountryName = val
      },
    },
    'detail.supplierAssessItemOutputDTO.bcountrySap': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.bcountrySap')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.bcountryName = val
      },
    },
    'detail.supplierAssessItemOutputDTO.paymentDay': {
      handler: function (val) {
        console.log('detail.supplierAssessItemOutputDTO.paymentDay')
        console.log(val)
        this.detail.supplierAssessItemOutputDTO.paymentDays = val
      },
    },
  },

  methods: {
    // async save(goBack) {
    //   goBack()
    // },
    // async submit(goBack) {
    //   goBack()
    // },

    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    changeAttachment2(attachmentIds2) {
      this.detail.attachmentIds2 = attachmentIds2
    },
    editSupplierP() {
      this.form2 = { ...this.payComs }
      this.formShow2 = true
    },
    addPayCom() {
      this.form2 = {
        ...this.payComs,
        supplierAssessItemId: this.detail.id,
      }
      this.formShow2 = true
    },
    async addCom(newCom) {
      console.log(newCom)
      await this.loadItemPayList()
      this.detail.total = 0
      this.supplierAssessItemPayList.forEach((s) => {
        if (s.amount != null && s.amount !== '') {
          // 检查对象a是否包含supplierBankListOutputDTOS数组
          this.detail.total += s.amount
          // if (!s.supplierBankListOutputDTOS) {
          //   // 如果不包含，赋值一个空数组
          //   s.supplierBankListOutputDTOS = []
          // }
          // s.supplierBankListOutputDTOS.push(newBank)
        }
      })
      this.payComs = false
      // if (
      //   this.supplierPurchaserList.some(
      //     (s) => s.paymentCompany === newCom.paymentCompany,
      //   )
      // ) {
      //   this.$dialog.message.error('付款公司重复')
      //   return
      // }
      // this.supplierPurchaserList.push(newCom)
      // console.log(this.supplierPurchaserList)
    },
    // addBank(newBank) {
    //   console.log(newBank)
    //   // if (
    //   //   this.supplierPurchaserList.some(
    //   //     (s) => s.paymentCompany === newBank.paymentCompany,
    //   //   )
    //   // ) {
    //   //   this.supplierPurchaserList.supplierBankListOutputDTOS.push(newBank)
    //   // }
    //   // 假设this.supplierPurchaserList是已经定义好的数组
    //   this.supplierPurchaserList.forEach((s) => {
    //     if (s.paymentCompany === newBank.paymentCompany) {
    //       // 检查对象a是否包含supplierBankListOutputDTOS数组
    //       if (!s.supplierBankListOutputDTOS) {
    //         // 如果不包含，赋值一个空数组
    //         s.supplierBankListOutputDTOS = []
    //       }
    //       s.supplierBankListOutputDTOS.push(newBank)
    //     }
    //   })
    //   console.log('1213ad', this.supplierPurchaserList)
    // },
    async delSupplierP() {
      // this.supplierPurchaserList = this.supplierPurchaserList.filter(
      //   (s) => !(s.id === this.payCom.id),
      // )
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/SupplierAssess/assessItemPayDelete',
        { id: this.payComs.id },
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadItemPayList()
        this.payComs = false
        this.detail.total = 0
        this.supplierAssessItemPayList.forEach((s) => {
          if (s.amount != null && s.amount !== '') {
            // 检查对象a是否包含supplierBankListOutputDTOS数组
            this.detail.total += s.amount
            // if (!s.supplierBankListOutputDTOS) {
            //   // 如果不包含，赋值一个空数组
            //   s.supplierBankListOutputDTOS = []
            // }
            // s.supplierBankListOutputDTOS.push(newBank)
          }
        })
      }
    },
    // editSupplierBank() {
    //   this.form1 = { ...this.bank }
    //   this.formShow1 = true
    // },
    // addSupplierBank() {
    //   this.form1 = {
    //     supplierId: this.$route.params.id,
    //     purchaserId: this.payCom.id,
    //     paymentCompany: this.payCom.paymentCompany,
    //   }
    //   this.formShow1 = true
    // },
    // getCompWithOperation() {
    //   const ids = this.supplierPurchaserList.map((i) => i.id)
    //   const delList = this.isEdit
    //     ? this.detail.supplierPurchaserList
    //         .filter((i) => !ids.includes(i.id))
    //         .map((i) => {
    //           return { ...i, operationType: 3 }
    //         })
    //     : []
    //   const others = this.supplierPurchaserList.map((i) => {
    //     return { ...i, operationType: i.id ? 2 : 1 }
    //   })
    //   others.forEach((s) => {
    //     if ('supplierBankListOutputDTOS' in s) {
    //       // s包含supplierBankListOutputDTOS属性
    //       s.supplierBankListOutputDTOS = s.supplierBankListOutputDTOS.map(
    //         (i) => {
    //           return { ...i, operationType: i.id ? 2 : 1 }
    //         },
    //       )
    //     } else {
    //       // s不包含supplierBankListOutputDTOS属性
    //       this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
    //     }
    //     // s.supplierBankListOutputDTOS = s.supplierBankListOutputDTOS.map((i) => {
    //     //   return { ...i, operationType: i.id ? 2 : 1 }
    //     // })
    //   })
    //   return [...delList, ...others]
    // },
    async save(goBack, notMove = false) {
      console.log(this.id)
      console.log(this.buniessId)
      console.log(this.status)
      // if (!this.$refs.form.validate()) {
      //   return false
      // }
      // if (this.supplierPurchaserList.length === 0) {
      //   this.$dialog.message.warning('请填写结算信息')
      //   return false
      // }
      // const detailList = this.getCompWithOperation()
      this.saveStatus = true
      // detailList.forEach((s) => {
      //   if ('supplierBankListOutputDTOS' in s) {
      //     // s包含supplierBankListOutputDTOS属性
      //     if (s.supplierBankListOutputDTOS.length <= 0) {
      //       this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
      //       this.saveStatus = false
      //     }
      //   } else {
      //     // s不包含supplierBankListOutputDTOS属性
      //     this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
      //     this.saveStatus = false
      //   }
      // })
      if (!this.saveStatus) {
        return
      }
      // const supplierInOutScoreModifyDTOList =
      //   this.supplierInOutScoreModifyDTOList
      // this.supplierInOutScoreModifyDTOList.forEach((item) => {
      //   item.total =
      //     item.prodeptNum +
      //     item.saledeptNum +
      //     item.lastYearSales +
      //     item.productIntroduction +
      //     item.productAdvantages +
      //     item.clients +
      //     item.marketShare
      // })
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/SupplierAssess/assessSaveOrUpdate',
        {
          ...this.detail,
          // supplierAssessId: this.buniessId,
          supplierAssessItemModifyDTO: this.detail.supplierAssessItemOutputDTO,
          // supplierInOutItem: this.supplierInOutItem,
          // supplierPurchaserList: [...detailList],
          // supplierInOutScoreModifyDTOList: [
          //   ...this.supplierInOutScoreModifyDTOList,
          // ],
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      // console.log(this.businessParam)
      // if (this.businessParam) {
      //   this.$store.commit('setReportId', {
      //     ...this.businessParam,
      //     reportId: '1',
      //   })
      //   this.closeAndTo('supplier-assess-detail', {
      //     id: this.businessParam.businessId,
      //   })
      // }
      if (this.isNew) goBack()
    },
    // async submit(goBack) {
    //   if (!(this.$refs?.aform?.validate() ?? true)) return
    //   const data = await this.save(goBack, true)
    //   if (!data) {
    //     return
    //   } else {
    //     if (!this.detail.auditParams) {
    //       const { errorRaw } = await this.getAsync(
    //         '/business/shipAffairs/supplierInOut/supplierInOutSubmit',
    //         { applyId: data },
    //       )
    //       if (!errorRaw) goBack()
    //     } else {
    //       const error = await this.$refs.audit.submit()
    //       if (!error) goBack()
    //     }
    //   }
    // },
    async sendOA(goBack) {
      this.sendloading = true
      if (!(this.$refs?.form?.validate() ?? true)) {
        this.$dialog.message.warning('请填写基本信息')
        this.sendloading = false
        return
      }
      if (!(this.$refs?.form1?.validate() ?? true)) {
        this.$dialog.message.warning('请填写签约信息')
        this.sendloading = false
        return
      }
      if (!(this.$refs?.form2?.validate() ?? true)) {
        this.$dialog.message.warning('请填写评估信息')
        this.sendloading = false
        return
      }
      console.log(this.detail.supplierAssessItemOutputDTO.benefiAccount)
      if (!(this.$refs?.form3?.validate() ?? true)) {
        this.$dialog.message.warning('请填写结算信息')
        this.sendloading = false
        return
      }
      if (!(this.$refs?.form4?.validate() ?? true)) {
        this.$dialog.message.warning('请填写有效期信息')
        this.sendloading = false
        return
      }
      if (
        (this.detail.ctype == '1' || this.detail.ctype == '2') &&
        this.detail.businessStatus == '补充合同附件' &&
        (!this.detail.attachmentIds2 || this.detail.attachmentIds2 == '')
      ) {
        this.$dialog.message.warning('请上传评审合同附件')
        this.sendloading = false
        return
      }
      // this.$dialog.message.warning('发送OA成功')
      if (
        this.detail.supplierAssessItemOutputDTO.benefiAccount == '' &&
        this.detail.supplierAssessItemOutputDTO.benefiAccount1 == '' &&
        this.detail.supplierAssessItemOutputDTO.nosignreason == ''
      ) {
        this.$dialog.message.warning('请填写未维护结算信息的原因')
        this.sendloading = false
        return
      }
      /**
      if (
        ((this.detail.supplierAssessItemOutputDTO.benefiAccount != '' &&
          this.detail.supplierAssessItemOutputDTO.bcountrySap !=
            this.detail.supplierAssessItemOutputDTO.bankCountrySap) ||
          (this.detail.supplierAssessItemOutputDTO.benefiAccount1 != '' &&
            this.detail.supplierAssessItemOutputDTO.bcountrySap !=
              this.detail.supplierAssessItemOutputDTO.bankCountrySap1)) &&
        this.detail.supplierAssessItemOutputDTO.countrySapReason == ''
      ) {
        this.$dialog.message.warning(
          '请填写收款人注册地和其开户行注册地不一致原因',
        )
        return
      }
      if (
        this.detail.supplierAssessItemOutputDTO.projectName !=
          this.detail.supplierAssessItemOutputDTO.beneficiary &&
        this.detail.supplierAssessItemOutputDTO.nameReason == ''
      ) {
        this.$dialog.message.warning('请填写供应商名称和收款人名称不一致原因')
        return
      }
      if (
        ((this.detail.supplierAssessItemOutputDTO.benefiAccount != '' &&
          this.detail.supplierAssessItemOutputDTO.bcountrySap !=
            this.detail.supplierAssessItemOutputDTO.bankCountrySap) ||
          (this.detail.supplierAssessItemOutputDTO.benefiAccount1 != '' &&
            this.detail.supplierAssessItemOutputDTO.bcountrySap !=
              this.detail.supplierAssessItemOutputDTO.bankCountrySap1)) &&
        this.detail.supplierAssessItemOutputDTO.bcountrySap == ''
      ) {
        this.$dialog.message.warning('请填写收款人开户银行代码')
        return
      }
      if (
        this.detail.supplierAssessItemOutputDTO.benefiAccount != '' &&
        this.detail.supplierAssessItemOutputDTO.benefiAccount1 != '' &&
        this.detail.supplierAssessItemOutputDTO.accountCurr ==
          this.detail.supplierAssessItemOutputDTO.accountCurr1 &&
        (this.detail.supplierAssessItemOutputDTO.usageDescription == '' ||
          this.detail.supplierAssessItemOutputDTO.usageDescription1 == '')
      ) {
        this.$dialog.message.warning('请填写用户账户用途说明')
        return
      }
       */
      // let test = true
      // if (test) return
      const data = await this.save(goBack, true)
      console.log(data)
      if (!data) {
        this.sendloading = false
        return
      } else {
        // if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/SupplierAssess/sendOA',
          { id: this.detail.id },
        )
        this.loadDetail()
        if (!errorRaw) {
          this.sendloading = false
          this.$dialog.message.success('发送成功')
          goBack()
        }
        // } else {
        //   const error = await this.$refs.audit.submit()
        //   if (!error) goBack()
        // }
      }
      this.sendloading = false
    },
    async loadDetail() {
      // if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/SupplierAssess/getById',
        { id: this.$route.params.id },
      )
      this.detail = { ...data }
      this.detail.multiOffice = JSON.parse(this.detail.multiOffice) //转换多选项处理
      if (this.$route.params.id == 'new') {
        this.detail.ctype = this.$route.params.ctype
        this.detail.multiType = '2'
        this.isNew = true
      }
      // this.supplierInOutItem = data.supplierInOutItem
      // this.supplierInOutItem = data.supplierInOutItem
      // this.supplierInOutScoreModifyDTOList = [
      //   ...data.supplierInOutScoreModifyDTOList,
      // ]
      // this.supplierPurchaserList = data.supplierPurchaserList.map((s) => {
      //   return { ...s, vid: s.id, operationType: 0 }
      // })
      // this.supplierPurchaserList.forEach((s) => {
      //   s.supplierBankListOutputDTOS.forEach((a) => {
      //     a.operationType = 0
      //   })
      // })
      // if (this.detail.applyType != 2 && this.detail.status == 2) {
      //   this.panel = [0, 5, 7]
      //   this.$dialog.message.error('请在评级打分模块填写评分后再提交！')
      // }
      // if (this.detail.paymentType == '1') this.loadItemPayList()
    },
    async loadItemPayList() {
      if (!this.detail.id || this.detail.id == '')
        this.detail.id = this.$route.params.id
      // setTimeout(() => {
      //   const { data } = this.getAsync(
      //     '/business/shipAffairs/SupplierAssess/pageItemPayList',
      //     { id: this.detail.id },
      //   )
      //   this.supplierAssessItemPayList = data
      // }, 500)
      const { data } = await this.getAsync(
        '/business/shipAffairs/SupplierAssess/pageItemPayList',
        { id: this.detail.id },
      )
      this.supplierAssessItemPayList = data
    },
    // async loadDetailByType() {
    //   const { data } = await this.getAsync(
    //     '/business/shipAffairs/supplierInOut/supplierInOutDetailByApplyType',
    //     {
    //       applyType: this.$route.params.applyType,
    //       supplierId: this.$route.params.supplierId,
    //     },
    //   )
    //   this.detail = { ...data }
    //   this.supplierInOutItem = data.supplierInOutItem
    //   this.supplierInOutScoreModifyDTOList = [
    //     ...data.supplierInOutScoreModifyDTOList,
    //   ]
    //   this.supplierPurchaserList = data.supplierPurchaserList.map((s) => {
    //     return { ...s, vid: s.id, operationType: 0 }
    //   })
    //   this.supplierPurchaserList.forEach((s) => {
    //     s.supplierBankListOutputDTOS.forEach((a) => {
    //       a.operationType = 0
    //     })
    //   })
    //   this.supplierInOutItem.recommendGroup = '程军剑、李瑞民、张海玉、纪文光'
    // },
  },

  mounted() {
    // if (this.$route.params.applyType != undefined) {
    //   // console.log(1)
    //   this.detail.applyType = this.$route.params.applyType
    //   // 退出
    //   if (this.detail.applyType != 1) {
    //     this.loadDetailByType()
    //   }
    // }
    // if (this.detail.applyType == 2) {
    //   this.panel = [0, 1, 2, 3, 4, 5, 6]
    // }
    this.loadDetail()
    // this.loadItemPayList()
  },
}
</script>

<style></style>
