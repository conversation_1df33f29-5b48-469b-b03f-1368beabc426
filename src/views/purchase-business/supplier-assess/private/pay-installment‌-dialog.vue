<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        分期付款
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <!-- <v-col cols="12" md="2">
                <vs-date-picker
                  v-model="formData.beginDate"
                  label="有效期-开始"
                  :rules="[rules.required]"
                  required
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  v-model="formData.endDate"
                  label="有效期-结束"
                  :rules="[rules.required]"
                  required
                ></vs-date-picker>
              </v-col>  -->
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="formData.amount"
                  label="金额"
                  type="number"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <!-- <v-text-field
                  v-model="formData.currency"
                  label="币别"
                  :rules="[rules.required]"
                  required
                ></v-text-field> -->
                <v-select
                  v-model="formData.currency"
                  :items="[
                    { text: '-', value: '-' },
                    { text: 'CNY', value: '人民币' },
                    { text: 'RUB', value: '俄罗斯卢布' },
                    {
                      text: 'ZAR',
                      value: '南非兰得',
                    },
                    {
                      text: 'IDR',
                      value: '印尼盾',
                    },
                  ]"
                  label="币别"
                  dense
                  :readonly="isEdit"
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="formData.term"
                  label="发起付款条件"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-select
                  v-model="formData.paymentCompany"
                  label="付款公司"
                  :rules="[rules.required]"
                  required
                  :items="payComs"
                ></v-select>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-select
                  v-model="formData.status"
                  label="状态"
                  :rules="[rules.required]"
                  required
                  :items="statuses"
                ></v-select>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  v-model="formData.supplierType"
                  label="供应商类型"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col> -->
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'pay-installment‌-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.statuses = [
      { text: '有效', value: 0 },
      { text: '暂停整顿', value: 1 },
      { text: '冻结', value: 2 },
      { text: '黑名单', value: 3 },
    ]
    this.payComs = ['3000', '3800', '8903', '3402']
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        email: (v) => /.+@.+\..+/.test(v) || '请输入有效的邮箱',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    // async save() {
    //   if (!this.$refs.form.validate()) {
    //     return
    //   }
    //   this.$emit('change', false)
    //   this.$emit('success', this.formData)
    // },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/shipAffairs/SupplierAssess/assessItemPaySaveOrUpdate'
        : '/business/shipAffairs/SupplierAssess/assessItemPaySaveOrUpdate'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        // supplierAssessItemId: this.buniessItemId,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
