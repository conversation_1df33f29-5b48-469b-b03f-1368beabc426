<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['供应商评审合同详细信息:编辑']"
      :title="`供应商评审合同详细信息-${
        isEdit ? detail.applicationNo : '新增'
      }`"
      :tooltip="isEdit ? detail.applicationNo : '新增'"
      :subtitles="subtitles"
    >
      <template v-slot:titlebtns>
        <v-btn
          v-if="false"
          width="90"
          tile
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          class="mx-1"
        >
          返回列表
        </v-btn>
        <v-btn
          v-if="!isEdit"
          width="60"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          :loading="loading"
        >
          保存
        </v-btn>
      </template>
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <v-btn
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: detail.systemReportId },
          }"
          color="info"
          small
          class="mx-1"
          v-permission="['备件申请:查看部门报表']"
        >
          查看部门报表
        </v-btn> -->
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <v-card-text>
        <v-expansion-panels multiple accordion v-model="panel" focusable>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              基本信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form">
                  <v-row>
                    <!-- <v-col cols="12" md="3" class="py-0">
                      <span style="font-size: 18px">签署协议</span>
                    </v-col> -->
                    <v-col cols="12" md="9" class="py-0">
                      <!-- &nbsp;&nbsp;&nbsp;&nbsp; -->
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label=" 签署协议"
                        class="pb-0"
                        v-model="detail.isSign"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="2"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.isSign"
                        :items="[
                          { text: '是', value: 1 },
                          { text: '否', value: 2 },
                        ]"
                        label="签署协议"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        required
                        outlined
                      ></v-select> -->
                    </v-col>
                    <!-- <v-col cols="12" class="py-0" v-if="detail.applyType != 1">
                      <v-textarea
                        v-model="detail.remark"
                        :label="detail.applyType == 2 ? '退出说明' : '评级说明'"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.required]"
                        height="50px"
                      ></v-textarea>
                    </v-col> -->
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label="外部个人供应商"
                        class="pb-0"
                        v-model="detail.personalSupplier"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="2"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.personalSupplier"
                        :items="[
                          { text: '是', value: 1 },
                          { text: '否', value: 2 },
                        ]"
                        label="外部个人供应商"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        required
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label="供应商类型细类"
                        class="pb-0"
                        v-model="detail.purchaseType"
                        row
                      >
                        <v-radio label="船舶购置" value="S1"></v-radio>
                        <v-radio label="集装箱购置" value="S4"></v-radio>
                        <v-radio label="土地购置" value="S3"></v-radio>
                        <v-radio label="房产购置" value="S2"></v-radio>
                        <v-radio label="其他" value="S5"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.purchaseType"
                        :items="[
                          { text: '船舶购置', value: 'S1' },
                          { text: '集装箱购置', value: 'S4' },
                          { text: '土地购置', value: 'S3' },
                          {
                            text: '房产购置',
                            value: 'S2',
                          },
                          {
                            text: '其他',
                            value: 'S5',
                          },
                        ]"
                        label="供应商类型细类"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        required
                        outlined
                      ></v-select> -->
                    </v-col>
                    <!-- <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.applyType"
                        :items="[
                          { text: '是', value: 1 },
                          { text: '否', value: 2 },
                        ]"
                        label="是否批量评审"
                        dense
                        :rules="[rules.required]"
                        readonly
                        required
                        outlined
                      ></v-select>
                    </v-col> -->
                  </v-row>
                  <v-row>
                    <!-- <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.applyType"
                        :items="[
                          { text: '准入', value: 1 },
                          { text: '退出', value: 2 },
                          { text: '评级', value: 3 },
                        ]"
                        label="评审所属公司"
                        dense
                        :rules="[rules.required]"
                        readonly
                        required
                        outlined
                      ></v-select>
                    </v-col> -->
                    <v-col cols="12" md="12" class="py-0">
                      <v-text-field
                        label="合同名称"
                        v-model="detail.contractName"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="12" class="py-0">
                      <v-textarea
                        label="供应商基本信息"
                        v-model="detail.supplierInfo"
                        dense
                        :rules="[rules.required]"
                        required
                        :readonly="isEdit"
                        outlined
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              签约信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
              <v-card-text>
                <v-form ref="form">
                  <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
                  <!-- <v-row>
                    <v-col cols="12" md="3" class="py-0"></v-col>
                    <v-col cols="12" md="4" class="py-0">他方签约公司</v-col>
                    <v-col cols="12" md="5" class="py-0">我方签约公司</v-col>
                  </v-row> -->
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">他方签约公司</span>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <!-- <vs-date-picker
                        :readonly="isEdit"
                        outlined
                        dense
                        v-model="detail.projectName"
                        use-today
                        label="公司注册全称"
                        :rules="[rules.required]"
                      ></vs-date-picker> -->
                      <v-text-field
                        label="公司注册全称(他方签约公司)"
                        v-model="detail.projectName"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <!-- <v-col cols="12" md="6" class="py-0"></v-col> -->
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="供应商税号(他方签约公司)"
                        v-model="detail.suppliertaxcode"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="detail.applyPurpose"
                  label="申请目的"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col> -->
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.supplierCode"
                        label="供应商业务系统编码(他方签约公司)"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.required]"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.projectNameSap"
                        label="SAP代码(他方签约公司)"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.required]"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0"></v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.bcountrysap"
                        :items="[
                          { text: '-', value: '-' },
                          { text: 'CN', value: '中国' },
                          { text: 'HK', value: '中国香港' },
                          {
                            text: 'ID',
                            value: '印度尼西亚',
                          },
                          {
                            text: 'JP',
                            value: '日本',
                          },
                        ]"
                        label="注册国家地区代码(他方签约公司)"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.bcountryname"
                        label="注册国家地区名称(他方签约公司)"
                        dense
                        outlined
                        :rules="[rules.required]"
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">我方签约公司</span>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <!-- <vs-date-picker
                        :readonly="isEdit"
                        outlined
                        dense
                        v-model="detail.projectName"
                        use-today
                        label="公司注册全称"
                        :rules="[rules.required]"
                      ></vs-date-picker> -->
                      <v-select
                        v-model="detail.myCompany"
                        :items="[
                          {
                            text: 'SITC Shipping Group Company Limited',
                            value: '1000',
                          },
                          {
                            text: 'SITC Shipping Company Limited',
                            value: '1001',
                          },
                          {
                            text: 'SITC SHIPOWNING GROUP COMPANY LIMITED',
                            value: '3000',
                          },
                          {
                            text: 'SITC Development Company Limited',
                            value: '3001',
                          },
                          {
                            text: 'New SITC Development Company Limited',
                            value: '3002',
                          },
                        ]"
                        label="公司注册全称(我方签约公司)"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0"></v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.myCompanySap"
                        label="SAP代码(我方签约公司)"
                        :readonly="isEdit"
                        dense
                        outlined
                        :rules="[rules.required]"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0"></v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.acountrysap"
                        :items="[
                          { text: '-', value: '-' },
                          { text: 'CN', value: '中国' },
                          { text: 'HK', value: '中国香港' },
                          {
                            text: 'ID',
                            value: '印度尼西亚',
                          },
                          {
                            text: 'JP',
                            value: '日本',
                          },
                        ]"
                        label="注册国家地区代码(他方签约公司)"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.acountryname"
                        label="注册国家地区名称(我方签约公司)"
                        dense
                        outlined
                        :rules="[rules.required]"
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">合同标的金额</span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="合同标的金额"
                        v-model="detail.value"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.waers"
                        :items="[
                          { text: '-', value: '-' },
                          { text: 'CNY', value: '人民币' },
                          { text: 'RUB', value: '俄罗斯卢布' },
                          {
                            text: 'ZAR',
                            value: '南非兰得',
                          },
                          {
                            text: 'IDR',
                            value: '印尼盾',
                          },
                        ]"
                        label="币别"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="单价"
                        v-model="detail.price"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="数量"
                        v-model="detail.num"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              评估信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
              <v-card-text>
                <v-form ref="form">
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label=" 关连交易及关连方认定结果"
                        class="pb-0"
                        v-model="detail.relation"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="2"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.relation"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        label="关连交易及关连方认定结果"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">
                        履约能力评估类型（请填写评估结果说明、扫描件上传证明文件）
                      </span>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.supplierNote1"
                        label="公司注册证明(提供有效期内证照扫描件)"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.supplierNote2"
                        label="经营资质(提供与业务内容相匹配的经营资质或经营许可)"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.supplierNote3"
                        label="信用调查(行业口碑、审计报告、财务报表或其他公开信息)"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.supplierNote5"
                        label="询价与遴选(须提供多家供应商作为遴选对象或多家询价)"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 20px">
                        供应商ESG评估表（本公司根据供应商反馈及实际情况进行以下评估）
                      </span>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 16px">
                        1)遵守国家及当地的法律法规及适用的要求以及商业道德与社会义务；遵守反腐败法律法规；遵守反垄断和反竞争法律法规，杜绝不正当竞争行为。
                      </span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        class="pb-0"
                        v-model="detail.question1"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="-1"></v-radio>
                        <v-radio label="不确定" value="0"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.question1"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <!-- <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="detail.applyPurpose"
                  label="申请目的"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col> -->
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 16px">
                        2)尊重并保护所有员工的基本人权，包括但不限于言论自由、结社自由、平等权利和尊严。
                      </span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        class="pb-0"
                        v-model="detail.question2"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="-1"></v-radio>
                        <v-radio label="不确定" value="0"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.question2"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 16px">
                        3)禁止使用强迫劳动、童工和歧视性雇佣实践。
                      </span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        class="pb-0"
                        v-model="detail.question3"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="-1"></v-radio>
                        <v-radio label="不确定" value="0"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.question3"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 16px">
                        4)提供安全、健康的工作环境以及提供必要的培训和指导。对危险品和废物进行分类管理，确保它们不会对员工的健康和安全造成威胁。
                      </span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        class="pb-0"
                        v-model="detail.question4"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="-1"></v-radio>
                        <v-radio label="不确定" value="0"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.question4"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 16px">
                        5)遵守环境保护法律法规，采取必要的环保及节能措施。
                      </span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        class="pb-0"
                        v-model="detail.question5"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="-1"></v-radio>
                        <v-radio label="不确定" value="0"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.question5"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 16px">
                        6)承诺其业务不对自然栖息地与生物多样性产生负面影响。
                      </span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        class="pb-0"
                        v-model="detail.question6"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="-1"></v-radio>
                        <v-radio label="不确定" value="0"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.question6"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <span style="font-size: 16px">
                        7)该供应商能帮助海丰国际(SITC)提高ESG管理水平，并提供证明。
                      </span>
                      <span style="color: red; font-size: 14px">
                        （证明文件包括但不限于ESG报告、第三方机构评分等级、环评报告、质量检测报告等，如无前述证明文件，应要求供应商签署《可持续发展倡议书》）
                      </span>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        class="pb-0"
                        v-model="detail.question9"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="-1"></v-radio>
                        <v-radio label="不确定" value="0"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.question9"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '-1' },
                          { text: '不确定', value: '0' },
                        ]"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              结算信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
              <v-card-text>
                <v-form ref="form">
                  <v-row>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.paymentDay"
                        :items="[
                          { text: '-', value: '-' },
                          { text: '立即到期', value: 'Z001' },
                          { text: '10天到期', value: 'Z010' },
                          {
                            text: '15天到期',
                            value: 'Z015',
                          },
                          {
                            text: '20天到期',
                            value: 'Z020',
                          },
                        ]"
                        label="赊销条件"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="9" class="py-0">
                      <v-text-field
                        v-model="detail.paymentDays"
                        label="赊销条件描述"
                        :readonly="isEdit"
                        dense
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-text-field
                        v-model="detail.beneficiary"
                        label="收款人名称"
                        :readonly="isEdit"
                        dense
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.benefiBank"
                        label="	收款人开户行"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.benefiAccount"
                        label="收款人账号"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.accountCurr"
                        :items="[
                          { text: '-', value: '-' },
                          { text: 'CNY', value: '人民币' },
                          { text: 'RUB', value: '俄罗斯卢布' },
                          {
                            text: 'ZAR',
                            value: '南非兰得',
                          },
                          {
                            text: 'IDR',
                            value: '印尼盾',
                          },
                        ]"
                        label="币别"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="币别名称"
                        v-model="detail.accountCurrName"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="收款人开户SWIFT代码"
                        v-model="detail.bankSwift"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.bankCountrySap"
                        :items="[
                          { text: '-', value: '-' },
                          { text: 'CN', value: '中国' },
                          { text: 'HK', value: '中国香港' },
                          {
                            text: 'ID',
                            value: '印度尼西亚',
                          },
                          {
                            text: 'JP',
                            value: '日本',
                          },
                        ]"
                        label="收款人开户国家代码"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="收款人开户国家"
                        v-model="detail.bankCountryName"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="detail.applyPurpose"
                  label="申请目的"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col> -->
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.bankAreaName"
                        label="收款人开户地区/省"
                        :readonly="isEdit"
                        dense
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.bankCityName"
                        label="收款人开户城市"
                        :readonly="isEdit"
                        dense
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.bankStreetName"
                        label="	收款人开户银行地址"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.benefiBank1"
                        label="	收款人开户行1"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.benefiAccount1"
                        label="收款人账号1"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.accountCurr1"
                        :items="[
                          { text: '-', value: '-' },
                          { text: 'CNY', value: '人民币' },
                          { text: 'RUB', value: '俄罗斯卢布' },
                          {
                            text: 'ZAR',
                            value: '南非兰得',
                          },
                          {
                            text: 'IDR',
                            value: '印尼盾',
                          },
                        ]"
                        label="币别1"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="币别名称1"
                        v-model="detail.accountCurrName1"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="收款人开户SWIFT代码1"
                        v-model="detail.bankSwift1"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.bankCountrySap1"
                        :items="[
                          { text: '-', value: '-' },
                          { text: 'CN', value: '中国' },
                          { text: 'HK', value: '中国香港' },
                          {
                            text: 'ID',
                            value: '印度尼西亚',
                          },
                          {
                            text: 'JP',
                            value: '日本',
                          },
                        ]"
                        label="收款人开户国家代码1"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="收款人开户国家1"
                        v-model="detail.bankCountryName1"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="detail.applyPurpose"
                  label="申请目的"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col> -->
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.bankAreaName1"
                        label="收款人开户地区/省1"
                        :readonly="isEdit"
                        dense
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.bankCityName1"
                        label="收款人开户城市1"
                        :readonly="isEdit"
                        dense
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        v-model="detail.bankStreetName1"
                        label="	收款人开户银行地址1"
                        dense
                        outlined
                        :readonly="isEdit"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.clause"
                        label="结算条件简要描述"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        label="未维护结算信息的原因"
                        v-model="detail.nosignreason"
                        dense
                        height="50px"
                        :readonly="isEdit"
                        outlined
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" md="6" class="py-0">
                      <v-select
                        v-model="detail.payer"
                        :items="[
                          {
                            text: 'SITC Shipping Group Company Limited',
                            value: '1000',
                          },
                          {
                            text: 'SITC Shipping Company Limited',
                            value: '1001',
                          },
                          {
                            text: 'SITC SHIPOWNING GROUP COMPANY LIMITED',
                            value: '3000',
                          },
                          {
                            text: 'SITC Development Company Limited',
                            value: '3001',
                          },
                          {
                            text: 'New SITC Development Company Limited',
                            value: '3002',
                          },
                        ]"
                        label="付款人名称"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-text-field
                        label="付款人SAP代码"
                        v-model="detail.sapcode"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-radio-group
                        :rules="[rules.radio]"
                        ref="radio"
                        label=" 是否分期付款"
                        class="pb-0"
                        v-model="detail.paymentType"
                        row
                      >
                        <v-radio label="是" value="1"></v-radio>
                        <v-radio label="否" value="2"></v-radio>
                      </v-radio-group>
                      <!-- <v-select
                        v-model="detail.paymentType"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '2' },
                        ]"
                        label="是否分期付款"
                        dense
                        :rules="[rules.required]"
                        :readonly="isEdit"
                        outlined
                      ></v-select> -->
                    </v-col>
                    <v-col
                      v-if="detail.paymentType == '1'"
                      cols="12"
                      md="3"
                      class="py-0"
                    >
                      <v-text-field
                        label="总计金额"
                        v-model="detail.total"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-text-field>
                    </v-col>
                    <v-col
                      v-if="detail.paymentType == '1'"
                      cols="12"
                      md="3"
                      class="py-0"
                    >
                      <v-select
                        v-model="detail.totalType"
                        :items="[
                          { text: '-', value: '-' },
                          { text: 'CNY', value: '人民币' },
                          { text: 'RUB', value: '俄罗斯卢布' },
                          {
                            text: 'ZAR',
                            value: '南非兰得',
                          },
                          {
                            text: 'IDR',
                            value: '印尼盾',
                          },
                        ]"
                        label="币别"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
              <v-card-text v-if="detail.paymentType == '1'">
                <!-- <v-card-title v-if="detail.applyType == 1"> -->
                <v-card-title>
                  <div></div>
                  <v-spacer></v-spacer>
                  <template>
                    <!-- <v-btn
                      v-if="!isEdit"
                      outlined
                      small
                      tile
                      color="success"
                      class="mx-1"
                      @click="addSupplierP"
                      v-permission="['供应商评审:新增分期']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增
                    </v-btn> -->
                    <!-- <v-btn
                      v-if="!isEdit"
                      small
                      :to="{
                        name: 'supplier-assess-detail-item',
                        params: { id: 'new', buniessId: this.$route.params.id },
                      }"
                      outlined
                      tile
                      color="success"
                      class="mx-1"
                      v-permission="['供应商评审:新增合同']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增
                    </v-btn> -->
                    <!-- <v-btn
                      outlined
                      small
                      tile
                      color="success"
                      :disabled="!bank"
                      class="mx-1"
                      @click="editSupplierBank"
                      v-permission="['供应商准入:编辑银行信息']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      编辑银行信息
                    </v-btn> -->
                    <v-btn
                      v-if="!isEdit"
                      outlined
                      small
                      tile
                      color="success"
                      class="mx-1"
                      @click="addPayCom"
                      v-permission="['供应商评审:新增结算信息']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增分期付款
                    </v-btn>
                    <v-btn
                      v-if="!isEdit"
                      small
                      outlined
                      tile
                      :disabled="!payComs"
                      color="warning"
                      class="mx-1"
                      @click="editSupplierP"
                      v-permission="['供应商评审:修改合同']"
                    >
                      <v-icon left>mdi-pencil</v-icon>
                      修改
                    </v-btn>
                    <v-btn
                      v-if="!isEdit"
                      small
                      outlined
                      tile
                      :disabled="!payComs"
                      color="error"
                      class="mx-1"
                      @click="delSupplierP"
                      v-permission="['供应商评审:删除结算信息']"
                    >
                      <v-icon left>mdi-delete-empty</v-icon>
                      删除
                    </v-btn>
                  </template>
                </v-card-title>
                <template>
                  <v-table-list
                    :headers="payComHeaders"
                    title="分期付款"
                    :items="supplierAssessItemPayList"
                    v-model="payComs"
                    :single-select="true"
                    @dbclick="editSupplierP"
                  >
                    <template v-slot:[`item.status`]="{ item }">
                      {{ ['有效', '暂停整顿', '冻结', '黑名单'][item.status] }}
                    </template>
                    <template v-slot:[`item.paymentType`]="{ item }">
                      {{ ['-', '是', '否'][item.paymentType] }}
                    </template>
                    <template v-slot:[`item.isSign`]="{ item }">
                      {{ ['-', '是', '否'][item.isSign] }}
                    </template>
                    <template v-slot:[`item.havaBillInformation`]="{ item }">
                      {{ ['-', '是', '否'][item.havaBillInformation] }}
                    </template>
                    <template v-slot:expanded-item="{ headers, item }">
                      <td :colspan="headers.length">
                        <v-list class="my-2" dense>
                          <v-list-item-group v-model="bank" color="primary">
                            <v-list-item-subtitle
                              class="d-flex justify-space-between"
                              v-for="b in item.supplierBankListOutputDTOS"
                              :key="b.id"
                              :value="b"
                            >
                              <div>银行名称:{{ b.bank }}</div>
                              <div>银行账户:{{ b.account }}</div>
                              <div>
                                <!-- 币别:{{ currencyInfo[b.currencyType].ccyCode }} -->
                                币别:{{
                                  currencyInfo.find(
                                    (i) => i.id === b.currencyType,
                                  ).ccyCode
                                }}
                              </div>
                              <!-- {{ b.ccyCode }} -->
                              <!-- <v-row>
                                <v-col cols="12" md="4">
                                  开户账号
                                  <v-text-field
                                    v-model="b.bank"
                                    outlined
                                    disabled
                                    readonly
                                    dense
                                  ></v-text-field>
                                </v-col>
                                <v-col cols="12" md="4">
                                  开户行
                                  <v-text-field
                                    v-model="b.account"
                                    outlined
                                    disabled
                                    readonly
                                    dense
                                  ></v-text-field>
                                </v-col>
                                <v-col cols="12" md="4">
                                  币种
                                  <v-select
                                    v-model="b.currencyType"
                                    outlined
                                    required
                                    disabled
                                    readonly
                                    dense
                                    item-text="ccyName"
                                    item-value="id"
                                    :items="currencyInfo"
                                  ></v-select>
                                </v-col>
                              </v-row> -->
                            </v-list-item-subtitle>
                          </v-list-item-group>
                        </v-list>
                      </td>
                    </template>
                  </v-table-list>
                </template>
                <!-- <v-table-list
                  ref="table"
                  v-model="selected"
                  :headers="headers"
                  :items="detailInfo.familyMember"
                  item-key="name"
                ></v-table-list> -->
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              有效期信息
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <!-- <v-card-subtitle>他方签约公司</v-card-subtitle> -->
              <v-card-text>
                <v-form ref="form">
                  <v-row>
                    <v-col cols="12" md="3" class="py-0">
                      <vs-date-picker
                        outlined
                        dense
                        v-model="detail.beginDate"
                        use-today
                        label="合同生效日期"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.beginTime"
                        label="合同生效日期及条件"
                        :readonly="isEdit"
                        dense
                        outlined
                        height="50px"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <vs-date-picker
                        outlined
                        dense
                        v-model="detail.overDate"
                        use-today
                        label="合同终止日期"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.overTime"
                        label="	合同终止日期及条件"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" md="3" class="py-0">
                      <v-select
                        v-model="detail.continot"
                        :items="[
                          { text: '是', value: '1' },
                          { text: '否', value: '2' },
                        ]"
                        label="是否自动延续"
                        dense
                        :readonly="isEdit"
                        outlined
                      ></v-select>
                    </v-col>
                    <v-col cols="12" class="py-0">
                      <v-textarea
                        v-model="detail.isContinu"
                        label="延续条件"
                        dense
                        outlined
                        height="50px"
                        :readonly="isEdit"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <!-- <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              合同标的金额
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-attach-list
                  :attachments="detail.attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="detail.shipCode"
                ></v-attach-list>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel> -->
          <!-- <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              上传营业执照附件文档
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-attach-list
                  :attachments="detail.attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="detail.shipCode"
                ></v-attach-list>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel> -->
          <!-- <v-expansion-panel>
            <v-expansion-panel-header style="color: #3399cc">
              上传承诺函、合同等相关附件文档
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-attach-list
                  :attachments="detail.attachmentRecords2"
                  @change="changeAttachment2"
                  :ship-code="detail.shipCode"
                ></v-attach-list>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel> -->
        </v-expansion-panels>
      </v-card-text>
    </v-detail-view>
    <!-- <bank-dialog
      :initialData="form1"
      v-model="formShow1"
      @success="addBank"
    ></bank-dialog> -->
    <pay-installment‌-dialog
      :initialData="form2"
      v-model="formShow2"
      @success="addCom"
    ></pay-installment‌-dialog>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
// import bankDialog from '../private/bank-dialog.vue'
import PayInstallment‌Dialog from './pay-installment‌-dialog.vue'
import currencyHelper from '@/mixin/currencyHelper'
export default {
  components: { PayInstallment‌Dialog },
  name: 'supplier-assess-detail-item',
  mixins: [routerControl, currencyHelper],
  created() {
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.backRouteName = 'supplier-assess-detail/'
    this.buniessId = this.$route.params.buniessId
    this.status = this.$route.params.buniessStatus
    this.subtitles = [
      //   '准入推荐信息',
      //   '基础信息',
      //   '规模及产品信息',
      //   '结算信息',
      //   '评级打分',
    ]
    this.payComHeaders = [
      // { text: '', value: 'data-table-expand' },
      { text: '金额', value: 'amount' },
      { text: '币别', value: 'currency' },
      { text: '分期付款条件', value: 'term' },
      // { text: '有效期-结束', value: 'endDate' },
      // { text: '联系人姓名', value: 'managerName' },
      // { text: '联系人电话', value: 'managerPhone' },
      // { text: '评级打分', value: 'score' },
      // { text: '状态', value: 'status' },
      // { text: '供应商类型', value: 'supplierType' },
    ]
  },
  computed: {
    isEdit() {
      return (
        this.$route.params.id !== 'new' &&
        this.status !== '1' &&
        this.status !== '4'
      )
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    canEdit() {
      return ['1', '4'].includes(this.status) || this.status == null
    },
    payCom() {
      return this.payComs.length > 0 ? this.payComs : false
    },
  },
  data() {
    return {
      detail: {
        applicationNo: '',
        attachmentIds: [],
      },
      select: false,
      dialog: false,
      searchObj: {},
      rules: {
        required: (v) => !!v || v === false || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        notAboveTwenty: (v) => parseInt(v) <= 20 || '数字不能超过20',
      },
      panel: [0, 1, 2, 3, 4, 5, 6, 7], //根据需要调整panel数组中的值，以控制哪些面板是展开的
      form1: {},
      formShow1: false,
      form2: {},
      formShow2: false,
      supplierPurchaserList: [],
      supplierAssessItemPayList: [],
      payComs: false,
      bank: false,
      supplierInOutScoreModifyDTOList: [],
      supplierInOutItem: { recommendGroup: '程军剑、李瑞民、张海玉、纪文光' },
      saveStatus: true,
    }
  },
  watch: {
    '$store.state.reportParams.businessParams': {
      handler: function (val) {
        console.log(val)
        // if (this.isEdit) return
        const businessParam = val.find(
          (item) =>
            item.businessType === 'supplierAssess_item' &&
            item.businessId === this.$route.params.buniessId &&
            item.id === this.$route.params.buniessId,
        )
        if (!!businessParam && businessParam.businessId) {
          console.log(
            businessParam.businessType + ' ' + businessParam.businessId,
          )
          console.log(businessParam)
          // this.backRouteName = 'security-check-detail-new'

          this.businessParam = businessParam
          // this.detail.type = businessParam.otherParams.type
          // this.deptReportInspection.businessId = businessParam.businessId
          // this.deptReportInspection.shipInfoDO.shipCode =
          //   businessParam.otherParams.shipCode
          // this.deptReportInspection.type =
          //   businessParam.otherParams.type == '3' ? '1' : '0'
          // this.detail.seaJson = { ...businessParam.otherParams }
          // this.detail.shipJson = { ...businessParam.otherParams }
        }
      },
      deep: true,
      immediate: true,
    },
    'detail.paymentType': {
      handler: function (val) {
        console.log(val)
        if ('1' == val) this.loadItemPayList()
      },
    },
  },

  methods: {
    // async save(goBack) {
    //   goBack()
    // },
    // async submit(goBack) {
    //   goBack()
    // },

    // changeAttachment(attachmentIds) {
    //   this.detail.attachmentIds = attachmentIds
    // },
    // changeAttachment2(attachmentIds) {
    //   this.detail.attachmentIds2 = attachmentIds
    // },
    editSupplierP() {
      this.form2 = { ...this.payComs }
      this.formShow2 = true
    },
    addPayCom() {
      this.form2 = {
        ...this.payComs,
        supplierAssessItemId: this.detail.id,
      }
      this.formShow2 = true
    },
    async addCom(newCom) {
      console.log(newCom)
      await this.loadItemPayList()
      this.detail.total = 0
      this.supplierAssessItemPayList.forEach((s) => {
        if (s.amount != null && s.amount !== '') {
          // 检查对象a是否包含supplierBankListOutputDTOS数组
          this.detail.total += s.amount
          // if (!s.supplierBankListOutputDTOS) {
          //   // 如果不包含，赋值一个空数组
          //   s.supplierBankListOutputDTOS = []
          // }
          // s.supplierBankListOutputDTOS.push(newBank)
        }
      })
      this.payComs = false
      // if (
      //   this.supplierPurchaserList.some(
      //     (s) => s.paymentCompany === newCom.paymentCompany,
      //   )
      // ) {
      //   this.$dialog.message.error('付款公司重复')
      //   return
      // }
      // this.supplierPurchaserList.push(newCom)
      // console.log(this.supplierPurchaserList)
    },
    // addBank(newBank) {
    //   console.log(newBank)
    //   // if (
    //   //   this.supplierPurchaserList.some(
    //   //     (s) => s.paymentCompany === newBank.paymentCompany,
    //   //   )
    //   // ) {
    //   //   this.supplierPurchaserList.supplierBankListOutputDTOS.push(newBank)
    //   // }
    //   // 假设this.supplierPurchaserList是已经定义好的数组
    //   this.supplierPurchaserList.forEach((s) => {
    //     if (s.paymentCompany === newBank.paymentCompany) {
    //       // 检查对象a是否包含supplierBankListOutputDTOS数组
    //       if (!s.supplierBankListOutputDTOS) {
    //         // 如果不包含，赋值一个空数组
    //         s.supplierBankListOutputDTOS = []
    //       }
    //       s.supplierBankListOutputDTOS.push(newBank)
    //     }
    //   })
    //   console.log('1213ad', this.supplierPurchaserList)
    // },
    async delSupplierP() {
      // this.supplierPurchaserList = this.supplierPurchaserList.filter(
      //   (s) => !(s.id === this.payCom.id),
      // )
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/SupplierAssess/assessItemPayDelete',
        { id: this.payComs.id },
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.loadItemPayList()
        this.payComs = false
        this.detail.total = 0
        this.supplierAssessItemPayList.forEach((s) => {
          if (s.amount != null && s.amount !== '') {
            // 检查对象a是否包含supplierBankListOutputDTOS数组
            this.detail.total += s.amount
            // if (!s.supplierBankListOutputDTOS) {
            //   // 如果不包含，赋值一个空数组
            //   s.supplierBankListOutputDTOS = []
            // }
            // s.supplierBankListOutputDTOS.push(newBank)
          }
        })
      }
    },
    // editSupplierBank() {
    //   this.form1 = { ...this.bank }
    //   this.formShow1 = true
    // },
    // addSupplierBank() {
    //   this.form1 = {
    //     supplierId: this.$route.params.id,
    //     purchaserId: this.payCom.id,
    //     paymentCompany: this.payCom.paymentCompany,
    //   }
    //   this.formShow1 = true
    // },
    // getCompWithOperation() {
    //   const ids = this.supplierPurchaserList.map((i) => i.id)
    //   const delList = this.isEdit
    //     ? this.detail.supplierPurchaserList
    //         .filter((i) => !ids.includes(i.id))
    //         .map((i) => {
    //           return { ...i, operationType: 3 }
    //         })
    //     : []
    //   const others = this.supplierPurchaserList.map((i) => {
    //     return { ...i, operationType: i.id ? 2 : 1 }
    //   })
    //   others.forEach((s) => {
    //     if ('supplierBankListOutputDTOS' in s) {
    //       // s包含supplierBankListOutputDTOS属性
    //       s.supplierBankListOutputDTOS = s.supplierBankListOutputDTOS.map(
    //         (i) => {
    //           return { ...i, operationType: i.id ? 2 : 1 }
    //         },
    //       )
    //     } else {
    //       // s不包含supplierBankListOutputDTOS属性
    //       this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
    //     }
    //     // s.supplierBankListOutputDTOS = s.supplierBankListOutputDTOS.map((i) => {
    //     //   return { ...i, operationType: i.id ? 2 : 1 }
    //     // })
    //   })
    //   return [...delList, ...others]
    // },
    async save(goBack, notMove = false) {
      console.log(this.id)
      console.log(this.buniessId)
      console.log(this.status)
      if (!this.$refs.form.validate()) {
        return false
      }
      // if (this.supplierPurchaserList.length === 0) {
      //   this.$dialog.message.warning('请填写结算信息')
      //   return false
      // }
      // const detailList = this.getCompWithOperation()
      this.saveStatus = true
      // detailList.forEach((s) => {
      //   if ('supplierBankListOutputDTOS' in s) {
      //     // s包含supplierBankListOutputDTOS属性
      //     if (s.supplierBankListOutputDTOS.length <= 0) {
      //       this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
      //       this.saveStatus = false
      //     }
      //   } else {
      //     // s不包含supplierBankListOutputDTOS属性
      //     this.$dialog.message.error('请录入' + s.paymentCompany + '银行信息')
      //     this.saveStatus = false
      //   }
      // })
      if (!this.saveStatus) {
        return
      }
      // const supplierInOutScoreModifyDTOList =
      //   this.supplierInOutScoreModifyDTOList
      // this.supplierInOutScoreModifyDTOList.forEach((item) => {
      //   item.total =
      //     item.prodeptNum +
      //     item.saledeptNum +
      //     item.lastYearSales +
      //     item.productIntroduction +
      //     item.productAdvantages +
      //     item.clients +
      //     item.marketShare
      // })
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/SupplierAssess/assessItemSaveOrUpdate',
        {
          ...this.detail,
          supplierAssessId: this.buniessId,
          // supplierInOutItem: this.supplierInOutItem,
          // supplierPurchaserList: [...detailList],
          // supplierInOutScoreModifyDTOList: [
          //   ...this.supplierInOutScoreModifyDTOList,
          // ],
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      console.log(this.businessParam)
      if (this.businessParam) {
        this.$store.commit('setReportId', {
          ...this.businessParam,
          reportId: '1',
        })
        this.closeAndTo('supplier-assess-detail', {
          id: this.businessParam.businessId,
        })
      }
      // goBack()
    },
    // async submit(goBack) {
    //   if (!(this.$refs?.aform?.validate() ?? true)) return
    //   const data = await this.save(goBack, true)
    //   if (!data) {
    //     return
    //   } else {
    //     if (!this.detail.auditParams) {
    //       const { errorRaw } = await this.getAsync(
    //         '/business/shipAffairs/supplierInOut/supplierInOutSubmit',
    //         { applyId: data },
    //       )
    //       if (!errorRaw) goBack()
    //     } else {
    //       const error = await this.$refs.audit.submit()
    //       if (!error) goBack()
    //     }
    //   }
    // },
    async loadDetail() {
      // if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/SupplierAssess/getItemDetailById',
        { id: this.$route.params.id },
      )
      this.detail = { ...data }
      // this.supplierInOutItem = data.supplierInOutItem
      // this.supplierInOutScoreModifyDTOList = [
      //   ...data.supplierInOutScoreModifyDTOList,
      // ]
      // this.supplierPurchaserList = data.supplierPurchaserList.map((s) => {
      //   return { ...s, vid: s.id, operationType: 0 }
      // })
      // this.supplierPurchaserList.forEach((s) => {
      //   s.supplierBankListOutputDTOS.forEach((a) => {
      //     a.operationType = 0
      //   })
      // })
      // if (this.detail.applyType != 2 && this.detail.status == 2) {
      //   this.panel = [0, 5, 7]
      //   this.$dialog.message.error('请在评级打分模块填写评分后再提交！')
      // }
      if (this.detail.paymentType == '1') this.loadItemPayList()
    },
    async loadItemPayList() {
      if (!this.detail.id || this.detail.id == '')
        this.detail.id = this.$route.params.id
      // setTimeout(() => {
      //   const { data } = this.getAsync(
      //     '/business/shipAffairs/SupplierAssess/pageItemPayList',
      //     { id: this.detail.id },
      //   )
      //   this.supplierAssessItemPayList = data
      // }, 500)
      const { data } = await this.getAsync(
        '/business/shipAffairs/SupplierAssess/pageItemPayList',
        { id: this.detail.id },
      )
      this.supplierAssessItemPayList = data
    },
    // async loadDetailByType() {
    //   const { data } = await this.getAsync(
    //     '/business/shipAffairs/supplierInOut/supplierInOutDetailByApplyType',
    //     {
    //       applyType: this.$route.params.applyType,
    //       supplierId: this.$route.params.supplierId,
    //     },
    //   )
    //   this.detail = { ...data }
    //   this.supplierInOutItem = data.supplierInOutItem
    //   this.supplierInOutScoreModifyDTOList = [
    //     ...data.supplierInOutScoreModifyDTOList,
    //   ]
    //   this.supplierPurchaserList = data.supplierPurchaserList.map((s) => {
    //     return { ...s, vid: s.id, operationType: 0 }
    //   })
    //   this.supplierPurchaserList.forEach((s) => {
    //     s.supplierBankListOutputDTOS.forEach((a) => {
    //       a.operationType = 0
    //     })
    //   })
    //   this.supplierInOutItem.recommendGroup = '程军剑、李瑞民、张海玉、纪文光'
    // },
  },

  mounted() {
    // if (this.$route.params.applyType != undefined) {
    //   // console.log(1)
    //   this.detail.applyType = this.$route.params.applyType
    //   // 退出
    //   if (this.detail.applyType != 1) {
    //     this.loadDetailByType()
    //   }
    // }
    // if (this.detail.applyType == 2) {
    //   this.panel = [0, 1, 2, 3, 4, 5, 6]
    // }
    this.loadDetail()
    // this.loadItemPayList()
  },
}
</script>

<style></style>
