<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    label="选择供应商合同评审"
    :headers="headers"
    item-text="contractName"
    item-value="id"
    :req-url="reqUrl"
    :search-remain="searchObj"
    @update="update"
    @open="updateSearchObj"
    max-width="3300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
    :clearable="clearable"
    fuzzy-label="模糊查询"
  >
    <!-- <template v-slot:searchflieds>
      <v-col cols="12" sm="6" md="3">
        <v-select
          v-model="searchObj.euipmentType"
          outlined
          label="设备类型"
          dense
          clearable
          :items="euipmentTypes"
        ></v-select>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-text-field
          label="供应商名称"
          outlined
          dense
          v-model="searchObj.name"
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-text-field
          label="英文名称"
          outlined
          dense
          v-model="searchObj.nameEn"
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-text-field
          label="SAP代码"
          outlined
          dense
          v-model="searchObj.sapCode"
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-select
          v-model="searchObj.businessStatus"
          :items="[
            { text: '已通过SAP评审', value: '已通过SAP评审' },
            { text: '未通过SAP评审', value: '未通过SAP评审' },
            { text: '已退出', value: '已退出' },
          ]"
          label="状态"
          outlined
          clearable
          dense
        ></v-select>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-select
          v-model="searchObj.type"
          :items="[
            { text: '内部公司', value: '0' },
            { text: '外部', value: '2' },
            { text: '员工', value: '3' },
          ]"
          label="内部、外部、个人"
          outlined
          clearable
          dense
        ></v-select>
      </v-col>
      <v-col cols="12" sm="6" md="3">
        <v-select
          v-model="searchObj.businessType2"
          :items="[
            { text: '船员', value: '船员' },
            { text: '备件', value: '备件' },
            { text: '物料', value: '物料' },
            { text: '滑油', value: '滑油' },
            { text: '化学品', value: '化学品' },
            { text: '缆绳', value: '缆绳' },
            { text: '绑扎件', value: '绑扎件' },
            { text: '油漆', value: '油漆' },
            { text: '锚、锚链', value: '锚、锚链' },
            { text: '消防救生检验', value: '消防救生检验' },
            { text: '海图', value: '海图' },
            { text: '通导', value: '通导' },
            { text: '坞修', value: '坞修' },
            { text: '航修', value: '航修' },
            { text: '固定资产', value: '固定资产' },
            { text: '年度协议', value: '年度协议' },
            { text: '大宗采购', value: '大宗采购' },
            { text: '大宗采购', value: '大宗采购' },
            { text: '查看全部（包含未分配业务类型的供应商）', value: '' },
          ]"
          label="业务类型"
          outlined
          clearable
          dense
        ></v-select>
      </v-col>
    </template>
    <template v-slot:[`item.account`]="{ item }">
      <v-chip small v-if="item.sapCode != null">{{ item.sapCode }}</v-chip>
      <v-chip small v-if="item.sapCode == null">{{ item.account }}</v-chip>
    </template> -->
  </v-dialog-select>
</template>
<script>
export default {
  name: 'supplier-assess-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/business/shipAffairs/SupplierAssess/pageList'
    this.headers = [
      { text: '申请类型', value: 'ctype' },
      { text: '评审合同名称', value: 'contractName' },
      { text: 'OA审批编号', value: 'applicationNo' },
      // { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    // this.equipmentStatuses = [
    //   { text: '使用', value: 1 },
    //   { text: '停用', value: 2 },
    //   { text: '报废', value: 3 },
    // ]
    // this.euipmentTypes = [
    //   { text: '主机', value: '0' },
    //   { text: '副机', value: '1' },
    //   { text: '辅助设备', value: '2' },
    //   { text: '通导设备', value: '3' },
    // ]
  },
  props: {
    shipCode: String,
    businessStatus: String,
    value: [String, Object],
    disabled: [String, Boolean],
    readonly: [String, Boolean],
    numbers: Array,
    initSelected: Object,
    // read
  },
  data() {
    return {
      searchObj: { shipCode: '', businessStatus: '已完成OA评审' },
      supplier: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  watch: {
    value(val) {
      console.log(val)
      this.val = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
    businessStatus(val) {
      this.searchObj.businessStatus = val
    },
  },

  methods: {
    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },
    update() {
      console.log(this.val)
      this.$emit('update', this.val)
      // this.$emit('update', this.val.id)
      // this.$emit('update:numbers', this.val.equipmentNumber)
    },
    updateSearchObj() {
      //   // this.$nextTick(() => {
      //   // })
      //   if (this.searchObj.shipCode !== this.shipCode) {
      //     console.log('asd')
      //     this.searchObj.shipCode = ''
      //     this.$nextTick(() => {
      //       this.searchObj.shipCode = this.shipCode
      //     })
      //   }
    },
  },

  mounted() {
    this.searchObj.shipCode = this.shipCode
  },
}
</script>

<style></style>
