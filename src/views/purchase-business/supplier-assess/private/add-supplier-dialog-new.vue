<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        选择供应商
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12">
                <supplier-select
                  :rules="[rules.required]"
                  v-model="formData.supplier"
                  :initSelected="supplierEngine"
                  :businessStatus="
                    this.formData.ctype == '1'
                      ? '未通过SAP评审'
                      : '已通过SAP评审'
                  "
                  required
                ></supplier-select>
                <!-- <engine-select
                  v-model="searchObj.equipmentInformationId"
                  :shipCode="detail.shipCode"
                  :initSelected="initEngine"
                  :rules="[false]"
                ></engine-select> -->
              </v-col>
              <v-col cols="12">
                <supplier-assess-select
                  :rules="[rules.required]"
                  v-model="formData.supplierAssess"
                  :initSelected="supplierAssessEngine"
                  required
                ></supplier-assess-select>
                <!-- <engine-select
                  v-model="searchObj.equipmentInformationId"
                  :shipCode="detail.shipCode"
                  :initSelected="initEngine"
                  :rules="[false]"
                ></engine-select> -->
              </v-col>
              <!-- <v-col cols="12" md="3">
                <vs-date-picker
                  label="检查日期"
                  :rules="[rules.required]"
                  v-model="formData.inspectionTime"
                  @change="changeTime"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col> -->
              <v-col cols="12" md="6">
                <v-select
                  label="检查类型"
                  v-model="formData.ctype"
                  :rules="[rules.required]"
                  :items="insTypes"
                  outlined
                  required
                  readonly="true"
                  dense
                ></v-select>
              </v-col>
            </v-row>
            <!-- <v-row>
              <v-col cols="12" md="3">
                <v-select
                  v-if="formData.inspectionType === '2'"
                  label="风险等级"
                  v-model="formData.riskLevel"
                  :rules="[rules.required]"
                  :items="风险等级"
                  outlined
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-if="formData.inspectionType === '2'"
                  label="下次窗口日期"
                  v-model="formData.nextInspectionTime"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col>
            </v-row> -->
            <v-row>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  创建
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import SupplierSelect from './supplier-select.vue'
import SupplierAssessSelect from './supplier-assess-select.vue'
export default {
  components: { SupplierSelect, SupplierAssessSelect },
  name: 'add-supplier-dialog-new',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({ ctype: '1' }),
    },
    date1: {
      type: Date,
      default: null,
    },
  },
  created() {
    this.insTypes = [
      { text: '新增供应商评审', value: '1' },
      { text: '续签供应商评审（年度评审）', value: '2' },
      { text: '变更银行结算信息', value: '3' },
      { text: '加入黑名单', value: '4' },
    ]
    this.风险等级 = [
      { text: '低风险', value: '0' },
      { text: '标准风险', value: '1' },
      { text: '高风险', value: '2' },
    ]
  },
  data() {
    return {
      dialog: false,
      formData: {
        ctype: '1',
        supplier: '',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      supplierEngine: {},
      supplierAssessEngine: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      // this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.formData.supplier = ''
      this.formData.supplierAssess = ''
      this.supplierEngine = {}
      this.supplierAssessEngine = {}
      console.log(this.formData)
      // 已通过SAP评审  未通过SAP评审//此处好像无效
      if (this.formData.ctype == '1')
        this.supplierEngine.businessStatus = '未通过SAP评审'
      else this.supplierEngine.businessStatus = '已通过SAP评审'
    },
    // open(val) {
    //   this.dialog = val
    //   // this.$refs?.form?.formData?.reset()
    //   this.$refs?.form?.resetValidation()
    //   this.questionTypes = [
    //     { text: '无缺陷', value: '0' },
    //     { text: '缺陷项', value: '1' },
    //   ]
    //   // <!--"`${this.checkType === '1' ? 'security_ques_types_psc' : 'security_ques_types'}`"-->
    //   if ('2' == this.checkType) this.dict_type = 'security_ques_types_psc'
    //   if (['0', '1'].includes(this.checkType))
    //     this.questionTypes.push({ text: '建议项', value: '2' })
    //   this.$nextTick(() => {
    //     this.formData = { ...this.initialData }
    //     this.questionId =
    //       this.formData.id || Math.floor(Math.random() * 1000 + 1)
    //   })
    // },
    // date1(val) {
    //   // this.dialog = val
    //   console.log(val)
    //   // this.$refs?.form?.resetValidation()
    //   this.formData.nextInspectionTime = val
    // },
    'formData.inspectionTime'(val) {
      // console.log(val)
      /** 
      if (this.formData.inspectionType == '2') {
        let date_1 = new Date(val)
        // console.log(date_1)
        let date_2 = date_1.setMonth(date_1.getMonth() + 0)
        // console.log(date_2)
        if (!!this.formData.riskLevel && this.formData.riskLevel == '0') {
          date_2 = date_1.setMonth(date_1.getMonth() + 9)
        } else if (
          !!this.formData.riskLevel &&
          this.formData.riskLevel == '1'
        ) {
          date_2 = date_1.setMonth(date_1.getMonth() + 5)
        } else if (
          !!this.formData.riskLevel &&
          this.formData.riskLevel == '2'
        ) {
          date_2 = date_1.setMonth(date_1.getMonth() + 2)
        }
        let date_s = new Date(date_2)
        // console.log(date_s)
        //获取yyyy-mm-dd格式日期：date_s.toISOString().substr(0, 10)
        this.formData.nextInspectionTime = date_s.toISOString().substr(0, 10)
      }
      */
      this.changeTime(val, this.formData.riskLevel)
    },
    'formData.riskLevel'(val) {
      // console.log(val)
      /** 
      if (this.formData.inspectionType == '2') {
        let date_1 = new Date(this.formData.inspectionTime)
        // console.log(date_1)
        let date_2 = date_1.setMonth(date_1.getMonth() + 0)
        // console.log(date_2)
        if (!!val && val == '0') {
          date_2 = date_1.setMonth(date_1.getMonth() + 9)
        } else if (!!val && val == '1') {
          date_2 = date_1.setMonth(date_1.getMonth() + 5)
        } else if (!!val && val == '2') {
          date_2 = date_1.setMonth(date_1.getMonth() + 2)
        }
        let date_s = new Date(date_2)
        // console.log(date_s)
        //获取yyyy-mm-dd格式日期：date_s.toISOString().substr(0, 10)
        this.formData.nextInspectionTime = date_s.toISOString().substr(0, 10)
        
      }
      */
      this.changeTime(this.formData.inspectionTime, val)
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    changeTime(inspectionTime, riskLevel) {
      if (!inspectionTime || inspectionTime == '') return
      if (this.formData.inspectionType == '2') {
        let date_1 = new Date(inspectionTime)
        // console.log(date_1)
        let date_2 = date_1.setMonth(date_1.getMonth() + 0)
        // console.log(date_2)
        if (!!riskLevel && riskLevel == '0') {
          date_2 = date_1.setMonth(date_1.getMonth() + 9)
        } else if (!!riskLevel && riskLevel == '1') {
          date_2 = date_1.setMonth(date_1.getMonth() + 5)
        } else if (!!riskLevel && riskLevel == '2') {
          date_2 = date_1.setMonth(date_1.getMonth() + 2)
        }
        let date_s = new Date(date_2)
        // console.log(date_s)
        //获取yyyy-mm-dd格式日期：date_s.toISOString().substr(0, 10)
        this.formData.nextInspectionTime = date_s.toISOString().substr(0, 10)
      }
    },
    async save() {
      console.log(this.formData)
      // if (!this.$refs.form.validate()) {
      //   return
      // }
      // console.log(this.formData.supplier)
      // console.log('this.$refs.form.formData.supplier')
      // console.log(this.$refs.form)
      if (this.formData.supplier == '' && this.formData.supplierAssess == '') {
        this.$dialog.message.error('请选择供应商或供应商合同评审记录')
        return
      }
      const url = '/business/shipAffairs/SupplierAssess/createSupplierAssess'
      const { errorRaw, data } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success', data)
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
