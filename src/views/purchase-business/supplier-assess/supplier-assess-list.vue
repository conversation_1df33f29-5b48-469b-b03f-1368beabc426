<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col> -->
        <v-col cols="12" sm="6" md="3">
          <v-select
            v-model="searchObj.businessStatus"
            :items="[
              { text: '草稿', value: '草稿' },
              {
                text: '已发送OA评审（一阶段）',
                value: '已发送OA评审（一阶段）',
              },
              { text: '退回（一阶段）', value: '退回（一阶段）' },
              { text: '补充合同附件', value: '补充合同附件' },
              {
                text: '已发送OA评审（二阶段）',
                value: '已发送OA评审（二阶段）',
              },
              { text: '退回（二阶段）', value: '退回（二阶段）' },
              { text: '已完成OA评审', value: '已完成OA评审' },
            ]"
            label="状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="formShow = true"
          v-permission="['供应商列表:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn> -->
        <!-- <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="!selected"
          @click="resetPassword"
          v-permission="['供应商准入:账户密码重置']"
        >
          <span class="mdi mdi-account-reactivate"></span>
          账户密码重置
        </v-btn> -->
        <!-- <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{
            name: 'supplier-assess-detail',
            params: { id: 'new', ctype: '1' },
          }"
          v-permission="['供应商评审:新增供应商评审']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增供应商评审
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="createWork1"
          v-permission="['供应商评审:新增供应商评审']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增供应商评审
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="createWork2"
          v-permission="['供应商评审:续签供应商评审(年度评审)']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          续签供应商评审(年度评审)
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="createWork3"
          v-permission="['供应商评审:变更银行结算信息']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          变更银行结算信息
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="createWork4"
          v-permission="['供应商评审:加入黑名单']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          加入黑名单
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="deleteSupplierAssess"
          v-permission="['供应商评审:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.ctype`]="{ item }">
        <v-chip small v-if="item.ctype == '1'">新增供应商评审</v-chip>
        <v-chip small color="warning" v-if="item.ctype == '2'">
          续签供应商评审（年度评审）
        </v-chip>
        <v-chip small color="warning" v-if="item.ctype == '3'">
          变更银行结算信息
        </v-chip>
        <v-chip small color="error" v-if="item.ctype == '4'">加入黑名单</v-chip>
      </template>
    </v-table-searchable>
    <add-supplier-dialog-new
      @success="addSuccess"
      :initialData="initialData"
      v-model="dialog"
    ></add-supplier-dialog-new>
  </v-container>
</template>
<script>
import addSupplierDialogNew from './private/add-supplier-dialog-new.vue'
// import addSupplierDialogNew from './private/add-supplier-dialog-new'
export default {
  components: { addSupplierDialogNew },
  name: 'supplier-assess-list',
  created() {
    this.tableName = '供应商合同评审'
    this.reqUrl = ''
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.reqUrl = '/business/shipAffairs/SupplierAssess/pageList'
    this.headers = [
      { text: '申请类型', value: 'ctype' },
      { text: '评审合同名称', value: 'contractName' },
      { text: 'OA审批编号', value: 'applicationNo' },
      // { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      // { text: 'SAP代码', value: 'sapCode' },
      // { text: '供应商名称', value: 'name' },
      // { text: '英文名称', value: 'nameEn' },
      // // { text: '账号', value: 'account' },
      // { text: '账号', value: 'sapCode' },
      // { text: '邮箱', value: 'supplierMail' },
    ]
    this.fuzzyLabel = '模糊查询'
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'supplier-assess-detail' }
  },

  data() {
    return {
      searchObj: {
        isMe: true,
      },
      selected: false,
      dialog: false,
      initialData: [],
    }
  },

  methods: {
    addSuccess(id) {
      this.$router.push({ name: 'supplier-assess-detail', params: { id } })
      this.$refs.table.loadTableData()
    },
    async deleteSupplierAssess() {
      if (
        !(
          this.selected.businessStatus == '草稿' ||
          this.selected.businessStatus == '退回（一阶段）'
        )
      ) {
        this.$dialog.message.warning('只能删除草稿或退回（一阶段）的记录')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/SupplierAssess/deleteSupplierAssess',
        { id: this.selected.id },
      )
      if (!errorRaw) {
        this.selected = false
        this.$refs.table.loadTableData()
      }
    },
    createWork1() {
      this.initialData = {
        // shipCode: this.detail.shipCode,
        // problemPhotos: [],
        // correctEvidences: [],
        ctype: '1',
      }
      this.dialog = true
    },
    createWork2() {
      this.initialData = {
        // shipCode: this.detail.shipCode,
        // problemPhotos: [],
        // correctEvidences: [],
        ctype: '2',
      }
      this.dialog = true
    },
    createWork3() {
      this.initialData = {
        // shipCode: this.detail.shipCode,
        // problemPhotos: [],
        // correctEvidences: [],
        ctype: '3',
      }
      this.dialog = true
    },
    createWork4() {
      this.initialData = {
        // shipCode: this.detail.shipCode,
        // problemPhotos: [],
        // correctEvidences: [],
        ctype: '4',
      }
      this.dialog = true
    },
  },

  mounted() {},
}
</script>

<style></style>
