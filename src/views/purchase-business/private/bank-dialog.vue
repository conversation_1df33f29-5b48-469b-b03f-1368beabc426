<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        供应商银行信息
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="4">
                <v-text-field
                  v-model="formData.account"
                  label="开户账号"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.address"
                  label="开户地址"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.bank"
                  label="开户行"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.bankCode"
                  label="开户银行代码"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="4">
                <v-text-field
                  v-model="formData.bank"
                  label="开户行"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col>
              <!-- 
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="formData.city"
                  label="开户城市"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="formData.country"
                  label="开户国家"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col> -->

              <v-col cols="12" md="4">
                <v-select
                  v-model="formData.currencyType"
                  label="币种"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                  item-text="ccyName"
                  item-value="id"
                  :items="currencyInfo"
                ></v-select>
              </v-col>
              <!-- @change="(val) => (formData.ccyCode = val)" -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  v-model="formData.name"
                  label="开户名"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="formData.region"
                  label="开户地区/省"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.swiftCode"
                  label="swift代码"
                  :rules="[rules.required]"
                  outlined
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'bank-dialog',
  mixins: [currencyHelper],
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.$emit('change', false)
      console.log(111111, this.formData)
      this.$emit('success', this.formData)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
