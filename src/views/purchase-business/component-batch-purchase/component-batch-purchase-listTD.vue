<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      :single-select="false"
    >
      <template #searchflieds>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col> -->
        <v-col cols="12" md="3">
          <v-select
            v-model="searchObj.businessStatus"
            :items="[
              { text: '未提交', value: '未提交' },
              { text: '待各主管确认', value: '待各主管确认' },
              { text: '待采购主管确认', value: '待采购主管确认' },
              { text: '超预算待确认', value: '超预算待确认' },
              { text: '已生成订单', value: '已生成订单' },
            ]"
            label="业务状态"
            dense
            required
            outlined
            clearable
          ></v-select>
        </v-col>
        <!-- <v-col cols="12" md="3" v-show="false">
          <v-select
            v-model="searchObj.type"
            :items="[
              { text: '机务', value: '机务' },
              { text: '通导', value: '通导' },
            ]"
            label="业务状态"
            dense
            required
            outlined
            clearable
          ></v-select>
        </v-col> -->
        <v-col cols="12" md="3">
          <v-supply-select-list
            v-model="searchObj.supplierId"
            label="供应商"
            outlined
            clearable
            dense
          ></v-supply-select-list>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="selected.length == 0"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="copyApply"
          v-permission="['备件专项集采:复制']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          复制
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{
            name: 'component-batch-purchase-detail',
            params: { id: 'new', type: '通导' },
          }"
          v-permission="['备件专项集采:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!canDel"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delApply"
          v-permission="['备件专项集采:删除列表']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!canAbandon"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="anandonApply"
          v-permission="['备件专项集采:废弃']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          废弃
        </v-btn>
      </template>
      <template v-slot:[`item.needConfirmNum`]="{ item }">
        <v-chip small v-if="item.needConfirmNum <= 0">
          {{ item.needConfirmNum }}
        </v-chip>
        <v-chip small color="warning" v-if="item.needConfirmNum > 0">
          {{ item.needConfirmNum }}
        </v-chip>
      </template>
      <template v-slot:[`item.applyType`]="{ item }">
        <v-chip small v-if="item.applyType == 1">常规</v-chip>
        <v-chip small color="warning" v-if="item.applyType == 2">紧急</v-chip>
        <v-chip small color="error" v-if="item.applyType == 3">坞修</v-chip>
        <v-chip small color="error" v-if="item.applyType == 4">固定资产</v-chip>
        <v-chip small color="error" v-if="item.applyType == 5">
          通导设备固定资产
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'component-batch-purchase-listTD',
  created() {
    this.tableName = '通导专项集采'
    this.reqUrl = ''
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.reqUrl = '/business/shipAffairs/componentBatchPurchase/page'
    this.headers = [
      { text: '单号', value: 'orderNo' },
      { text: '申请人', value: 'applicantName' },
      { text: '集采类型', value: 'type' },
      { text: '采购类型', value: 'applyType' },
      { text: '供应项目', value: 'supplyItem' },
      { text: '供应商', value: 'supplierName' },
      { text: '金额', value: 'money' },
      { text: '币种', value: 'currencyId' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '待确认数量', value: 'needConfirmNum' },
      { text: '备注', value: 'applyRemark' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.fuzzyLabel = '单号'
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'component-batch-purchase-detail' }
  },

  data() {
    return {
      searchObj: { type: '通导' },
      selected: [],
      // type: '',
    }
  },
  computed: {
    canDel() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            item.businessStatus == '未提交' &&
            item.applicantId == this.$local.data.get('userInfo').id,
        )
      )
    },
    canAbandon() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            (item.businessStatus == '待各主管确认' ||
              item.businessStatus == '待采购主管确认') &&
            item.applicantId == this.$local.data.get('userInfo').id,
        )
      )
    },
  },
  methods: {
    async delApply() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/componentBatchPurchase/deleteBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async copyApply() {
      // if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/componentBatchPurchase/copyBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`复制成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async anandonApply() {
      if (!(await this.$dialog.msgbox.confirm('确定废弃此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/componentBatchPurchase/abandonBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`废弃成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
  },

  mounted() {
    if (this.$route.query.businessStatus != undefined) {
      this.searchObj.businessStatus = this.$route.query.businessStatus
    }
    // if (this.$route.meta.type != undefined) {
    //   this.searchObj.type = this.$route.meta.type
    // }
  },
}
</script>

<style></style>
