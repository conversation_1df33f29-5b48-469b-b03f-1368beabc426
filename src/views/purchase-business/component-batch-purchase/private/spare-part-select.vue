<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        备件选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="3">
              <v-ship-select
                v-model="searchObj.shipCode"
                clearable
              ></v-ship-select>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-select
                :disabled="!searchObj.shipCode"
                v-model="searchObj.firstId"
                label="设备主体"
                outlined
                dense
                :items="firstEquipment"
                :loading="firstLoading"
                clearable
              ></v-select>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-autocomplete
                :disabled="!searchObj.firstId"
                v-model="searchObj.equipmentId"
                label="子设备"
                outlined
                dense
                :items="subEquipments"
                :loading="loading"
                clearable
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.drawingSerialNumberLike"
                label="图纸号"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col>
            <!-- <v-col cols="12" sm="6" md="3">
              <v-autocomplete
                v-model="searchObj.equipmentSecondId"
                label="大模块"
                outlined
                dense
                :items="secondEquipments"
                clearable
              ></v-autocomplete>
            </v-col> -->
            <!-- <v-col cols="12" sm="6" md="3">
              <v-autocomplete
                v-model="searchObj.equipmentId"
                label="子设备"
                outlined
                dense
                :items="subEquipments"
                :loading="loading"
                clearable
              ></v-autocomplete>
            </v-col> -->
            <!-- <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.drawingSerialNumberLike"
                label="图纸号"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col> -->
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.equipmentCodeLike"
                label="备件号"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.equipmentNameLike"
                label="备件名"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-dict-select
                v-model="searchObj.standardType"
                label="备件标准化分类"
                clearable
                dense
                outlined
                dict-type="component_standard_ype"
              ></v-dict-select>
            </v-col>
          </template>
          <template #btns></template>
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo.chShipName }}
          </template>
          <template v-slot:[`item.componentProperty`]="{ item }">
            {{ ['普通备件', 'SAP备件', '固定资产'][item.componentProperty] }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'spare-part-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.tableName = '子设备管理'
    this.reqUrl =
      '/business/shipAffairs/equipmentInformation/componentPageByBath'
    this.headers = [
      { text: '船舶名称', value: 'shipInfo' },
      { text: '子设备', value: 'equipmentThirdCname' },
      { text: '图纸号', value: 'drawingSerialNumber' },
      // { text: '在图编号', value: 'drawingNo' },
      { text: '备件名称', value: 'componentEname' },
      { text: '备件号', value: 'componentNumber' },
      { text: '标准化分类', value: 'standardType' },
      { text: '备件类型', value: 'componentProperty' },
      { text: '单位', value: 'componentUnit' },
      { text: '型号', value: 'componentModel' },
      { text: '参数', value: 'componentParameter' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    components: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {},
      selected: [],
      firstEquipment: [],
      firstLoading: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    'searchObj.shipCode': {
      handler(val) {
        if (val) this.loadFirstEquipment()
      },
    },
    'searchObj.equipmentSecondId': {
      handler(val) {
        if (val) this.loadSubEqu()
      },
    },
    'searchObj.firstId': {
      handler(val) {
        // if (val) this.loadSecondEquipment()
        if (val) this.loadSubEqu()
      },
    },
    searchRemain(val) {
      this.searchObj = val
    },
    components: {
      handler(val) {
        this.selected = val.map((i) => {
          return { ...i, vid: i.id, id: i.componentId, remarkk: i.remark }
        })
      },
      deep: true,
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    // 子设备获取
    async loadSubEqu() {
      this.loading = true
      // const reqUrl = this.searchObj.equipmentSecondId
      //   ? '/business/shipAffairs/equipmentInformation/getEquipmentThridBySecondId'
      //   : '/business/shipAffairs/equipmentInformation/getEquipmentThirdByMainEquipmentId'
      const reqUrl =
        '/business/shipAffairs/equipmentInformation/getEquipmentThirdByMainEquipmentId'
      const { data } = await this.getAsync(reqUrl, {
        MainEquipmentId: this.searchObj.firstId,
        // secondEquipmentId: this.searchObj?.equipmentSecondId,
      })
      this.subEquipments = data?.map((i) => {
        return {
          text: i.subEquipmentCname,
          value: i.id,
        }
      })
      this.loading = false
    },
    // 大模块
    async loadSecondEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentSecondByMainEquipmentId',
        { MainEquipmentId: this.searchObj.equipmentInformationId },
      )
      this.secondEquipments = data?.map((i) => {
        return {
          text: i.name,
          value: i.id,
        }
      })
    },
    confirm() {
      const components = this.selected.map((i) => {
        const comp = {
          ...i,
          componentId: i.id,
          shipCode: i.shipInfo.shipCode,
          id: i.vid,
          applyNumber: i.applyNumber || 0,
          price: i.price || 0,
          componentNumber: i.componentNumber,
          equipmentThirdCname: i.equipmentThirdCname,
          sapComponentNumberNew: i.sapComponentNumberNew,
          manager: i.manager,
          remark: i.remarkk || '',
        }
        return comp
      })
      this.$emit('update:components', components)
      this.$emit('change', false)
    },
    async loadFirstEquipment() {
      this.firstLoading = true
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { shipCode: this.searchObj.shipCode, size: 2000 },
      )
      const { records } = data
      this.firstEquipment = records?.map((i) => {
        return {
          text: i.equipmentEname,
          value: i.id,
        }
      })
      this.firstLoading = false
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
