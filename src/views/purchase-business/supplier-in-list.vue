<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.applyType"
            :items="[
              { text: '准入', value: 1 },
              { text: '退出', value: 2 },
              { text: '评级', value: 3 },
              { text: '慎用', value: 4 },
            ]"
            label="申请类型"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'supplier-in-detail', params: { id: 'new' } }"
          v-permission="['供应商准入:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增准入
        </v-btn> -->
      </template>
      <template v-slot:[`item.applyType`]="{ item }">
        <v-chip small v-if="item.applyType == 1">准入</v-chip>
        <v-chip small color="warning" v-if="item.applyType == 2">退出</v-chip>
        <v-chip small color="warning" v-if="item.applyType == 4">慎用</v-chip>
        <v-chip small color="error" v-if="item.applyType == 3">评级</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'supplier-in-list',
  created() {
    this.tableName = '供应商准入/退出/评级/慎用'
    this.reqUrl = ''
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.reqUrl = '/business/shipAffairs/supplierInOut/list'
    this.headers = [
      { text: '申请单号', value: 'applicationNo' },
      { text: '申请类型', value: 'applyType' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      // { text: 'SAP代码', value: 'sapCode' },
      // { text: '供应商名称', value: 'name' },
      // { text: '英文名称', value: 'nameEn' },
      // // { text: '账号', value: 'account' },
      // { text: '账号', value: 'sapCode' },
      // { text: '邮箱', value: 'supplierMail' },
    ]
    this.fuzzyLabel = '模糊查询'
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'supplier-in-detail' }
  },

  data() {
    return {
      searchObj: {
        isMe: true,
      },
      selected: false,
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
