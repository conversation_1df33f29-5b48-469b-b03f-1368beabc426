<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">报文明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="费用项目表头"
      :items="list"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
// cardCode	固定资产卡片编码	string
// ccyCode	币种 三位码如usd	string
// dataRow	数据包行号	integer
// id	物理主键	string
// innerCode	内部订单 限A类	string
// money	金额	number
// orderCode	发票号	string
// purchaseOrderCode	采购订单号	string
// sapCenterCode	利润中心	string
// sapCode	SAP科目代码	string
// sapCostType	费用类型 A: 厂修, B: 常规维修, C:船员奖励, D:船东费用,E:物料存货；F:金额调整	string
// sapId	sap报文id	string
// shipCode	船舶编码	string
// shipName	船舶名称	string
// supplySpaCode	供应商spa编号	string
// uuid	UUID业务系统对于这笔业务的唯一标识符	string
export default {
  name: 'cost-sap-mes-002',
  created() {
    this.费用项目表头 = [
      { text: '数据包行号', value: 'dataRow' },
      { text: 'SAP科目代码', value: 'sapCode' },
      { text: '费用类型', value: 'sapCostType' },
      { text: '金额', value: 'money' },
      { text: '币种', value: 'ccyCode' },
      { text: '发票号', value: 'orderCode' },
      { text: '采购订单号', value: 'purchaseOrderCode' },
      { text: '内部订单', value: 'innerCode' },
      { text: '供应商spa编号', value: 'supplySpaCode' },
      { text: '利润中心', value: 'sapCenterCode' },
      { text: '固定资产卡片编码', value: 'cardCode' },
    ]
  },
  props: {
    itemId: String,
  },
  data() {
    return {
      list: [],
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/costSap/getById/${this.itemId}`,
      )
      this.list = data.list
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
