<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">报文明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="费用项目表头"
      :items="list"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
// businessCode	业务代码;STI 盘盈/STO 盘亏/SLO 领用	string
// id	物理主键	string
// itemCode	物件编号（sap编码）	string
// itemId	物件业务系统id	string
// itemName	物件名称	string
// itemNo	物件编码	string
// itemNumber	物件数量	integer(int32)
export default {
  name: 'cost-sap-mes-003',
  created() {
    this.费用项目表头 = [
      { text: '业务代码', value: 'businessCode' },
      { text: '物件编号', value: 'itemCode' },
      { text: '物件名称', value: 'itemName' },
      { text: '物件编码', value: 'itemNo' },
      { text: '物件数量', value: 'itemNumber' },
      { text: '单船利润中心', value: 'vslPtr' },
      { text: '资产编号', value: 'assetId' },
    ]
  },
  props: {
    itemId: String,
  },
  data() {
    return {
      list: [],
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/businessCostController/getDetailOfJMM003',
        { costSapId: this.itemId },
      )
      this.list = data
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
