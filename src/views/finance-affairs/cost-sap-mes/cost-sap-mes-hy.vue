<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">报文明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="费用项目表头"
      :items="list"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
// cardCode	固定资产卡片编码	string
// ccyCode	币种 三位码如usd	string
// dataRow	数据包行号	integer
// id	物理主键	string
// innerCode	内部订单 限A类	string
// money	金额	number
// orderCode	发票号	string
// purchaseOrderCode	采购订单号	string
// sapCenterCode	利润中心	string
// sapCode	SAP科目代码	string
// sapCostType	费用类型 A: 厂修, B: 常规维修, C:船员奖励, D:船东费用,E:物料存货；F:金额调整	string
// sapId	sap报文id	string
// shipCode	船舶编码	string
// shipName	船舶名称	string
// supplySpaCode	供应商spa编号	string
// uuid	UUID业务系统对于这笔业务的唯一标识符	string
export default {
  name: 'cost-sap-mes-hy',
  created() {
    this.费用项目表头 = [
      { text: '数据包行号', value: 'rowId' },
      { text: '接口ID', value: 'intId' },
      { text: '单船利润中心', value: 'vslPtr' },
      { text: '业务系统船舶代码', value: 'vslCde' },
      { text: '船舶名称', value: 'vslName' },
      { text: '物料编号', value: 'materielId' },
      { text: '费用代码', value: 'freCde' },
      { text: '资产编号', value: 'assetId' },
      { text: '业务系统批次号', value: 'charg' },
      { text: '业务类型代码', value: 'buscd' },
      { text: '业务系统入库单号', value: 'warelstId' },
      { text: '入库单行号', value: 'warelstRowid' },
      { text: '供应商', value: 'vendorId' },
      { text: '发票号', value: 'invoiceNo' },
      { text: '采购订单号', value: 'ponum' },
      { text: '数量', value: 'quantity' },
      { text: '数量单位', value: 'unit' },
      { text: '金额', value: 'amount' },
      { text: '货币码', value: 'currency' },
      { text: '文本', value: 'text' },
      { text: '业务系统唯一标识符', value: 'busUuid' },
      { text: '调整标志', value: 'resv1' },
      { text: '预留2', value: 'resv2' },
      { text: '预留3', value: 'resv3' },
      { text: '预留4', value: 'resv4' },
    ]
  },
  props: {
    itemId: String,
  },
  data() {
    return {
      list: [],
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/costSapNew/getById/${this.itemId}`,
      )
      this.list = data.list
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
