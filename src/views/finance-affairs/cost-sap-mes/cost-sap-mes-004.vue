<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">报文明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="mainType == 'MATERIALS' ? 费用项目表头 : 资产表头"
      :items="list"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
// cardCode	固定资产卡片编码	string
// ccyCode	币种 三位码如usd	string
// dataRow	数据包行号	integer
// id	物理主键	string
// innerCode	内部订单 限A类	string
// money	金额	number
// orderCode	发票号	string
// purchaseOrderCode	采购订单号	string
// sapCenterCode	利润中心	string
// sapCode	SAP科目代码	string
// sapCostType	费用类型 A: 厂修, B: 常规维修, C:船员奖励, D:船东费用,E:物料存货；F:金额调整	string
// sapId	sap报文id	string
// shipCode	船舶编码	string
// shipName	船舶名称	string
// supplySpaCode	供应商spa编号	string
// uuid	UUID业务系统对于这笔业务的唯一标识符	string
export default {
  name: 'cost-sap-mes-004',
  created() {
    this.费用项目表头 = [
      { text: '数据包行号', value: 'rowId' },
      { text: '接口ID', value: 'intId' },
      { text: '物料编号', value: 'matnr' },
      { text: '物料类型', value: 'mtart' },
      { text: '行业', value: 'mbrsh' },
      { text: '工厂', value: 'werks' },
      { text: '存储地点', value: 'lgort' },
      { text: '物料描述', value: 'maktx' },
      { text: '基本计量单位', value: 'meins' },
      { text: '批次管理标识', value: 'xchpf' },
      { text: '利润中心', value: 'prctr' },
      { text: '评估类', value: 'bklas' },
      { text: '错误消息', value: 'msgStr' },
      { text: '错误类型', value: 'msgType' },
    ]
    this.资产表头 = [
      { text: '数据包行号', value: 'rowId' },
      { text: '接口ID', value: 'intId' },
      { text: '采购订单号', value: 'ponum' },
      { text: '公司代码', value: 'bukrs' },
      { text: '资产类', value: 'anlkl' },
      { text: '资产描述', value: 'txt50' },
      { text: '资产细类', value: 'ord41' },
      { text: '经济用途', value: 'ord42' },
      { text: '折旧码', value: 'afasl' },
      { text: '计划年使用期', value: 'ndjar' },
      { text: '计划使用期间', value: 'ndper' },
      // { text: '', value: '' },
      // { text: '', value: '' },
      // { text: '', value: '' },
      // { text: '', value: '' },
      { text: '利润中心', value: 'prctr' },
      { text: 'SAP资产编码', value: 'asset' },
      { text: '错误消息', value: 'msgStr' },
      { text: '错误类型', value: 'msgType' },
    ]
  },
  props: {
    itemId: String,
    mainType: String,
  },
  data() {
    return {
      list: [],
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/costSapNew/get004ById/${this.itemId}`,
      )
      this.list = data.list004
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
