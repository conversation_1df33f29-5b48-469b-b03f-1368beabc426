<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selecteds"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      show-expand
      use-ship
      :single-select="false"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            v-model="searchObj.year"
            label="年度"
            outlined
            clearable
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.status"
            :items="status"
            label="状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            readonly
            disabled
            v-model="searchObj.sapType"
            :items="sapTypes"
            label="数据包类型"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="业务代码"
            outlined
            dense
            clearable
            v-model="searchObj.businessCode"
          ></v-text-field>
        </v-col>
        <!-- <v-col cols="12" md="2">
          <v-text-field
            label="凭证号"
            outlined
            dense
            clearable
            v-model="searchObj.orderId"
          ></v-text-field>
        </v-col> -->
        <v-col cols="12" md="2">
          <v-text-field
            label="数据库表唯一标识"
            outlined
            dense
            clearable
            v-model="searchObj.dataUuid"
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :loading="loading"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="downloadExcel"
          v-permission="['费用SAP报文:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          :disabled="!canSend"
          @click="sendSapMsg"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['费用SAP报文:发送报文']"
        >
          <v-icon left>mdi-send</v-icon>
          发送报文
        </v-btn>
        <v-btn
          :disabled="!canYingshe"
          @click="mapMsg"
          outlined
          tile
          color="primary"
          class="mx-1"
          v-permission="['费用SAP报文:映射']"
        >
          <v-icon left>mdi-circle-multiple</v-icon>
          映射
        </v-btn>
        <v-btn
          :disabled="selected.status != 24 && selected.status != 25"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delVoucher"
          v-permission="['费用SAP报文:手工作废']"
        >
          <v-icon left>mdi-cancel</v-icon>
          手工作废
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="refreshVoucher"
          v-permission="['费用SAP报文:刷新报文状态']"
        >
          <v-icon left>mdi-refresh</v-icon>
          刷新报文状态
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="success"
          v-permission="['费用SAP报文:手工成功']"
        >
          <v-icon left>mdi-check</v-icon>
          手工成功
        </v-btn>
        <!-- 手工冲销 -->
        <!-- <v-btn
          :disabled="!canWriteOff"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="writeOff"
          v-permission="['费用SAP报文:手工冲销']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          手工冲销
        </v-btn> -->
        <!-- TODO:后续删除手动执行成功和失败的接口 -->
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="success"
          v-permission="['费用SAP报文:手工成功']"
        >
          <v-icon left>mdi-check</v-icon>
          手工成功
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="fail"
          v-permission="['费用SAP报文:手工失败']"
        >
          <v-icon left>mdi-close</v-icon>
          手工失败
        </v-btn> -->
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ statues[item.status] }}
      </template>
      <template v-slot:[`item.repairFlag`]="{ item }">
        {{ item.repairFlag ? '是' : '否' }}
      </template>
      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length">
          <cost-sap-mes-001
            v-if="item.sapType == 'JMM001'"
            :item-id="item.id"
          />
          <cost-sap-mes-002
            v-if="item.sapType == 'JMM002'"
            :item-id="item.id"
          />
          <cost-sap-mes-003
            v-if="item.sapType == 'JMM003'"
            :item-id="item.id"
          />
        </td>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import { stateMap, statues } from '../private/constant'
import costSapMes001 from './cost-sap-mes-001.vue'
import CostSapMes002 from './cost-sap-mes-002.vue'
import CostSapMes003 from './cost-sap-mes-003.vue'
// actualAccountDate	实际记账日期	string
// apiCreateTime	接口数据生成时间	string
// businessCode	业务代码	string
// businessHappenDate	业务发生日期	string
// companyCode	公司代码	string
// dataNum	数据包个数	integer
// dataUuid	数据库表唯一标识	string
// errorMsg	错误消息	string
// id	物理主键	string
// orderId	费用凭证id	string
// originSys	来源系统	string
// remark	备注	string
// repairFlag	是否坞修 0否 1是	integer
// sapCenterCode	SAP 利润中心代码;做重复值校验，确保编号唯一性	string
// shipCode	船舶编码	string
// shipName	船舶名	string
// status	状态 20:未通过映射 21:映射错误 22:报文错误 23:已通过映射 24:已发送SAP 25:SAP审批未通过30:付款审批中 31:付款审批未通过 32:付款审批通过 33:已付款 34:付款审批未提交	integer
// supply	供应商	string

export default {
  components: { costSapMes001, CostSapMes002, CostSapMes003 },
  name: 'cost-sap-mes-list',
  created() {
    this.tableName = '费用SAP报文'
    this.reqUrl = '/business/shipAffairs/costSap/page'
    this.headers = [
      { text: '', value: 'data-table-expand' },
      { text: '船舶', value: 'shipName', sortable: false },
      { text: '业务代码', value: 'businessCode' },
      { text: '公司代码', value: 'companyCode' },
      {
        text: '费用凭证号',
        value: 'orderId',
        sortable: false,
      },
      { text: '数据包个数', value: 'dataNum', hideDefault: true },
      { text: '数据包分类', value: 'sapType', hideDefault: true },
      { text: '数据库表唯一标识', value: 'dataUuid', sortable: false },
      { text: '接口数据生成时间', value: 'apiCreateTime' },
      { text: '实际记账日期', value: 'actualAccountDate' },
      { text: '业务发生日期', value: 'businessHappenDate' },
      { text: 'SAP实际过账日期', value: 'buDate' },
      { text: '来源系统', value: 'originSys' },
      { text: '状态', value: 'status' },
      { text: '坞修', value: 'repairFlag' },
      // { text: '供应商', value: 'supply', hideDefault: true },
      { text: '供应商', value: 'supplyName', hideDefault: true },
      { text: '错误消息', value: 'errorMsg' },
    ]
    this.statues = stateMap
    this.status = statues
    this.searchDate = {
      label: '',
      value: '',
    }
    this.sapTypes = ['JMM001', 'JMM002', 'JMM003']
  },

  data() {
    return {
      selecteds: [],
      searchObj: {
        year: '',
        status: '',
        sapType: this.$route.meta.sapType,
        isBudget: this.$route.meta.isBudget,
      },
      loading: false,
    }
  },

  computed: {
    selected: {
      get() {
        if (this.selecteds.length > 1) return false
        return this.selecteds[0] || false
      },
      set(val) {
        this.selecteds = val ? [val] : []
      },
    },
    canSend() {
      return (
        this.selecteds.length > 0 &&
        this.selecteds.every(
          (item) => item.status == 23 || item.status == 22 || item.status == 27,
        )
      )
    },
    canYingshe() {
      return (
        this.selecteds.length > 0 &&
        this.selecteds.every((item) => item.status == 20 || item.status == 21)
      )
    },
    canWriteOff() {
      return this.selected.status == 26 && this.selected.originSys != '0002'
    },
  },

  methods: {
    async delVoucher() {
      if (!(await this.$dialog.msgbox.confirm('确定作废此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costSap/handCancel/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('作废成功')
        this.selected = false
      }
      await this.$refs.table.loadTableData()
    },
    async writeOff() {
      if (!(await this.$dialog.msgbox.confirm('确定冲销此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costSap/handTrade/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('冲销成功')
        this.selected = false
      }
      await this.$refs.table.loadTableData()
    },
    async sendSapMsg() {
      if (!(await this.$dialog.msgbox.confirm('确定发送所选记录？'))) return
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/costSap/sendBatchSap',
        this.selecteds.map((item) => item.id),
      )
      if (!errorRaw) {
        this.$dialog.message.info(data)
        this.selecteds = []
      }
      await this.$refs.table.loadTableData()
    },

    async mapMsg() {
      if (!(await this.$dialog.msgbox.confirm('确定映射此记录？'))) return
      // const { errorRaw } = await this.getAsync(
      //   `/business/shipAffairs/costSap/mapSap/${this.selected.id}`,
      // )
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costSap/sendBatchMapSap',
        this.selecteds.map((item) => item.id),
      )
      if (!errorRaw) {
        this.$dialog.message.success('映射成功')
        this.selecteds = []
      }
      await this.$refs.table.loadTableData()
    },
    async success() {
      if (!(await this.$dialog.msgbox.confirm('确定成功此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costSap/executeSap/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('执行成功')
        this.selected = false
      }
      await this.$refs.table.loadTableData()
    },
    async fail() {
      if (!(await this.$dialog.msgbox.confirm('确定失败此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costSap/executeFailSap/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('执行失败')
        this.selected = false
      }
      await this.$refs.table.loadTableData()
    },
    async refreshVoucher() {
      const { errorRaw } = await this.postAsync(
        `/monitor/job/run?jobId=1592863103117307123`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('执行成功')
      }
      await this.$refs.table.loadTableData()
    },
    async downloadExcel() {
      this.loading = true
      let params = { ...this.$refs.table.searchRemain }
      params = {
        ...params,
        // fuzzyParam: this.$refs.table.fuzzyParam,
        // shipCode: this.$refs.table.ship,
        // fromTime: this.dates?.start?.toISOString()?.split('T')?.[0],
        // toTime: this.dates?.end?.toISOString()?.split('T')?.[0],
        // fromTime2: this.dates2?.start?.toISOString()?.split('T')?.[0],
        // toTime2: this.dates2?.end?.toISOString()?.split('T')?.[0],
      }
      await this.getBlobDownload(
        '/business/shipAffairs/costSap/excelExport',
        params,
        // 时间戳后四位
        `费用SAP报文-${new Date().getTime().toString().slice(-4)}.xlsx`,
      )
      this.loading = false
    },
  },

  mounted() {},
}
</script>

<style></style>
