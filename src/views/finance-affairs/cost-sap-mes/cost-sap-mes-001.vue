<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">报文明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="费用项目表头"
      :items="list"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
// costSubjectId	成本类科目sap科目代码	string
// currencyCode	币种编码	string
// currencyId	币种id	string
// id	物理主键	string
// inoutCode	入库单号	string
// inoutMode	出入库表示：I（大写英文字母i）	string
// inoutSingleProjectNo	入库单行项目号	string
// itemCode	物件SAP编号	string
// itemId	物件id	string
// itemName	物件名称	string
// itemNo	物件编码	string
// itemNumber	数量	number
// itemPrice	金额	number
// mainId	主表id	string
// orderNo	采购订单号	string
// supplierCode	供应商编号（供应商sap编号）	string
// supplierId	供应商id	string
// supplierName	供应商名称	string
export default {
  name: 'cost-sap-mes-001',
  created() {
    this.费用项目表头 = [
      { text: 'SAP科目代码', value: 'costSubjectId' },
      { text: '币种', value: 'currencyCode' },
      { text: '出入库', value: 'inoutMode' },
      { text: '入库单号', value: 'inoutCode' },
      { text: '入库单行项目号', value: 'inoutSingleProjectNo' },
      { text: '物件SAP编号', value: 'itemCode' },
      { text: '物件名称', value: 'itemName' },
      { text: '物件编码', value: 'itemNo' },
      { text: '数量', value: 'itemNumber' },
      { text: '金额', value: 'itemPrice' },
      { text: '采购订单号', value: 'orderNo' },
      { text: '供应商sap', value: 'supplierCode' },
      { text: '供应商名称', value: 'supplierName' },
    ]
  },
  props: {
    itemId: String,
  },
  data() {
    return {
      list: [],
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/businessCostController/getDetailOfJMM001',
        { costSapId: this.itemId },
      )
      this.list = data
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
