<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="shipParams"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="shipParams.status"
            outlined
            label="审批状态"
            dense
            :items="statusMap"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="shipParams.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="shipParams.businessStatus"
            :items="businessStatusList"
            label="业务状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-btn
          :disabled="!canPDF"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="dowloadPDF"
          v-permission="['批量预算:下载PDF']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          下载PDF
        </v-btn>
        <a v-if="true" :href="downPDF" ref="downPDFHref"></a> -->
        <!-- <v-btn
          outlined
          tile
          color="warning"
          class="mx-1"
          v-permission="['批量预算:生成预算数据']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成预算数据
        </v-btn>
        <v-btn
          outlined
          tile
          color="warning"
          class="mx-1"
          v-permission="['批量预算:生成费用项目&费用凭证']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成费用项目&费用凭证
        </v-btn> -->
        <!-- <v-btn
          :disabled="selected.businessStatus != '已生成费用项目'"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="genSapMes"
          v-permission="['批量预算:生成报文']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成报文
        </v-btn> -->
        <!-- <v-btn
          :to="{ name: 'batch-cost-detail', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['批量预算:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn> -->
        <v-btn
          :disabled="
            selected.businessStatus != '未提交' &&
            selected.businessStatus != '待生成预算数据' &&
            selected.applicantId == userId
          "
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['批量预算:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses[item.status] }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'batch-cost-list',
  created() {
    this.tableName = '批量预算列表'
    this.reqUrl = '/business/shipAffairs/batchCostApply/page'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      //   { text: '所属船舶', value: 'shipInfo' },
      //{ text: '船舶编码', value: 'shipCode' },
      { text: '申请编号', value: 'applicationNo' },
      { text: '费用科目', value: 'subjectName' },
      { text: '供应商', value: 'supplyName' },
      { text: '币种', value: 'ccyCode' },
      { text: '预算总金额', value: 'total' },
      { text: '预算折算美金', value: 'totalToUsd' },
      { text: '付款总金额', value: 'finalTotal' },
      { text: '付款折算美金', value: 'finalTotalToUsd' },
      { text: '付款公司', value: 'paymentCompany' },
      { text: '创建时间', value: 'createTime' },
      { text: '更新时间', value: 'updateTime' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '创建时间期间',
      interval: true,
    }
    this.pushParams = { name: 'batch-cost-detail' }
    this.businessStatusList = [
      // { text: '', value: '' },
      // { text: '', value: '' },
      // { text: '', value: '' },
      { text: '审批通过', value: '审批通过' },
      {
        text: '发票待提交',
        value: '预算数据已生成,费用项目、费用凭证已部分生成',
      },
      { text: '预算数据已生成', value: '预算数据已生成' },
      {
        text: '费用项目、费用凭证已部分生成',
        value: '费用项目、费用凭证已部分生成',
      },
    ]
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
    ]
    this.statuses = ['暂无审批', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', '', 'warning', 'success', 'error']
  },

  data() {
    return {
      selected: false,
      useName: false,
      shipParams: {
        shipCode: '',
        department: '',
        auditFlag: true,
        isMe: true,
        status: '2',
        businessStatus: '',
      },
      downPDF: '',
      loading: false,
      userId: this.$local.data.get('userInfo').id,
    }
  },
  computed: {
    canPDF() {
      return (
        this.selected.status == 3 ||
        this.selected.businessStatus == '财务主管' ||
        this.selected.businessStatus == '财务经理'
      )
    },
  },
  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/batchCostApply/batchCostApplyDelete',
        { applyId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async genSapMes() {
      const ids = []
      // const ids = this.selected.id
      ids[0] = this.selected.id
      console.log(ids)
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/batchCostApply/createSapList',
        ids,
      )
      if (!errorRaw) this.$dialog.message.success('生成成功')
      await this.$refs.table.loadTableData()
    },
    async dowloadPDF() {
      // const ids = this.selected.map((item) => item.id)
      const ids = []
      ids[0] = this.selected.id
      const { data } = await this.postAsync(
        '/business/shipAffairs/batchCostApply/createBatchCostPDFList',
        ids,
      )
      if (data) {
        console.log(data)
        data.forEach((item) => {
          this.dowloadPDFs(item)
        })
      }
    },
    async dowloadPDFs(item) {
      this.downPDF = `/api/system/file/download?fileName=${encodeURIComponent(
        item.fileName,
      )}&filePath=${item.filePath}`
      console.log(this.$refs.downPDFHref)

      const link = this.$refs.downPDFHref
      link.href = this.downPDF
      link.download = this.extractFilename(this.downPDF)
      link.style.display = 'none'
      document.body.appendChild(link)

      // 模拟点击<a>标签以触发下载
      link.click()
    },
    extractFilename(url) {
      return url.substring(url.lastIndexOf('/') + 1)
    },
  },

  mounted() {
    console.log('this.$route.query.status:', this.$route.query.status)
    if (this.$route.query.status != undefined) {
      this.shipParams.status = this.$route.query.status
      this.shipParams.businessStatus = this.$route.query.businessStatus
    }
  },
}
</script>

<style></style>
