<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['批量预算:编辑']"
      :title="`批量预算-${isEdit ? detail.applicationNo : '新增'}`"
      :tooltip="isEdit ? detail.applicationNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="detail.status == '3' ? subtitles2 : subtitles"
      :can-save="
        detail.status == 1 || detail.status == 4 || detail.status == ''
      "
      :can-submit="
        !(
          detail.businessStatus == '待生成预算数据' ||
          detail.businessStatus == '预算数据已生成' ||
          detail.status == '3'
        )
      "
      @save="save"
      @submit="submit"
    >
      <template v-slot:custombtns>
        <v-btn
          v-if="detail.businessStatus == '待生成预算数据'"
          tile
          color="error"
          small
          class="mx-1"
          @click="genBudgetData"
          v-permission="['批量预算:生成预算数据']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成预算数据
        </v-btn>
        <!-- <v-btn
          v-if="detail.businessStatus == '预算数据已生成'"
          tile
          color="error"
          small
          class="mx-1"
          @click="genCostProjectData"
          v-permission="['批量预算:生成费用项目&费用凭证']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成费用项目&费用凭证
        </v-btn>
        <v-btn
          v-if="detail.businessStatus == '费用项目、费用凭证已生成'"
          tile
          color="error"
          small
          class="mx-1"
          @click="delCostProjectData"
          v-permission="['批量预算:删除费用项目&费用凭证']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          删除费用项目&费用凭证
        </v-btn> -->
        <v-btn
          v-if="
            detail.status == '3' &&
            detail.businessStatus != '废弃' &&
            detail.businessStatus != '费用项目、费用凭证已全部生成'
          "
          :loading="loading"
          tile
          color="error"
          small
          class="mx-1"
          @click="save2"
          v-permission="['批量预算:生成费用项目&费用凭证']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          保存发票信息
        </v-btn>
        <v-btn
          v-if="
            detail.status == '3' &&
            detail.businessStatus != '废弃' &&
            detail.businessStatus != '费用项目、费用凭证已全部生成'
          "
          :loading="loading"
          tile
          color="error"
          small
          class="mx-1"
          @click="genCostProjectData"
          v-permission="['批量预算:生成费用项目&费用凭证']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          保存并提交发票
        </v-btn>
        <v-btn
          v-if="detail.status == '3'"
          :loading="loading"
          tile
          color="error"
          small
          class="mx-1"
          @click="delCostProjectData"
          v-permission="['批量预算:删除费用项目&费用凭证']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          删除发票
        </v-btn>
      </template>
      <template
        v-if="detail.auditParams && detail.auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #发票信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col md="3" cols="12">
                <v-text-field
                  label="订单号"
                  v-model="detail.applicationNo"
                  outlined
                  dense
                  disabled
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  label="发票编号"
                  v-model="detail.invoiceCode"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="!canEditPay"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :disabled="!canEditPay"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-if="isEdit"
                  outlined
                  dense
                  v-model="detail.ccyCode"
                  :rules="[rules.required]"
                  readonly
                  label="发票币种"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="发票总金额"
                  v-model="detail.finalTotal"
                  outlined
                  dense
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #结算信息>
        <v-form ref="formPay">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="供应商"
                  dense
                  outlined
                  v-model="detail.supplyName"
                  readonly
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  outlined
                  dense
                  label="开户银行"
                  v-model="detail.openBank"
                  :rules="[rules.required]"
                  :items="banks"
                  item-text="bank"
                  return-object
                  @change="
                    () => {
                      detail.accountBank = detail.openBank.account
                      detail.openBank = detail.openBank.bank
                    }
                  "
                ></v-select>
                <!-- <v-text-field
                  outlined
                  dense
                  label="开户银行"
                  v-model="detail.openBank"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field> -->
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="银行账户"
                  v-model="detail.accountBank"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-handler
                  label="申请人"
                  readonly
                  v-model="detail.applicantId"
                  :init-user="initUser"
                  :use-current="!isEdit"
                  :rules="[rules.required]"
                ></v-handler>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  :disabled="!canEdit"
                  label="实际申请人"
                  item-text="nickName"
                  v-model="applyPerson"
                  :headers="userHeaders"
                  req-url="/system/user/page"
                  :init-selected="initApplyPerson"
                  :search-remain="searchObj"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="用户名"
                        v-model="searchObj.nickName"
                        outlined
                        dense
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="6" md="6">
                      <treeselect
                        v-model="searchObj.deptId"
                        :options="deptTree"
                        placeholder="请选择部门"
                        outlined
                        dense
                      />
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.applyDeptName"
                  label="申请部门"
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="申请时间"
                  v-model="detail.applyDate"
                  use-today
                  outlined
                  dense
                  readonly
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3" v-if="false">
                <v-select
                  v-model="detail.needApprove"
                  :items="[
                    { text: '是', value: true },
                    { text: '否', value: false },
                  ]"
                  label="是否需要审批"
                  dense
                  required
                  outlined
                  :disabled="isEdit"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :disabled="isEdit"
                  v-model="detail.applyType"
                  :items="[
                    { text: '常规', value: 1 },
                    { text: '紧急', value: 2 },
                  ]"
                  label="申请类型"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.supplyItem"
                  label="供应项目"
                  dense
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="detail.status != '3'">
                <vs-date-picker
                  v-model="detail.proposedDate"
                  label="发生日期"
                  dense
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  required
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <port-select-dialog2
                  v-if="!isEdit"
                  @select="
                    (p) => {
                      detail.deliveryPlace = p.portCn
                    }
                  "
                  :rules="[rules.required]"
                ></port-select-dialog2>
                <v-text-field
                  label="港口"
                  v-else
                  dense
                  outlined
                  v-model="detail.deliveryPlace"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="detail.subjectId"
                  :init-selected="detail.initSubject"
                  :search-dicts="searchDicts"
                  :search-remain="searchObj"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  :readonly="!canEdit"
                  required
                  dense
                  fuzzy-label="模糊搜索"
                  :rules="[rules.required]"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :readonly="!canEdit"
                  :items="付款公司选项"
                  label="付款公司"
                  v-model="detail.paymentCompany"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-supply-select2
                  :readonly="!canEdit"
                  v-model="detail.supplyId"
                  :payment-company="detail.paymentCompany"
                  @select="
                    (item) => {
                      currency = item.currency
                    }
                  "
                  :init-selected="initSupply"
                  :rules="[rules.required]"
                ></v-supply-select2>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-if="isEdit"
                  outlined
                  dense
                  v-model="detail.ccyCode"
                  :rules="[rules.required]"
                  readonly
                  label="币种"
                ></v-text-field>
                <v-select
                  v-else
                  v-model="detail.currencyObj"
                  :items="currency"
                  item-text="ccyCode"
                  return-object
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  label="币种"
                  outlined
                  dense
                  :disabled="currency.length === 0"
                  @change="
                    (item) => {
                      currencyId = item.currencyType
                      detail.bankDesc = item.bank + item.account
                    }
                  "
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="预算总金额"
                  v-model="detail.total"
                  outlined
                  dense
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="预算总金额折算美金"
                  v-model="detail.totalToUsd"
                  outlined
                  dense
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="9"></v-col>
              <!-- 生成费项时候填写 -->
              <!-- <v-col
                cols="12"
                md="3"
                v-if="
                  detail.businessStatus == '预算数据已生成' ||
                  detail.businessStatus == 'OA立项审批通过' ||
                  detail.status == '3'
                "
              >
                <v-text-field
                  label="发票编号"
                  v-model="detail.invoiceCode"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="!canEditPay"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="3"
                v-if="
                  detail.businessStatus == '预算数据已生成' ||
                  detail.businessStatus == 'OA立项审批通过' ||
                  detail.status == '3'
                "
              >
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :disabled="!canEditPay"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col> -->
              <v-col
                cols="12"
                md="3"
                v-if="
                  detail.businessStatus == '预算数据已生成' ||
                  detail.businessStatus == 'OA立项审批通过' ||
                  detail.status == '3'
                "
              >
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.happenDate"
                  label="发生日期"
                  :rules="[rules.required]"
                  :disabled="!canEditPay"
                ></vs-date-picker>
              </v-col>
              <!-- <v-col
                cols="12"
                md="3"
                v-if="
                  detail.businessStatus == '预算数据已生成' ||
                  detail.businessStatus == 'OA立项审批通过' ||
                  detail.status == '3'
                "
              >
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col> -->
              <v-col
                cols="12"
                md="3"
                v-if="
                  detail.businessStatus == '预算数据已生成' ||
                  detail.businessStatus == 'OA立项审批通过' ||
                  detail.status == '3'
                "
              >
                <v-text-field
                  label="付款总金额"
                  v-model="detail.finalTotal"
                  outlined
                  dense
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="3"
                v-if="
                  detail.businessStatus == '预算数据已生成' ||
                  detail.businessStatus == 'OA立项审批通过' ||
                  detail.status == '3'
                "
              >
                <v-text-field
                  label="付款总金额折算美金"
                  v-model="detail.finalTotalToUsd"
                  outlined
                  dense
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12">银行信息:{{ detail.bankDesc }}</v-col>
              <v-col cols="12" md="3" v-if="!isEdit">
                <v-btn
                  tile
                  color="error"
                  class="mx-1"
                  @click="dowExcel"
                  :disabled="!detail.ccyCode"
                >
                  <v-icon left>mdi-arrow-collapse-down</v-icon>
                  下载实际申请人分管船舶EXCEL模板
                </v-btn>
              </v-col>
              <v-col cols="12" md="3" v-if="!isEdit">
                <v-file-input
                  outlined
                  dense
                  accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
                  label="导入EXCEL"
                  v-model="file"
                  :disabled="!detail.ccyCode"
                ></v-file-input>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  :readonly="isEdit"
                  v-model="detail.remark"
                  dense
                  outlined
                  label="备注"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
            <v-attach-list
              :attachments="detail.attachmentRecords"
              @change="changeAttachment"
            ></v-attach-list>
          </v-form>
        </v-container>
      </template>
      <!-- <template #付款信息>
        <v-container fluid>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="发票编号"
                  v-model="detail.invoiceCode"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :readonly="!canEdit"
                  :rules="[canEdit ? rules.required : true]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.happenDate"
                  :readonly="!canEdit"
                  label="发生日期"
                  :rules="[canEdit ? rules.required : true]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="付款总金额"
                  v-model="detail.total"
                  outlined
                  dense
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="付款总金额折算美金"
                  v-model="detail.totalToUsd"
                  outlined
                  dense
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">银行信息:{{ detail.bankDesc }}</v-col>
              <v-col cols="12">
                <v-textarea
                  :readonly="isEdit"
                  v-model="detail.remark"
                  dense
                  outlined
                  label="备注"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template> -->
      <template #批量预算明细按钮>
        <v-btn
          :disabled="!detail.ccyCode"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createShips"
          v-permission="['批量预算明细:新增']"
          v-if="!isEdit"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择船舶
        </v-btn>
        <v-btn
          :disabled="!canDel"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCom"
          v-permission="['批量预算明细:删除']"
          v-if="!isEdit"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #批量预算明细>
        <v-form ref="form2">
          <v-table-list2
            :headers="
              detail.businessStatus == '预算数据已生成' || detail.status == '3'
                ? headers2
                : headers
            "
            item-key="itemDetailId"
            :items="ships"
            :single-select="false"
            v-model="selectedItems"
          >
            <template v-if="!isEdit" v-slot:[`item.money`]="{ item }">
              <!-- <vue-numeric
                :readonly="isEdit"
                v-model="item.money"
                :precision="isJPY ? 0 : 2"
                style="border: 1px solid black"
                @blur="changeData1($event)"
                dense
              ></vue-numeric> -->
              <v-text-field
                :rules="[isJPY ? rules.numberRule2 : rules.numberRule1]"
                outlined
                dense
                required
                type="number"
                :readonly="isEdit"
                v-model="item.money"
                :precision="isJPY ? 0 : 2"
                @blur="changeData1($event)"
                @input="
                  item.money = isJPY
                    ? parseInt(item.money)
                    : parseFloat(item.money).toFixed(2)
                "
              ></v-text-field>
            </template>
            <template v-slot:[`item.finalMoney`]="{ item }">
              <!-- <vue-numeric
                v-if="!item.costProjectId"
                v-model="item.finalMoney"
                :precision="isJPY ? 0 : 2"
                style="border: 1px solid black; width: 100%"
                dense
                required
                @change="
                  () => {
                    if (item.finalMoney > item.money) {
                      item.overBudget = 1
                    } else {
                      item.overBudget = 0
                    }
                  }
                "
                outlined
              ></vue-numeric> -->
              <v-text-field
                v-if="!item.costProjectId"
                v-model="item.finalMoney"
                :rules="[isJPY ? rules.numberRule2 : rules.numberRule1]"
                outlined
                dense
                required
                type="number"
                @change="
                  () => {
                    item.finalMoney = isJPY
                      ? parseInt(item.finalMoney)
                      : parseFloat(item.finalMoney).toFixed(2)
                    if (item.finalMoney > item.money) {
                      item.overBudget = 1
                    } else {
                      item.overBudget = 0
                    }
                  }
                "
              ></v-text-field>
            </template>
            <template v-slot:[`item.overBudget`]="{ item }">
              <v-select
                v-model="item.overBudget"
                :items="[
                  { text: '是', value: 1 },
                  { text: '否', value: 0 },
                ]"
                label="是否超预算"
                readonly
                dense
                required
                outlined
              ></v-select>
            </template>
            <template v-slot:[`item.budgetRemark`]="{ item }">
              <v-text-field
                outlined
                dense
                :readonly="item.overBudget == 0"
                v-model="item.budgetRemark"
                label="超预算说明"
                :rules="item.overBudget == 0 ? [] : [rules.required]"
              ></v-text-field>
            </template>
            <template v-slot:[`item.costProjectStatus`]="{ item }">
              {{ stateMap[item.costProjectStatus] }}
            </template>
            <template v-slot:[`item.costOrderStatus`]="{ item }">
              {{ stateMap[item.costOrderStatus] }}
            </template>
            <template v-slot:[`item.toDetail`]="{ item }">
              <v-btn
                v-if="item.budgetId"
                :to="{
                  name: 'large-purchase-detail',
                  params: { id: item.budgetId },
                }"
                color="primary"
                text
                small
              >
                预算详情
              </v-btn>
              <!-- <v-btn
                v-if="item.costProjectId"
                :to="{
                  name: 'cost-project-detail',
                  params: { id: item.costProjectId },
                }"
                color="primary"
                text
                small
              >
                费用项目详情
              </v-btn> -->
              <v-btn
                v-if="item.costOrderId"
                :to="{
                  name: 'cost-voucher-reload',
                  params: { id: item.costOrderId },
                }"
                color="primary"
                text
                small
              >
                发票详情
              </v-btn>
            </template>
            <template v-slot:[`item.toUsd`]="{ item }">
              {{ (item.money * rate).toFixed(2) }}
            </template>
            <template v-if="!isEdit" v-slot:[`item.remark`]="{ item }">
              <v-text-field
                :readonly="isEdit"
                v-model="item.remark"
                dense
                outlined
                label="备注"
                :rules="[rules.required]"
              ></v-text-field>
            </template>
          </v-table-list2>
        </v-form>
      </template>
    </v-detail-view>
    <ship-select
      v-model="dialog"
      :searchRemain="searchObj"
      :ships.sync="ships"
    ></ship-select>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
// import VueNumeric from 'vue-numeric'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import PortSelectDialog2 from '../../maritime-maintence/components/port-select-dialog2.vue'
import routerControl from '@/mixin/routerControl'
import { stateMap, statues } from '../private/constant'
// '../components/port-select-dialog.vue'
export default {
  components: {
    // VueNumeric,
    Treeselect,
    PortSelectDialog2,
  },
  mixins: [currencyHelper, routerControl],
  name: 'batch-cost-detail',
  created() {
    this.backRouteName = 'batch-cost-list'
    this.subtitles = ['基本信息', '批量预算明细']
    this.subtitles2 = ['发票信息', '结算信息', '基本信息', '批量预算明细']
    this.headers = [
      { text: '船舶', value: 'chShipName' },
      { text: '预算金额', value: 'money' },
      // { text: '折算美金', value: 'toUsd' },
      { text: '备注', value: 'remark' },
    ]
    this.headers2 = [
      { text: '船舶', value: 'chShipName' },
      { text: '预算金额', value: 'money' },
      { text: '付款金额', value: 'finalMoney' },
      // { text: '折算美金', value: 'toUsd' },
      { text: '是否超预算', value: 'overBudget' },
      { text: '超预算说明', value: 'budgetRemark' },
      { text: '备注', value: 'remark' },
      { text: '详情', value: 'toDetail' },
      { text: '预算审批状态', value: 'budgetStatus' },
      // { text: '费用项目状态', value: 'costProjectStatus' },
      { text: '发票状态', value: 'costOrderStatus' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.userHeaders = [
      { text: '用户名', value: 'nickName' },
      { text: '岗位名称', value: 'deptName' },
      { text: '部门名称', value: 'parentDeptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
    this.付款公司选项 = ['3000', '3800', '8903', '3402']
    this.stateMap = stateMap
    this.status = statues
  },
  data() {
    return {
      detail: {
        term: '',
        list: [],
        status: '',
        ccyId: '',
        needApprove: true,
        needSendOa: 0,
      },
      dialog: false,
      initData: {},
      selected: false,
      detailsList: [],
      updateFlag1: true,
      updateFlag2: true,
      updateFlag3: true,
      shipCodes: [],
      selectedItems: [],
      canDel: false,
      searchObj: { efFlag: 1, nickName: '', deptId: '1', paymentCompany: '' },
      bankDesc: '',
      initSupply: {},
      currency: [],
      currencyId: '',
      applyPerson: {},
      initApplyPerson: '',
      deptTree: [],
      rules: {
        required: (v) => !!v || '必填项不能为空',
        required2: (v) =>
          (v !== null && v !== undefined && v !== '') || '必填项不能为空',
        isDate: (v) => (!!isNaN(v) && !isNaN(Date.parse(v))) || '格式错误',
        numberRule1: (v) => {
          // 检查v是否为数字,如果是,返回验证是否保留两位小数的结果,否则返回错误信息
          if (!isNaN(parseFloat(v)) && isFinite(v)) {
            // 使用正则表达式来检查是否有超过两位小数
            if (/^-?\d+(\.\d{1,2})?$/.test(v)) {
              return true
            } else {
              return '输入的数字必须保留两位小数'
            }
          } else {
            return '请输入数字'
          }
        },
        numberRule2: (v) => {
          // 检查v是否为数字，如果是，返回转换为整数的值，否则返回错误信息
          if (!isNaN(parseFloat(v))) {
            return parseInt(v) == v || '输入的数字必须为整数'
          } else {
            return '请输入数字'
          }
        },
      },
      ships: [],
      initUser: false,
      file: null,
      loading: false,
      banks: [],
    }
  },
  watch: {
    selectedItems(val) {
      if (val.length > 0) {
        this.canDel = true
      } else {
        this.canDel = false
      }
    },
    applyPerson(val) {
      if (val) {
        this.detail.initApply = val.id
        this.detail.applyDeptName = val.parentDeptName
      }
    },
    'detail.paymentCompany'(val) {
      if (val) {
        this.searchObj.paymentCompany = val
        // this.initSupply = {}
        // this.detail.ccyCode = ''
        // this.detail.bankDesc = ''
        this.updateFlag1 = true
        this.updateFlag2 = true
        this.updateFlag3 = true
      }
    },
    'detail.currencyObj'(val) {
      if (val) {
        this.updateFlag1 = true
        this.updateFlag2 = true
        this.updateFlag3 = true
        // while (this.detail.list.length > 0) {
        //   this.detail.list.pop()
        // }
        var totalPrice = 0
        this.ships.forEach((item) => {
          totalPrice = Number(totalPrice) + Number(item.money)
        })
        this.detail.total = totalPrice.toFixed(2)
        this.detail.totalToUsd = (totalPrice * this.rate).toFixed(2)
        this.detail.ccyCode = this.detail.currencyObj.ccyCode
      }
    },
    ships: {
      handler(val) {
        // console.log(val)
        var totalPrice = 0
        var finalTotalPrice = 0
        val.forEach((item) => {
          totalPrice = Number(totalPrice) + Number(item.money)
          finalTotalPrice = Number(finalTotalPrice) + Number(item.finalMoney)
        })
        this.detail.total = totalPrice.toFixed(2)
        this.detail.totalToUsd = (totalPrice * this.rate).toFixed(2)
        this.detail.finalTotal = finalTotalPrice.toFixed(2)
        this.detail.finalTotalToUsd = (finalTotalPrice * this.rate).toFixed(2)
      },
      deep: true,
    },
    detail: {
      handler(val) {
        if (val.happenDate && val.supplyId && val.paymentCompany) {
          this.loadHappenDate(val)
        }
      },
      deep: true,
    },
    currencyId(val) {
      this.detail.ccyId = val
    },
    'detail.invoiceCode'(val) {
      if (val) {
        console.log(val)
        this.checkDuplicate()
      }
    },
    file(val) {
      if (val) {
        this.importExcel()
      }
    },
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
      // return this.$route.params.id == 'new'
      //   ? false
      //   : this.detail.status == '1' || this.detail.status == '4'
      //   ? false
      //   : true
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    canEdit() {
      return this.detail.status != 1 && this.detail.status != 2
    },
    isJPY() {
      return (
        this.detail.currencyObj.ccyCode === 'JPY' ||
        this.detail.currencyObj.ccyCode === '日元' ||
        this.detail.ccyCode === 'JPY' ||
        this.detail.ccyCode === '日元'
      )
    },
    rate() {
      return this.currencyInfo.find(
        (i) => i.ccyCode === this.detail.currencyObj?.ccyCode,
      )?.rateToMain
    },
    canEditPay() {
      return (
        this.detail.businessStatus == '预算数据已生成' ||
        this.detail.businessStatus == 'OA立项审批通过'
      )
    },
  },

  methods: {
    createShips() {
      this.searchObj = {
        paymentCompany: this.detail.paymentCompany,
        userId: this.detail.initApply,
      }
      console.log(this.searchObj)
      this.dialog = true
    },
    async getDeptTreeList() {
      const { data } = await this.getAsync('/system/dept/getDeptTreeList')
      this.deptTree = data
    },
    async delCom() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const ids = this.selectedItems.map((i) => {
        return { itemDetailId: i.itemDetailId }
      })
      ids.forEach((item1) => {
        this.ships = this.ships.filter(
          (i) => i.itemDetailId !== item1.itemDetailId,
        )
      })
      this.canDel = false
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    changeData1(event) {
      if (event.target.value && this.updateFlag1) {
        this.ships.forEach((item) => {
          item.money = event.target.value
        })
        this.updateFlag1 = false
      }
    },
    changeData2(event) {
      if (event.target.value) {
        this.ships.forEach((item) => {
          item.finalMoney = event.target.value
        })
      }
    },
    getShipsWithOperation() {
      const ids = this.ships.map((i) => i.id)
      const delList = this.isEdit
        ? this.ships
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.ships.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return false
      }
      if (!this.$refs.form2.validate()) {
        return false
      }
      if (this.ships.length === 0) {
        this.$dialog.message.warning('请添加船舶')
        return false
      }
      const detailList = this.getShipsWithOperation()
      detailList.forEach((item) => {
        item.toUsd = (item.money * this.rate).toFixed(2)
      })
      this.detail.finalTotalToUsd = isNaN(this.detail.finalTotalToUsd)
        ? 0
        : this.detail.finalTotalToUsd
      this.detail.finalTotal = isNaN(this.detail.finalTotal)
        ? 0
        : this.detail.finalTotal
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/batchCostApply/batchCostApplySaveOrUpdate',
        { ...this.detail, detailList },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async save2() {
      if (
        !this.detail.invoiceCode ||
        this.detail.invoiceCode.trim() === '' ||
        this.detail.invoiceCode === undefined
      ) {
        this.$dialog.message.error('请填写发票号')
        return
      }
      if (
        !this.detail.invoiceDate ||
        this.detail.invoiceDate.trim() === '' ||
        this.detail.invoiceDate === undefined
      ) {
        this.$dialog.message.error('请填写发票日期')
        return
      }
      if (!this.$refs.form.validate()) {
        return false
      }
      if (!this.$refs.form2.validate()) {
        return false
      }
      if (this.ships.length === 0) {
        this.$dialog.message.warning('请添加船舶')
        return false
      }
      const detailList = this.getShipsWithOperation()
      detailList.forEach((item) => {
        item.toUsd = (item.money * this.rate).toFixed(2)
      })
      this.detail.finalTotalToUsd = isNaN(this.detail.finalTotalToUsd)
        ? 0
        : this.detail.finalTotalToUsd
      this.detail.finalTotal = isNaN(this.detail.finalTotal)
        ? 0
        : this.detail.finalTotal
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/batchCostApply/batchCostApplySaveOrUpdate',
        { ...this.detail, detailList },
      )
      if (errorRaw) return false
      this.closeAndTo(this.backRouteName)
    },
    async submit(goBack) {
      // console.log(!(this.$refs?.aform?.validate() ?? true))
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      // console.log('data:' + data)
      // console.log('!data:' + !data)
      // console.log('!status:' + this.detail.status)
      // if (this.detail.status == 4) {
      //   const { errorRaw } = await this.getAsync(
      //     '/business/shipAffairs/batchCostApply/batchCostApplySubmit',
      //     { applyId: data },
      //   )
      //   if (!errorRaw) goBack()
      //   // if (!error) goBack()
      // }
      if (!data) return
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/batchCostApply/batchCostApplySubmit',
          { applyId: data },
        )
        if (!errorRaw) goBack()
      } else {
        console.log(this.$refs)
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    // async submit(goBack) {//批量预算不带审批流
    //   // alert(111)
    //   // console.log(!(this.$refs?.aform?.validate() ?? true))
    //   // if (!(this.$refs?.aform?.validate() ?? true)) return
    //   const data = await this.save(goBack, true)
    //   if (!data) return
    //   // if (!this.detail.auditParams) {
    //   const { errorRaw } = await this.getAsync(
    //     '/business/shipAffairs/batchCostApply/batchCostApplySubmit',
    //     { applyId: data },
    //   )
    //   if (!errorRaw) goBack()
    //   // } else {
    //   //   const error = await this.$refs.audit.submit()
    //   //   if (!error) goBack()
    //   // }
    // },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/batchCostApply/batchCostApplyDetailById',
        { applyId: this.$route.params.id },
      )
      this.detail = { ...data }
      this.ships = [...data.detailList]
      this.ships.forEach((item) => {
        item.itemDetailId = item.id
        item.overBudget = item.finalMoney > item.money ? 1 : 0
      })
      // console.log(this.ships)
      this.currencyId = data.ccyId
      this.initUser = { id: data.applicantId, nickName: data.applicantName }
      this.initSupply = {
        id: data.supplyId,
        name: data.supplyName,
      }
      //   this.usdMoney = data.usdMoney
      // this.initApply = data.initApply
      this.initApplyPerson = {
        nickName: data.initPersonName,
        id: data.initApply,
      }
      this.detail.initSubject = {
        subjectName: data.subjectName,
        id: data.subjectId,
      }
      this.detail.currencyObj = {
        ccyName: data.ccyName,
        ccyCode: data.ccyCode,
        id: data.ccyId,
      }
      this.detail.happenDate = this.detail.proposedDate
      this.loadBank()
      // this.detail.initSup = { name: data.supplyName, id: data.supplyId }
      // console.log(this.ships)
    },
    async loadHappenDate(val) {
      const { data } = await this.postAsync(
        '/business/shipAffairs/costProject/getPayDateBatch',
        {
          happenDate: val.happenDate,
          paymentCompany: val.paymentCompany,
          supplyId: val.supplyId,
          shipCode: 'test',
        },
      )
      this.detail.payDate = data
    },
    async genBudgetData(goBack) {
      const data = await this.save(goBack, true)
      if (!data) return
      // if (!this.detail.auditParams) {
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/batchCostApply/genBudgetData',
        { applyId: data },
      )
      if (errorRaw) return
      this.$dialog.message.success('操作成功')
      this.closeAndTo(this.backRouteName)
    },
    async genCostProjectData(goBack) {
      if (
        !this.detail.invoiceCode ||
        this.detail.invoiceCode.trim() === '' ||
        this.detail.invoiceCode === undefined
      ) {
        this.$dialog.message.error('请填写发票号')
        return
      }
      if (
        !this.detail.invoiceDate ||
        this.detail.invoiceDate.trim() === '' ||
        this.detail.invoiceDate === undefined
      ) {
        this.$dialog.message.error('请填写发票日期')
        return
      }
      this.loading = true
      if (this.selectedItems.length === 0 || !this.selectedItems) {
        this.loading = false
        this.$dialog.message.warning('请选择明细')
        return false
      }
      // 判断勾选的明细是否全部未生成费用项目和凭证
      for (let i = 0; i < this.selectedItems.length; i++) {
        let item = this.selectedItems[i]
        if (
          this.checkStringNotEmpty(item.costProjectId) ||
          this.checkStringNotEmpty(item.costOrderId)
        ) {
          this.loading = false
          this.$dialog.message.warning(
            '选中的明细中存在已生成费用项目&费用凭证的记录！',
          )
          return
        }
        if (
          item.budgetStatus != '审批通过' &&
          item.budgetStatus != 'OA立项审批通过'
        ) {
          this.loading = false
          this.$dialog.message.warning('选中的明细中存在未审批通过的记录！')
          return
        }
      }
      this.checkDuplicate()
      const data = await this.save(goBack, true)
      if (!data) {
        this.loading = false
        return
      }
      // if (!this.detail.auditParams) {
      // const { errorRaw } = await this.getAsync(
      //   '/business/shipAffairs/batchCostApply/genCostProjectData2',
      //   { applyId: data },
      // )
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/batchCostApply/genCostProjectData2',
        { ...this.detail, detailList: this.selectedItems },
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.$dialog.message.success('操作成功')
      this.closeAndTo(this.backRouteName)

      // // -------一次性生成开始
      // this.checkDuplicate()
      // const data = await this.save(goBack, true)
      // if (!data) return
      // // if (!this.detail.auditParams) {
      // const { errorRaw } = await this.getAsync(
      //   '/business/shipAffairs/batchCostApply/genCostProjectData',
      //   { applyId: data },
      // )
      // if (errorRaw) return
      // this.$dialog.message.success('操作成功')
      // this.closeAndTo(this.backRouteName)

      // // 一次性生成结束
    },
    // 禁止保存（点击保存，增加弹框提示，不可保存，只能取消）：同发票号，同船，同供应商，同费用科目
    // 费用项目查重（点击保存，增加弹框提示，提示是否确定保存，点击确定可保存，可取消）：
    // 1. 发票号查重：同发票号，同船，同供应商
    // 2. 金额查重：同金额，同船，同供应商，同费用科目
    // 3. 发生日期查重：同发生日期，同船，同供应商
    async checkDuplicate() {
      if (this.detail.businessStatus != '预算数据已生成') return
      if (!this.detail.invoiceCode) return
      for (const item of this.ships) {
        const { data } = await this.getAsync(
          '/business/shipAffairs/costProject/list',
          {
            shipCode: item.shipCode,
            supplyId: this.detail.supplyId,
            subjectId: this.detail.subjectId,
            invoiceCode: this.detail.invoiceCode,
          },
        )
        if (data.length > 0) {
          this.detail.invoiceCode = ''
          this.$dialog.message.error(item.chShipName + '发票号重复,请重新输入')
          return
        }
      }
    },
    // async delCostProjectData(goBack) {
    //   if (
    //     !(await this.$dialog.msgbox.confirm('确定删除费用项目及费用凭证吗？'))
    //   )
    //     return
    //   const data = await this.save(goBack, true)
    //   if (!data) return
    //   const { errorRaw } = await this.getAsync(
    //     '/business/shipAffairs/batchCostApply/delCostProjectData',
    //     { applyId: data },
    //   )
    //   if (errorRaw) return
    //   this.$dialog.message.success('删除成功')
    //   this.closeAndTo(this.backRouteName)
    // },
    async delCostProjectData() {
      this.loading = true
      if (this.selectedItems.length === 0 || !this.selectedItems) {
        this.loading = false
        this.$dialog.message.warning('请选择明细')
        return false
      }
      // 判断勾选的明细是否全部未生成费用项目和凭证
      for (let i = 0; i < this.selectedItems.length; i++) {
        let item = this.selectedItems[i]
        if (
          !this.checkStringNotEmpty(item.costProjectId) ||
          !this.checkStringNotEmpty(item.costOrderId)
        ) {
          this.loading = false
          this.$dialog.message.warning(
            '选中的明细中存在未生成费用项目&费用凭证的记录！',
          )
          return
        }
        // if (item.costProjectId != null || item.costOrderId != null) {
        // if (item.costProjectStatus != 0) {
        //   this.$dialog.message.warning(
        //     '选中的明细中对应费用项目状态不可删除，未做凭证的费用项目可删除！',
        //   )
        //   return
        // }
        if (
          item.costOrderStatus != 10 &&
          item.costOrderStatus != 12 &&
          item.costOrderStatus != 99
        ) {
          this.loading = false
          this.$dialog.message.warning(
            '选中的明细中对应费用凭证状态不可删除，未提交/待实际申请人确认/审批失败的费用凭证可删除！',
          )
          return
        }
        // }
      }
      if (
        !(await this.$dialog.msgbox.confirm(
          '确定删除勾选的费用项目及费用凭证吗？',
        ))
      )
        return
      // const data = await this.save(goBack, true)
      // if (!data) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/batchCostApply/delCostProjectData2',
        { ...this.detail, detailList: this.selectedItems },
      )
      if (errorRaw) return
      this.$dialog.message.success('操作成功')
      this.closeAndTo(this.backRouteName)

      //     未调整为勾选删除前
      // if (
      //   !(await this.$dialog.msgbox.confirm('确定删除勾选的费用项目及费用凭证吗？'))
      // )
      //   return
      // const data = await this.save(goBack, true)
      // if (!data) return
      // const { errorRaw } = await this.getAsync(
      //   '/business/shipAffairs/batchCostApply/delCostProjectData',
      //   { applyId: data },
      // )
      // if (errorRaw) return
      // this.$dialog.message.success('删除成功')
      // this.closeAndTo(this.backRouteName)
    },
    checkStringNotEmpty(str) {
      // 判断字符串不为null，undefined，且长度大于0
      return str !== null && str !== undefined && str.trim().length > 0
    },
    async dowExcel() {
      // const items = this.components.map((i) => i.materialId)
      const { errorRaw } = await this.blobDownload(
        '/business/shipAffairs/batchCostApply/dowExcel',
        {
          paymentCompany: this.detail.paymentCompany,
          userId: this.detail.initApply,
        },
        '批量预算导入模板.xlsx',
      )
      if (errorRaw) this.$dialog.message.error(errorRaw)
    },
    async importExcel() {
      let formData = new FormData()
      formData.append('file', this.file)
      let shipSearchDTO = {
        paymentCompany: this.detail.paymentCompany,
        userId: this.detail.initApply,
      }
      formData.append(
        'shipSearchDTO',
        new Blob([JSON.stringify(shipSearchDTO)], { type: 'application/json' }),
      )
      // console.log(formData)
      // formData.append('paymentCompany', this.detail.paymentCompany)
      // formData.append('userId', this.detail.initApply)
      const { data } = await this.postAsync(
        '/business/shipAffairs/batchCostApply/importExcel',
        formData,
      )
      if (data) {
        // console.log(data)
        this.ships = data
        this.ships = this.ships.map((i) => {
          return {
            ...i,
            // money: 0,
            // toUsd: 0,
            // remark: '',
            vid: i.id,
            itemDetailId: i.id,
            chShipName: i.chShipName,
            id: null,
          }
        })
        // console.log(this.ships)
      }
    },
    async loadBank() {
      // if (this.detail.status != 3) {
      //   return
      // }
      const { data } = await this.getAsync(
        '/business/shipAffairs/Supplier/getBankMessageByShipCodeAndType',
        {
          purchaseCompany: this.detail.paymentCompany,
          supplierId: this.detail.supplyId,
          currencyId: this.detail.ccyId,
        },
      )
      if (data.length === 0) {
        this.$dialog.message.error('供应商未维护银行信息')
        return
      }
      data.forEach((item) => {
        item.bank = item.bank + ':' + item.account
      })
      this.banks = data
      // console.log(this.banks)
      if (data.length === 1) {
        this.detail.accountBank = data[0].account
        this.detail.openBank = data[0].bank
      }
      if (data.length > 1) {
        this.$dialog.message.info('供应商维护了多个银行信息,请选择')
      }
    },
  },

  mounted() {
    this.loadDetail()
    this.getDeptTreeList()
  },
}
</script>

<style></style>
