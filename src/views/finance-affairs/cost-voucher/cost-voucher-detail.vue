<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['费用凭证:编辑']"
      :title="`发票管理-${detail.orderCode || '新增'}`"
      :tooltip="detail.orderCode || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        detail.status == 10 ||
        (detail.status == 11 &&
          (!detail.auditParams || detail.auditParams.taskId)) ||
        detail.status == 12 ||
        detail.status == undefined
      "
      :can-save="
        detail.status == 10 || detail.status == 12 || detail.status == undefined
      "
      @save="save"
      @submit="submit"
    >
      <!-- :can-submit="
        !detail.auditParams ||
        detail.auditParams.taskId ||
        detail.status == 10 ||
        detail.status == 11 ||
        detail.status == 12 ||
        detail.status == undefined
      " -->
      <template v-slot:custombtns>
        <v-btn
          v-if="isEdit && detail.status !== 99"
          tile
          color="warning"
          small
          class="mx-1"
          @click="saveFile"
          :loading="quoteLoading"
          v-permission="['费用凭证:保存附件']"
        >
          <span class="mdi mdi-file-arrow-up-down-outline"></span>
          保存附件
        </v-btn>
        <v-btn
          v-if="detail.status == 99 && isApplyPerson"
          tile
          color="error"
          small
          class="mx-1"
          @click="startProcess"
          :loading="quoteLoading"
          v-permission="['费用凭证:实际申请人确认']"
        >
          <v-icon left>mdi-check</v-icon>
          实际申请人确认
        </v-btn>
        <v-btn
          v-if="detail.status == 99 && isApplyPerson"
          tile
          color="error"
          small
          class="mx-1"
          @click="returnProcess"
          :loading="quoteLoading"
          v-permission="['费用凭证:退回']"
        >
          <v-icon left>mdi-check</v-icon>
          退回
        </v-btn>
      </template>
      <template
        v-if="detail.auditParams && detail.auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <v-form ref="form">
            <v-row>
              <v-col cols="3">
                <v-ship-select
                  :readonly="!!detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="3">
                <v-text-field
                  :readonly="isEdit"
                  outlined
                  dense
                  label="发票数"
                  v-model="detail.orderNum"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="3">
                <v-text-field
                  outlined
                  dense
                  label="供应商"
                  readonly
                  v-model="detail.supplyName"
                  hint="选择费用项目后带出"
                ></v-text-field>
              </v-col>
              <v-col cols="3">
                <v-text-field
                  outlined
                  dense
                  label="实际申请人"
                  readonly
                  v-model="detail.applyPersonName"
                  hint="选择费用项目后带出"
                ></v-text-field>
              </v-col>
              <v-col cols="3">
                <v-text-field
                  outlined
                  dense
                  label="申请部门"
                  readonly
                  v-model="detail.applyDeptName"
                  hint="选择费用项目后带出"
                ></v-text-field>
              </v-col>
              <v-col cols="3">
                <v-text-field
                  outlined
                  dense
                  label="币种"
                  readonly
                  v-model="detail.ccyName"
                  hint="选择费用项目后带出"
                ></v-text-field>
              </v-col>
              <v-col cols="3">
                <v-text-field
                  outlined
                  dense
                  label="付款金额"
                  readonly
                  v-model="detail.moneyPay"
                  hint="选择费用项目后带出"
                ></v-text-field>
              </v-col>
              <v-col cols="3">
                <v-text-field
                  outlined
                  dense
                  label="金额大写"
                  readonly
                  v-model="detail.moneyUpper"
                  hint="选择费用项目后带出"
                ></v-text-field>
              </v-col>
              <v-col cols="3">
                <v-text-field
                  outlined
                  dense
                  label="付款公司"
                  readonly
                  v-model="detail.payCompany"
                  hint="选择费用项目后带出"
                ></v-text-field>
              </v-col>
              <v-col cols="3">
                <vs-date-picker
                  outlined
                  dense
                  label="凭证日期"
                  readonly
                  v-model="detail.orderDate"
                  hint="选择费用项目后带出"
                ></vs-date-picker>
              </v-col>
            </v-row>
            <v-attach-list
              :attachments="detail.attachmentRecords"
              @change="changeAttachment"
            ></v-attach-list>
          </v-form>
        </v-container>
      </template>
      <template
        v-if="!detail.auditParams || detail.auditParams.taskId"
        #费用项目明细按钮
      >
        <v-btn
          v-if="!isEdit"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="dialog = true"
          v-permission="['费用凭证:选择费用项目']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择费用项目
        </v-btn>
      </template>
      <template #发票行项目明细>
        <v-table-list :headers="headers" :items="projects">
          <template v-slot:[`item.businessModule`]="{ item }">
            {{
              [
                '单次预算',
                '备件订单',
                '物料订单',
                '滑油订单',
                '航修修理单',
                '坞修修理单',
                '坞修备件订单',
                '分期付款',
                '备用金',
                '自修奖',
                '船东账',
                '新造船',
                '坞修物料订单',
                '批量预算',
              ][item.businessModule]
            }}
          </template>
          <template v-slot:[`item.orderCode`]="{ item }">
            <v-btn
              v-if="routerMap[item.businessModule] !== ''"
              :to="{
                name: routerMap[item.businessModule],
                params: { id: item.businessId },
              }"
              text
              small
            >
              {{ item.orderCode }}
            </v-btn>
            <div v-else>-</div>
          </template>

          <template v-slot:[`item.repairFlag`]="{ item }">
            {{ item.repairFlag === 1 ? '是' : '否' }}
          </template>
          <template v-slot:[`item.overBudget`]="{ item }">
            {{ item.overBudget ? '是' : '否' }}
          </template>
          <template v-slot:[`item.budgetRemark`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on }">
                <span v-on="on">
                  {{ item.budgetRemark.substring(0, 25) }}...
                </span>
              </template>
              <span>{{ item.budgetRemark }}</span>
            </v-tooltip>
          </template>
          <template v-slot:[`item.status`]="{ item }">
            {{ stateMap[item.status] }}
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <project-select-dialog
      v-model="dialog"
      :projects.sync="projects"
      :shipCode="detail.shipCode"
    ></project-select-dialog>
  </v-container>
</template>
<script>
import { stateMap } from '../private/constant'
import projectSelectDialog from './private/project-select-dialog.vue'
import routerControl from '@/mixin/routerControl'

// applyPerson	申请人id		false
// string
// attachmentIds	附件		false
// array
// string
// idList	需要合并的费用项目id(同船&同供应商&同货币&同业务类型&申请人)		false
// array
// string
// moneyPay	付款总金额		false
// number
// moneyUpper	金额大写		false
// string
// orderCode	凭证号		false
// string
// orderDate	凭证日期		false
// string(date-time)
// orderNum	发票数		false
// integer(int32)
// payCompany	付款公司		false
// string
// shipCode	船舶编码		false
// string
// supplyId	供应商id		false
// string
export default {
  components: { projectSelectDialog },
  name: 'cost-voucher-detail',
  props: {
    // 订单id
    orderId: {
      type: String,
    },
    // 发票id
    costOrderId: {
      type: String,
    },
  },
  mixins: [routerControl],
  created() {
    this.backRouteName = 'cost-voucher-list'
    this.subtitles = ['基本信息', '发票行项目明细']
    this.headers = [
      { text: '费用科目名', value: 'subjectName', sortable: false },
      { text: '发票编号', value: 'invoiceCode' },
      { text: '供应商名', value: 'supplyName' },
      { text: '币种', value: 'ccyName', sortable: false },
      { text: '发生日期', value: 'happenDate' },
      { text: '付款日期', value: 'payDate' },
      { text: '原币金额', value: 'money' },
      { text: '是否超预算', value: 'overBudget' },
      { text: '超预算说明', value: 'budgetRemark' },
      { text: '坞修', value: 'repairFlag' },
      { text: '单号', value: 'orderCode' },
      { text: '折算美金', value: 'usdMoney' },
      { text: '业务模块', value: 'businessModule' },
      {
        text: '模块编号',
        value: 'orderCode',
        hideDefault: true,
        sortable: false,
      },
      { text: '实际申请人', value: 'applyPersonName', sortable: false },
      { text: '申请部门', value: 'applyDeptName', sortable: false },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.stateMap = stateMap
    this.routerMap = {
      0: '',
      1: 'spare-order-detail',
      2: 'materials-order-detail',
      3: 'soil-order-detail',
      4: 'voyage-repair-detail',
      5: 'dock-repair-detail',
      6: 'spare-order-detail',
      7: 'hire-purchase-detail',
      8: '',
      9: 'self-repair-bonus-detail',
      10: '',
      11: '',
      12: 'materials-order-detail',
      13: 'batch-cost-detail',
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    isApplyPerson() {
      return this.userId == this.detail.applyPerson
    },
  },
  data() {
    return {
      detail: { attachmentRecords: [] },
      dialog: false,
      project: false,
      projects: [],
      searchRemain: { shipCode: '' },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      quoteLoading: false,
      userId: this.$local.data.get('userInfo').id,
      flag: false,
    }
  },

  watch: {
    costOrderId(val) {
      if (val) {
        console.log(val)
        this.loadDetail()
      }
    },
    'detail.shipCode'(val) {
      if (val) {
        this.searchRemain.shipCode = val
      }
    },
    projects(val) {
      if (val.length > 0 && !this.isEdit) {
        this.loadInfo(val)
      }
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async saveFile() {
      const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
      const { errorRaw } = await this.postAsync(reqUrl, {
        ...this.detail,
      })
      if (!errorRaw) {
        this.$dialog.message.success(`附件保存成功`)
        this.closeAndTo(this.backRouteName)
      }
    },

    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return false
      }
      const idList = this.projects.map((item) => item.id)
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/costOrder/modifyCostOrder'
        : '/business/shipAffairs/costOrder/createCostOrder'
      const { data, errorRaw } = await this.postAsync(reqUrl, {
        ...this.detail,
        idList,
      })
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      // 未提交和审批失败状态重新提交到待实际申请人确认状态
      if (
        this.detail.status == 10 ||
        this.detail.status == 12 ||
        this.detail.status == undefined
      ) {
        if (this.detail.applyPerson != this.userId) {
          this.detail.id = data
          this.detail.status = 99
          this.detail.businessStatus = '待实际申请人确认'
          const idList = this.projects.map((item) => item.id)
          const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
          const { errorRaw } = await this.postAsync(reqUrl, {
            ...this.detail,
            idList,
          })
          if (errorRaw) return false
          this.flag = true
          goBack()
        } else {
          this.detail.id = data
          this.quoteLoading = true
          this.startProcess()
          this.quoteLoading = false
          this.flag = true
        }
      }
      if (!this.flag) {
        if (
          !this.detail.auditParams &&
          (this.detail.status == 99 || this.detail.status == 10)
        ) {
          this.quoteLoading = true
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/costOrder/process/start',
            { id: data },
          )
          this.quoteLoading = false
          if (!errorRaw) goBack()
        } else {
          this.quoteLoading = true
          const error = await this.$refs.audit.submit()
          this.quoteLoading = false
          if (!error) {
            const { data } = await this.getAsync(
              `/business/shipAffairs/costOrder/getById/${this.$route.params.id}`,
            )
            if (data.status == 13) {
              const ids = [this.$route.params.id]
              const { errorRaw } = await this.postAsync(
                '/business/shipAffairs/costOrder/createSapNewList',
                ids,
              )
              if (!errorRaw) goBack()
            } else {
              goBack()
            }
          }
        }
      }
    },
    async returnProcess() {
      this.quoteLoading = true
      this.detail.status = 10
      this.detail.businessStatus = '未提交'
      const idList = this.projects.map((item) => item.id)
      const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
      const { errorRaw } = await this.postAsync(reqUrl, {
        ...this.detail,
        idList,
      })
      if (errorRaw) return false
      this.quoteLoading = false
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
    async startProcess() {
      // if (!(this.$refs?.aform?.validate() ?? true)) return
      // const data = await this.save(goBack, true)
      // if (!data) return
      // 未提交和审批失败状态重新提交到待实际申请人确认状态
      // if (this.detail.status == 10 || this.detail.status == 12) {
      //   this.detail.status = 99
      //   this.detail.businessStatus = '待实际申请人确认'
      //   const idList = this.projects.map((item) => item.id)
      //   const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
      //   const { errorRaw } = await this.postAsync(reqUrl, {
      //     ...this.detail,
      //     idList,
      //   })
      //   if (errorRaw) return false
      //   this.closeAndTo(this.backRouteName)
      //   // this.$router.push('/finance-affairs/cost-voucher-list')
      //   // this.$router.go(-1)
      // }

      // if (!this.detail.auditParams) {
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/costOrder/process/start',
        { id: this.detail.id },
      )
      this.quoteLoading = false
      if (!errorRaw) this.closeAndTo(this.backRouteName)
      // } else {
      //   this.quoteLoading = true
      //   const error = await this.$refs.audit.submit()
      //   this.quoteLoading = false
      //   if (!error) this.closeAndTo(this.backRouteName)
      // }
    },
    async loadDetail() {
      if (!this.isEdit || this.costOrderId == undefined) return
      // const { data } = await this.getAsync(
      //   `/business/shipAffairs/costOrder/getById/${this.$route.params.id}`,
      // )
      const { data } = await this.getAsync(
        `/business/shipAffairs/costOrder/getById/${this.costOrderId}`,
      )
      this.detail = data
      this.projects = data.list
    },

    async loadInfo(val) {
      const { data } = await this.postAsync(
        '/business/shipAffairs/costOrder/getCostOrderInfo',
        val.map((i) => i.id),
      )
      Object.assign(this.detail, data)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
