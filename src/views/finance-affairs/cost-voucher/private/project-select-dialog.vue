<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        费用项目选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          outlined
          fuzzy-label="发票号/模块编号"
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="2">
              <v-dialog-select
                label="供应商"
                item-text="name"
                item-value="id"
                v-model="searchObj.supplyId"
                :headers="supHeaders"
                req-url="/business/shipAffairs/Supplier/list"
                fuzzy-label="供应商"
                :search-remain="searchCreditDate"
                clearable
                @clear="
                  () => {
                    searchObj.supplyId = ''
                  }
                "
              ></v-dialog-select>
            </v-col>
          </template>
          <template #btns></template>
          <template v-slot:[`item.status`]="{ item }">
            {{ statues[item.status] }}
          </template>
          <template v-slot:[`item.businessModule`]="{ item }">
            {{
              [
                '单次预算',
                '备件订单',
                '物料订单',
                '滑油订单',
                '航修修理单',
                '坞修修理单',
                '坞修备件订单',
                '分期付款',
                '备用金',
                '自修奖',
                '船东账',
                '新造船',
                '坞修物料订单',
                '批量预算',
              ][item.businessModule]
            }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { stateMap, statues } from '../../private/constant'
export default {
  name: 'subject-select-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/shipAffairs/costProject/pageFinance'
    this.headers = [
      { text: '船舶', value: 'shipName', sortable: false },
      { text: '费用科目名', value: 'subjectName', sortable: false },
      { text: '发票编号', value: 'invoiceCode' },
      { text: '订单号', value: 'orderCode' },
      { text: '供应商名', value: 'supplyName' },
      { text: '币种', value: 'ccyName', sortable: false },
      { text: '发生日期', value: 'happenDate', hideDefault: true },
      { text: '付款日期', value: 'payDate' },
      { text: '折算美金', value: 'usdMoney' },
      { text: '业务模块', value: 'businessModule' },
      { text: '实际申请人', value: 'applyPersonName', sortable: false },
      { text: '申请部门', value: 'applyDeptName', sortable: false },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.statues = stateMap
    this.status = statues
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: {
      type: String,
      default: '',
    },
    projects: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchObj: { status: 0, shipCode: '', applyPerson: '' },
      selected: [],
      searchCreditDate: {
        creditDate: null,
        manager: this.$local.data.get('userInfo').id,
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    selected: {
      handler(val, oldVal) {
        if (val.length === 1 && val[0].id !== oldVal[0]?.id) {
          this.searchObj = {
            // ...val[0],
            businessModule: val[0].businessModule,
            ccyCode: val[0].ccyCode,
            ccyId: val[0].ccyId,
            ccyName: val[0].ccyName,
            shipCode: val[0].shipCode,
            supplyId: val[0].supplyId,
            supplyName: val[0].supplyName,
            applyPerson: val[0].applyPerson,
            status: 0,
            // orderCode: val[0].orderCode, 去除同订单校验
            subjectId: '',
          }
          // if (val[0].orderCode) this.loadSpecOrder(val[0])
        } else if (val.length === 0) {
          this.searchObj = { shipCode: this.shipCode, status: 0 }
        }
      },
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const projects = [...this.selected]
      this.$emit('update:projects', projects)
      this.$emit('change', false)
    },
    async loadSpecOrder(val) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/costProject/list',
        {
          ...val,
          status: 0,
        },
      )
      this.selected = data
      await this.$nextTick()
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
