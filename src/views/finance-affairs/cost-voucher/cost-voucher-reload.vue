<template>
  <v-container fluid>
    <!-- 备件订单发票 -->
    <cost-gen-spare
      v-show="
        detail.historyFlag &&
        (detail.businessModule == 1 || detail.businessModule == 6)
      "
      :history-flag="detail.historyFlag"
      :cost-order-id="detail.id"
    ></cost-gen-spare>
    <!-- 物料订单发票 -->
    <cost-gen-material
      v-show="
        detail.historyFlag &&
        (detail.businessModule == 2 || detail.businessModule == 12)
      "
      :history-flag="detail.historyFlag"
      :cost-order-id="detail.id"
    ></cost-gen-material>
    <!-- 滑油订单发票 -->
    <cost-gen-soil
      v-show="detail.historyFlag && detail.businessModule == 3"
      :history-flag="detail.historyFlag"
      :cost-order-id="detail.id"
    ></cost-gen-soil>
    <!-- 航修订单发票 -->
    <cost-gen-voyage
      v-show="detail.historyFlag && detail.businessModule == 4"
      :history-flag="detail.historyFlag"
      :cost-order-id="detail.id"
    ></cost-gen-voyage>
    <!-- 单次预算发票 -->
    <cost-gen-budget
      v-show="detail.historyFlag && detail.businessModule == 0"
      :history-flag="detail.historyFlag"
      :cost-order-id="detail.id"
    ></cost-gen-budget>
    <!-- 批量预算发票 -->
    <cost-gen-batch-order
      v-show="detail.historyFlag && detail.businessModule >= 13"
      :history-flag="detail.historyFlag"
      :cost-order-id="detail.id"
    ></cost-gen-batch-order>
    <!-- 历史数据 -->
    <cost-voucher-detail
      v-show="!detail.historyFlag"
      :cost-order-id="detail.id"
    ></cost-voucher-detail>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import costGenSpare from '../cost-gen/cost-gen-spare.vue'
import costGenMaterial from '../cost-gen/cost-gen-material.vue'
import costGenSoil from '../cost-gen/cost-gen-soil.vue'
import costGenVoyage from '../cost-gen/cost-gen-voyage.vue'
import costVoucherDetail from './cost-voucher-detail.vue'
import costGenBudget from '../cost-gen/cost-gen-budget.vue'
import CostGenBatchOrder from '../cost-gen/cost-gen-batch-order.vue'
// 各模块发票详情界面跳转界面
// 根据模块跳转不同页面，原发票相关操作需同步到发票录入界面
export default {
  components: {
    costGenSpare,
    costGenMaterial,
    costGenSoil,
    costGenVoyage,
    costVoucherDetail,
    costGenBudget,
    CostGenBatchOrder,
  },
  name: 'cost-voucher-reload',
  mixins: [routerControl],
  created() {},

  computed: {},
  data() {
    return {
      detail: { historyFlag: true },
    }
  },

  watch: {},

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/costOrder/getById/${this.$route.params.id}`,
      )
      this.detail = data
      // this.projects = data.list
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
