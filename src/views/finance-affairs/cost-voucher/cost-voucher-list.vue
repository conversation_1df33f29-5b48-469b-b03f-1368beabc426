<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      :single-select="false"
      :search-date="searchDate"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.status"
            :items="status"
            label="状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <!-- v-if="searchObj.status == 11 || searchObj.status == 10" -->
          <v-select
            v-model="searchObj.businessStatus"
            :items="businessStatusList"
            label="审批人"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2" v-if="searchObj.status == 11">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessModule"
            label="业务模块"
            outlined
            dense
            clearable
            :items="bussinessModules"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            label="是否超预算"
            v-model="searchObj.isOverBudget"
            clearable
            outlined
            dense
            :items="[
              { text: '是', value: '1' },
              { text: '否', value: '0' },
            ]"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-supply-select-list
            v-model="searchObj.supplyId"
            label="供应商"
            outlined
            clearable
            dense
          ></v-supply-select-list>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <!-- <v-handler
            clearable
            label="实际申请人"
            v-model="searchObj.applyPerson"
            :use-current="false"
          ></v-handler> -->
          <v-handler
            clearable
            label="实际申请人"
            v-model="searchObj.applyPerson"
            :use-current="false"
            :initUser="isApplicantId"
          ></v-handler>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.year"
            label="年度"
            outlined
            dense
            clearable
          ></v-text-field>
        </v-col> -->
        <v-col cols="12" sm="6" md="2">
          <v-menu
            v-model="datesMenu2"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                ref="dates2"
                :value="dateRangeText2"
                :label="'凭证日期'"
                append-icon="mdi-calendar"
                outlined
                dense
                readonly
                clearable
                @click:clear="dates2 = []"
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <vc-date-picker
              v-model="dates2"
              mode="date"
              is-range
            ></vc-date-picker>
          </v-menu>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="凭证号"
            outlined
            dense
            clearable
            v-model="searchObj.orderCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="发票号/订单号"
            outlined
            dense
            clearable
            v-model="searchObj.invoiceCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-autocomplete
            label="对账负责人"
            v-model="searchObj.manager"
            dense
            outlined
            item-text="nickName"
            item-value="id"
            :items="financeList"
            clearable
          ></v-autocomplete>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :loading="loading"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="downloadExcel"
          v-permission="['费用凭证:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          :to="{ name: 'cost-voucher-reload', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['费用凭证:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!-- 生成报文 -->
        <!-- <v-btn
          :disabled="!canGen"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="genSapMes"
          v-permission="['费用凭证:生成报文']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成报文
        </v-btn> -->
        <v-btn
          :disabled="!canGen"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="genSapMesNew"
          v-permission="['费用凭证:生成报文']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成报文(new)
        </v-btn>
        <!-- 删除 -->
        <v-btn
          :disabled="!canDel"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delVoucher"
          v-permission="['费用凭证:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!canSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="submitApply"
          v-permission="['费用凭证:审批通过']"
        >
          <v-icon left>mdi-send</v-icon>
          审批通过
        </v-btn>
        <v-btn
          :disabled="!canPDF"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="dowloadPDF"
          v-permission="['费用凭证:下载凭证']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          下载凭证
        </v-btn>
        <a v-if="true" :href="downPDF" ref="downPDFHref"></a>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ statues[item.status] }}
      </template>
      <template v-slot:[`item.businessModule`]="{ item }">
        {{ businessModuleMap[item.businessModule] }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applyPerson	申请人id	string
// applyPersonName	申请人名	string
// businessModule	业务模块（写死：0手动录入 1备件订单 2物料订单 3滑油订单 4航修修理单 5坞修修理单 6坞修订单）	integer
// businessStatus	业务状态	string
// ccyId	币种id	string
// ccyName	币种名	string
// id	物理主键	string
// moneyPay	付款总金额	number
// moneyUpper	金额大写	string
// orderCode	凭证号	string
// orderDate	凭证日期	string
// orderNum	发票数	integer
// payCompany	付款公司	string
// shipCode	船舶编码	string
// shipName	船舶名	string
// status	10:未提交 11:审批中 12：审批失败13:审批通过 14:已生成SAP报文 20:未通过映射 21:映射错误 22:报文错误 23:已通过映射 24:已发送SAP 25:SAP审批未通过30:付款审批中 31:付款审批未通过 32:付款审批通过 33:已付款	integer
// supplyId	供应商id	string
// supplyName	供应商名	string
import { stateMap, statues } from '../private/constant'
export default {
  name: 'cost-voucher-list',
  created() {
    this.tableName = '发票管理'
    this.reqUrl = '/business/shipAffairs/costOrder/page'
    this.headers = [
      { text: '船舶', value: 'shipName' },
      { text: '凭证号', value: 'orderCode', hideDefault: true },
      { text: '发票号', value: 'invoiceCode' },
      { text: '订单号', value: 'projectOrderNo' },
      { text: '申请人', value: 'applyPersonName' },
      { text: '凭证日期', value: 'orderDate', hideDefault: true },
      { text: '付款到期日', value: 'latestPayDate' },
      { text: '付款公司', value: 'payCompany' },
      { text: '供应商', value: 'supplyName' },
      { text: '币种', value: 'ccyName' },
      { text: '付款总金额', value: 'moneyPay' },
      { text: '是否超单次预算', value: 'isOverBudget' },
      { text: '发票数', value: 'orderNum' },
      { text: '业务模块', value: 'businessModule' },
      { text: '状态', value: 'status' },
      { text: '审批人', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
      { text: '录单人', value: 'applyUserName' },
    ]
    this.fuzzyLabel = ''
    // this.searchDate = {
    //   label: '',
    //   value: '',
    // }
    this.searchDate = {
      label: '发生日期',
      interval: true,
    }
    this.statues = stateMap
    this.status = statues.filter((item) => item.value > 9)
    this.pushParams = { name: 'cost-voucher-reload' }
    this.businessModuleMap = {
      0: '单次预算',
      1: '备件订单',
      2: '物料订单',
      3: '滑油订单',
      4: '航修修理单',
      5: '坞修修理单',
      6: '坞修备件订单',
      7: '分期付款',
      8: '备用金',
      9: '自修奖',
      10: '船东账',
      11: '新造船',
      12: '坞修物料订单',
      13: '批量预算',
    }
    this.bussinessModules = [
      { text: '单次预算', value: 0 },
      { text: '备件订单', value: 1 },
      { text: '物料订单', value: 2 },
      { text: '滑油订单', value: 3 },
      { text: '航修修理单', value: 4 },
      { text: '坞修修理单', value: 5 },
      { text: '坞修备件订单', value: 6 },
      { text: '分期付款', value: 7 },
      { text: '备用金', value: 8 },
      { text: '自修奖', value: 9 },
      { text: '船东账', value: 10 },
      { text: '新造船', value: 11 },
      { text: '坞修物料订单', value: 12 },
      { text: '批量预算', value: 13 },
    ]
    this.businessStatusList = [
      { text: '未提交', value: '未提交' },
      // { text: '待实际申请人确认', value: '待实际申请人确认' },
      { text: '实际申请人退回', value: '实际申请人退回' },
      { text: '管船组长', value: '管船组长' },
      { text: '通导信息部负责人', value: '通导信息部负责人' },
      // { text: '管船五组组长', value: '管船五组组长' },
      { text: '山东船管负责人', value: '山东船管负责人' },
      { text: 'ISM办主任', value: 'ISM办主任' },
      // { text: '船管商务负责人', value: '船管商务负责人' },
      { text: '采购管理部负责人', value: '采购管理部负责人' },
      // { text: '管船中心商务经理', value: '管船中心商务经理' },
      { text: '商务部负责人', value: '商务部负责人' },
      { text: '总轮机长', value: '总轮机长' },
      { text: '船管总经理', value: '船管总经理' },
      { text: '管船中心总经理', value: '管船中心总经理' },
      // { text: '管船中心负责人', value: '管船中心负责人' },
      { text: '单船财务', value: '单船财务' },
      { text: '财务主管', value: '财务主管' },
      { text: '财务经理', value: '财务经理' },
      // { text: '审批通过', value: '审批通过' },
      // { text: '已生成SAP报文', value: '已生成SAP报文' },
    ]
  },

  data() {
    return {
      selected: [],
      searchObj: {
        year: '',
        status: 11,
        auditFlag: true,
        isMe: true,
        businessStatus: '',
      },
      loading: false,
      downPDF: '',
      isApplicantId: false,
      datesMenu2: false,
      dates2: [],
      financeList: [],
    }
  },

  computed: {
    canGen() {
      return (
        this.selected.length > 0 &&
        this.selected.every((item) => item.status === 13 || item.status === 28)
      )
    },
    canSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every((item) => item?.auditParams?.taskId)
      )
    },
    canPDF() {
      return (
        this.selected.length > 0 &&
        this.selected.every((item) => item.status >= 13 || item.status == 11)
      )
    },
    canDel() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            (item.status == 10 || item.status == 12 || item.status == 99) &&
            item.businessModule != 13 &&
            item.applyUser == this.$local.data.get('userInfo').id,
        )
      )
    },
    dateRangeText2() {
      return this.dates2?.start && this.dates2?.end
        ? `${this.dates2.start.toLocaleDateString()} 至 ${this.dates2?.end.toLocaleDateString()}`
        : ''
    },
  },

  watch: {
    dates2: {
      handler() {
        if (this.dates2?.start && this.dates2?.end) {
          this.datesMenu2 = false
          this.searchObj.fromTime2 = this.dates2?.start
            .toISOString()
            .split('T')[0]
          this.searchObj.toTime2 = this.dates2?.end.toISOString().split('T')[0]

          console.log(
            this.searchObj.fromTime2,
            this.searchObj.toTime2,
            this.dates2,
          )
          this.$refs.table.loadTableData()
        } else {
          this.$refs.table.loadTableData()
        }
      },
    },
    'searchObj.status': {
      handler() {
        // if (this.searchObj.status != 11 && this.searchObj.status != 10) {
        this.searchObj.businessStatus = ''
        // }
        this.selected = []
      },
    },
    'searchObj.shipCode': {
      handler() {
        this.selected = []
      },
    },
    'searchObj.businessStatus': {
      handler() {
        this.selected = []
      },
    },
    'searchObj.isMe': {
      handler() {
        this.selected = []
      },
    },
    searchObj(val) {
      if (val) {
        this.selected = []
        console.log('1111', val)
      }
    },
  },
  methods: {
    async delVoucher() {
      // 未提交、审批失败、待实际申请人确认的可删除
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costOrder/deleteCostOrder',
        [this.selected[0].id],
      )
      if (!errorRaw) this.$dialog.message.success('删除成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async genSapMes() {
      const ids = this.selected.map((item) => item.id)
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costOrder/createSapList',
        ids,
      )
      if (!errorRaw) this.$dialog.message.success('生成成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async genSapMesNew() {
      const ids = this.selected.map((item) => item.id)
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costOrder/createSapNewList',
        ids,
      )
      if (!errorRaw) this.$dialog.message.success('生成成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async submitApply() {
      if (!(await this.$dialog.msgbox.confirm('确定审批通过所选记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/batchCompleteTaskAndCommentAndSetVar',
        this.selected.map((item) => ({
          adopt: true,
          comment: '',
          params: {},
          taskId: item.auditParams.taskId,
        })),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`提交成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
    async dowloadPDF() {
      const ids = this.selected.map((item) => item.id)
      const { data } = await this.postAsync(
        '/business/shipAffairs/costOrder/createCostOrderPDFList',
        ids,
      )
      if (data) {
        console.log(data)
        data.forEach((item) => {
          this.dowloadPDFs(item)
        })
      }
    },
    async dowloadPDFs(item) {
      this.downPDF = `/api/system/file/download?fileName=${encodeURIComponent(
        item.fileName,
      )}&filePath=${item.filePath}`
      console.log(this.$refs.downPDFHref)

      const link = this.$refs.downPDFHref
      link.href = this.downPDF
      link.download = this.extractFilename(this.downPDF)
      link.style.display = 'none'
      document.body.appendChild(link)

      // 模拟点击<a>标签以触发下载
      link.click()
    },
    extractFilename(url) {
      return url.substring(url.lastIndexOf('/') + 1)
    },
    async downloadExcel() {
      this.loading = true
      let params = { ...this.$refs.table.searchRemain }
      params = {
        ...params,
        fuzzyParam: this.$refs.table.fuzzyParam,
        shipCode: this.$refs.table.ship,
        fromTime: this.dates?.start?.toISOString()?.split('T')?.[0],
        toTime: this.dates?.end?.toISOString()?.split('T')?.[0],
        fromTime2: this.dates2?.start?.toISOString()?.split('T')?.[0],
        toTime2: this.dates2?.end?.toISOString()?.split('T')?.[0],
      }
      await this.getBlobDownload(
        '/business/shipAffairs/costOrder/excelExport',
        params,
        // 时间戳后四位
        `费用凭证审批-${new Date().getTime().toString().slice(-4)}.xlsx`,
      )
      this.loading = false
    },
    async getFinanceList() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getFinanceList`,
      )
      console.log(data)
      this.financeList = data
    },
  },

  mounted() {
    this.getFinanceList()
    if (this.$route.query.status != undefined) {
      this.searchObj.status = this.$route.query.status
      this.searchObj.businessStatus = this.$route.query.businessStatus
      this.searchObj.applyPerson = this.$route.query.applyPerson
      if (
        this.$route.query.businessStatus == '实际申请人退回' ||
        this.$route.query.businessStatus == '未提交'
      ) {
        this.searchObj.manager = this.$route.query.manager
      }
      this.isApplicantId = this.searchObj.applyPerson
        ? {
            id: this.searchObj.applyPerson,
            nickName: this.$local.data.get('userInfo').nickName,
          }
        : false
      // 在3秒后执行一次任务
      // setTimeout(() => {
      //   this.searchObj.applyPerson = ''
      // }, 1000)
    }
  },
}
</script>

<style></style>
