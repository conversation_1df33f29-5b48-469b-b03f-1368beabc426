// status	状态 0：未做凭证 1：已做凭证
// status   状态 10:未提交 11:审批中 12：审批失败13:审批通过 14:已生成SAP报文 20:未通过映射 21:映射错误 22:报文错误 23:已通过映射 24:已发送SAP 25:SAP审批未通过30:付款审批中 31:付款审批未通过 32:付款审批通过 33:已付款	integer
// status	状态 20:未通过映射 21:映射错误 22:报文错误 23:已通过映射 24:已发送SAP 25:SAP审批未通过30:付款审批中 31:付款审批未通过 32:付款审批通过 33:已付款 34:付款审批未提交	integer

// const stateMap = {
//   0: '未做凭证',
//   1: '已做凭证',
//   2: '审批中',
//   3: '审批通过',
//   4: '已生成SAP报文',
//   5: '已发送SAP',
//   6: 'SAP审批未通过',
//   7: '付款审批中',
//   8: '付款审批通过',
//   9: '付款审批未通过',
//   10: '未提交',
//   11: '凭证审批中',
//   12: '审批失败',
//   13: '审批通过',
//   14: '已生成SAP报文',
//   20: '未通过映射',
//   21: '映射错误',
//   22: '报文错误',
//   23: '已通过映射',
//   24: '已发送SAP',
//   25: 'SAP审批未通过',
//   26: 'SAP执行成功',
//   27: 'SAP执行失败',
//   28: 'SAP已作废',
//   29: 'SAP已冲销',
//   30: '付款审批中',
//   31: '付款审批未通过',
//   32: '付款审批通过',
//   33: '已付款',
//   34: '付款审批未提交',
//   99: '待实际申请人确认',
// }
// const statues = [
//   { value: 0, text: '未做凭证' },
//   { value: 1, text: '已做凭证' },
//   { value: 2, text: '审批中' },
//   { value: 3, text: '审批通过' },
//   { value: 4, text: '已生成SAP报文' },
//   { value: 5, text: '已发送SAP' },
//   { value: 6, text: 'SAP审批未通过' },
//   { value: 7, text: '付款审批中' },
//   { value: 8, text: '付款审批通过' },
//   { value: 9, text: '付款审批未通过' },
//   { value: 10, text: '未提交' },
//   { value: 11, text: '凭证审批中' },
//   { value: 12, text: '审批失败' },
//   { value: 13, text: '审批通过' },
//   { value: 14, text: '已生成SAP报文' },
//   { value: 20, text: '未通过映射' },
//   { value: 21, text: '映射错误' },
//   { value: 22, text: '报文错误' },
//   { value: 23, text: '已通过映射' },
//   { value: 24, text: '已发送SAP' },
//   { value: 25, text: 'SAP审批未通过' },
//   { value: 26, text: 'SAP执行成功' },
//   { value: 27, text: 'SAP执行失败' },
//   { value: 28, text: 'SAP已作废' },
//   { value: 29, text: 'SAP已冲销' },
//   { value: 30, text: '付款审批中' },
//   { value: 31, text: '付款审批未通过' },
//   { value: 32, text: '付款审批通过' },
//   { value: 33, text: '已付款' },
//   { value: 34, text: '付款审批未提交' },
//   { value: 99, text: '待实际申请人确认' },
// ]
const stateMap = {
  0: '费用项目未提交', //未做凭证
  1: '发票未提交', //已做凭证
  // 2: '审批中',
  // 3: '审批通过',
  // 4: '已生成SAP报文',
  // 5: '已发送SAP',
  // 6: 'SAP审批未通过',
  // 7: '付款审批中',
  // 8: '付款审批通过',
  // 9: '付款审批未通过',
  10: '发票未提交', //未提交
  11: '发票审批中', //凭证审批中
  12: '发票审批退回', //审批失败
  13: '发票已审批', //审批通过
  // 14: '已生成SAP报文',//已生成SAP报文
  // 20: '未通过映射',//未通过映射
  21: '发票财务未确认-业务', //映射错误
  22: '发票财务未确认-业务', //报文错误
  // 23: '已通过映射',//已通过映射
  24: '发票财务确认中', //已发送SAP
  // 25: 'SAP审批未通过',//SAP审批未通过
  26: '发票财务已确认', //SAP执行成功
  27: '发票财务未确认-SAP', //SAP执行失败
  // 28: 'SAP已作废',//SAP已作废
  // 29: 'SAP已冲销',//SAP已冲销
  30: '付款审批审批中', //付款审批中
  31: '付款审批退回', //付款审批未通过
  32: '付款审批已完成', //付款审批通过
  33: '已付款', //已付款
  34: '付款审批未提交', //付款审批未提交
  // 35: '付款审批采购主管确认中', //付款审批采购主管确认中
  // 36: '付款审批采购主管已确认', //付款审批采购主管已确认
  99: '发票待实际申请人确认', //待实际申请人确认
  100: '废弃', //待实际申请人确认
}
const statues = [
  { value: 0, text: '费用项目未提交' },
  { value: 1, text: '发票未提交' },
  // { value: 2, text: '审批中' },
  // { value: 3, text: '审批通过' },
  // { value: 4, text: '已生成SAP报文' },
  // { value: 5, text: '已发送SAP' },
  // { value: 6, text: 'SAP审批未通过' },
  // { value: 7, text: '付款审批中' },
  // { value: 8, text: '付款审批通过' },
  // { value: 9, text: '付款审批未通过' },
  { value: 10, text: '发票未提交' },
  { value: 11, text: '发票审批中' },
  { value: 12, text: '发票审批退回' },
  { value: 13, text: '发票已审批' },
  // { value: 14, text: '已生成SAP报文' },
  // { value: 20, text: '未通过映射' },
  { value: 21, text: '发票财务未确认-业务' },
  { value: 22, text: '发票财务未确认-业务' },
  // { value: 23, text: '已通过映射' },
  { value: 24, text: '发票财务确认中' },
  // { value: 25, text: 'SAP审批未通过' },
  { value: 26, text: '发票财务已确认' },
  { value: 27, text: '发票财务未确认-SAP' },
  // { value: 28, text: 'SAP已作废' },
  // { value: 29, text: 'SAP已冲销' },
  { value: 30, text: '付款审批审批中' },
  { value: 31, text: '付款审批退回' },
  { value: 32, text: '付款审批已完成' },
  { value: 33, text: '已付款' },
  { value: 34, text: '付款审批未提交' },
  // { value: 35, text: '付款审批采购主管确认中' },
  // { value: 36, text: '付款审批采购主管已确认' },
  { value: 99, text: '发票待实际申请人确认' },
  { value: 100, text: '废弃' },
]
export { stateMap, statues }
