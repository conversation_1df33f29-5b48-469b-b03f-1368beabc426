<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['费用项目:编辑']"
      :title="`费用项目-${isEdit ? detail.invoiceCode : '新增'}`"
      :tooltip="isEdit ? detail.invoiceCode : '新增'"
      :backRouteName="backRouteName"
      :can-save="false"
    >
      <!-- @save="save"
      :can-save="canEdit" -->
      <v-card-text>
        <v-form ref="form" :readonly="!canEdit">
          <v-container fluid>
            <v-row>
              <v-col md="3" cols="12">
                <v-dialog-select
                  :disabled="!canEdit"
                  label="实际申请人"
                  item-text="nickName"
                  v-model="applyPerson"
                  :headers="userHeaders"
                  :rules="[canEdit ? rules.required : true]"
                  req-url="/system/user/page"
                  :init-selected="detail.initApply"
                  :search-remain="searchObj"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="用户名"
                        v-model="searchObj.nickName"
                        outlined
                        dense
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="6" md="6">
                      <treeselect
                        v-model="searchObj.deptId"
                        :options="deptTree"
                        placeholder="请选择部门"
                        outlined
                        dense
                      />
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.applyDeptName"
                  label="申请部门"
                  disabled
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <!-- <v-select
                  outlined
                  dense
                  label="船舶"
                  :disabled="!canEdit || !applyPerson.id"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                  :items="shipList"
                ></v-select> -->
                <v-ship-select
                  readonly
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.orderCode"
                  label="订单号"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-btn
                  v-if="routerMap[detail.businessModule] !== ''"
                  :to="{
                    name: routerMap[detail.businessModule],
                    params: {
                      id:
                        detail.businessId == null
                          ? detail.budgetId
                          : detail.businessId,
                    },
                  }"
                  text
                  small
                >
                  {{ routerMapName[detail.businessModule] }}详情:
                  {{ detail.orderCode }}
                </v-btn>
              </v-col>
              <v-col md="3" cols="12">
                <!-- <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="detail.subjectId"
                  :rules="[rules.required]"
                  :init-selected="detail.initSubject"
                  :search-dicts="searchDicts"
                  :search-remain="searchObj"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  :readonly="!canEdit"
                  required
                  dense
                  fuzzy-label="模糊搜索"
                  @select="
                    (item) => {
                      useLife = item.useLife
                    }
                  "
                ></v-dialog-select> -->
                <v-text-field
                  outlined
                  dense
                  v-model="detail.subjectName"
                  label="费用科目"
                  readonly
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.invoiceCode"
                  label="发票编号"
                  readonly
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  readonly
                  :rules="[canEdit ? rules.required : true]"
                ></vs-date-picker>
              </v-col>

              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  use-today
                  v-model="detail.addDate"
                  disabled
                  label="录单日期"
                  :rules="[canEdit ? rules.required : true]"
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.addPersonName"
                  label="录单人"
                  disabled
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.happenDate"
                  readonly
                  label="发生日期"
                  :rules="[canEdit ? rules.required : true]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-supply-select
                  :disabled="!detail.shipCode"
                  v-model="detail.supplyId"
                  :rules="[canEdit ? rules.required : true]"
                  :ship-code="detail.shipCode"
                  :readonly="isEdit"
                  @select="
                    (item) => {
                      currency = item.currency
                    }
                  "
                  :init-selected="initSupply"
                ></v-supply-select>
                <!-- <v-text-field
                  outlined
                  dense
                  v-model="detail.supplierName"
                  readonly
                  label="供应商"
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field> -->
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.ccyCode"
                  readonly
                  label="币种"
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field>
                <!-- <v-select
                  v-else
                  v-model="detail.currencyObj"
                  :items="currency"
                  item-text="ccyCode"
                  return-object
                  :readonly="isEdit"
                  label="币种"
                  :rules="[canEdit ? rules.required : true]"
                  outlined
                  dense
                  :disabled="currency.length === 0"
                  @change="
                    (item) => {
                      currencyId = item.currencyType
                      bankDesc = item.bank + item.account
                      setUsd(null, item.currencyId)
                    }
                  "
                ></v-select> -->
              </v-col>
              <v-col md="3" cols="12">
                <v-numeric
                  :disabled="!currencyId"
                  :precision="isJPY ? 0 : 2"
                  outlined
                  dense
                  v-model="detail.money"
                  readonly
                  label="金额"
                  :rules="[canEdit ? rules.required : true]"
                  @input="setUsd"
                ></v-numeric>
              </v-col>
              <v-col cols="12" md="3">
                <v-numeric
                  precision="2"
                  outlined
                  dense
                  label="折算美金"
                  v-model="detail.usdMoney"
                  disabled
                ></v-numeric>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  readonly
                  dense
                  label="采购数量"
                  v-model="detail.count"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.createPlace"
                  :readonly="isEdit"
                  label="创建地点"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-select
                  :items="[
                    { text: '是', value: true },
                    { text: '否', value: false },
                  ]"
                  label="是否超预算"
                  readonly
                  dense
                  v-model="detail.overBudget"
                  outlined
                ></v-select>
              </v-col>
              <v-col md="3" cols="12" v-if="this.detail.overBudget == 1">
                <v-text-field
                  outlined
                  dense
                  readonly
                  v-model="detail.budgetRemark"
                  label="超预算说明"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <!-- <v-text-field
                  outlined
                  dense
                  readonly
                  v-model="detail.accidentNo"
                  label="事故单号"
                ></v-text-field> -->
                <v-btn
                  v-if="detail.accidentId"
                  :to="{
                    name: 'accident-detail',
                    params: { id: detail.accidentId },
                  }"
                  text
                >
                  事故报告详情
                </v-btn>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="备注"
                  dense
                  outlined
                  readonly
                  v-model="detail.remark"
                  :rules="[canEdit ? rules.required : true]"
                ></v-textarea>
              </v-col>
              <v-col v-if="!isEdit">银行信息:{{ bankDesc }}</v-col>
              <v-col v-if="!!useLife">使用寿命:{{ useLife }}</v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-detail-view>
    <v-dialog v-model="dialog" max-width="1500" hide-overlay attach="#mask">
      <v-card>
        <v-card-title class="text-h5">
          重复提醒-{{ reduplicateTypes[reduplicateType] }}
        </v-card-title>
        <v-card-text>
          <v-table-list
            :table-name="''"
            :headers="duplicateHeaders"
            :items="duplicateList"
          ></v-table-list>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn depressed @click="closeForm">取消</v-btn>
          <v-btn
            depressed
            color="primary"
            :disabled="reduplicateType === 0"
            @click="confirm"
          >
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: { Treeselect },
  mixins: [currencyHelper],
  name: 'cost-project-detail',
  created() {
    this.backRouteName = 'cost-project-list'
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.userHeaders = [
      { text: '用户名', value: 'nickName' },
      { text: '岗位名称', value: 'deptName' },
      { text: '部门名称', value: 'parentDeptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.duplicateHeaders = [
      { text: '船舶', value: 'shipName', sortable: false },
      { text: '系列', value: 'seriesShip', sortable: false },
      { text: '费用科目名', value: 'subjectName', sortable: false },
      { text: '发票编号', value: 'invoiceCode' },
      { text: '供应商名', value: 'supplyName' },
      { text: '币种', value: 'ccyName' },
      { text: '原币金额', value: 'money', sortable: false },
      { text: '发生日期', value: 'happenDate', hideDefault: true },
      { text: '付款日期', value: 'payDate' },
      { text: '折算美金', value: 'usdMoney' },
      { text: '业务模块', value: 'businessModule' },
      { text: '实际申请人', value: 'applyPersonName', sortable: false },
      { text: '申请部门', value: 'applyDeptName', sortable: false },
    ]
    this.reduplicateTypes = [
      '完全相同',
      '发票号相同',
      '金额相同',
      '发生日期相同',
    ]
    this.routerMap = {
      0: 'large-purchase-detail',
      1: 'spare-order-detail',
      2: 'materials-order-detail',
      3: 'soil-order-detail',
      4: 'voyage-repair-detail',
      5: 'dock-repair-detail',
      6: 'spare-order-detail',
      7: 'hire-purchase-detail',
      8: '',
      9: 'self-repair-bonus-detail',
      10: '',
      11: '',
      12: 'materials-order-detail',
      13: 'batch-cost-detail',
    }
    this.routerMapName = {
      0: '单次预算',
      1: '备件订单',
      2: '物料订单',
      3: '滑油订单',
      4: '航修修理单',
      5: '坞修修理单',
      6: '坞修备件订单',
      7: '分期付款',
      8: '备用金',
      9: '自修奖',
      10: '船东账',
      11: '新造船',
      12: '坞修物料订单',
      13: '批量预算',
    }
    this.bussinessModules = [
      { text: '单次预算', value: 0 },
      { text: '备件订单', value: 1 },
      { text: '物料订单', value: 2 },
      { text: '滑油订单', value: 3 },
      { text: '航修修理单', value: 4 },
      { text: '坞修修理单', value: 5 },
      { text: '坞修备件订单', value: 6 },
      { text: '分期付款', value: 7 },
      { text: '备用金', value: 8 },
      { text: '自修奖', value: 9 },
      { text: '船东账', value: 10 },
      { text: '新造船', value: 11 },
      { text: '坞修物料订单', value: 12 },
      { text: '批量预算', value: 13 },
    ]
  },
  data() {
    return {
      deptTree: [],
      detail: {
        businessModule: 0,
        addPersonName: this.$local.data.get('userInfo').nickName,
        ccyId: '',
        money: 0,
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      applyPerson: {},
      searchObj: { shipCode: '', efFlag: 1, nickName: '', deptId: '1' },
      currency: [],
      shipList: [],
      initSupply: {},
      currencyId: '',
      duplicateList: [],
      dialog: false,
      reduplicateType: -1,
      bankDesc: '',
      useLife: '',
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    // usdMoney: {
    //   get() {
    //     return (
    //       this.detail.usdMoney ??
    //       (this.currencyInfo?.find((i) => i.id === this.currencyId)
    //         ?.rateToMain || 0) * (this.detail.money || 0)
    //     )
    //   },
    //   set() {
    //     // this.detail.usdMoney = val
    //   },
    // },
    canEdit() {
      return !this.detail.status
    },
    isJPY() {
      return this.detail.ccyCode === 'JPY' || this.detail.ccyCode === '日元'
    },
  },
  watch: {
    applyPerson(val) {
      if (!this.isEdit && val) {
        this.detail.applyPerson = val.id
        this.detail.applyDeptName = val.parentDeptName
        this.loadShips(val.id)
      }
    },
    detail: {
      handler(val) {
        if (!this.isEdit && val.happenDate && val.supplyId && val.shipCode) {
          this.loadHappenDate(val)
        }
      },
      deep: true,
    },
    'detail.shipCode'(val) {
      this.searchObj.shipCode = val
    },
    currencyId(val) {
      this.detail.ccyId = val
    },
  },
  methods: {
    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/shipAffairs/costProject/getById/${this.$route.params.id}`,
      )
      this.detail = data
      this.initSupply = {
        id: data.supplyId,
        name: data.supplyName,
      }
      //   this.usdMoney = data.usdMoney
      this.applyPerson = data.applyPerson
      this.currencyId = data.ccyId
      this.detail.initApply = {
        nickName: data.applyPersonName,
        id: data.applyPerson,
      }
      this.detail.initSubject = {
        subjectName: data.subjectName,
        id: data.subjectId,
      }
      this.detail.initSup = { name: data.supplyName, id: data.supplyId }
      this.getDeptTreeList()
    },

    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (!this.isEdit && !(await this.checkDuplicate())) {
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costProject/modifyCostProject',
        { ...this.detail },
      )
      if (!errorRaw) {
        this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        goBack()
      }
    },

    // 禁止保存（点击保存，增加弹框提示，不可保存，只能取消）：同发票号，同船，同供应商，同费用科目
    // 费用项目查重（点击保存，增加弹框提示，提示是否确定保存，点击确定可保存，可取消）：
    // 1. 发票号查重：同发票号，同船，同供应商
    // 2. 金额查重：同金额，同船，同供应商，同费用科目
    // 3. 发生日期查重：同发生日期，同船，同供应商
    async checkDuplicate() {
      const re0 = this.getAsync('/business/shipAffairs/costProject/list', {
        shipCode: this.detail.shipCode,
        supplyId: this.detail.supplyId,
        subjectId: this.detail.subjectId,
        invoiceCode: this.detail.invoiceCode,
        // money: this.detail.money,
        // happenDate: this.detail.happenDate,
      })
      const re1 = this.getAsync('/business/shipAffairs/costProject/list', {
        shipCode: this.detail.shipCode,
        supplyId: this.detail.supplyId,
        invoiceCode: this.detail.invoiceCode,
      })
      const re2 = this.getAsync('/business/shipAffairs/costProject/list', {
        shipCode: this.detail.shipCode,
        supplyId: this.detail.supplyId,
        subjectId: this.detail.subjectId,
        money: this.detail.money,
      })
      const re3 = this.getAsync('/business/shipAffairs/costProject/list', {
        shipCode: this.detail.shipCode,
        supplyId: this.detail.supplyId,
        happenDate: this.detail.happenDate,
      })
      const [res0, res1, res2, res3] = await Promise.all([re0, re1, re2, re3])
      if (res0.data.length) {
        this.duplicateList = res0.data
        this.reduplicateType = 0
        this.dialog = true
      } else if (res1.data.length) {
        this.duplicateList = res1.data
        this.reduplicateType = 1
        this.dialog = true
      } else if (res2.data.length) {
        this.duplicateList = res2.data
        this.reduplicateType = 2
        this.dialog = true
      } else if (res3.data.length) {
        this.duplicateList = res3.data
        this.reduplicateType = 3
        this.dialog = true
      } else {
        this.reduplicateType = -1
        return true
      }
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },

    closeForm() {
      this.dialog = false
      this.resolveFn(false)
    },

    async confirm() {
      this.dialog = false
      this.resolveFn(true)
    },

    async loadHappenDate(val) {
      const { data } = await this.postAsync(
        '/business/shipAffairs/costProject/getPayDate',
        {
          happenDate: val.happenDate,
          shipCode: val.shipCode,
          supplyId: val.supplyId,
        },
      )
      this.detail.payDate = data
    },

    select(item) {
      console.log(item)
    },

    setUsd(val, currencyId = false) {
      this.detail.usdMoney =
        (this.currencyInfo?.find(
          (i) => i.id === (currencyId || this.currencyId),
        )?.rateToMain || 0) * (val || this.detail.money || 0)
    },

    async loadShips(managerId) {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/ship/managementOwner/ships`,
        { managerId },
      )
      if (errorRaw) {
        return
      }
      this.shipList = data.map((i) => ({
        text: i.chShipName,
        value: i.shipCode,
      }))
    },
    async getDeptTreeList() {
      const { data } = await this.getAsync('/system/dept/getDeptTreeList')
      this.deptTree = data
    },
  },

  mounted() {
    this.loadDetail()
    this.getDeptTreeList()
  },
}
</script>

//
<style lang="scss" scoped>
::v-deep input {
  text-align: left !important;
}
</style>
