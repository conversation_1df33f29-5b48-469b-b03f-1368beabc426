<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      use-ship
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      :search-date="searchDate"
      fuzzy-label="备注/发票号/模块编号"
    >
      <!-- use-series -->
      <template #searchflieds>
        <!-- <v-col cols="12" md="2">
          <v-dict-select
            clearable
            v-model="searchObj.seriesShip"
            label="系列船舶"
            dense
            outlined
            dict-type="series_ship"
          ></v-dict-select>
        </v-col> -->
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessModule"
            label="业务模块"
            outlined
            dense
            clearable
            :items="bussinessModules"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.status"
            :items="status"
            label="状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-dict-select
            clearable
            v-model="searchObj.subjectType"
            label="费用类型"
            dense
            outlined
            dict-type="cost_subject_type"
          ></v-dict-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-autocomplete
            clearable
            v-model="searchObj.subjectId"
            label="费用科目"
            dense
            outlined
            :items="costSubjects"
            :disabled="!searchObj.subjectType"
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-supply-select-list
            v-model="searchObj.supplyId"
            label="供应商"
            outlined
            clearable
            dense
          ></v-supply-select-list>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-handler
            clearable
            label="实际申请人"
            v-model="searchObj.applyPerson"
            :use-current="false"
          ></v-handler>
        </v-col>
        <v-col cols="12" sm="6" md="4">
          <v-menu
            v-model="datesMenu2"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                ref="dates2"
                :value="dateRangeText2"
                :label="'发生日期'"
                append-icon="mdi-calendar"
                outlined
                dense
                readonly
                clearable
                @click:clear="dates2 = []"
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <vc-date-picker
              v-model="dates2"
              mode="date"
              is-range
            ></vc-date-picker>
          </v-menu>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :loading="loading"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="downloadExcel"
          v-permission="['费用项目:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          :to="{ name: 'cost-gen-budget' }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['费用项目:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!-- <v-btn
          @click="copyProject"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['费用项目:新增']"
          :disabled="selected.businessModule != 0"
        >
          <v-icon left>mdi-content-copy</v-icon>
          复制
        </v-btn> -->
        <!-- <v-btn
          :to="{ name: 'cost-project-detail', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['费用项目:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn> -->
        <!-- :disabled="!selected || selected.businessModule == 13" -->
        <v-btn
          :disabled="
            selected.addPerson != userId ||
            selected.businessModule == 13 ||
            selected.status != 0
          "
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['费用项目:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.businessModule`]="{ item }">
        {{
          [
            '单次预算',
            '备件订单',
            '物料订单',
            '滑油订单',
            '航修修理单',
            '坞修修理单',
            '坞修备件订单',
            '分期付款',
            '备用金',
            '自修奖',
            '船东账',
            '新造船',
            '坞修物料订单',
            '批量预算',
          ][item.businessModule]
        }}
      </template>
      <template v-slot:[`item.overBudget`]="{ item }">
        {{ item.overBudget ? '是' : '否' }}
      </template>
      <template v-slot:[`item.budgetRemark`]="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <span v-on="on">{{ item.budgetRemark.substring(0, 25) }}...</span>
          </template>
          <span>{{ item.budgetRemark }}</span>
        </v-tooltip>
      </template>
      <template v-slot:[`item.orderCode`]="{ item }">
        <v-btn
          v-if="routerMap[item.businessModule] !== ''"
          :to="{
            name: routerMap[item.businessModule],
            params: { id: item.businessId },
          }"
          text
          small
        >
          {{ item.orderCode }}
        </v-btn>
        <!-- <div v-else>-</div> -->
        <div v-else>{{ item.orderCode }}</div>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ stateMap[item.status] }}
      </template>
      <template v-slot:[`item.repairFlag`]="{ item }">
        {{ item.repairFlag === 1 ? '是' : '否' }}
      </template>
      <template v-slot:[`item.voucher`]="{ item }">
        <router-link
          v-if="item.costOrderId"
          :to="{
            name: 'cost-voucher-detail',
            params: { id: item.costOrderId },
          }"
        >
          发票详情
        </router-link>
        <!-- <router-link
          v-if="item.costOrderId && item.businessModule != '13'"
          :to="{
            name: 'cost-voucher-detail',
            params: { id: item.costOrderId },
          }"
        >
          凭证
        </router-link> -->
        <!-- <router-link
          v-else-if="item.costOrderId && item.businessModule == '13'"
          :to="{
            name: 'batch-cost-detail',
            params: { id: item.costOrderId },
          }"
        >
          批量预算
        </router-link> -->
        <div v-else>-</div>
      </template>
      <template v-slot:[`item.money`]="{ item }">
        {{ item.money.toLocaleString() }}
      </template>
      <template v-slot:[`item.usdMoney`]="{ item }">
        {{ item.usdMoney.toLocaleString() }}
      </template>
    </v-table-searchable>
    <copy-dialog ref="dialog" :initialData="selected || {}"></copy-dialog>
  </v-container>
</template>
<script>
import qs from 'qs'
// applyDept	申请部门id(由申请人带出)	string
// applyDeptName	申请部门名(由申请人带出)	string
// applyPerson	实际申请人Id	string
// applyPersonName	实际申请人名	string
// businessModule	业务模块（写死：0手动录入 1备件订单 2物料订单 3滑油订单 4航修修理单 5坞修修理单 6坞修备件订单 7分期付款 8备用金 9自修奖 10船东账 11新造船 12坞修物料订单）	integer
// ccyId	币种id	string
// ccyName	币种	string
// happenDate	发生日期	string
// id	物理主键	string
// invoiceCode	发票编号	string
// money	金额	number
// orderCode	订单（修理单）号（手动录入时为空）	string
// payDate	付款日期（发生日期+赊销期）	string
// remark	备注	string
// repairFlag	是否坞修 0否 1是	integer
// shipCode	船舶编码	string
// shipName	船舶名	string
// status	状态 0：未做凭证 1：已做凭证 2：审批中 3：审批通过 4：已生成SAP报文 5：已发送SAP 6：SAP审批未通过 7：付款审批中 8：付款审批通过 9：付款审批未通过 10：已付款	integer
// subjectId	费用科目id	string
// subjectName	费用科目名称	string
// supplyId	供应商	string
// supplyName	供应商名	string
// usdMoney	折算美金	number
import { stateMap, statues } from '../private/constant'
import copyDialog from './private/copy-dialog.vue'
export default {
  components: { copyDialog },
  name: 'cost-project-list',
  created() {
    this.tableName = '发票行项目明细'
    this.reqUrl = '/business/shipAffairs/costProject/page'
    this.headers = [
      { text: '船舶', value: 'shipName' },
      { text: '系列', value: 'seriesShip', sortable: false },
      { text: '费用科目名', value: 'subjectName', sortable: false },
      { text: '发票编号', value: 'invoiceCode' },
      { text: '供应商名', value: 'supplyName' },
      { text: '币种', value: 'ccyName' },
      { text: '原币金额', value: 'money', sortable: false },
      { text: '是否超预算', value: 'overBudget' },
      { text: '超预算说明', value: 'budgetRemark' },
      { text: '发生日期', value: 'happenDate', hideDefault: true },
      { text: '付款日期', value: 'payDate' },
      { text: '录单时间', value: 'addDate', hideDefault: true },
      { text: '折算美金', value: 'usdMoney' },
      { text: '业务模块', value: 'businessModule' },
      {
        text: '模块编号',
        value: 'orderCode',
        //hideDefault: true,
        sortable: false,
      },
      { text: '实际申请人', value: 'applyPersonName', sortable: false },
      { text: '申请部门', value: 'parentDeptName', sortable: false },
      { text: '状态', value: 'status' },
      { text: '坞修', value: 'repairFlag', hideDefault: true, sortable: false },
      { text: '采购数量', value: 'count', hideDefault: true },
      { text: '发票', value: 'voucher' },
      { text: '备注', value: 'remark' },
    ]
    this.routerMap = {
      0: '',
      1: 'spare-order-detail',
      2: 'materials-order-detail',
      3: 'soil-order-detail',
      4: 'voyage-repair-detail',
      5: 'dock-repair-detail',
      6: 'spare-order-detail',
      7: 'hire-purchase-detail',
      8: '',
      9: 'self-repair-bonus-detail',
      10: '',
      11: '',
      12: 'materials-order-detail',
      13: 'batch-cost-detail',
    }
    this.bussinessModules = [
      { text: '单次预算', value: 0 },
      { text: '备件订单', value: 1 },
      { text: '物料订单', value: 2 },
      { text: '滑油订单', value: 3 },
      { text: '航修修理单', value: 4 },
      { text: '坞修修理单', value: 5 },
      { text: '坞修备件订单', value: 6 },
      { text: '分期付款', value: 7 },
      { text: '备用金', value: 8 },
      { text: '自修奖', value: 9 },
      { text: '船东账', value: 10 },
      { text: '新造船', value: 11 },
      { text: '坞修物料订单', value: 12 },
      { text: '批量预算', value: 13 },
    ]
    this.pushParams = { name: 'cost-project-detail' }
    this.stateMap = stateMap
    this.status = statues
    this.searchDate = {
      label: '录单时间',
      interval: true,
    }
  },

  data() {
    return {
      selected: false,
      searchObj: {},
      costSubjects: [],
      loading: false,
      userId: this.$local.data.get('userInfo').id,
      datesMenu2: false,
      dates2: [],
    }
  },

  watch: {
    'searchObj.subjectType': {
      handler: function (val) {
        this.getCostSubjects(val)
      },
    },
    dates2: {
      handler() {
        if (this.dates2?.start && this.dates2?.end) {
          this.datesMenu2 = false
          this.searchObj.fromTime2 = this.dates2?.start
            .toISOString()
            .split('T')[0]
          this.searchObj.toTime2 = this.dates2?.end.toISOString().split('T')[0]

          console.log(
            this.searchObj.fromTime2,
            this.searchObj.toTime2,
            this.dates2,
          )
          this.$refs.table.loadTableData()
        } else {
          this.$refs.table.loadTableData()
        }
      },
    },
  },

  computed: {
    excelUrl() {
      return `/business/shipAffairs/costProject/excelExport?${qs.stringify(
        this.searchObj,
      )}`
    },
    dateRangeText2() {
      return this.dates2?.start && this.dates2?.end
        ? `${this.dates2.start.toLocaleDateString()} 至 ${this.dates2?.end.toLocaleDateString()}`
        : ''
    },
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costProject/deleteCostProject',
        [this.selected.id],
      )
      if (!errorRaw) this.$dialog.message.success('删除成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },

    async getCostSubjects(subjectType) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/costSubject/page',
        {
          subjectType,
          size: 1000,
          current: 1,
        },
      )
      this.costSubjects = data.records.map((item) => ({
        text: item.subjectName,
        value: item.id,
      }))
    },

    async downloadExcel() {
      this.loading = true
      let params = { ...this.$refs.table.searchRemain }
      params = {
        ...params,
        fuzzyParam: this.$refs.table.fuzzyParam,
        shipCode: this.$refs.table.ship,
        fromTime: this.dates?.start?.toISOString()?.split('T')?.[0],
        toTime: this.dates?.end?.toISOString()?.split('T')?.[0],
      }
      await this.getBlobDownload(
        '/business/shipAffairs/costProject/excelExport',
        params,
        // 时间戳后四位
        `费用项目-${new Date().getTime().toString().slice(-4)}.xlsx`,
      )
      this.loading = false
    },

    async copyProject() {
      // if (!(await this.$dialog.msgbox.confirm('确定复制此记录？'))) return
      const { num } = await this.$refs.dialog.confirm()
      if (!num) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costProject/copyById/${this.selected.id}/${num}`,
      )
      if (!errorRaw) this.$dialog.message.success('复制成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
