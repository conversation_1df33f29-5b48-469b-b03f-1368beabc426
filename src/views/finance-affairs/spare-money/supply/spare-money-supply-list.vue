<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      use-ship
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            outlined
            label="状态"
            dense
            :items="statusMap"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="selected.businessStatus !== '已完成'"
          outlined
          tile
          class="mx-1"
          @click="send(0)"
          v-permission="['备用金补充:发送SAP核算包']"
        >
          <v-icon left>mdi-send</v-icon>
          发送SAP核算包
        </v-btn>
        <v-btn
          :disabled="selected.businessStatus !== '已发送SAP'"
          outlined
          tile
          class="mx-1"
          @click="send(1)"
          v-permission="['备用金补充:发送SAP评审包']"
        >
          <v-icon left>mdi-send</v-icon>
          发送SAP评审包
        </v-btn>
        <v-btn
          :disabled="selected.businessStatus !== '已发送OA'"
          outlined
          tile
          class="mx-1"
          @click="send(2)"
          v-permission="['备用金补充:船长确认']"
        >
          <v-icon left>mdi-check</v-icon>
          船长确认
        </v-btn>
        <v-btn
          :to="{ name: 'spare-money-supply-detail', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['备用金补充:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status !== 0 && selected.status !== 3"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['备用金补充:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses[item.status] }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// addCny	补充的人民币	number
// addUsd	补充的美元	number
// applyDept	申请部门	string
// applyDeptName	申请部门名	string
// applyPerson	申请人	string
// applyPersonName	申请人名	string
// applyTime	申请时间	string
// applyType	申请类型 0消耗备用金1补充备用金2更改船长信息	integer
// businessStatus	业务状态	string
// captainBank	船长开户行	string
// captainBankCode	船长银行卡号	string
// captainName	船长名	string
// captainSapCode	船长sapcode	string
// cnyRes	人民币结果	number
// cnyTotal	人民币金额总计	number
// id	物理主键	string
// joinPlace	交接地点	string
// joinTime	交接时间	string
// post	职务	string
// projectCode	项目编号	string
// remark	备注	string
// shipCode	船舶编码	string
// shipName	船舶名	string
// spareInfoId	备用金主表id	string
// status	审核状态0：未开始；1：进行中；2：已完成；3：已驳回	integer
// usdRes	美元结果	number
// usdTotal	美元金额总计	number
export default {
  name: 'spare-money-supply-list',
  created() {
    this.tableName = '备用金-补充'
    this.reqUrl = '/business/shipAffairs/spareApply/page'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipName' },
      { text: '项目编号', value: 'projectCode' },
      { text: '补充的美元', value: 'addCny' },
      { text: '补充的人民币', value: 'addUsd' },
      { text: '申请人', value: 'applyPersonName' },
      { text: '申请时间', value: 'applyTime' },
      { text: '审核状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'spare-money-supply-detail' }
    this.statusColors = ['', 'warning', 'success', 'error']
    this.statuses = ['草稿', '审批中', '已审批', '已驳回']
    this.statusMap = [
      { text: '草稿', value: 0 },
      { text: '审批中', value: 1 },
      { text: '已审批', value: 2 },
      { text: '已驳回', value: 3 },
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: { applyType: 1 },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/spareApply/deleteSpareApply',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },

    async send(type) {
      const url = [
        '/business/shipAffairs/spareApply/sendSap',
        '/business/shipAffairs/spareApply/sendOA',
        '/business/shipAffairs/spareApply/ownerCheck',
      ][type]
      const { errorRaw } = await this.getAsync(url, { id: this.selected.id })
      if (errorRaw) return
      this.$dialog.message.success('发送成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
