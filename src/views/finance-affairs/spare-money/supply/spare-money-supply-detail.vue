<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['备用金补充:编辑']"
      :title="`备用金补充 ${isEdit ? detail.projectCode : '新增'}`"
      :tooltip="isEdit ? detail.projectCode : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        (!canAddCny || !canAddUsd) &&
        (!detail.auditParams || detail.auditParams.taskId)
      "
      @save="save"
      @submit="submit"
      :can-save="!canAddCny || !canAddUsd"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <v-card-text>
            <v-form ref="form">
              <v-row>
                <v-col class="pt-0" cols="12" md="3">
                  <v-ship-select
                    :readonly="isEdit"
                    v-model="detail.shipCode"
                    dense
                    :rules="[rules.required]"
                    :clearable="false"
                  ></v-ship-select>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-handler
                    label="申请人"
                    :readonly="isEdit"
                    v-model="detail.applyPerson"
                    :init-user="initHandler"
                    :use-current="!isEdit"
                    :rules="[rules.required]"
                    @selectUser="
                      (user) => (detail.applyDeptName = user.deptName)
                    "
                  ></v-handler>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    label="申请部门"
                    v-model="detail.applyDeptName"
                    outlined
                    dense
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <vs-date-picker
                    label="申请时间"
                    v-model="detail.applyTime"
                    :readonly="isEdit"
                    outlined
                    dense
                    use-today
                    :rules="[rules.required]"
                  ></vs-date-picker>
                </v-col>
                <v-col
                  class="pt-0"
                  v-for="h in tableFeilds"
                  :key="h.value"
                  cols="12"
                  md="3"
                >
                  <v-text-field
                    v-if="h.value == 'addCny'"
                    v-model="detail[h.value]"
                    :rules="[rules.maxCny, rules.required]"
                    :label="h.text"
                    type="number"
                    dense
                    outlined
                    :readonly="!canEdit"
                    :disabled="canAddCny"
                  ></v-text-field>
                  <v-text-field
                    v-else
                    v-model="detail[h.value]"
                    :rules="[rules.maxUsd, rules.required]"
                    :label="h.text"
                    type="number"
                    dense
                    outlined
                    :readonly="!canEdit"
                    :disabled="canAddUsd"
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    v-model="detail.cnyRes"
                    label="人民币余额"
                    type="number"
                    dense
                    outlined
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    v-model="detail.usdRes"
                    label="美元余额"
                    type="number"
                    dense
                    outlined
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="12">
                  <v-textarea
                    outlined
                    label="备注"
                    dense
                    v-model="detail.remark"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-container>
        <v-card-text>
          <v-attach-list
            :attachments="detail.commonAttachments"
            @change="(ids) => (detail.attachmentIds = ids)"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-if="shipSpareInfo.id" #当前船舶信息>
        <v-container fluid>
          <v-row>
            <v-col cols="12" md="2" v-for="(h, i) in shipSpareFeilds" :key="i">
              {{ h.text }}：{{ shipSpareInfo[h.value] }}
            </v-col>
          </v-row>
        </v-container>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
// addCny	补充的人民币		false	 number
// addUsd	补充的美元		false	 number
// applyDept	申请部门		false	 string
// applyPerson	申请人		false	 string
// applyTime	申请时间		false	 string(date-time)
// applyType	申请类型 0消耗备用金1补充备用金2更改船长信息		false	 integer(int32)
// attachmentIds	附件		false	 array string
// captainBank	船长开户行		false	 string
// captainBankCode	船长银行卡号		false	 string
// captainName	船长名		false	 string
// captainSapCode	船长sapcode		false	 string
// cnyRes	人民币余额		false	 number
// cnyTotal	人民币金额总计		false	 number
// costDetail	费用明细		false	 array
// SpareProject
// crewSupervisor	船员主管		false	 array string
// id	物理主键		false	 string
// joinPlace	交接地点		false	 string
// joinTime	交接时间		false	 string(date-time)
// post	职务		false	 string
// projectCode	项目编号		false	 string
// remark	备注		false	 string
// shipCode	船舶编码		true	 string
// usdRes	美元余额		false	 number
// usdTotal	美元金额总计		false	 number
export default {
  name: 'spare-money-supply-detail',
  created() {
    this.backRouteName = 'spare-money-supply-list'
    this.subtitles = ['基本信息', '当前船舶信息']
    this.tableFeilds = [
      { text: '补充的人民币', value: 'addCny' },
      { text: '补充的美元', value: 'addUsd' },
    ]
    // captainBank	船长开户行	string
    // captainBankCode	船长银行卡号	string
    // captainName	船长名	string
    // captainSapCode	船长sapcode	string
    // cnyBottom	人民币下限	number
    // cnyTop	人民币上限	number
    // id	物理主键	string
    // joinPlace	交接地点	string
    // joinTime	交接时间	string(date-time)
    // remainCny	剩余人民币	number
    // remainUsd	剩余美元	number
    // remark	备注	string
    // shipCode	船舶编码	string
    // shipName	船舶名	string
    // usdBottom	美元下限	number
    // usdTop	美元上限	number
    this.shipSpareFeilds = [
      { text: '人民币下限', value: 'cnyBottom' },
      { text: '人民币上限', value: 'cnyTop' },
      { text: '美元下限', value: 'usdBottom' },
      { text: '美元上限', value: 'usdTop' },
      { text: '剩余人民币', value: 'remainCny' },
      { text: '剩余美元', value: 'remainUsd' },
      { text: '当前船长名', value: 'captainName' },
      { text: '当前船长开户行', value: 'captainBank' },
      { text: '当前船长银行卡号', value: 'captainBankCode' },
      { text: '当前船长sapcode', value: 'captainSapCode' },
    ]
  },
  data() {
    return {
      detail: {
        attachmentIds: [],
        commonAttachments: [],
        shipCode: '',
        applyType: 1,
        applyDeptName: this.$local.data.get('userInfo').deptName,
        addCny: 0,
        addUsd: 0,
      },
      shipSpareInfo: {},
      rules: {
        required: (v) => !!v || v >= 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        maxUsd: () => '未获取到当前船舶信息',
        maxCny: () => '未获取到当前船舶信息',
      },
      initHandler: false,
    }
  },

  watch: {
    'detail.shipCode'(val) {
      if (!val) return
      this.loadShipSpareInfo()
    },
    'detail.addUsd'(val) {
      if (this.isEdit) return
      this.detail.usdRes = this.shipSpareInfo.remainUsd + val * 1
    },
    'detail.addCny'(val) {
      if (this.isEdit) return
      this.detail.cnyRes = this.shipSpareInfo.remainCny + val * 1
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return this.detail.status !== 1 && this.detail.status !== 2
    },
    canAddCny() {
      return this.shipSpareInfo.remainCny >= this.shipSpareInfo.cnyBottom
    },
    canAddUsd() {
      return this.shipSpareInfo.remainUsd >= this.shipSpareInfo.usdBottom
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return
      if (this.detail.addCny == 0 && this.detail.addUsd == 0) {
        this.$dialog.message.error('补充的人民币和美元不能同时为0')
        return
      }
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/spareApply/modifySpareApply',
        this.detail,
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/spareApply/process/start',
            { id: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/shipAffairs/spareApply/getById/${this.$route.params.id}`,
      )
      this.detail = {
        ...data,
      }
      this.$refs.form.resetValidation()
      this.initHandler = {
        id: data.applyPerson,
        nickName: data.applyPersonName,
      }
    },

    async loadShipSpareInfo() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/spareInfo/getByCode/${this.detail.shipCode}`,
      )
      if (!data) {
        this.shipSpareInfo = {}
        this.$dialog.message.error('当前船舶未维护备用金信息,无法进行操作')
        this.rules.maxUsd = () => '未获取到当前船舶信息'
        this.rules.maxCny = () => '未获取到当前船舶信息'
        return
      }
      this.shipSpareInfo = data
      this.detail.usdRes = this.shipSpareInfo.remainUsd
      this.detail.cnyRes = this.shipSpareInfo.remainCny
      if (
        !this.isEdit &&
        this.shipSpareInfo.remainCny >= this.shipSpareInfo.cnyBottom
      ) {
        this.$dialog.message.warning(
          '当前船舶人民币备用金已超过下限,无法进行操作',
        )
      }
      if (
        !this.isEdit &&
        this.shipSpareInfo.remainUsd >= this.shipSpareInfo.usdBottom
      ) {
        this.$dialog.message.warning(
          '当前船舶美元备用金已超过下限,无法进行操作',
        )
      }
      this.rules.maxUsd = (v) =>
        !v ||
        v <= this.shipSpareInfo.usdTop - this.shipSpareInfo.remainUsd ||
        '美元补充金额不得超过船舶上限'
      this.rules.maxCny = (v) =>
        !v ||
        v <= this.shipSpareInfo.cnyTop - this.shipSpareInfo.remainCny ||
        '人民币补充金额不得超过船舶上限'
      this.$refs.form.resetValidation()
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
