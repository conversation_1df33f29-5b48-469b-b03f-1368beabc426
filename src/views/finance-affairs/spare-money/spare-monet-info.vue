<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="mt-2 px-0" fluid>
            <v-row>
              <v-col
                class="pt-0"
                v-for="h in headers"
                :key="h.value"
                cols="12"
                :md="h.value === 'remark' ? 12 : 3"
              >
                <v-ship-select
                  v-if="h.value === 'shipName'"
                  v-model="formData.shipCode"
                  :disabled="isEdit"
                  :rules="[rules.required]"
                ></v-ship-select>
                <v-text-field
                  v-else-if="h.type == 'number'"
                  v-model="formData[h.value]"
                  :rules="[rules.required]"
                  :label="h.text"
                  type="number"
                  dense
                  outlined
                  :disabled="isEdit && h.noEdit"
                ></v-text-field>
                <v-textarea
                  v-else-if="h.value === 'remark'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-textarea>
                <v-text-field
                  v-else-if="h.value === 'captainBankCode'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  :rules="[rules.bankCard]"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  :rules="[rules.required]"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                  :disabled="isEdit && h.noEdit"
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['备用金设置:编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
      use-ship
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['备用金设置:初始化船舶']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          初始化船舶
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editItem"
          v-permission="['备用金设置:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['备用金设置:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// captainBank	船长开户行	string
// captainBankCode	船长银行卡号	string
// captainName	船长名	string
// captainSapCode	船长sapcode	string
// id	物理主键	string
// remainCny	剩余人民币	number
// remainUsd	剩余美元	number
// shipCode	船舶编码	string
// shipName	船舶名	string
// cnyBottom	人民币下限
// cnyTop	人民币上限
// usdBottom	美元下限
// usdTop	美元上限
export default {
  name: 'spare-monet-info',
  created() {
    this.tableName = '备用金设置'
    this.reqUrl = '/business/shipAffairs/spareInfo/page'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipName', noEdit: true },
      { text: '船长名', value: 'captainName', noEdit: true },
      { text: '船长开户行', value: 'captainBank', noEdit: true },
      { text: '船长银行卡号', value: 'captainBankCode', noEdit: true },
      { text: '船长sapcode', value: 'captainSapCode', noEdit: true },
      { text: '剩余人民币', value: 'remainCny', noEdit: true, type: 'number' },
      { text: '剩余美元', value: 'remainUsd', noEdit: true, type: 'number' },
      { text: '人民币下限', value: 'cnyBottom', type: 'number' },
      { text: '人民币上限', value: 'cnyTop', type: 'number' },
      { text: '美元下限', value: 'usdBottom', type: 'number' },
      { text: '美元上限', value: 'usdTop', type: 'number' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        bankCard: (v) => /^\d{16,19}$/.test(v) || '银行卡号16-19位数字组成',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/spareInfo/deleteSpareInfo',
        [this.selected.id],
      )
      if (errorRaw) return
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/spareInfo/modifySpareInfo'
        : '/business/shipAffairs/spareInfo/initSpareInfo'
      const { errorRaw } = await this.postAsync(reqUrl, { ...this.formData })
      if (errorRaw) return
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
