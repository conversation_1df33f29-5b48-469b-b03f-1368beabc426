<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船长变更:编辑']"
      :title="`备用金船长变更-${isEdit ? detail.projectCode : '新增'}`"
      :tooltip="isEdit ? detail.projectCode : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <v-card-text>
            <v-form ref="form">
              <v-row>
                <v-col class="pt-0" cols="12" md="3">
                  <v-ship-select
                    :readonly="isEdit"
                    v-model="detail.shipCode"
                    dense
                    :rules="[rules.required]"
                  ></v-ship-select>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-handler
                    label="申请人"
                    :readonly="isEdit"
                    v-model="detail.applyPerson"
                    :init-user="initHandler"
                    :use-current="!isEdit"
                    :rules="[rules.required]"
                    @selectUser="
                      (user) => (detail.applyDeptName = user.deptName)
                    "
                  ></v-handler>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    label="申请部门"
                    v-model="detail.applyDeptName"
                    outlined
                    dense
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <vs-date-picker
                    label="申请时间"
                    v-model="detail.applyTime"
                    :readonly="isEdit"
                    outlined
                    dense
                    use-today
                    :rules="[rules.required]"
                  ></vs-date-picker>
                </v-col>
                <v-col
                  class="pt-0"
                  v-for="h in tableFeilds"
                  :key="h.value"
                  cols="12"
                  md="3"
                >
                  <v-text-field
                    v-if="h.type == 'number'"
                    v-model="detail[h.value]"
                    :rules="[rules.required]"
                    :label="h.text"
                    type="number"
                    dense
                    outlined
                    :readonly="!canEdit"
                  ></v-text-field>
                  <v-text-field
                    v-else-if="h.value === 'captainBankCode'"
                    v-model="detail[h.value]"
                    :label="h.text"
                    :rules="[rules.bankCard]"
                    dense
                    outlined
                    :readonly="!canEdit"
                  ></v-text-field>
                  <vs-date-picker
                    v-else-if="h.type === 'date'"
                    v-model="detail[h.value]"
                    :label="h.text"
                    :rules="[rules.required]"
                    dense
                    outlined
                    :readonly="!canEdit"
                  ></vs-date-picker>
                  <v-text-field
                    v-else
                    :rules="[rules.required]"
                    v-model="detail[h.value]"
                    :label="h.text"
                    dense
                    outlined
                    :readonly="!canEdit"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="12">
                  <v-textarea
                    outlined
                    label="备注"
                    dense
                    v-model="detail.remark"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-container>
        <v-card-text>
          <v-attach-list
            :attachments="detail.commonAttachments"
            @change="(ids) => (detail.attachmentIds = ids)"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-if="shipSpareInfo.id" #当前船舶信息>
        <v-container fluid>
          <v-row>
            <v-col cols="12" md="2" v-for="(h, i) in shipSpareFeilds" :key="i">
              {{ h.text }}：{{ shipSpareInfo[h.value] }}
            </v-col>
          </v-row>
        </v-container>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
// addCny	补充的人民币		false	 number
// addUsd	补充的美元		false	 number
// applyDept	申请部门		false	 string
// applyPerson	申请人		false	 string
// applyTime	申请时间		false	 string(date-time)
// applyType	申请类型 0消耗备用金1补充备用金2更改船长信息		false	 integer(int32)
// attachmentIds	附件		false	 array string
// captainBank	船长开户行		false	 string
// captainBankCode	船长银行卡号		false	 string
// captainName	船长名		false	 string
// captainSapCode	船长sapcode		false	 string
// cnyRes	人民币结果		false	 number
// cnyTotal	人民币金额总计		false	 number
// costDetail	费用明细		false	 array
// SpareProject
// crewSupervisor	船员主管		false	 array string
// id	物理主键		false	 string
// joinPlace	交接地点		false	 string
// joinTime	交接时间		false	 string(date-time)
// post	职务		false	 string
// projectCode	项目编号		false	 string
// remark	备注		false	 string
// shipCode	船舶编码		true	 string
// usdRes	美元结果		false	 number
// usdTotal	美元金额总计		false	 number
export default {
  name: 'captain-change-detail',
  created() {
    this.backRouteName = 'captain-change-list'
    this.subtitles = ['基本信息', '当前船舶信息']
    this.tableFeilds = [
      { text: '变更的船长名', value: 'captainName' },
      { text: '变更的船长开户行', value: 'captainBank' },
      { text: '变更的船长银行卡号', value: 'captainBankCode' },
      { text: '变更的船长sapcode', value: 'captainSapCode' },
      { text: '交接地点', value: 'joinPlace' },
      { text: '交接时间', value: 'joinTime', type: 'date' },
    ]
    // captainBank	船长开户行	string
    // captainBankCode	船长银行卡号	string
    // captainName	船长名	string
    // captainSapCode	船长sapcode	string
    // cnyBottom	人民币下限	number
    // cnyTop	人民币上限	number
    // id	物理主键	string
    // joinPlace	交接地点	string
    // joinTime	交接时间	string(date-time)
    // remainCny	剩余人民币	number
    // remainUsd	剩余美元	number
    // remark	备注	string
    // shipCode	船舶编码	string
    // shipName	船舶名	string
    // usdBottom	美元下限	number
    // usdTop	美元上限	number
    this.shipSpareFeilds = [
      { text: '船舶名', value: 'shipName' },
      { text: '人民币下限', value: 'cnyBottom' },
      { text: '人民币上限', value: 'cnyTop' },
      { text: '美元下限', value: 'usdBottom' },
      { text: '美元上限', value: 'usdTop' },
      { text: '剩余人民币', value: 'remainCny' },
      { text: '剩余美元', value: 'remainUsd' },
      { text: '当前船长名', value: 'captainName' },
      { text: '当前船长开户行', value: 'captainBank' },
      { text: '当前船长银行卡号', value: 'captainBankCode' },
      { text: '当前船长sapcode', value: 'captainSapCode' },
    ]
  },
  data() {
    return {
      detail: {
        attachmentIds: [],
        commonAttachments: [],
        shipCode: '',
        applyType: 2,
        applyDeptName: this.$local.data.get('userInfo').deptName,
      },
      shipSpareInfo: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        bankCard: (v) => /^\d{16,19}$/.test(v) || '银行卡号16-19位数字组成',
      },
      initHandler: false,
    }
  },

  watch: {
    'detail.shipCode'(val) {
      if (!val) return
      this.loadShipSpareInfo()
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return this.detail.status !== 1 && this.detail.status !== 2
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/spareApply/modifySpareApply',
        this.detail,
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/spareApply/process/start',
            { id: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/shipAffairs/spareApply/getById/${this.$route.params.id}`,
      )
      this.detail = {
        ...data,
      }
      this.initHandler = {
        id: data.applyPerson,
        nickName: data.applyPersonName,
      }
    },

    async loadShipSpareInfo() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/spareInfo/getByCode/${this.detail.shipCode}`,
      )
      this.shipSpareInfo = data
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
