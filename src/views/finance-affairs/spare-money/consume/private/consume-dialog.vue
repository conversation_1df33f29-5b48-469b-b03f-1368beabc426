<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        备用金消耗
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-select
                  v-model="formData.ccyCode"
                  :items="['USD', 'CNY']"
                  item-text="ccyCode"
                  item-value="ccyCode"
                  label="币种"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="formData.subjectId"
                  :rules="[rules.required]"
                  :init-selected="formData.initSubject"
                  :search-dicts="searchDicts"
                  item-text="subjectName"
                  item-value="id"
                  fuzzy-label="模糊查询"
                  :headers="subHeaders"
                  :readonly="isEdit"
                  required
                  dense
                  @select="
                    (item) => {
                      formData.subjectName = item.subjectName
                    }
                  "
                ></v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.payMoney"
                  label="付款金额"
                  dense
                  :rules="[rules.required]"
                  type="number"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.place"
                  label="地点"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="formData.date"
                  label="日期"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="formData.payDetail"
                  label="支付明细"
                  dense
                  outlined
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import currencyHelper from '@/mixin/currencyHelper'
// ccyCode	币种code		true	string
// date	日期		false	string
// payDetail	支付明细		false	string
// payMoney	付款金额		true	number
// place	地点		false	string
// subjectId	科目id		true	string
// subjectName	科目名		true	string
export default {
  name: 'consume-dialog',
  mixins: [currencyHelper],
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.formData.initSubject = this.initialData?.subjectId
        ? {
            id: this.initialData?.subjectId,
            subjectName: this.initialData?.subjectName,
          }
        : {}
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.$emit('change', false)
      if (this.isEdit) return
      this.$emit('success', {
        ...this.formData,
        id: this.formData?.id || Math.random(10000),
      })
    },
  },

  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
