<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['备用金消耗:编辑']"
      :title="`备用金消耗 ${isEdit ? detail.projectCode : '新增'}`"
      :tooltip="isEdit ? detail.projectCode : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <v-card-text>
            <v-form ref="form">
              <v-row>
                <v-col class="pt-0" cols="12" md="3">
                  <v-ship-select
                    :readonly="isEdit"
                    v-model="detail.shipCode"
                    dense
                    :rules="[rules.required]"
                    :clearable="false"
                  ></v-ship-select>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-handler
                    label="申请人"
                    :readonly="isEdit"
                    v-model="detail.applyPerson"
                    :init-user="initHandler"
                    :use-current="!isEdit"
                    :rules="[rules.required]"
                    @selectUser="
                      (user) => (detail.applyDeptName = user.deptName)
                    "
                  ></v-handler>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    label="申请部门"
                    v-model="detail.applyDeptName"
                    outlined
                    dense
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <vs-date-picker
                    label="申请时间"
                    v-model="detail.applyTime"
                    :readonly="isEdit"
                    outlined
                    dense
                    use-today
                    :rules="[rules.required]"
                  ></vs-date-picker>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    v-model="detail.cnyRes"
                    label="人民币余额"
                    type="number"
                    dense
                    outlined
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    v-model="detail.usdRes"
                    label="美元余额"
                    type="number"
                    dense
                    outlined
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    v-model="detail.cnyTotal"
                    label="人民币合计"
                    type="number"
                    dense
                    outlined
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col class="pt-0" cols="12" md="3">
                  <v-text-field
                    v-model="detail.usdTotal"
                    label="美元合计"
                    type="number"
                    dense
                    outlined
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="12">
                  <v-textarea
                    outlined
                    label="备注"
                    dense
                    v-model="detail.remark"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-container>
      </template>
      <template v-if="canEdit" #消耗详情按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createConsumption"
          v-permission="['消耗详情:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!select"
          outlined
          tile
          small
          color="warning"
          class="mx-1"
          @click="editConsumption"
          v-permission="['消耗详情:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!select"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delConsumption"
          v-permission="['消耗详情:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #消耗详情>
        <v-table-list
          v-model="select"
          :headers="headers"
          :items="detail.costDetail"
        ></v-table-list>
        <v-card-text>
          <v-attach-list
            :attachments="detail.commonAttachments"
            @change="(ids) => (detail.attachmentIds = ids)"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-if="shipSpareInfo.id" #当前船舶信息>
        <v-container fluid>
          <v-row>
            <v-col cols="12" md="2" v-for="(h, i) in shipSpareFeilds" :key="i">
              {{ h.text }}：{{ shipSpareInfo[h.value] }}
            </v-col>
          </v-row>
        </v-container>
      </template>
      <consume-dialog
        v-model="dialog"
        :initialData="initialData"
        @success="
          (item) => {
            detail.costDetail.push(item)
          }
        "
      ></consume-dialog>
    </v-detail-view>
  </v-container>
</template>
<script>
import consumeDialog from './private/consume-dialog.vue'
// addCny	补充的人民币		false	 number
// addUsd	补充的美元		false	 number
// applyDept	申请部门		false	 string
// applyPerson	申请人		false	 string
// applyTime	申请时间		false	 string(date-time)
// applyType	申请类型 0消耗备用金1补充备用金2更改船长信息		false	 integer(int32)
// attachmentIds	附件		false	 array string
// captainBank	船长开户行		false	 string
// captainBankCode	船长银行卡号		false	 string
// captainName	船长名		false	 string
// captainSapCode	船长sapcode		false	 string
// cnyRes	人民币余额		false	 number
// cnyTotal	人民币金额总计		false	 number
// costDetail	费用明细		false	 array
// SpareProject
// crewSupervisor	船员主管		false	 array string
// id	物理主键		false	 string
// joinPlace	交接地点		false	 string
// joinTime	交接时间		false	 string(date-time)
// post	职务		false	 string
// projectCode	项目编号		false	 string
// remark	备注		false	 string
// shipCode	船舶编码		true	 string
// usdRes	美元余额		false	 number
// usdTotal	美元金额总计		false	 number

// ccyCode	币种code		true	string
// date	日期		false	string
// payDetail	支付明细		false	string
// payMoney	付款金额		true	number
// place	地点		false	string
// subjectId	科目id		true	string
// subjectName	科目名		true	string
export default {
  components: { consumeDialog },
  name: 'spare-money-consume-detail',
  created() {
    this.backRouteName = 'spare-money-consume-list'
    this.subtitles = ['基本信息', '消耗详情', '当前船舶信息']
    this.tableFeilds = [
      { text: '补充的人民币', value: 'addCny' },
      { text: '补充的美元', value: 'addUsd' },
    ]
    // captainBank	船长开户行	string
    // captainBankCode	船长银行卡号	string
    // captainName	船长名	string
    // captainSapCode	船长sapcode	string
    // cnyBottom	人民币下限	number
    // cnyTop	人民币上限	number
    // id	物理主键	string
    // joinPlace	交接地点	string
    // joinTime	交接时间	string(date-time)
    // remainCny	剩余人民币	number
    // remainUsd	剩余美元	number
    // remark	备注	string
    // shipCode	船舶编码	string
    // shipName	船舶名	string
    // usdBottom	美元下限	number
    // usdTop	美元上限	number
    this.shipSpareFeilds = [
      { text: '人民币下限', value: 'cnyBottom' },
      { text: '人民币上限', value: 'cnyTop' },
      { text: '美元下限', value: 'usdBottom' },
      { text: '美元上限', value: 'usdTop' },
      { text: '剩余人民币', value: 'remainCny' },
      { text: '剩余美元', value: 'remainUsd' },
      { text: '当前船长名', value: 'captainName' },
      { text: '当前船长开户行', value: 'captainBank' },
      { text: '当前船长银行卡号', value: 'captainBankCode' },
      { text: '当前船长sapcode', value: 'captainSapCode' },
    ]

    this.headers = [
      { text: '币种', value: 'ccyCode' },
      { text: '日期', value: 'date' },
      { text: '地点', value: 'place' },
      { text: '科目', value: 'subjectName' },
      { text: '支付明细', value: 'payDetail' },
      { text: '付款金额', value: 'payMoney' },
    ]
  },
  data() {
    return {
      detail: {
        attachmentIds: [],
        commonAttachments: [],
        costDetail: [],
        shipCode: '',
        applyType: 0,
        applyDeptName: this.$local.data.get('userInfo').deptName,
      },
      shipSpareInfo: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        maxUsd: () => '未获取到当前船舶信息',
        maxCny: () => '未获取到当前船舶信息',
      },
      initHandler: false,
      select: false,
      dialog: false,
      initialData: {},
    }
  },

  watch: {
    'detail.shipCode'(val) {
      if (!val) return
      this.loadShipSpareInfo()
    },
    'detail.addUsd'(val) {
      if (this.isEdit) return
      this.detail.usdRes = this.shipSpareInfo.remainUsd + val * 1
    },
    'detail.cnyTotal'(val) {
      if (this.isEdit) return
      this.detail.cnyRes = this.shipSpareInfo.remainCny + val * 1
    },
    'detail.costDetail': {
      handler(val) {
        let cnyTotal = val
          .filter((item) => item.ccyCode === 'CNY')
          .reduce((a, b) => a + b.payMoney * 1, 0)
        this.detail.cnyTotal = cnyTotal
        let usdTotal = val
          .filter((item) => item.ccyCode === 'USD')
          .reduce((a, b) => a + b.payMoney * 1, 0)
        this.detail.usdTotal = usdTotal
        this.detail.cnyRes = this.shipSpareInfo.id
          ? this.shipSpareInfo.remainCny - cnyTotal * 1
          : this.detail.cnyRes
        this.detail.usdRes = this.shipSpareInfo.id
          ? this.shipSpareInfo.remainUsd - usdTotal * 1
          : this.detail.usdRes
      },
      deep: true,
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return this.detail.status !== 1 && this.detail.status !== 2
    },
    canAddCny() {
      return this.shipSpareInfo.remainCny > this.shipSpareInfo.cnyBottom
    },
    canAddUsd() {
      return this.shipSpareInfo.remainUsd > this.shipSpareInfo.usdBottom
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return
      if (this.detail.costDetail.length === 0) {
        this.$dialog.message.error('请添加费用明细')
        return
      }
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/spareApply/modifySpareApply',
        this.detail,
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/spareApply/process/start',
            { id: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/shipAffairs/spareApply/getById/${this.$route.params.id}`,
      )
      this.detail = {
        ...data,
      }
      this.detail.costDetail = data.costDetail.map((i, index) => ({
        ...i,
        id: index,
      }))
      this.$refs.form.resetValidation()
      this.initHandler = {
        id: data.applyPerson,
        nickName: data.applyPersonName,
      }
    },

    async loadShipSpareInfo() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/spareInfo/getByCode/${this.detail.shipCode}`,
      )
      if (!data) {
        this.shipSpareInfo = {}
        this.$dialog.message.error('当前船舶未维护备用金信息,无法进行操作')
        this.rules.maxUsd = () => '未获取到当前船舶信息'
        this.rules.maxCny = () => '未获取到当前船舶信息'
        return
      }
      this.shipSpareInfo = data
      this.rules.maxUsd = (v) =>
        v <= this.shipSpareInfo.usdTop - this.shipSpareInfo.remainUsd ||
        '美元补充金额不得超过船舶上限'
      this.rules.maxCny = (v) =>
        v <= this.shipSpareInfo.usdTop - this.shipSpareInfo.remainUsd ||
        '人民币补充金额不得超过船舶上限'
      this.$refs.form.resetValidation()
    },

    createConsumption() {
      this.initialData = {}
      this.dialog = true
    },
    editConsumption() {
      this.initialData = this.select
      this.dialog = true
    },
    delConsumption() {
      this.detail.costDetail = this.detail.costDetail.filter(
        (item) => item.id !== this.select.id,
      )
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
