<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['付款审批:编辑']"
      :title="`付款审批-${isEdit ? detail.payCode : '新增'}`"
      :tooltip="isEdit ? detail.payCode : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      :can-save="!canSave"
    >
      <template v-slot:custombtns>
        <!-- <v-btn
          v-if="!canSave"
          tile
          color="error"
          small
          class="mx-1"
          @click="submit"
          v-permission="['付款审批:提交至采购主管确认']"
        >
          <v-icon left>mdi-check</v-icon>
          提交至采购主管确认
        </v-btn>
        <v-btn
          v-if="canConfirm"
          tile
          color="error"
          small
          class="mx-1"
          @click="confirm"
          v-permission="['付款审批:采购主管确认']"
        >
          <v-icon left>mdi-check</v-icon>
          采购主管确认
        </v-btn> -->
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-select
                  outlined
                  dense
                  label="付款公司SAP代码"
                  v-model="paySapCode"
                  :readonly="isEdit"
                  :rules="[rules.required]"
                  :items="付款单位代码"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  type="number"
                  outlined
                  dense
                  label="票据数量"
                  v-model="detail.orderNum"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="付款日期"
                  v-model="detail.payDate"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="收款方"
                  v-model="detail.payee"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="收款方sap代码"
                  v-model="detail.payeeSapCode"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-if="!isEdit"
                  outlined
                  dense
                  label="开户银行"
                  v-model="detail.openBank"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  :items="banks"
                  item-text="bank"
                  return-object
                  @change="
                    () => {
                      detail.accountBank = detail.openBank.account
                      detail.openBank = detail.openBank.bank
                    }
                  "
                ></v-select>
                <v-text-field
                  v-else
                  outlined
                  dense
                  label="开户银行"
                  v-model="detail.openBank"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="银行账户"
                  v-model="detail.accountBank"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="原币金额"
                  v-model="detail.originMoney"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="原币货币"
                  v-model="detail.originCcyCode"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="oa账号"
                  v-model="detail.accountOa"
                  readonly
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  outlined
                  dense
                  label="付款用途"
                  v-model="detail.payPurpose"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-combobox
                  v-if="false"
                  outlined
                  dense
                  label="采购主管"
                  v-model="detail.managercgName"
                  disabled
                  multiple
                  readonly
                  hint="请联系采购管理部维护供应商采购主管"
                  :rules="[rules.required]"
                ></v-combobox>
                <v-autocomplete
                  label="采购负责人"
                  v-model="detail.managercgId"
                  :rules="[rules.required]"
                  disabled
                  dense
                  readonly
                  outlined
                  item-text="nickName"
                  item-value="id"
                  multiple
                  :items="cgList"
                ></v-autocomplete>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="开户银行(发票)"
                  v-model="detail.openBankOrder"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="银行账户(发票)"
                  v-model="detail.accountBankOrder"
                  readonly
                ></v-text-field>
              </v-col> -->
            </v-row>
          </v-form>
          <v-card-text>
            <v-attach-list
              readonly
              title="附件(系统自动生成)"
              :attachments="detail.attachmentRecords"
            ></v-attach-list>
          </v-card-text>
          <v-card-text>
            <v-attach-list
              title="附件"
              :attachments="detail.attachmentRecords2"
              @change="(ids) => (detail.attachmentIds2 = ids)"
            ></v-attach-list>
          </v-card-text>
        </v-container>
      </template>
      <template #费用凭证明细按钮>
        <v-btn
          v-if="!isEdit"
          :disabled="!paySapCode && !isEdit"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="dialog = true"
          v-permission="['费用凭证明细:选择费用项目']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择凭证
        </v-btn>
      </template>
      <template #费用凭证明细>
        <v-table-list :headers="headers" :items="voucher">
          <template v-slot:[`item.status`]="{ item }">
            {{ statues[item.status] }}
          </template>
          <template v-slot:[`item.businessModule`]="{ item }">
            {{ businessModuleMap[item.businessModule] }}
          </template>
        </v-table-list>
      </template>
      <!-- <template #批量费用明细>
        <v-table-list
          :headers="headersBatchCost"
          :items="batchCost"
        ></v-table-list>
      </template> -->
      <!-- <template #费用项目明细> -->
      <v-card-title v-if="canSave">
        发票行项目明细
        <v-spacer></v-spacer>
      </v-card-title>
      <v-table-list
        v-if="canSave"
        :headers="headersCostProject"
        :items="costProjects"
      >
        <template v-slot:[`item.businessModule`]="{ item }">
          {{
            [
              '单次预算',
              '备件订单',
              '物料订单',
              '滑油订单',
              '航修修理单',
              '坞修修理单',
              '坞修备件订单',
              '分期付款',
              '备用金',
              '自修奖',
              '船东账',
              '新造船',
              '坞修物料订单',
              '批量费用',
            ][item.businessModule]
          }}
        </template>
        <template v-slot:[`item.status`]="{ item }">
          {{ stateMap[item.status] }}
        </template>
        <template v-slot:[`item.repairFlag`]="{ item }">
          {{ item.repairFlag === 1 ? '是' : '否' }}
        </template>
      </v-table-list>
      <!-- </template> -->
    </v-detail-view>
    <cost-voucher-select
      v-model="dialog"
      :voucher.sync="voucher"
      :batchCost.sync="batchCost"
      :pay-sap-code="paySapCode"
    ></cost-voucher-select>
  </v-container>
</template>
<script>
import costVoucherSelect from './private/cost-voucher-select.vue'
import { stateMap, statues } from '../private/constant'
import routerControl from '@/mixin/routerControl'
// accountBank	银行账户	string
// accountOa	oa账号	string
// applyDate	申请时间	string(date-time)
// applyDept	申请部门	string
// applyPerson	申请人	string
// businessRange	业务区间	string
// oaProjectCode	oa项目编号	string
// openBank	开户银行	string
// orderNum	票据数量	integer(int32)
// originCcyId	原币货币	string
// originMoney	原币金额	number
// payCode	付款编号	string
// payCompany	付款公司	string
// payDate	付款日期	string(date-time)
// payPurpose	付款用途	string
// paySapCode	付款方sap代码	string
// payee	收款方	string
// payeeSapCode	收款方sap代码	string
// remark	备注	string
export default {
  components: { costVoucherSelect },
  name: 'cost-payment-detail',
  mixins: [routerControl],

  created() {
    this.backRouteName = 'cost-payment-list'
    // this.subtitles = ['基本信息', '费用凭证明细', '批量费用明细']
    this.subtitles = ['基本信息', '费用凭证明细']
    // this.付款单位代码 = ['3800', '8903', '3402', '3607', '3000']
    this.付款单位代码 = ['8903', '3000']
    this.headers = [
      { text: '船舶', value: 'shipName' },
      { text: '凭证号', value: 'orderCode' },
      { text: '申请人', value: 'applyPersonName' },
      { text: '凭证日期', value: 'orderDate' },
      { text: '付款公司', value: 'payCompany' },
      { text: '供应商', value: 'supplyName' },
      { text: '币种', value: 'ccyName' },
      { text: '付款总金额', value: 'moneyPay' },
      { text: '发票数', value: 'orderNum' },
      { text: '业务模块', value: 'businessModule' },
      { text: '状态', value: 'status' },
    ]
    this.headersBatchCost = [
      { text: '申请编号', value: 'applicationNo' },
      { text: '申请人', value: 'initPersonName' },
      { text: '付款日期', value: 'payDate' },
      { text: '付款公司', value: 'paymentCompany' },
      { text: '供应商', value: 'supplyName' },
      { text: '币种', value: 'ccyName' },
      { text: '付款总金额', value: 'total' },
      { text: '费用科目', value: 'subjectName' },
      { text: '状态', value: 'businessStatus' },
    ]
    this.headersCostProject = [
      { text: '船舶', value: 'shipName' },
      { text: '系列', value: 'seriesShip', sortable: false },
      { text: '费用科目名', value: 'subjectName', sortable: false },
      { text: '发票编号', value: 'invoiceCode' },
      { text: '订单号', value: 'orderCode' },
      { text: '供应商名', value: 'supplyName' },
      { text: '币种', value: 'ccyName' },
      { text: '原币金额', value: 'money', sortable: false },
      { text: '发生日期', value: 'happenDate', hideDefault: true },
      { text: '付款日期', value: 'payDate' },
      { text: '折算美金', value: 'usdMoney' },
      { text: '业务模块', value: 'businessModule' },
      { text: '实际申请人', value: 'applyPersonName', sortable: false },
      { text: '申请部门', value: 'parentDeptName', sortable: false },
      { text: '状态', value: 'status' },
      { text: '坞修', value: 'repairFlag', hideDefault: true, sortable: false },
      { text: '采购数量', value: 'count', hideDefault: true },
      { text: '备注', value: 'remark' },
    ]
    this.stateMap = stateMap
    this.statues = stateMap
    this.status = statues
    this.businessModuleMap = {
      0: '单次预算',
      1: '备件订单',
      2: '物料订单',
      3: '滑油订单',
      4: '航修修理单',
      5: '坞修修理单',
      6: '坞修备件订单',
      7: '分期付款',
      8: '备用金',
      9: '自修奖',
      10: '船东账',
      11: '新造船',
      12: '坞修物料订单',
      13: '批量预算',
    }
  },
  data() {
    return {
      detail: {
        orderNum: 0,
        originMoney: 0,
        originCcyCode: '',
        accountBank: '',
        accountOa: '',
        openBank: '',
      },
      dialog: false,
      voucher: [],
      batchCost: [],
      costProjects: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      banks: [],
      paySapCode: '',
      cgList: [],
    }
  },
  watch: {
    voucher(val) {
      if (val.length > 0 && !this.isEdit) {
        this.loadInfo(val, 1)
      }
    },
    batchCost(val) {
      if (val.length > 0 && !this.isEdit) {
        this.loadInfo(val, 2)
      }
    },
    paySapCode(newVal, oldVal) {
      if (oldVal && newVal !== oldVal) {
        this.voucher = []
        this.batchCost = []
        this.detail = {}
      }
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canSave() {
      return this.$route.params.id !== 'new' || this.detail.status == 34
    },
    canConfirm() {
      //       PAY_AUDIT_IN(30, "付款审批中"),
      // PAY_AUDIT_FAIL(31, "付款审批未通过"),
      // PAY_AUDIT_PASS(32, "付款审批通过"),
      // HAVE_PAY(33, "已付款"),
      // PAY_AUDIT_NO(34, "付款审批未提交"),
      // PAY_AUDIT_NO_JW(35, "付款审批采购主管确认中"),
      // PAY_AUDIT_YES_JW(36, "付款审批采购主管已确认");
      return (
        this.detail.status == 35 &&
        this.detail.managercgId.includes(this.$local.data.get('userInfo').id)
      )
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return false
      }
      const idList = this.voucher.map((item) => item.id)
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/costPay/modifyCostPay'
        : '/business/shipAffairs/costPay/createCostPay'
      const { data, errorRaw } = await this.postAsync(reqUrl, {
        ...this.detail,
        idList,
        paySapCode: this.paySapCode,
      })
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit() {
      if (!this.$refs.form.validate()) {
        return false
      }
      this.detail.status = 35
      const idList = this.voucher.map((item) => item.id)
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/costPay/modifyCostPay'
        : '/business/shipAffairs/costPay/createCostPay'
      const { errorRaw } = await this.postAsync(reqUrl, {
        ...this.detail,
        idList,
        paySapCode: this.paySapCode,
      })
      if (errorRaw) return false
      this.closeAndTo(this.backRouteName)
    },

    async confirm() {
      if (!this.$refs.form.validate()) {
        return false
      }
      this.detail.status = 36
      const idList = this.voucher.map((item) => item.id)
      const reqUrl = '/business/shipAffairs/costPay/modifyCostPayStatus'
      const { errorRaw } = await this.postAsync(reqUrl, {
        ...this.detail,
        idList,
        paySapCode: this.paySapCode,
      })
      if (errorRaw) return false
      this.closeAndTo(this.backRouteName)
    },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/shipAffairs/costPay/getById/${this.$route.params.id}`,
      )
      this.detail = data
      this.detail.managercgName = data.managercgName
      this.paySapCode = data.paySapCode
      this.voucher = data.costOrderOutputDTOS
      this.batchCost = data.batchCostApplyOutputDTOS
      this.costProjects = data.costProjectOutputDTOS
    },

    async loadInfo(val, num) {
      var idList = []
      if (num == 1) {
        idList = val.map((i) => i.id)
        if (this.batchCost.length > 0) {
          var ids = this.batchCost.map((i) => i.id)
          idList = idList.concat(ids)
        }
      } else if (num == 2) {
        idList = val.map((i) => i.id)
        if (this.voucher.length > 0) {
          var ids2 = this.voucher.map((i) => i.id)
          idList = idList.concat(ids2)
        }
      }

      console.log(idList)

      const { data } = await this.postAsync(
        '/business/shipAffairs/costPay/getCreateInfo',
        idList,
        // val.map((i) => i.id),
      )
      await this.loadBank(val[0])
      // delete data.accountBank
      // delete data.openBank
      Object.assign(this.detail, data)
    },
    // async loadInfoBatchCost(val) {
    // const { data } = await this.postAsync(
    //   '/business/shipAffairs/costPay/getCreateInfo',
    //   val.map((i) => i.id),
    // )
    // await this.loadBank(val[0])
    // delete data.accountBank
    // delete data.openBank
    // Object.assign(this.detail, data)
    // },

    async loadBank({ ccyId, supplyId }) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/Supplier/getBankMessageByShipCodeAndType',
        {
          purchaseCompany: this.paySapCode,
          supplierId: supplyId,
          currencyId: ccyId,
        },
      )
      if (data.length === 0) {
        this.$dialog.message.error('供应商未维护银行信息')
        return
      }
      data.forEach((item) => {
        item.bank = item.bank + ':' + item.account
      })
      this.banks = data
      // console.log(this.banks)
      if (data.length === 1) {
        this.detail.accountBank = data[0].account
        this.detail.openBank = data[0].bank
      }
      if (data.length > 1) {
        this.$dialog.message.info('供应商维护了多个银行信息,请选择')
      }
    },
    async getCgList() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getCgList`,
      )
      console.log(data)
      this.cgList = data
    },
  },

  mounted() {
    this.getCgList()
    this.loadDetail()
  },
}
</script>

<style></style>
