<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        费用凭证选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable-pay
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          outlined
        >
          <template #btns>
            <v-col cols="12" md="2">
              <v-text-field
                label="对账标识（修改用）"
                small
                clearable
                v-model="checkFlag"
              ></v-text-field>
            </v-col>
            <v-btn
              :disabled="!selected"
              :loading="loading"
              outlined
              tile
              color="success"
              class="mx-1"
              @click="updateCheckFlag"
            >
              <v-icon left>mdi-file-excel</v-icon>
              更新对账标识
            </v-btn>
            <v-btn
              :disabled="!selected"
              :loading="loading"
              outlined
              tile
              color="error"
              class="mx-1"
              @click="clearCheckFlag"
            >
              <v-icon left>mdi-file-excel</v-icon>
              清空对账标识
            </v-btn>
            <v-btn
              :loading="loading"
              outlined
              tile
              color="info"
              class="mx-1"
              @click="downloadExcel"
            >
              <v-icon left>mdi-file-excel</v-icon>
              导出EXCEL
            </v-btn>
          </template>
          <template #searchflieds>
            <v-col cols="12" md="3">
              <vs-date-picker
                label="凭证/凭证付款日期"
                v-model="toTime"
                dense
                outlined
                :max-date="untilDate"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="3">
              <v-dict-select
                clearable
                v-model="creditDate"
                label="赊销期"
                dense
                outlined
                dict-type="credit_date"
              ></v-dict-select>
            </v-col>
            <v-col cols="12" md="3">
              <!-- :disabled="!creditDate" -->
              <v-dialog-select
                label="供应商"
                item-text="name"
                item-value="id"
                v-model="searchObj.supplyId"
                :headers="supHeaders"
                @select="changes()"
                req-url="/business/shipAffairs/Supplier/creditDateList"
                fuzzy-label="供应商"
                :search-remain="searchCreditDate"
                clearable
                @clear="
                  () => {
                    searchObj.supplyId = ''
                    changes()
                  }
                "
              ></v-dialog-select>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                label="对账标识（查询用）"
                outlined
                dense
                clearable
                v-model="searchObj.checkFlag"
              ></v-text-field>
            </v-col>
            <!-- <v-col cols="12" md="3">
              <v-select
                outlined
                dense
                v-model="searchObj.company"
                label="公司"
                :items="deptInfo"
                clearable
              ></v-select>
            </v-col>
            <v-col cols="12" md="3">
              <v-select
                outlined
                dense
                v-model="searchObj.depts"
                :items="groups"
                @change="changes()"
                label="部门"
                multiple
                clearable
              ></v-select>
            </v-col> -->
          </template>
          <!-- <template #btns></template> -->
          <template v-slot:[`item.status`]="{ item }">
            {{ statues[item.status] }}
          </template>
          <template v-slot:[`item.businessModule`]="{ item }">
            {{ businessModuleMap[item.businessModule] }}
          </template>
        </v-table-searchable-pay>
      </v-card-text>
      <!-- <v-card-text>
        <v-table-searchable2
          ref="table2"
          table-name="批量费用选择"
          v-model="selected2"
          :headers="headersBatchCost"
          :req-url="reqUrlBatchCost"
          :search-remain="searchObjBatchCost"
          :fix-header="false"
          :single-select="false"
          outlined
        >
          <template #searchflieds></template>
          <template #btns></template>
        </v-table-searchable2>
      </v-card-text> -->
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import vTableSearchablePay from '@/components/v-table-searchablePay.vue'
const today = new Date(Date.now())
const untilDate = new Date(new Date().setDate(today.getDate() + 20))
  .toISOString()
  .substr(0, 10)
import { stateMap, statues } from '../../private/constant'
export default {
  components: { vTableSearchablePay },
  name: 'cost-voucher-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/shipAffairs/costOrder/page'
    this.reqUrlBatchCost = '/business/shipAffairs/batchCostApply/page'
    this.headers = [
      { text: '船舶', value: 'shipName', sortable: false },
      { text: '凭证号', value: 'orderCode', sortable: false },
      { text: '发票号', value: 'invoiceCode', sortable: false },
      { text: '申请人', value: 'applyPersonName', sortable: false },
      { text: '凭证日期', value: 'orderDate', sortable: false },
      { text: '付款公司', value: 'payCompany', sortable: false },
      { text: '供应商', value: 'supplyName', sortable: false },
      { text: '币种', value: 'ccyName', sortable: false },
      { text: '付款总金额', value: 'moneyPay', sortable: false },
      { text: '发票数', value: 'orderNum', sortable: false },
      { text: '业务模块', value: 'businessModule', sortable: false },
      { text: '状态', value: 'status', sortable: false },
      { text: '开户银行', value: 'openBank', sortable: false },
      { text: '银行账户', value: 'accountBank', sortable: false },
      { text: '对账标识', value: 'checkFlag', sortable: false },
    ]
    this.headersBatchCost = [
      { text: '申请编号', value: 'applicationNo' },
      { text: '申请人', value: 'initPersonName' },
      { text: '付款日期', value: 'payDate' },
      { text: '付款公司', value: 'paymentCompany' },
      { text: '供应商', value: 'supplyName' },
      { text: '币种', value: 'ccyName' },
      { text: '付款总金额', value: 'total' },
      { text: '费用科目', value: 'subjectName' },
      { text: '状态', value: 'businessStatus' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.statues = stateMap
    this.status = statues
    this.untilDate = untilDate
    this.businessModuleMap = {
      0: '单次预算',
      1: '备件订单',
      2: '物料订单',
      3: '滑油订单',
      4: '航修修理单',
      5: '坞修修理单',
      6: '坞修备件订单',
      7: '分期付款',
      8: '备用金',
      9: '自修奖',
      10: '船东账',
      11: '新造船',
      12: '坞修物料订单',
      13: '批量预算',
    }
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    paySapCode: {
      type: String,
      default: '',
    },
    voucher: Array,
    batchCost: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchObj: {
        status: 26,
        toTime: untilDate,
        company: '',
        auditFlag: false,
        depts: [],
        supplyId: '',
        creditDate: '',
        manager: this.$local.data.get('userInfo').id,
      },
      selected: [],
      toTime: untilDate,
      deptInfo: [],
      searchObjBatchCost: {
        businessStatus: 'SAP执行成功',
        toTime: untilDate,
        company: '',
        auditFlag: false,
        depts: [],
        supplyId: '',
      },
      selected2: [],
      creditDate: null,
      searchCreditDate: {
        creditDate: null,
        manager: this.$local.data.get('userInfo').id,
        paymentCompany: this.paySapCode,
      },
      loading: false,
      checkFlag: '',
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    creditDate(val) {
      this.searchCreditDate.creditDate = val
      this.searchObj.creditDate = val
      // if(val){

      // }
    },
    selected: {
      handler(val) {
        if (val.length === 1) {
          // this.searchObj = {
          //   ccyId: val[0].ccyId,
          //   supplyId: val[0].supplyId,
          //   payCompany: val[0].payCompany,
          //   status: 26,
          //   toTime: this.toTime,
          //   company: this.searchObj.company,
          //   depts: this.searchObj.depts,
          //   auditFlag: false,
          //   shipCode: '',
          // }
          this.searchObj = {
            ...val[0],
            status: 26,
            toTime: this.toTime,
            company: this.searchObj.company,
            depts: this.searchObj.depts,
            auditFlag: false,
            shipCode: '',
            creditDate: this.creditDate,
            manager: this.$local.data.get('userInfo').id,
          }
          delete this.searchObj.applyPerson
          delete this.searchObj.applyPersonName
          delete this.searchObj.applyDept
          delete this.searchObj.applyDeptName
          delete this.searchObj.businessModule
          delete this.searchObj.orderCode
          delete this.searchObj.invoiceCode
          delete this.searchObj.checkFlag
          this.searchObjBatchCost = {
            businessStatus: 'SAP执行成功',
            toTime: this.searchObj.toTime,
            company: this.searchObj.company,
            depts: this.searchObj.depts,
            supplyId: this.searchObj.supplyId,
            paymentCompany: this.paySapCode,
            initApply: val[0].applyPerson,
            auditFlag: false,
            ccyId: val[0].ccyId,
          }
        } else if (val.length === 0) {
          this.searchObj = {
            payCompany: this.paySapCode,
            status: 26,
            toTime: this.toTime,
            company: this.searchObj.company,
            depts: this.searchObj.depts,
            auditFlag: false,
            shipCode: '',
            creditDate: this.creditDate,
            manager: this.$local.data.get('userInfo').id,
          }
          this.searchObjBatchCost = {
            businessStatus: 'SAP执行成功',
            toTime: this.searchObjBatchCost.toTime,
            company: this.searchObjBatchCost.company,
            depts: this.searchObjBatchCost.depts,
            paymentCompany: this.paySapCode,
            auditFlag: false,
          }
        }
      },
    },
    // selected2: {
    //   handler(val) {
    //     if (val.length === 1) {
    //       this.searchObjBatchCost = {
    //         // ...val[0],
    //         supplyId: val[0].supplyId,
    //         paymentCompany: val[0].paymentCompany,
    //         ccyId: val[0].ccyId,
    //         businessStatus: 'SAP执行成功',
    //         toTime: this.searchObjBatchCost.toTime,
    //         company: this.searchObjBatchCost.company,
    //         depts: this.searchObjBatchCost.depts,
    //         auditFlag: false,
    //       }
    //       this.searchObj = {
    //         status: 26,
    //         toTime: this.toTime,
    //         company: this.searchObj.company,
    //         depts: this.searchObj.depts,
    //         auditFlag: this.searchObj.auditFlag,
    //         shipCode: '',
    //         applyPerson: val[0].initApply,
    //         ccyId: val[0].ccyId,
    //         payCompany: this.paySapCode,
    //       }
    //     } else if (val.length === 0) {
    //       this.searchObjBatchCost = {
    //         businessStatus: 'SAP执行成功',
    //         toTime: this.searchObjBatchCost.toTime,
    //         company: this.searchObjBatchCost.company,
    //         depts: this.searchObjBatchCost.depts,
    //         paymentCompany: this.paySapCode,
    //         auditFlag: false,
    //       }
    //       this.searchObj = {
    //         payCompany: this.paySapCode,
    //         status: 26,
    //         toTime: this.toTime,
    //         company: this.searchObj.company,
    //         depts: this.searchObj.depts,
    //         auditFlag: false,
    //         shipCode: '',
    //       }
    //     }
    //   },
    // },
    paySapCode(val) {
      this.searchObj.payCompany = val
      this.searchCreditDate.paymentCompany = val
      this.searchObjBatchCost.paymentCompany = val
    },
    voucher(val) {
      this.selected = val
    },
    batchCost(val) {
      this.selected2 = val
    },
    toTime(val) {
      this.searchObj.toTime = val
      this.searchObjBatchCost.toTime = val
    },
    'searchObj.company'() {
      this.searchObj.depts = []
      this.searchObjBatchCost.company = this.searchObj.company
    },
  },
  computed: {
    groups() {
      const depts = this.deptInfo.find(
        (item) => item.value === this.searchObj.company,
      )
      let groups = []
      // 递归获取groups，当当前节点isLeader为false时，以id和name为value和text返回对象，否则获取其children继续递归
      const getGroups = (data) => {
        data.forEach((item) => {
          if (!item.isLeader) {
            groups.push({
              value: item.id,
              text: item.name,
            })
          } else {
            getGroups(item.children)
          }
        })
      }
      if (depts) {
        getGroups(depts?.children || [])
      }

      return groups
    },
  },
  methods: {
    changes() {
      this.searchObjBatchCost.depts = this.searchObj.depts
      this.searchObjBatchCost.supplyId = this.searchObj.supplyId
    },
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const voucher = [...this.selected]
      this.$emit('update:voucher', voucher)
      const batchCost = [...this.selected2]
      this.$emit('update:batchCost', batchCost)
      this.$emit('change', false)
    },

    async loadDeptInfo() {
      const { data } = await this.getAsync('/system/dept/getDeptTreeList')
      // 遍历部门树结构，找到deptType为0的部门
      const deptInfo = []
      const findDept = (data) => {
        data.forEach((item) => {
          if (item.deptType === '0') {
            deptInfo.push({
              value: item.id,
              text: item.name,
              deptType: item.deptType,
              children: item.children,
            })
          }
          if (item.children) {
            findDept(item.children)
          }
        })
      }
      findDept(data)
      this.deptInfo = deptInfo
      // this.deptInfo = data[0].children.map((i) => ({
      //   value: i.id,
      //   text: i.name,
      //   deptType: i.deptType,
      // }))
    },
    async downloadExcel() {
      this.loading = true
      let params = { ...this.searchObj }
      params = {
        ...params,
      }
      await this.getBlobDownload(
        '/business/shipAffairs/costOrder/excelExport',
        params,
        // 时间戳后四位
        `费用凭证审批-${new Date().getTime().toString().slice(-4)}.xlsx`,
      )
      this.loading = false
    },
    async updateCheckFlag() {
      if (this.selected.length == 0) {
        this.$dialog.message.error(`请勾选记录`)
        return
      }
      this.selected.forEach((item) => {
        item.checkFlag = this.checkFlag
      })
      const url = `/business/shipAffairs/costOrder/updateCheckFlag`
      const { errorRaw } = await this.postAsync(url, this.selected)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
    },
    async clearCheckFlag() {
      if (this.selected.length == 0) {
        this.$dialog.message.error(`请勾选记录`)
        return
      }
      this.selected.forEach((item) => {
        item.checkFlag = ''
      })
      const url = `/business/shipAffairs/costOrder/updateCheckFlag`
      const { errorRaw } = await this.postAsync(url, this.selected)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
  mounted() {
    this.loadDeptInfo()
    // console.log(this.searchObj)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
