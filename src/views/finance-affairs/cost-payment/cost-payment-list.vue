<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      :push-params="pushParams"
      :search-date="searchDate"
      fuzzy-label="模糊搜索"
      :showExportButton="true"
      :specialHeaders="specialHeaders"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.year"
            label="年度"
            outlined
            dense
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.status"
            :items="status"
            label="状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-handler
            clearable
            label="申请人"
            v-model="searchObj.applyPersonId"
            :use-current="false"
            :initUser="isApplicantId"
          ></v-handler>
        </v-col>
        <v-col cols="12" md="2">
          <v-handler
            clearable
            label="采购负责人"
            v-model="searchObj.cgId"
            :use-current="false"
            :initUser="isCgId"
          ></v-handler>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!excelUrl"
          outlined
          tile
          color="success"
          class="mx-1"
          :href="excelUrl"
          v-permission="['付款审批:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'cost-payment-detail', params: { id: 'new' } }"
          v-permission="['付款审批:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status != 30"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="handPass"
          v-permission="['付款审批:手动通过']"
        >
          <v-icon left>mdi-check</v-icon>
          手动通过
        </v-btn>
        <v-btn
          :disabled="selected.status != 30"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="handCancel"
          v-permission="['付款审批:手动拒绝']"
        >
          <v-icon left>mdi-cancel</v-icon>
          手动拒绝
        </v-btn>
        <v-btn
          :disabled="!canHandPay"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="handPay"
          v-permission="['付款审批:已付款']"
        >
          <v-icon left>mdi-cash-sync</v-icon>
          已付款
        </v-btn>
        <!-- :disabled="selected.status != 34 || selected.applyPersonId != userId" -->
        <v-btn
          :disabled="selected.status != 34 || selected.applyPersonId != userId"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="sendOA"
          v-permission="['付款审批:发送到OA']"
        >
          <v-icon left>mdi-send</v-icon>
          发送到OA
        </v-btn>
        <!-- :disabled="selected.status != 34 || selected.applyPersonId != userId" -->
        <v-btn
          :disabled="
            (selected.status != 34 &&
              selected.status != 35 &&
              selected.status != 36 &&
              selected.status != 31) ||
            selected.applyPersonId != userId
          "
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['付款审批:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ statues[item.status] }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// accountBank	银行账户	string
// accountOa	oa账号	string
// applyDate	申请时间	string
// applyDept	申请部门	string
// applyPerson	申请人	string
// businessRange	业务区间	string
// id	物理主键	string
// oaProjectCode	oa项目编号	string
// openBank	开户银行	string
// orderNum	票据数量	integer
// originCcyId	原币货币	string
// originMoney	原币金额	number
// payCode	付款编号	string
// payCompany	付款公司	string
// payDate	付款日期	string
// payPurpose	付款用途	string
// paySapCode	付款方sap代码	string
// payee	收款方	string
// payeeSapCode	收款方sap代码	string
// remark	备注	string
// status	30:付款审批中 31:付款审批未通过 32:付款审批通过 33:已付款 34:付款审批未提交	integer
import { stateMap, statues } from '../private/constant'
export default {
  name: 'cost-payment-list',
  created() {
    this.tableName = '付款审批'
    this.reqUrl = '/business/shipAffairs/costPay/page'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '付款编号', value: 'payCode', sortable: false },
      { text: '付款公司', value: 'payCompany', sortable: false },
      { text: '付款日期', value: 'payDate', sortable: false },
      { text: '付款用途', value: 'payPurpose', sortable: false },
      { text: '付款方sap代码', value: 'paySapCode', sortable: false },
      { text: '收款方', value: 'payee', sortable: false },
      { text: '收款方sap代码', value: 'payeeSapCode', sortable: false },
      { text: '票据数量', value: 'orderNum', sortable: false },
      { text: '原币货币', value: 'originCcyCode' },
      { text: '原币金额', value: 'originMoney' },
      { text: '开户银行', value: 'openBank' },
      { text: '银行账户', value: 'accountBank' },
      { text: 'oa账号', value: 'accountOa' },
      { text: '申请时间', value: 'applyDate' },
      { text: '申请部门', value: 'applyDept' },
      { text: '申请人', value: 'applyPerson' },
      { text: '采购负责人', value: 'managercgName' },
      { text: '业务区间', value: 'businessRange' },
      { text: 'oa项目编号', value: 'oaProjectCode' },
      { text: '备注', value: 'remark' },
      { text: '状态', value: 'status' },
      { text: 'OA审批完成时间', value: 'oaCompleteTime' },
      { text: '付款完成时间', value: 'sapPayDate' },
    ]
    this.statues = stateMap
    this.status = statues
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '申请日期',
      value: 'applyDate',
      interval: true,
    }
    this.pushParams = { name: 'cost-payment-detail' }
    this.specialHeaders = [
      {
        text: 'status',
        value: [
          { text: 30, value: '付款审批审批中' },
          { text: 31, value: '付款审批退回' },
          { text: 32, value: '付款审批已完成' },
          { text: 33, value: '已付款' },
          { text: 34, value: '付款审批未提交' },
        ],
      },
    ]
  },

  computed: {
    canDel() {
      return this.selected && this.selected.status == 34
    },
    excelUrl() {
      return this.selected?.attachmentRecords?.length
        ? `/api/system/file/download?fileName=${encodeURIComponent(
            this.selected.attachmentRecords[0].name,
          )}&filePath=${this.selected.attachmentRecords[0].filePath}`
        : ''
    },
    canHandPay() {
      return (
        this.selected &&
        this.selected.status == 32 &&
        (this.selected.payCompany == 3402 || this.selected.payCompany == 8903)
      )
    },
  },

  data() {
    return {
      selected: false,
      searchObj: {
        year: '',
        status: '',
      },
      userId: this.$local.data.get('userInfo').id,
      isApplicantId: false,
      isCgId: false,
    }
  },
  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costPay/deleteCostPay',
        [this.selected.id],
      )
      if (!errorRaw) this.$dialog.message.success('删除成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },

    async handPass() {
      if (!(await this.$dialog.msgbox.confirm('确定已审批?'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costPay/handSuccess/${this.selected.id}`,
      )
      if (!errorRaw) this.$dialog.message.success('操作成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },

    async handCancel() {
      if (!(await this.$dialog.msgbox.confirm('确定审批驳回？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costPay/handFail/${this.selected.id}`,
      )
      if (!errorRaw) this.$dialog.message.success('操作成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },

    async handPay() {
      if (!(await this.$dialog.msgbox.confirm('确定已付款'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costPay/handPay/${this.selected.id}`,
        [this.selected.id],
      )
      if (!errorRaw) this.$dialog.message.success('操作成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async sendOA() {
      if (!(await this.$dialog.msgbox.confirm('确定发送OA?'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/costPay/sendOA/${this.selected.id}`,
        [this.selected.id],
      )
      if (!errorRaw) this.$dialog.message.success('操作成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {
    console.log(this.$route.query.status)
    console.log(this.$route.query.managerCg)
    console.log(this.$route.query.manager)
    if (this.$route.query.status != undefined) {
      this.searchObj.status = this.$route.query.status
      if (this.searchObj.status == 35) {
        this.searchObj.cgId = this.$route.query.managerCg
        this.isCgId = this.searchObj.cgId
          ? {
              id: this.searchObj.cgId,
              nickName: this.$local.data.get('userInfo').nickName,
            }
          : false
      }

      if (this.searchObj.status == 36) {
        this.searchObj.applyPersonId = this.$route.query.manager
        this.isApplicantId = this.searchObj.applyPersonId
          ? {
              id: this.searchObj.applyPersonId,
              nickName: this.$local.data.get('userInfo').nickName,
            }
          : false
      }
      console.log(this.isApplicantId)
    }
  },
}
</script>

<style></style>
