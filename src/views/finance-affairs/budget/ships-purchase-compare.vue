<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        多船采购单价对比
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="loadStatistic"
          v-permission="['多船采购单价对比:搜索']"
        >
          <v-icon left>mdi-magnify-expand</v-icon>
          搜索
        </v-btn>
        <v-btn
          :href="excelUrl"
          outlined
          tile
          color="info"
          class="mx-1"
          v-permission="['多船采购单价对比:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-form ref="form">
          <v-row>
            <!-- <v-col cols="12" sm="6" md="2">
              <v-text-field
                outlined
                dense
                label="年份"
                clearable
                v-model="searchObj.year"
                :rules="[rules.required]"
              ></v-text-field>
            </v-col> -->
            <v-col cols="12" sm="6" md="4">
              <v-menu
                v-model="datesMenu"
                :close-on-content-click="false"
                :nudge-right="40"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    ref="dates"
                    :value="dateRangeText"
                    label="时间范围"
                    append-icon="mdi-calendar"
                    outlined
                    dense
                    readonly
                    clearable
                    @click:clear="dates = []"
                    v-bind="attrs"
                    v-on="on"
                    :rules="[rules.required]"
                  ></v-text-field>
                </template>
                <vc-date-picker
                  v-model="dates"
                  mode="date"
                  is-range
                ></vc-date-picker>
              </v-menu>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-select
                v-model="searchObj.itemType"
                label="物品类型"
                outlined
                dense
                :items="itemTypes"
                :rules="[rules.required]"
              ></v-select>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                :rules="[rules.required]"
                v-model="searchObj.itemCode"
                label="物品编码/id"
                outlined
                dense
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-text-field
                v-model="searchObj.itemName"
                label="物品名称"
                outlined
                dense
              ></v-text-field>
            </v-col>
            <v-col md="3" cols="12">
              <v-dialog-select
                req-url="/business/shipAffairs/costSubject/page"
                label="费用科目"
                v-model="searchObj.costSubId"
                :search-dicts="searchDicts"
                item-text="subjectName"
                item-value="id"
                :headers="subHeaders"
                dense
              ></v-dialog-select>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-divider></v-divider>
      <vue-pdf-embed v-if="pdfFile" :source="pdfFile" />
    </v-card>
  </v-container>
</template>
<script>
// ccyCode		string
// costSubName	费用科目名称	string
// deliveryDate	交付日期	string(date-time)
// discount	折扣	number
// itemCode	备件/物料编号	string
// itemId	物件id	string
// itemName	物资名称	string
// orderId	采购订单id	string
// orderNo	采购订单No	string
// price	单价	number
// purchaseNum	订单购买数量	number
// supplierName	供应商名称	string
// toUsd	折算美金	number
import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
export default {
  name: 'ships-purchase-compare',
  components: { VuePdfEmbed },
  created() {
    this.itemTypes = [
      { text: '备件', value: '01' },
      { text: '物料', value: '02' },
      { text: '滑油', value: '03' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
    excelUrl() {
      return `/api/business/shipAffairs/purchaseStatistic/exportComparisonDetailInExcel?itemCode=${this.searchObj.itemCode}&year=${this.searchObj.year}&itemName=${this.searchObj.itemName}&itemType=${this.searchObj.itemType}`
    },
  },

  data() {
    return {
      selected: false,
      searchObj: {
        // year: new Date().getFullYear(),
        shipCode: '',
        itemType: '',
        itemCode: '',
        itemName: '',
        fromTime: '',
        toTime: '',
      },
      dates: {
        start: '',
        end: '',
      },
      datesMenu: false,
      list: [],
      pdfFile: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },

  activated() {
    if (this.$route.query.itemType) {
      // this.searchObj.year = this.$route.query.year
      this.searchObj.itemType = this.$route.query.itemType
      this.searchObj.itemCode = this.$route.query.itemCode
      this.searchObj.itemName = this.$route.query.itemName
      this.searchObj.fromTime = this.$route.query.fromTime
      this.searchObj.toTime = this.$route.query.toTime
      this.dates.start = new Date(this.searchObj.fromTime)
      this.dates.end = new Date(this.searchObj.toTime)
      this.loadStatistic()
    }
  },

  methods: {
    loadStatistic() {
      this.pdfFile = `/api/business/shipAffairs/purchaseStatistic/getComparisonDetail?itemCode=${this.searchObj.itemCode}&itemName=${this.searchObj.itemName}&itemType=${this.searchObj.itemType}&fromTime=${this.searchObj.fromTime}&toTime=${this.searchObj.toTime}`
    },
    async getExcel() {},
    loadingFailed(error) {
      //   this.pdfFile = null
      //   this.$dialog.message.error('pdf加载失败!')
      console.log(error)
    },
  },

  mounted() {},
}
</script>

<style></style>
