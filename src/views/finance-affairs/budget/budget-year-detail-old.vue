<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['年度费用预算:编辑']"
      :title="`年度费用预算-${isEdit ? detail.budgetYear : '新增'}`"
      :tooltip="isEdit ? detail.budgetYear : '新增'"
      :backRouteName="backRouteName"
      @save="save"
      @submit="submit"
      :can-submit="canSubmit"
      :can-save="detail.businessStatus == '草稿' || !isEdit"
    >
      <template v-slot:custombtns>
        <v-btn
          tile
          color="error"
          small
          class="mx-1"
          v-if="
            detail.status == '1' || detail.status == '3' || detail.status == '4'
          "
          :to="{
            name: 'budget-year-detail-adjust',
            params: { id: detail.id },
          }"
          v-permission="['年度费用预算:调整年度预算']"
        >
          <v-icon left>mdi-application-edit</v-icon>
          调整年度预算
        </v-btn>
        <v-btn
          v-if="detail.businessStatus == '草稿' || !isEdit"
          tile
          color="error"
          small
          class="mx-1"
          @click="dowExcel"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          导出EXCEL模板
        </v-btn>
        <v-btn
          v-if="detail.businessStatus == '待财务主管确认'"
          tile
          color="error"
          small
          class="mx-1"
          @click="finSupConfirm"
          v-permission="['年度费用预算:财务主管确认']"
        >
          <v-icon left>mdi-check</v-icon>
          财务主管确认
        </v-btn>
        <v-btn
          v-if="detail.businessStatus == '待财务主管确认'"
          tile
          color="error"
          small
          class="mx-1"
          @click="finSupReturn"
          v-permission="['年度费用预算:退回']"
        >
          <v-icon left>mdi-check</v-icon>
          退回
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  v-model="detail.budgetYear"
                  label="年度"
                  outlined
                  dense
                  :readonly="isEdit"
                  :rules="[rules.required, rules.yyyy]"
                ></v-text-field> -->
                <v-autocomplete
                  label="年度"
                  outlined
                  dense
                  :rules="[rules.required]"
                  required
                  :readonly="isEdit"
                  v-model="detail.budgetYear"
                  :items="yearList"
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="allBudget"
                  label="总费用(USD)"
                  outlined
                  readonly
                  dense
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.applyDate"
                  label="填报日期"
                  use-today
                  outlined
                  disabled
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :value="
                    detail.applyUserName || $local.data.get('userInfo').nickName
                  "
                  label="填报用户"
                  outlined
                  disabled
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="9">
                <v-text-field
                  v-model="detail.remark"
                  label="备注"
                  outlined
                  dense
                  :readonly="isEdit"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-file-input
                  v-if="detail.businessStatus == '草稿' || !isEdit"
                  outlined
                  dense
                  accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
                  label="导入EXCEL"
                  v-model="file"
                ></v-file-input>
              </v-col>
            </v-row>
            <v-form ref="tform">
              <v-divider></v-divider>
              <v-data-table
                v-if="!isAdjust"
                hide-default-footer
                disable-pagination
                dense
                :headers="isEdit ? headers : headers2"
                class="use-divider"
                :show-expand="true"
                :items="list"
              >
                <template v-slot:[`item.budgetMoney`]="{ item }">
                  <v-text-field
                    :readonly="isEdit"
                    v-model="item.budgetMoney"
                    single-line
                    dense
                    :rules="[rules.required, rules.number]"
                    @blur="
                      item.budgetMoney = Number(item.budgetMoney).toFixed(2)
                    "
                    outlined
                  ></v-text-field>
                </template>
                <template v-slot:[`item.originalMoneyChange`]="{ item }">
                  {{ (item.budgetMoney - item.originalMoney).toLocaleString() }}
                </template>
                <template v-slot:[`item.applyMoney`]="{ item }">
                  {{ item.applyMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.usedMoney`]="{ item }">
                  {{ item.usedMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.notUsedMoney`]="{ item }">
                  {{ item.notUsedMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.payFrequency`]="{ item }">
                  <div class="my-n3">
                    <v-select
                      v-model="item.payFrequency"
                      :items="[
                        { text: '年度', value: '1' },
                        { text: '季度', value: '2' },
                        { text: '月度', value: '3' },
                        { text: '不定期', value: '4' },
                      ]"
                      dense
                      :rules="[rules.required]"
                      readonly
                      required
                      outlined
                    ></v-select>
                  </div>
                </template>
                <template v-slot:[`item.remark`]="{ item }">
                  <v-text-field
                    :readonly="isEdit"
                    v-model="item.remark"
                    single-line
                    dense
                    outlined
                  ></v-text-field>
                </template>
                <template v-slot:expanded-item="{ headers, item }">
                  <td :colspan="headers.length">
                    <budgetCostsubjectList
                      :item-id="isEdit ? item.subjectId : item.id"
                      :ship-code="detail.shipCode"
                      :year="detail.budgetYear"
                    ></budgetCostsubjectList>
                  </td>
                </template>
              </v-data-table>
              <!-- 预算调整流程明细 -->
              <v-data-table
                v-if="isAdjust"
                hide-default-footer
                disable-pagination
                dense
                :headers="headersAdjust"
                class="use-divider"
                show-expand
                :items="list"
              >
                <template v-slot:[`item.budgetMoney`]="{ item }">
                  {{ item.budgetMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.moneyAfter`]="{ item }">
                  <span
                    v-if="item.moneyAfter > item.budgetMoney"
                    style="color: red"
                  >
                    {{ item.moneyAfter.toLocaleString() }}
                  </span>
                  <span v-if="item.moneyAfter <= item.budgetMoney">
                    {{ item.moneyAfter.toLocaleString() }}
                  </span>
                </template>
                <template v-slot:[`item.usedMoney`]="{ item }">
                  {{ item.usedMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.notUsedMoney`]="{ item }">
                  {{ item.notUsedMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.payFrequency`]="{ item }">
                  <div class="my-n3">
                    <v-select
                      v-model="item.payFrequency"
                      :items="[
                        { text: '年度', value: '1' },
                        { text: '季度', value: '2' },
                        { text: '月度', value: '3' },
                        { text: '不定期', value: '4' },
                      ]"
                      dense
                      readonly
                      required
                      outlined
                    ></v-select>
                  </div>
                </template>
                <!-- <template v-slot:[`item.remark`]="{ item }">
                  <v-text-field
                    v-model="item.remark"
                    single-line
                    asdasd
                    dense
                    outlined
                  ></v-text-field>
                </template> -->
                <template v-slot:[`item.remark`]="{ item }">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <span v-on="on">
                        {{ item.remark.substring(0, 9) }}...
                      </span>
                    </template>
                    <span>{{ item.remark }}</span>
                  </v-tooltip>
                </template>
                <template v-slot:[`item.notUsedMoneyAfter`]="{ item }">
                  <span
                    v-if="item.moneyAfter > item.budgetMoney"
                    style="color: red"
                  >
                    {{ (item.moneyAfter - item.usedMoney).toLocaleString() }}
                  </span>
                  <span v-if="item.moneyAfter <= item.budgetMoney">
                    {{ (item.moneyAfter - item.usedMoney).toLocaleString() }}
                  </span>
                </template>
                <template v-slot:[`item.originalMoneyChange`]="{ item }">
                  {{ (item.budgetMoney - item.originalMoney).toLocaleString() }}
                </template>
                <template v-slot:expanded-item="{ headers, item }">
                  <td :colspan="headers.length">
                    <budgetCostsubjectList
                      :item-id="isEdit ? item.subjectId : item.id"
                      :ship-code="detail.shipCode"
                      :year="detail.budgetYear"
                    ></budgetCostsubjectList>
                  </td>
                </template>
              </v-data-table>
              <v-divider></v-divider>
            </v-form>
          </v-container>
        </v-form>
      </v-card-text>
    </v-detail-view>
  </v-container>
</template>
<script>
import budgetCostsubjectList from './budget-costsubject-list.vue'
import routerControl from '@/mixin/routerControl'
// budgetMoney	预算金额		true
// number
// remark	备注		false
// string
// sapCode	预算编号(费用科目SAP代码)		false
// string
// subjectId	费用科目id		true
// string
// subjectName	科目名称		false
// string
// subjectType	科目类型		false
// string
export default {
  components: { budgetCostsubjectList },
  name: 'budget-year-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'budget-year-list'
    this.subtitles = []
    this.headers = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目类型', value: 'subjectType' },
      { text: '费用科目SAP代码', value: 'sapCode' },
      { text: '年初原始预算金额(USD)', value: 'originalMoney' },
      { text: '当前预算金额(USD)', value: 'budgetMoney' },
      { text: '预算差额(USD)', value: 'originalMoneyChange' },
      { text: '已申请预算(USD)', value: 'applyMoney' },
      { text: '已付预算(USD)', value: 'usedMoney' },
      { text: '剩余预算(USD)', value: 'notUsedMoney' },
      { text: '付款频次', value: 'payFrequency' },
      { text: '已付次数', value: 'usedPayFrequency' },
      { text: '备注', value: 'remark' },
    ]
    this.headers2 = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目类型', value: 'subjectType' },
      { text: '费用科目SAP代码', value: 'sapCode' },
      { text: '预算金额(USD)', value: 'budgetMoney' },
      { text: '付款频次', value: 'payFrequency' },
      { text: '备注', value: 'remark' },
    ]
    this.headersAdjust = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目类型', value: 'subjectType' },
      { text: '费用科目SAP代码', value: 'sapCode' },
      // { text: '预算金额(USD)', value: 'budgetMoney' },
      { text: '年初原始预算金额(USD)', value: 'originalMoney' },
      { text: '当前预算金额(USD)', value: 'budgetMoney' },
      { text: '预算差额(USD)', value: 'originalMoneyChange' },
      { text: '调整后金额(USD)', value: 'moneyAfter' },
      { text: '已付预算(USD)', value: 'usedMoney' },
      { text: '调整前剩余预算(USD)', value: 'notUsedMoney' },
      { text: '调整后剩余预算(USD)', value: 'notUsedMoneyAfter' },
      { text: '付款频次', value: 'payFrequency' },
      { text: '已付次数', value: 'usedPayFrequency' },
      { text: '备注', value: 'remark' },
    ]
    this.rangeArray(2020, new Date().getFullYear() + 1)
  },
  data() {
    return {
      list: [],
      detail: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        yyyy: (v) => /^\d{4}$/.test(v) || '请输入年份格式',
      },
      file: null,
      yearList: Array.from({ length: 50 }, (_, i) => i + 1),
    }
  },
  watch: {
    file(val) {
      if (val) {
        this.importExcel()
      }
    },
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new' && this.detail.status !== '-1'
    },
    allBudget() {
      return this.list
        .reduce((a, b) => a + (b.budgetMoney || 0) * 1, 0)
        .toFixed(2)
    },
    canSubmit() {
      return (
        this.detail.businessStatus == '草稿' ||
        !this.isEdit ||
        this.detail.status == '2'
      )
    },
    isAdjust() {
      //                   /**
      // *      * 状态 0未确认 1已确认
      // *      * //    ("-1", "单船财务保存，草稿"),  草稿
      // *      * //    ("0", "单船财务提交，待财务主管确认"), 待财务主管确认
      // *      * //    NOT_STARTED("1", "未开始(草稿)、财务主管已确认"),财务主管已确认
      // *      * //    HAVE_IN_HAND("2", "进行中(已提交)、调整预算提交审批"),调整预算提交审批各节点状态
      // *      * //    COMPLETED("3", "已完成、调整预算审批通过，更新最新预算数据"), 预算调整通过
      // *      * //    REJECT("4", "已驳回、调整预算审批被驳回，保持原有预算数据预算数据"),预算调整被驳回
      // *      *
      return this.detail.status == 2
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return
      const reqUrl =
        this.$route.params.id !== 'new'
          ? '/business/shipAffairs/budgetYear/modifyBudgetYear'
          : '/business/shipAffairs/budgetYear/addBudgetYear'
      const { errorRaw, data } = await this.postAsync(reqUrl, {
        ...this.detail,
        list: this.list,
        allBudget: this.allBudget,
      })
      if (errorRaw) return false
      if (notMove) {
        return data
      }
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (!this.$refs.tform.validate()) return
      const data = await this.save(goBack, true)
      console.log('this.detail.businessStatus', this.detail.businessStatus)
      if (data) {
        // 单船财务提交到财务主管确认
        if (
          this.detail.businessStatus == '草稿' ||
          this.detail.businessStatus == undefined ||
          !this.isEdit
        ) {
          this.detail.status = '0'
          this.detail.id = data
          this.detail.businessStatus = '待财务主管确认'
          const { errorRaw } = await this.postAsync(
            '/business/shipAffairs/budgetYear/modifyBudgetYear',
            {
              ...this.detail,
              list: this.list,
              allBudget: this.allBudget,
            },
          )
          if (errorRaw) return false
          goBack()
        } else {
          //审批流提交审批submit
          if (!this.detail.auditParams) {
            const { errorRaw } = await this.getAsync(
              '/business/shipAffairs/budgetYear/budgetYearSubmit',
              { applyId: data },
            )
            if (!errorRaw) goBack()
          } else {
            const error = await this.$refs.audit.submit()
            if (!error) goBack()
          }
        }
      }
      // const { errorRaw } = await this.getAsync(
      //   `/business/shipAffairs/budgetYear/haveCheck/${data}`,
      // )
      // if (errorRaw) return
      // goBack()
    },

    async loadDetail() {
      if (!this.isEdit) {
        const { data } = await this.getAsync(
          '/business/shipAffairs/budgetYear/getSubjectList',
        )
        this.list = data.map((i) => ({ ...i, budgetMoney: 0, subjectId: i.id }))
        return
      }
      const { data } = await this.getAsync(
        `/business/shipAffairs/budgetYear/getByIdNew/${this.$route.params.id}`,
      )
      this.detail = data
      this.list = data.list
      this.detail.budgetYear = Number(data.budgetYear)
    },
    async dowExcel() {
      // const items = this.components.map((i) => i.materialId)
      const { errorRaw } = await this.blobDownload(
        '/business/shipAffairs/budgetYear/dowExcel',
        { ...this.detail, items: this.list },
        '年度预算导入模板.xlsx',
      )
      if (errorRaw) this.$dialog.message.error(errorRaw)
    },
    async importExcel() {
      let formData = new FormData()
      formData.append('file', this.file)
      const { data } = await this.postAsync(
        '/business/shipAffairs/budgetYear/importExcel',
        formData,
      )
      if (data) {
        this.list = data.list
        // this.detail.budgetYear = data.budgetYear
        this.detail.budgetYear = Number(data.budgetYear)
        this.detail.remark = data.remark
        this.detail.shipCode = data.shipCode
      }
    },
    // 财务主管确认
    async finSupConfirm() {
      if (!(await this.$dialog.msgbox.confirm('确定生效此年度预算吗？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/budgetYear/haveCheck/${this.detail.id}`,
      )
      if (errorRaw) return
      this.closeAndTo(this.backRouteName)
    },
    // 财务主管退回
    async finSupReturn() {
      if (!(await this.$dialog.msgbox.confirm('确定退回此年度预算吗？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/budgetYear/haveReturn/${this.detail.id}`,
      )
      if (errorRaw) return
      this.closeAndTo(this.backRouteName)
    },
    rangeArray(start, end) {
      let length = end - start + 1
      let step = start - 1
      this.yearList = Array.from({ length: length }, () => {
        step++
        return step
      })
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
