<template>
  <v-container fluid>
    <v-dialog
      v-model="loading"
      persistent
      max-width="380"
      scrollable
      v-if="isEdit"
    >
      <v-card class="loading-card">
        <v-card-text class="loading-content">
          <div class="loading-header">
            <v-icon size="32" color="primary">mdi-database-sync</v-icon>
          </div>
          <div class="loading-body">
            <h3 class="loading-title">数据加载中</h3>
            <div class="progress-wrapper">
              <div class="progress-bar">
                <div class="progress-inner"></div>
              </div>
            </div>
            <div class="loading-message">
              <v-icon small color="grey darken-1">mdi-information</v-icon>
              <span>系统正在处理预算数据，请稍候...</span>
            </div>
          </div>
          <div class="loading-footer">
            <v-btn
              small
              outlined
              color="primary"
              @click="loading = false"
              class="close-btn"
            >
              <v-icon left small>mdi-close</v-icon>
              关闭提示
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-detail-view
      v-permission="['年度费用预算:编辑']"
      :title="`年度费用预算-${detail.budgetYear}-调整`"
      :tooltip="detail.budgetYear"
      :backRouteName="backRouteName"
      @save="save"
      @submit="submit"
      :can-submit="true"
      :can-save="false"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  readonly
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  readonly
                  v-model="detail.budgetYear"
                  label="年度"
                  outlined
                  dense
                  :rules="[rules.required, rules.yyyy]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="allBudget"
                  label="总费用"
                  outlined
                  readonly
                  dense
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.applyDate"
                  label="填报日期"
                  use-today
                  outlined
                  disabled
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :value="
                    detail.applyUserName || $local.data.get('userInfo').nickName
                  "
                  label="填报用户"
                  outlined
                  disabled
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="9">
                <v-text-field
                  v-model="detail.remark"
                  label="备注"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
            </v-row>
            <v-attach-list
              :attachments="detail.attachmentRecords"
              @change="changeAttachment"
            ></v-attach-list>
            <v-form ref="tform">
              <v-divider></v-divider>
              <v-data-table
                hide-default-footer
                disable-pagination
                dense
                :headers="headers"
                class="use-divider"
                show-expand
                :items="list"
              >
                <template v-slot:[`item.budgetMoney`]="{ item }">
                  {{ item.budgetMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.moneyAfter`]="{ item }">
                  <v-text-field
                    v-model="item.moneyAfter"
                    single-line
                    dense
                    :rules="[rules.required, rules.number]"
                    outlined
                  ></v-text-field>
                </template>
                <template v-slot:[`item.usedMoney`]="{ item }">
                  {{ item.usedMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.notUsedMoney`]="{ item }">
                  {{ item.notUsedMoney.toLocaleString() }}
                </template>
                <template v-slot:[`item.payFrequency`]="{ item }">
                  <div class="my-n3">
                    <v-select
                      v-model="item.payFrequency"
                      :items="[
                        { text: '年度', value: '1' },
                        { text: '季度', value: '2' },
                        { text: '月度', value: '3' },
                        { text: '不定期', value: '4' },
                      ]"
                      dense
                      readonly
                      required
                      outlined
                    ></v-select>
                  </div>
                </template>
                <template v-slot:[`item.remark`]="{ item }">
                  <!-- <v-text-field
                    v-model="item.remark"
                    single-line
                    dense
                    outlined
                  ></v-text-field> -->
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        @click="editRemark2(item)"
                        v-bind="attrs"
                        v-on="on"
                        :readonly="isEdit"
                        v-model="item.remark"
                        label="推荐理由"
                        single-line
                        dense
                        outlined
                      ></v-text-field>
                    </template>
                    <span>{{ item.remark }}</span>
                  </v-tooltip>
                </template>
                <template v-slot:[`item.originalMoneyChange`]="{ item }">
                  {{ (item.budgetMoney - item.originalMoney).toLocaleString() }}
                </template>
                <template v-slot:[`item.notUsedMoneyAfter`]="{ item }">
                  {{ (item.moneyAfter - item.applyMoney).toLocaleString() }}
                </template>
                <template v-slot:expanded-item="{ headers, item }">
                  <td :colspan="headers.length">
                    <budgetCostsubjectList
                      :item-id="isEdit ? item.subjectId : item.id"
                      :ship-code="detail.shipCode"
                      :year="detail.budgetYear"
                    ></budgetCostsubjectList>
                  </td>
                </template>
              </v-data-table>
              <v-divider></v-divider>
            </v-form>
          </v-container>
        </v-form>
      </v-card-text>
    </v-detail-view>
    <v-dialog v-model="dialog2" max-width="600">
      <template v-slot:default="dialog2">
        <v-card style="height: 320px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark2"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog2.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-0" style="color: black">
                  科目名称：{{ editRemarkDetails2.subjectName }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  当前预算金额(USD)：{{ editRemarkDetails2.budgetMoney }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  调整后金额(USD)：{{ editRemarkDetails2.moneyAfter }}
                </v-col>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails2.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import budgetCostsubjectList from '././budget-costsubject-list.vue'
import routerControl from '@/mixin/routerControl'
// budgetMoney	预算金额		true
// number
// remark	备注		false
// string
// sapCode	预算编号(费用科目SAP代码)		false
// string
// subjectId	费用科目id		true
// string
// subjectName	科目名称		false
// string
// subjectType	科目类型		false
// string
export default {
  components: { budgetCostsubjectList },
  name: 'budget-year-detail-adjust',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'budget-year-list'
    this.subtitles = []
    this.headers = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目类型', value: 'subjectType' },
      { text: '费用科目SAP代码', value: 'sapCode' },
      // { text: '预算金额(USD)', value: 'budgetMoney' },
      { text: '年初原始预算金额(USD)', value: 'originalMoney' },
      { text: '当前预算金额(USD)', value: 'budgetMoney' },
      { text: '预算差额(USD)', value: 'originalMoneyChange' },
      { text: '调整后金额(USD)', value: 'moneyAfter' },
      { text: '已申请预算(USD)', value: 'applyMoney' },
      { text: '已付预算(USD)', value: 'usedMoney' },
      { text: '调整前剩余预算(USD)', value: 'notUsedMoney' },
      { text: '调整后剩余预算(USD)', value: 'notUsedMoneyAfter' },
      { text: '付款频次', value: 'payFrequency' },
      { text: '已付次数', value: 'usedPayFrequency' },
      { text: '备注', value: 'remark' },
    ]
  },
  data() {
    return {
      list: [],
      detail: { attachmentRecords: [] },
      loading: true,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        yyyy: (v) => /^\d{4}$/.test(v) || '请输入年份格式',
      },
      dialog2: false,
      editRemarkDetails2: {},
    }
  },
  watch: {},
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    allBudget() {
      return this.list.reduce((a, b) => a + (b.budgetMoney || 0) * 1, 0)
    },
    // canSubmit() {
    //   return !this.detail?.status
    // },
    // isAdjust() {
    //   return !this.detail?.status
    // },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/budgetYear/modifyBudgetYear'
      const { errorRaw, data } = await this.postAsync(reqUrl, {
        ...this.detail,
        list: this.list,
        allBudget: this.allBudget,
      })
      if (errorRaw) return false
      if (notMove) {
        return data
      }
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (!this.$refs.tform.validate()) return
      const data = await this.save(goBack, true)
      //审批流提交审批submit
      // if (!this.detail.auditParams) {
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/budgetYear/budgetYearSubmit',
        { applyId: data },
      )
      if (!errorRaw) goBack()
      // } else {
      //   const error = await this.$refs.audit.submit()
      //   if (!error) goBack()
      // }
    },

    async loadDetail() {
      this.loading = true
      const { data } = await this.getAsync(
        `/business/shipAffairs/budgetYear/getById/${this.$route.params.id}`,
      )
      this.detail = data
      this.list = data.list
      this.list.forEach((item) => {
        item.moneyAfter = item.budgetMoney
        item.moneyBefore = item.budgetMoney
      })
      this.loading = false
    },
    editRemark2(item) {
      // console.log(item)
      console.log(item)
      this.editRemarkDetails2 = item
      this.dialog2 = true
    },
    saveRemark2() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog2 = false
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style scoped>
.loading-card {
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #ffffff;
}

.loading-content {
  padding: 24px;
  min-height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.loading-header {
  text-align: center;
  margin-bottom: 20px;
}

.loading-body {
  text-align: center;
}

.loading-title {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
}

.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
  margin-top: 16px;
}

.loading-footer {
  text-align: center;
  margin-top: 24px;
}

.progress-wrapper {
  padding: 0 40px;
}

.progress-bar {
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #1976d2, #42a5f5);
  width: 30%;
  border-radius: 2px;
  animation: progress 1.5s ease-in-out infinite;
  background-size: 200% 100%;
}

.close-btn {
  min-width: 100px;
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* 添加过渡动画 */
.v-dialog-transition-enter-active,
.v-dialog-transition-leave-active {
  transition: all 0.3s ease;
}

.v-dialog-transition-enter,
.v-dialog-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
