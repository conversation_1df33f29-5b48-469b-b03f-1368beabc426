<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        费用统计清单
        <v-spacer></v-spacer>
        <v-btn
          :disabled="!searchObj.shipCode"
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="loadStatistic"
          v-permission="['费用统计清单:搜索']"
        >
          <v-icon left>mdi-magnify-expand</v-icon>
          搜索
        </v-btn>
        <v-btn
          :disabled="!searchObj.shipCode"
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="getExcel"
          v-permission="['费用统计清单:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-row>
          <v-col cols="12" sm="6" md="4">
            <v-menu
              v-model="datesMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  ref="dates"
                  :value="dateRangeText"
                  label="时间范围"
                  append-icon="mdi-calendar"
                  outlined
                  dense
                  readonly
                  clearable
                  @click:clear="dates = []"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <vc-date-picker
                v-model="dates"
                mode="date"
                is-range
              ></vc-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-dict-select
              clearable
              v-model="searchObj.subjectType"
              label="费用类型"
              dense
              outlined
              dict-type="cost_subject_type"
            ></v-dict-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-autocomplete
              clearable
              v-model="searchObj.subjectName"
              label="费用科目"
              dense
              outlined
              :items="costSubjects"
              :disabled="!searchObj.subjectType"
            ></v-autocomplete>
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              dense
              v-model="searchObj.flag"
              label="去坞修&船员&固定资产&保险费"
              outlined
              :items="yn"
            ></v-select>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-data-table
        :headers="headers"
        :items="list"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
        :item-class="classFilter"
      >
        <template v-slot:[`item.name`]="{ item }">
          {{ `${item.name}/${item.nameEn}` }}
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>
<script>
// ccyName	币种	string
// completeDate	完成日期	string(date-time)
// happenDate	发生日期	string(date-time)
// invoiceCode	发票编号	string
// invoiceDate	发票日期	string(date-time)
// money	金额	number
// name	供应商中文名	string
// nameEn	供应商英文名	string
// payDate	付款日期（发生日期+赊销期）	string(date-time)
// place	完成地点	string
// remark	备注	string
// shipCode	船舶编码	string
// shipName	船舶名	string
// sort	排序使用 1普通 2（小计或合计） 3合计	integer(int32)
// subjectName	费用科目名称	string
// subjectType	费用科目类型	string
// supplyName	供应商名	string
// usdMoney	折算美金	number
export default {
  name: 'budget-statics-list',
  created() {
    this.headers = [
      { text: '科目业务分类', value: 'subjectType' },
      { text: '科目名称', value: 'subjectName' },
      { text: '船舶', value: 'shipName' },
      { text: '金额', value: 'money' },
      { text: '币种', value: 'ccyName' },
      { text: '折算美金', value: 'usdMoney' },
      { text: '发生日期', value: 'happenDate' },
      { text: '完成日期', value: 'completeDate' },
      { text: '完成地点', value: 'place' },
      { text: '供应商', value: 'name' },
      { text: '发票编号', value: 'invoiceCode' },
      { text: '发票日期', value: 'invoiceDate' },
      { text: '付款日期', value: 'payDate' },
      { text: '备注', value: 'remark' },
    ]

    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
    excelUrl() {
      return `/api/business/shipAffairs/budgetYear/exportCostStatistics?shipCode=${this.searchObj.shipCode}&year=${this.searchObj.year}`
    },
  },

  data() {
    return {
      selected: false,
      searchObj: {
        // year: new Date().getFullYear(),
        shipCode: '',
        subjectType: '',
        subjectName: '',
        flag: false,
      },
      dates: [],
      datesMenu: false,
      list: [],
      costSubjects: [],
    }
  },

  watch: {
    'searchObj.subjectType': {
      handler: function (val) {
        this.getCostSubjects(val)
      },
    },
  },

  methods: {
    async loadStatistic() {
      const { data } = await this.postAsync(
        '/business/shipAffairs/budgetYear/costStatistics',
        {
          ...this.searchObj,
          fromTime: this.dates?.start?.toISOString()?.split('T')[0],
          toTime: this.dates?.end?.toISOString()?.split('T')[0],
        },
      )
      this.list = data
    },
    async getExcel() {
      await this.blobDownload(
        '/business/shipAffairs/budgetYear/exportCostStatistics',
        {
          ...this.searchObj,
          fromTime: this.dates?.start?.toISOString()?.split('T')[0],
          toTime: this.dates?.end?.toISOString()?.split('T')[0],
        },
      )
    },

    async getCostSubjects(subjectType) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/costSubject/page',
        {
          subjectType,
          size: 1000,
          current: 1,
        },
      )
      this.costSubjects = data.records.map((item) => item.subjectName)
    },

    classFilter(item) {
      // 合计行背景色为深蓝，科目小计行为浅蓝
      if (item.subjectName.includes('小计')) {
        return 'blue lighten-5'
      } else if (item.subjectName.includes('合计')) {
        return 'blue darken-1'
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
