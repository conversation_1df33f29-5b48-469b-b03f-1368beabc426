<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :push-params="pushParams"
      :search-remain="searchObj"
      :single-select="false"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <!-- <v-text-field
            outlined
            dense
            label="年份"
            clearable
            v-model="searchObj.year"
          ></v-text-field> -->
          <v-autocomplete
            outlined
            dense
            label="年度"
            clearable
            v-model="searchObj.budgetYear"
            :items="yearList"
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            :items="[
              { text: '草稿', value: '-1' },
              { text: '待确认生效', value: '0' },
              { text: '已生效', value: '1' },
              { text: '预算调整审批中', value: '2' },
              { text: '预算调整审批通过', value: '3' },
              { text: '预算调整被驳回', value: '4' },
            ]"
            outlined
            dense
            label="状态"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!canExport"
          :loading="loading"
          tile
          color="primary"
          outlined
          class="mx-1"
          @click="dowExcel"
          v-permission="['年度费用预算:导出预算']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          导出预算数据
        </v-btn>
        <v-btn
          :disabled="!canBatchSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="batchSubmit"
          v-permission="['年度费用预算:批量确认']"
        >
          <v-icon left>mdi-send</v-icon>
          批量确认
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="
            selected.status !== '1' &&
            selected.status !== '3' &&
            selected.status !== '4'
          "
          :to="{
            name: 'budget-year-detail-adjust',
            params: { id: selected.id },
          }"
          v-permission="['年度费用预算:调整年度预算']"
        >
          <v-icon left>mdi-application-edit</v-icon>
          调整年度预算
        </v-btn> -->
        <!-- <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="!canBatchSubmit"
          :to="{
            name: 'budget-year-detail-adjust',
            params: { id: selected.id },
          }"
          v-permission="['年度费用预算:调整年度预算']"
        >
          <v-icon left>mdi-application-edit</v-icon>
          调整年度预算
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :to="{ name: 'budget-year-detail', params: { id: 'new' } }"
          v-permission="['年度费用预算:新建年度预算']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新建年度预算
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :href="`/api/business/shipAffairs/budgetYear/excelBudgetYear?year=${searchObj.year}`"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn> -->
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status == -1" :dark="true">草稿</v-chip>
        <v-chip v-if="item.status == 0" color="info" :dark="true">
          待确认生效
        </v-chip>
        <v-chip v-if="item.status == 1" color="success" :dark="true">
          已生效
        </v-chip>
        <v-chip v-if="item.status == 2" color="warning" :dark="true">
          预算调整审批中
        </v-chip>
        <v-chip v-if="item.status == 3" color="success" :dark="true">
          预算调整审批通过
        </v-chip>
        <v-chip v-if="item.status == 4" color="error" :dark="true">
          预算调整被驳回
        </v-chip>
        <!-- this.statusColors = ['info', '', 'warning', 'success', 'error'] -->
      </template>
      <template v-slot:[`item.allBudget`]="{ item }">
        {{ item.allBudget.toLocaleString() }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// allBudget	预算合计（美元）	number
// applyDate	填报时间	string
// applyUser	填报人	string
// applyUserName	填报人名	string
// budgetYear	年度	string
// id	物理主键	string
// remark	备注	string
// shipCode	船舶编码	string
// shipName	船舶名	string
// status	状态 0未确认 1已确认	integer
export default {
  name: 'budget-year-list',
  created() {
    this.tableName = '年度费用预算'
    this.reqUrl = '/business/shipAffairs/budgetYear/page'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶名', value: 'shipName' },
      { text: '年度', value: 'budgetYear' },
      { text: '预算合计（美元）', value: 'allBudget' },
      { text: '填报人', value: 'applyUserName' },
      { text: '填报时间', value: 'applyDate' },
      { text: '状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'budget-year-detail' }
    this.rangeArray(2020, new Date().getFullYear() + 1)
  },

  data() {
    return {
      selected: false,
      loading: false,
      searchObj: {
        budgetYear: new Date().getFullYear(),
        status: '1',
        isMe: true,
      },
      yearList: Array.from({ length: 50 }, (_, i) => i + 1),
    }
  },
  computed: {
    canBatchSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            item?.status == '0' && item?.businessStatus == '待财务主管确认',
        )
      )
    },
    canExport() {
      // 只有当选中的条目数量为 1 时，按钮才可点击
      return this.selected.length === 1
    },
  },
  methods: {
    async dowExcel() {
      this.loading = true
      const selectedId = this.selected[0].id // 获取选中的ID
      const name = this.selected[0].shipName
      const year = this.selected[0].budgetYear
      const allBudget = this.selected[0].allBudget

      const { errorRaw } = await this.getBlobDownload(
        `/business/shipAffairs/budgetYear/dowExcel1?id=${selectedId}`,
        {},
        name + '-' + year + '-' + allBudget + `.xlsx`,
      )
      if (errorRaw) this.$dialog.message.error(errorRaw)
      this.loading = false
    },
    rangeArray(start, end) {
      let length = end - start + 1
      let step = start - 1
      this.yearList = Array.from({ length: length }, () => {
        step++
        return step
      })
    },
    async batchSubmit() {
      if (!(await this.$dialog.msgbox.confirm('确定提交所选记录？'))) return
      this.loading = true
      this.selected.forEach((item) => {
        const { errorRaw } = this.getAsync(
          `/business/shipAffairs/budgetYear/haveCheck/${item.id}`,
        )
        if (errorRaw) {
          return
        }
      })
      this.$dialog.message.success(`提交成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
  },

  mounted() {
    if (this.$route.query.status != undefined) {
      this.searchObj.status = this.$route.query.status
      if (this.searchObj.status == 2) {
        this.searchObj.budgetYear = ''
      }

      // // 在3秒后执行一次任务
      // setTimeout(() => {
      //   this.searchObj.status = ''
      // }, 1000)
    }
  },
}
</script>

<style></style>
