<template>
  <v-sheet class="my-3">
    <!-- <v-card-subtitle class="text-h6 py-1">预算明细</v-card-subtitle> -->
    <v-divider></v-divider>
    <!-- dense
      :headers="预算明细表头"
      :items="list"
      hide-default-footer
      disable-pagination -->
    <v-table-searchable
      ref="table"
      :show-select="false"
      :table-name="tableName"
      :headers="预算明细表头"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
    >
      <template v-slot:[`item.applyRemark`]="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <span v-on="on">{{ item.applyRemark.substring(0, 9) }}...</span>
          </template>
          <span>{{ item.applyRemark }}</span>
        </v-tooltip>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses2[item.status] }}
        </v-chip>
      </template>
      <template v-slot:[`item.money`]="{ item }">
        {{ item.money.toLocaleString() }}
      </template>
      <template v-slot:[`item.finalMoney`]="{ item }">
        {{ item.finalMoney.toLocaleString() }}
      </template>
      <template v-slot:[`item.moneyUsd`]="{ item }">
        {{ item.moneyUsd.toLocaleString() }}
      </template>
      <template v-slot:[`item.supplierName`]="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <span v-on="on">{{ item.supplierName.substring(0, 25) }}...</span>
          </template>
          <span>{{ item.supplierName }}</span>
        </v-tooltip>
      </template>
      <template v-slot:[`item.toDetail`]="{ item }">
        <v-btn
          v-if="item.id"
          :to="{
            name: 'large-purchase-detail',
            params: { id: item.id },
          }"
          color="primary"
          text
          small
        >
          预算明细
        </v-btn>
        <v-btn
          v-if="item.accidentId"
          :to="{
            name: 'accident-detail',
            params: { id: item.accidentId },
          }"
          color="primary"
          text
          small
        >
          事故报告
        </v-btn>
        <v-btn
          v-if="item.costProjectId"
          :to="{
            name: 'cost-project-detail',
            params: { id: item.costProjectId },
          }"
          color="primary"
          text
          small
        >
          费用项目
        </v-btn>
        <v-btn
          v-if="item.businessId"
          :to="{
            name: routerMap[item.dataSource],
            params: { id: item.businessId },
          }"
          color="primary"
          text
          small
        >
          订单详情
        </v-btn>
      </template>
    </v-table-searchable>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
export default {
  name: 'budget-costsubject-list',
  created() {
    this.tableName = '预算明细'
    this.reqUrl = '/business/shipAffairs/supplyCommon/page'
    this.预算明细表头 = [
      // { text: '船舶名称', value: 'shipInfo' },
      { text: '链接', value: 'toDetail' },
      { text: '数据来源', value: 'dataSource' },
      { text: '供应商', value: 'supplierName' },
      { text: '供应项目', value: 'supplyItem' },
      { text: '订单号', value: 'orderNo' },
      { text: '科目', value: 'costSubjectName' },
      { text: '预算金额', value: 'money' },
      { text: '实际付款金额', value: 'finalMoney' },
      { text: '折算美金', value: 'moneyUsd' },
      { text: '币种', value: 'currencyName' },
      { text: '发生日期', value: 'proposedDate' },
      { text: '实际申请人', value: 'applicantName' },
      { text: '预算说明', value: 'applyRemark' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      // { text: '附件', value: 'attachmentRecords' },
    ]
    this.statuses = ['', '草稿', '审批中', '通过']
    this.color = ['', '草稿', '审批中', '通过']
    this.dataSources = [
      { text: '单次预算', value: '单次预算' },
      { text: '批量预算', value: '批量预算' },
      { text: '备件订单', value: '备件订单' },
      { text: '物料订单', value: '物料订单' },
      { text: '滑油订单', value: '滑油订单' },
      { text: '航修修理单', value: '航修修理单' },
      { text: '坞修修理单', value: '坞修修理单' },
    ]
    this.routerMap = {
      // 0: '',
      备件订单: 'spare-order-detail',
      物料订单: 'materials-order-detail',
      滑油订单: 'soil-order-detail',
      航修修理单: 'voyage-repair-detail',
      坞修修理单: 'dock-repair-detail',
      // 6: 'spare-order-detail',
      // 7: 'hire-purchase-detail',
      // 8: '',
      // 9: 'self-repair-bonus-detail',
      // 10: '',
      // 11: '',
      // 12: 'materials-order-detail',
      // 13: 'batch-cost-detail',
    }
    // this.routerMap = {
    //   0: '',
    //   1: 'spare-order-detail',
    //   2: 'materials-order-detail',
    //   3: 'soil-order-detail',
    //   4: 'voyage-repair-detail',
    //   5: 'dock-repair-detail',
    //   6: 'spare-order-detail',
    //   7: 'hire-purchase-detail',
    //   8: '',
    //   9: 'self-repair-bonus-detail',
    //   10: '',
    //   11: '',
    //   12: 'materials-order-detail',
    //   13: 'batch-cost-detail',
    // }
    this.bussinessModules = [
      // { text: '手动录入', value: 0 },
      { text: '备件订单', value: '备件订单' },
      { text: '物料订单', value: '物料订单' },
      { text: '滑油订单', value: '滑油订单' },
      { text: '航修修理单', value: '航修修理单' },
      { text: '坞修修理单', value: '坞修修理单' },
      // { text: '坞修备件订单', value: 6 },
      // { text: '分期付款', value: 7 },
      // { text: '备用金', value: 8 },
      // { text: '自修奖', value: 9 },
      // { text: '船东账', value: 10 },
      // { text: '新造船', value: 11 },
      // { text: '坞修物料订单', value: 12 },
      // { text: '批量费用', value: 13 },
    ]
    this.statuses2 = ['暂无审批', '草稿', '审批中', '已审批', '已驳回', '废弃']
    this.statusColors = ['info', '', 'warning', 'success', 'error', 'error']
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
      { text: '废弃', value: '5' },
    ]
  },
  props: {
    itemId: String,
    shipCode: String,
    year: String,
  },
  data() {
    return {
      list: [],
      searchObj: {
        budgetYearFlag: '年度预算',
        costSubjectId: this.itemId,
        shipCode: this.shipCode,
        year: this.year,
        // status: 3,
      },
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/supplyCommon/page',
        {
          costSubjectId: this.itemId,
          shipCode: this.shipCode,
          year: this.year,
          budgetYearFlag: '年度预算',
          // status: 3,
        },
      )
      this.list = data.records
    },
  },

  mounted() {
    // this.loadDetail()
  },
}
</script>

<style></style>
