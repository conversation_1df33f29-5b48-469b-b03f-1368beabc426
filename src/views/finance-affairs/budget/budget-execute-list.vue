<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        预算执行分析
        <v-spacer></v-spacer>
        <v-btn
          :disabled="
            isMultiple ? searchObj.shipCodes.length === 0 : !searchObj.shipCode
          "
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="loadStatistic"
          v-permission="['预算执行分析:搜索']"
        >
          <v-icon left>mdi-magnify-expand</v-icon>
          搜索
        </v-btn>
        <v-btn
          :disabled="
            isMultiple ? searchObj.shipCodes.length === 0 : !searchObj.shipCode
          "
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="getExcel"
          v-permission="['预算执行分析:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-row>
          <v-col cols="12" sm="6" md="2">
            <v-text-field
              outlined
              dense
              label="年份"
              clearable
              v-model="searchObj.year"
            ></v-text-field>
          </v-col>
          <v-col v-if="isMultiple" cols="12" sm="6" md="4">
            <v-ship-select
              v-model="searchObj.shipCodes"
              multiple
            ></v-ship-select>
          </v-col>
          <v-col v-else cols="12" sm="6" md="4">
            <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              dense
              v-model="searchObj.flag"
              label="去坞修&船员&固定资产&保险费"
              outlined
              :items="yn"
            ></v-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-switch
              class="mt-1"
              dense
              v-model="isMultiple"
              label="多船"
            ></v-switch>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-data-table
        :headers="isMultiple ? headers2 : headers1"
        :items="list"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
      ></v-data-table>
    </v-card>
  </v-container>
</template>
<script>
// compareLastActual	与上年预算比较	number
// compareLastBudget	与上年决算比较	number
// lastProgress	去年完成百分比	string
// lastYearActual	去年决算	number
// lastYearBudget	去年预算	number
// nowProgress	今年完成百分比	string
// nowYearActual	今年决算	number
// nowYearBudget	今年预算	number
// sapCode	项目编号	string
// subjectName	科目名称	string
// subjectType	科目业务分类	string
// twoProgress	前年完成百分比	string
// twoYearActual	前年决算	number
// twoYearBudget	前年预算	number
export default {
  name: 'budget-execute-list',
  created() {
    this.headers = [
      { text: '船舶', value: 'shipName', type: 1 },
      { text: '科目名称', value: 'subjectName', type: 2 },
      { text: '科目业务分类', value: 'subjectType', type: 2 },
      { text: 'SAP', value: 'sapCode', type: 2 },
      { text: '前年预算', value: 'twoYearBudget' },
      { text: '前年决算', value: 'twoYearActual' },
      { text: '前年完成百分比', value: 'twoProgress' },
      { text: '去年预算', value: 'lastYearBudget' },
      { text: '去年决算', value: 'lastYearActual' },
      { text: '去年完成百分比', value: 'lastProgress' },
      { text: '今年预算', value: 'nowYearBudget' },
      { text: '今年决算', value: 'nowYearActual' },
      { text: '今年完成百分比', value: 'nowProgress' },
      { text: '与上年预算比较', value: 'compareLastBudget' },
      { text: '与上年决算比较', value: 'compareLastActual' },
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
    excelUrl() {
      return `/api/business/shipAffairs/budgetYear/exportBudgetExecute?shipCode=${this.searchObj.shipCode}&year=${this.searchObj.year}`
    },
    headers1() {
      return this.headers.filter((x) => x.type !== 1)
    },
    headers2() {
      return this.headers.filter((x) => x.type !== 2)
    },
  },

  data() {
    return {
      selected: false,
      searchObj: {
        year: new Date().getFullYear(),
        shipCode: '',
        shipCodes: [],
        flag: false,
      },
      dates: [],
      datesMenu: false,
      list: [],
      isMultiple: false,
    }
  },

  methods: {
    async loadStatistic() {
      const url = this.isMultiple
        ? '/business/shipAffairs/budgetYear/budgetExecuteTotal'
        : '/business/shipAffairs/budgetYear/budgetExecute'
      const { data } = await this.postAsync(url, this.searchObj)
      this.list = data
    },
    async getExcel() {
      const url = this.isMultiple
        ? '/business/shipAffairs/budgetYear/budgetExecuteTotalExcel'
        : '/business/shipAffairs/budgetYear/exportBudgetExecute'
      await this.blobDownload(url, this.searchObj)
    },
  },

  mounted() {},
}
</script>

<style></style>
