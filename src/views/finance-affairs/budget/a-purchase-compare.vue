<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        单船采购单价汇总
        <v-spacer></v-spacer>
        <v-btn
          :disabled="!searchObj.shipCode"
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="loadStatistic"
          v-permission="['单船采购单价:搜索']"
        >
          <v-icon left>mdi-magnify-expand</v-icon>
          搜索
        </v-btn>
        <v-btn
          :disabled="!searchObj.shipCode"
          outlined
          tile
          color="info"
          class="mx-1"
          :href="excelUrl"
          v-permission="['单船采购单价:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-form ref="form">
          <v-row>
            <v-col cols="12" sm="6" md="2">
              <v-ship-select
                :rules="[rules.required]"
                v-model="searchObj.shipCode"
              ></v-ship-select>
            </v-col>
            <!-- <v-col cols="12" sm="6" md="2">
              <v-text-field
                outlined
                dense
                label="年份"
                clearable
                v-model="searchObj.year"
                :rules="[rules.required]"
              ></v-text-field>
            </v-col> -->
            <v-col cols="12" sm="6" md="4">
              <v-menu
                v-model="datesMenu"
                :close-on-content-click="false"
                :nudge-right="40"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    ref="dates"
                    :value="dateRangeText"
                    label="时间范围"
                    append-icon="mdi-calendar"
                    outlined
                    dense
                    readonly
                    clearable
                    @click:clear="dates = []"
                    v-bind="attrs"
                    v-on="on"
                    :rules="[rules.required]"
                  ></v-text-field>
                </template>
                <vc-date-picker
                  v-model="dates"
                  mode="date"
                  is-range
                ></vc-date-picker>
              </v-menu>
            </v-col>
            <v-col cols="12" sm="6" md="2">
              <v-select
                v-model="searchObj.itemType"
                label="物品类型"
                outlined
                dense
                :items="itemTypes"
                :rules="[rules.required]"
              ></v-select>
            </v-col>
            <v-col md="3" cols="12">
              <v-dialog-select
                req-url="/business/shipAffairs/costSubject/page"
                label="费用科目"
                v-model="searchObj.costSubId"
                :search-dicts="searchDicts"
                item-text="subjectName"
                item-value="id"
                :headers="subHeaders"
                dense
              ></v-dialog-select>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-divider></v-divider>
      <v-data-table
        :headers="headers"
        :items="list"
        hide-default-footer
        disable-pagination
        dense
        item-key="itemId"
        class="use-divider"
      >
        <template v-slot:[`item.name`]="{ item }">
          {{ `${item.name}/${item.nameEn}` }}
        </template>
        <template v-slot:[`item.action`]="{ item }">
          <v-btn
            x-small
            tile
            color="info"
            class="mx-1"
            :to="{
              name: 'ships-purchase-compare',
              query: {
                itemType: searchObj.itemType,
                fromTime: dates.start.toISOString().split('T')[0],
                toTime: dates.end.toISOString().split('T')[0],
                itemCode:
                  searchObj.itemType === '01' ? item.itemCode : item.itemId,
                itemName: item.itemName,
              },
            }"
          >
            多船比价
          </v-btn>
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>
<script>
// ccyCode		string
// costSubName	费用科目名称	string
// deliveryDate	交付日期	string(date-time)
// discount	折扣	number
// itemCode	备件/物料编号	string
// itemId	物件id	string
// itemName	物资名称	string
// orderId	采购订单id	string
// orderNo	采购订单No	string
// price	单价	number
// purchaseNum	订单购买数量	number
// supplierName	供应商名称	string
// toUsd	折算美金	number
export default {
  name: 'a-purchase-compare',
  created() {
    this.headers = [
      { text: '物资名称', value: 'itemName' },
      { text: '物资编号', value: 'itemCode' },
      { text: '供应商', value: 'supplierName' },
      { text: '采购数量', value: 'purchaseNum' },
      { text: '币种', value: 'ccyCode' },
      { text: '单价', value: 'price' },
      { text: '折扣', value: 'discount' },
      { text: '折算美金', value: 'toUsd' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '采购订单', value: 'orderNo' },
      { text: '费用科目', value: 'costSubName' },
      { text: '多船比价', value: 'action' },
    ]
    this.itemTypes = [
      { text: '备件', value: '01' },
      { text: '物料', value: '02' },
      { text: '滑油', value: '03' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
    excelUrl() {
      return `/api/business/shipAffairs/purchaseStatistic/exportSummaryOfAShipInExcel?shipCode=${
        this.searchObj.shipCode
      }&itemType=${this.searchObj.itemType}&costSubId=${
        this.searchObj.costSubId || ''
      }&fromTime=${this.dates?.start?.toISOString()?.split('T')[0]}&toTime=${
        this.dates?.end?.toISOString()?.split('T')[0]
      }`
    },
  },

  data() {
    return {
      selected: false,
      searchObj: {
        // year: new Date().getFullYear(),
        shipCode: '',
      },
      dates: [],
      datesMenu: false,
      list: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },

  methods: {
    async loadStatistic() {
      if (!this.$refs.form.validate()) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseStatistic/getSummaryOfAShip',
        {
          ...this.searchObj,
          fromTime: this.dates?.start?.toISOString()?.split('T')[0],
          toTime: this.dates?.end?.toISOString()?.split('T')[0],
        },
      )
      this.list = data ?? []
    },
    async getExcel() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/budgetYear/exportCostStatistics',
        this.searchObj,
      )
      this.$download(data, '预算执行分析.xlsx')
    },
  },

  mounted() {},
}
</script>

<style></style>
