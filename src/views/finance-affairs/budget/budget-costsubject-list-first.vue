<template>
  <v-sheet class="my-3">
    <!-- <v-card-subtitle class="text-h6 py-1">预算明细</v-card-subtitle> -->
    <v-divider></v-divider>
    <!-- dense
      :headers="预算明细表头"
      :items="list"
      hide-default-footer
      disable-pagination -->
    <v-form ref="tform">
      <v-divider></v-divider>
      <v-data-table
        v-if="!isAdjust"
        hide-default-footer
        disable-pagination
        dense
        :headers="isEdit ? headers : headers2"
        class="use-divider"
        :show-expand="true"
        :items="list"
      >
        <template v-slot:[`item.budgetMoney`]="{ item }">
          <v-text-field
            :readonly="isEdit"
            v-model="item.budgetMoney"
            single-line
            dense
            :rules="[rules.required, rules.number]"
            @blur="item.budgetMoney = Number(item.budgetMoney).toFixed(2)"
            outlined
          ></v-text-field>
        </template>
        <template v-slot:[`item.originalMoneyChange`]="{ item }">
          {{ (item.budgetMoney - item.originalMoney).toLocaleString() }}
        </template>
        <template v-slot:[`item.applyMoney`]="{ item }">
          {{ item.applyMoney.toLocaleString() }}
        </template>
        <template v-slot:[`item.usedMoney`]="{ item }">
          {{ item.usedMoney.toLocaleString() }}
        </template>
        <template v-slot:[`item.notUsedMoney`]="{ item }">
          {{ item.notUsedMoney.toLocaleString() }}
        </template>
        <template v-slot:[`item.payFrequency`]="{ item }">
          <div class="my-n3">
            <v-select
              v-model="item.payFrequency"
              :items="[
                { text: '年度', value: '1' },
                { text: '季度', value: '2' },
                { text: '月度', value: '3' },
                { text: '不定期', value: '4' },
              ]"
              dense
              :rules="[rules.required]"
              readonly
              required
              outlined
            ></v-select>
          </div>
        </template>
        <template v-slot:[`item.remark`]="{ item }">
          <v-text-field
            :readonly="isEdit"
            v-model="item.remark"
            single-line
            dense
            outlined
          ></v-text-field>
        </template>
        <template v-slot:expanded-item="{ headers, item }">
          <td :colspan="headers.length">
            <budgetCostsubjectList
              :item-id="isEdit ? item.subjectId : item.id"
              :ship-code="shipCode"
              :year="year"
            ></budgetCostsubjectList>
          </td>
        </template>
      </v-data-table>
      <!-- 预算调整流程明细 -->
      <v-data-table
        v-if="isAdjust"
        hide-default-footer
        disable-pagination
        dense
        :headers="headersAdjust"
        class="use-divider"
        show-expand
        :items="list"
      >
        <template v-slot:[`item.budgetMoney`]="{ item }">
          {{ item.budgetMoney.toLocaleString() }}
        </template>
        <template v-slot:[`item.moneyAfter`]="{ item }">
          <span v-if="item.moneyAfter > item.budgetMoney" style="color: red">
            {{ item.moneyAfter.toLocaleString() }}
          </span>
          <span v-if="item.moneyAfter <= item.budgetMoney">
            {{ item.moneyAfter.toLocaleString() }}
          </span>
        </template>
        <template v-slot:[`item.usedMoney`]="{ item }">
          {{ item.usedMoney.toLocaleString() }}
        </template>
        <template v-slot:[`item.notUsedMoney`]="{ item }">
          {{ item.notUsedMoney.toLocaleString() }}
        </template>
        <template v-slot:[`item.payFrequency`]="{ item }">
          <div class="my-n3">
            <v-select
              v-model="item.payFrequency"
              :items="[
                { text: '年度', value: '1' },
                { text: '季度', value: '2' },
                { text: '月度', value: '3' },
                { text: '不定期', value: '4' },
              ]"
              dense
              readonly
              required
              outlined
            ></v-select>
          </div>
        </template>
        <!-- <template v-slot:[`item.remark`]="{ item }">
                  <v-text-field
                    v-model="item.remark"
                    single-line
                    asdasd
                    dense
                    outlined
                  ></v-text-field>
                </template> -->
        <template v-slot:[`item.remark`]="{ item }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <span v-on="on">{{ item.remark.substring(0, 9) }}...</span>
            </template>
            <span>{{ item.remark }}</span>
          </v-tooltip>
        </template>
        <template v-slot:[`item.notUsedMoneyAfter`]="{ item }">
          <span v-if="item.moneyAfter > item.budgetMoney" style="color: red">
            {{ (item.moneyAfter - item.applyMoney).toLocaleString() }}
          </span>
          <span v-if="item.moneyAfter <= item.budgetMoney">
            {{ (item.moneyAfter - item.applyMoney).toLocaleString() }}
          </span>
        </template>
        <template v-slot:[`item.originalMoneyChange`]="{ item }">
          {{ (item.budgetMoney - item.originalMoney).toLocaleString() }}
        </template>
        <template v-slot:expanded-item="{ headers, item }">
          <td :colspan="headers.length">
            <budgetCostsubjectList
              :item-id="isEdit ? item.subjectId : item.id"
              :ship-code="shipCode"
              :year="year"
            ></budgetCostsubjectList>
          </td>
        </template>
      </v-data-table>
      <v-divider></v-divider>
    </v-form>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
import budgetCostsubjectList from '././budget-costsubject-list.vue'
export default {
  components: { budgetCostsubjectList },
  name: 'budget-costsubject-list-first',
  created() {
    this.tableName = '预算明细'
    this.reqUrl = '/business/shipAffairs/supplyCommon/page'
    this.headers = [
      { text: '二级科目名称', value: 'subjectName' },
      // { text: '科目类型', value: 'subjectType' },
      { text: '费用科目SAP代码', value: 'sapCode' },
      { text: '年初原始预算金额(USD)', value: 'originalMoney' },
      { text: '当前预算金额(USD)', value: 'budgetMoney' },
      { text: '预算差额(USD)', value: 'originalMoneyChange' },
      { text: '已申请预算(USD)', value: 'applyMoney' },
      { text: '已付预算(USD)', value: 'usedMoney' },
      { text: '剩余预算(USD)', value: 'notUsedMoney' },
      { text: '付款频次', value: 'payFrequency' },
      { text: '已付次数', value: 'usedPayFrequency' },
      { text: '备注', value: 'remark' },
    ]
    this.headers2 = [
      { text: '二级科目名称', value: 'subjectName' },
      // { text: '科目类型', value: 'subjectType' },
      { text: '费用科目SAP代码', value: 'sapCode' },
      { text: '预算金额(USD)', value: 'budgetMoney' },
      { text: '付款频次', value: 'payFrequency' },
      { text: '备注', value: 'remark' },
    ]
    this.headersAdjust = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目类型', value: 'subjectType' },
      { text: '费用科目SAP代码', value: 'sapCode' },
      // { text: '预算金额(USD)', value: 'budgetMoney' },
      { text: '年初原始预算金额(USD)', value: 'originalMoney' },
      { text: '当前预算金额(USD)', value: 'budgetMoney' },
      { text: '预算差额(USD)', value: 'originalMoneyChange' },
      { text: '调整后金额(USD)', value: 'moneyAfter' },
      { text: '已申请预算(USD)', value: 'applyMoney' },
      { text: '已付预算(USD)', value: 'usedMoney' },
      { text: '调整前剩余预算(USD)', value: 'notUsedMoney' },
      { text: '调整后剩余预算(USD)', value: 'notUsedMoneyAfter' },
      { text: '付款频次', value: 'payFrequency' },
      { text: '已付次数', value: 'usedPayFrequency' },
      { text: '备注', value: 'remark' },
    ]
  },
  props: {
    subjectType: String,
    budgetId: String,
    shipCode: String,
    year: String,
    isAdjust: Boolean,
    isEdit: Boolean,
  },
  data() {
    return {
      list: [],
      searchObj: {
        // budgetYearFlag: '年度预算',
        subjectType: this.subjectType,
        budgetId: this.budgetId,
        // shipCode: this.shipCode,
        // year: this.year,
        // status: 3,
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        yyyy: (v) => /^\d{4}$/.test(v) || '请输入年份格式',
      },
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/budgetYear/getItemList',
        {
          subjectType: this.subjectType,
          budgetId: this.budgetId,
          // shipCode: this.shipCode,
          // year: this.year,
          // budgetYearFlag: '年度预算',
          // status: 3,
        },
      )
      this.list = data
      console.log(data)
      console.log(this.list)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
