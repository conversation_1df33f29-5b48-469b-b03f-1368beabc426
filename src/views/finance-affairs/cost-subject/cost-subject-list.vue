<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.subjectName"
                  label="科目名称"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-dict-select
                  v-model="formData.subjectType"
                  label="业务分类"
                  :rules="[rules.required]"
                  required
                  dict-type="cost_subject_type"
                  dense
                ></v-dict-select>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-select
                  outlined
                  :items="costTypes"
                  v-model="formData.sapCostType"
                  label="SAP费用类型"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-select>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.sapCode"
                  label="SAP代码"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.subjectName2"
                  label="科目名称(费用类型E)"
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.sapCode2"
                  label="SAP代码(费用类型E)"
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.subjectName3"
                  label="科目名称(费用类型F)"
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.sapCode3"
                  label="SAP代码(费用类型F)"
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.misCode"
                  label="MIS代码"
                  dense
                ></v-text-field>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-select
                  outlined
                  :items="flag"
                  v-model="formData.repairFlag"
                  label="坞修"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-select>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-select
                  outlined
                  :items="flag"
                  v-model="formData.businessAllocate"
                  label="是否SAP"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-select>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.useLife"
                  label="使用寿命(年)"
                  type="number"
                  :rules="[rules.number]"
                  dense
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.sort"
                  label="排序号"
                  type="number"
                  :rules="[rules.number]"
                  dense
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-select
                  v-model="formData.budgetSapCode"
                  :items="[
                    { text: '预估船东成本-通导', value: '2000999952' },
                    { text: '预估船东成本-海务', value: '2000999953' },
                    { text: '预估船东成本-机务', value: '2000999954' },
                  ]"
                  label="预估船东成本"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
              </v-col> -->
              <v-col cols="12" md="2">
                <v-select
                  label="付款频次"
                  v-model="formData.payFrequency"
                  :items="[
                    { text: '年度', value: '1' },
                    { text: '半年', value: '5' },
                    { text: '季度', value: '2' },
                    { text: '月度', value: '3' },
                    { text: '不定期', value: '4' },
                  ]"
                  dense
                  :rules="[rules.required]"
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  v-model="formData.sapCodeNew"
                  label="SAP代码"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['费用科目:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      fuzzy-label="模糊查询"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['费用科目:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['费用科目:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.sapCostType`]="{ item }">
        {{ costTypes.find((i) => i.value === item.sapCostType).text }}
      </template>
      <template v-slot:[`item.repairFlag`]="{ item }">
        {{ ['否', '是'][item.repairFlag] }}
      </template>
      <template v-slot:[`item.businessAllocate`]="{ item }">
        {{ ['否', '是'][item.businessAllocate] }}
      </template>
      <template v-slot:[`item.budgetSapCode`]="{ item }">
        <!-- <span v-if="item.businessAllocate == '2000999952'">
          预估船东成本-通导
        </span>
        <span v-if="item.businessAllocate == '2000999953'">
          预估船东成本-海务
        </span>
        <span v-if="item.businessAllocate == '2000999954'">
          预估船东成本-机务
        </span> -->
        {{
          item.budgetSapCode == '2000999952'
            ? '预估船东成本-通导'
            : item.budgetSapCode == '2000999953'
            ? '预估船东成本-海务'
            : '预估船东成本-机务'
        }}
      </template>
      <template v-slot:[`item.payFrequency`]="{ item }">
        {{ ['-', '年度', '季度', '月度', '不定期', '半年'][item.payFrequency] }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// businessAllocate	业务分配（是否SAP） 0否 1是	integer
// id	物理主键	string
// repairFlag	是否坞修 0否 1是	integer
// sapCode	SAP代码	string
// sapCostType	SAP费用类型 A: 厂修, B: 常规维修, C:船员奖励, D:船东费用,E:物料存货；F:金额调整	string
// subjectName	科目名称	string
// subjectType	科目业务分类	string
export default {
  name: 'cost-subject-list',
  created() {
    this.tableName = '费用科目'
    this.reqUrl = '/business/shipAffairs/costSubject/page'
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.headers = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      // { text: 'SAP费用类型', value: 'sapCostType' },
      // { text: 'MIS代码', value: 'misCode' },
      // { text: 'SAP代码', value: 'sapCode' },
      // { text: '科目名称(费用类型E)', value: 'subjectName2' },
      { text: '费用代码', value: 'sapCodeNew' },
      // { text: 'SAP代码(费用类型E)', value: 'sapCode2' },
      // { text: '科目名称(费用类型F)', value: 'subjectName3' },
      // { text: 'SAP代码(费用类型F)', value: 'sapCode3' },
      // { text: '坞修', value: 'repairFlag' },
      // { text: '是否SAP', value: 'businessAllocate' },
      // { text: '预估船东成本', value: 'budgetSapCode' },
      { text: '付款频次', value: 'payFrequency' },
      // { text: '费用代码（新）', value: 'sapCodeNew' },
    ]
    this.fuzzyLabel = ''
    this.costTypes = [
      { text: 'A厂修', value: 'A' },
      { text: 'B常规维修', value: 'B' },
      { text: 'C船员奖励', value: 'C' },
      { text: 'D船东费用', value: 'D' },
      { text: 'E物料存货', value: 'E' },
      { text: 'F金额调整', value: 'F' },
    ]
    this.flag = [
      { text: '否', value: 0 },
      { text: '是', value: 1 },
    ]
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => !isNaN(v) || '必须为数字',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costSubject/deleteCostSubject',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/costSubject/modifyCostSubject'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
