<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="formData.shipCode"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <!-- <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="科目名称"
                  v-model="formData.subjectId"
                  :rules="[rules.required]"
                  :init-selected="formData.initSelected"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  required
                  dense
                  :search-remain="searchObj"
                ></v-dialog-select> -->
                <v-select
                  label="费用SAP代码"
                  outlined
                  dense
                  v-model="formData.subjectId"
                  :items="[
                    { text: 'SYRF厂修费', value: 'SYRF' },
                    { text: 'INSP检验费', value: 'INSP' },
                    { text: 'DRM坞修物料费', value: 'DRM' },
                    { text: 'DRP坞修油漆费', value: 'DRP' },
                    { text: 'DRSP坞修备件费', value: 'DRSP' },
                    { text: 'ALZI铝块锌块费', value: 'ALZI' },
                    { text: 'OTSR外协费', value: 'OTSR' },
                    { text: '6401031211', value: '6401031211' },
                    { text: '6401031212', value: '6401031212' },
                    { text: '6401031213', value: '6401031213' },
                  ]"
                  :rules="[rules.required]"
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  label="坞修卡片编码"
                  v-model="formData.cardCode"
                  required
                  dense
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  label="内部订单编号"
                  v-model="formData.orderCode"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  label="开始时间"
                  v-model="formData.startDate"
                  :rules="[rules.required]"
                  required
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  label="结束时间"
                  v-model="formData.endDate"
                  :rules="[rules.required]"
                  required
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  outlined
                  label="状态"
                  v-model="formData.status"
                  :rules="[rules.required]"
                  required
                  dense
                  :items="statues"
                ></v-select>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['内部订单号:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
      show-expand
      use-ship
      fuzzy-label="模糊查询"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['内部订单号:新建']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['内部订单号:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ ['无效', '有效'][item.status] }}
      </template>
      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length">
          <costSapMesInner
            :shipCode="item.shipCpde"
            :assetId="item.cardCode"
            :freCde="item.subjectId"
            :item="item"
          />
        </td>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import costSapMesInner from './cost-sap-mes-inner.vue'
// cardCode	坞修卡片编码	string
// endDate	结束时间	string
// id	物理主键	string
// orderCode	内部订单编号	string
// shipCode	船舶编码	string
// shipName	船舶名	string
// startDate	开始时间	string
// status	状态 0 无效 1生效	string
// subjectId	费用科目id 仅A类	string
// subjectName	科目业务分类+科目名称	string
export default {
  components: { costSapMesInner },
  name: 'cost-inner-list',
  created() {
    this.tableName = '坞修卡片映射'
    this.reqUrl = '/business/shipAffairs/costSubject/pageInner'
    this.headers = [
      { text: '', value: 'data-table-expand' },
      { text: '船舶', value: 'shipName' },
      // { text: '业务分类+科目名称', value: 'subjectName' },
      { text: '费用SAP代码', value: 'subjectId' },
      { text: '坞修卡片编码', value: 'cardCode' },
      // { text: '内部订单编号', value: 'orderCode' },
      { text: '开始时间', value: 'startDate' },
      { text: '结束时间', value: 'endDate' },
      { text: '状态', value: 'status' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDate = {
      label: '生效时间',
      interval: true,
    }
    this.statues = [
      { text: '无效', value: '0' },
      { text: '生效', value: '1' },
    ]
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      searchObj: { sapCostType: 'A' },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/costSubject/deleteCostSubjectInner',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected,
        initSelected: {
          subjectName: this.selected.subjectName.split('-')[1],
          id: this.selected.subjectId,
        },
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/costSubject/modifyCostSubjectInner'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.resetValidation()
      this.formData = {}
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
