<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  :disabled="isEdit"
                  v-model="formData.shipCode"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-ship-select>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  label="业务系统订单号"
                  v-model="formData.purchaseOrderNo"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-dialog-select
                  :disabled="!formData.shipCode || isEdit"
                  v-model="formData.purchaseOrderId"
                  :init-selected="initOrder"
                  :headers="orderHeaders"
                  item-value="id"
                  max-width="1300"
                  item-text="orderNo"
                  label="备件订单"
                  req-url="/business/shipAffairs/purchaseManage/purchaseOrderPage"
                  :search-remain="searchObj2"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="订单号"
                        outlined
                        dense
                        clearable
                        v-model="searchObj2.orderNo"
                      ></v-text-field>
                    </v-col>
                  </template>
                  <template v-slot:[`item.isDockRepair`]="{ item }">
                    {{ item.isDockRepair ? '是' : '否' }}
                  </template>
                  <template v-slot:[`item.applyType`]="{ item }">
                    <v-chip small v-if="item.applyType == 1">常规</v-chip>
                    <v-chip small color="warning" v-if="item.applyType == 2">
                      紧急
                    </v-chip>
                    <v-chip small color="error" v-if="item.applyType == 3">
                      坞修
                    </v-chip>
                    <v-chip small color="error" v-if="item.applyType == 4">
                      固定资产
                    </v-chip>
                    <v-chip small color="error" v-if="item.applyType == 5">
                      通导设备固定资产
                    </v-chip>
                    <v-chip small color="error" v-if="item.applyType == 99">
                      邮件采购
                    </v-chip>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dialog-select
                  :disabled="!formData.purchaseOrderId || isEdit"
                  v-model="formData.assetItemId"
                  :init-selected="initAsset"
                  :headers="assetHeaders"
                  item-value="itemId"
                  max-width="1300"
                  item-text="componentName"
                  label="选择资产"
                  req-url="/business/shipAffairs/purchaseManage/purchaseFixedDetailById"
                  :search-remain="{
                    id: formData.purchaseOrderId,
                  }"
                >
                  <template #searchflieds></template>
                  <template v-slot:[`item.typess`]="{ item }">
                    {{
                      item.typess == 0
                        ? '普通备件'
                        : item.typess == 1
                        ? 'SAP备件'
                        : item.typess == 2
                        ? '固定资产'
                        : item.typess == 3
                        ? '通导固定资产'
                        : ''
                    }}
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  label="SAP资产编号"
                  v-model="formData.assetCode"
                  required
                  dense
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['内部订单号:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <!-- @dbclick="editItem" -->
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      fuzzy-label="模糊查询"
      @dbclick="editItem"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['内部订单号:新建']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['内部订单号:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ ['无效', '有效'][item.status] }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// import engineSelect from '@/views/maritime-maintence/spare-part/apply/private/engine-select.vue'
// cardCode	坞修卡片编码	string
// endDate	结束时间	string
// id	物理主键	string
// orderCode	内部订单编号	string
// shipCode	船舶编码	string
// shipName	船舶名	string
// startDate	开始时间	string
// status	状态 0 无效 1生效	string
// subjectId	费用科目id 仅A类	string
// subjectName	科目业务分类+科目名称	string
export default {
  components: {},
  name: 'cost-inner-list',
  created() {
    this.tableName = 'SAP资产清单'
    this.reqUrl = '/costAssetSap/pageAsset'
    this.headers = [
      { text: '船舶', value: 'shipName' },
      // { text: '业务分类+科目名称', value: 'subjectName' },
      { text: '资产名称', value: 'assetItemName' },
      { text: '费用类型/名称/代码', value: 'subjectName' },
      { text: 'SAP资产编号', value: 'assetCode' },
      { text: '采购订单号', value: 'purchaseOrderNo' },
      // { text: '开始时间', value: 'startDate' },
      // { text: '结束时间', value: 'endDate' },
      // { text: '状态', value: 'status' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDate = {
      label: '生效时间',
      interval: true,
    }
    this.statues = [
      { text: '无效', value: '0' },
      { text: '生效', value: '1' },
    ]
    this.orderHeaders = [
      { text: '船舶', value: 'shipInfo' },
      { text: '订单号', value: 'orderNo' },
      { text: '申请类型', value: 'applyType' },
      { text: '申请单号', value: 'applyNo' },
      { text: '创建日期', value: 'createTime' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '入库完成日期', value: 'completeTime' },
      { text: '是否坞修', value: 'isDockRepair' },
    ]
    this.assetHeaders = [
      { text: '资产名称', value: 'componentName' },
      { text: '备件号', value: 'componentNumber' },
      { text: '备件属性', value: 'typess' },
      { text: '子设备英文名称', value: 'subEquipmentCname' },
      { text: '子设备中文名称', value: 'subEquipmentCname' },
    ]
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      searchObj: { sapCostType: 'A' },
      initEngine: {},
      items: [],
      dialog: false,
      searchObj2: {
        // shipCode: this.formData.shipCode,
        orderType: '01',
        // isFixed: true,
      },
      initOrder: {},
      initAsset: {},
    }
  },
  watch: {
    'formData.shipCode'(val) {
      if (val) {
        this.searchObj2.shipCode = val
        // this.loadDepository()
      }
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/costAssetSap/deleteCostAssetSap',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {}
      this.initOrder = {}
      this.initAsset = {}
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected,
      }
      ;(this.initAsset = {
        itemId: this.selected.assetItemId,
        componentName: this.selected.assetItemName,
      }),
        (this.initOrder = {
          id: this.selected.purchaseOrderId,
          orderNo: this.selected.purchaseOrderNo,
        }),
        (this.formShow = true)
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/costAssetSap/modifyCostAssetSap'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.resetValidation()
      this.formData = {}
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
