<template>
  <v-container fluid>
    <v-detail-view-invoice
      v-permission="['航修修理单生成项目:编辑']"
      title="航修订单发票审批"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        detail.status == 10 ||
        (detail.status == 11 &&
          (!detail.auditParams || detail.auditParams.taskId)) ||
        detail.status == 12 ||
        detail.status == undefined
      "
      :can-save="
        detail.status == 10 || detail.status == 12 || detail.status == undefined
      "
      :can-save-file="isEdit && detail.status !== 99"
      :can-confirm="detail.status == 99 && isApplyPerson"
      :detail="detail"
      @save="save"
      @submit="submit"
    >
      <template #custombtns>
        <!-- <v-btn
          v-if="detail.status == undefined"
          width="60"
          tile
          @click="clear"
          color="success"
          small
          class="mx-1"
          v-permission="['航修修理单生成项目:清空重录']"
        >
          清空重录
        </v-btn> -->
      </template>
      <template
        v-if="detail.auditParams && detail.auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #发票信息>
        <v-form ref="form" :readonly="isEdit">
          <v-container fluid>
            <v-row>
              <!-- <v-col md="3" cols="12">
                <v-ship-select
                  :disabled="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col> -->
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/voyageRepair/orderPageFinance"
                  label="航修修理单"
                  v-model="detail.orderId"
                  :rules="[rules.required]"
                  item-text="orderNo"
                  item-value="id"
                  :readonly="detail.orderId != ''"
                  :init-selected="initOrder"
                  :headers="orderHeaders"
                  :search-remain="searchObj"
                  @select="loadOrderInfo"
                  required
                  dense
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="修理单号"
                        outlined
                        dense
                        v-model="searchObj.fuzzyParam"
                      ></v-text-field>
                    </v-col>
                  </template>
                  <template v-slot:[`item.isDockRepair`]="{ item }">
                    {{ item.isDockRepair ? '是' : '否' }}
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  dense
                  v-model="detail.invoiceCode"
                  label="发票编号"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  v-model="detail.ccyId"
                  :readonly="isEdit"
                  label="发票币种"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field> -->
                <v-autocomplete
                  label="发票币种"
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  v-model="detail.ccyId"
                  dense
                  outlined
                  item-text="ccyCode"
                  item-value="id"
                  :rules="[rules.required]"
                  :items="currencyInfo"
                ></v-autocomplete>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  :readonly="isEdit && isEditFinc"
                  v-model="detail.moneyPay"
                  label="发票金额"
                  :disabled="!detail.ccyId || !isEditFinc"
                  type="number"
                  outlined
                  dense
                  :rules="detail.ccyId == 2 ? [rules.int] : [rules.decimal]"
                ></v-text-field>
                <!-- <vue-numeric
                  style="display: none"
                  label="发票金额"
                  single-line
                  outlined
                  dense
                  v-model="detail.moneyPay"
                  :precision="detail.ccyId == 2 ? 0 : 2"
                ></vue-numeric> -->
                <!-- <v-text-field
                  type="number"
                  outlined
                  dense
                  v-model="detail.moneyPay"
                  label="发票金额"
                  :rules="[rules.required]"
                ></v-text-field> -->
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #结算信息>
        <v-form ref="formPay">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="供应商"
                  dense
                  outlined
                  v-model="detail.supplyName"
                  readonly
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-if="!isEdit || !isEditFinc"
                  outlined
                  dense
                  label="开户银行"
                  v-model="detail.openBank"
                  :rules="[rules.required]"
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  :items="banks"
                  item-text="bank"
                  return-object
                  @change="
                    () => {
                      detail.accountBank = detail.openBank.account
                      detail.openBank = detail.openBank.bank
                    }
                  "
                ></v-select>
                <v-text-field
                  v-else
                  outlined
                  dense
                  label="开户银行"
                  v-model="detail.openBank"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="银行账户"
                  v-model="detail.accountBank"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #订单信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.applyPersonName"
                  label="实际申请人"
                  readonly="true"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.applyDeptName"
                  label="申请部门"
                  readonly="true"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <!--              <v-col md="3" cols="12">-->
              <!--                <v-handler-->
              <!--                  :disabled="isEdit"-->
              <!--                  label="实际申请人"-->
              <!--                  :use-current="false"-->
              <!--                  v-model="detail.applyPerson"-->
              <!--                  :rules="[rules.required]"-->
              <!--                  @selectUser="selectUser"-->
              <!--                ></v-handler>-->
              <!--              </v-col>-->
              <!--              <v-col md="3" cols="12">-->
              <!--                <v-text-field-->
              <!--                  outlined-->
              <!--                  dense-->
              <!--                  v-model="detail.applyDeptName"-->
              <!--                  label="申请部门"-->
              <!--                  disabled-->
              <!--                  :rules="[rules.required]"-->
              <!--                ></v-text-field>-->
              <!--              </v-col>-->
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.shipName"
                  label="船舶"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <!-- <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.invoiceCode"
                  label="发票编号"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :readonly="isEdit"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col> -->
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  use-today
                  v-model="detail.addDate"
                  label="录单日期"
                  :rules="[rules.required]"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <!-- <v-handler
                  outlined
                  dense
                  v-model="detail.addPerson"
                  label="录单人"
                  disabled
                  :rules="[rules.required]"
                  use-current
                ></v-handler> -->
                <v-dialog-select
                  :disabled="true"
                  label="录单人"
                  item-text="nickName"
                  v-model="detail.addPerson"
                  :headers="userHeaders"
                  :rules="[rules.required]"
                  req-url="/system/user/page"
                  :init-selected="detail.initAddPerson"
                ></v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.createPlace"
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  label="创建地点"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.occurDate"
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  label="发生日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.ccyName"
                  :readonly="isEdit"
                  label="币种"
                  :rules="[rules.required]"
                  outlined
                  dense
                  disabled
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  type="number"
                  outlined
                  dense
                  v-model="detail.moneyOrder"
                  disabled
                  label="金额"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="折算美金"
                  v-model="detail.usdMoneyOrder"
                  disabled
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  label="供应商"
                  dense
                  outlined
                  v-model="detail.supplyName"
                  readonly
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col> -->
              <v-col cols="12">
                <v-textarea
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  label="备注"
                  dense
                  outlined
                  v-model="detail.remark"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
        <v-card-text>
          <v-attach-list
            title="发票附件（订单带出）"
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-card-text>
        <v-card-text>
          <v-attach-list
            title="完工单附件（完工单带出）"
            :attachments="detail.attachmentRecords3"
            @change="changeAttachment3"
          ></v-attach-list>
        </v-card-text>
        <v-table-list :headers="subjectHeaders" :items="非调整科目">
          <template v-slot:[`item.money`]="{ item }">
            <vue-numeric
              v-model="item.money"
              :readonly="isEdit && isEditFinc"
              :disabled="!isEditFinc"
              :precision="isJPY ? 0 : 2"
              :max="item.maxMoney"
              style="border: 1px solid black"
              @blur="checkMaxValue($event, item.maxMoney)"
              dense
            ></vue-numeric>
          </template>
          <template v-slot:[`item.usdMoney`]="{ item }">
            {{ (item.money * detail.rate).toFixed(2) }}
          </template>
          <template v-slot:[`item.adjustMoney`]="{ item }">
            {{ (item.money - item.originMoney).toFixed(2) }}
          </template>
          <template v-slot:[`item.usdOriginMoney`]="{ item }">
            {{ (item.originMoney * detail.rate).toFixed(2) }}
          </template>
          <template v-slot:[`item.usdAdjustMoney`]="{ item }">
            {{ ((item.money - item.originMoney) * detail.rate).toFixed(2) }}
          </template>
        </v-table-list>
        <v-card-text>
          <v-row>
            <!-- <v-col cols="12" md="3">
              <v-text-field
                v-model="detail.ccyName"
                :readonly="isEdit"
                label="币种"
                :rules="[rules.required]"
                outlined
                dense
                disabled
              ></v-text-field>
            </v-col> -->
            <v-col md="3" cols="12">
              <v-text-field
                type="number"
                outlined
                dense
                v-model="detail.money"
                label="合计付款金额"
                disabled
                :rules="[rules.required]"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                outlined
                dense
                label="折算美金"
                v-model="detail.usdMoney"
                disabled
              ></v-text-field>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
    </v-detail-view-invoice>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import computeMixin from './private/computeMixin'
import VueNumeric from 'vue-numeric'

export default {
  components: {
    VueNumeric,
  },
  name: 'cost-gen-voyage',
  props: {
    // 订单id
    orderId: {
      type: String,
    },
    // 发票id
    costOrderId: {
      type: String,
    },
    historyFlag: {
      type: Boolean,
    },
  },
  created() {
    this.backRouteName = 'cost-voucher-list'
    this.subtitles = ['发票信息', '结算信息', '订单信息']
    this.orderHeaders = [
      { text: '修理单号', value: 'orderNo' },
      { text: '供应商', value: 'supplierName' },
      { text: '修理费用', value: 'reallyRepairExpense' },
      { text: '其他费用', value: 'reallyOtherMoney' },
      // { text: '申请部门', value: 'dept' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.subjectHeaders = [
      { text: '费用科目', value: 'subjectName' },
      { text: '原币金额', value: 'money' },
      { text: '付款折算', value: 'usdMoney' },
      { text: '原始金额', value: 'originMoney' },
      { text: '原始折算', value: 'usdOriginMoney' },
      { text: '调整金额', value: 'adjustMoney' },
      { text: '调整折算', value: 'usdAdjustMoney' },
    ]
    this.headers = [
      { text: '备件名', value: 'componentName' },
      { text: '备件号', value: 'componentNumber' },
      { text: '子设备中文名称', value: 'subEquipmentCname' },
      { text: '订购数量', value: 'purchaseNum' },
      { text: '单价', value: 'unitPrice' },
      { text: '折扣', value: 'discount' },
      { text: '折后单价', value: 'discount' },
      { text: '入库数量', value: 'warehouseNum' },
    ]
  },
  mixins: [currencyHelper, computeMixin],
  data() {
    return {
      detail: {
        orderId: '',
        ccyName: '',
        ccyId: '',
        money: 0,
        usdMoney: 0,
        rate: 1,
        invoiceCodeOld: '',
        initAddPerson: {
          nickName: this.$local.data.get('userInfo').nickName,
          id: this.$local.data.get('userInfo').id,
        },
      },
      projects: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
        decimal4: (v) =>
          /^\d+(\.\d{1,4})?$/.test(v) || '必须为整数或最多四位小数',
      },
      searchObj: { shipCode: '', businessStatus: '全部完工', status: '3' },
      // detailList: [],
      banks: [],
      bankCcId: '',
      purchaseCompany: '',
      initOrder: {},
      flag: false,
      userId: this.$local.data.get('userInfo').id,
    }
  },
  watch: {
    costOrderId(val) {
      if (val) {
        console.log(val)
        this.loadDetail()
      }
    },
    'detai.orderId'(val) {
      this.getOrderInfo(val)
    },
    'detail.occurDate': {
      async handler(val) {
        if (val && this.detail.supplyId && this.detail.shipCode) {
          const { data } = await this.postAsync(
            '/business/shipAffairs/costProject/getPayDate',
            {
              happenDate: val,
              shipCode: this.detail.shipCode,
              supplyId: this.detail.supplyId,
            },
          )
          // this.detail.payDate = data
          // this.detail.happenDate = val
          this.$set(this.detail, 'payDate', data)
          this.$set(this.detail, 'happenDate', val)
        }
      },
    },
    detail: {
      handler(val) {
        if (val.occurDate && val.supplyId && val.shipCode) {
          // this.loadHappenDate(val)
        }
      },
      deep: true,
    },
    'detail.shipCode'(val) {
      if (val) {
        this.searchObj.shipCode = val
      }
    },
    非调整科目: {
      handler(val) {
        this.detail.money = val
          .reduce((acc, cur) => Number(acc) + Number(cur.money), 0)
          .toFixed(2)
        this.detail.usdMoney = (
          Math.round(this.detail.money * (this.detail.rate || 0) * 100) / 100
        ).toFixed(2)
      },
      deep: true,
    },
  },

  computed: {
    isEdit() {
      return (
        this.$route.params.id !== undefined &&
        this.detail.status != 10 &&
        this.detail.status != 12 &&
        this.detail.status != undefined
      )
    },
    isEditFinc() {
      return (
        (this.detail.status == 10 ||
          this.detail.status == 12 ||
          this.detail.status == undefined) &&
        this.detail.initAddPerson.id == this.$local.data.get('userInfo').id
      )
    },
    rate() {
      return this.currencyInfo.find((i) => i.id === this.detail?.currencyId)
        ?.rateToMain
      // return this.currencyInfo.find((i) => i.ccyCode === this.detail?.ccyName)
      //   ?.rateToMain
    },
    isJPY() {
      return this.detail.ccyName === 'JPY' || this.detail.ccyName === '日元'
    },
    isApplyPerson() {
      return this.userId == this.detail.applyPerson
    },
  },

  methods: {
    checkMaxValue(event, maxValue) {
      let inputValue = event.target.value
      if (inputValue > maxValue) {
        this.$dialog.message.error('输入的值超过了最大限制,已变更为最大值')
      }
    },
    async save(goBack, notMove = false) {
      if (
        !this.detail.orderId ||
        this.detail.orderId.trim() === '' ||
        this.detail.orderId === undefined
      ) {
        this.$dialog.message.error('请选择预算')
        return
      }
      if (
        !this.detail.invoiceCode ||
        this.detail.invoiceCode.trim() === '' ||
        this.detail.invoiceCode === undefined
      ) {
        this.$dialog.message.error('请填写发票号')
        return
      }
      if (
        !this.detail.invoiceDate ||
        this.detail.invoiceDate.trim() === '' ||
        this.detail.invoiceDate === undefined
      ) {
        this.$dialog.message.error('请填写发票日期')
        return
      }
      if (Number(this.detail.moneyPay) != Number(this.detail.money)) {
        this.$dialog.message.error('发票金额与合计付款金额不一致，保存失败！')
        return
      }
      if (this.detail.ccyId != this.detail.currencyId) {
        this.$dialog.message.error('发票币种与订单币种不一致，保存失败！')
        return
      }
      if (
        // this.detail.invoiceCodeOld != '' &&
        this.detail.invoiceCodeOld != this.detail.invoiceCode
      ) {
        const { data } = await this.getAsync(
          '/business/shipAffairs/costProject/list',
          {
            shipCode: this.detail.shipCode,
            supplyId: this.detail.supplyId,
            // subjectId: this.detail.subjectId,
            subjectId: this.getProjects()[0].subjectId,
            invoiceCode: this.detail.invoiceCode,
            // money: this.detail.money,
            // happenDate: this.detail.happenDate,
          },
        )
        if (data.length > 0) {
          this.$dialog.message.error('发票编号重复！')
          return
        }
        console.log(data)
      }
      // const { data } = await this.getAsync(
      //   '/business/shipAffairs/costProject/list',
      //   {
      //     shipCode: this.detail.shipCode,
      //     supplyId: this.detail.supplyId,
      //     // subjectId: this.detail.subjectId,
      //     subjectId: this.getProjects()[0].subjectId,
      //     invoiceCode: this.detail.invoiceCode,
      //     // money: this.detail.money,
      //     // happenDate: this.detail.happenDate,
      //   },
      // )
      // if (data.length > 0) {
      //   this.$dialog.message.error('发票编号重复！')
      //   return
      // }
      // console.log(data)
      if (!this.$refs.form.validate()) return
      // 保存订单详情信息
      const flag = await this.saveDetails()
      console.log('11111111111111:' + flag)
      if (flag) {
        const projects = this.getProjects()
        for (const item of projects) {
          item.remark = this.detail.remark
          this.detail.businessModule = item.businessModule
        }
        // const { errorRaw } = await this.postAsync(
        //   '/business/shipAffairs/businessCostController/saveOrUpdateOrderProjectOfVoyageRepairBudget',
        //   projects,
        // )
        // if (errorRaw) return
        // this.$dialog.message.success('生成成功')
        // this.clear()
        this.detail.earliestPayDate = this.detail.payDate
        const { errorRaw, data } = await this.postAsync(
          '/business/shipAffairs/businessCostController/modifyOrderSubjectOfPurchaseBudgetOrder',
          { ...this.detail, costProjectPriceModifyDTOs: projects },
        )
        if (errorRaw) return
        // this.$dialog.message.success('操作成功')
        // this.clear()
        if (notMove) return data
        if (this.costOrderId == undefined) {
          this.$dialog.message.success('操作成功')
          this.clear()
        } else {
          goBack()
        }
      }
    },
    async submit(goBack) {
      console.log('----------------------------------')
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      // 未提交和审批失败状态重新提交到待实际申请人确认状态
      if (
        this.detail.status == 10 ||
        this.detail.status == 12 ||
        this.detail.status == undefined
      ) {
        //  if (Number(this.detail.moneyPay) != Number(this.detail.money)) {
        // 发票金额与合计付款金额不一致
        if (Number(this.detail.moneyPay) != Number(this.detail.moneyOrder)) {
          console.log('发票金额与订单金额不一致')
          this.detail.id = data
          this.detail.status = 99
          this.detail.businessStatus = '待实际申请人确认'
          // const idList = this.projects.map((item) => item.id)
          const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
          const { errorRaw } = await this.postAsync(reqUrl, {
            ...this.detail,
            // idList,
          })
          if (errorRaw) return false
          this.flag = true
          if (this.costOrderId == undefined) {
            this.$dialog.message.success('操作成功')
            this.clear()
          } else {
            goBack()
          }
        } else {
          console.log('发票金额与订单金额一致')
          this.detail.id = data
          this.detail.status = 13
          this.detail.businessStatus = '审批通过'
          // const idList = this.projects.map((item) => item.id)
          const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
          const { errorRaw } = await this.postAsync(reqUrl, {
            ...this.detail,
            // idList,
          })
          if (errorRaw) return false
          if (this.detail.status == 13) {
            const ids = [this.detail.id]
            const { errorRaw } = await this.postAsync(
              '/business/shipAffairs/costOrder/createSapNewList',
              ids,
            )
            if (!errorRaw)
              if (this.costOrderId == undefined) {
                this.$dialog.message.success('操作成功')
                this.clear()
              } else {
                goBack()
              }
          }
          this.flag = true
          if (this.costOrderId == undefined) {
            this.$dialog.message.success('操作成功')
            this.clear()
          } else {
            goBack()
          }
        }
      }
      if (!this.flag) {
        if (
          !this.detail.auditParams &&
          (this.detail.status == 99 || this.detail.status == 10)
        ) {
          this.quoteLoading = true
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/costOrder/process/start',
            { id: data },
          )
          this.quoteLoading = false
          if (!errorRaw)
            if (this.costOrderId == undefined) {
              this.$dialog.message.success('操作成功')
              this.clear()
            } else {
              goBack()
            }
        } else {
          this.quoteLoading = true
          const error = await this.$refs.audit.submit()
          this.quoteLoading = false
          if (!error) {
            const { data } = await this.getAsync(
              `/business/shipAffairs/costOrder/getById/${this.$route.params.id}`,
            )
            if (data.status == 13) {
              const ids = [this.$route.params.id]
              const { errorRaw } = await this.postAsync(
                '/business/shipAffairs/costOrder/createSapNewList',
                ids,
              )
              if (!errorRaw)
                if (this.costOrderId == undefined) {
                  this.$dialog.message.success('操作成功')
                  this.clear()
                } else {
                  goBack()
                }
            } else {
              if (this.costOrderId == undefined) {
                this.$dialog.message.success('操作成功')
                this.clear()
              } else {
                goBack()
              }
            }
          }
        }
      }
    },
    async startProcess() {
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/costOrder/process/start',
        { id: this.detail.id },
      )
      this.quoteLoading = false
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
    async saveDetails() {
      const { data, errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/modifyVoyageOrderFinalMoney',
        { id: this.detail.orderId, finalMoney: this.detail.money },
      )
      if (errorRaw) {
        return false
      }
      if (data) {
        //不超预算 可以提交
        return true
      }
    },
    clear() {
      this.$parent.$parent.$parent.refresh(null, this.$options.name)
      return
    },

    async getOrderInfo(id) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/businessCostController/getOrderSubjectOfVoyageRepair',
        { orderId: id },
      )
      this.projects = data.map((i, index) => ({
        ...i,
        vid: index + 1,
        originMoney: i.money,
        maxMoney: i.money,
        subjectName: i.subjectName || '未知科目-调整',
      }))
      this.detail.occurDate = data?.[0]?.happenDate
      this.detail.happenDate = data?.[0]?.happenDate
      this.detail.payDate = data?.[0]?.payDate
      this.bankCcId = data?.[0]?.ccyId
      this.purchaseCompany = data?.[0]?.purchaseCompany
    },

    async loadOrderInfo(order) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/getOrderDetailById',
        {
          id: order.id,
        },
      )
      await this.getOrderInfo(data.id)
      this.detail.remark = data.remark
      this.detail.ccyName = data.ccyCode
      this.detail.shipCode = data.shipInfo.shipCode
      this.detail.shipName = data.shipInfo.chShipName
      // this.detail.ccyId = data.currencyId
      this.detail.currencyId = data.currencyId
      this.detail.money = (
        data.reallyRepairExpense + data.reallyOtherMoney
      ).toFixed(2)
      // this.detail.usdMoney = data.reallyFinalMoney
      this.detail.usdMoney = (
        (data.reallyRepairExpense + data.reallyOtherMoney) *
        data.rateToMain
      ).toFixed(2)
      this.detail.moneyOrder = this.detail.money
      this.detail.usdMoneyOrder = this.detail.usdMoney
      this.detail.supplyName = data.supplierName
      this.detail.applyPerson = data.managerId
      this.detail.applyDept = data.managerDeptId
      this.detail.applyPersonName = data.managerName
      this.detail.applyDeptName = data.managerDept
      this.detail.supplyId = data.supplierId
      this.detail.invoiceCode = data.invoiceCode
      this.detail.invoiceDate = data.invoiceDate
      // 发票
      this.detail.attachmentRecords = data.attachmentRecords4
      // data.attachmentRecords4.forEach((item) => {
      //   this.detail.attachmentRecords.push(item)
      // })
      this.detail.attachmentIds = data.attachmentIds4
      // 完工单
      this.detail.attachmentRecords3 = data.attachmentRecords3
      this.detail.attachmentIds2 = data.attachmentIds3

      this.detail.rate = data.rateToMain
      this.detail.initAddPerson = {
        nickName: this.$local.data.get('userInfo').nickName,
        id: this.$local.data.get('userInfo').id,
      }
      this.loadBank()
    },

    async loadHappenDate(val) {
      const { data } = await this.postAsync(
        '/business/shipAffairs/costProject/getPayDate',
        {
          happenDate: val.occurDate,
          shipCode: val.shipCode,
          supplyId: val.supplyId,
        },
      )
      this.detail.payDate = data
    },
    async loadBank() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/Supplier/getBankMessageByShipCodeAndType',
        {
          purchaseCompany: this.purchaseCompany,
          supplierId: this.detail.supplyId,
          currencyId: this.bankCcId,
        },
      )
      if (data.length === 0) {
        this.$dialog.message.error('供应商未维护银行信息')
        return
      }
      data.forEach((item) => {
        item.bank = item.bank + ':' + item.account
      })
      this.banks = data
      // console.log(this.banks)
      if (data.length === 1) {
        this.detail.accountBank = data[0].account
        this.detail.openBank = data[0].bank
      }
      if (data.length > 1) {
        this.$dialog.message.info('供应商维护了多个银行信息,请选择')
      }
    },
    // 发票附件
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    // 签收单附件
    changeAttachment3(attachmentIds) {
      this.detail.attachmentIds2 = attachmentIds
    },
    async loadDetail() {
      if (!this.historyFlag) return
      if (this.costOrderId == undefined) return
      console.log(2222222222222222222222222)
      // if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/shipAffairs/costOrder/getByIdNew/${this.costOrderId}`,
      )
      console.log(data)
      this.detail = data
      this.detail.supplyId = data.supplyId
      this.detail.shipCode = data.shipCode
      this.detail.attachmentRecords3 = data.attachmentRecords2
      this.detail.attachmentIds2 = data.attachmentIds2
      // this.detail.payDate = data.earliestPayDate
      this.$set(this.detail, 'payDate', data.earliestPayDate)
      this.purchaseCompany = data.payCompany
      this.bankCcId = data.ccyId
      this.loadBank()
      this.detail.initApply = {
        nickName: data.applyPersonName,
        id: data.applyPerson,
      }
      this.detail.addDate = data.orderDate
      this.detail.initAddPerson = {
        nickName: data.applyUserName,
        id: data.applyUser,
      }
      this.shipList = [
        {
          text: data.shipName,
          value: data.shipCode,
        },
      ]
      // this.detail.occurDate = data.happenDate
      this.$set(this.detail, 'occurDate', data.happenDate)
      this.detail.invoiceCodeOld = data.invoiceCode
      this.detail.remark = data.remark
      // this.detail.createPlace = data.detailList[0].createPlace
      // this.projects = []
      // this.projects = data.list.map((i, index) => ({
      //   ...i,
      //   vid: index + 1,
      //   originMoney: i.subjectName.includes('运费')
      //     ? // ['备件运费采购', '滑油运费采购'].includes(i.subjectName)
      //       0
      //     : i.money,
      //   maxMoney: i.money,
      // }))
      data.list.forEach((item, index) => {
        const newItem = {
          ...item,
          vid: index + 1,
          // originMoney: item.subjectName.includes('运费') ? 0 : item.originMoney,
          // maxMoney: item.money,
          maxMoney: item.originMoney,
        }
        this.projects.push(newItem)
        this.$set(this.projects, index, newItem) // 按索引设置元素:ml-citation{ref="7,8" data="citationList"}
      })
      console.log(this.projects)

      this.detail.currencyId = data.ccyId
      // this.rate = this.currencyInfo.find(
      //   (i) => i.id === this.detail?.currencyId,
      // )?.rateToMain
      this.detail.rate = this.rate
      // this.detail.money = data.moneyPay
      this.$set(this.detail, 'money', data.moneyPay)
      // this.detail.usdMoney = (Math.round(data.moneyPay * 100) / 100).toFixed(2)
      this.$set(
        this.detail,
        'usdMoney',
        (Math.round(data.moneyPay * 100) / 100).toFixed(2),
      )
      this.$set(this.detail, 'moneyOrder', data.moneyOrder)
      this.$set(this.detail, 'usdMoneyOrder', data.usdMoneyOrder)
      // this.detail.moneyOrder = this.非调整科目
      //   .reduce((acc, cur) => acc + cur.money, 0)
      //   .toFixed(2)
      // this.detail.usdMoneyOrder = (
      //   Math.round(this.detail.moneyOrder * this.rate * 100) / 100
      // ).toFixed(2)
      // this.非调整科目 = []
      // this.非调整科目 = this.projects.filter(
      //   (i) => !i.subjectName.includes('调整'),
      // )
      this.$set(
        this, // 目标对象（当前 Vue 实例）
        '非调整科目', // 属性名
        this.projects.filter((i) => !i.subjectName.includes('调整')), // 过滤后的数组
      )

      // this.detailList = data.detailList
      // this.detailList.forEach((item) => {
      //   item.payPrice = item.unitPrice
      //   item.payTotalPrice = this.calculateDifference(
      //     item.payPrice * item.warehouseNum,
      //   )
      //   item.payTotalPriceMax = Number(
      //     this.calculateDifference(item.payPrice * item.warehouseNum),
      //   )
      // })
      this.initOrder = { id: data.orderId, orderNo: data.orderNo }
      // 加载页面数据  增加审批 增加保存附件操作
      // console.log(data.id)
      // this.detail = data
      // this.projects = data.list
    },
    async loadPayDate() {
      console.log('loadPayDateloadPayDateloadPayDateloadPayDateloadPayDate')
      if (
        this.detail.occurDate &&
        this.detail.supplyId &&
        this.detail.shipCode
      ) {
        const { data } = await this.postAsync(
          '/business/shipAffairs/costProject/getPayDate',
          {
            happenDate: this.detail.occurDate,
            shipCode: this.detail.shipCode,
            supplyId: this.detail.supplyId,
          },
        )
        // this.detail.payDate = data
        // this.detail.happenDate = this.detail.occurDate
        this.$set(this.detail, 'payDate', data)
        this.$set(this.detail, 'happenDate', this.detail.occurDate)
      }
    },

    // async selectUser(user) {
    //   this.detail.applyDeptName = user.deptName
    // },
  },

  mounted() {
    console.log('this.$route.params.id:', this.$route.params.id)
  },
}
</script>

<style>
/* 隐藏所有数字输入框的增减按钮 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
/* Firefox 兼容 */
/* input[type='number'] {
  -moz-appearance: textfield;
} */
</style>
