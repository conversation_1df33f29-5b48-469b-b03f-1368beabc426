<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['自修修理单生成项目:编辑']"
      title="自修修理单生成项目"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #custombtns>
        <v-btn
          width="60"
          tile
          @click="clear"
          color="success"
          small
          class="mx-1"
          v-permission="['自修修理单生成项目:清空重录']"
        >
          清空重录
        </v-btn>
      </template>
      <template #基本信息>
        <v-form ref="form" :readonly="isEdit">
          <v-container fluid>
            <v-row>
              <v-col md="3" cols="12">
                <v-ship-select
                  :disabled="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  :disabled="!detail.shipCode"
                  req-url="/business/shipAffairs/repairBonus/page"
                  label="自修分配单"
                  v-model="detail.businessId"
                  :rules="[rules.required]"
                  item-text="applyCode"
                  item-value="id"
                  :headers="orderHeaders"
                  :readonly="isEdit"
                  :search-remain="searchObj"
                  @select="loadOrderInfo"
                  required
                  dense
                >
                  <template v-slot:[`item.isDockRepair`]="{ item }">
                    {{ item.isDockRepair ? '是' : '否' }}
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.invoiceCode"
                  label="发票编号"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :readonly="isEdit"
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <v-handler
                  @selectUser="selectUser"
                  :disabled="isEdit"
                  label="实际申请人"
                  :use-current="false"
                  v-model="detail.applicantId"
                ></v-handler>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.applyDeptName"
                  label="申请部门"
                  disabled
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  use-today
                  v-model="detail.addDate"
                  label="录单日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <v-handler
                  outlined
                  dense
                  v-model="detail.addPerson"
                  label="录单人"
                  disabled
                  :rules="[rules.required]"
                  use-current
                ></v-handler>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.createPlace"
                  :readonly="isEdit"
                  label="创建地点"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.happenDate"
                  :readonly="isEdit"
                  label="发生日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.ccyName"
                  :readonly="isEdit"
                  label="币种"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  type="number"
                  outlined
                  dense
                  v-model="detail.money"
                  disabled
                  label="金额"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="折算美金"
                  v-model="detail.usdMoney"
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="供应商"
                  dense
                  outlined
                  v-model="detail.supplyName"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="备注"
                  v-model="detail.remark"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'cost-gen-self',
  created() {
    this.backRouteName = 'cost-project-list'
    this.subtitles = ['基本信息']
    this.orderHeaders = [
      { text: '申请单号', value: 'applyCode' },
      { text: '自修奖申请时间', value: 'applyTime' },
      { text: '部门', value: 'department' },
      { text: '船舶', value: 'shipName' },
      { text: '状态', value: 'status' },
    ]
    this.subjectHeaders = [
      { text: '费用科目', value: 'subjectName' },
      { text: '原币金额', value: 'money' },
      { text: '折算美元', value: 'usdMoney' },
    ]
    this.headers = [
      { text: '备件名', value: 'componentName' },
      { text: '备件号', value: 'componentNumber' },
      { text: '子设备中文名称', value: 'subEquipmentCname' },
      { text: '订购数量', value: 'purchaseNum' },
      { text: '单价', value: 'unitPrice' },
      { text: '折扣', value: 'discount' },
      { text: '折后单价', value: 'discount' },
      { text: '入库数量', value: 'warehouseNum' },
    ]
  },
  data() {
    return {
      detail: { businessId: '', ccyName: '' },
      projects: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchObj: { shipCode: '', status: 'CHECK ' },
      detailList: [],
    }
  },
  watch: {
    'detai.businessId'(val) {
      this.getOrderInfo(val)
    },
    'detail.shipCode'(val) {
      if (val) {
        this.searchObj.shipCode = val
      }
    },
  },

  computed: {
    isEdit() {
      return false
    },
  },

  methods: {
    async save() {
      if (!this.$refs.form.validate()) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/repairBonus/createProject',
        { ...this.detail },
      )
      if (errorRaw) return
      this.$dialog.message.success('生成成功')
      this.clear()
    },

    clear() {
      this.$parent.$parent.$parent.refresh(null, this.$options.name)
      return
    },

    async getOrderInfo(id) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/businessCostController/getOrderSubjectOfVoyageRepair',
        { orderId: id },
      )
      this.projects = data
    },

    async loadOrderInfo(order) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/repairBonus/getRepairBonusProject',
        {
          id: order.id,
        },
      )
      delete data.businessId
      Object.assign(this.detail, data)
    },

    async loadHappenDate(val) {
      const { data } = await this.postAsync(
        '/business/shipAffairs/costProject/getPayDate',
        {
          happenDate: val.occurDate,
          shipCode: val.shipCode,
          supplyId: val.supplyId,
        },
      )
      this.detail.payDate = data
    },

    async selectUser(user) {
      this.detail.applyDeptName = user.deptName
    },
  },

  mounted() {},
}
</script>

<style></style>
