<template>
  <v-container fluid>
    <v-detail-view-invoice
      v-permission="['预算生成项目:编辑']"
      title="预算发票审批"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        detail.status == 10 ||
        (detail.status == 11 &&
          (!detail.auditParams || detail.auditParams.taskId)) ||
        detail.status == 12 ||
        detail.status == undefined
      "
      :can-save="
        detail.status == 10 || detail.status == 12 || detail.status == undefined
      "
      :can-save-file="isEdit && detail.status !== 99"
      :can-confirm="detail.status == 99 && isApplyPerson"
      :detail="detail"
      @save="save"
      @submit="submit"
    >
      <template #custombtns>
        <!-- <v-btn
          v-if="detail.status == undefined"
          width="60"
          tile
          @click="clear"
          color="success"
          small
          class="mx-1"
          v-permission="['预算生成项目:清空重录']"
        >
          清空重录
        </v-btn> -->
      </template>
      <template
        v-if="detail.auditParams && detail.auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #发票信息>
        <v-form ref="form" :readonly="isEdit">
          <v-container fluid>
            <v-row>
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/supplyCommon/page22"
                  label="选择预算"
                  v-model="detail.orderId"
                  :rules="[rules.required]"
                  item-text="orderNo"
                  item-value="id"
                  :headers="orderHeaders"
                  :readonly="detail.orderId != ''"
                  :search-remain="searchObj2"
                  :init-selected="initOrder"
                  @select="loadBudgetInfo"
                  required
                  dense
                  fuzzy-label="订单号/供应项目/预算说明"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-ship-select
                        v-model="searchObj2.shipCode"
                      ></v-ship-select>
                    </v-col>
                    <v-col cols="12" sm="6" md="3">
                      <v-handler
                        clearable
                        label="实际申请人"
                        v-model="searchObj2.applicantId"
                        :use-current="false"
                      ></v-handler>
                    </v-col>
                    <v-col cols="12" sm="6" md="2">
                      <v-dict-select
                        clearable
                        v-model="searchObj2.subjectType"
                        label="费用类型"
                        dense
                        outlined
                        dict-type="cost_subject_type"
                      ></v-dict-select>
                    </v-col>
                    <v-col cols="12" sm="6" md="2">
                      <v-autocomplete
                        clearable
                        item-text="subjectName"
                        item-value="id"
                        v-model="searchObj2.costSubjectId"
                        label="费用科目"
                        dense
                        outlined
                        :items="costSubjects"
                        :disabled="!searchObj2.subjectType"
                      ></v-autocomplete>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  dense
                  v-model="detail.invoiceCode"
                  label="发票编号"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-autocomplete
                  label="发票币种"
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  v-model="detail.ccyId"
                  dense
                  outlined
                  item-text="ccyCode"
                  item-value="id"
                  :rules="[rules.required]"
                  :items="currencyInfo"
                ></v-autocomplete>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  :readonly="isEdit && isEditFinc"
                  v-model="detail.moneyPay"
                  label="发票金额"
                  :disabled="!detail.ccyId || !isEditFinc"
                  type="number"
                  outlined
                  dense
                  :rules="detail.ccyId == 2 ? [rules.int] : [rules.decimal]"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #结算信息>
        <v-form ref="formPay">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="供应商"
                  dense
                  outlined
                  v-model="detail.supplyName"
                  readonly
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-if="!isEdit || !isEditFinc"
                  outlined
                  dense
                  label="开户银行"
                  v-model="detail.openBank"
                  :rules="[rules.required]"
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  :items="banks"
                  item-text="bank"
                  return-object
                  @change="
                    () => {
                      detail.accountBank = detail.openBank.account
                      detail.openBank = detail.openBank.bank
                    }
                  "
                ></v-select>
                <v-text-field
                  v-else
                  outlined
                  dense
                  label="开户银行"
                  v-model="detail.openBank"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="银行账户"
                  v-model="detail.accountBank"
                  :rules="[rules.required]"
                  readonly
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #订单信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <!-- <v-col md="3" cols="12"> -->
              <!-- <v-dialog-select
                  req-url="/business/shipAffairs/supplyCommon/page"
                  label="选择预算"
                  v-model="detail.orderId"
                  :rules="[rules.required]"
                  item-text="orderNo"
                  item-value="id"
                  :headers="orderHeaders"
                  :search-remain="searchObj2"
                  @select="loadBudgetInfo"
                  required
                  dense
                  :disabled="!detail.shipCode"
                ></v-dialog-select> -->
              <!-- <v-dialog-select
                  req-url="/business/shipAffairs/supplyCommon/page22"
                  label="选择预算"
                  v-model="detail.orderId"
                  :rules="[rules.required]"
                  item-text="orderNo"
                  item-value="id"
                  :headers="orderHeaders"
                  :search-remain="searchObj2"
                  @select="loadBudgetInfo"
                  required
                  dense
                  fuzzy-label="订单号/供应项目/预算说明"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-ship-select
                        v-model="searchObj2.shipCode"
                      ></v-ship-select>
                    </v-col>
                    <v-col cols="12" sm="6" md="3">
                      <v-handler
                        clearable
                        label="实际申请人"
                        v-model="searchObj2.applicantId"
                        :use-current="false"
                      ></v-handler>
                    </v-col>
                    <v-col cols="12" sm="6" md="2">
                      <v-dict-select
                        clearable
                        v-model="searchObj2.subjectType"
                        label="费用类型"
                        dense
                        outlined
                        dict-type="cost_subject_type"
                      ></v-dict-select>
                    </v-col>
                    <v-col cols="12" sm="6" md="2">
                      <v-autocomplete
                        clearable
                        item-text="subjectName"
                        item-value="id"
                        v-model="searchObj2.costSubjectId"
                        label="费用科目"
                        dense
                        outlined
                        :items="costSubjects"
                        :disabled="!searchObj2.subjectType"
                      ></v-autocomplete>
                    </v-col>
                  </template>
                </v-dialog-select> -->
              <!-- </v-col> -->
              <v-col md="3" cols="12">
                <!-- :disabled="!canEdit || applyCanEdit" -->
                <v-dialog-select
                  :disabled="true"
                  label="实际申请人"
                  item-text="nickName"
                  v-model="applyPerson"
                  :headers="userHeaders"
                  :rules="[canEdit ? rules.required : true]"
                  req-url="/system/user/page"
                  :init-selected="detail.initApply"
                  :search-remain="searchObj"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="用户名"
                        v-model="searchObj.nickName"
                        outlined
                        dense
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="6" md="6">
                      <treeselect
                        v-model="searchObj.deptId"
                        :options="deptTree"
                        placeholder="请选择部门"
                        outlined
                        dense
                      />
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.applyDeptName"
                  label="申请部门"
                  disabled
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <!-- :disabled="!canEdit || !applyPerson.id || applyCanEdit" -->
                <v-select
                  outlined
                  dense
                  label="船舶"
                  :disabled="true"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                  :items="shipList"
                ></v-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="detail.subjectId"
                  :rules="[rules.required]"
                  :init-selected="detail.initSubject"
                  :search-dicts="searchDicts"
                  :search-remain="searchObj"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  readonly
                  disabled
                  required
                  dense
                  fuzzy-label="模糊搜索"
                  @select="
                    (item) => {
                      useLife = item.useLife
                    }
                  "
                ></v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.orderCode"
                  label="订单号"
                  :rules="[canEdit ? rules.required : true]"
                  readonly
                ></v-text-field>
              </v-col>
              <!-- <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.invoiceCode"
                  label="发票编号"
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :readonly="!canEdit"
                  :rules="[canEdit ? rules.required : true]"
                ></vs-date-picker>
              </v-col> -->

              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  use-today
                  v-model="detail.addDate"
                  disabled
                  label="录单日期"
                  :rules="[canEdit ? rules.required : true]"
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <!-- <v-text-field
                  outlined
                  dense
                  v-model="detail.addPersonName"
                  label="录单人"
                  disabled
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field> -->
                <v-dialog-select
                  :disabled="true"
                  label="录单人"
                  item-text="nickName"
                  v-model="detail.addPerson"
                  :headers="userHeaders"
                  :rules="[rules.required]"
                  req-url="/system/user/page"
                  :init-selected="detail.initAddPerson"
                ></v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.occurDate"
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  label="发生日期"
                  :rules="[canEdit ? rules.required : true]"
                ></vs-date-picker>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col> -->
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  v-if="isEdit"
                  outlined
                  dense
                  v-model="detail.supplyName"
                  readonly
                  label="供应商"
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field> -->
              <!-- <v-supply-select
                  :disabled="!detail.shipCode"
                  v-model="detail.supplyId"
                  :rules="[canEdit ? rules.required : true]"
                  :ship-code="detail.shipCode"
                  :readonly="isEdit"
                  @select="
                    (item) => {
                      currency = item.currency
                    }
                  "
                  :init-selected="initSupply"
                ></v-supply-select> -->
              <!-- </v-col> -->
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.ccyName"
                  readonly
                  label="币种"
                  :rules="[canEdit ? rules.required : true]"
                ></v-text-field>
                <!-- <v-select
                  v-else
                  v-model="detail.currencyObj"
                  :items="currency"
                  item-text="ccyCode"
                  return-object
                  :readonly="isEdit"
                  label="币种"
                  :rules="[canEdit ? rules.required : true]"
                  outlined
                  dense
                  :disabled="currency.length === 0"
                  @change="
                    (item) => {
                      currencyId = item.currencyType
                      bankDesc = item.bank + item.account
                      setUsd(null, item.currencyId)
                    }
                  "
                ></v-select> -->
              </v-col>
              <v-col md="3" cols="12">
                <!-- <v-numeric
                  :disabled="!currencyId"
                  :precision="isJPY ? 0 : 2"
                  outlined
                  dense
                  v-model="detail.money"
                  :readonly="!canEdit"
                  label="金额"
                  :rules="[canEdit ? rules.required : true]"
                ></v-numeric> -->
                <!-- <v-text-field
                  :disabled="!currencyId"
                  v-model="detail.money"
                  :rules="[isJPY ? rules.numberRule2 : rules.numberRule1]"
                  outlined
                  dense
                  required
                  :readonly="!canEdit"
                  label="金额"
                  type="number"
                  @change="
                    () => {
                      detail.money = isJPY
                        ? parseInt(detail.money)
                        : parseFloat(detail.money).toFixed(2)
                    }
                  "
                ></v-text-field> -->
                <v-text-field
                  type="number"
                  outlined
                  dense
                  v-model="detail.moneyOrder"
                  disabled
                  label="金额"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <v-numeric
                  precision="2"
                  outlined
                  dense
                  label="折算美金"
                  v-model="detail.usdMoney"
                  disabled
                ></v-numeric> -->
                <v-text-field
                  outlined
                  dense
                  label="折算美金"
                  v-model="detail.usdMoneyOrder"
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="采购数量"
                  v-model="detail.count"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.createPlace"
                  label="创建地点"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <!-- <v-text-field
                  outlined
                  dense
                  v-model="detail.overBudget"
                  readonly
                  label="是否超预算"
                ></v-text-field> -->
                <v-select
                  v-model="detail.overBudget"
                  :items="[
                    { text: '是', value: 1 },
                    { text: '否', value: 0 },
                  ]"
                  label="是否超预算"
                  readonly
                  dense
                  required
                  outlined
                ></v-select>
              </v-col>
              <v-col md="3" cols="12" v-if="detail.overBudget == 1">
                <v-text-field
                  outlined
                  dense
                  :readonly="isEdit && isEditFinc && detail.overBudget == 0"
                  :disabled="!isEditFinc"
                  v-model="detail.budgetRemark"
                  label="超预算说明"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  readonly
                  v-model="detail.accidentNo"
                  label="事故单号"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="备注"
                  dense
                  outlined
                  v-model="detail.remark"
                  :readonly="isEdit && isEditFinc"
                  :disabled="!isEditFinc"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <!-- <v-col v-if="!isEdit">银行信息:{{ bankDesc }}</v-col> -->
              <v-col v-if="!!useLife">使用寿命:{{ useLife }}</v-col>
            </v-row>
          </v-container>
        </v-form>
        <v-card-text>
          <v-attach-list
            title="发票附件"
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-card-text>
        <v-table-list
          :headers="subjectHeaders"
          :items="非调整科目"
          item-key="vid"
        >
          <template v-slot:[`item.money`]="{ item }">
            <!-- v-if="item.subjectName.includes('运费')" -->
            <vue-numeric
              v-model="item.money"
              :readonly="isEdit && isEditFinc"
              :disabled="!isEditFinc"
              :precision="isJPY ? 0 : 2"
              style="border: 1px solid black"
              dense
            ></vue-numeric>
          </template>
          <template v-slot:[`item.usdMoney`]="{ item }">
            {{ (item.money * detail.rate).toFixed(2) }}
          </template>
          <template v-slot:[`item.adjustMoney`]="{ item }">
            {{ (item.money - item.originMoney).toFixed(2) }}
          </template>
          <template v-slot:[`item.usdOriginMoney`]="{ item }">
            {{ (item.originMoney * detail.rate).toFixed(2) }}
          </template>
          <template v-slot:[`item.usdAdjustMoney`]="{ item }">
            {{ ((item.money - item.originMoney) * detail.rate).toFixed(2) }}
          </template>
        </v-table-list>
        <v-card-text>
          <v-row>
            <v-col md="3" cols="12">
              <v-text-field
                type="number"
                outlined
                dense
                v-model="detail.money"
                label="合计付款金额"
                disabled
                :rules="[rules.required]"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                outlined
                dense
                label="折算美金"
                v-model="detail.usdMoney"
                disabled
              ></v-text-field>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
    </v-detail-view-invoice>
    <v-dialog v-model="dialog" max-width="1500" hide-overlay attach="#mask">
      <v-card>
        <v-card-title class="text-h5">
          重复提醒-{{ reduplicateTypes[reduplicateType] }}
        </v-card-title>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn depressed @click="closeForm">取消</v-btn>
          <v-btn
            depressed
            color="primary"
            :disabled="reduplicateType === 0"
            @click="confirm"
          >
            确定
          </v-btn>
        </v-card-actions>
        <v-card-text>
          <v-table-list
            :table-name="''"
            :headers="duplicateHeaders"
            :items="duplicateList"
          ></v-table-list>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn depressed @click="closeForm">取消</v-btn>
          <v-btn
            depressed
            color="primary"
            :disabled="reduplicateType === 0"
            @click="confirm"
          >
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import computeMixin from './private/computeMixin'
import VueNumeric from 'vue-numeric'
export default {
  components: { Treeselect, VueNumeric },
  mixins: [currencyHelper, computeMixin],
  name: 'cost-gen-budget',
  props: {
    // 订单id
    orderId: {
      type: String,
    },
    // 发票id
    costOrderId: {
      type: String,
    },
    historyFlag: {
      type: Boolean,
    },
  },
  created() {
    this.backRouteName = 'cost-voucher-list'
    this.subtitles = ['发票信息', '结算信息', '订单信息']
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.userHeaders = [
      { text: '用户名', value: 'nickName' },
      { text: '岗位名称', value: 'deptName' },
      { text: '部门名称', value: 'parentDeptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.subjectHeaders = [
      { text: '费用科目', value: 'subjectName' },
      { text: '付款金额', value: 'money' },
      { text: '付款折算', value: 'usdMoney' },
      { text: '原始金额', value: 'originMoney' },
      { text: '原始折算', value: 'usdOriginMoney' },
      { text: '调整金额', value: 'adjustMoney' },
      { text: '调整折算', value: 'usdAdjustMoney' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.duplicateHeaders = [
      { text: '船舶', value: 'shipName', sortable: false },
      { text: '系列', value: 'seriesShip', sortable: false },
      { text: '费用科目名', value: 'subjectName', sortable: false },
      { text: '发票编号', value: 'invoiceCode' },
      { text: '供应商名', value: 'supplyName' },
      { text: '币种', value: 'ccyName' },
      { text: '原币金额', value: 'money', sortable: false },
      { text: '发生日期', value: 'happenDate', hideDefault: true },
      { text: '付款日期', value: 'payDate' },
      { text: '折算美金', value: 'usdMoney' },
      { text: '业务模块', value: 'businessModule' },
      { text: '实际申请人', value: 'applyPersonName', sortable: false },
      { text: '申请部门', value: 'applyDeptName', sortable: false },
    ]
    this.orderHeaders = [
      { text: '船舶名称', value: 'shipInfo' },
      { text: '数据来源', value: 'dataSource' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '供应商', value: 'supplierName' },
      { text: '供应项目', value: 'supplyItem' },
      { text: '订单号', value: 'orderNo' },
      { text: '科目', value: 'costSubjectName' },
      { text: '预算金额', value: 'money' },
      { text: '币种', value: 'currencyName' },
      { text: '实际申请人', value: 'applicantName' },
      { text: '预算说明', value: 'applyRemark' },
    ]
    this.reduplicateTypes = [
      '完全相同',
      '发票号相同',
      '金额相同',
      '发生日期相同',
    ]
  },
  data() {
    return {
      deptTree: [],
      detail: {
        orderId: '',
        businessModule: 0,
        addPersonName: this.$local.data.get('userInfo').nickName,
        ccyId: '',
        money: 0,
        rate: 1,
        // status: '',
        shipCode: '',
        costSubjects: [],
        invoiceCodeOld: '',
        initAddPerson: {
          nickName: this.$local.data.get('userInfo').nickName,
          id: this.$local.data.get('userInfo').id,
        },
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        numberRule1: (v) => {
          // 检查v是否为数字,如果是,返回验证是否保留两位小数的结果,否则返回错误信息
          if (!isNaN(parseFloat(v)) && isFinite(v)) {
            // 使用正则表达式来检查是否有超过两位小数
            if (/^-?\d+(\.\d{1,2})?$/.test(v)) {
              return true
            } else {
              return '输入的数字必须保留两位小数'
            }
          } else {
            return '请输入数字'
          }
        },
        numberRule2: (v) => {
          // 检查v是否为数字，如果是，返回转换为整数的值，否则返回错误信息
          if (!isNaN(parseFloat(v))) {
            return parseInt(v) == v || '输入的数字必须为整数'
          } else {
            return '请输入数字'
          }
        },
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
        decimal4: (v) =>
          /^\d+(\.\d{1,4})?$/.test(v) || '必须为整数或最多四位小数',
      },
      applyPerson: {},
      searchObj: { shipCode: '', efFlag: 1, nickName: '', deptId: '1' },
      searchObj2: {
        shipCode: '',
        status: 3,
        isGenCost: 0,
        dataSource: '单次预算',
        businessStatus: '审批通过、OA立项审批通过',
      },
      currency: [],
      shipList: [],
      initSupply: {},
      currencyId: '',
      duplicateList: [],
      dialog: false,
      reduplicateType: -1,
      bankDesc: '',
      useLife: '',
      budgetMoney: 0,
      applyCanEdit: false,
      banks: [],
      bankCcId: '',
      purchaseCompany: '',
      initOrder: {},
      flag: false,
      userId: this.$local.data.get('userInfo').id,
      projects: [],
      // isJPY: false,
    }
  },
  computed: {
    // isEdit() {
    //   // return this.$route.params.id !== 'new'
    //   return this.$route.params.id !== 'new'
    // },
    isEdit() {
      return (
        this.$route.params.id !== undefined &&
        this.detail.status != 10 &&
        this.detail.status != 12 &&
        this.detail.status != undefined
      )
    },
    isEditFinc() {
      return (
        (this.detail.status == 10 ||
          this.detail.status == 12 ||
          this.detail.status == undefined) &&
        this.detail.initAddPerson.id == this.$local.data.get('userInfo').id
      )
    },
    rate() {
      return this.currencyInfo.find((i) => i.id === this.detail?.currencyId)
        ?.rateToMain
    },
    // usdMoney: {
    //   get() {
    //     return (
    //       this.detail.usdMoney ??
    //       (this.currencyInfo?.find((i) => i.id === this.currencyId)
    //         ?.rateToMain || 0) * (this.detail.money || 0)
    //     )
    //   },
    //   set() {
    //     // this.detail.usdMoney = val
    //   },
    // },
    canEdit() {
      return !this.detail.status
    },
    isApplyPerson() {
      return this.userId == this.detail.applyPerson
    },
    // isJPY() {
    //   // console.log('this.detail.ccyCode:', this.detail.ccyCode)
    //   return this.detail.ccyCode === 'JPY' || this.detail.ccyCode === '日元'
    // },
  },
  watch: {
    costOrderId(val) {
      if (val) {
        console.log(val)
        this.loadDetail()
      }
    },
    applyPerson(val) {
      if (val) {
        this.detail.applyPerson = val.id
        this.detail.applyDeptName = val.parentDeptName
        this.searchObj2.applicantId = val.id
        this.loadShips(val.id)
      }
    },
    detail: {
      handler(val) {
        if (val.happenDate && val.supplyId && val.shipCode) {
          this.loadHappenDate(val)
        }
        if (val.money && val.money > this.detail.moneyOrder) {
          this.detail.overBudget = 1
        }
        if (val.money && val.money <= this.detail.moneyOrder) {
          this.detail.overBudget = 0
          this.detail.budgetRemark = ''
        }
        if (val.money) {
          // this.setUsd()
        }
      },
      deep: true,
    },
    'detail.shipCode'(val) {
      this.searchObj.shipCode = val
      this.searchObj2.shipCode = val
    },
    // currencyId(val) {
    //   this.detail.ccyId = val
    // },
    'searchObj2.subjectType': {
      handler: function (val) {
        this.getCostSubjects(val)
      },
    },
    非调整科目: {
      handler(val) {
        this.detail.money = val
          .reduce((acc, cur) => Number(acc) + Number(cur.money), 0)
          .toFixed(2)
        this.detail.usdMoney = (
          Math.round(this.detail.money * (this.detail.rate || 0) * 100) / 100
        ).toFixed(2)
      },
      deep: true,
    },
    'detail.occurDate': {
      async handler(val) {
        if (val && this.detail.supplyId && this.detail.shipCode) {
          const { data } = await this.postAsync(
            '/business/shipAffairs/costProject/getPayDate',
            {
              happenDate: val,
              shipCode: this.detail.shipCode,
              supplyId: this.detail.supplyId,
            },
          )
          // this.detail.payDate = data
          // this.detail.happenDate = val
          this.$set(this.detail, 'payDate', data)
          this.$set(this.detail, 'happenDate', val)
        }
      },
    },
  },
  methods: {
    // async loadDetail() {
    //   if (!this.isEdit) {
    //     return
    //   }
    //   const { data } = await this.getAsync(
    //     `/business/shipAffairs/costProject/getById/${this.$route.params.id}`,
    //   )
    //   this.detail = data
    //   this.initSupply = {
    //     id: data.supplyId,
    //     name: data.supplyName,
    //   }
    //   //   this.usdMoney = data.usdMoney
    //   this.applyPerson = data.applyPerson
    //   // this.currencyId = data.ccyId
    //   this.detail.initApply = {
    //     nickName: data.applyPersonName,
    //     id: data.applyPerson,
    //   }
    //   this.detail.initSubject = {
    //     subjectName: data.subjectName,
    //     id: data.subjectId,
    //   }
    //   this.detail.initSup = { name: data.supplyName, id: data.supplyId }
    //   this.getDeptTreeList()
    // },

    async save(goBack, notMove = false) {
      if (
        !this.detail.orderId ||
        this.detail.orderId.trim() === '' ||
        this.detail.orderId === undefined
      ) {
        this.$dialog.message.error('请选择预算')
        return
      }
      if (
        !this.detail.invoiceCode ||
        this.detail.invoiceCode.trim() === '' ||
        this.detail.invoiceCode === undefined
      ) {
        this.$dialog.message.error('请填写发票号')
        return
      }
      if (
        !this.detail.invoiceDate ||
        this.detail.invoiceDate.trim() === '' ||
        this.detail.invoiceDate === undefined
      ) {
        this.$dialog.message.error('请填写发票日期')
        return
      }
      if (Number(this.detail.moneyPay) != Number(this.detail.money)) {
        this.$dialog.message.error('发票金额与合计付款金额不一致，保存失败！')
        return
      }
      if (this.detail.ccyId != this.detail.currencyId) {
        this.$dialog.message.error('发票币种与订单币种不一致，保存失败！')
        return
      }
      if (!this.$refs.form.validate()) {
        return
      }
      if (
        // this.detail.invoiceCodeOld != '' &&
        this.detail.invoiceCodeOld != this.detail.invoiceCode
      ) {
        if (!(await this.checkDuplicate())) {
          return
        }
      }
      const flag = await this.checkBudgetYear()
      console.log('11111111111111:' + flag)
      // modifyCostProjectNew
      if (flag) {
        const projects = this.getProjects()
        for (const item of projects) {
          item.remark = this.detail.remark
          this.detail.businessModule = item.businessModule
        }
        // const { errorRaw } = await this.postAsync(
        //   '/business/shipAffairs/businessCostController/saveOrUpdateOrderProjectOfVoyageRepairBudget',
        //   projects,
        // )
        // if (errorRaw) return
        // this.$dialog.message.success('生成成功')
        // this.clear()
        this.detail.earliestPayDate = this.detail.payDate
        const { errorRaw, data } = await this.postAsync(
          '/business/shipAffairs/businessCostController/modifyOrderSubjectOfPurchaseBudgetOrder',
          { ...this.detail, costProjectPriceModifyDTOs: projects },
        )
        if (errorRaw) return
        // this.$dialog.message.success('操作成功')
        // this.clear()
        if (notMove) return data
        if (this.costOrderId == undefined) {
          this.$dialog.message.success('操作成功')
          this.clear()
        } else {
          goBack()
        }
        // goBack()
        // const { data, errorRaw } = await this.postAsync(
        //   '/business/shipAffairs/costProject/modifyCostProjectNew',
        //   { ...this.detail },
        // )
        // if (!errorRaw) {
        //   // 修改预算数据不可生成费用项目
        //   const reqUrl = '/business/shipAffairs/supplyCommon/updateIsGenCost'
        //   const { errorRaw } = await this.getAsync(reqUrl, {
        //     id: this.detail.budgetId,
        //     isGenCost: true,
        //     costProjectId: data,
        //   })
        //   if (errorRaw) return false

        //   this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        //   if (notMove) return data
        //   goBack()
        // }
      }
    },
    async submit(goBack) {
      console.log('----------------------------------')
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      // 未提交和审批失败状态重新提交到待实际申请人确认状态
      if (
        this.detail.status == 10 ||
        this.detail.status == 12 ||
        this.detail.status == undefined
      ) {
        //  if (Number(this.detail.moneyPay) != Number(this.detail.money)) {
        // 发票金额与合计付款金额不一致
        if (Number(this.detail.moneyPay) != Number(this.detail.moneyOrder)) {
          console.log('发票金额与订单金额不一致')
          this.detail.id = data
          this.detail.status = 99
          this.detail.businessStatus = '待实际申请人确认'
          // const idList = this.projects.map((item) => item.id)
          const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
          const { errorRaw } = await this.postAsync(reqUrl, {
            ...this.detail,
            // idList,
          })
          if (errorRaw) return false
          this.flag = true
          if (this.costOrderId == undefined) {
            this.$dialog.message.success('操作成功')
            this.clear()
          } else {
            goBack()
          }
        } else {
          console.log('发票金额与订单金额一致')
          this.detail.id = data
          this.detail.status = 13
          this.detail.businessStatus = '审批通过'
          // const idList = this.projects.map((item) => item.id)
          const reqUrl = '/business/shipAffairs/costOrder/modifyCostOrder'
          const { errorRaw } = await this.postAsync(reqUrl, {
            ...this.detail,
            // idList,
          })
          if (errorRaw) return false
          if (this.detail.status == 13) {
            const ids = [this.detail.id]
            const { errorRaw } = await this.postAsync(
              '/business/shipAffairs/costOrder/createSapNewList',
              ids,
            )
            if (!errorRaw)
              if (this.costOrderId == undefined) {
                this.$dialog.message.success('操作成功')
                this.clear()
              } else {
                goBack()
              }
          }
          this.flag = true
          if (this.costOrderId == undefined) {
            this.$dialog.message.success('操作成功')
            this.clear()
          } else {
            goBack()
          }
        }
      }
      if (!this.flag) {
        if (
          !this.detail.auditParams &&
          (this.detail.status == 99 || this.detail.status == 10)
        ) {
          this.quoteLoading = true
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/costOrder/process/start',
            { id: data },
          )
          this.quoteLoading = false
          if (!errorRaw)
            if (this.costOrderId == undefined) {
              this.$dialog.message.success('操作成功')
              this.clear()
            } else {
              goBack()
            }
        } else {
          this.quoteLoading = true
          const error = await this.$refs.audit.submit()
          this.quoteLoading = false
          if (!error) {
            const { data } = await this.getAsync(
              `/business/shipAffairs/costOrder/getById/${this.$route.params.id}`,
            )
            if (data.status == 13) {
              const ids = [this.$route.params.id]
              const { errorRaw } = await this.postAsync(
                '/business/shipAffairs/costOrder/createSapNewList',
                ids,
              )
              if (!errorRaw)
                if (this.costOrderId == undefined) {
                  this.$dialog.message.success('操作成功')
                  this.clear()
                } else {
                  goBack()
                }
            } else {
              if (this.costOrderId == undefined) {
                this.$dialog.message.success('操作成功')
                this.clear()
              } else {
                goBack()
              }
            }
          }
        }
      }
    },
    async startProcess() {
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/costOrder/process/start',
        { id: this.detail.id },
      )
      this.quoteLoading = false
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
    // 禁止保存（点击保存，增加弹框提示，不可保存，只能取消）：同发票号，同船，同供应商，同费用科目
    // 费用项目查重（点击保存，增加弹框提示，提示是否确定保存，点击确定可保存，可取消）：
    // 1. 发票号查重：同发票号，同船，同供应商
    // 2. 金额查重：同金额，同船，同供应商，同费用科目
    // 3. 发生日期查重：同发生日期，同船，同供应商
    async checkDuplicate() {
      const re0 = this.getAsync('/business/shipAffairs/costProject/list', {
        shipCode: this.detail.shipCode,
        supplyId: this.detail.supplyId,
        subjectId: this.detail.subjectId,
        invoiceCode: this.detail.invoiceCode,
        subjectName: this.detail.subjectName,
        // money: this.detail.money,
        // happenDate: this.detail.happenDate,
      })
      const re1 = this.getAsync('/business/shipAffairs/costProject/list', {
        shipCode: this.detail.shipCode,
        supplyId: this.detail.supplyId,
        invoiceCode: this.detail.invoiceCode,
        subjectName: this.detail.subjectName,
      })
      const re2 = this.getAsync('/business/shipAffairs/costProject/list', {
        shipCode: this.detail.shipCode,
        supplyId: this.detail.supplyId,
        subjectId: this.detail.subjectId,
        money: this.detail.money,
        subjectName: this.detail.subjectName,
      })
      const re3 = this.getAsync('/business/shipAffairs/costProject/list', {
        shipCode: this.detail.shipCode,
        supplyId: this.detail.supplyId,
        happenDate: this.detail.happenDate,
      })
      const [res0, res1, res2, res3] = await Promise.all([re0, re1, re2, re3])
      if (res0.data.length) {
        this.duplicateList = res0.data
        this.reduplicateType = 0
        this.dialog = true
      } else if (res1.data.length) {
        this.duplicateList = res1.data
        this.reduplicateType = 1
        this.dialog = true
      } else if (res2.data.length) {
        this.duplicateList = res2.data
        this.reduplicateType = 2
        this.dialog = true
      } else if (res3.data.length) {
        this.duplicateList = res3.data
        this.reduplicateType = 3
        this.dialog = true
      } else {
        this.reduplicateType = -1
        return true
      }
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },

    closeForm() {
      this.dialog = false
      this.resolveFn(false)
    },

    async confirm() {
      this.dialog = false
      this.resolveFn(true)
    },

    async loadHappenDate(val) {
      const { data } = await this.postAsync(
        '/business/shipAffairs/costProject/getPayDate',
        {
          happenDate: val.happenDate,
          shipCode: val.shipCode,
          supplyId: val.supplyId,
        },
      )
      this.detail.payDate = data
    },

    select(item) {
      console.log(item)
    },

    // setUsd(val, currencyId = false) {
    //   this.detail.usdMoney =
    //     (this.currencyInfo?.find(
    //       (i) => i.id === (currencyId || this.currencyId),
    //     )?.rateToMain || 0) * (val || this.detail.money || 0)
    //   this.detail.usdMoney = this.detail.usdMoney.toFixed(2)
    // },

    async loadShips(managerId) {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/ship/managementOwner/ships`,
        { managerId },
      )
      if (errorRaw) {
        return
      }
      this.shipList = data.map((i) => ({
        text: i.chShipName,
        value: i.shipCode,
      }))
    },
    async getDeptTreeList() {
      const { data } = await this.getAsync('/system/dept/getDeptTreeList')
      this.deptTree = data
    },
    async loadBudgetInfo(budget) {
      console.log(budget)
      let { data } = await this.getAsync(
        '/business/shipAffairs/supplyCommon/getDetailById',
        { Id: budget.id },
      )
      await this.getOrderInfo(data.id)
      console.log(data)
      data.costSubjectId = data.costSubjectId
        .replace(/[\u4e00-\u9fa5]/g, '')
        .replace(/-/g, '')
      // /system/user/page
      // this.currencyId = data.currencyId
      this.detail.currencyId = data.currencyId
      this.detail.ccyCode = data.ccyCode
      this.detail.ccyName = data.ccyCode
      // if (this.detail.ccyCode === 'JPY' || this.detail.ccyCode === '日元') {
      //   this.isJPY = true
      // }
      this.detail.money = data.money
      this.detail.usdMoney = data.moneyUsd
      this.detail.moneyOrder = data.money
      this.detail.usdMoneyOrder = data.moneyUsd
      // this.budgetMoney = data.money
      // this.setUsd()
      this.detail.accidentId = data.accidentId
      this.detail.accidentNo = data.accidentNo
      this.detail.initSubject = {
        subjectName: data.costSubjectName,
        id: data.costSubjectId,
      }
      this.detail.subjectName = data.costSubjectName
      this.detail.businessModule = 0
      this.detail.businessId = data.businessId
      this.detail.budgetId = data.id
      this.detail.subjectId = data.costSubjectId
      this.detail.supplyName = data.supplierName
      this.detail.supplyId = data.supplierId
      this.detail.orderCode = data.orderNo
      this.detail.happenDate = data.proposedDate
      this.detail.remark = data.applyRemark
      this.detail.shipCode = data.shipCode
      this.detail.rate = data.rateToMain
      // 新增直接选择预算数据  自动带出实际申请人、部门、船舶
      this.detail.initApply = {
        nickName: data.applicantNameNow,
        id: data.applicantIdNow,
      }
      this.purchaseCompany = data.payCompany
      this.bankCcId = data.currencyId
      this.loadBank()
      data = await this.getAsync('/system/user/page', {
        nickName: data.applicantNameNow,
        efFlag: 1,
        userId: data.applicantIdNow,
      })
      this.detail.applyPerson = data.data.records[0].id
      this.detail.applyDeptName = data.data.records[0].parentDeptName
      this.searchObj2.applicantId = data.data.records[0].id
      this.loadShips(data.data.records[0].id)
      this.applyCanEdit = true
    },
    async getOrderInfo(id) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/businessCostController/getOrderSubjectOfBudget',
        { orderId: id },
      )
      this.projects = data.map((i, index) => ({
        ...i,
        vid: index + 1,
        originMoney: i.subjectName.includes('调整') ? 0 : i.money,
        // originMoney: i.money,
      }))
      this.detail.occurDate = data?.[0]?.happenDate
      this.detail.happenDate = data?.[0]?.happenDate
      this.detail.payDate = data?.[0]?.payDate
      this.bankCcId = data?.[0]?.ccyId
      this.purchaseCompany = data?.[0]?.purchaseCompany
    },
    async checkBudgetYear() {
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/budgetYear/budgetYearCheckByTypeNew',
        {
          date: this.detail.happenDate,
          shipCode: this.detail.shipCode,
          subjectId: this.detail.initSubject.id,
          money: this.detail.money,
          type: '2', //生成费用项目
          budgetId: this.detail.budgetId,
        },
      )
      if (errorRaw) {
        return false
      }
      if (data) {
        //不超预算 可以提交
        return true
      }
      // this.$dialog.message.warning('当前')
    },
    clear() {
      this.$parent.$parent.$parent.refresh(null, this.$options.name)
      return
    },
    async getCostSubjects(subjectType) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/costSubject/page',
        {
          subjectType,
          size: 1000,
          current: 1,
        },
      )
      // { text: '草稿', value: '1' },
      this.costSubjects = data.records
    },
    async loadBank() {
      console.log(
        'this.purchaseCompany:',
        this.purchaseCompany,
        ',currencyId:',
        this.bankCcId,
      )
      const { data } = await this.getAsync(
        '/business/shipAffairs/Supplier/getBankMessageByShipCodeAndType',
        {
          purchaseCompany: this.purchaseCompany,
          supplierId: this.detail.supplyId,
          currencyId: this.bankCcId,
        },
      )
      if (data.length === 0) {
        this.$dialog.message.error('供应商未维护银行信息')
        return
      }
      data.forEach((item) => {
        item.bank = item.bank + ':' + item.account
      })
      this.banks = data
      // console.log(this.banks)
      if (data.length === 1) {
        this.detail.accountBank = data[0].account
        this.detail.openBank = data[0].bank
      }
      if (data.length > 1) {
        this.$dialog.message.info('供应商维护了多个银行信息,请选择')
      }
    },
    // 发票附件
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async loadDetail() {
      if (!this.historyFlag) return
      if (this.costOrderId == undefined) return
      console.log(2222222222222222222222222)
      // if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/shipAffairs/costOrder/getByIdNew/${this.costOrderId}`,
      )
      console.log(data)
      this.detail = data
      this.detail.supplyId = data.supplyId
      this.detail.shipCode = data.shipCode
      this.detail.attachmentRecords3 = data.attachmentRecords2
      this.detail.attachmentIds2 = data.attachmentIds2
      // this.detail.payDate = data.earliestPayDate
      this.$set(this.detail, 'payDate', data.earliestPayDate)
      this.purchaseCompany = data.payCompany
      this.bankCcId = data.ccyId
      this.loadBank()
      this.detail.initApply = {
        nickName: data.applyPersonName,
        id: data.applyPerson,
      }
      this.detail.addDate = data.orderDate
      this.detail.orderCode = data.orderNo
      this.detail.initAddPerson = {
        nickName: data.applyUserName,
        id: data.applyUser,
      }
      this.shipList = [
        {
          text: data.shipName,
          value: data.shipCode,
        },
      ]
      // this.detail.occurDate = data.happenDate
      this.$set(this.detail, 'occurDate', data.happenDate)
      this.detail.invoiceCodeOld = data.invoiceCode
      this.detail.remark = data.remark
      // this.detail.createPlace = data.detailList[0].createPlace
      // this.projects = []
      // this.projects = data.list.map((i, index) => ({
      //   ...i,
      //   vid: index + 1,
      //   originMoney: i.subjectName.includes('运费')
      //     ? // ['备件运费采购', '滑油运费采购'].includes(i.subjectName)
      //       0
      //     : i.money,
      //   maxMoney: i.money,
      // }))
      data.list.forEach((item, index) => {
        const newItem = {
          ...item,
          vid: index + 1,
        }
        this.projects.push(newItem)
        this.$set(this.projects, index, newItem) // 按索引设置元素:ml-citation{ref="7,8" data="citationList"}
        if (!newItem.subjectName.includes('调整')) {
          const initSubject = {
            subjectName: newItem.subjectName,
            id: newItem.subjectId,
          }
          this.$set(this.detail, 'initSubject', initSubject)
        }
      })
      console.log(this.projects)

      this.detail.currencyId = data.ccyId
      // this.rate = this.currencyInfo.find(
      //   (i) => i.id === this.detail?.currencyId,
      // )?.rateToMain
      this.detail.rate = this.rate
      // this.detail.money = data.moneyPay
      this.$set(this.detail, 'money', data.moneyPay)
      // this.detail.usdMoney = (Math.round(data.moneyPay * 100) / 100).toFixed(2)
      this.$set(
        this.detail,
        'usdMoney',
        (Math.round(data.moneyPay * 100) / 100).toFixed(2),
      )
      this.$set(this.detail, 'moneyOrder', data.moneyOrder)
      this.$set(this.detail, 'usdMoneyOrder', data.usdMoneyOrder)
      // this.detail.moneyOrder = this.非调整科目
      //   .reduce((acc, cur) => acc + cur.money, 0)
      //   .toFixed(2)
      // this.detail.usdMoneyOrder = (
      //   Math.round(this.detail.moneyOrder * this.rate * 100) / 100
      // ).toFixed(2)
      // this.非调整科目 = []
      // this.非调整科目 = this.projects.filter(
      //   (i) => !i.subjectName.includes('调整'),
      // )
      this.$set(
        this, // 目标对象（当前 Vue 实例）
        '非调整科目', // 属性名
        this.projects.filter((i) => !i.subjectName.includes('调整')), // 过滤后的数组
      )
      this.$set(this.detail, 'budgetRemark', this.projects[0].budgetRemark)
      // this.detailList = data.detailList
      // this.detailList.forEach((item) => {
      //   item.payPrice = item.unitPrice
      //   item.payTotalPrice = this.calculateDifference(
      //     item.payPrice * item.warehouseNum,
      //   )
      //   item.payTotalPriceMax = Number(
      //     this.calculateDifference(item.payPrice * item.warehouseNum),
      //   )
      // })
      this.initOrder = { id: data.orderId, orderNo: data.orderNo }
      // 加载页面数据  增加审批 增加保存附件操作
      // console.log(data.id)
      // this.detail = data
      // this.projects = data.list
    },
  },

  mounted() {
    // this.loadDetail()
    this.getDeptTreeList()
  },
}
</script>

//
<style lang="scss" scoped>
::v-deep input {
  text-align: left !important;
}
</style>
<style>
/* 隐藏所有数字输入框的增减按钮 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
/* Firefox 兼容 */
/* input[type='number'] {
  -moz-appearance: textfield;
} */
</style>
