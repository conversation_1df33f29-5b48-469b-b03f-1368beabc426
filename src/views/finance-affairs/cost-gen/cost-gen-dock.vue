<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['坞修修理单生成项目:编辑']"
      title="坞修修理单生成项目"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #custombtns>
        <v-btn
          width="60"
          tile
          @click="clear"
          color="success"
          small
          class="mx-1"
          v-permission="['坞修修理单生成项目:清空重录']"
        >
          清空重录
        </v-btn>
      </template>
      <template #基本信息>
        <v-form ref="form" :readonly="isEdit">
          <v-container fluid>
            <v-row>
              <!-- <v-col md="3" cols="12">
                <v-ship-select
                  :disabled="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col> -->
              <v-col md="3" cols="12">
                <v-handler
                  :disabled="isEdit"
                  label="实际申请人"
                  :use-current="false"
                  v-model="detail.applyPerson"
                  :rules="[rules.required]"
                  @selectUser="selectUser"
                ></v-handler>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.applyDeptName"
                  label="申请部门"
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-select
                  outlined
                  dense
                  label="船舶"
                  :disabled="!detail.applyPerson"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                  :items="shipList"
                ></v-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/dockRepairApply/getPageOfOrder"
                  label="坞修修理单"
                  v-model="detail.orderId"
                  :rules="[rules.required]"
                  item-text="orderNo"
                  item-value="id"
                  :headers="orderHeaders"
                  :readonly="isEdit"
                  :search-remain="searchObj"
                  @select="loadOrderInfo"
                  required
                  dense
                  :disabled="!detail.shipCode"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-select
                        v-model="searchObj.dockRepairType"
                        label="修理类型"
                        outlined
                        dense
                        :items="['厂修', '外协']"
                      ></v-select>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.invoiceCode"
                  label="发票编号"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :readonly="isEdit"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  use-today
                  v-model="detail.addDate"
                  label="录单日期"
                  :rules="[rules.required]"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <v-handler
                  outlined
                  dense
                  v-model="detail.addPerson"
                  label="录单人"
                  disabled
                  :rules="[rules.required]"
                  use-current
                ></v-handler>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.createPlace"
                  :readonly="isEdit"
                  label="创建地点"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.occurDate"
                  :readonly="isEdit"
                  label="发生日期"
                  :rules="[rules.required]"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.ccyName"
                  :readonly="isEdit"
                  label="币种"
                  :rules="[rules.required]"
                  outlined
                  dense
                  disabled
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  type="number"
                  outlined
                  dense
                  v-model="detail.money"
                  disabled
                  label="金额"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="折算美金"
                  v-model="detail.usdMoney"
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="供应商"
                  dense
                  outlined
                  v-model="detail.supplyName"
                  readonly
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col>
                <v-select
                  v-model="detail.overBudget"
                  :items="[
                    { text: '是', value: 1 },
                    { text: '否', value: 0 },
                  ]"
                  label="是否超预算"
                  readonly
                  dense
                  required
                  outlined
                ></v-select>
              </v-col>
              <v-col md="3" cols="12" v-if="detail.overBudget == 1">
                <v-text-field
                  outlined
                  dense
                  :readonly="detail.overBudget == 0"
                  v-model="detail.budgetRemark"
                  label="超预算说明"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="备注"
                  dense
                  outlined
                  v-model="detail.remark"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
        <v-table-list
          :headers="subjectHeaders"
          :items="非调整科目"
          item-key="vid"
        >
          <!-- <template v-slot:[`item.money`]="{ item }">
            <v-numeric
              v-if="item.subjectName !== '厂修费'"
              v-model="item.money"
              :precision="isJPY ? 0 : 2"
              dense
            ></v-numeric>
            <div v-else>{{ item.money }}</div>
          </template> -->
          <template v-slot:[`item.usdMoney`]="{ item }">
            {{ (item.money * rate).toFixed(2) }}
          </template>
          <template v-slot:[`item.adjustMoney`]="{ item }">
            {{ (item.money - item.originMoney).toFixed(2) }}
          </template>
          <template v-slot:[`item.usdOriginMoney`]="{ item }">
            {{ (item.originMoney * rate).toFixed(2) }}
          </template>
          <template v-slot:[`item.usdAdjustMoney`]="{ item }">
            {{ ((item.money - item.originMoney) * rate).toFixed(2) }}
          </template>
        </v-table-list>
      </template>
      <template #订单信息>
        <v-table-list :headers="headers" :items="detailList">
          <!-- <template v-slot:[`item.afterRepairPrice`]="{ item }">
            {{ item.repairPrice * item.repairDiscount }}
          </template> -->
          <template v-slot:[`item.isCompleted`]="{ item }">
            {{
              item.isCompleted == 1
                ? '是'
                : item.isCompleted == 2
                ? '取消'
                : '否'
            }}
          </template>
          <template v-slot:[`item.payPrice`]="{ item }">
            <vue-numeric
              readonly
              v-model="item.payPrice"
              :precision="isJPY ? 0 : 2"
              :max="item.unitPrice"
              style="border: 1px solid black"
              @input="changeDetails"
              dense
            ></vue-numeric>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import computeMixin from './private/computeMixin'
import VueNumeric from 'vue-numeric'
export default {
  components: {
    VueNumeric,
  },
  name: 'cost-gen-dock',
  created() {
    this.backRouteName = 'cost-project-list'
    this.subtitles = ['基本信息', '订单信息']
    this.orderHeaders = [
      { text: '修理单号', value: 'orderNo' },
      { text: '修理费用', value: 'totalPrice' },
      { text: '币种', value: 'ccyCode' },
      { text: '折算美元', value: 'toUsd' },
      { text: '供应商', value: 'supplierName' },
      { text: '坞修类型', value: 'dockRepairType' },
      { text: '决算金额', value: 'accountMoney', hideDefault: true },
      // { text: '申请部门', value: 'dept' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.subjectHeaders = [
      { text: '费用科目', value: 'subjectName' },
      { text: '付款金额', value: 'money' },
      { text: '付款折算', value: 'usdMoney' },
      { text: '原始金额', value: 'originMoney' },
      { text: '原始折算', value: 'usdOriginMoney' },
      { text: '调整金额', value: 'adjustMoney' },
      { text: '调整折算', value: 'usdAdjustMoney' },
    ]
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目编号', value: 'itemNo' },
      { text: '是否完工', value: 'isCompleted' },
      // { text: '修理折扣', value: 'repairDiscount' },
      { text: '成交单价', value: 'repairPrice' },
      { text: '付款单价', value: 'payPrice' },
      // { text: '修理原价', value: 'repairPrice' },
      // { text: '折扣后价格', value: 'afterRepairPrice' },
      { text: '备注', value: 'remark' },
    ]
  },
  mixins: [currencyHelper, computeMixin],
  data() {
    return {
      detail: { orderId: '', ccyName: '', ccyId: '', money: 0, usdMoney: 0 },
      projects: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchObj: {
        shipCode: '',
        businessStatus: '账单审核通过',
        dockRepairType: '厂修',
      },
      detailList: [],
    }
  },
  watch: {
    'detai.orderId'(val) {
      this.getOrderInfo(val)
    },
    detail: {
      handler(val) {
        if (val.occurDate && val.supplyId && val.shipCode) {
          // this.loadHappenDate(val)
        }
      },
      deep: true,
    },
    'detail.shipCode'(val) {
      if (val) {
        this.searchObj.shipCode = val
      }
    },
    'searchObj.dockRepairType'(val) {
      // this.searchObj.businessStatus =
      //   val === '厂修' ? '账单审核通过' : '账单审核通过,全部完工'
      this.searchObj.businessStatus =
        val === '厂修' ? '账单审核通过' : '账单审核通过'
    },
    非调整科目: {
      handler(val) {
        console.log(val)
        this.detail.money = val
          .reduce((acc, cur) => Number(acc) + Number(cur.money), 0)
          .toFixed(2)
        this.detail.usdMoney = this.detail.money * (this.rate || 0).toFixed(2)
      },
      deep: true,
    },
  },

  computed: {
    isEdit() {
      return false
    },
    rate() {
      return this.currencyInfo.find((i) => i.id === this.detail?.ccyId)
        ?.rateToMain
    },
    isJPY() {
      return this.detail.ccyName === 'JPY' || this.detail.ccyName === '日元'
    },
  },

  methods: {
    async save() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/costProject/list',
        {
          shipCode: this.detail.shipCode,
          supplyId: this.detail.supplyId,
          subjectId: this.detail.subjectId,
          invoiceCode: this.detail.invoiceCode,
          // money: this.detail.money,
          // happenDate: this.detail.happenDate,
        },
      )
      if (data.length > 0) {
        this.$dialog.message.error('发票编号重复！')
        return
      }
      if (!this.$refs.form.validate()) return
      // 保存订单详情信息
      const flag = await this.saveDetails()
      console.log('11111111111111:' + flag)
      if (flag) {
        const projects = this.getProjects()
        for (const item of projects) {
          item.remark = this.detail.remark
          item.budgetRemark = this.detail.budgetRemark
          item.overBudget = this.detail.overBudget
        }
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/businessCostController/modifyOrderSubjectOfDockRepairBudget',
          projects,
        )
        if (errorRaw) return
        this.$dialog.message.success('生成成功')
        this.clear()
      }
    },

    clear() {
      this.$parent.$parent.$parent.refresh(null, this.$options.name)
      return
    },

    async getOrderInfo(orderId) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/businessCostController/getOrderSubjectOfDockRepair',
        {
          orderId,
        },
      )
      this.projects = data.map((i, index) => ({
        ...i,
        vid: index + 1,
        originMoney: ['备件其他费用', '滑油其他费用', '物料其他费用'].includes(
          i.subjectName,
        )
          ? 0
          : i.money,
      }))
      console.log(this.projects)
      this.detail.occurDate = data?.[0]?.happenDate
      this.detail.payDate = data?.[0]?.payDate
    },

    async loadOrderInfo(order) {
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfOrderById2',
        {
          id: order.id,
        },
      )
      await this.getOrderInfo(data.id)
      this.detail.ccyName = data.ccyCode
      this.detail.ccyId = data.currencyId
      this.detail.money = data.accountMoney
      // this.detail.money = data.totalFinalPrice
      this.detail.usdMoney = data.toUsd
      this.detail.supplyName = data.supplierName
      this.detail.supplyId = data.supplierId
      this.detail.overBudget = data.overBudget
      this.detailList = data.detailList
      // this.detailList.forEach((item) => {
      //   if (item.isCompleted == 1) {
      //     item.payPrice = item.repairPrice
      //   } else {
      //     item.payPrice = 0
      //   }
      // })
      this.detail.rate = data.rateToMain
    },

    async loadHappenDate(val) {
      const { data } = await this.postAsync(
        '/business/shipAffairs/costProject/getPayDate',
        {
          happenDate: val.occurDate,
          shipCode: val.shipCode,
          supplyId: val.supplyId,
        },
      )
      this.detail.payDate = data
    },
    changeDetails() {
      let totalsss = 0
      this.detailList.forEach((detail) => {
        totalsss = totalsss + detail.payPrice
      })
      this.非调整科目.forEach((item) => {
        if (!item.subjectName.includes('其他费用')) {
          item.money = totalsss.toFixed(2)
        }
      })
    },
    async saveDetails() {
      const { data, errorRaw } = await this.postAsync(
        '/dockRepairApply/modifyRepairDetailsByGen',
        this.detailList,
      )
      if (errorRaw) {
        return false
      }
      if (data) {
        //不超预算 可以提交
        return true
      }
    },
    // async selectUser(user) {
    //   this.detail.applyDeptName = user.deptName
    // },
  },

  mounted() {},
}
</script>

<style></style>
