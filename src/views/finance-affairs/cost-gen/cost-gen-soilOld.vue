<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['滑油订单生成项目:编辑']"
      title="滑油订单生成项目"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #custombtns>
        <v-btn
          width="60"
          tile
          @click="clear"
          color="success"
          small
          class="mx-1"
          v-permission="['滑油订单生成项目:清空重录']"
        >
          清空重录
        </v-btn>
      </template>
      <template #基本信息>
        <v-form ref="form" :readonly="isEdit">
          <v-container fluid>
            <v-row>
              <!-- <v-col md="3" cols="12">
                <v-ship-select
                  :disabled="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col> -->

              <v-col md="3" cols="12">
                <v-handler
                  :disabled="isEdit"
                  label="实际申请人"
                  :use-current="false"
                  v-model="detail.applyPerson"
                  :rules="[rules.required]"
                  @selectUser="selectUser"
                ></v-handler>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.applyDeptName"
                  label="申请部门"
                  disabled
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-select
                  outlined
                  dense
                  label="船舶"
                  :disabled="!detail.applyPerson"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                  :items="shipList"
                ></v-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/purchaseManage/purchaseOrderPage"
                  label="滑油订单"
                  v-model="detail.orderId"
                  :rules="[rules.required]"
                  item-text="orderNo"
                  item-value="id"
                  :headers="orderHeaders"
                  :disabled="!detail.shipCode"
                  :search-remain="searchObj"
                  @select="loadOrderInfo"
                  required
                  dense
                >
                  <template v-slot:[`item.isDockRepair`]="{ item }">
                    {{ item.isDockRepair ? '是' : '否' }}
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.invoiceCode"
                  label="发票编号"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.invoiceDate"
                  label="发票日期"
                  :readonly="isEdit"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  use-today
                  v-model="detail.addDate"
                  label="录单日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <v-handler
                  outlined
                  dense
                  v-model="detail.addPerson"
                  label="录单人"
                  disabled
                  :rules="[rules.required]"
                  use-current
                ></v-handler>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.createPlace"
                  :readonly="isEdit"
                  label="创建地点"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.occurDate"
                  :readonly="isEdit"
                  label="发生日期"
                  :rules="[rules.required]"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.ccyName"
                  :readonly="isEdit"
                  label="币种"
                  :rules="[rules.required]"
                  outlined
                  dense
                  disabled
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-text-field
                  type="number"
                  outlined
                  dense
                  v-model="detail.money"
                  disabled
                  label="金额"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="折算美金"
                  v-model="detail.usdMoney"
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="供应商"
                  dense
                  outlined
                  v-model="detail.supplyName"
                  readonly
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.payDate"
                  label="付款日期"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="备注"
                  dense
                  outlined
                  v-model="detail.remark"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
        <v-table-list
          :headers="subjectHeaders"
          :items="非调整科目"
          item-key="vid"
        >
          <template v-slot:[`item.money`]="{ item }">
            <vue-numeric
              v-model="item.money"
              :precision="isJPY ? 0 : 2"
              :max="
                item.subjectName.includes('其他费用') ? Infinity : item.maxMoney
              "
              v-if="item.subjectName.includes('其他费用')"
              style="border: 1px solid black"
              @blur="
                checkMaxValue(
                  $event,
                  item.subjectName.includes('其他费用')
                    ? Infinity
                    : item.maxMoney,
                )
              "
              dense
            ></vue-numeric>
          </template>
          <template v-slot:[`item.usdMoney`]="{ item }">
            {{ (item.money * detail.rate).toFixed(2) }}
          </template>
          <template v-slot:[`item.adjustMoney`]="{ item }">
            {{ (item.money - item.originMoney).toFixed(2) }}
          </template>
          <template v-slot:[`item.usdOriginMoney`]="{ item }">
            {{ (item.originMoney * detail.rate).toFixed(2) }}
          </template>
          <template v-slot:[`item.usdAdjustMoney`]="{ item }">
            {{ ((item.money - item.originMoney) * detail.rate).toFixed(2) }}
          </template>
        </v-table-list>
      </template>
      <template #订单信息>
        <v-table-list :headers="headers" :items="detailList">
          <template v-slot:[`item.greaseType`]="{ item }">
            <span v-if="item.greaseType == '1581991857030852611'">
              主机系统油
            </span>
            <span v-if="item.greaseType == '1581991857047629825'">
              副机系统油
            </span>
            <span v-if="item.greaseType == '1581991857022464003'">
              主机气缸油
            </span>
            <span v-if="item.greaseType == '1649340717607944194'">
              其他小品种油
            </span>
          </template>
          <template v-slot:[`item.payPrice`]="{ item }">
            <vue-numeric
              v-model="item.payPrice"
              :precision="isJPY ? 0 : 4"
              :max="item.unitPrice"
              style="border: 1px solid black"
              @input="changeDetails"
              @blur="checkMaxValue($event, item.unitPrice)"
              dense
            ></vue-numeric>
          </template>
          <!-- <template v-slot:[`item.discountPrice`]="{ item }">
            {{ (item.discount * item.unitPrice).toFixed(2) }}
          </template> -->
          <template v-slot:[`item.totalPrice`]="{ item }">
            {{ (item.unitPrice * item.warehouseNum).toFixed(2) }}
          </template>
          <template v-slot:[`item.payTotalPrice`]="{ item }">
            {{ (item.payPrice * item.warehouseNum).toFixed(2) }}
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import computeMixin from './private/computeMixin'
import VueNumeric from 'vue-numeric'

export default {
  components: {
    VueNumeric,
  },
  name: 'cost-gen-soil',
  created() {
    this.backRouteName = 'cost-project-list'
    this.subtitles = ['基本信息', '订单信息']
    this.orderHeaders = [
      { text: '订单号', value: 'orderNo' },
      { text: '申请单号', value: 'applyNo' },
      { text: '创建日期', value: 'createTime' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '交货港口', value: 'orderPortName' },
      { text: '入库完成日期', value: 'completeTime' },
    ]
    this.subjectHeaders = [
      { text: '费用科目', value: 'subjectName' },
      { text: '付款金额', value: 'money' },
      { text: '付款折算', value: 'usdMoney' },
      { text: '原始金额', value: 'originMoney' },
      { text: '原始折算', value: 'usdOriginMoney' },
      { text: '调整金额', value: 'adjustMoney' },
      { text: '调整折算', value: 'usdAdjustMoney' },
    ]
    this.headers = [
      { text: '滑油英文名', value: 'greaseName' },
      { text: '滑油号', value: 'greaseNo' },
      { text: '滑油类型', value: 'greaseType' },
      { text: '订购数量', value: 'purchaseNum' },
      { text: '成交单价', value: 'unitPrice' },
      { text: '付款单价', value: 'payPrice' },
      // { text: '折扣', value: 'discount' },
      // { text: '折后单价', value: 'discountPrice' },
      { text: '总价', value: 'totalPrice' },
      { text: '付款总价', value: 'payTotalPrice' },
      { text: '入库数量', value: 'warehouseNum' },
    ]
  },
  data() {
    return {
      detail: {
        orderId: '',
        ccyName: '',
        ccyId: '',
        money: 0,
        usdMoney: 0,
        rate: 1,
      },
      projects: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchObj: { shipCode: '', orderType: '03', businessStatus: 1004 },
      detailList: [],
    }
  },
  mixins: [currencyHelper, computeMixin],
  watch: {
    'detai.orderId'(val) {
      this.getOrderInfo(val)
    },
    // detail: {
    //   handler(val) {
    //     if (val.occurDate && val.supplyId && val.shipCode) {
    //       this.loadHappenDate(val)
    //     }
    //   },
    //   deep: true,
    // },
    'detail.shipCode'(val) {
      if (val) {
        this.searchObj.shipCode = val
      }
    },
    非调整科目: {
      handler(val) {
        this.detail.money = val
          .reduce((acc, cur) => Number(acc) + Number(cur.money), 0)
          .toFixed(2)
        this.detail.usdMoney = (
          Math.round(this.detail.money * (this.detail.rate || 0) * 100) / 100
        ).toFixed(2)
      },
      deep: true,
    },
    detailList: {
      handler(val) {
        // 主机气缸油 total01  1581991857022464003
        // 系统油total02       1581991857030852611
        // 副机油total03       1581991857047629825
        // 滑油其他费用
        // 其他油种total04     1649340717607944194
        // let totalsss = 0
        let total01 = 0
        let total02 = 0
        let total03 = 0
        let total04 = 0
        val.forEach((detail) => {
          if (detail.costSubjectId == '1581991857022464003') {
            total01 =
              Number(total01) +
              Number(detail.payPrice) * Number(detail.warehouseNum)
          }
          if (detail.costSubjectId == '1581991857030852611') {
            total02 =
              Number(total02) +
              Number(detail.payPrice) * Number(detail.warehouseNum)
          }
          if (detail.costSubjectId == '1581991857047629825') {
            total03 =
              Number(total03) +
              Number(detail.payPrice) * Number(detail.warehouseNum)
          }
          if (detail.costSubjectId == '1649340717607944194') {
            total04 =
              Number(total04) +
              Number(detail.payPrice) * Number(detail.warehouseNum)
          }
        })
        this.非调整科目.forEach((item) => {
          if (!item.subjectName.includes('其他费用')) {
            if (item.subjectId == '1581991857022464003') {
              item.money = Number(total01).toFixed(2)
            }
            if (item.subjectId == '1581991857030852611') {
              item.money = Number(total02).toFixed(2)
            }
            if (item.subjectId == '1581991857047629825') {
              item.money = Number(total03).toFixed(2)
            }
            if (item.subjectId == '1649340717607944194') {
              item.money = Number(total04).toFixed(2)
            }
            // item.money = totalsss
          }
        })
      },
      deep: true,
    },
  },

  computed: {
    isEdit() {
      return false
    },
    rate() {
      return this.currencyInfo.find((i) => i.id === this.detail?.ccyId)
        ?.rateToMain
    },
    isJPY() {
      return this.detail.ccyName === 'JPY' || this.detail.ccyName === '日元'
    },
  },

  methods: {
    checkMaxValue(event, maxValue) {
      let inputValue = event.target.value
      console.log(inputValue)
      if (inputValue > maxValue) {
        this.$dialog.message.error('输入的值超过了最大限制,已变更为最大值')
        // event.target.value = maxValue
      }
    },
    async save() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/costProject/list',
        {
          shipCode: this.detail.shipCode,
          supplyId: this.detail.supplyId,
          subjectId: this.detail.subjectId,
          invoiceCode: this.detail.invoiceCode,
          // money: this.detail.money,
          // happenDate: this.detail.happenDate,
        },
      )
      if (data.length > 0) {
        this.$dialog.message.error('发票编号重复！')
        return
      }
      console.log(data)
      if (!this.$refs.form.validate()) return
      // 保存订单详情信息
      const flag = await this.saveDetails()
      if (flag) {
        const projects = this.getProjects()
        for (const item of projects) {
          item.remark = this.detail.remark
        }
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/businessCostController/modifyOrderSubjectOfPurchaseBudget',
          projects,
        )
        if (errorRaw) return
        this.$dialog.message.success('生成成功')
        this.clear()
      }
    },

    clear() {
      this.$parent.$parent.$parent.refresh(null, this.$options.name)
      return
    },

    async getOrderInfo(orderId) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/businessCostController/getOrderSubjectOfPurchase',
        {
          orderId,
          orderType: '03',
        },
      )
      this.projects = data.map((i, index) => ({
        ...i,
        vid: index + 1,
        originMoney: ['备件其他费用采购', '滑油其他费用采购'].includes(
          i.subjectName,
        )
          ? 0
          : i.money,
        maxMoney: i.money,
      }))
      this.detail.occurDate = data?.[0]?.happenDate
      this.detail.payDate = data?.[0]?.payDate
    },

    async loadOrderInfo(order) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseOrderById',
        {
          id: order.id,
        },
      )
      await this.getOrderInfo(data.id)
      this.detail.ccyName = data.currencyName
      this.detail.ccyId = data.currencyId
      // this.detail.money = (Math.round(data.totalPrice * 100) / 100).toFixed(4)
      this.detail.money = this.非调整科目
        .reduce((acc, cur) => acc + cur.money, 0)
        .toFixed(2)
      this.detail.usdMoney = (
        Math.round(data.totalPrice * data.rate * 100) / 100
      ).toFixed(2)
      this.detail.supplyName = data.supplierName
      this.detail.supplyId = data.supplierId
      this.detailList = data.detailList
      this.detailList.forEach((item) => {
        item.payPrice = item.unitPrice
      })
      this.detail.rate = data.rate
    },

    async loadHappenDate(val) {
      const { data } = await this.postAsync(
        '/business/shipAffairs/costProject/getPayDate',
        {
          happenDate: val.occurDate,
          shipCode: val.shipCode,
          supplyId: val.supplyId,
        },
      )
      this.detail.payDate = data
    },
    async saveDetails() {
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/modifyOrderDetailsByGen',
        this.detailList,
      )
      if (errorRaw) {
        return false
      }
      if (data) {
        //不超预算 可以提交
        return true
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
