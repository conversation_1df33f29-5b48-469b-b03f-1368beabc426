export default {
  data() {
    return {
      shipList: [],
    }
  },
  computed: {
    rate() {
      return this.currencyInfo.find((i) => i.id === this.detail?.ccyId)
        ?.rateToMain
    },
    非调整科目() {
      return this.projects.filter((i) => !i.subjectName.includes('调整'))
    },
  },

  methods: {
    getProjects() {
      // 存在调整科目
      const { id, ...detailWithoutId } = this.detail
      console.log(id)
      let projects = []
      console.log('this.非调整科目:', this.非调整科目)
      for (const item of this.非调整科目) {
        console.log('非调整科目.item:', item)
        const 调整科目 = this.projects.find(
          (i) => i.subjectName === `${item.subjectName}-调整`,
        )
        console.log('调整科目:', 调整科目)
        console.log('item.subjectName:', item.subjectName)
        if (调整科目) {
          if (item.money - item.originMoney != 0) {
            projects.push({
              ...调整科目,
              // ...this.detail,
              ...detailWithoutId,
              money: item.money - item.originMoney,
              subjectName: item.subjectName + '-调整',
              businessId: 调整科目.businessId || this.detail.orderId,
              usdMoney: ((item.money - item.originMoney) * this.rate).toFixed(
                2,
              ),
            })
          }
        } else {
          if (item.money - item.originMoney != 0) {
            console.log('aaaadqwe12313123123123123')
            console.log(item.money - item.originMoney)
            console.log('aaaadqwe12313123123123123')
            projects.push({
              ...item,
              // ...this.detail,
              ...detailWithoutId,
              id: null,
              money: item.money - item.originMoney,
              subjectName: item.subjectName + '-调整',
              businessId: item.businessId || this.detail.orderId,
              usdMoney: ((item.money - item.originMoney) * this.rate).toFixed(
                2,
              ),
            })
          }
        }
        if (item.money === 0) continue
        projects.push({
          ...item,
          // ...this.detail,
          ...detailWithoutId,
          money: item.money,
          businessId: item.businessId || this.detail.orderId,
          usdMoney: (item.money * this.rate).toFixed(2),
        })
      }
      console.log('projects:', projects)

      return projects
    },
    async selectUser(user) {
      this.loadShips(user.id)
      this.detail.applyDeptName = user.parentDeptName
    },

    async loadShips(managerId) {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/ship/managementOwner/ships`,
        { managerId },
      )
      if (errorRaw) {
        return
      }
      this.shipList = data.map((i) => ({
        text: i.chShipName,
        value: i.shipCode,
      }))
    },
  },
}
