<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="isShow" class="md-2">
        <v-card-title>
          {{ title }}
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-text-field
                label="公司编号"
                outlined
                dense
                v-model="newInfo.pCode"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="调配公司"
                outlined
                dense
                required
                :items="items"
                v-model="newInfo.creCompany"
                clearable
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="船员性质"
                outlined
                dense
                required
                v-model="newInfo.creFeature"
                :items="['自有船员', '外聘船员', '外包船员']"
                clearable
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="劳动合同签署公司"
                outlined
                dense
                required
                v-model="newInfo.creType"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="有无合同"
                outlined
                dense
                required
                v-model="newInfo.flagContract"
                :items="[
                  { text: '有', value: true },
                  { text: '无', value: false },
                ]"
                clearable
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="合同主体"
                outlined
                dense
                required
                v-model="newInfo.subjectContract"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="有无社保"
                outlined
                dense
                required
                v-model="newInfo.flagSocialSecurity"
                :items="[
                  { text: '有', value: true },
                  { text: '无', value: false },
                ]"
                clearable
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="有无中介"
                outlined
                dense
                required
                v-model="newInfo.flagAgent"
                :items="[
                  { text: '有', value: true },
                  { text: '无', value: false },
                ]"
                clearable
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="中介公司"
                outlined
                dense
                required
                v-model="newInfo.agentCompany"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="发送主体"
                outlined
                dense
                required
                v-model="newInfo.subjectProvide"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-textarea
                outlined
                dense
                required
                label="劳动合同签署公司描述"
                v-model="newInfo.creDescribe"
              ></v-textarea>
            </v-col>
          </v-row>
          <v-col cols="12">
            <v-btn
              outlined
              tile
              color="success"
              class="mx-1"
              :loading="loading1"
              @click="save"
              v-permission="['船员属性管理:编辑']"
              block
            >
              <v-icon left>mdi-plus-circle</v-icon>
              保存
            </v-btn>
          </v-col>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      @dbclick="edit"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-select
            label="船员管理公司"
            outlined
            dense
            required
            :items="items"
            v-model="searchRemain.creCompany"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员性质"
            outlined
            dense
            required
            v-model="searchRemain.creFeature"
            :items="['自有船员', '外聘船员', '外包船员']"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="newAdd"
          v-permission="['船员属性管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="edit"
          v-permission="['船员属性管理:更新']"
        >
          <v-icon left>mdi-pencil</v-icon>
          更新
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="Audit"
          v-permission="['船员属性管理:使用状态变更']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          使用状态变更
        </v-btn>
      </template>
      <template v-slot:[`item.flagContract`]="{ item }">
        {{ item.flagContract ? '有' : '否' }}
      </template>
      <template v-slot:[`item.flagSocialSecurity`]="{ item }">
        {{ item.flagSocialSecurity ? '有' : '否' }}
      </template>
      <template v-slot:[`item.flagAgent`]="{ item }">
        {{ item.flagAgent ? '有' : '否' }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ item.status ? '有' : '否' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'crew-attribute-management',
  created() {
    this.tableName = '船员属性管理'
    this.reqUrl = '/business/crew/crewProperty/list'
    this.headers = [
      { text: '公司代码', value: 'pCode' },
      { text: '船员管理公司', value: 'creCompany' },
      { text: '船员性质', value: 'creFeature' },
      { text: '劳动合同签署公司', value: 'creType' },
      { text: '劳动合同签署公司描述', value: 'creDescribe' },
      { text: '有无合同', value: 'flagContract', hideDefault: true },
      { text: '合同主体', value: 'subjectContract', hideDefault: true },
      { text: '有无社保', value: 'flagSocialSecurity', hideDefault: true },
      { text: '有无中介', value: 'flagAgent', hideDefault: true },
      { text: '发送主体', value: 'subjectProvide', hideDefault: true },
      { text: '中介公司', value: 'agentCompany', hideDefault: true },
      { text: '使用状态', value: 'status', hideDefault: true },
    ]
    this.fuzzyLabel = ''
    this.getAttritude()
  },

  data() {
    return {
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      isShow: false,
      isEdit: false,
      loading1: false,
      title: '',
      searchRemain: {},
      newInfo: {},
      selected: false,
      items: [],
    }
  },

  methods: {
    newAdd() {
      this.isShow = !this.isShow
      this.isEdit = !this.isEdit
      this.newInfo = {}
      this.title = '新增船员属性'
    },
    edit() {
      this.isEdit = !this.isEdit
      this.isShow = !this.isShow
      this.title = '修改船员属性'
      this.newInfo = this.selected
    },
    closeForm() {
      this.isShow = false
    },
    async getAttritude() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.items = data
    },
    async save() {
      if (!this.isEdit) {
        this.loading1 = true
        const { errorRaw } = await this.postAsync(
          `/business/crew/crewProperty/save`,
          this.newInfo,
        )
        this.loading1 = false
        if (errorRaw) {
          return
        }
        this.$dialog.message.success(`新增成功`)
        this.newInfo = {}
        this.isShow = false
        await this.$refs.table.loadTableData()
      } else {
        this.loading1 = true
        const { errorRaw } = await this.postAsync(
          `/business/crew/crewProperty/update`,
          this.newInfo,
        )
        this.loading1 = false
        if (errorRaw) {
          return
        }
        this.$dialog.message.success(`新增成功`)
        this.newInfo = {}
        this.selected = false
        this.isShow = false
        this.isEdit = false
        await this.$refs.table.loadTableData()
      }
    },
    async Audit() {
      if (
        !(await this.$dialog.msgbox.confirm('是否要更改当前记录的使用状态？'))
      )
        return

      const { errorRaw } = await this.getAsync(
        `/business/crew/crewProperty/changeStatus`,
        { id: this.selected.id, status: !this.selected.status },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`更改成功`)
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
