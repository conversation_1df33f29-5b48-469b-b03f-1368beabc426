<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶季度考评:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      :canSubmit="!detailInfo.submited"
    >
      <template #船舶季度考核基本信息>
        <v-container fluid>
          <v-form ref="form">
            <v-card-text>
              <v-row>
                <v-col cols="12" md="2">
                  <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-handler v-model="detailInfo.handler"></v-handler>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="考核发起时间"
                    outlined
                    dense
                    v-model="detailInfo.submitTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="考核开始时间"
                    outlined
                    dense
                    v-model="detailInfo.evaluateFromTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="考核结束时间"
                    outlined
                    dense
                    v-model="detailInfo.evaluateToTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="总分"
                    outlined
                    dense
                    v-model="detailInfo.totalScore"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    outlined
                    dense
                    label="当前状态"
                    :items="[
                      { text: '草稿', value: false },
                      { text: '已提交', value: true },
                    ]"
                    readonly
                    v-model="detailInfo.submited"
                  ></v-select>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-container>
      </template>
      <template #船员考评奖发放详情>
        <v-table-list
          :headers="creHeaders"
          :items="detailInfo.crewItems"
          use-page
          v-model="creSelected"
        >
          <template v-slot:[`item.deductMoney`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.deductMoney"
              type="number"
              :rules="[(v) => v >= 0 || `需填写大于0的数`]"
            ></v-text-field>
          </template>
        </v-table-list>
      </template>

      <template #船员考评奖发放详情按钮>
        <!-- <v-import-more-btn
          importUrl="business/crew/crewEvaluate/crewEvaluateImport"
          buttonLabel="导入船员绩效考核"
          @importSuccess="importSuccess"
          :extraParam="dynamicParam"
        ></v-import-more-btn>
        <v-btn
          :disabled="!creSelected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="update"
          v-permission="['考核项目:修改']"
          :loading="loading"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn> -->
      </template>
    </v-detail-view>
    <ship-quarter-item-dialog
      v-model="dialog"
      @success="success"
      :initialData="initialData"
    ></ship-quarter-item-dialog>
  </v-container>
</template>
<script>
import shipQuarterItemDialog from './ship-quarter-item-dialog.vue'
export default {
  components: { shipQuarterItemDialog },
  name: 'crew-quarter-detail',
  created() {
    this.backRouteName = 'crew-quarter-list'
    this.subtitles = ['船舶季度考核基本信息', '船员考评奖发放详情']
    this.headers = [
      { text: '考核项目', value: 'item' },
      { text: '考核人', value: 'tester' },
      { text: '标准分值', value: 'topScore' },
      { text: '实际分值', value: 'itemScore' },
    ]
    this.creHeaders = [
      { text: '船员', value: 'userName' },
      { text: '船员职务', value: 'position' },
      { text: '分数', value: 'score' },
      { text: '考评奖金', value: 'money' },
    ]
  },
  data() {
    return {
      title: '新增船舶季度考核',
      detailInfo: {},
      dialog: false,
      selected: false,
      creSelected: false,
      initialData: {},
      initUser: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      loading: false,
      dynamicParam: {},
    }
  },

  methods: {
    async save(goBack) {
      if (this.detailInfo.submited) {
        goBack()
        return
      }
      if (!(await this.$dialog.msgbox.confirm('信息是否确认无误？'))) return
      const { errorRaw } = await this.postAsync(
        `/business/crew/crewEvaluate/update`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async submit(goBack) {
      if (!(await this.$dialog.msgbox.confirm('信息是否确认无误？'))) return
      const error = await this.postAsync(
        `/business/crew/crewEvaluate/update`,
        this.detailInfo,
      )
      if (error.errorRaw) {
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/crew/crewEvaluate/submit`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      goBack()
      this.$dialog.message.success('提交成功')
    },
    success(val) {
      val.seq = this.detailInfo.itemScoreList.length + 1
      this.detailInfo.itemScoreList.push(val)
      this.$dialog.message.success('添加成功')
    },
    async getDetailInfo() {
      if (this.$route.params.id === `new`) return
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewEvaluate/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.title = data.shipName + '---船舶季度考核'
      this.initUser = {}
      this.detailInfo = data
      this.updateDynamicParam(this.detailInfo.id)
    },
    create() {
      this.dialog = true
      this.initialData = {}
    },
    del() {
      this.detailInfo.itemScoreList = this.detailInfo.itemScoreList.filter(
        (ele) => ele.seq !== this.selected.seq,
      )
      for (let i = 0; i < this.detailInfo.itemScoreList.length; i++) {
        this.detailInfo.itemScoreList[i].seq = i + 1
      }
      this.selected = false
      this.$dialog.message.success('删除成功')
    },
    async update() {
      if (!(await this.$dialog.msgbox.confirm('填写的抵扣项是否无误？'))) return
      this.loading = true

      const { errorRaw } = await this.getAsync(
        `/business/crew/shipEvaluate/crewEvaluate/modify`,
        { id: this.creSelected.id, deduction: this.creSelected.deductMoney },
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.creSelected = false
      this.$dialog.message.success('修改成功')
      this.loading = false
      await this.getDetailInfo()
    },
    updateDynamicParam(id) {
      this.dynamicParam.key = id
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
