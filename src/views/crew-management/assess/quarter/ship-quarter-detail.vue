<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶季度考评:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      :canSubmit="!detailInfo.submited"
    >
      <template #船舶季度考核基本信息>
        <v-container fluid>
          <v-form ref="form">
            <v-card-text>
              <v-row>
                <v-col cols="12" md="2">
                  <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-handler v-model="detailInfo.handler"></v-handler>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="考核发起时间"
                    outlined
                    dense
                    v-model="detailInfo.submitTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="考核开始时间"
                    outlined
                    dense
                    v-model="detailInfo.evaluateFromTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="考核结束时间"
                    outlined
                    dense
                    v-model="detailInfo.evaluateToTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="总分"
                    outlined
                    dense
                    v-model="detailInfo.totalScore"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    outlined
                    dense
                    label="当前状态"
                    :items="[
                      { text: '草稿', value: false },
                      { text: '已提交', value: true },
                    ]"
                    readonly
                    v-model="detailInfo.submited"
                  ></v-select>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-container>
      </template>
      <template #考核项目>
        <v-table-list
          :items="detailInfo.itemScoreList"
          :headers="headers"
          item-key="seq"
          v-model="selected"
          use-page
        >
          <template v-slot:[`item.item`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.item"
              :rules="[rules.required]"
              required
            ></v-text-field>
          </template>
          <template v-slot:[`item.topScore`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.topScore"
              :rules="[rules.required]"
              type="number"
              required
            ></v-text-field>
          </template>
          <template v-slot:[`item.itemScore`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.itemScore"
              :rules="[rules.number]"
              required
              type="number"
            ></v-text-field>
          </template>
          <template v-slot:[`item.tester`]="{ item }">
            <v-text-field single-line v-model="item.tester"></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template #考核项目按钮>
        <v-btn outlined tile color="success" class="mx-1" @click.stop="create">
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #船员考评奖发放详情>
        <v-table-list
          :headers="creHeaders"
          :items="detailInfo.crewItems"
          use-page
          v-model="creSelected"
        >
          <template v-slot:[`item.deductMoney`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.deductMoney"
              type="number"
              :rules="[(v) => v >= 0 || `需填写大于0的数`]"
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template #船员考评奖发放详情按钮>
        <v-btn
          :disabled="!creSelected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="update"
          v-permission="['考核项目:修改']"
          :loading="loading"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
      </template>
    </v-detail-view>
    <ship-quarter-item-dialog
      v-model="dialog"
      @success="success"
      :initialData="initialData"
    ></ship-quarter-item-dialog>
  </v-container>
</template>
<script>
import shipQuarterItemDialog from './ship-quarter-item-dialog.vue'
export default {
  components: { shipQuarterItemDialog },
  name: 'ship-quarter-detail',
  created() {
    this.backRouteName = 'ship-quarter-list'
    this.subtitles = ['船舶季度考核基本信息', '考核项目', '船员考评奖发放详情']
    this.headers = [
      { text: '考核项目', value: 'item' },
      { text: '考核人', value: 'tester' },
      { text: '标准分值', value: 'topScore' },
      { text: '实际分值', value: 'itemScore' },
    ]
    this.creHeaders = [
      { text: '船员', value: 'userName' },
      { text: '船员职务', value: 'position' },
      { text: '第一个月考勤天数', value: 'month1Days' },
      { text: '第一个月考评奖金', value: 'month1Money' },
      { text: '第二个月考勤天数', value: 'month2Days' },
      { text: '第二个月考评奖金', value: 'month2Money' },
      { text: '第三个月考勤天数', value: 'month3Days' },
      { text: '第三个月考评奖金', value: 'month3Money' },
      { text: '总奖金', value: 'totalMoney' },
      { text: '扣除奖金', value: 'deductMoney' },
    ]
  },
  data() {
    return {
      title: '新增船舶季度考核',
      detailInfo: {},
      dialog: false,
      selected: false,
      creSelected: false,
      initialData: {},
      initUser: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      loading: false,
    }
  },

  methods: {
    async save(goBack) {
      if (this.detailInfo.submited) {
        goBack()
        return
      }
      if (!(await this.$dialog.msgbox.confirm('信息是否确认无误？'))) return
      const { errorRaw } = await this.postAsync(
        `/business/crew/shipEvaluate/seasonTest/update`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async submit(goBack) {
      if (!(await this.$dialog.msgbox.confirm('信息是否确认无误？'))) return
      const error = await this.postAsync(
        `/business/crew/shipEvaluate/seasonTest/update`,
        this.detailInfo,
      )
      if (error.errorRaw) {
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/crew/shipEvaluate/seasonTest/submit`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      goBack()
      this.$dialog.message.success('提交成功')
    },
    success(val) {
      val.seq = this.detailInfo.itemScoreList.length + 1
      this.detailInfo.itemScoreList.push(val)
      this.$dialog.message.success('添加成功')
    },
    async getDetailInfo() {
      if (this.$route.params.id === `new`) return
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/shipEvaluate/seasonTest/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.title = data.shipName + '---船舶季度考核'
      this.initUser = {}
      this.detailInfo = data
    },
    create() {
      this.dialog = true
      this.initialData = {}
    },
    del() {
      this.detailInfo.itemScoreList = this.detailInfo.itemScoreList.filter(
        (ele) => ele.seq !== this.selected.seq,
      )
      for (let i = 0; i < this.detailInfo.itemScoreList.length; i++) {
        this.detailInfo.itemScoreList[i].seq = i + 1
      }
      this.selected = false
      this.$dialog.message.success('删除成功')
    },
    async update() {
      if (!(await this.$dialog.msgbox.confirm('填写的抵扣项是否无误？'))) return
      this.loading = true

      const { errorRaw } = await this.getAsync(
        `/business/crew/shipEvaluate/crewEvaluate/modify`,
        { id: this.creSelected.id, deduction: this.creSelected.deductMoney },
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.creSelected = false
      this.$dialog.message.success('修改成功')
      this.loading = false
      await this.getDetailInfo()
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
