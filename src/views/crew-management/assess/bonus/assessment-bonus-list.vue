<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="isShow">
        <v-card-title>
          初始化船舶岗位考评奖金标准
          <v-spacer></v-spacer>
          <v-icon @click="isShow = false">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="3">
              <v-ship-select v-model="shipBonusInit.shipCode"></v-ship-select>
            </v-col>
            <v-spacer></v-spacer>
            <v-btn
              outlined
              dense
              tile
              class="mx-1"
              color="success"
              @click="initBonus"
              v-permission="['船舶考评标准:初始化']"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              初始化
            </v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      show-expand
      use-ship
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="initAllShipBonus"
          v-permission="['船舶考评标准:初始化所有船舶岗位考评奖金标准']"
        >
          <v-icon left>mdi-file-multiple</v-icon>
          初始化所有船舶岗位考评奖金标准
        </v-btn>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          @click="openShow"
          v-permission="['船舶考评标准:初始化船舶岗位考评奖金标准']"
        >
          <v-icon left>mdi-file</v-icon>
          初始化船舶岗位考评奖金标准
        </v-btn>
        <v-btn
          :disabled="!selected"
          tile
          outlined
          dense
          color="#E65100"
          class="mx-1"
          @click="updateBonusStandard"
          v-permission="['考核奖金详情:更新船舶岗位标准']"
        >
          <v-icon left>mdi-pencil</v-icon>
          更新船舶岗位标准
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['船舶考评标准:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length">
          <v-container fluid>
            <v-card-subtitle class="text-h6 py-1">考核奖金详情</v-card-subtitle>
            <v-divider></v-divider>
            <v-table-list
              v-model="crewSelected"
              item-key="position"
              :headers="crewHeaders"
              :items="item.standardList"
              use-page
            >
              <template v-slot:[`item.evaluateStandard`]="{ item }">
                <v-text-field
                  single-line
                  v-model="item.evaluateStandard"
                  dense
                ></v-text-field>
              </template>
            </v-table-list>
            <v-divider></v-divider>
          </v-container>
        </td>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'assessment-bonus-list',
  created() {
    this.tableName = '船舶考评奖金标准'
    this.reqUrl = '/business/crew/shipEvaluate/standard/page'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '维护人姓名', value: 'handler' },
      { text: '', value: 'data-table-expand' },
    ]
    this.crewHeaders = [
      { text: '岗位', value: 'position' },
      { text: '考评奖金标准', value: 'evaluateStandard' },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: false,
      crewSelected: false,
      shipBonusInit: {},
      isShow: false,
      dialog: false,
      initialData: {},
    }
  },

  methods: {
    openShow() {
      this.isShow = true
    },
    changeDialog() {
      this.dialog = true
      this.initialData = this.crewSelected
    },
    success(val) {
      this.crewSelected = val
    },
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/shipEvaluate/standard/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = false
      this.crewSelected = false
      await this.$refs.table.loadTableData()
    },
    async initAllShipBonus() {
      if (
        !(await this.$dialog.msgbox.confirm('确定初始化所有船舶岗位工资标准？'))
      )
        return
      const { errorRaw } = await this.getAsync(
        `/business/crew/shipEvaluate/standard/initAll`,
      )
      if (errorRaw) {
        return
      }

      await this.$refs.table.loadTableData()
      this.$dialog.message.success('初始化成功')
    },
    async initBonus() {
      const { errorRaw } = await this.getAsync(
        `/business/crew/shipEvaluate/standard/init`,
        this.shipBonusInit,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('初始化成功')
      await this.$refs.table.loadTableData()
      this.isShow = false
      this.shipBonusInit = {}
    },
    async updateBonusStandard() {
      if (
        !(await this.$dialog.msgbox.confirm(
          '是否更新选中记录的船舶岗位工资标准？',
        ))
      )
        return
      const { errorRaw } = await this.postAsync(
        `/business/crew/shipEvaluate/standard/update`,
        this.selected,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('更新成功')
      this.selected = false
      this.crewSelected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
