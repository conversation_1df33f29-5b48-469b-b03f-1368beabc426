// 适任证书类别
const competencyMap = new Map([
  ['A', '无限航区'],
  ['B', '沿海航区'],
])
// 发证机关
const departmentMap = new Map([
  ['GA', '上海海事局'],
  ['DA', '天津海事局'],
  ['BA', '辽宁海事局'],
  ['EA', '山东海事局'],
  ['KA', '广东海事局'],
  ['CA', '河北海事局'],
  ['FG', '江苏海事局'],
  ['HE', '浙江海事局'],
  ['JD', '福建海事局'],
  ['LE', '广西海事局'],
  ['MA', '海南海事局'],
  ['PC', '长江海事局'],
  ['NA', '黑龙江海事局'],
  ['KB', '深圳海事局'],
  ['BB', '营口海事局'],
  ['EB', '烟台海事局'],
  ['FA', '连云港海事局'],
  ['JC', '厦门海事局'],
  ['KD', '汕头海事局'],
  ['KC', '湛江海事局'],
  ['EC', '日照海事局'],
  ['HA', '宁波海事局'],
  ['EH', '济南海事局'],
  ['SZ', '四川省地方海事局'],
  ['EJ', '青岛海事局'],
  ['ED', '威海海事局'],
  ['HB', '舟山海事局'],
  ['HG', '台州海事局'],
  ['HD', '温州海事局'],
  ['HF', '嘉兴海事局'],
  ['JA', '福州海事局'],
  ['JB', '泉州海事局'],
  ['KL', '广州海事局'],
  ['KE', '珠海海事局'],
  ['KH', '江门海事局'],
  ['KN', '惠州海事局'],
  ['ZG', '潮州海事局'],
  ['ZH', '揭阳海事局'],
  ['KG', '茂名海事局'],
  ['KT', '阳江海事局'],
  ['KJ', '汕尾海事局'],
  ['KF', '中山海事局'],
  ['ZA', '佛山海事局'],
  ['KM', '东莞海事局'],
])
// 工作部门
const workMap = new Map([
  ['1', '甲板部'],
  ['2', '轮机部'],
  ['3', '无线电通信'],
])
// 适任证书的等级
const deckLevel = new Map([
  ['1', '3000 总吨及以上船舶'],
  ['2', '500 至 3000 总吨船舶'],
  ['3', '未满 500 总吨船舶'],
  ['4', '500 总吨及以上船舶'],
])

const engineLevel = new Map([
  ['1', '主推进动力装置 3000 千瓦及以上船舶'],
  ['2', '主推进动力装置 750 至 3000 千瓦船舶'],
  ['3', '主推进动力装置未满 750 千瓦船舶'],
  ['4', '主推进动力装置 750 千瓦及以上船舶'],
])

const radioLevel = new Map([
  ['1', 'GMDSS 无线电电子'],
  ['2', 'GMDSS 无线电操作员'],
])

// 船上岗位
const deckMap = new Map([
  ['1', '船长'],
  ['2', '大副'],
  ['3', '二副'],
  ['4', '三副'],
  ['5', '水手'],
  ['6', '高级水手'],
])

const engineMap = new Map([
  ['1', '轮机长'],
  ['2', '大管轮'],
  ['3', '二管轮'],
  ['4', '三管轮'],
  ['5', '机工'],
  ['6', '高级机工'],
  ['7', '电气电子员'],
  ['8', '电子机工'],
])

const radioMap = new Map([
  ['1', '一级无线电电子员'],
  ['2', '二级无线电电子员'],
  ['3', '通用操作员'],
  ['4', '限用操作员'],
])

export default function (cerCode) {
  console.log('start')
  if (cerCode.length !== 15) return
  const certificateObj = {}
  // 适任证书类别
  if (competencyMap.has(cerCode[0]))
    certificateObj.competency = competencyMap.get(cerCode[0])
  // 发证机关代码
  if (departmentMap.has(cerCode.slice(1, 3)))
    certificateObj.department = departmentMap.get(cerCode.slice(1, 3))

  // 工作部门
  if (workMap.has(cerCode[3])) {
    certificateObj.work = workMap.get(cerCode[3])

    // 适任证书等级
    if (cerCode[3] === '1') {
      certificateObj.certificateLevel = deckLevel.get(cerCode[4])
    } else if (cerCode[3] === '2') {
      certificateObj.certificateLevel = engineLevel.get(cerCode[4])
    } else if (cerCode[3] === '3') {
      certificateObj.certificateLevel = radioLevel.get(cerCode[4])
    }

    // 船上职务
    if (cerCode[3] === '1') {
      certificateObj.position = deckMap.get(cerCode[5])
    } else if (cerCode[3] === '2') {
      certificateObj.position = engineMap.get(cerCode[5])
    } else if (cerCode[3] === '3') {
      certificateObj.position = radioMap.get(cerCode[5])
    }
  }
  return certificateObj
}
