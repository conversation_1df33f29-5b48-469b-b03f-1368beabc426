<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船员证书:编辑']"
      :title="`${titleName}---详情信息`"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template v-slot:证书的基础信息>
        <v-card-text class="pt-0">
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="2">
                <v-select
                  label="证书名称"
                  outlined
                  dense
                  :items="category"
                  :readonly="newCard !== `new`"
                  :rules="[rules.required]"
                  v-model="detailInfo.category"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="证书编号"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :readonly="newCard !== `new`"
                  v-model="detailInfo.code"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-dialog-select
                  v-model="detailInfo.creId"
                  item-value="creId"
                  item-text="name"
                  req-url="/business/crew/baseInfo/simple/page"
                  :rules="[rules.required]"
                  label="船员"
                  :init-selected="initSelected"
                  :headers="headers"
                  :search-remain="searchRemain"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        v-model="searchRemain.name"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="身份证号"
                        outlined
                        dense
                        v-model="searchRemain.idCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        v-model="searchRemain.position"
                      ></v-ship-station>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="2">
                <!-- <v-select
                  label="证书类别"
                  outlined
                  :rules="
                    detailInfo.category === '适任证书' ? [rules.required] : []
                  "
                  :items="['无限航区', '沿海航区']"
                  required
                  dense
                  v-model="detailInfo.realCategory"
                ></v-select> -->
                <v-text-field
                  label="证书类别"
                  outlined
                  dense
                  v-model="detailInfo.realCategory"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="证书英文名称"
                  outlined
                  dense
                  v-model="detailInfo.enName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="签发机构"
                  outlined
                  :rules="
                    detailInfo.category === '适任证书' ? [rules.required] : []
                  "
                  dense
                  v-model="detailInfo.msa"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-ship-station
                  label="证书职务"
                  outlined
                  dense
                  :rules="
                    detailInfo.category === '适任证书' ? [rules.required] : []
                  "
                  v-model="detailInfo.certificatePost"
                ></v-ship-station>
              </v-col>
              <v-col cols="12" md="2">
                <!-- <vs-date-picker
                  label="签发日期"
                  outlined
                  required
                  dense
                  :rules="
                    detailInfo.category !== '巴拿马保安员背书SSO' &&
                    detailInfo.category !== '巴拿马保安背书SDSD' &&
                    detailInfo.category !== '巴拿马大厨背书'
                      ? [rules.required]
                      : []
                  "
                  v-model="detailInfo.signDate"
                ></vs-date-picker>
                <v-col cols="12" md="2">
                <vs-date-picker
                  label="到期日期"
                  :disabled="detailInfo.category === `国际旅行健康检查证明书`"
                  outlined
                  dense
                  :rules="
                    detailInfo.category !== '服务簿' &&
                    detailInfo.category !== '船上厨师培训合格证明'
                      ? [rules.required]
                      : []
                  "
                  required
                  v-model="detailInfo.expireDate"
                ></vs-date-picker> -->
                <vs-date-picker
                  label="签发日期"
                  outlined
                  required
                  dense
                  v-model="detailInfo.signDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="到期日期"
                  :disabled="detailInfo.category === `国际旅行健康检查证明书`"
                  outlined
                  dense
                  required
                  v-model="detailInfo.expireDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="签发地址"
                  outlined
                  dense
                  v-model="detailInfo.signPlace"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="存放地址"
                  outlined
                  dense
                  v-model="detailInfo.storeLocate"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="国家地区"
                  outlined
                  dense
                  v-model="detailInfo.countryPart"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="申办单位"
                  outlined
                  dense
                  v-model="detailInfo.applicant"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="证书状态"
                  outlined
                  dense
                  :items="[
                    '已存档',
                    '已调出',
                    '换证使用',
                    '考试调出',
                    '离职调出',
                  ]"
                  v-model="detailInfo.status"
                ></v-select>
              </v-col>
              <v-col
                cols="12"
                md="2"
                v-if="newCard !== 'new' && detailInfo.category !== `船员服务簿`"
              >
                <v-text-field
                  label="证书有效期（个月）"
                  outlined
                  dense
                  readonly
                  v-model="detailInfo.remaining_months"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="预警时间"
                  outlined
                  dense
                  v-model="detailInfo.warnTime"
                ></vs-date-picker>
              </v-col>
            </v-row>
            <v-row>
              <v-col v-if="detailInfo.category === `适任证书`">
                <v-textarea
                  outlined
                  dense
                  label="船舶种类描述"
                  v-model="detailInfo.shipCategory"
                ></v-textarea>
              </v-col>
              <v-col>
                <v-textarea
                  outlined
                  dense
                  label="备注"
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-attach-list
          :attachments="detailInfo.attachmentRecords"
          @change="changeAttachment"
        ></v-attach-list>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import rules from './rules'
export default {
  name: 'certificate-detail',
  created() {
    this.backRouteName = 'certificate-management'
    this.subtitles = ['证书的基础信息']
    this.category = [
      '海员证',
      '培训合格证书',
      '服务簿',
      '适任证书',
      '海事局健康证书',
      '护照',
      '香港适任证书',
      '巴拿马证书',
      '巴拿马GMDSS证书',
      '巴拿马保安员背书SSO',
      '巴拿马保安背书SDSD',
      '巴拿马大厨背书',
      '船上厨师培训合格证明',
      '卫检健康证',
      '黄热',
      '霍乱',
    ]
    this.realCategory = ['无限航区', '沿海航区']
    this.newCard = this.$route.params.id
    this.headers = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.initSelected = {}
  },
  data() {
    return {
      detailInfo: {
        code: '',
        category: '',
        attachmentIds: [],
        attachmentRecords: [],
      },
      titleName: '新增证书界面',
      rules: {
        // required: (v) => !!v || v === 0 || '必填项不能为空',
        // number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchRemain: {},
    }
  },
  watch: {
    'detailInfo.category': {
      handler(val) {
        if (val === '适任证书') {
          console.log(val)
          const obj = rules(this.detailInfo.code)
          console.log(obj)
          if (obj) {
            console.log(this.detailInfo)
            if (obj.department) {
              this.detailInfo.msa = obj.department
            }
            if (obj.position) {
              this.detailInfo.certificatePost = obj.position
            }
            // this.detailInfo.realCategory = obj.competency
          }
        }
      },
    },
    'detailInfo.signDate': {
      handler(newval) {
        if (this.detailInfo.category === '国际旅行健康检查证明书') {
          let newstrNumber = newval.split('-')
          let number = Number.parseInt(newstrNumber[0], 10) + 1
          newstrNumber[0] = number

          let expireDate = newstrNumber.join('-')

          this.detailInfo.expireDate = expireDate
        }
      },
    },
    'detailInfo.expireDate': {
      handler(newval) {
        let newstrNumber = newval.split('-')
        let number = Number.parseInt(newstrNumber[0], 10) - 1
        newstrNumber[0] = number

        let warnTime = newstrNumber.join('-')

        this.detailInfo.warnTime = warnTime
      },
    },
    'detailInfo.code': {
      handler(val) {
        if (this.detailInfo.category === '适任证书') {
          const obj = rules(val)
          if (obj) {
            if (obj.department) {
              this.detailInfo.msa = obj.department
            }
            if (obj.position) {
              this.detailInfo.certificatePost = obj.position
            }
            // this.detailInfo.realCategory = obj.competency
          }
        }
      },
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    async save(goBack) {
      const status = new Map([
        ['已存档', 'ARCHIVED'],
        ['已调出', 'OUT'],
        ['换证使用', 'REPLACE_USE'],
        ['考试调出', 'TEST_OUT'],
        ['离职调出', 'LEAVE_OUT'],
      ])
      const category = new Map([
        ['海员证', 'SEAMAN'],
        ['培训合格证书', 'TRAINING'],
        ['服务簿', 'CREW_SERVICE'],
        ['适任证书', 'COMPETENCY'],
        ['海事局健康证书', 'MARITIME_HEALTH'],
        ['护照', 'PASSPORT'],
        ['香港适任证书', 'HONG_KONG_COMPETENCY'],
        ['巴拿马证书', 'PANAMA_COMPETENCY'],
        ['巴拿马GMDSS证书', 'PANAMA_COMPETENCY_GMDSS'],
        ['巴拿马保安员背书SSO', 'PANAMA_COMPETENCY_SSO'],
        ['巴拿马保安背书SDSD', 'PANAMA_COMPETENCY_SDSD'],
        ['巴拿马大厨背书', 'PANAMA_CHIEF'],
        ['船上厨师培训合格证明', 'MCL_CHIEF'],
        ['卫检健康证', 'HEALTH_INSPECTION'],
        ['黄热', 'YELLOW_FEVER_VACCINE'],
        ['霍乱', 'CHOLERA'],
      ])
      let curObject = {
        ...this.detailInfo,
        status: status.get(this.detailInfo.status),
        category: category.get(this.detailInfo.category),
      }
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw } = await this.postAsync(
        `/business/crew/certificate/modifyCrewCertificate`,
        curObject,
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = {}
      goBack()
    },
    async getCertificateDetail() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/certificate/getById/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        this.titleName = this.detailInfo.creName + '---' + this.detailInfo.name
        this.initSelected = { name: data.creName, creId: data.creId }
      }
    },
  },

  async mounted() {
    await this.getCertificateDetail()
  },
}
</script>

<style></style>
