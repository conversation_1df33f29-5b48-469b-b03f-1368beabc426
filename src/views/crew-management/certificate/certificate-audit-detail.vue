<template>
  <v-container fluid>
    <v-detail-view
      title="证书审核详情"
      :backRouteName="backRouteName"
      :can-save="false"
    >
      <template>
        <v-card-title>
          证书基本信息
          <v-spacer></v-spacer>
          <v-btn
            color="success"
            @click="handleApprove"
            class="mx1"
            outlined
            tile
          >
            <v-icon left>mdi-check-circle</v-icon>
            审核通过
          </v-btn>
          <v-btn color="error" @click="handleReject" outlined tile>
            <v-icon left>mdi-close-circle</v-icon>
            审核不通过
          </v-btn>
        </v-card-title>
        <v-card-text class="pt-0">
          <v-row>
            <v-col cols="12" md="3">
              <v-text-field
                label="船员姓名"
                outlined
                dense
                readonly
                v-model="detailInfo.creName"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                label="身份证号"
                outlined
                dense
                readonly
                v-model="detailInfo.idCard"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                label="证书类型"
                outlined
                dense
                readonly
                v-model="detailInfo.category"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                label="证书编号"
                outlined
                dense
                readonly
                v-model="detailInfo.code"
              ></v-text-field>
            </v-col>

            <!-- 新增字段 -->
            <v-col cols="12" md="3">
              <v-text-field
                label="签发机构"
                outlined
                dense
                readonly
                v-model="detailInfo.msa"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                label="证书职务"
                outlined
                dense
                readonly
                v-model="detailInfo.certificatePost"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <vs-date-picker
                label="签发日期"
                outlined
                dense
                readonly
                v-model="detailInfo.signDate"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="3">
              <vs-date-picker
                label="到期日期"
                outlined
                dense
                readonly
                v-model="detailInfo.expireDate"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                label="存放地址"
                outlined
                dense
                readonly
                v-model="detailInfo.storeLocate"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <v-text-field
                label="国家地区"
                outlined
                dense
                readonly
                v-model="detailInfo.countryPart"
              ></v-text-field>
            </v-col>
          </v-row>

          <!-- 船舶种类描述 -->
          <v-row v-if="detailInfo.category === '适任证书'">
            <v-col cols="12">
              <v-textarea
                outlined
                dense
                label="船舶种类描述"
                readonly
                v-model="detailInfo.shipCategory"
              ></v-textarea>
            </v-col>
          </v-row>

          <!-- 备注信息 -->
          <v-row>
            <v-col cols="12">
              <v-textarea
                outlined
                dense
                label="备注"
                readonly
                v-model="detailInfo.remark"
              ></v-textarea>
            </v-col>
          </v-row>

          <!-- 附件展示 -->
          <v-attach-list
            :attachments="detailInfo.attachmentRecords"
            readonly
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>

<script>
import routerControl from '@/mixin/routerControl'

export default {
  mixins: [routerControl],

  name: 'certificate-audit-detail',
  data() {
    return {
      backRouteName: 'certificate-audit',
      detailInfo: {
        creName: '',
        idCard: '',
        category: '',
        code: '',
        msa: '',
        certificatePost: '',
        signDate: '',
        expireDate: '',
        storeLocate: '',
        countryPart: '',
        shipCategory: '',
        remark: '',
        attachmentRecords: [],
      },
    }
  },
  methods: {
    async handleApprove() {
      try {
        const { errorRaw } = await this.getAsync(
          `/business/crew/certificate/successAudit/${this.$route.params.id}`,
        )
        if (errorRaw) {
          this.$dialog.message.error(errorRaw)
          return
        }
        this.$dialog.message.success('审核通过成功')
        this.closeAndTo(this.backRouteName, {}, {})
      } catch (error) {
        console.error('审核失败:', error)
        this.$dialog.message.error('审核操作失败，请稍后重试')
        this.closeAndTo(this.backRouteName, {}, {})
      }
    },
    async handleReject() {
      const { errorRaw } = await this.getAsync(
        `/business/crew/certificate/refuseAudit/${this.$route.params.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(errorRaw)
        return
      }

      this.$dialog.message.success('审核不通过成功')
      await this.closeAndTo(this.backRouteName, {})
    },

    async loadDetail() {
      try {
        const { data } = await this.getAsync(
          `/business/crew/certificate/getById/${this.$route.params.id}`,
        )
        this.detailInfo = {
          ...this.detailInfo,
          ...data,
          // 转换证书类型值
          category: this.getCategoryText(data.category),
        }
      } catch (error) {
        console.error('加载详情失败:', error)
      }
    },

    getCategoryText(value) {
      const categoryMap = {
        SEAMAN: '海员证',
        TRAINING: '培训合格证书',
        CREW_SERVICE: '服务簿',
        COMPETENCY: '适任证书',
        MARITIME_HEALTH: '海事局健康证书',
        PASSPORT: '护照',
        HONG_KONG_COMPETENCY: '香港适任证书',
        PANAMA_COMPETENCY: '巴拿马证书',
        PANAMA_COMPETENCY_GMDSS: '巴拿马GMDSS证书',
        PANAMA_COMPETENCY_SSO: '巴拿马保安员背书SSO',
        PANAMA_COMPETENCY_SDSD: '巴拿马保安背书SDSD',
        PANAMA_CHIEF: '巴拿马大厨背书',
        MCL_CHIEF: '船上厨师培训合格证明',
        HEALTH_INSPECTION: '卫检健康证',
        YELLOW_FEVER_VACCINE: '黄热',
        CHOLERA: '霍乱',
      }
      return categoryMap[value] || value
    },
  },
  mounted() {
    this.loadDetail()
  },
}
</script>

<style scoped>
/* 可根据需要添加审核页面特有样式 */
</style>
