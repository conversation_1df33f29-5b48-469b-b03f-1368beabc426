<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      table-name="待审核证书"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      :push-params="pushParams"
      item-key="id"
    >
      <template #searchflieds>
        <v-col cols="12" md="3">
          <v-select
            label="船员管理公司"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.crewCompany"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="3">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            v-model="searchRemain.name"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="3">
          <v-select
            label="证书类型"
            outlined
            dense
            v-model="searchRemain.category"
            :items="category"
            clearable
          ></v-select>
        </v-col>
      </template>

      <template v-slot:[`item.actions`]="{ item }">
        <v-btn
          small
          color="primary"
          @click="handleAudit(item)"
          v-permission="['船员证书审核:操作']"
        >
          审核
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>

<script>
import routerControl from '@/mixin/routerControl'
export default {
  mixins: [routerControl],
  name: 'certificate-audit',
  data() {
    return {
      selected: [],
      searchRemain: {
        company: '',
        name: '',
        category: '',
        newInfo: {},
      },
      pushParams: {
        name: 'certificate-audit-detail',
      },
      category: [
        { text: '海员证', value: 'SEAMAN' },
        { text: '培训合格证书', value: 'TRAINING' },
        { text: '服务簿', value: 'CREW_SERVICE' },
        { text: '适任证书', value: 'COMPETENCY' },
        { text: '海事局健康证书', value: 'MARITIME_HEALTH' },
        { text: '护照', value: 'PASSPORT' },
        { text: '香港适任证书', value: 'HONG_KONG_COMPETENCY' },
        { text: '巴拿马证书', value: 'PANAMA_COMPETENCY' },
        { text: '巴拿马GMDSS证书', value: 'PANAMA_COMPETENCY_GMDSS' },
        { text: '巴拿马保安员背书SSO', value: 'PANAMA_COMPETENCY_SSO' },
        { text: '巴拿马保安背书SDSD', value: 'PANAMA_COMPETENCY_SDSD' },
        { text: '巴拿马大厨背书', value: 'PANAMA_CHIEF' },
        { text: '船上厨师培训合格证明', value: 'MCL_CHIEF' },
        { text: '卫检健康证', value: 'HEALTH_INSPECTION' },
        { text: '黄热', value: 'YELLOW_FEVER_VACCINE' },
        { text: '霍乱', value: 'CHOLERA' },
      ],
      headers: [
        { text: '姓名', value: 'creName' },
        { text: '身份证号', value: 'idCard' },
        { text: '证书类型', value: 'category' },
      ],
      reqUrl: '/business/crew/certificate/auditPage',
      fuzzyLabel: '',
    }
  },
  methods: {
    handleAudit(item) {
      // 审核逻辑实现
      console.log('审核项目:', item)
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data
    },
  },
  mounted() {
    this.getCreFirst()
  },
}
</script>
