<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :searchRemain="searchRemain"
      :search-date="searchDate"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      useShip
      :single-select="false"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            required
            v-model="searchRemain.name"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="证书名称"
            outlined
            dense
            required
            clearable
            v-model="searchRemain.category"
            :items="category"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="证书有效期"
            outlined
            dense
            required
            v-model="searchRemain.remaining_months"
            clearable
            :items="remaining_months"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="证书状态"
            outlined
            dense
            required
            v-model="searchRemain.status"
            clearable
            :items="status"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="证书类别"
            outlined
            dense
            required
            clearable
            v-model="searchRemain.realCategory"
            :items="realCategory"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员实际职务"
            outlined
            dense
            required
            clearable
            v-model="searchRemain.post"
            :items="post"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          dense
          tile
          color="blue"
          :disabled="selected.length === 0"
          class="mx-1"
          @click="exportCertificate"
          v-permission="['船员证书:新增']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          导出船员证书
          <a v-if="true" :href="downPDF" ref="downPDFHref"></a>
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/certificate/information-detail/new"
          v-permission="['船员证书:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['船员证书:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.remaining_months`]="{ item }">
        <v-chip
          color="red"
          v-if="
            item.remaining_months <= 6 &&
            item.remaining_months > 0 &&
            item.availableFlag
          "
          dark
          small
        >
          {{ item.remaining_months }}个月
        </v-chip>
        <v-chip
          color="orange"
          v-else-if="
            item.remaining_months > 6 &&
            item.remaining_months <= 12 &&
            item.availableFlag
          "
          dark
          small
        >
          {{ item.remaining_months }}个月
        </v-chip>
        <v-chip
          color=""
          v-else-if="item.remaining_months < 0 && item.availableFlag"
          dark
          small
        >
          证书过期
        </v-chip>
        <v-chip color="green" v-else-if="item.availableFlag" dark small>
          {{ item.remaining_months }}个月
        </v-chip>
        <v-chip v-else></v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'certificate-management',
  created() {
    this.tableName = '船员证书资料'
    this.reqUrl = '/business/crew/certificate/page'
    this.headers = [
      { text: '证书编号', value: 'code' },
      { text: '证书名称', value: 'category' },
      { text: '船员姓名', value: 'creName' },
      { text: '身份证号', value: 'idCard' },
      {
        text: '证书有效期限',
        value: 'remaining_months',
      },
      { text: '国家地区', value: 'countryPart' },
      { text: '签发日期', value: 'signDate' },
      { text: '到期日期', value: 'expireDate' },
      { text: '审核人', value: 'approvalUserName' },

      { text: '附件', value: 'attachmentRecords', sortable: false },
    ]
    this.category = [
      { text: '海员证', value: 'SEAMAN' },
      { text: '培训合格证书', value: 'TRAINING' },
      { text: '服务簿', value: 'CREW_SERVICE' },
      { text: '适任证书', value: 'COMPETENCY' },
      { text: '海事局健康证书', value: 'MARITIME_HEALTH' },
      { text: '护照', value: 'PASSPORT' },
      { text: '香港适任证书', value: 'HONG_KONG_COMPETENCY' },
      { text: '巴拿马证书', value: 'PANAMA_COMPETENCY' },
      { text: '巴拿马GMDSS证书', value: 'PANAMA_COMPETENCY_GMDSS' },
      { text: '巴拿马保安员背书SSO', value: 'PANAMA_COMPETENCY_SSO' },
      { text: '巴拿马保安背书SDSD', value: 'PANAMA_COMPETENCY_SDSD' },
      { text: '巴拿马大厨背书', value: 'PANAMA_CHIEF' },
      { text: '船上厨师培训合格证明', value: 'MCL_CHIEF' },
      { text: '卫检健康证', value: 'HEALTH_INSPECTION' },
      { text: '黄热', value: 'YELLOW_FEVER_VACCINE' },
      { text: '霍乱', value: 'CHOLERA' },
    ]
    this.realCategory = [
      { text: '无限航区', value: '无限航区' },
      {
        text: '沿海航区',
        value: '沿海航区',
      },
    ]
    this.post = [
      { text: '船长', value: '船长' },
      { text: '专职见习船长', value: '专职见习船长' },
      { text: '大副', value: '大副' },
      { text: '专职见习大副', value: '专职见习大副' },
      { text: '二副', value: '二副' },
      { text: '专职见习二副', value: '专职见习二副' },
      { text: '三副', value: '三副' },
      { text: '专职见习三副', value: '专职见习三副' },
      { text: '水手长', value: '水手长' },
      { text: '高级值班水手', value: '高级值班水手' },
      { text: '值班水手', value: '值班水手' },
      { text: '二水', value: '二水' },
      { text: '轮机长', value: '轮机长' },
      { text: '专职见习轮机长', value: '专职见习轮机长' },
      { text: '大管轮', value: '大管轮' },
      { text: '专职见习大管轮', value: '专职见习大管轮' },
      { text: '二管轮', value: '二管轮' },
      { text: '专职见习二管轮', value: '专职见习二管轮' },
      { text: '三管轮', value: '三管轮' },
      { text: '专职见习三管轮', value: '专职见习三管轮' },
      { text: '电子电气员', value: '电子电气员' },
      { text: '专职见习电子电气员', value: '专职见习电子电气员' },
      { text: '电机员', value: '电机员' },
      { text: '电工', value: '电工' },
      { text: '机工长', value: '机工长' },
      { text: '高级值班机工', value: '高级值班机工' },
      { text: '值班机工', value: '值班机工' },
      { text: '二机', value: '二机' },
      { text: '大厨', value: '大厨' },
      { text: '见习二副', value: '见习二副' },
      { text: '见习三副', value: '见习三副' },
      { text: '见习二管轮', value: '见习二管轮' },
      { text: '见习三管轮', value: '见习三管轮' },
      { text: '见习电子电气员', value: '见习电子电气员' },
      { text: '见习电子电气员', value: '见习电子电气员' },
      { text: '见习电子技工', value: '见习电子技工' },
      { text: '见习水手', value: '见习水手' },
      { text: '见习值班水手', value: '见习值班水手' },
      { text: '见习机工', value: '见习机工' },
      { text: '见习值班机工', value: '见习值班机工' },
    ]
    this.status = [
      { text: '已存档', value: 'ARCHIVED' },
      { text: '已调出', value: 'OUT' },
      { text: '换证使用', value: 'REPLACE_USE' },
      { text: '考试调出', value: 'TEST_OUT' },
      { text: '离职调出', value: 'LEAVE_OUT' },
    ]
    this.remaining_months = [
      { text: '失效', value: '0' },
      { text: '0-6个月', value: '1' },
      { text: '6-12个月', value: '2' },
      { text: '12个月以上', value: '3' },
      { text: '未过期', value: '4' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'certificate-detail',
    }
    this.searchDate = {
      interval: true,
      label: '证书有效时间',
    }
    this.searchRemain.roleName = JSON.parse(
      localStorage.getItem('userInfo'),
    ).roleName
    console.log(this.searchRemain)
    console.log('userInfo', JSON.parse(localStorage.getItem('userInfo')))
  },

  data() {
    return {
      selected: [],
      searchRemain: {},
      attachmentRecords: [],
    }
  },

  methods: {
    async exportCertificate() {
      if (this.selected.length > 200) {
        this.$dialog.message.error(`数量过大，请选择200条以内的数据`)
        return
      }
      if (!(await this.$dialog.msgbox.confirm('是否导出所选证书？'))) return
      let arr = this.selected.map((ele) => ele.id)
      const { data } = await this.postAsync(
        '/business/crew/certificate/exportCrewCertificate',
        arr,
      )
      if (data) {
        data.forEach((item) => {
          this.dowloadPDFs(item)
        })
      }
    },
    async dowloadPDFs(item) {
      this.downPDF = `/api/system/file/download?fileName=${encodeURIComponent(
        item.fileName,
      )}&filePath=${item.filePath}`
      console.log(this.downPDF)

      const link = this.$refs.downPDFHref
      link.href = this.downPDF
      link.download = this.extractFilename(this.downPDF)
      link.style.display = 'none'
      document.body.appendChild(link)

      // 模拟点击<a>标签以触发下载
      link.click()
    },
    extractFilename(url) {
      return url.substring(url.lastIndexOf('/') + 1)
    },
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      let newSelected = Array.from(this.selected, (value) => value.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/certificate/deleteCrewCertificate`,
        newSelected,
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
