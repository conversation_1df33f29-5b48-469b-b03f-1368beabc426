<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">报文明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="itemHeader"
      :items="list"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
// costSubjectId	成本类科目sap科目代码	string
// currencyCode	币种编码	string
// currencyId	币种id	string
// id	物理主键	string
// inoutCode	入库单号	string
// inoutMode	出入库表示：I（大写英文字母i）	string
// inoutSingleProjectNo	入库单行项目号	string
// itemCode	物件SAP编号	string
// itemId	物件id	string
// itemName	物件名称	string
// itemNo	物件编码	string
// itemNumber	数量	number
// itemPrice	金额	number
// mainId	主表id	string
// orderNo	采购订单号	string
// supplierCode	供应商编号（供应商sap编号）	string
// supplierId	供应商id	string
// supplierName	供应商名称	string
export default {
  name: 'invoice-sap-049-item',
  created() {
    this.itemHeader = [
      { text: '数据包行号', value: 'rowId' },
      { text: '目的港公司代码', value: 'compCode' },
      { text: '供应商或债权人账号', value: 'vendorId' },
      { text: '姓名', value: 'zName' },
      { text: '应发数成本', value: 'costAmount' },
      { text: '代扣统筹', value: 'whOverall' },
      { text: '代扣公积金', value: 'whFund' },
      { text: '代扣个人所得税', value: 'taxAmt' },
      { text: '实发工资', value: 'actualWages' },
      { text: '业务系统唯一标识符', value: 'busUuid' },
      { text: '利润中心', value: 'prctr' },
      { text: '场景识别', value: 'ifCase' },
      { text: '货币', value: 'currency' },
      { text: '预留1', value: 'resv1' },
      { text: '预留2', value: 'resv2' },
      { text: '预留3', value: 'resv3' },
      { text: '预留4', value: 'resv4' },
    ]
  },
  props: {
    itemId: String,
  },
  data() {
    return {
      itemHeader: [],
      list: [],
    }
  },

  methods: {
    async loadItems() {
      const { data } = await this.getAsync(
        '/business/crew/costManageSap/049item/list',
        { parentId: this.itemId },
      )
      this.list = data
    },
  },

  mounted() {
    this.loadItems()
  },
}
</script>

<style></style>
