<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">报文明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="itemHeader"
      :items="list"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
// costSubjectId	成本类科目sap科目代码	string
// currencyCode	币种编码	string
// currencyId	币种id	string
// id	物理主键	string
// inoutCode	入库单号	string
// inoutMode	出入库表示：I（大写英文字母i）	string
// inoutSingleProjectNo	入库单行项目号	string
// itemCode	物件SAP编号	string
// itemId	物件id	string
// itemName	物件名称	string
// itemNo	物件编码	string
// itemNumber	数量	number
// itemPrice	金额	number
// mainId	主表id	string
// orderNo	采购订单号	string
// supplierCode	供应商编号（供应商sap编号）	string
// supplierId	供应商id	string
// supplierName	供应商名称	string
export default {
  name: 'invoice-sap-071-item',
  created() {
    this.itemHeader = [
      { text: '数据包行号', value: 'rowId' },
      { text: '费项代码', value: 'freCde' },
      { text: '金额', value: 'amount' },
      { text: '货币码', value: 'currency' },
      { text: '船员公司利润中心', value: 'prctr' },
      { text: '单船公司', value: 'vslComp' },
      { text: '单船利润中心', value: 'vslPtr' },
      { text: '备注', value: 'remark' },
      { text: '业务系统唯一标识符', value: 'busUuid' },
    ]
  },
  props: {
    itemId: String,
  },
  data() {
    return {
      itemHeader: [],
      list: [],
    }
  },

  methods: {
    async loadItems() {
      const { data } = await this.getAsync(
        '/business/crew/costManageSap/071item/list',
        { parentId: this.itemId },
      )
      this.list = data
    },
  },

  mounted() {
    this.loadItems()
  },
}
</script>

<style></style>
