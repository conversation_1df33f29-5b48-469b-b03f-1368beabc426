<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selecteds"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      show-expand
      :single-select="false"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.compCode"
            :items="compCodeItems"
            label="船员公司代码"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.status"
            :items="statusItems"
            label="状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.category"
            :items="categoryItems"
            label="分类"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="业务代码"
            outlined
            dense
            clearable
            v-model="searchObj.serviceId"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="数据包唯一标识号"
            outlined
            dense
            clearable
            v-model="searchObj.pkgUuid"
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :loading="aloading"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="coverSAP"
          v-permission="['费用SAP报文:冲销SAP']"
        >
          <v-icon left>mdi-hand-back-right-off-outline</v-icon>
          冲销SAP
        </v-btn>
        <v-btn
          :loading="loading"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="sendSAP"
          v-permission="['费用SAP报文:发送SAP']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          发送SAP
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" small dark color="info">
          暂未失效
        </v-chip>
        <v-chip v-else-if="item.status === 1" small dark color="success">
          有费用项目已经发OA
        </v-chip>
        <v-chip v-else-if="item.status === 2" small dark color="#00695C">
          已完成审批
        </v-chip>
        <v-chip v-else-if="item.status === 22" small dark color="error">
          发送失败
        </v-chip>
        <v-chip v-else-if="item.status === 3" small dark color="#9E9E9E">
          草稿
        </v-chip>
        <v-chip v-else-if="item.status === 4" small dark color="#880E4F">
          已冲销
        </v-chip>
      </template>
      <template v-slot:[`item.errMsg`]="{ item }">
        <v-btn
          outlined
          tile
          class="mx-1"
          color="blue"
          block
          @click="checkErrMsg(item.errMsg)"
        >
          查看
        </v-btn>
      </template>
      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length">
          <invoice-sap-item
            v-if="item.category === 'JFI070'"
            :item-id="item.id"
          />
          <invoice-sap-071-item
            v-if="item.category === 'JFI071'"
            :item-id="item.id"
          />
          <invoice-sap-049-item
            v-if="item.category === 'JFI049'"
            :item-id="item.id"
          />
        </td>
      </template>
    </v-table-searchable>
    <v-dialog v-model="isDialog" width="1000" hide-overlay attach="#mask">
      <v-card>
        <v-card-title>
          错误信息
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-textarea
            readonly
            v-model="dialogText"
            row-height="15"
            rows="4"
            dense
            outlined
          ></v-textarea>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import InvoiceSapItem from './invoice-sap-item.vue'
import InvoiceSap071Item from './invoice-sap-071-item.vue'
import InvoiceSap049Item from './invoice-sap-049-item.vue'

export default {
  components: { InvoiceSapItem, InvoiceSap071Item, InvoiceSap049Item },
  name: 'invoice-sap-list',
  created() {
    this.tableName = '费用SAP报文'
    this.reqUrl = '/business/crew/costManageSap/page'
    this.headers = [
      { text: '', value: 'data-table-expand' },
      { text: '业务代码', value: 'serviceId' },
      { text: '船员公司代码', value: 'compCode', sortable: false },
      { text: '分类', value: 'category', sortable: false },
      { text: '数据包唯一标识号', value: 'pkgUuid', sortable: false },
      { text: '接口数据生成时间', value: 'genTime' },
      { text: '实际记账日期', value: 'pstDate' },
      { text: '业务发生日期', value: 'busDate' },
      { text: '状态', value: 'status' },
      { text: '错误信息', value: 'errMsg' },
    ]
    this.searchDate = {
      label: '',
      value: '',
    }
    this.categoryItems = ['JFI070', 'JFI071', 'JFI049']
    this.statusItems = [
      { text: '暂未失效', value: 0 },
      { text: '有费用项目已经发0A', value: 1 },
      { text: '已完成审批', value: 2 },
      { text: '发送失败', value: 22 },
      { text: '草稿', value: 3 },
      { text: '已冲销', value: 4 },
    ]
  },

  data() {
    return {
      selecteds: [],
      searchObj: {
        year: '',
        status: '',
        category: '',
      },
      loading: false,
      aloading: false,
      dialogText: '',
      isDialog: false,
      compCodeItems: [],
      statusItems: [],
      categoryItems: [],
    }
  },

  computed: {
    selected: {
      get() {
        if (this.selecteds.length > 1) return false
        return this.selecteds[0] || false
      },
      set(val) {
        this.selecteds = val ? [val] : []
      },
    },
    canSend() {
      return (
        this.selecteds.length > 0 &&
        this.selecteds.every(
          (item) => item.status == 23 || item.status == 22 || item.status == 27,
        )
      )
    },
    canYingshe() {
      return (
        this.selecteds.length > 0 &&
        this.selecteds.every((item) => item.status == 20 || item.status == 21)
      )
    },
    canWriteOff() {
      return this.selected.status == 26 && this.selected.originSys != '0002'
    },
  },

  methods: {
    async delVoucher() {
      if (!(await this.$dialog.msgbox.confirm('确定作废此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/costManageSap/handCancel/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('作废成功')
        this.selected = false
      }
      await this.$refs.table.loadTableData()
    },
    async writeOff() {
      if (!(await this.$dialog.msgbox.confirm('确定冲销此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/costManageSap/handTrade/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('冲销成功')
        this.selected = false
      }
      await this.$refs.table.loadTableData()
    },
    async sendSapMsg() {
      if (!(await this.$dialog.msgbox.confirm('确定发送所选记录？'))) return
      const { errorRaw, data } = await this.postAsync(
        '/business/crew/costManageSap/sendBatchSap',
        this.selecteds.map((item) => item.id),
      )
      if (!errorRaw) {
        this.$dialog.message.info(data)
        this.selecteds = []
      }
      await this.$refs.table.loadTableData()
    },

    async mapMsg() {
      if (!(await this.$dialog.msgbox.confirm('确定映射此记录？'))) return
      // const { errorRaw } = await this.getAsync(
      //   `/business/crew/costManageSap/mapSap/${this.selected.id}`,
      // )
      const { errorRaw } = await this.postAsync(
        '/business/crew/costManageSap/sendBatchMapSap',
        this.selecteds.map((item) => item.id),
      )
      if (!errorRaw) {
        this.$dialog.message.success('映射成功')
        this.selecteds = []
      }
      await this.$refs.table.loadTableData()
    },
    async success() {
      if (!(await this.$dialog.msgbox.confirm('确定成功此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/costManageSap/executeSap/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('执行成功')
        this.selected = false
      }
      await this.$refs.table.loadTableData()
    },
    async fail() {
      if (!(await this.$dialog.msgbox.confirm('确定失败此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/costManageSap/executeFailSap/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('执行失败')
        this.selected = false
      }
      await this.$refs.table.loadTableData()
    },
    async refreshVoucher() {
      const { errorRaw } = await this.postAsync(
        `/monitor/job/run?jobId=1592863103117307123`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('执行成功')
      }
      await this.$refs.table.loadTableData()
    },
    async downloadExcel() {
      this.loading = true
      let params = { ...this.$refs.table.searchRemain }
      params = {
        ...params,
        // fuzzyParam: this.$refs.table.fuzzyParam,
        // shipCode: this.$refs.table.ship,
        // fromTime: this.dates?.start?.toISOString()?.split('T')?.[0],
        // toTime: this.dates?.end?.toISOString()?.split('T')?.[0],
        // fromTime2: this.dates2?.start?.toISOString()?.split('T')?.[0],
        // toTime2: this.dates2?.end?.toISOString()?.split('T')?.[0],
      }
      await this.getBlobDownload(
        '/business/crew/costManageSap/excelExport',
        params,
        // 时间戳后四位
        `费用SAP报文-${new Date().getTime().toString().slice(-4)}.xlsx`,
      )
      this.loading = false
    },
    async sendSAP() {
      if (!(await this.$dialog.msgbox.confirm('是否确认发送SAP'))) return
      if (this.selecteds.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }

      this.loading = true
      try {
        // 循环处理每个选中项
        const results = await Promise.all(
          this.selecteds.map(async (item) => {
            const { errorRaw } = await this.getAsync(
              '/business/crew/costManageSap/sendSap',
              { id: item.id },
            )
            return { success: !errorRaw, id: item.id }
          }),
        )

        // 统计结果
        const successCount = results.filter((r) => r.success).length
        const failCount = results.length - successCount

        if (failCount > 0) {
          this.$dialog.message.error(
            `成功 ${successCount} 条，失败 ${failCount} 条`,
          )
        } else {
          this.$dialog.message.success(`全部发送成功（共 ${successCount} 条）`)
        }
      } finally {
        this.loading = false
        this.selecteds = []
        await this.$refs.table.loadTableData()
      }
    },
    //冲销SAP
    async coverSAP() {
      if (!(await this.$dialog.msgbox.confirm('是否确认冲销SAP'))) return
      if (this.selecteds.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }

      this.sendUrl = '/business/crew/costManageSap/coverSAP'
      this.aloading = true
      const { errorRaw } = await this.getAsync(this.sendUrl, {
        id: this.selecteds[0].id,
      })
      this.aloading = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('冲销完成')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async getCompCodeItems() {
      const { errorRaw, data } = await this.getAsync(
        `/system/dict-data/getValuesByDictType`,
        { dictType: 'ship_crew_conpany' },
      )
      if (errorRaw) {
        this.$dialog.message.error('船员公司代码列表获取失败')
        return
      }
      this.compCodeItems = data
    },
    checkErrMsg(value) {
      this.isDialog = true
      this.dialogText = value
    },
    closeForm() {
      this.isDialog = false
      this.dialogText = ''
    },
  },

  async mounted() {
    await this.getCompCodeItems()
  },
}
</script>

<style></style>
