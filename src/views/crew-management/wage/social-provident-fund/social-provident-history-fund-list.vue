<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            v-model="crewName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="身份证号"
            outlined
            dense
            v-model="idNumber"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            v-model="searchRemain.crewPost"
            clearable
          ></v-ship-station>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          color="#0D47A1"
          outlined
          tile
          class="mx-1"
          @click="GoTOHistory"
          v-permission="['船员历史社保公积金信息:查看当前设备公积金信息']"
        >
          <v-icon left dark>mdi-eye</v-icon>
          查看当前设备公积金信息
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
const debounce = function (func, delay = 300) {
  let timer = null
  return function () {
    let context = this
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(context, arguments)
    }, delay)
  }
}

export default {
  name: 'social-provident-history-fund-list',
  created() {
    this.tableName = '船员历史社保公积金信息'
    this.reqUrl = '/business/crew/salaryPart/insurance/pageHistory'
    this.headers = [
      { text: '船员姓名', value: 'crewName' },
      { text: '船员身份证号', value: 'idNumber' },
      { text: '船员岗位', value: 'crewPost' },
      { text: '公积金应扣金额（￥）', value: 'accumulationBase' },
      { text: '社保应扣金额（￥）', value: 'insuranceBase' },
      { text: '年月', value: 'yearAndMonth' },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: false,
      searchRemain: {
        crewName: '',
        idNumber: '',
      },
      crewName: '',
      idNumber: '',
    }
  },
  watch: {
    crewName() {
      this.debounceLoadTableData(this)
    },
    idNumber() {
      this.debounceLoadTableData(this)
    },
    searchRemain: {
      handler(value) {
        console.log(value)
      },
      deep: true,
    },
  },

  methods: {
    debounceLoadTableData: debounce(function (that) {
      that.searchRemain.crewName = that.crewName
      that.searchRemain.idNumber = that.idNumber
    }, 500),
    GoTOHistory() {
      this.$router.push({ name: 'social-provident-fund-list' })
    },
  },

  mounted() {},
}
</script>

<style></style>
