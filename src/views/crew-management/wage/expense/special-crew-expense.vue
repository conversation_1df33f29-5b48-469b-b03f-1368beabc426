<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :search-date="searchDate"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="申请人姓名"
            outlined
            dense
            v-model="searchRemain.applyPerson"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="所属地"
            outlined
            dense
            v-model="searchRemain.basePlace"
            :items="[
              { text: '上海', value: '上海' },
              { text: '山东', value: '山东' },
            ]"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="费用项目"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.itemName"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-year-month-picker
            :use-current="false"
            label="年月选择"
            v-model="searchRemain.yearAndMonth"
            outlined
            dense
          ></v-year-month-picker>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="工资公司"
            outlined
            dense
            required
            :items="companyInfo"
            v-model="searchRemain.salaryPropertyId"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="杂费公司"
            outlined
            dense
            required
            :items="companyInfo"
            v-model="searchRemain.applyPropertyId"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-import-more-btn
          importUrl="/business/crew/salary/apply/special/excelsImport"
          @importSuccess="importSuccess"
          @importError="importError"
        ></v-import-more-btn> -->

        <v-btn
          :disabled="!selected"
          outlined
          dense
          tile
          color="info"
          class="mx-1"
          :loading="loading3"
          @click="sendOA"
          v-permission="['费用申请:发起审批']"
        >
          <v-icon>mdi-arrow-up-bold</v-icon>
          发起审批
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          dense
          tile
          color="error"
          class="mx-1"
          :loading="loading4"
          @click="cancelApproval"
          v-permission="['费用申请:取消审批']"
        >
          <v-icon>mdi-arrow-u-down-left</v-icon>
          取消审批
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/invoice-management/special-crew-expense-detail/new"
          v-permission="['特殊船员杂费:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['费用申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" color="" dark small>暂未生效</v-chip>
        <v-chip v-else-if="item.status === 1" color="info" dark small>
          有费用项目已经发OA
        </v-chip>
        <v-chip v-else-if="2" color="success" dark small>OA通过</v-chip>
        <v-chip v-else color="error" dark small>OA未通过</v-chip>
      </template>
    </v-table-searchable>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="dialog1"
    >
      <v-card>
        <v-card-title>
          生成特殊船员杂费
          <v-spacer></v-spacer>
          <v-icon @click="closeDialog">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="2">
                  <v-year-month-picker
                    outlined
                    dense
                    v-model="yearAndMonth"
                    :clearable="false"
                  ></v-year-month-picker>
                </v-col>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    :loading="loading"
                    color="success"
                    class="mx-1"
                    v-permission="['特殊船员杂费:生成特殊船员杂费']"
                    @click="generateSpecial"
                    block
                  >
                    <v-icon left>mdi-cog</v-icon>
                    生成
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
// import moment from 'moment'
export default {
  name: 'special-crew-expense',
  created() {
    this.tableName = '特殊船员杂费汇总信息'
    this.reqUrl = '/business/crew/salary/apply/special/page'
    this.headers = [
      { text: '申请人姓名', value: 'applyPerson' },
      { text: '费用项目', value: 'itemName' },
      { text: '所属地', value: 'basePlace' },

      { text: '币种', value: 'billType' },
      { text: '金额', value: 'totalMoney' },
      { text: '备注', value: 'remark' },
      { text: '发起时间', value: 'applyDate' },
      { text: '状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'special-crew-expense-detail',
    }
    this.searchDate = {
      interval: true,
      label: '发起申请起末时间',
    }
    // this.searchRemain.yearAndMonth = moment()
    //   .subtract(1, 'months')
    //   .format('YYYY-MM')
    // console.log('yearAndMonth', this.searchRemain.yearAndMonth)
  },

  data() {
    return {
      selected: false,
      loading: false,
      dialog1: false,
      // searchRemain: {
      //   yearAndMonth: '',
      // },
      searchRemain: {},
      newInfo: [],
      companyInfo: [],
    }
  },

  methods: {
    async generateSpecial() {
      if (!(await this.$dialog.msgbox.confirm('是否确认生成？'))) return
      this.loading = true
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/apply/special/detail/gen`,
        { yearAndMonth: this.yearAndMonth },
      )
      this.loading = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.closeDialog()
      await this.$refs.table.loadTableData()
    },
    openDialog() {
      this.dialog1 = true
    },
    closeDialog() {
      this.dialog1 = false
    },
    async sendOA() {
      if (!(await this.$dialog.msgbox.confirm('是否发送OA审批？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary//apply/special/sendOA`,
        {
          id: this.selected.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/apply/special/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/item/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data.map((val) => val?.itemName)
    },
    async getCreSecond() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.companyInfo = data
    },
  },

  async mounted() {
    await this.getCreFirst()
    await this.getCreSecond()
  },
}
</script>

<style></style>
