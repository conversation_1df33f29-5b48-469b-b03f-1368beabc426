<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['费用申请:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      :can-save="canSave()"
    >
      <template v-slot:topcontent v-if="detailInfo.auditParams">
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit
              ref="audit"
              :auditParams="auditParams"
              :showParams="isTrue"
            ></v-audit>
          </v-card-text>
        </v-form>
      </template>
      <template #费用申请基本信息>
        <v-card-text>
          <v-form ref="form">
            <v-container fluid>
              <v-row>
                <v-col cols="12" md="4">
                  <v-text-field
                    label="项目名称"
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.itemName"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="4">
                  <v-text-field
                    label="供应商"
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.supplierName"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="申请人姓名"
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.applyPerson"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="船员公司"
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.area"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="申请日期"
                    outlined
                    dense
                    useToday
                    disabled
                    v-model="detailInfo.createTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="币种"
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.billType"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2" v-if="newCard !== `new`">
                  <v-text-field
                    label="金额"
                    outlined
                    readonly
                    dense
                    disabled
                    v-model="detailInfo.totalMoney"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="实际付款币种"
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.realBillType"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    label="费用承担方"
                    outlined
                    dense
                    :items="[
                      { text: '船东承担', value: false },
                      { text: '船员公司承担', value: true },
                    ]"
                    disabled
                    v-model="detailInfo.shareFlag"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="8">
                  <v-text-field
                    label="备注"
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.remark"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import VAudit from '@/components/v-audit.vue'

export default {
  components: { VAudit },
  name: 'expense-s-external-detail',
  async created() {
    this.backRouteName = 'expense-items-external'
    this.subtitles = ['费用申请基本信息']
    this.headers = [
      { text: '申请人姓名', value: 'applyPerson' },
      { text: '付款项目', value: 'itemName' },
      { text: '币种', value: 'billType' },
      { text: '金额', value: 'totalMoney' },
      { text: '供应商', value: 'supplierName' },
      { text: '备注', value: 'remark' },
    ]
    this.projectHeaders = [
      { text: '项目名称', value: 'itemName' },
      { text: '部门', value: 'dept' },
      { text: '工资类别', value: 'salaryType' },
      { text: '票据形式', value: 'billForm' },
      { text: '支付方式', value: 'payType' },
      { text: '单独支付标识', value: 'singlePayFlag' },
      { text: '是否计税', value: 'individualTaxFlag' },
    ]
    this.crewheaders = [
      // { text: '船名', value: 'shipcode' },
      { text: '船员姓名', value: 'payeeUserId' },
      { text: '付款项目', value: 'itemName' },
      { text: '币种', value: 'feeType' },
      { text: '银行卡号', value: 'rmbCard' },
    ]
    this.detailHeaders = [
      { text: '船名', value: 'shipName' },
      { text: '付款项目', value: 'itemName' },
      { text: '总计', value: 'amount' },
      { text: '操作', value: 'export' },
    ]

    await this.getcurrencyInfo()
  },

  data() {
    return {
      title: '新增费用申请信息',
      selected: false,
      detailInfo: {
        applyPerson: this.$local.data.get('userInfo').nickName,
        billType: '',
        shareType: 1,
      },
      localcurrencyInfo: [],
      auditParams: false,
      initialData: {},
      awardData: {},
      initSelected: {},
      initSelectedItemId: {},
      dialog: false,
      inputDialog: false,
      isEdit: false,
      isInput: false,
      isChosen: false,
      creSearchRemain: {
        status: 1,
      },
      totalData: {},
    }
  },
  computed: {
    isTrue: function () {
      return (
        this.detailInfo.auditParams.taskId &&
        !this.detailInfo.auditParams.isReject
      )
    },
    newCard: function () {
      console.log('toatalData', this.totalData)
      return this.$route.params.id
    },
    oaStatus: function () {
      return this.detailInfo.status
    },
  },

  watch: {},
  props: {
    open: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    canSubmit() {
      return this.detailInfo.status !== 2
    },
    canSave() {
      return true
    },
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },

    select() {
      console.log('flag', this.$route.query.flag)
    },
    selectItemName(val) {
      this.detailInfo.itemId = val.id
    },
    async getDetailInfo() {
      if (this.$route.params.id === `new`) return
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/item/external/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }

      this.title = `${data.applyPerson}-外部付款费用申请信息`
      this.detailInfo = data
      this.auditParams = data.auditParams
      console.log('this.auditParams', this.auditParams)
      this.getTotalInfo()
    },

    async save(goBack) {
      // if (!(await this.$dialog.msgbox.confirm('当前基础信息是否确认无误？')))
      //   return
      // if (this.$refs.aform && !this.$refs.aform.validate()) {
      //   this.loading = false
      //   return
      // }
      // const url = (this.$route.params.id =
      //   '/business/crew/salary/item/external/update')
      // const { errorRaw } = await this.postAsync(url, this.detailInfo)
      // if (errorRaw) {
      //   return
      // }
      // this.$dialog.message.success('保存成功')

      const error = await this.$refs.audit.submit()
      if (error) {
        this.$dialog.message.error('审批发起失败')
        return
      }
      this.$dialog.message.success('审批发起成功')

      goBack()
    },
    async submit() {
      console.log('执行到submit了')
      if (!this.detailInfo.auditParams) {
        if (!(await this.$dialog.msgbox.confirm('是否确认提交审批？'))) return
        const url =
          this.$route.params.id === 'new'
            ? '/business/crew/salary/apply/save'
            : '/business/crew/salary/apply/update'
        const { errorRaw1 } = await this.postAsync(url, this.detailInfo)
        if (errorRaw1) {
          return
        }

        const { errorRaw } = await this.getAsync(
          `/business/crew/salary/salaryItemApply/submit`,
          {
            id: this.$route.params.id,
          },
        )
        if (errorRaw) {
          this.sloading = false
          return
        }
        this.$dialog.message.success('成功提交审批')
      } else {
        if (!(await this.$dialog.msgbox.confirm('是否确认提交审批？'))) return
        const errorRaw = await this.$refs.audit.submit()
        if (errorRaw) {
          this.sloading = false
          return
        }
        this.$dialog.message.success('成功提交审批')
      }
      this.sloading = false
      this.closeAndTo(this.backRouteName, {})
    },
    addExpense() {
      console.log(this.detailInfo.itemId)

      this.isEdit = true
      this.initialData = {
        applyId: this.$route.params.id,
        feeType: this.detailInfo.billType,
        itemName: this.detailInfo.itemName,
        itemId: this.detailInfo.itemId,
      }
      this.dialog = true
    },
    async inputExpense() {
      this.isChosen = true
      console.log(this.isChosen)
      this.inputDialog = true
      this.total = false
    },
    async inputCrewPerformance() {
      this.isChosen = false
      this.inputDialog = true
      this.total = false
    },
    async inputAllExpense() {
      this.isChosen = true
      console.log(this.isChosen)
      this.inputDialog = true
      this.total = true
    },
    async inputAllCrewPerformance() {
      this.isChosen = false
      this.inputDialog = true
      this.total = true
    },
    editElectronicchart(item) {
      if (this.detailInfo.status != 0) {
        return this.$dialog.message.warning('只有待提交状态才能编辑')
      }
      this.initialData = item
      this.initSelected = {
        name: item.payeeName,
        userId: item.payeeUserId,
      }
      this.initSelectedItemId = {
        itemName: item.itemName,
        id: item.id,
      }
      this.isEdit = false
      this.dialog = true
    },

    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/crew/salary/apply/detail/delete',
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      await this.getDetailInfo()
    },
    async success() {
      this.selected = false
      await this.getDetailInfo()
    },
  },
  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
