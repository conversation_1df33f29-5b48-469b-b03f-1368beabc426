<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['费用分摊:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-save="false"
    >
      <template #费用分摊基本信息>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-text-field
                label="所属年月"
                outlined
                readonly
                dense
                v-model="detailInfo.yearAndMonth"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否分摊完成"
                outlined
                dense
                readonly
                :items="[
                  { text: '已完成', value: true },
                  { text: '未完成', value: false },
                ]"
                v-model="detailInfo.sumFlag"
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否已发起SAP"
                outlined
                dense
                readonly
                :items="[
                  { text: '已发送SAP', value: true },
                  { text: '未发送SAP', value: false },
                ]"
                v-model="detailInfo.sapFlag"
              ></v-select>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <template #管理费用按钮>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="black"
          class="mx-1"
          @click="allocateOpen"
          v-permission="['费用分摊:分摊费用']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          分摊
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="openDetailInfo"
          v-permission="['费用分摊:查看']"
        >
          <v-icon left>mdi-magnify</v-icon>
          查看
        </v-btn>
      </template>
      <template #管理费用>
        <v-table-list
          item-key="id"
          :headers="detail2Headers"
          :items="itemList"
          v-model="selected"
        >
          <template v-slot:[`item.completeFlag`]="{ item }">
            <v-chip v-if="!item.completeFlag" small dark>未分摊完成</v-chip>
            <v-chip v-else small color="success" dark>已分摊完成</v-chip>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="totalDialog"
    >
      <v-card>
        <v-card-title>
          合计
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-table-list
            :headers="detailHeaders"
            :items="this.itemDetailList"
            :show-select="false"
          ></v-table-list>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="alloDialog"
    >
      <v-card>
        <v-card-title>
          分摊详情
          <v-spacer></v-spacer>
          <v-icon @click="closeAlloForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <div>总金额: {{ totalManagementCost }}</div>
          <div>已分摊金额: {{ totalAmount }}</div>
          <div>未分摊金额: {{ unallocatedAmount }}</div>
          <div>
            <v-btn @click="resetAllocation">
              <v-icon left>mdi-plus-circle</v-icon>
              全部清除数据
            </v-btn>
          </div>
          <v-table-list
            :headers="alloHeaders"
            :items="this.alloShipList"
            :show-select="false"
          >
            <template v-slot:[`item.amount`]="{ item }">
              <v-text-field
                single-line
                dense
                v-model="item.amount"
                type="number"
              ></v-text-field>
            </template>
          </v-table-list>
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            @click="saveDetail"
            block
            :loading="loading"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            保存
          </v-btn>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  name: 'expenses-invoice-detail',
  created() {
    this.backRouteName = 'expenses-invoice-list'
    this.subtitles = ['费用分摊基本信息', '管理费用']
    this.detail2Headers = [
      { text: '费用名称', value: 'itemName' },
      { text: '费用分类', value: 'itemCategory' },
      { text: '金额', value: 'amount' },
      { text: '费用代码', value: 'itemCode' },
      { text: '是否分摊完成', value: 'completeFlag' },
    ]
    this.detailHeaders = [
      { text: '船员', value: 'name' },
      { text: '来源', value: 'shipName' },
      { text: '金额', value: 'amount' },
    ]
    this.alloHeaders = [
      { text: '船名', value: 'shipName' },
      { text: '配员标准', value: 'staffStandards' },
      { text: '金额', value: 'amount' },
    ]
  },
  computed: {
    totalAmount() {
      return this.alloShipList
        .reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
        .toFixed(2)
    },
    unallocatedAmount() {
      const result = this.totalManagementCost - this.totalAmount
      // console.log('amount', result)
      return result.toFixed(2)
    },
  },
  data() {
    return {
      itemDetailList: [],
      totalDialog: false,
      loading2: false,
      title: '费用分摊详情',
      detailInfo: {
        detail2: [],
      },
      itemList: [],
      items: [],
      detail1Headers: [],
      detailHeaders: [],
      alloHeaders: [],
      alloShipList: [],
      dialog: false,
      alloDialog: false,
      selected: false,
      loading: false,
      totalManagementCost: 0,
      nollocatedAmount: 0,
    }
  },
  watch: {
    items: {
      handler(val) {
        for (let name in val[0]) {
          const hName = name.slice(9)
          this.detailInfo.detail1.forEach((ele) => {
            if (ele.itemName === hName) {
              ele.itemMoney = val[0][name]
            }
          })
        }
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    resetAllocation() {
      this.alloShipList.forEach((item) => {
        item.amount = '0.00'
      })
      // this.totalManagementCost = '0.00' // 如果需要清零总管理费用
    },
    // 方法来计算初始的分摊金额
    calculateInitialAllocation() {
      const totalManagementCost = parseFloat(this.totalManagementCost) // 总管理费用
      let totalWeight = 0
      this.alloShipList.forEach((item) => {
        totalWeight += parseFloat(item.staffStandards || 0)
      })

      if (totalWeight > 0) {
        this.alloShipList.forEach((item) => {
          const weight = parseFloat(item.staffStandards)
          item.amount = ((totalManagementCost * weight) / totalWeight).toFixed(
            2,
          )
        })
      }
    },
    closeForm() {
      this.totalDialog = false
    },
    async saveDetail() {
      this.loading = true
      if (parseFloat(this.unallocatedAmount) !== 0) {
        this.$dialog.message.error('未分配金额不为零，无法提交')
        this.loading = false
        return // 中断执行
      }
      if (this.detailInfo.sumFlag) {
        this.$dialog.message.error('该项目已进入汇总，无法操作')
        this.loading = false
        return // 中断执行
      }
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/costSum/item/saveBatch`,
        {
          details: this.alloShipList,
          allocationItem: this.selected,
          yearAndMonth: this.detailInfo.yearAndMonth,
          company: this.detailInfo.company,
        },
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.loading = false
      this.$dialog.message.success('保存成功')
      this.alloDialog = false
      this.getItemList()
      this.selected = false
    },
    closeAlloForm() {
      this.alloDialog = false
    },
    async openDetailInfo() {
      // console.log('selected', this.selected)
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/allocation/item/detail`,
        {
          id: this.selected.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.itemDetailList = data
      this.totalDialog = true
    },
    async allocateOpen() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/costSum/oriItem`,
        {
          company: this.detailInfo.company,
          parentId: this.selected.id,
        },
      )
      if (errorRaw) {
        return
      }
      // console.log('111data', data)
      this.alloShipList = data // 假设后端返回了需要的数据
      // console.log('alloShipList', this.alloShipList)
      this.totalManagementCost = this.selected.amount // 获取总管理费用
      // console.log(this.selected.completeFlag)
      if (!this.selected.completeFlag) {
        this.calculateInitialAllocation() // 计算初始的分摊金额
      }
      this.alloDialog = true
    },
    async save(goBack) {
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/allocation/add`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/allocation/detail`,
        {
          id: this.$route.params.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.title = `${data.company}-${data.yearAndMonth}-费用分摊详情`
      this.detailInfo = data
      // console.log('detailInfo', this.detailInfo)
    },
    async getItemList() {
      // console.log('ttttttttttt')
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/allocation/item/list`,
        {
          id: this.$route.params.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.itemList = data
    },
    create() {
      this.dialog = true
    },
    success(val) {
      if (
        this.detailInfo.detail2.find((ele) => ele.position === val.position)
      ) {
        this.$dialog.message.error('该岗位已存在，请勿重复添加')
        return
      }
      this.detailInfo.detail2.push(val)
    },
  },
  async mounted() {
    await this.getItemList()
    await this.getDetailInfo()
  },
}
</script>

<style></style>
