<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="isShow">
        <v-card-title>
          {{ isEdit ? '新增' : '修改' }}---船员对外付款费用
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-dialog-select
                label="付款项目"
                dense
                outlined
                :fuzzy-label="fuzzyLabel"
                v-model="salaryItem.itemName"
                table-name="付款项目选择"
                :headers="projectHeaders"
                req-url="/business/crew/salary/outPayItem/page"
                itemText="itemName"
                item-value="itemName"
                @select="selectItemName"
                :init-selected="initSelectedItemId1"
                :req-params="{ externalFlag: 1 }"
              >
                <template v-slot:[`item.dept`]="{ item }">
                  {{
                    item.dept === '1'
                      ? '上海船员公司'
                      : item.dept === '2'
                      ? '山东船员公司'
                      : '上海船员部'
                  }}
                </template>
                <template v-slot:[`item.salaryType`]="{ item }">
                  {{ item.salaryType === 1 ? '工资类' : '费用类' }}
                </template>
                <template v-slot:[`item.billForm`]="{ item }">
                  {{
                    item.billForm === 1
                      ? '帐单及形式发票'
                      : item.billForm === 2
                      ? '发票'
                      : item.billForm === 3
                      ? '明细表'
                      : '账单'
                  }}
                </template>
                <template v-slot:[`item.singlePayFlag`]="{ item }">
                  {{
                    item.singlePayFlag === 1
                      ? '单独支付'
                      : item.singlePayFlag === 2
                      ? '并入工资'
                      : '其他'
                  }}
                </template>
                <template v-slot:[`item.individualTaxFlag`]="{ item }">
                  {{ item.individualTaxFlag ? '计税' : '不计税' }}
                </template>
              </v-dialog-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-supply-select2
                v-model="searchRemain.supplierId"
                label="供应商"
                outlined
                itemText="name"
                item-value="id"
                @select="selectSupplier"
                dense
                required
              ></v-supply-select2>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                v-model="salaryItem.billType"
                :items="[
                  { text: '人民币', value: 'CNY' },
                  { text: '美元', value: 'USD' },
                ]"
                label="币别"
                dense
                outlined
                required
              ></v-select>
            </v-col>
            <!-- <v-col cols="12" md="2">
              <v-text-field
                label="币别"
                outlined
                dense
                v-model="salaryItem.billType"
              ></v-text-field>
            </v-col> -->
            <v-col cols="12" md="2">
              <v-text-field
                label="金额"
                outlined
                dense
                v-model="salaryItem.totalMoney"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="船员公司"
                outlined
                dense
                :items="[
                  { text: '山东', value: '山东' },
                  { text: '上海', value: '上海' },
                ]"
                v-model="salaryItem.area"
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                label="业务日期"
                outlined
                dense
                useToday
                v-model="salaryItem.applyDate"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                v-model="salaryItem.realBillType"
                :items="[
                  { text: '人民币', value: 'CNY' },
                  { text: '美元', value: 'USD' },
                ]"
                label="实际付款币别"
                dense
                outlined
                required
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="费用承担方"
                outlined
                dense
                :items="[
                  { text: '船东承担', value: 0 },
                  { text: '船员公司承担', value: 1 },
                ]"
                v-model="salaryItem.shareFlag"
              ></v-select>
            </v-col>
            <v-col cols="12" md="8">
              <v-text-field
                label="备注"
                outlined
                dense
                v-model="salaryItem.remark"
              ></v-text-field>
            </v-col>
            <v-col v-if="isAppoint" cols="12" md="2">
              <v-ship-select v-model="salaryItem.shipCode"></v-ship-select>
            </v-col>
          </v-row>
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            :loading="loading1"
            @click="save"
            block
            v-permission="['船员费用对外付款:保存']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            保存
          </v-btn>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :search-date="searchDate"
      :single-select="false"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="申请人姓名"
            outlined
            dense
            v-model="searchRemain.applyPerson"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="审批状态"
            outlined
            dense
            required
            :items="statusList"
            v-model="searchRemain.status"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="费用项目"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.itemName"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-supply-select2
            v-model="searchRemain.supplierId"
            label="供应商"
            outlined
            clearable
            dense
          ></v-supply-select2>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="addExpense"
          v-permission="['船员费用对外付款:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editElectronicchart"
          v-permission="['船员费用对外付款:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="info"
          class="mx-1"
          :loading="loading2"
          @click="submit"
          v-permission="['船员费用对外付款:发起记账前审批']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          发起记账前审批
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          :loading="loading6"
          @click="cancel"
          v-permission="['船员费用对外付款:取消记账前审批']"
        >
          <v-icon left>mdi-arrow-u-down-left</v-icon>
          取消记账前审批
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="sendSAPNew"
          v-permission="['船员费用对外付款:发送SAP记账']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          发送SAP记账（新）
        </v-btn>
        <!--        <v-btn-->
        <!--          :disabled="selected.length === 0"-->
        <!--          outlined-->
        <!--          tile-->
        <!--          color="info"-->
        <!--          class="mx-1"-->
        <!--          :loading="loading4"-->
        <!--          @click="sendSAP"-->
        <!--          v-permission="['船员费用对外付款:发送SAP记账']"-->
        <!--        >-->
        <!--          <v-icon left>mdi-arrow-up-bold</v-icon>-->
        <!--          发送SAP记账-->
        <!--        </v-btn>-->

        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="summyExpense"
          :loading="loading5"
          v-permission="['船员费用对外付款:汇总发送OA审批']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          汇总发送OA审批
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="addExpense"
          v-permission="['船员费用对外付款:删除']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          删除
        </v-btn>
        <v-btn
          color="#0D47A1"
          outlined
          tile
          class="mx-1"
          @click="GoTOTotal"
          v-permission="['船员当前社保公积金查询:查看历史设备公积金信息']"
        >
          <v-icon left dark>mdi-eye</v-icon>
          查看汇总发送信息
        </v-btn>
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['费用申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" color="" dark small>草稿</v-chip>
        <v-chip v-else-if="item.status === 1" color="info" dark small>
          已发送SAP
        </v-chip>
        <v-chip v-else-if="item.status === 2" color="success" dark small>
          已汇总
        </v-chip>
        <v-chip v-else-if="item.status === 3" color="success" dark small>
          已发送OA审批
        </v-chip>
        <v-chip v-else-if="item.status === 4" color="success" dark small>
          已完成
        </v-chip>
        <v-chip v-else-if="item.status === 5" color="info" dark small>
          记账前审批进行中
        </v-chip>
        <v-chip v-else-if="item.status === 6" color="success" dark small>
          记账前审批完成
        </v-chip>
        <v-chip v-else color="error" dark small>OA未通过</v-chip>
      </template>
      <template v-slot:[`item.shareFlag`]="{ item }">
        <v-chip v-if="item.shareFlag === false" dark small>船东承担</v-chip>
        <v-chip v-else-if="item.shareFlag === true" dark small>
          船员公司承担
        </v-chip>
        <v-chip v-else color="error" dark small>错误状态</v-chip>
      </template>
    </v-table-searchable>
    <!--    <v-dialog v-model="isDialog" width="1000" hide-overlay attach="#mask">-->
    <!--      <v-card>-->
    <!--        <v-card-title>-->
    <!--          备注-->
    <!--          <v-spacer></v-spacer>-->
    <!--          <v-icon @click="closeCard">mdi-close</v-icon>-->
    <!--        </v-card-title>-->
    <!--        <v-card-text>-->
    <!--          <v-form ref="form">-->
    <!--            <v-container>-->
    <!--              <v-row>-->
    <!--                <v-col cols="12" md="3">-->
    <!--                  <vs-date-picker-->
    <!--                    v-model="recordDate"-->
    <!--                    label="日期"-->
    <!--                    :rules="[rules.required]"-->
    <!--                    required-->
    <!--                    outlined-->
    <!--                    dense-->
    <!--                  ></vs-date-picker>-->
    <!--                </v-col>-->
    <!--                <v-col cols="12">-->
    <!--                  <v-btn-->
    <!--                    :loading="loading3"-->
    <!--                    outlined-->
    <!--                    tile-->
    <!--                    color="success"-->
    <!--                    class="mx-1"-->
    <!--                    @click="sendSAPNew"-->
    <!--                    block-->
    <!--                  >-->
    <!--                    <v-icon left>mdi-plus-circle</v-icon>-->
    <!--                    {{ '发送' }}-->
    <!--                  </v-btn>-->
    <!--                </v-col>-->
    <!--              </v-row>-->
    <!--            </v-container>-->
    <!--          </v-form>-->
    <!--        </v-card-text>-->
    <!--      </v-card>-->
    <!--    </v-dialog>-->
  </v-container>
</template>

<script>
import currencyHelper from '@/mixin/currencyHelper'
import vShipSelect from '@/components/v-ship-select.vue'
export default {
  components: { vShipSelect },
  name: 'expense-items-external',
  mixins: [currencyHelper],
  created() {
    this.tableName = '对外付款费用信息查询详情'
    this.reqUrl = '/business/crew/salary/item/external/detail/page'
    this.headers = [
      { text: '申请人姓名', value: 'applyPerson' },
      { text: '费用项目', value: 'itemName' },
      { text: '供应商', value: 'supplierName' },
      { text: '费用金额', value: 'totalMoney' },
      { text: '币别', value: 'billType' },
      { text: '船员公司', value: 'area' },
      { text: '实际付款币别', value: 'realBillType' },
      { text: '备注', value: 'remark' },
      { text: '业务日期', value: 'applyDate' },
      { text: '费用承担方', value: 'shareFlag' },

      { text: '状态', value: 'status' },
    ]
    this.projectHeaders = [
      { text: '项目名称', value: 'itemName' },
      // { text: '部门', value: 'dept' },
      { text: '项目代码', value: 'itemCode' },
      { text: '项目类别', value: 'itemCategory' },
      // { text: '费用科目', value: 'parentSap' },
      // { text: '单独支付标识', value: 'singlePayFlag' },
      // { text: '是否计税', value: 'individualTaxFlag' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'expense-items-external-detail',
    }
    this.searchDate = {
      interval: true,
      label: '发起申请起末时间',
    }
  },

  data() {
    return {
      selected: [],
      searchRemain: {},
      newInfo: [],
      expenseInfo: [],
      isShow: false,
      isDialog: false,
      isEdit: false,
      recordDate: '',
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      loading5: false,
      loading6: false,
      rules: {
        required: (v) => !!v || v == '0' || '必填项不能为空',
      },
      salaryItem: {},
      statusList: [
        { text: '草稿', value: 0 },
        { text: '已发送SAP', value: 1 },
        { text: '已汇总', value: 2 },
        { text: '已发送OA审批', value: 3 },
        { text: '已完成', value: 4 },
        { text: '记账前审批进行中', value: 5 },
        { text: '记账前审批完成', value: 6 },
      ],
    }
  },

  methods: {
    editElectronicchart() {
      this.salaryItem = { ...this.selected[0] }
      this.isEdit = false
      this.isShow = true
    },
    async submit() {
      const userId = this.$local.data.get('userInfo').id
      console.log('userId', userId)
      console.log('selected', this.selected)
      if (this.selected.some((e) => e.userId !== userId)) {
        this.$dialog.message.error('非申请人不可操作')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定发起记账审批吗？'))) return
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      this.loading2 = true
      let totalStatus = this.selected.map((ele) => ele.status)
      // 判断是否全部为草稿状态
      if (totalStatus.filter((element) => element !== 0).length > 0) {
        this.$dialog.message.error('请选择草稿状态记录')
        this.loading2 = false
        return
      }
      let arr = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/item/external/submit`,
        arr,
      )
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('汇总成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async cancel() {
      if (!(await this.$dialog.msgbox.confirm('确定取消记账前审批吗？'))) return
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      this.loading6 = true
      let totalStatus = this.selected.map((ele) => ele.status)
      // 判断是否全部为记账前审批进行中状态
      if (totalStatus.filter((element) => element !== 5).length > 0) {
        this.$dialog.message.error('请选择记账前审批进行中的状态记录')
        this.loading6 = false
        return
      }
      let arr = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/item/external/cancel`,
        arr,
      )
      this.loading6 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    // 发送SAP
    async sendSAP() {
      if (!(await this.$dialog.msgbox.confirm('是否确认发送sap？'))) return
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      let totalStatus = this.selected.map((ele) => ele.status)
      // 判断是否全部为草稿状态
      // 检查 totalStatus 数组中的每个元素是否都是 6 或 22
      if (!totalStatus.every((element) => element === 6 || element === 22)) {
        this.$dialog.message.error('请选择记账前审批已完成记录')
        return
      }
      this.loading4 = true
      let arr = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/item/external/submitSAP`,
        {
          idList: arr,
        },
      )
      this.loading4 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('发送成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    // openSapNew() {
    //   if (this.selected.length === 0) {
    //     this.$dialog.message.error('请选择记录')
    //     return
    //   }
    //   let totalStatus = this.selected.map((ele) => ele.status)
    //   // 判断是否全部为草稿状态
    //   // 检查 totalStatus 数组中的每个元素是否都是 6 或 22
    //   if (!totalStatus.every((element) => element === 6 || element === 22)) {
    //     this.$dialog.message.error('请选择记账前审批已完成记录')
    //     return
    //   }
    //   this.isDialog = true
    // },
    // 发送SAP 新
    async sendSAPNew() {
      // if (!this.$refs.form.validate()) {
      //   this.$dialog.message.error('请选择日期')
      //   return
      // }
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      let totalStatus = this.selected.map((ele) => ele.status)
      // 判断是否全部为草稿状态
      // 检查 totalStatus 数组中的每个元素是否都是 6 或 22
      if (!totalStatus.every((element) => element === 6 || element === 22)) {
        this.$dialog.message.error('请选择记账前审批已完成记录')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('是否确认发送sap？'))) return
      this.loading3 = true
      let arr = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/item/external/submitSAPNew`,
        {
          idList: arr,
        },
      )
      this.loading3 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('发送成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async summyExpense() {
      if (!(await this.$dialog.msgbox.confirm('确定生成汇总记录吗？'))) return
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      // let totalDate = this.selected.map((ele) => ele.applyDate)
      // // 判断是否全部为相同日期
      // if (
      //   totalDate.filter((element) => element !== this.selected[0].applyDate)
      //     .length > 0
      // ) {
      //   return this.$dialog.message.error('请选择相同日期')
      // }
      let totalSupplierName = this.selected.map((ele) => ele.supplierName)
      // 判断是否全部为相同供应商
      if (
        totalSupplierName.filter(
          (element) => element !== this.selected[0].supplierName,
        ).length > 0
      ) {
        return this.$dialog.message.error('请选择相同供应商')
      }
      let totalStatus = this.selected.map((ele) => ele.status)
      // 判断是否全部为草稿状态
      if (totalStatus.filter((element) => element !== 1).length > 0) {
        console.log(
          'totalStatus',
          totalStatus.filter((element) => element !== 1),
        )
        this.$dialog.message.error('请选择已完成记账前审批记录')
        return
      }
      this.loading5 = true
      let arr = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/item/external/summary`,
        arr,
      )
      this.loading5 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('汇总成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    GoTOTotal() {
      this.$router.replace({ name: 'expense-items-external-total' })
    },
    selectItemName(val) {
      this.salaryItem.itemId = val.parentSap
      this.salaryItem.itemName = val.itemName
    },
    selectSupplier(val) {
      this.salaryItem.supplierId = val.sapCode
      this.salaryItem.supplierName = val.name
      console.log('this.salaryItem', this.salaryItem)
    },
    closeForm() {
      this.selected = []
      this.salaryItem = {}
      this.isEdit = false
      this.isShow = false
    },
    closeCard() {
      this.recordDate = ''
      this.isDialog = false
    },
    addExpense() {
      this.selected = []
      this.salaryItem = {}
      this.isEdit = true
      this.isShow = true
    },
    async save() {
      console.log('this.salaryItem', this.salaryItem)
      this.loading1 = true
      const url = this.isEdit
        ? '/business/crew/salary/item/external/detail/save'
        : '/business/crew/salary/item/external/detail/update'
      const { errorRaw } = await this.postAsync(url, this.salaryItem)
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      this.selected = []
      this.isShow = false
      this.salaryItem = {}
      await this.$refs.table.loadTableData()
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      let arr = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/item/external/detail/deleteBatch`,
        arr,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/item/outPayList`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data.map((val) => val?.itemName)
      this.expenseInfo = data
      console.log('this.expenseInfo', this.expenseInfo)
    },
  },

  async mounted() {
    await this.getCreFirst()
  },
}
</script>

<style></style>
