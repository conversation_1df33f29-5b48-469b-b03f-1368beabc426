<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :single-select="false"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :search-remain="searchRemain"
      :fix-header="false"
      :push-params="pushParams"
      use-ship
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :loading="loading2"
          @click="exportExcel"
          v-permission="['费用汇总:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="info"
          class="mx-1"
          :loading="loading1"
          @click="sendSAP"
          v-permission="['费用汇总:发送SAP记账']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          发送SAP记账
        </v-btn>
      </template>
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-year-month-picker
            outlined
            dense
            :use-current="false"
            v-model="searchRemain.yearAndMonth"
          ></v-year-month-picker>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船舶所属船员公司"
            outlined
            dense
            required
            :items="companys"
            item-text="dictLabel"
            item-value="dictValue"
            v-model="searchRemain.creComCode"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template v-slot:[`item.approveSubmitFlag`]="{ item }">
        <v-chip v-if="!item.approveSubmitFlag" small dark>未发起审批</v-chip>
        <v-chip v-else small color="success" dark>已发起审批</v-chip>
      </template>
      <template v-slot:[`item.sapFlag`]="{ item }">
        <v-chip v-if="!item.sapFlag" small dark>未发送SAP</v-chip>
        <v-chip v-else small color="success" dark>已发送SAP</v-chip>
      </template>
      <template v-slot:[`item.oaSendFlag`]="{ item }">
        <v-chip v-if="!item.oaSendFlag" small dark>未发起OA</v-chip>
        <v-chip v-else small color="success" dark>已发起OA</v-chip>
      </template>
    </v-table-searchable>
    <expenses-sum-dialog v-model="dialog"></expenses-sum-dialog>
  </v-container>
</template>
<script>
import expensesSumDialog from './expenses-sum-dialog.vue'
export default {
  components: { expensesSumDialog },
  name: 'expenses-sum-list',
  created() {
    this.tableName = '费用汇总'
    this.reqUrl = '/business/crew/salary/crewCostSum/page'
    this.headers = [
      { text: '船舶所属船员公司', value: 'crewCompany' },
      // { text: '费用承担公司', value: 'payCompany' },
      { text: '船名', value: 'shipName' },
      { text: '金额', value: 'amount' },
      { text: '金额(无船东奖励)', value: 'alloSum' },
      { text: '汇率', value: 'exchangeRate' },
      { text: '账单所属年月', value: 'yearAndMonth' },
      { text: '是否已发送SAP', value: 'sapFlag' },
      { text: '考试培训费', value: 'oaSendFlag' },
      { text: '外聘船员管理费', value: 'approveSubmitFlag' },
      { text: '公司公积金-外籍船', value: 'remark' },
      { text: 'gong', value: 'actions', sortable: false },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'expenses-sum-detail' }
  },

  data() {
    return {
      selected: [],
      companys: [],
      searchRemain: {
        yearAndMonth: '',
        shipCode: '',
        creComCode: '',
      },
      newInfo: {},
      canAdd: false,
      loading1: false,
      loading2: false,
      dialog: false,
      baseURL:
        'https://jk.sitc.com/webroot/decision/view/report?viewlet=Test%252FADMIN_CENTER%252FcrewCompanyTicket.cpt&',
    }
  },

  methods: {
    async sendSAP() {
      if (!(await this.$dialog.msgbox.confirm('确定发送sap吗？'))) return
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      // let totalStatus = this.selected.map((ele) => ele.sapFlag)
      // // 判断是否全部为草稿状态
      // // 检查 totalStatus 数组中的每个元素是否都是 6 或 22
      // if (totalStatus.every((element) => element === true)) {
      //   this.$dialog.message.error('请选择记账前审批已完成记录')
      //   return
      // }
      this.loading1 = true
      let ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/crewCostSum/sendSap`,
        {
          ids: ids,
        },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('发送成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    exportExcel() {
      // this.loading2 = true
      // const baseUrl = '/api/business/crew/salary/crewCostSum/export'
      // const downloadUrl = `${baseUrl}?shipCode=${encodeURIComponent(
      //   this.searchRemain.shipCode,
      // )}&yearAndMonth=${encodeURIComponent(
      //   this.searchRemain.yearAndMonth,
      // )}&creComCode=${encodeURIComponent(this.searchRemain.creComCode)}`
      // console.log('url', downloadUrl)
      // window.location.href = downloadUrl
      // this.loading2 = false
      if (this.searchRemain.creComCode == '') {
        this.$dialog.message.error('请选择船舶所属船员公司')
        return
      }
      if (this.searchRemain.yearAndMonth == '') {
        this.$dialog.message.error('请选择账单所属年月')
        return
      }
      this.openInNewWindow()
    },
    close() {
      this.canAdd = false
      this.newInfo = {}
    },
    init() {
      this.dialog = true
    },
    async save() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/crewCostSum/add`,
        this.newInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('创建成功')
      this.newInfo = {}
      this.canAdd = false
      await this.$refs.table.loadTableData()
    },
    // async sendApprove() {
    //   if (!(await this.$dialog.msgbox.confirm('确定将选中记录提交审批？')))
    //     return
    //   const ids = this.selected.map((ele) => ele.id)
    //   const { errorRaw } = await this.postAsync(
    //     `/business/crew/salary/allocation/submit`,
    //     {
    //       ids: ids,
    //     },
    //   )
    //   if (errorRaw) {
    //     return
    //   }
    //   this.selected = []
    //   this.$dialog.message.success('提交成功')
    // },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/CreCompanyDic/list`,
      )
      if (errorRaw) {
        return
      }
      this.companys = data
      console.log('companys', this.companys)
    },
    async del() {
      if (this.selected.some((ele) => ele.approveSubmitFlag)) {
        this.$dialog.message.error('已有记录发送审批，无法删除')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/crewCostSum/deleteBatch`,
        ids,
      )
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
    },
    openInNewWindow() {
      this.$nextTick(() => {
        // 确保 DOM 已经渲染
        if (this.$refs.table && this.$refs.table.dates) {
          console.log('dates', this.searchRemain)
          let creComCode = this.searchRemain.creComCode
          console.log(creComCode)
          let url
          url = this.baseURL
          url = url + '__bypagesize__=false&'
          url = url + 'crewCode=' + creComCode
          url = url + '&yearAndMonth=' + this.searchRemain.yearAndMonth

          window.open(url, '_blank')
        } else {
          this.$dialog.message.error('请等待数据加载，稍后请重试。')
          location.reload()
        }
      })
    },
  },

  async mounted() {
    await this.getCreFirst()
  },
}
</script>

<style></style>
