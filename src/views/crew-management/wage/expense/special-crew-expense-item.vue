<template>
  <v-container fluid>
    <!-- 编辑表单（新增 / 修改） -->
    <v-expand-transition>
      <v-card v-if="isShow">
        <v-card-title>
          {{ isEdit ? '修改' : '新增' }} 特殊船员杂费
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-text-field
                label="收款人姓名"
                v-model="salaryItem.payeeName"
                outlined
                dense
                disabled
                type="text"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="付款项目"
                outlined
                dense
                disabled
                v-model="salaryItem.itemName"
                type="text"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="金额"
                outlined
                dense
                disabled
                v-model="salaryItem.amount"
                type="number"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="税"
                outlined
                dense
                v-model.number="salaryItem.tax"
                type="number"
                step="0.01"
                @input="countTax"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="实付"
                outlined
                dense
                disabled
                v-model="salaryItem.actualPayment"
                type="number"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            :loading="loadingSave"
            @click="save"
            block
            v-permission="['船东考核标准:编辑']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            保存
          </v-btn>
        </v-card-text>
      </v-card>
    </v-expand-transition>

    <!-- 主表格 -->
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchCriteria"
      :search-date="searchDate"
      :single-select="false"
      @dbclick="editSalaryItem"
    >
      <!-- 自定义查询条件 -->
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="申请人姓名"
            outlined
            dense
            v-model="searchCriteria.applyPerson"
            clearable
          ></v-text-field>
        </v-col>
      </template>

      <!-- 表格上方操作按钮 -->
      <template #btns>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="summaryExpense"
          v-permission="['船员费用对外付款:汇总发送OA审批']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          汇总发送OA审批
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="addExpense"
          v-permission="['船员费用对外付款:删除']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          删除
        </v-btn>
        <v-btn
          color="#0D47A1"
          outlined
          tile
          class="mx-1"
          @click="viewSummaryInfo"
          v-permission="['船员当前社保公积金查询:查看历史设备公积金信息']"
        >
          <v-icon left dark>mdi-eye</v-icon>
          查看汇总发送信息
        </v-btn>
        <v-btn
          outlined
          :loading="loadingGenerate"
          tile
          color="success"
          class="mx-1"
          @click="openGenerateDialog"
          v-permission="['特殊船员杂费:生成特殊船员杂费']"
        >
          <v-icon left>mdi-cog</v-icon>
          生成特殊船员杂费
        </v-btn>
      </template>

      <!-- 自定义状态显示 -->
      <template v-slot:[`item.oaSend`]="{ item }">
        <v-chip v-if="item.oaSend === 0" color="" dark small>未汇总</v-chip>
        <v-chip v-else-if="item.oaSend === 1" color="info" dark small>
          已汇总
        </v-chip>
        <v-chip v-else-if="item.oaSend === 2" color="success" dark small>
          已发送OA
        </v-chip>
        <v-chip v-else color="error" dark small>OA未通过</v-chip>
      </template>
    </v-table-searchable>

    <!-- 生成特殊船员杂费 Dialog -->
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="dialogGenerate"
    >
      <v-card>
        <v-card-title>
          生成特殊船员杂费
          <v-spacer></v-spacer>
          <v-icon @click="closeGenerateDialog">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="2">
                  <v-year-month-picker
                    outlined
                    dense
                    v-model="yearAndMonth"
                    :clearable="false"
                  ></v-year-month-picker>
                </v-col>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    :loading="loadingGenerate"
                    color="success"
                    class="mx-1"
                    v-permission="['特殊船员杂费:生成特殊船员杂费']"
                    @click="generateSpecial"
                    block
                  >
                    <v-icon left>mdi-cog</v-icon>
                    生成
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import currencyHelper from '@/mixin/currencyHelper'

export default {
  name: 'special-crew-expense-item',
  mixins: [currencyHelper],
  data() {
    return {
      // 表格配置
      tableName: '特殊船员杂费详情',
      reqUrl: '/business/crew/salary/apply/special/detail/page',
      headers: [
        { text: '船名', value: 'shipName' },
        { text: '收款人姓名', value: 'payeeName' },
        { text: '付款项目', value: 'itemName' },
        { text: '金额', value: 'amount' },
        { text: '税', value: 'tax' },
        { text: '实付', value: 'actualPayment' },
        { text: '身份证号码', value: 'idCardNumber' },
        { text: '银行账户', value: 'bankAccount' },
        { text: '备注', value: 'remark' },
        { text: '生效时间', value: 'approvedTime' },
        { text: '状态', value: 'oaSend' },
      ],

      // 查询条件
      fuzzyLabel: '',
      searchCriteria: {},

      // 选中行
      selected: [],

      // 弹层 / 对话框
      isShow: false, // 是否展示编辑卡片
      isEdit: false, // 当前是否为修改模式
      dialogGenerate: false, // 生成特殊杂费对话框

      // 当前编辑记录
      salaryItem: {},

      // 年月选择
      yearAndMonth: '',

      // 加载状态
      loadingSave: false,
      loadingSummary: false,
      loadingGenerate: false,

      // 日期筛选等
      searchDate: {
        interval: true,
        label: '发起申请起末时间',
      },
    }
  },
  created() {
    this.initData()
  },
  methods: {
    countTax() {
      // 转换数值类型
      const amount = parseFloat(this.salaryItem.amount) || 0
      const tax = parseFloat(this.salaryItem.tax) || 0

      if (tax < 0) {
        this.salaryItem.tax = 0
        this.$dialog.message.error('税不能为负数')
        return
      }

      // 限制小数位数并格式化
      this.salaryItem.actualPayment = (amount - tax).toFixed(2)
    },
    // 初始化加载一些下拉信息等
    async initData() {
      // 如需预加载接口，可在此调用
    },

    // 打开“生成特殊船员杂费”对话框
    openGenerateDialog() {
      this.dialogGenerate = true
    },
    // 关闭“生成特殊船员杂费”对话框
    closeGenerateDialog() {
      this.dialogGenerate = false
    },

    // 生成特殊船员杂费
    async generateSpecial() {
      if (!(await this.$dialog.msgbox.confirm('是否确认生成？'))) return
      this.loadingGenerate = true
      const { errorRaw } = await this.getAsync(
        '/business/crew/salary/apply/special/detail/gen',
        { yearAndMonth: this.yearAndMonth },
      )
      this.loadingGenerate = false
      if (errorRaw) return
      this.$dialog.message.success('操作成功')
      this.closeGenerateDialog()
      await this.$refs.table.loadTableData()
    },

    // 点击表格按钮：汇总发送 OA 审批
    async summaryExpense() {
      console.log('汇总发送 OA 审批')
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      // 校验是否都是未汇总
      const notSummary = this.selected.some((item) => item.oaSend !== 0)
      if (notSummary) {
        this.$dialog.message.error('请选择未汇总记录')
        return
      }
      this.loadingSummary = true
      const ids = this.selected.map((item) => item.id)
      const { errorRaw } = await this.postAsync(
        '/business/crew/salary/apply/special/summary',
        ids,
      )
      this.loadingSummary = false
      if (errorRaw) return
      this.$dialog.message.success('汇总成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },

    // 点击“查看汇总发送信息”
    viewSummaryInfo() {
      // 根据自己路由命名实际情况调整
      this.$router.replace({ name: 'special-crew-expense' })
    },

    // 点击删除按钮（此处只是示例，实际应调用后台删除接口）
    addExpense() {
      // 示例仅切换到“新增”模式并展示表单
      this.isEdit = false
      this.salaryItem = {}
      this.selected = []
      this.isShow = true
    },

    // 打开编辑模式（示例：双击行或点击编辑按钮时）
    editSalaryItem(item) {
      console.log('编辑：', item)
      this.isShow = true
      this.isEdit = true
      this.salaryItem = { ...item[0] }
    },

    // 关闭表单卡片
    closeForm() {
      this.isShow = false
      this.isEdit = false
      this.salaryItem = {}
      this.selected = []
    },

    // 保存
    async save() {
      this.countTax()
      if (!(await this.$dialog.msgbox.confirm('是否确认保存？'))) return
      this.loadingSave = true
      const url = this.isEdit
        ? '/business/crew/salary//apply/special/update'
        : '/business/crew/salary/item/external/detail/save'
      const { errorRaw } = await this.postAsync(url, this.salaryItem)
      this.loadingSave = false
      if (errorRaw) return
      this.$dialog.message.success('保存成功')
      this.closeForm()
      await this.$refs.table.loadTableData()
    },
  },
  watch: {
    'salaryItem.tax': {
      handler() {
        this.countTax()
      },
      immediate: true,
    },
  },
}
</script>

<style scoped>
/* 根据需要添加样式 */
</style>
