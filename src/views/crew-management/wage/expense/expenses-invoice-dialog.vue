<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-year-month-picker
                  v-model="yearMonth"
                  outlined
                  dense
                  :clearable="false"
                ></v-year-month-picker>
              </v-col>
              <v-col cols="12" md="4">
                <v-select
                  v-model="companys"
                  :items="companyOption"
                  label="公司选择"
                  multiple
                  outlined
                  dense
                  clearable
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  :loading="loading"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'expenses-invoice-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      companyOption: [],
      companys: [],
      items: [],
      yearMonth: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      loading: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
    yearMonth: {
      async handler() {
        await this.getShipList()
      },
    },
    chips(val) {
      console.log('chips', val)
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      this.loading = true
      if (!this.$refs.form.validate()) {
        return
      }
      const salaryMonthMakeInitParam = {
        shipCodes: this.companys,
        yearAndMonth: this.yearMonth,
      }
      //  /business/crew/salary/allocation/initInner
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/allocation/initInner`,
        salaryMonthMakeInitParam,
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.$emit('change', false)
      this.$emit('success')
      this.loading = false
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.companyOption = data
      console.log(this.companyOption)
    },
  },
  async beforeMount() {
    await this.getCreFirst()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
