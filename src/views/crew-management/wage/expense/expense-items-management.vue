<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="isShow">
        <v-card-title>
          {{ isEdit ? '新增' : '修改' }}---船员费用项目
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-text-field
                label="项目名称"
                outlined
                dense
                v-model="salaryItem.itemName"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="项目分类"
                outlined
                dense
                :items="itemCategoryList"
                v-model="salaryItem.itemCategory"
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="项目代码"
                outlined
                dense
                v-model="salaryItem.itemCode"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            @click="save"
            :loading="loading1"
            block
            v-permission="['船员费用项目:编辑']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            保存
          </v-btn>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      @dbclick="editElectronicchart"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="项目名称"
            outlined
            dense
            v-model="searchRemain.itemName"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="项目分类"
            outlined
            dense
            :items="itemCategoryList"
            v-model="searchRemain.itemCategory"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="addExpense"
          v-permission="['船员费用项目:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editElectronicchart"
          v-permission="['船员费用项目:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船员费用项目:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'expense-items-management',
  created() {
    this.tableName = '船员费用项目查询'
    this.reqUrl = '/business/crew/salary/item/page'
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目代码', value: 'itemCode' },
      { text: '项目类别', value: 'itemCategory' },
    ]
    this.fuzzyLabel = ''
    // this.searchDate = {
    //   label: '',
    //   value: '',
    // }
  },

  data() {
    return {
      selected: false,
      isShow: false,
      isEdit: false,
      searchRemain: {},
      salaryItem: {},
      itemCategoryList: [],
    }
  },

  methods: {
    closeForm() {
      this.selected = false
      this.salaryItem = {}
      this.isEdit = false
      this.isShow = false
    },
    addExpense() {
      this.selected = false
      this.salaryItem = {}
      this.isEdit = true
      this.isShow = true
    },
    async save() {
      const url = this.isEdit
        ? '/business/crew/salary/item/save'
        : '/business/crew/salary/item/update'
      this.loading1 = true
      const { errorRaw } = await this.postAsync(url, this.salaryItem)
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      this.selected = false
      this.isShow = false
      this.salaryItem = {}
      await this.$refs.table.loadTableData()
    },
    editElectronicchart() {
      this.salaryItem = { ...this.selected }
      this.isEdit = false
      this.isShow = true
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/item/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async getItemCategory() {
      const { errorRaw, data } = await this.getAsync(
        `/system/dict-data/getValuesByDictType`,
        { dictType: 'cre_salary_item_category' },
      )
      if (errorRaw) {
        this.$dialog.message.error('项目分类列表获取失败')
        return
      }
      this.itemCategoryList = data
    },
  },
  async mounted() {
    await this.getItemCategory()
  },
}
</script>

<style></style>
