<template>
  <v-dialog attach="#mask" hide-overlay width="500" persistent v-model="dialog">
    <v-card>
      <v-card-title>
        根据船名导入 {{ isChosen ? '自修奖' : '船员绩效' }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-select
                  label="地区"
                  outlined
                  dense
                  :items="[
                    { text: '上海', value: 3604 },
                    { text: '山东', value: 3607 },
                  ]"
                  v-model="area"
                ></v-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-ship-select
                  label="船名"
                  v-model="shipCode"
                  v-show="!isTotal"
                ></v-ship-select>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  导入
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'expense-input-dialog',
  mixins: [currencyHelper],
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.creHeaders = [
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
      { text: '银行卡号', value: 'rmbCard' },
    ]
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '部门', value: 'dept' },
      { text: '工资类别', value: 'salaryType' },
      { text: '票据形式', value: 'billForm' },
      { text: '支付方式', value: 'payType' },
      { text: '单独支付标识', value: 'singlePayFlag' },
      { text: '是否计税', value: 'individualTaxFlag' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    initSelected: {
      type: Object,
      default: () => {},
    },
    initSelectedItemId: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
    isChosen: {
      type: Boolean,
      default: false,
      //true为自修奖 false为船员绩效
    },
    isTotal: {
      type: Boolean,
      default: false,
      //true为全部船 false为单个船
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchRemain: {},
      shipcode: '',
      detail: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      // this.$refs?.form?.resetValidation()
      // this.formData = this.initialData
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    selectItemName(val) {
      this.formData.itemName = val.itemName
    },
    async save() {
      const segments = this.$route.path.split('/') // 将路径分割成段
      const lastSegment = segments[segments.length - 1] // 获取最后一个段
      console.log(lastSegment)
      if (this.isChosen == true) {
        const apiPath = this.isTotal
          ? '/business/crew/salary/apply_repair/all_bonus'
          : '/business/crew/salary/apply_repair/bonus'
        const { data } = await this.getAsync(apiPath, {
          area: this.area,
          shipCode: this.shipCode,
        })
        data.forEach((item) => {
          item.applyId = lastSegment // 为每个元素的 apply_id 赋值
        })
        this.detail = data
        if (data.length == 0) {
          this.$dialog.message.error(
            '该船目前没有自修奖数据，请在机务系统中维护',
          )
          return
        } else {
          console.log(this.detail)
        }
        const saveUrl = '/business/crew/salary/apply_repair/save'
        const { errorRaw } = await this.postAsync(saveUrl, data)
        if (errorRaw) {
          this.$dialog.message.error(errorRaw.msg)
        } else {
          this.$emit('change', false)
          this.$emit('success')
          this.$dialog.message.success('保存成功')
        }
      } else {
        const { data } = await this.getAsync(
          '/business/crew/salary/apply_evaluate/detail',
          { area: this.area, shipCode: this.shipCode },
        )
        let dataWithBankAccount = [] // 存储有 bank_account 的元素
        let dataWithoutBankAccount = [] // 存储没有 bank_account 的元素
        data.forEach((item) => {
          item.applyId = lastSegment // 为每个元素的 apply_id 赋值
          // 根据 item.bank_account 的有无，将元素分别添加到相应的数组
          if (item.bankAccount) {
            dataWithBankAccount.push(item)
            console.log('push_dataWithBankAccount')
          } else {
            dataWithoutBankAccount.push(item)
            console.log('dataWithoutBankAccount')
          }
        })
        this.detail = data
        if (data.length == 0) {
          this.$dialog.message.error(
            '该船目前没有船员绩效数据，请在船员绩效模块中维护',
          )
          return
        } else {
          console.log(this.detail)
        }
        if (dataWithoutBankAccount) {
          this.$dialog.message.error('导入数据中部分船员的银行卡号为空')
        }
        const saveUrl = '/business/crew/salary/evaluate_money/save'
        const { errorRaw } = await this.postAsync(saveUrl, dataWithBankAccount)
        if (errorRaw) {
          this.$dialog.message.error(errorRaw.msg)
        } else {
          this.$emit('change', false)
          this.$emit('success')
          this.$dialog.message.success('保存成功')
        }
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
