<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['管理费用标准:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #管理费用标准基本信息>
        <v-form ref="form">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="标准配员人数"
                  outlined
                  dense
                  v-model="detailInfo.personnelNumber"
                  type="number"
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  label="实习生标准配员人数"
                  outlined
                  dense
                  readonly
                  v-model="detailInfo.internPersonnelNumber"
                  type="number"
                ></v-text-field>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  label="伙食费标准(/天/人)"
                  v-model="detailInfo.mealStandard"
                  outlined
                  dense
                  type="number"
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="2">
                <v-text-field
                  label="管理费标准(/月/人)"
                  outlined
                  dense
                  v-model="detailInfo.managementStandard"
                  type="number"
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  label="实习生管理费标准(/月/人)"
                  outlined
                  dense
                  v-model="detailInfo.internManagementStandard"
                  type="number"
                ></v-text-field>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                <v-text-field
                  label="社保费标准(/月/人)"
                  outlined
                  dense
                  v-model="detailInfo.socialSecurityStandard"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="集结费标准(/年/人)"
                  outlined
                  dense
                  v-model="detailInfo.gatheringStandard"
                  type="number"
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="协议有效期开始时间"
                  outlined
                  dense
                  v-model="detailInfo.validityTermFrom"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="协议有效期结束时间"
                  outlined
                  dense
                  v-model="detailInfo.validityTermEnd"
                ></vs-date-picker>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-select
                  label="是否发送OA"
                  outlined
                  dense
                  :items="[
                    { text: '已发送OA', value: 1 },
                    { text: '未发送OA', value: 0 },
                  ]"
                  v-model="detailInfo.oaStatus"
                  readonly
                ></v-select>
              </v-col> -->
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <!-- <template #船员按岗位月度工资标准按钮>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="create"
          v-permission="['船员按岗位月度工资标准:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船员按岗位月度工资标准:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template> -->
      <!-- <template #船员按岗位月度工资标准>
        <v-table-list
          :headers="headers"
          :items="detailInfo.wagesStandard"
          item-key="position"
          v-model="selected"
          use-page
        >
          <template v-slot:[`item.num`]="{ item }">
            <v-text-field
              label="岗位人数"
              single-line
              dense
              v-model="item.num"
              type="number"
            ></v-text-field>
          </template>
          <template v-slot:[`item.standard`]="{ item }">
            <v-text-field
              label="月工资标准"
              single-line
              dense
              v-model="item.standard"
              type="number"
            ></v-text-field>
          </template>
        </v-table-list>
      </template> -->
    </v-detail-view>
    <expenses-standard-dialog
      v-model="dialog"
      :initialData="initialData"
      @success="success"
    ></expenses-standard-dialog>
  </v-container>
</template>
<script>
import expensesStandardDialog from './expenses-standard-dialog.vue'
export default {
  components: { expensesStandardDialog },
  name: 'expenses-standard-detail',
  created() {
    this.backRouteName = 'expenses-standard-management'
    this.subtitles = ['管理费用标准基本信息']
    this.headers = [
      { text: '岗位', value: 'position' },
      { text: '岗位人数', value: 'num' },
      { text: '月工资标准（$）', value: 'standard' },
    ]
  },
  data() {
    return {
      title: '新增管理费用标准详情',
      detailInfo: {
        oaStatus: 0,
        wagesStandard: [],
        personnelNumber: '',
      },
      initialData: {},
      list: [],
      selected: false,
      dialog: false,
    }
  },
  watch: {
    'detailInfo.shipCode': {
      handler(newValue) {
        if (newValue) {
          // console.log('newValue', newValue)
          this.getCrewNum()
        }
      },
    },
  },
  methods: {
    async save(goBack) {
      if (this.detailInfo.oaStatus === 1) {
        goBack()
        return
      }
      const { errorRaw } = await this.postAsync(
        `/manageCostStandard/saveOrUpdate`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message
      goBack()
    },
    async getDetailInfo() {
      if (this.$route.params.id === `new`) return
      const { errorRaw, data } = await this.getAsync(
        `/manageCostStandard/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = data
      this.title = `${data.shipName}-管理费用标准详情`
    },
    delAudit() {
      const len = this.detailInfo.wagesStandard.length
      this.detailInfo.wagesStandard = this.detailInfo.wagesStandard.filter(
        (ele) => ele.position !== this.selected.position,
      )
      if (len === this.detailInfo.wagesStandard.length) {
        this.$dialog.message.error('删除失败')
      } else {
        this.$dialog.message.success('删除成功')
      }
      this.selected = false
    },
    async getCrewNum() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salaryStandard/crewNum`,
        { shipCode: this.detailInfo.shipCode },
      )
      if (errorRaw) {
        return
      }
      this.detailInfo.personnelNumber = data
      // console.log('number', this.detailInfo.personnelNumber)
    },
    success(val) {
      console.log('val', val)
      if (this.detailInfo.wagesStandard === null) {
        this.detailInfo.wagesStandard = []
      }
      const len = this.detailInfo?.wagesStandard.length
      if (
        this.detailInfo.wagesStandard.find(
          (ele) => ele.position === val.position,
        )
      ) {
        this.$dialog.message.error('该岗位已存在')
        return
      }
      this.detailInfo.wagesStandard.unshift(val)
      if (len === this.detailInfo?.wagesStandard.length) {
        this.$dialog.message.error('添加失败')
      } else {
        this.$dialog.message.success('添加成功')
      }
    },
    create() {
      this.initialData = {}
      this.dialog = true
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
