<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      use-ship
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/invoice-management/expenses-standard-detail/new"
          v-permission="['管理费用标准:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="blue"
          class="mx-1"
          @click="alive"
          v-permission="['管理费用标准:生效']"
        >
          <v-icon left>mdi-check</v-icon>
          生效
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['管理费用标准:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.existStatus`]="{ item }">
        <v-chip v-if="item.existStatus" color="success" small dark>
          已生效
        </v-chip>
        <v-chip v-else color="" small dark>未生效</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'expenses-standard-management',
  created() {
    this.tableName = '管理费用标准'
    this.reqUrl = '/manageCostStandard/page'
    this.headers = [
      { text: '船名', value: 'shipName' },
      {
        text: '标准配员人数',
        value: 'personnelNumber',
      },
      { text: '管理费标准(/月/人)', value: 'managementStandard' },
      // {
      //   text: '实习生标准配员人数',
      //   value: 'internPersonnelNumber',
      // },
      // { text: '实习生管理费标准(/月/人)', value: 'internManagementStandard' },
      { text: '协议有效期开始时间', value: 'validityTermFrom' },
      { text: '协议有效期结束时间', value: 'validityTermEnd' },
      { text: '是否生效', value: 'existStatus' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'expenses-standard-detail' }
  },

  data() {
    return {
      selected: false,
    }
  },

  methods: {
    async del() {
      if (this.selected.oaStatus === 1) {
        this.$dialog.message.error('该记录已发送OA，无法删除')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        `/manageCostStandard/delete?id=${this.selected.id}`,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async alive() {
      if (this.selected.existStatus === 1) {
        this.$dialog.message.error('该记录已生效')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定生效该标准？'))) return
      const { errorRaw } = await this.postAsync(
        `/manageCostStandard/alive?id=${this.selected.id}`,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
