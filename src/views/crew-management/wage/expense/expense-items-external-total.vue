<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="4">
          <v-text-field
            label="项目名称"
            outlined
            dense
            v-model="searchRemain.itemName"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="发起人姓名"
            outlined
            dense
            v-model="searchRemain.applyPerson"
            clearable
          ></v-text-field>
        </v-col>
        <!--        <v-col cols="12" md="2">-->
        <!--          <v-select-->
        <!--            label="费用项目"-->
        <!--            outlined-->
        <!--            dense-->
        <!--            required-->
        <!--            :items="newInfo"-->
        <!--            v-model="searchRemain.itemName"-->
        <!--            clearable-->
        <!--          ></v-select>-->
        <!--        </v-col>-->
        <!--        <v-col cols="12" sm="6" md="2">-->
        <!--          <v-supply-select2-->
        <!--            v-model="searchRemain.supplierId"-->
        <!--            label="供应商"-->
        <!--            outlined-->
        <!--            clearable-->
        <!--            dense-->
        <!--          ></v-supply-select2>-->
        <!--        </v-col>-->
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          color="info"
          outlined
          tile
          class="mx-1"
          :loading="loading1"
          @click="submitAudit"
          v-permission="['船员当前社保公积金查询:查看历史设备公积金信息']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          发起审批
        </v-btn>
        <v-btn
          color="#0D47A1"
          outlined
          tile
          class="mx-1"
          @click="GoTOItems"
          v-permission="['船员当前社保公积金查询:查看历史设备公积金信息']"
        >
          <v-icon left dark>mdi-eye</v-icon>
          查看单条数据
        </v-btn>
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['费用申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" color="" dark small>草稿</v-chip>
        <v-chip v-else-if="item.status === 1" color="info" dark small>
          待审批
        </v-chip>
        <v-chip v-else-if="item.status === 2" color="success" dark small>
          审批完成
        </v-chip>
        <v-chip v-else color="error" dark small>OA未通过</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>

<script>
export default {
  name: 'expense-items-external-total',
  created() {
    this.tableName = '对外付款费用信息汇总详情'
    this.reqUrl = '/business/crew/salary/item/external/page'
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '公司', value: 'area' },
      { text: '发起人姓名', value: 'applyPerson' },
      { text: '费用金额', value: 'totalMoney' },
      { text: '备注', value: 'remark' },
      { text: '状态', value: 'status' },
    ]
    this.projectHeaders = [
      { text: '项目名称', value: 'itemName' },
      { text: '部门', value: 'dept' },
      { text: '工资类别', value: 'salaryType' },
      { text: '票据形式', value: 'billForm' },
      { text: '支付方式', value: 'payType' },
      { text: '单独支付标识', value: 'singlePayFlag' },
      { text: '是否计税', value: 'individualTaxFlag' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'expense-items-external-total-detail',
    }
    // this.searchDate = {
    //   interval: true,
    //   label: '发起申请起末时间',
    // }
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      newInfo: [],
      expenseInfo: [],
      isShow: false,
      loading1: false,
      isEdit: false,
      salaryItem: {},
    }
  },

  methods: {
    GoTOItems() {
      this.$router.replace({ name: 'expense-items-external' })
    },
    selectItemName(val) {
      this.salaryItem.itemId = val.id
      this.salaryItem.itemName = val.itemName
    },
    selectSupplier(val) {
      this.salaryItem.supplierId = val.id
      this.salaryItem.supplierName = val.name
      console.log('this.salaryItem', this.salaryItem)
    },
    closeForm() {
      this.selected = false
      this.salaryItem = {}
      this.isEdit = false
      this.isShow = false
    },
    addExpense() {
      this.selected = false
      this.salaryItem = {}
      this.isEdit = true
      this.isShow = true
    },
    async save() {
      console.log('this.salaryItem', this.salaryItem)
      const url = this.isEdit
        ? '/business/crew/salary/item/external/detail/save'
        : '/business/crew/salary/item/external/detail/update'
      const { errorRaw } = await this.postAsync(url, this.salaryItem)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      this.selected = false
      this.isShow = false
      this.salaryItem = {}
      await this.$refs.table.loadTableData()
    },
    async submitAudit() {
      if (!(await this.$dialog.msgbox.confirm('是否提交审批？'))) return
      this.loading1 = true
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/item/external/submitAudit?id=${encodeURIComponent(
          this.selected.id,
        )}`,
      )
      this.loading1 = false
      // const { errorRaw } = await this.postAsync(
      //   `/business/crew/salary/item/external/submitAudit`,
      //   {
      //     id: this.selected.id,
      //   },
      // )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/apply/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/item/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data.map((val) => val?.itemName)
      this.expenseInfo = data
      console.log('this.expenseInfo', this.expenseInfo)
    },
  },

  async mounted() {
    await this.getCreFirst()
  },
}
</script>

<style></style>
