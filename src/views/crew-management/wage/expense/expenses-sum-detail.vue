<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['费用汇总:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-save="false"
    >
      <template #费用汇总基本信息>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-text-field
                outlined
                dense
                v-model="detailInfo.yearAndMonth"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否已发起SAP"
                outlined
                dense
                readonly
                :items="[
                  { text: '已发起SAP', value: true },
                  { text: '未发起SAP', value: false },
                ]"
                v-model="detailInfo.sapFlag"
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否发送OA"
                outlined
                dense
                readonly
                :items="[
                  { text: '已发送OA', value: true },
                  { text: '未发送OA', value: false },
                ]"
                v-model="detailInfo.oaSendFlag"
              ></v-select>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <template #分摊费用>
        <v-table-list
          item-key="id"
          :headers="detail2Headers"
          :items="itemList"
          v-model="selected"
        ></v-table-list>
      </template>
      <template #非分摊费用>
        <v-table-list
          item-key="id"
          :headers="detail2Headers"
          :items="itemList2"
          v-model="selected2"
        ></v-table-list>
      </template>
      <template #船东奖励>
        <v-table-list
          item-key="id"
          :headers="detail2Headers"
          :items="itemList3"
          v-model="selected3"
        ></v-table-list>
      </template>
    </v-detail-view>
    <add-manage-fee-dialog
      v-model="dialog"
      @success="success"
    ></add-manage-fee-dialog>
  </v-container>
</template>
<script>
import addManageFeeDialog from './expenses-add-manage-fee-dialog.vue'
export default {
  components: { addManageFeeDialog },
  name: 'expenses-sum-detail',
  created() {
    this.backRouteName = 'expenses-sum-list'
    this.subtitles = ['费用汇总基本信息', '分摊费用', '非分摊费用', '船东奖励']
    this.detail2Headers = [
      { text: '公司名称', value: 'company' },
      { text: '项目名称', value: 'itemName' },
      { text: '项目分类', value: 'itemCategory' },
      { text: '费用代码', value: 'itemCode' },
      { text: '金额', value: 'amount' },
    ]
  },
  data() {
    return {
      title: '费用汇总详情',
      detailInfo: {
        detail2: [],
      },
      itemList: [],
      itemList2: [],
      itemList3: [],
      items: [],
      detail1Headers: [],
      detail2Headers: [],
      dialog: false,
      selected: false,
      selected2: false,
      selected3: false,
    }
  },
  watch: {
    items: {
      handler(val) {
        for (let name in val[0]) {
          const hName = name.slice(9)
          this.detailInfo.detail1.forEach((ele) => {
            if (ele.itemName === hName) {
              ele.itemMoney = val[0][name]
            }
          })
        }
      },
      deep: true,
      immediate: true,
    },
    'detailInfo.detail2': {
      handler(val) {
        for (let i = 0; i < val.length; i++) {
          val[i].index = i + 1
          val[i].rentTotal =
            Number(val[i].salary) +
            Number(val[i].mealsMoney) +
            Number(val[i].insurance) +
            Number(val[i].gathering) +
            Number(val[i].manageCost)
        }
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/crewCostSum/detail`,
        {
          id: this.$route.params.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.title = `${data.shipName}-${data.yearAndMonth}-费用汇总详情`
      this.detailInfo = data
    },
    delAudit() {
      const len = this.detailInfo.detail2.length
      this.detailInfo.detail2 = this.detailInfo.detail2.filter(
        (ele) => ele.index !== this.selected.index,
      )
      if (len === this.detailInfo.detail2.length) {
        this.$dialog.message.error('删除失败')
      } else {
        this.$dialog.message.success('删除成功')
      }
    },
    create() {
      this.dialog = true
    },
    async getItemList() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/crewCostSum/item/list`,
        {
          id: this.detailInfo.id,
          allocateFlag: true,
        },
      )
      if (errorRaw) {
        return
      }
      this.itemList = data
    },
    async getItemList2() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/crewCostSum/item/list`,
        {
          id: this.detailInfo.id,
          allocateFlag: false,
        },
      )
      if (errorRaw) {
        return
      }
      this.itemList2 = data
    },
    async getItemList3() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/crewCostSum/item/listShipReward`,
        {
          id: this.detailInfo.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.itemList3 = data
    },
    success(val) {
      if (
        this.detailInfo.detail2.find((ele) => ele.position === val.position)
      ) {
        this.$dialog.message.error('该岗位已存在，请勿重复添加')
        return
      }
      this.detailInfo.detail2.push(val)
    },
  },

  async mounted() {
    await this.getDetailInfo()
    await this.getItemList()
    await this.getItemList2()
    await this.getItemList3()
  },
}
</script>

<style></style>
