<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="canAdd">
        <v-card-title>
          新增外部船员公司费用账单
          <v-spacer></v-spacer>
          <v-icon left @click="close">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-ship-select v-model="newInfo.company"></v-ship-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-year-month-picker
                outlined
                dense
                v-model="newInfo.yearAndMonth"
                :clearable="false"
              ></v-year-month-picker>
            </v-col>
          </v-row>
          <v-col cols="12">
            <v-btn
              outlined
              tile
              color="success"
              class="mx-1"
              @click="save"
              v-permission="['费用分摊:创建']"
              block
            >
              <v-icon left>mdi-plus-circle</v-icon>
              创建
            </v-btn>
          </v-col>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :single-select="false"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :search-remain="searchRemain"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-year-month-picker
            outlined
            dense
            :use-current="false"
            v-model="searchRemain.yearAndMonth"
          ></v-year-month-picker>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color=""
          class="mx-1"
          @click="init"
          v-permission="['费用分摊:初始化']"
        >
          <v-icon left>mdi-card-text-outline</v-icon>
          初始化
        </v-btn>
        <v-btn
          outlined
          tile
          color=""
          class="mx-1"
          :loading="loading3"
          @click="withDraw"
          v-permission="['费用分摊:取回']"
        >
          <v-icon left>mdi-card-text-outline</v-icon>
          取回
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="complete"
          :loading="loading1"
          v-permission="['费用分摊:分摊完成']"
        >
          <v-icon left>mdi-account-check</v-icon>
          分摊完成
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          :loading="loading2"
          v-permission="['费用分摊:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.approveSubmitFlag`]="{ item }">
        <v-chip v-if="!item.approveSubmitFlag" small dark>未发起审批</v-chip>
        <v-chip v-else small color="success" dark>已发起审批</v-chip>
      </template>
      <template v-slot:[`item.sumFlag`]="{ item }">
        <v-chip v-if="!item.sumFlag" small dark>未完成</v-chip>
        <v-chip v-else small color="success" dark>已完成</v-chip>
      </template>
    </v-table-searchable>
    <expenses-invoice-dialog
      v-model="dialog"
      @success="success"
    ></expenses-invoice-dialog>
  </v-container>
</template>
<script>
import ExpensesInvoiceDialog from './expenses-invoice-dialog.vue'
export default {
  components: { ExpensesInvoiceDialog },
  name: 'expenses-invoice-list',
  created() {
    this.tableName = '费用分摊'
    this.reqUrl = '/business/crew/salary/allocation/page'
    this.headers = [
      { text: '公司名称', value: 'company' },
      { text: '账单所属年月', value: 'yearAndMonth' },
      { text: '是否发起审批', value: 'approveSubmitFlag' },
      { text: '是否完成分配', value: 'sumFlag' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'expenses-invoice-detail' }
  },

  data() {
    return {
      selected: [],
      searchRemain: {
        yearAndMonth: '',
      },
      newInfo: {},
      canAdd: false,
      dialog: false,
      loading1: false,
      loading2: false,
      loading3: false,
    }
  },

  methods: {
    close() {
      this.canAdd = false
      this.newInfo = {}
    },
    async success() {
      this.$dialog.message.success('初始化成功')
      await this.$refs.table.loadTableData()
    },
    init() {
      this.dialog = true
    },
    async save() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/allocation/add`,
        this.newInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('创建成功')
      this.newInfo = {}
      this.canAdd = false
      await this.$refs.table.loadTableData()
    },
    // async sendApprove() {
    //   if (!(await this.$dialog.msgbox.confirm('确定将选中记录提交审批？')))
    //     return
    //   const ids = this.selected.map((ele) => ele.id)
    //   const { errorRaw } = await this.postAsync(
    //     `/business/crew/salary/allocation/submit`,
    //     {
    //       ids: ids,
    //     },
    //   )
    //   if (errorRaw) {
    //     return
    //   }
    //   this.selected = []
    //   this.$dialog.message.success('提交成功')
    // },
    async del() {
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      if (this.selected.some((ele) => ele.approveSubmitFlag)) {
        this.$dialog.message.error('已有记录发送审批，无法删除')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.loading2 = true
      const ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/allocation/deleteBatch`,
        ids,
      )
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
    },
    async complete() {
      if (this.selected.some((ele) => ele.sumFlag)) {
        this.$dialog.message.error('勾选数据中包含已完成分摊数据，无法重复执行')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定分摊完成？'))) return
      if (this.selected.length < 1) {
        this.$dialog.message.error('请勾选数据')
        return
      }
      this.loading1 = true
      const ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/allocation/alloComplete`,
        ids,
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('操作成功')
      await this.$refs.table.loadTableData()
    },
    async withDraw() {
      if (this.selected.some((ele) => !ele.sumFlag)) {
        this.$dialog.message.error('勾选数据中包含未完成分摊数据，无法执行')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确认取回？'))) return
      if (this.selected.length < 1) {
        this.$dialog.message.error('请勾选数据')
        return
      }
      this.loading3 = true
      const ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/allocation/withDraw`,
        ids,
      )
      this.loading3 = false
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('取回成功')
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
