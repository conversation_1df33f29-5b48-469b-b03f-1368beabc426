<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      useShip
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :search-date="searchDate"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="申请人姓名"
            outlined
            dense
            v-model="searchRemain.applyPerson"
            clearable
          ></v-text-field>
        </v-col>
        <!-- <v-col cols="12" md="2">
          <v-text-field
            label="所属地"
            outlined
            dense
            v-model="searchRemain.basePlace"
            clearable
          ></v-text-field>
        </v-col> -->
        <v-col cols="12" md="2">
          <v-select
            label="费用项目"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.itemName"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['费用申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" color="" dark small>暂未生效</v-chip>
        <v-chip v-else-if="item.status === 1" color="info" dark small>
          有费用项目已经发OA
        </v-chip>
        <v-chip v-else-if="2" color="success" dark small>OA通过</v-chip>
        <v-chip v-else color="error" dark small>OA未通过</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'expense-epiboly-crew',
  created() {
    this.tableName = '外包船员费用'
    this.reqUrl = '/business/crew/salary/epiboly/page'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '费用部门名称', value: 'itemDept' },
      { text: '付费项目名称', value: 'itemName' },
      { text: '币种', value: 'feeType' },
      { text: '金额', value: 'amount' },
      { text: '申请人姓名', value: 'payeeName' },
      { text: '身份证号', value: 'idCardNumber' },
      { text: '银行账号', value: 'bankAccount' },
      { text: '备注', value: 'remark' },
      { text: 'OA审批生效时间', value: 'approvedTime', hideDefault: true },
      { text: '发起时间', value: 'createTime' },
    ]
    this.fuzzyLabel = ''
    // this.pushParams = {
    //   name: '',
    // }
    this.searchDate = {
      interval: true,
      label: '发起申请起末时间',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      newInfo: [],
    }
  },

  methods: {
    async sendOA() {
      if (!(await this.$dialog.msgbox.confirm('是否发送OA审批？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/epiboly/sendOA`,
        {
          id: this.selected.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/epiboly/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/item/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data.map((val) => val?.itemName)
    },
  },

  async mounted() {
    await this.getCreFirst()
  },
}
</script>

<style></style>
