<template>
  <v-container fluid>
    <v-dialog v-model="errorDialog" persistent max-width="800px">
      <v-card>
        <v-card-title class="text-h5">错误信息</v-card-title>
        <v-card-text style="white-space: pre-line">
          {{ errorMessage }}
        </v-card-text>
        <!-- 使用 errorMessage 显示错误信息 -->
        <v-card-actions>
          <v-spacer></v-spacer>
          <!-- 关闭按钮 -->
          <v-btn color="blue darken-1" text @click="errorDialog = false">
            关闭
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :search-date="searchDate"
      :push-params="pushParams"
      :single-select="false"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="申请人姓名"
            outlined
            dense
            v-model="searchRemain.applyPerson"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="所属地"
            outlined
            dense
            v-model="searchRemain.basePlace"
            :items="[
              { text: '上海', value: '上海' },
              { text: '山东', value: '山东' },
            ]"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="费用项目"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.itemName"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-import-more-btn
          importUrl="/business/crew/salary/apply/excelsImport"
          @importSuccess="importSuccess"
          @importError="importError"
        ></v-import-more-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="info"
          class="mx-1"
          :loading="loading1"
          @click="sendSAPNew"
          v-permission="['费用申请:发送SAP记账']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          发送SAP记账（新）
        </v-btn>
        <!--                <v-btn-->
        <!--                  :disabled="selected.length === 0"-->
        <!--                  outlined-->
        <!--                  tile-->
        <!--                  color="info"-->
        <!--                  class="mx-1"-->
        <!--                  :loading="loading2"-->
        <!--                  @click="sendSAP"-->
        <!--                  v-permission="['费用申请:发送SAP记账']"-->
        <!--                >-->
        <!--                  <v-icon left>mdi-arrow-up-bold</v-icon>-->
        <!--                  发送SAP记账-->
        <!--                </v-btn>-->
        <v-btn
          :disabled="!selected"
          outlined
          dense
          tile
          color="info"
          class="mx-1"
          :loading="loading3"
          @click="sendOA"
          v-permission="['费用申请:发起审批']"
        >
          <v-icon>mdi-arrow-up-bold</v-icon>
          发起审批
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          dense
          tile
          color="error"
          class="mx-1"
          :loading="loading4"
          @click="cancelApproval"
          v-permission="['费用申请:取消审批']"
        >
          <v-icon>mdi-arrow-u-down-left</v-icon>
          取消审批
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/invoice-management/expense-apply-detail/new"
          v-permission="['费用申请:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['费用申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" color="" dark small>草稿</v-chip>
        <v-chip
          v-else-if="item.status === 1 && getMatchingExpenseItem(item.itemName)"
          color="info"
          dark
          small
        >
          审批进行中
        </v-chip>
        <v-chip
          v-else-if="
            item.status === 1 && !getMatchingExpenseItem(item.itemName)
          "
          color="info"
          dark
          small
        >
          OA审批进行中
        </v-chip>
        <v-chip
          v-else-if="item.status == 2 && getMatchingExpenseItem(item.itemName)"
          color="success"
          dark
          small
        >
          审批通过
        </v-chip>
        <v-chip
          v-else-if="item.status == 2 && !getMatchingExpenseItem(item.itemName)"
          color="success"
          dark
          small
        >
          OA审批通过
        </v-chip>
        <v-chip v-else-if="4" color="success" dark small>已发送SAP记账</v-chip>
        <v-chip v-else-if="22" color="error" dark small>发送SAP失败</v-chip>
        <v-chip v-else color="error" dark small>审批未通过</v-chip>
      </template>
      <template v-slot:[`item.shareFlag`]="{ item }">
        <v-chip v-if="item.shareFlag === false" dark small>船东承担</v-chip>
        <v-chip v-else-if="item.shareFlag === true" dark small>
          船员公司承担
        </v-chip>
        <v-chip v-else color="error" dark small>错误状态</v-chip>
      </template>
    </v-table-searchable>
    <!--    <v-dialog v-model="isDialog" width="1000" hide-overlay attach="#mask">-->
    <!--      <v-card>-->
    <!--        <v-card-title>-->
    <!--          备注-->
    <!--          <v-spacer></v-spacer>-->
    <!--          <v-icon @click="closeCard">mdi-close</v-icon>-->
    <!--        </v-card-title>-->
    <!--        <v-card-text>-->
    <!--          <v-form ref="form">-->
    <!--            <v-container>-->
    <!--              <v-row>-->
    <!--                <v-col cols="12" md="3">-->
    <!--                  <vs-date-picker-->
    <!--                    v-model="recordDate"-->
    <!--                    label="日期"-->
    <!--                    :rules="[rules.required]"-->
    <!--                    required-->
    <!--                    outlined-->
    <!--                    dense-->
    <!--                  ></vs-date-picker>-->
    <!--                </v-col>-->
    <!--                <v-col cols="12">-->
    <!--                  <v-btn-->
    <!--                    :loading="loading1"-->
    <!--                    outlined-->
    <!--                    tile-->
    <!--                    color="success"-->
    <!--                    class="mx-1"-->
    <!--                    @click="sendSAPNew"-->
    <!--                    block-->
    <!--                  >-->
    <!--                    <v-icon left>mdi-plus-circle</v-icon>-->
    <!--                    {{ '发送' }}-->
    <!--                  </v-btn>-->
    <!--                </v-col>-->
    <!--              </v-row>-->
    <!--            </v-container>-->
    <!--          </v-form>-->
    <!--        </v-card-text>-->
    <!--      </v-card>-->
    <!--    </v-dialog>-->
  </v-container>
</template>
<script>
export default {
  name: 'expense-apply-management',
  computed: {
    isAdministrator() {
      console.log(this.$local.data.get('userInfo').nickName)
      return (
        this.$local.data.get('userInfo') &&
        this.$local.data.get('userInfo').nickName === '管理员'
      )
    },
  },
  created() {
    this.tableName = '费用申请'
    this.reqUrl = '/business/crew/salary/apply/page'
    this.headers = [
      { text: '申请人姓名', value: 'applyPerson' },
      { text: '所属地', value: 'basePlace' },
      { text: '费用项目', value: 'itemName' },
      { text: '申请日期', value: 'applyDate' },
      { text: '业务日期', value: 'serveDate' },
      { text: '实际真实总额	', value: 'realTotalMoney', formatNumber: true },
      { text: '币种', value: 'billType' },
      { text: '汇率', value: 'exchangeRate' },
      { text: '备注', value: 'remark' },
      { text: '费用承担方', value: 'shareFlag' },
      { text: '费用状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'expense-apply-detail',
    }
    this.searchDate = {
      interval: true,
      label: '发起申请起末时间',
    }
  },

  data() {
    return {
      selected: [],
      toSapSelected: [],
      searchRemain: {},
      newInfo: [],
      errorDialog: false, // 控制弹窗显示的变量
      loading1: false,
      isDialog: false,
      recordDate: '',
      rules: {
        required: (v) => !!v || v == '0' || '必填项不能为空',
      },
      loading2: false,
      loading3: false,
      loading4: false,
      errorMessage: '', // 存储错误信息的变量
      expenseItem: {},
      sendUrl: '',
    }
  },

  methods: {
    async sendSAP() {
      if (!(await this.$dialog.msgbox.confirm('是否确认发送SAP？'))) return
      this.loading2 = true
      // console.log('itemName--', this.selected[0].itemName)
      if (this.getMatchingExpenseItem(this.selected[0].itemName)) {
        this.sendUrl = '/business/crew/salary/salaryItemApply/sendSap'
        // console.log(this.sendUrl)
      } else {
        this.sendUrl = '/business/crew/salary/salaryItemApply/sendSapA'
        // console.log(this.sendUrl)
      }
      const { errorRaw } = await this.getAsync(this.sendUrl, {
        id: this.selected[0].id,
      })
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('发送成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    // openSapNew() {
    //   if (this.selected.length === 0) {
    //     this.$dialog.message.error('请选择记录')
    //     return
    //   }
    //   // if (this.selected[0].status !== 2 && this.selected[0].status !== 22) {
    //   //   this.$dialog.message.error('请选择已审批完成的记录')
    //   //   return
    //   // }
    //   this.isDialog = true
    //   this.toSapSelected = this.selected
    // },
    async sendSAPNew() {
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      if (this.selected[0].status !== 2 && this.selected[0].status !== 22) {
        this.$dialog.message.error('请选择已审批完成的记录')
        return
      }
      this.toSapSelected = this.selected
      // console.log('itemName--', this.toSapSelected[0].itemName)
      if (!(await this.$dialog.msgbox.confirm('是否确认发送SAP？'))) return
      this.loading1 = true
      if (this.getMatchingExpenseItem(this.toSapSelected[0].itemName)) {
        this.sendUrl = '/business/crew/salary/salaryItemApply/sendSapNew'
        // console.log(this.sendUrl)
      } else {
        this.sendUrl = '/business/crew/salary/salaryItemApply/sendSapANew'
        // console.log(this.sendUrl)
      }
      const { errorRaw } = await this.postAsync(this.sendUrl, {
        id: this.toSapSelected[0].id,
      })
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('发送成功')
      this.selected = []
      this.toSapSelected = []
      await this.$refs.table.loadTableData()
    },
    getMatchingExpenseItem(itemName) {
      const item = this.expenseItem.find(
        (expenseItem) => expenseItem.itemName === itemName,
      )
      // 直接返回比较结果，无需额外的if语句
      // console.log('expenseItem', this.expenseItem)
      //
      // console.log('expenseItem.itemName', this.expenseItem.itemName)
      // console.log('item', item)
      // console.log('itemName', itemName)
      //
      // console.log('itemid', item.id)
      // console.log('bizFlag', item.bizFlag === 2)
      return item && item.bizFlag === 2
    },
    async sendOA() {
      const numberOfSelectedItems = this.selected.length
      // console.log(numberOfSelectedItems)
      if (numberOfSelectedItems > 1) {
        this.$dialog.message.info('只能选择一条数据')
        return
      }
      // console.log(this.selected[0])
      if (this.selected[0].status !== 0) {
        this.$dialog.message.error('请选择状态为草稿的记录')
        return
      }
      // if (!(await this.$dialog.msgbox.confirm('是否发送OA审批？'))) return
      // const { errorRaw } = await this.getAsync(`/business/crew/salary/sendOA`, {
      //   id: this.selected[0].id,
      // })
      this.loading3 = true
      // const userId = this.$local.data.get('userInfo').id
      // console.log('userId', userId)
      // console.log('selected', this.selected)
      // if (this.selected.some((e) => e.userId !== userId)) {
      //   this.$dialog.message.error('非申请人不可操作')
      //   this.loading3 = false
      //   return
      // }
      if (this.getMatchingExpenseItem(this.selected[0].itemName)) {
        this.sendUrl = '/business/crew/salary/salaryItemApply/submit'
        // console.log(this.sendUrl)
      } else {
        this.sendUrl = '/business/crew/salary/sendOA'
        // console.log(this.sendUrl)
      }
      if (!(await this.$dialog.msgbox.confirm('是否发送OA审批？'))) return
      const { errorRaw } = await this.getAsync(this.sendUrl, {
        id: this.selected[0].id,
      })
      this.loading3 = false
      // let arr = this.selected.map((ele) => ele.id)
      // const { errorRaw } = await this.getAsync(
      //   `/business/crew/salary/sendOA`,
      //   arr,
      // )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async cancelApproval() {
      let ids = this.selected.map((ele) => ele.id)
      if (
        this.selected.every(
          (e) => e.status !== 1 || !this.getMatchingExpenseItem(e.itemName),
        )
      ) {
        this.$dialog.message.error('请选择审批进行中的记录')
        return
      }
      this.loading4 = true
      if (!(await this.$dialog.msgbox.confirm('是否取消审批？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/crew/salary/salaryItemApply/cancel',
        ids,
      )
      this.loading4 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return

      let arr = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/apply/delete`,
        arr,
      )
      // const { errorRaw } = await this.getAsync(
      //   `/business/crew/salary/apply/delete`,
      //   { id: this.selected.id },
      // )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
    async importError(errorRaw) {
      // console.log(errorRaw)
      this.errorMessage = errorRaw.msg // 更新错误信息
      this.errorDialog = true // 显示弹窗
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/item/feeApplyList`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data.map((val) => val?.itemName)
      this.expenseItem = data
      // console.log('expenseItem', this.expenseItem)
    },
    closeCard() {
      this.recordDate = ''
      this.isDialog = false
    },
  },

  async mounted() {
    await this.getCreFirst()
  },
}
</script>

<style></style>
