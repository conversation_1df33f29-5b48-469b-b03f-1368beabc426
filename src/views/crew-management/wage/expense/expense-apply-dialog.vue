<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1400"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ isEdit ? '新增' : '修改' }}---费用详情
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  label="船名"
                  v-model="formData.shipCode"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="费用部门"
                  outlined
                  dense
                  :items="['山东船员部', '厦门船员部', '上海船员部']"
                  v-model="formData.itemDept"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="付款项目"
                  outlined
                  dense
                  readonly
                  v-model="formData.itemName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="币种"
                  outlined
                  dense
                  item-text="ccyCode"
                  item-value="ccyCode"
                  :items="currencyInfo"
                  v-model="formData.feeType"
                  readonly
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="金额"
                  outlined
                  dense
                  v-model="formData.amount"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-dialog-select
                  label="收款人"
                  dense
                  outlined
                  table-name="收款人选择"
                  :headers="creHeaders"
                  :reqUrl="`/business/crew/baseInfo/salaryApply/page`"
                  itemText="name"
                  itemValue="userId"
                  :search-remain="searchRemain"
                  v-model="formData.payeeUserId"
                  :initSelected="initSelected"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.name"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="身份证号"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.idCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="船员职务"
                        clearable
                        v-model="searchRemain.position"
                      ></v-ship-station>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12">
                <v-text-field
                  label="备注"
                  outlined
                  dense
                  v-model="formData.remark"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  :loading="loading"
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '创建' : '修改' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'expense-apply-dialog',
  mixins: [currencyHelper],
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.creHeaders = [
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
      { text: '银行卡号', value: 'rmbCard' },
    ]
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '部门', value: 'dept' },
      { text: '工资类别', value: 'salaryType' },
      { text: '票据形式', value: 'billForm' },
      { text: '支付方式', value: 'payType' },
      { text: '单独支付标识', value: 'singlePayFlag' },
      { text: '是否计税', value: 'individualTaxFlag' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    initSelected: {
      type: Object,
      default: () => {},
    },
    initSelectedItemId: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      formData: {},
      searchRemain: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    selectItemName(val) {
      this.formData.itemName = val.itemName
      this.formData.itemId = val.itemId
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.loading = true
      const url = this.isEdit
        ? '/business/crew/salary/apply/detail/save'
        : '/business/crew/salary/apply/detail/update'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      this.loading = false
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
      this.$dialog.message.success('保存成功')
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
