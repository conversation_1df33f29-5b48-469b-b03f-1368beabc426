<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['特殊船员杂费:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      :can-submit="canSubmit()"
      :can-save="canSave()"
    >
      <template v-slot:topcontent v-if="detailInfo.auditParams">
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit
              ref="audit"
              :auditParams="auditParams"
              :showParams="isTrue"
            ></v-audit>
          </v-card-text>
        </v-form>
      </template>
      <template #特殊船员杂费基本信息>
        <v-card-text>
          <v-form ref="form">
            <v-container fluid>
              <v-row>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="申请人姓名"
                    required
                    :rules="[rules.required]"
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.applyPerson"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    label="所属地"
                    outlined
                    dense
                    :rules="[rules.required]"
                    :items="[
                      { text: '山东', value: '山东' },
                      { text: '上海', value: '上海' },
                    ]"
                    v-model="detailInfo.basePlace"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="申请日期"
                    outlined
                    dense
                    useToday
                    required
                    :rules="[rules.required]"
                    disabled
                    v-model="detailInfo.applyDate"
                  ></vs-date-picker>
                </v-col>

                <v-col cols="12" md="2">
                  <v-select
                    label="币种"
                    outlined
                    dense
                    item-text="ccyCode"
                    item-value="ccyCode"
                    required
                    :rules="[rules.required]"
                    disabled
                    :items="localcurrencyInfo"
                    v-model="detailInfo.billType"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="对人民币汇率"
                    readonly
                    outlined
                    dense
                    disabled
                    v-model="detailInfo.exchangeRate"
                  ></v-text-field>
                </v-col>
                <!-- <v-col cols="12" md="2" v-if="newCard !== `new`">
                  <v-text-field
                    label="本次计划申请总额"
                    outlined
                    dense
                    v-model="detailInfo.totalMoney"
                  ></v-text-field>
                </v-col> -->
                <v-col cols="12" md="2">
                  <v-select
                    label="付款项目"
                    dense
                    outlined
                    v-model="detailInfo.itemName"
                    table-name="付款项目选择"
                    required
                    :rules="[rules.required]"
                    :items="[
                      { text: '杂费（补发）', value: 特殊船员杂费 },
                      { text: '十三期工资', value: 十三期工资 },
                      { text: '工资（退票）', value: 船员工资重发 },
                    ]"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2" v-if="newCard !== `new`">
                  <v-text-field
                    label="金额"
                    outlined
                    readonly
                    dense
                    disabled
                    v-model="detailInfo.totalMoney"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="8">
                  <v-text-field
                    label="备注"
                    outlined
                    dense
                    :rules="[rules.required]"
                    v-model="detailInfo.remark"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2" v-if="newCard !== `new`">
                  <v-select
                    label="费用状态"
                    outlined
                    dense
                    readonly
                    disabled
                    :items="[
                      { text: '暂未生效', value: 0 },
                      { text: '有费用项目已经发OA', value: 1 },
                      { text: 'OA通过', value: 2 },
                      { text: 'OA未通过', value: 3 },
                    ]"
                    v-model="detailInfo.status"
                  ></v-select>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </template>
      <template #特殊船员杂费详情>
        <v-card-text>
          <v-table-searchable
            ref="table"
            :table-name="tableName"
            v-model="selected"
            :fuzzy-label="fuzzyLabel"
            :headers="headers"
            :req-url="reqUrl"
            :fix-header="false"
            :searchRemain="searchRemain"
          >
            <template #searchflieds>
              <v-col cols="12" md="1">
                <v-ship-select
                  outlined
                  clearable
                  dense
                  v-model="searchRemain.shipCode"
                ></v-ship-select>
              </v-col>
            </template>
            <template v-slot:[`item.operta`]="{ item }">
              <v-btn
                outlined
                tile
                small
                class="mx-1"
                color="info"
                @click="editElectronicchart(item)"
              >
                修改信息
              </v-btn>
            </template>
            <template v-slot:[`item.oaSend`]="{ item }">
              <v-chip v-if="item.oaSend === 0" small dark>未发送OA</v-chip>
              <v-chip v-else small dark color="success">已发送OA</v-chip>
            </template>
            <template v-slot:[`item.oaApproved`]="{ item }">
              <v-chip v-if="item.oaApproved === 0" small dark>未生效</v-chip>
              <v-chip v-else color="success" small dark>生效</v-chip>
            </template>
          </v-table-searchable>
        </v-card-text>
        <v-attach-list
          :attachments="detailInfo.attachmentRecords"
          @change="changeAttachment"
          :disabled="canSave()"
        ></v-attach-list>
      </template>
    </v-detail-view>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="totalDialog"
    >
      <v-card>
        <v-card-title>
          合计
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-table-list
            :headers="detailHeaders"
            :items="this.totalData"
            :show-select="false"
          >
            <template v-slot:[`item.export`]="{ item }">
              <v-btn
                outlined
                tile
                small
                class="mx-1"
                color="info"
                @click="exportShipData(item)"
              >
                导出单船数据
              </v-btn>
            </template>
          </v-table-list>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import VAudit from '@/components/v-audit.vue'
let rate

export default {
  components: { VAudit },
  name: 'special-crew-expense-detail',
  async created() {
    this.backRouteName = 'special-crew-expense'
    this.subtitles = ['特殊船员杂费基本信息', '特殊船员杂费详情']
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '付款项目', value: 'itemName' },
      { text: '币种', value: 'feeType' },
      { text: '金额', value: 'actualPayment' },
      { text: '收款人姓名', value: 'payeeName' },
      { text: '身份证号码', value: 'idCardNumber' },
      { text: '银行账户', value: 'bankAccount' },
      { text: '备注', value: 'remark' },

      { text: '生效时间', value: 'approvedTime' },
    ]
    this.projectHeaders = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目代码', value: 'itemCode' },
      { text: '项目类别', value: 'itemCategory' },
    ]
    this.crewheaders = [
      { text: '船员姓名', value: 'payeeUserId' },
      { text: '付款项目', value: 'itemName' },
      { text: '币种', value: 'feeType' },
      { text: '银行卡号', value: 'rmbCard' },
    ]
    this.detailHeaders = [
      { text: '船名', value: 'shipName' },
      { text: '付款项目', value: 'itemName' },
      { text: '总计', value: 'amount' },
      { text: '操作', value: 'export' },
    ]
    this.reqUrl = '/business/crew/salary/apply/special/detail/page'
    this.tableName = '特殊船员杂费详情'
    this.fuzzyLabel = ''

    await this.getcurrencyInfo()
  },

  data() {
    return {
      dialog1: false,
      title: '新增特殊船员杂费信息',
      selected: false,
      loading1: false,
      detailInfo: {
        applyPerson: this.$local.data.get('userInfo').nickName,
        billType: '',
      },
      localcurrencyInfo: [],
      auditParams: false,
      initialData: {},
      awardData: {},
      initSelected: {},
      initSelectedItemId: {},
      dialog: false,
      inputDialog: false,
      isEdit: false,
      isInput: false,
      isChosen: false,
      creSearchRemain: {
        status: 1,
      },
      searchRemain: {
        applyId: this.$route.params.id,
      },
      rules: {
        required: (v) => !!v || v === 0 || v === false || '必填项不能为空',
      },
      totalData: {},
      totalDialog: false,
    }
  },
  computed: {
    isTrue: function () {
      return (
        this.detailInfo.auditParams.taskId &&
        !this.detailInfo.auditParams.isReject
      )
    },
    newCard: function () {
      // console.log('toatalData', this.totalData)
      return this.$route.params.id
    },
    oaStatus: function () {
      return this.detailInfo.status
    },
  },

  watch: {
    'detailInfo.billType': {
      handler(val) {
        if (val === 'CNY') {
          this.detailInfo.exchangeRate = 1
        } else {
          this.detailInfo.exchangeRate = (1 / rate).toFixed(2)
        }
      },
    },
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    async generateSpecial() {
      if (!(await this.$dialog.msgbox.confirm('是否确认生成？'))) return
      this.loading = true
      const { errorRaw } = await this.getAsync(
        `/business/crew/salary/apply/special/detail/gen`,
        { yearAndMonth: this.yearAndMonth, id: this.$route.params.id },
      )
      this.loading = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.closeDialog()
      await this.$refs.table.loadTableData()
    },
    openDialog() {
      this.dialog1 = true
    },
    canSubmit() {
      return this.detailInfo.status !== 2 && this.detailInfo.status !== 0
    },
    canSave() {
      // console.log('canSave', this.detailInfo.status)
      // console.log(this.detailInfo.status !== 0)
      console.log('canSave', this.detailInfo.status)
      return this.detailInfo.status == 0
    },
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    openDetailInfo() {
      this.totalDialog = true
    },
    closeForm() {
      this.totalDialog = false
    },
    select() {
      // console.log('flag', this.$route.query.flag)
    },
    // selectItemName(val) {
    //   this.detailInfo.itemId = val.id
    // },
    async getDetailInfo() {
      if (this.$route.params.id === `new`) return
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/apply/special/info`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }

      this.title = `${data.applyPerson}-${data.applyDate}-特殊成员杂费详情申请信息`
      this.detailInfo = data
      this.auditParams = data.auditParams
      // console.log('this.auditParams', this.auditParams)
      this.getTotalInfo()
    },
    async getTotalInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/apply/special/sumByShip`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.totalData = data
    },
    async save(goBack) {
      if (!(await this.$dialog.msgbox.confirm('当前基础信息是否确认无误？')))
        return
      // if (!this.$refs.form.validate()) {
      //   this.$dialog.message.error('请填写完整信息')
      //   return
      // }
      // if (this.$refs.aform && !this.$refs.aform.validate()) {
      //   this.loading = false
      //   return
      // }
      const url =
        this.$route.params.id === 'new'
          ? '/business/crew/salary/apply/special/save'
          : '/business/crew/salary/apply/special/update'
      const { errorRaw } = await this.postAsync(url, this.detailInfo)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async submit() {
      // console.log('执行到submit了')
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请填写完整信息')
        return
      }
      if (!this.detailInfo.auditParams) {
        if (!(await this.$dialog.msgbox.confirm('是否确认提交审批？'))) return
        const url =
          this.$route.params.id === 'new'
            ? '/business/crew/salary/apply/special/save'
            : '/business/crew/salary/apply/special/update'
        const { errorRaw1 } = await this.postAsync(url, this.detailInfo)
        if (errorRaw1) {
          return
        }

        //todo 审批方式待修改
        const { errorRaw } = await this.getAsync(
          `/business/crew/salary/salaryItemApply/submit`,
          {
            id: this.$route.params.id,
          },
        )
        if (errorRaw) {
          this.sloading = false
          return
        }
        this.$dialog.message.success('成功提交审批')
      } else {
        if (!(await this.$dialog.msgbox.confirm('是否确认提交审批？'))) return
        const errorRaw = await this.$refs.audit.submit()
        if (errorRaw) {
          this.sloading = false
          return
        }
        this.$dialog.message.success('成功提交审批')
      }
      this.sloading = false
      this.closeAndTo(this.backRouteName, {})
    },
    addExpense() {
      // console.log(this.detailInfo.itemId)

      this.isEdit = true
      this.initialData = {
        applyId: this.$route.params.id,
        feeType: this.detailInfo.billType,
        itemName: this.detailInfo.itemName,
        itemId: this.detailInfo.itemId,
      }
      this.dialog = true
    },
    async inputExpense() {
      this.isChosen = true
      // console.log(this.isChosen)
      this.inputDialog = true
      this.total = false
    },
    async inputCrewPerformance() {
      this.isChosen = false
      this.inputDialog = true
      this.total = false
    },
    async inputAllExpense() {
      this.isChosen = true
      // console.log(this.isChosen)
      this.inputDialog = true
      this.total = true
    },
    async inputAllCrewPerformance() {
      this.isChosen = false
      this.inputDialog = true
      this.total = true
    },
    editElectronicchart(item) {
      if (this.detailInfo.status != 0) {
        return this.$dialog.message.warning('只有待提交状态才能编辑')
      }
      this.initialData = item
      this.initSelected = {
        name: item.payeeName,
        userId: item.payeeUserId,
      }
      this.initSelectedItemId = {
        itemName: item.itemName,
        id: item.id,
      }
      this.isEdit = false
      this.dialog = true
    },
    async exportShipData(item) {
      const baseUrl = '/api/business/crew/salary/apply/special/sumByShipExport'

      const downloadUrl = `${baseUrl}?shipCode=${encodeURIComponent(
        item.shipCode,
      )}&id=${encodeURIComponent(this.$route.params.id)}`

      window.location.href = downloadUrl
    },
    async exportTotalData() {
      const baseUrl = '/api/business/crew/salary/apply/special/allShipSumExport'

      const downloadUrl = `${baseUrl}?id=${encodeURIComponent(
        this.$route.params.id,
      )}`

      window.location.href = downloadUrl
    },
    async exportDetailData() {
      this.loading1 = true
      const baseUrl =
        '/api/business/crew/salary/apply/special/applyDetailExport'
      const downloadUrl = `${baseUrl}?id=${encodeURIComponent(
        this.$route.params.id,
      )}`
      window.location.href = downloadUrl
      this.loading1 = false
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/crew/salary/apply/special/detail/delete',
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      await this.getDetailInfo()
      await this.$refs.table.loadTableData()
    },
    async success() {
      this.selected = false
      await this.getDetailInfo()
      await this.$refs.table.loadTableData()
    },
    async getcurrencyInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/ship/currencyExchangeRate/page`,
      )
      if (errorRaw) return
      const rmbRate =
        data.records.find((ele) => ele.ccyCode === 'CNY')?.rateToThis - '0'
      this.localcurrencyInfo = data.records
        .filter((ele) => {
          if (ele.ccyName === '美元' || ele.ccyName === '人民币') {
            return ele
          }
        })
        .map((ele) => {
          return {
            ccyCode: ele.ccyCode,
            ccyName: ele.ccyName,
            rateToThis: (ele?.rateToThis - '0') / rmbRate,
          }
        })
      rate = data.records.find((ele) => ele.ccyCode === 'CNY')?.rateToThis
    },
  },
  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
