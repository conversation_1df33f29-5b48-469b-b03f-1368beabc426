<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :push-params="pushParams"
      :searchRemain="searchRemain"
      :fix-header="false"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-autocomplete
            label="年份"
            outlined
            dense
            v-model="searchRemain.year"
            :items="yearList"
            clearable
          ></v-autocomplete>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          dense
          :loading="loading1"
          class="mx-1"
          @click="generate"
          color="blue"
          tile
          v-permission="['船员在船天数:生成记录']"
        >
          <v-icon left>mdi-cogs</v-icon>
          生成记录
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'on-ship-days',
  created() {
    this.tableName = '十三期工资'
    this.reqUrl = '/business/crew/shipDays/page'
    this.headers = [
      { text: '姓名', value: 'creName' },
      { text: '身份证号码', value: 'idNumber' },
      { text: '在船天数', value: 'shipDays' },
      { text: '年份', value: 'year' },
      { text: '全年应税收入', value: 'salaryAllSum' },
      { text: '全年已交税额', value: 'monthTaxSum' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {}
    this.rangeArray(2000, 2070)
  },

  data() {
    return {
      yearList: Array.from({ length: 50 }, (_, i) => i + 1),
      selected: false,
      loading1: false,
      searchRemain: {},
      dialog: false,
      startDate: '',
      expireDate: '',
    }
  },

  methods: {
    rangeArray(start, end) {
      let length = end - start + 1
      let step = start - 1
      this.yearList = Array.from({ length: length }, () => {
        step++
        return step
      })
    },
    async generate() {
      this.loading1 = true
      const { errorRaw } = await this.getAsync(
        `/business/crew/shipDays/generate`,
        { year: this.searchRemain.year },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      await this.$refs.table.loadTableData()
    },
  },
  mounted() {},
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
