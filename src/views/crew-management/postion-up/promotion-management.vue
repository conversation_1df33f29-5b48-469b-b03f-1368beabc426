<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      useState
      :fix-header="false"
      :single-select="false"
      :searchRemain="searchRemain"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员工号"
            outlined
            dense
            required
            v-model="searchRemain.creId"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <vs-date-picker
            label="意愿培训时间"
            outlined
            dense
            required
            v-model="searchRemain.fromTime"
            clearable
          ></vs-date-picker>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="审批状态"
            outlined
            dense
            required
            v-model="searchRemain.status"
            clearable
            :items="[
              { text: '未开始', value: 0 },
              { text: '进行中', value: 1 },
              { text: '已完成', value: 2 },
              { text: '驳回', value: 3 },
            ]"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-account-on-broad-postion-up/crew-postion-up-detail/new"
          v-permission="['职务晋升申请:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['职务晋升申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" color="" small dark>未开始</v-chip>
        <v-chip v-else-if="item.status === 1" color="info" small dark>
          进行中
        </v-chip>
        <v-chip v-else-if="item.status === 2" color="success" small dark>
          已完成
        </v-chip>
        <v-chip v-else color="error" small dark>驳回</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'promotion-management',
  created() {
    this.tableName = '职务晋升管理'
    this.reqUrl = '/business/crew/cultivatePostApply/page'
    this.headers = [
      { text: '申请船员姓名', value: 'creName' },
      { text: '现任职务', value: 'curPost' },
      { text: '申请职务', value: 'applyPost' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '意愿培训时间', value: 'favoriteTime' },
      { text: '审批状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'on-broad-postion-up-detail' }
  },

  data() {
    return {
      searchRemain: {},
      selected: [],
    }
  },

  methods: {
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      let newSelected = Array.from(this.selected, (value) => value.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/cultivatePostApply/deleteCultivatePostApply`,
        newSelected,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
