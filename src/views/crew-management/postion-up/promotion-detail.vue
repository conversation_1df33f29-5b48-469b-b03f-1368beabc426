<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['职务晋升申请:编辑']"
      :title="titleName"
      :tooltip="titleName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
    >
      <template
        v-slot:topcontent
        v-if="auditParams && auditParams.processInstanceId"
      >
        <v-card-text class="mt-2 pb-0">
          <v-audit ref="audit" :auditParams="auditParams"></v-audit>
        </v-card-text>
      </template>
      <template v-slot:titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
          v-permission="['职务晋升申请:返回列表']"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <v-btn
          width="60"
          tile
          @click="save"
          color="success"
          small
          v-if="detailInfo.status !== 2"
          class="mx-1"
          :loading="loading"
          v-permission="['职务晋升申请:保存']"
        >
          保存
        </v-btn>
        <v-btn
          width="80"
          tile
          v-if="detailInfo.status === 0 || detailInfo.status === 3"
          @click="submit"
          color="success"
          small
          class="mx-1"
          :loading="sloading"
          v-permission="['职务晋升申请:保存并提交']"
        >
          保存并提交
        </v-btn>
      </template>
      <template v-slot:职务晋升详细信息>
        <v-card-text>
          <v-form ref="form">
            <v-container class="px-0 py-0">
              <v-row>
                <v-col cols="12" md="2">
                  <v-dialog-select
                    v-model="detailInfo.creId"
                    item-value="userId"
                    item-text="name"
                    :disabled="
                      detailInfo.status === 2 || detailInfo.status === 3
                    "
                    req-url="/business/crew/baseInfo/simple/page"
                    :rules="[rules.required]"
                    label="申请船员"
                    :headers="headers"
                    :init-selected="initSelected"
                  ></v-dialog-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-ship-station
                    label="现任职务"
                    :readonly="newCard !== `new`"
                    :rules="[rules.required]"
                    v-model="detailInfo.curPost"
                  ></v-ship-station>
                </v-col>
                <v-col cols="12" md="2">
                  <v-ship-station
                    label="申请岗位"
                    clearable
                    v-model="detailInfo.applyPost"
                  ></v-ship-station>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="意愿培训时间"
                    outlined
                    dense
                    :rules="[rules.required]"
                    v-model="detailInfo.favoriteTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2" v-if="newCard !== `new`">
                  <v-text-field
                    label="业务状态"
                    outlined
                    dense
                    v-model="detailInfo.businessStatus"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2" v-if="newCard !== `new`">
                  <v-select
                    label="当前状态"
                    outlined
                    dense
                    :items="[
                      { text: '未开始', value: 0 },
                      { text: '进行中', value: 1 },
                      { text: '已完成', value: 2 },
                      { text: '已驳回', value: 3 },
                    ]"
                    readonly
                    v-model="detailInfo.status"
                  ></v-select>
                </v-col>
              </v-row>
              <v-attach-list
                :attachments="detailInfo.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list>
            </v-container>
          </v-form>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'promotion-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'promotion-management'
    this.subtitles = ['职务晋升详细信息']
    this.newCard = this.$route.params.id
    this.getPromotionDetail()
    this.headers = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.initSelected = {}
  },
  data() {
    return {
      titleName: '新增职务晋升详情',
      auditParams: false,
      detailInfo: {
        attachmentIds: [],
        attachmentRecords: [],
        status: 0,
      },
      dataId: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      loading: false,
      sloading: false,
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    async submit() {
      this.sloading = true
      if (this.detailInfo.status === 0) {
        const { errorRaw, data } = await this.postAsync(
          '/business/crew/cultivatePostApply/modifyCultivatePostApply',
          this.detailInfo,
        )
        if (errorRaw) {
          return
        }
        const error = await this.getAsync(
          `/business/crew/cultivatePostApply/process/start`,
          { id: data },
        )
        if (error.errorRaw) {
          this.$dialog.message.error('审批提交失败')
          return
        }
      } else {
        const { errorRaw } = await this.postAsync(
          '/business/crew/cultivatePostApply/modifyCultivatePostApply',
          this.detailInfo,
        )
        if (errorRaw) {
          return
        }
        const error = await this.$refs.audit.submit()
        if (error) {
          this.$dialog.message.error(`审批提交失败，请重试`)
          return
        }
        this.$dialog.message.success(`审核提交成功`)
      }

      this.closeAndTo(this.backRouteName, {})
    },
    async save() {
      this.loading = true
      if (this.detailInfo.status === 0) {
        const { errorRaw } = await this.postAsync(
          '/business/crew/cultivatePostApply/modifyCultivatePostApply',
          this.detailInfo,
        )
        if (errorRaw) {
          return
        }
        this.$dialog.message.success('保存成功')
      } else {
        if (this.$refs.audit && !this.$refs.audit.validate()) {
          this.loading = false
          return
        }
        if (!this.$refs.form.validate()) {
          this.loading = false
          return
        }
        const error = await this.$refs.audit.submit()
        if (error) {
          this.$dialog.message.error(`审批提交失败，请重试`)
          return
        }
        this.$dialog.message.success(`审核提交成功`)
      }
      this.closeAndTo(this.backRouteName, {})
    },
    async getPromotionDetail() {
      if (this.newCard !== `new`) {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/cultivatePostApply/getById/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        this.initSelected = { name: data.creName, userId: data.creId }
        this.auditParams = data.auditParams
        this.titleName = this.detailInfo.creName + '---职务晋升详情'
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
