<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['在船船员调薪申请:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      :can-submit="canSubmit"
      :can-save="canSave"
    >
      <template
        v-slot:topcontent
        v-if="auditParams && auditParams.processInstanceId"
      >
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit
              ref="audit"
              :auditParams="auditParams"
              :shipCode="detailInfo.shipCode"
            ></v-audit>
          </v-card-text>
        </v-form>
      </template>
      <v-crew-card v-model="dialog" :initialData="initialData"></v-crew-card>
      <on-broad-management-salary-detail
        v-model="dialog"
        :initialData="initialData"
      ></on-broad-management-salary-detail>
      <template #船员在船职务申请信息>
        <v-container fluid>
          <v-form ref="form">
            <v-card-title>
              <v-btn
                x-large
                text
                v-if="isReadonly"
                color="primary"
                @click="loadPic"
              >
                查看船员详细信息
              </v-btn>
            </v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="2">
                  <v-dialog-select
                    v-model="detailInfo.userId"
                    item-value="userId"
                    item-text="name"
                    req-url="/business/crew/baseInfo/simpleByShip/page"
                    label="申请船员"
                    :headers="crewheaders"
                    :searchRemain="creSearchRemain"
                    :init-selected="initSelected"
                    @select="select"
                    :rules="[rules.required]"
                    :disabled="isReadonly"
                  >
                    <template #searchflieds>
                      <v-col cols="12" md="2">
                        <v-ship-select
                          label="船舶名称"
                          v-model="creSearchRemain.shipCode"
                          clearable
                          :must="[rules.required]"
                        ></v-ship-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="船员姓名"
                          outlined
                          dense
                          v-model="creSearchRemain.name"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="身份证号"
                          outlined
                          dense
                          v-model="creSearchRemain.idCard"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-ship-station
                          label="船员岗位"
                          v-model="creSearchRemain.position"
                          clearable
                        ></v-ship-station>
                      </v-col>
                    </template>
                  </v-dialog-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-ship-select
                    label="申请船舶"
                    v-model="detailInfo.shipCode"
                    :rules="[rules.required]"
                    :disabled="isReadonly"
                  ></v-ship-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-ship-station
                    label="当前职务"
                    v-model="detailInfo.originPost"
                    :rules="[rules.required]"
                    :disabled="isReadonly"
                  ></v-ship-station>
                </v-col>
                <v-col cols="12" md="2">
                  <v-ship-station
                    label="申请晋升职务"
                    v-model="detailInfo.postName"
                    :rules="[rules.required]"
                  ></v-ship-station>
                </v-col>
                <v-col cols="12" md="4" v-if="detailInfo.applyTime">
                  <vs-date-picker
                    label="职务晋升申请时间"
                    outlined
                    dense
                    v-model="detailInfo.applyTime"
                    :disabled="isReadonly"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="4">
                  <vs-date-picker
                    :label="dynamicLabel"
                    outlined
                    dense
                    v-model="detailInfo.changeTime"
                    :rules="[rules.required]"
                    :disabled="isReadonly"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2" v-if="isFlag">
                  <v-dialog-select
                    v-model="detailInfo.applyPost"
                    item-value="post"
                    item-text="post"
                    req-url="/business/crew/osmOnShipCrew/postChange/salaryStandardDetail"
                    label="船东工资方案"
                    :headers="salaryheaders"
                    :searchRemain="salarySearchRemain"
                    :init-selected="initSalarySelected"
                    :disabled="isShipCodeEmpty || isReadonly"
                    @select="selectSalary"
                    :rules="[rules.required]"
                  >
                    <template #searchflieds>
                      <v-col cols="12" md="4">
                        <v-select
                          label="工资标准"
                          outlined
                          dense
                          :items="newInfo"
                          item-value="id"
                          item-text="name"
                          v-model="salarySearchRemain.id"
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2" v-show="1 === 0">
                        <v-ship-station
                          label="职务"
                          outlined
                          dense
                          readyonly
                          v-model="salarySearchRemain.post"
                        ></v-ship-station>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          label="工资标准"
                          outlined
                          dense
                          :items="newInfo"
                          readonly
                          v-model="salarySearchRemain.status"
                        ></v-select>
                      </v-col>
                    </template>
                  </v-dialog-select>
                </v-col>

                <v-col cols="12" md="2" v-if="isFlag">
                  <v-text-field
                    label="变更后实际工资"
                    outlined
                    dense
                    type="number"
                    v-model="detailInfo.salaryStandard"
                    :rules="[rules.number]"
                    :disabled="isReadonly"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2" v-if="isFlag">
                  <v-select
                    label="流程状态"
                    outlined
                    dense
                    :items="[
                      { text: '未开始', value: 0 },
                      { text: '进行中', value: 1 },
                      { text: '申请通过', value: 2 },
                      { text: '驳回', value: 3 },
                    ]"
                    :disabled="true"
                    v-model="detailInfo.status"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2" v-if="isFlag">
                  <v-text-field
                    label="变更后实际船东工资标准"
                    outlined
                    dense
                    type="number"
                    readonly
                    v-model="detailInfo.standard"
                    :rules="[rules.number]"
                    :disabled="true"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="2" v-if="isFlag">
                  <v-select
                    label="是否为数据纠错"
                    outlined
                    dense
                    :items="[
                      { text: '否', value: 0 },
                      { text: '是', value: 2 },
                    ]"
                    v-model="detailInfo.flag"
                    :rules="[rules.required]"
                  ></v-select>
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-textarea
                    v-model="detailInfo.remark"
                    outlined
                    dense
                    label="备注"
                    :disabled="isReadonly"
                  ></v-textarea>
                </v-col>
              </v-row>
              <v-attach-list
                :ship-code="detailInfo.shipCode"
                :disabled="detailInfo.status === 2 || detailInfo.status === 1"
                :attachments="detailInfo.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list>
            </v-card-text>
          </v-form>
        </v-container>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import vCrewCard from '../../private/v-crew-card.vue'
export default {
  components: { vCrewCard },
  name: 'on-broad-postion-up-detail',
  mixins: [routerControl],
  created() {
    this.reqUrl = '/business/common/ship/detailInfo/page'
    this.backRouteName = 'on-broad-postion-up-list'
    this.subtitles = ['船员在船职务申请信息']
    this.crewheaders = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
      { text: '银行卡号', value: 'rmbCard' },
    ]
    this.salaryheaders = [
      { text: '职务', value: 'post' },
      // { text: '标准金额', value: 'standard' },
    ]
    if (this.$route.query.parentId) {
      this.parentId = this.$route.query.parentId
    }
    if (this.$route.query.flag) {
      this.flag = this.$route.query.flag
      console.log('flag', this.flag)
    }
  },
  data() {
    return {
      title: '在船船员职务申请',
      initialData: {},
      dialog: false,
      detailInfo: {
        status: 0,
        flag: 1,
      },
      creSearchRemain: {
        status: 4,
      },
      salarySearchRemain: {
        status: 1,
        id: null, // 初始化为 null 或你所需要的默认值
      },
      initSelected: {},
      initSalarySelected: {},
      sloading: false,
      auditParams: false,
      parentId: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      newInfo: {},
      flag: null,
    }
  },
  computed: {
    isReadonly() {
      return this.detailInfo?.status === 1 || this.detailInfo?.status === 2
    },
    isFlag() {
      return (
        this.flag === '0' ||
        this.detailInfo.type === '调薪' ||
        this.detailInfo.type === '薪酬数据纠正'
      )
    },
    dynamicLabel() {
      //根据按钮判断生效时间中的文字显示
      return this.$route.query.flag === '0'
        ? '调薪生效时间'
        : '职务晋升生效时间'
    },

    computedMenuProps() {
      if (this.isReadonly) {
        return { openOnClick: false }
      } else {
        return {} // 返回默认的menu-props或你想要的其他值
      }
    },
    canSubmit() {
      return this.detailInfo?.status !== 2 && this.detailInfo?.status !== 0
    },
    canSave() {
      return this.detailInfo?.status !== 1 && this.detailInfo?.status !== 2
    },
    isShipCodeEmpty() {
      if (!this.detailInfo.shipCode) {
        console.log('shipCode is not empty')
        return true
      }
      console.log('shipCode is empty')
      return false
    },
  },

  watch: {
    'detailInfo.shipCode': function (newVal) {
      // 当 detailInfo.shipCode 变化时，此函数将被调用
      this.getSalaryFirst(newVal)
    },
    newInfo: {
      immediate: true, // 这确保了 watcher 在初始化时会执行一次
      handler(val) {
        if (val.length > 0) {
          this.salarySearchRemain.id = val[0].id
          this.chosenPost()
        }
      },
    },
    isFlag(newVal) {
      if (newVal) {
        // 如果 isFlag 为 true
        this.detailInfo.flag = 1 // 给 flag 赋值为 1
      }
    },
    'detailInfo.flag'(newVal) {
      if (newVal === 2) {
        // 如果选择是
        this.$dialog.msgbox.alert(
          '数据纠错功能旨在修改之前存在问题的数据。使用此功能可能会覆盖船员最新的工资记录。请仅在确认船员数据确实存在错误时，才使用此功能。',
        )
        // 弹出提示
      }
    },
  },
  methods: {
    loadPic() {
      console.log('this.detailInfo', this.detailInfo)
      this.initialData = { userId: this.detailInfo.userId }
      this.dialog = true
    },
    async getSalaryFirst(newVal) {
      // if (this.flag == 1) {
      //   return
      // }
      if (!newVal) return
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/osmOnShipCrew/postChange/salaryStandard`,
        { shipCode: newVal },
      )
      if (errorRaw) {
        console.log(errorRaw)
        return
      }
      this.newInfo = data
      console.log(this.newInfo)
    },
    chosenPost() {
      //判断通过不晋升调薪按钮新增时，申请晋升职务默认为当前职务
      if (this.$route.query.flag === `0` && this.parentId == null) {
        this.salarySearchRemain.post = this.detailInfo.originPost
      }
    },
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    open() {
      console.log(this.detailInfo)
      this.salarySearchRemain.shipCode = this.detailInfo.shipCode
    },
    select(val) {
      console.log('flag', this.$route.query.flag)
      if (this.$route.params.id === `new`) {
        console.log('---')
      }
      this.detailInfo.originPost = val.position
      this.detailInfo.shipCode = this.creSearchRemain.shipCode
    },
    selectSalary(val) {
      this.detailInfo.standard = val.standard
    },
    async getDetailInfo() {
      console.log('parentId', this.parentId)
      if (this.$route.params.id === `new`) {
        if (this.parentId != null) {
          const { errorRaw, data } = await this.getAsync(
            `/business/crew/osmOnShipCrew/postChange/detail`,
            { id: this.parentId },
          )
          if (errorRaw) {
            return
          }
          this.title =
            data.crewName + '---' + data.shipName + '---在船船员调薪申请表'
          this.initSelected = { name: data.crewName, userId: data.userId }
          console.log(this.detailInfo)
          this.detailInfo = data
          this.detailInfo.id = null
          this.detailInfo.status = 0
          this.detailInfo.applyTime = null
          this.detailInfo.expireDate = null
          this.detailInfo.applyPost = null
          this.detailInfo.salaryStandard = null
          this.salarySearchRemain.post = null
        }
        return
      }
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/osmOnShipCrew/postChange/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.title =
        data.crewName + '---' + data.shipName + '---在船船员调薪申请表'
      this.initSelected = { name: data.crewName, userId: data.userId }
      this.initSalarySelected = { post: data.applyPost }
      this.auditParams = data.auditParams
      this.detailInfo = data
      if (this.detailInfo.type == '职务晋升') {
        this.flag = 1
      } else {
        this.flag = 0
      }
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return false
      }
      if (this.detailInfo.status === 2 || this.detailInfo.status === 1) {
        goBack()
        return
      }
      if (this.$route.query.parentId) {
        this.detailInfo.parentId = this.$route.query.parentId
      }
      const url =
        this.$route.params.id === `new`
          ? '/business/crew/osmOnShipCrew/postChange/save'
          : `/business/crew/osmOnShipCrew/postChange/update`
      const { errorRaw } = await this.postAsync(url, this.detailInfo)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async submit() {
      console.log('----------------')
      console.log(this.$refs.audit)
      // if (!this.$refs.form.validate()) {
      //   return false
      // }
      if (!this.$refs.aform.validate()) {
        return false
      }

      this.sloading = true
      const url =
        this.$route.params.id === `new`
          ? '/business/crew/osmOnShipCrew/postChange/save'
          : `/business/crew/osmOnShipCrew/postChange/update`
      const { errorRaw, data } = await this.postAsync(url, this.detailInfo)
      if (errorRaw) {
        return
      }
      let id = this.$route.params.id === `new` ? data : this.$route.params.id
      this.$dialog.message.success('保存成功')
      if (!this.detailInfo.auditParams) {
        const error = await this.getAsync(
          `/business/crew/osmOnShipCrew/postChange/submit`,
          { id: id },
        )
        if (error.errorRaw) {
          return
        }
        this.$dialog.message.success('成功发起审批')
      } else {
        const error = await this.$refs.audit.submit()
        if (error) {
          this.$dialog.message.error('审批发起失败')
          return
        }
        this.$dialog.message.success('审批发起成功')
      }
      this.sloading = false
      this.closeAndTo(this.backRouteName)
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
