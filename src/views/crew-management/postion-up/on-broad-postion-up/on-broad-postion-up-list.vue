<template>
  <v-container fluid>
    <!-- <v-card v-if="isShow">
      <v-card-title>
        调整实际生效时间
        <v-spacer></v-spacer>
        <v-icon @click="isShow = false">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="3">
            <vs-date-picker
              label="实际生效时间"
              outlined
              dense
              v-model="changeTime"
            ></vs-date-picker>
          </v-col>
        </v-row>
        <v-btn outlined tile color="success" class="mx-1" @click="change" block>
          <v-icon left>mdi-plus-circle</v-icon>
          保存
        </v-btn>
      </v-card-text>
    </v-card> -->
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :push-params="pushParams"
      :showExportButton="true"
      use-ship
    >
      <template #searchflieds>
        <!-- <v-col cols="12" md="2">
          <v-dialog-select
            v-model="searchRemain.userName"
            item-value="userId"
            item-text="name"
            req-url="/business/crew/baseInfo/simple/page"
            label="申请船员"
            :headers="crewheaders"
            :searchRemain="creSearchRemain"
          >
            <template #searchflieds>
              <v-col cols="12" md="2">
                <v-text-field
                  label="船员姓名"
                  outlined
                  dense
                  v-model="creSearchRemain.name"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="身份证号"
                  outlined
                  dense
                  v-model="creSearchRemain.idCard"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-ship-station
                  label="船员岗位"
                  v-model="creSearchRemain.position"
                  clearable
                ></v-ship-station>
              </v-col>
            </template>
          </v-dialog-select>
        </v-col> -->
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            v-model="searchRemain.userName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="申请晋升职务"
            outlined
            dense
            v-model="searchRemain.postName"
            clearable
          ></v-ship-station>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="调薪类型"
            outlined
            dense
            :items="[
              { text: '职务晋升', value: '职务晋升' },
              { text: '调薪', value: '调薪' },
              { text: '薪酬数据纠正', value: '薪酬数据纠正' },
            ]"
            clearable
            v-model="searchRemain.type"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="流程状态"
            outlined
            dense
            :items="[
              { text: '未开始', value: 0 },
              { text: '进行中', value: 1 },
              { text: '申请通过', value: 2 },
              { text: '驳回', value: 3 },
              { text: '完成', value: 4 },
            ]"
            clearable
            v-model="searchRemain.status"
          ></v-select>
        </v-col>

        <v-col cols="12" md="2">
          <v-select
            label="申请船员级别"
            clearable
            v-model="searchRemain.businessType"
            :items="[
              { text: '甲板部在船职务晋升申请', value: 'positionChange1' },
              { text: '轮机部在船职务晋升申请', value: 'positionChange2' },
              { text: '船员调薪（未超浮动）', value: 'positionChange3' },
              { text: '船员调薪（超过浮动）', value: 'positionChange4' },
            ]"
            dense
            outlined
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchRemain.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="isShow = true"
          v-permission="['在船船员调薪:调整实际生效时间']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          调整实际生效时间
        </v-btn> -->
        <v-btn
          :disabled="!selected"
          :loading="loading"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="upload"
          v-permission="['在船船员调薪:发起审批']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          发起审批
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="handleClick"
          v-permission="['在船船员调薪:发起晋升对应的调薪']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          发起晋升对应的调薪
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{
            path: '/crew-management/business-list/on-broad-postion-up-detail/new',
            query: { flag: '0' },
          }"
          v-permission="['在船船员调薪:新增调薪申请']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增调薪申请
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{
            path: '/crew-management/business-list/on-broad-postion-up-detail/new',
            query: { flag: '1' },
          }"
          v-permission="['在船船员调薪:新增晋升申请']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增晋升申请
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['在船船员调薪:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0 && item.type === '调薪'" color="" small>
          调薪草稿
        </v-chip>
        <v-chip
          v-else-if="item.status === 0 && item.type === '职务晋升'"
          color=""
          small
        >
          职务晋升草稿
        </v-chip>
        <v-chip
          v-else-if="item.status === 0 && item.type === '薪酬数据纠正'"
          color=""
          small
        >
          薪酬数据纠正草稿
        </v-chip>
        <v-chip
          v-else-if="item.status === 1 && item.type === '调薪'"
          color="info"
          small
        >
          调薪进行中
        </v-chip>
        <v-chip
          v-else-if="item.status === 1 && item.type === '职务晋升'"
          color="info"
          small
        >
          职务晋升进行中
        </v-chip>
        <v-chip
          v-else-if="item.status === 1 && item.type === '薪酬数据纠正'"
          color="info"
          small
        >
          薪酬数据纠正进行中
        </v-chip>
        <v-chip
          v-else-if="item.status === 2 && item.type === '调薪'"
          color="success"
          small
        >
          调薪申请通过
        </v-chip>
        <v-chip
          v-else-if="item.status === 2 && item.type === '职务晋升'"
          color="yellow"
          small
        >
          申请通过,待发起调薪流程
        </v-chip>
        <v-chip
          v-else-if="item.status === 2 && item.type === '薪酬数据纠正'"
          color="success"
          small
        >
          薪酬数据纠正通过
        </v-chip>
        <v-chip v-else-if="item.status === 3" color="error" small>驳回</v-chip>
        <v-chip
          v-else-if="item.status === 4 && item.type === '职务晋升'"
          color="success"
          small
        >
          职务晋升申请通过
        </v-chip>
        <v-chip v-else color="error" small>未知状态</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'on-broad-postion-up-list',
  created() {
    this.tableName = '船员在船船员调薪申请名单'
    this.reqUrl = '/business/crew/osmOnShipCrew/postChange/page'
    this.headers = [
      { text: '类型', value: 'type' },
      { text: '申请船舶', value: 'shipName' },
      { text: '申请船员', value: 'crewName' },

      { text: '当前职务', value: 'originPost' },
      { text: '申请晋升职务', value: 'postName' },
      { text: '申请时间', value: 'applyTime' },
      { text: '生效时间', value: 'changeTime' },
      { text: '业务状态', value: 'status' },
    ]
    this.crewheaders = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '调薪申请时间',
      interval: true,
    }
    this.pushParams = { name: 'on-broad-postion-up-detail' }
    this.nowUserId = this.$store.state.user.userId
  },

  data() {
    return {
      selected: false,
      searchRemain: { isMe: false, businessType: 'positionChange1' },
      creSearchRemain: {},
      isShow: false,
      loading: false,
    }
  },

  methods: {
    handleClick() {
      if (this.selected && this.selected.id) {
        if (this.selected.type == '职务晋升' && this.selected.status == 2) {
          this.$router.push({
            path: '/crew-management/business-list/on-broad-postion-up-detail/new',
            query: { flag: '0', parentId: this.selected.id },
          })
        } else {
          return this.$dialog.message.error(
            '请选择待发起调薪流程的职务晋升流程！',
          )
        }
      } else {
        // 可以添加一些错误处理，例如提示用户选择一个选项
        alert('请选择一个有效的选项')
      }
    },
    async change() {
      if (!(await this.$dialog.msgbox.confirm('确定修改此记录的生效时间？')))
        return
      if (!this.changeTime) {
        return this.$dialog.message.warning('请选择生效时间！')
      }
      if (this.selected.status !== '2') {
        return this.$dialog.message.warning(
          '只有审批通过的记录才能修改生效时间！',
        )
      }
      const { errorRaw } = await this.getAsync(
        `/business/crew/osmOnShipCrew/postChange/changeTime`,
        { id: this.selected.id, changeTime: this.changeTime },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('修改成功！')
      await this.$refs.table.loadTableData()
      this.isShow = false
    },
    async delAudit() {
      if (this.selected.status !== 0 && this.selected.status !== 3) {
        this.$dialog.message.error('只有草稿和驳回的记录才可删除！')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/osmOnShipCrew/postChange/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功！')
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async upload() {
      if (this.selected.status !== 0 && this.selected.status !== 3) {
        return this.$dialog.message.warning(
          '只有草稿和驳回的记录才能发起审批！',
        )
      }
      if (!(await this.$dialog.msgbox.confirm('是否将选中记录发起审批？')))
        return
      this.loading = true
      const { errorRaw } = await this.getAsync(
        `/business/crew/osmOnShipCrew/postChange/submit`,
        { id: this.selected.id },
      )
      this.loading = false
      if (errorRaw) {
        return
      }
      this.selected = false
      await this.$refs.table.loadTableData()
      this.$dialog.message.success('审批发送成功')
    },
  },

  mounted() {},
}
</script>

<style></style>
