<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['劳动合同查询:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template v-slot:劳动合同详情>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="员工工号"
                  outlined
                  dense
                  :rules="[rules.required]"
                  v-model="detailInfo.empId"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-dialog-select
                  v-model="detailInfo.creId"
                  item-value="creId"
                  item-text="name"
                  req-url="/business/crew/baseInfo/simple/page"
                  :rules="[rules.required]"
                  label="申请船员"
                  :headers="creHeaders"
                  :init-selected="initSelected"
                  :search-remain="searchRemain"
                  @select="selectCrew"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        v-model="searchRemain.name"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="船员身份证号码"
                        outlined
                        dense
                        v-model="searchRemain.idCard"
                      ></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="身份证号"
                  outlined
                  dense
                  readonly
                  v-model="detailInfo.idNo"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="签署公司"
                  outlined
                  :items="comItems"
                  dense
                  :rules="[rules.required]"
                  v-model="detailInfo.signedCompany"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="档案地址"
                  outlined
                  dense
                  v-model="detailInfo.storePlace"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-handler v-model="detailInfo.handler"></v-handler>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  dense
                  label="合同签署时间"
                  :rules="[rules.required]"
                  v-model="detailInfo.signedDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  dense
                  label="合同起始时间"
                  :rules="[rules.required]"
                  v-model="detailInfo.startDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  dense
                  label="合同结束时间"
                  :rules="[rules.required]"
                  v-model="detailInfo.endDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  v-if="newCard !== `new`"
                  outlined
                  dense
                  label="预警时间"
                  :rules="[rules.required]"
                  v-model="detailInfo.warningDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  v-if="newCard !== `new`"
                  outlined
                  dense
                  label="操作时间"
                  v-model="detailInfo.operateDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  v-if="newCard !== `new`"
                  label="合同状态"
                  outlined
                  readonly
                  dense
                  :items="[
                    { text: '大于两个月到期', value: 1 },
                    { text: '小于两个月到期', value: 2 },
                    { text: '已过期', value: 3 },
                    { text: '无固定期限', value: 4 },
                  ]"
                  v-model="detailInfo.contractStatus"
                ></v-select>
              </v-col>
            </v-row>
            <v-attach-list
              title="劳动合同附件"
              :attachments="detailInfo.attachmentRecords"
              @change="changeAttachment"
            ></v-attach-list>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:历史劳动合同>
        <v-card-text>
          <v-table-list :headers="headers" :items="detailInfo.historyRecords">
            <template v-slot:[`item.contractStatus`]="{ item }">
              {{
                item.statuscontractStatus === 1
                  ? '大于两个月到期'
                  : item.contractStatus === 2
                  ? '小于两个月到期'
                  : '已到期'
              }}
            </template>
          </v-table-list>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'contract-detail',
  created() {
    this.backRouteName = 'contract-management'
    this.subtitles = ['劳动合同详情', '历史劳动合同']
    this.headers = [
      { text: '合同编码', value: 'contractCode' },
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idNo' },
      { text: '经办人', value: 'handler' },
      { text: '签约公司', value: 'signedCompany' },
      { text: '签署时间', value: 'signedDate' },
      { text: '起始时间', value: 'startDate' },
      { text: '存放位置', value: 'storePlace' },
      { text: '预警时间', value: 'warningDate' },
      { text: '结束日期', value: 'endDate' },
      { text: '合同状态', value: 'contractStatus' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.creHeaders = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.newCard = this.$route.params.id
    this.getDetailInfo()
    this.getComThree()
    this.initSelected = {}
  },
  data() {
    return {
      title: '新增劳动合同记录',
      detailInfo: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      comItems: [],
      searchRemain: {},
    }
  },

  methods: {
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (!(await this.$dialog.msgbox.confirm('填写的信息是否已确认无误？')))
        return

      if (this.newCard === `new`) {
        const { errorRaw } = await this.postAsync(
          `/business/crew/labelContract/save`,
          this.detailInfo,
        )
        if (errorRaw) {
          this.$dialog.message.error(`保存失败，请重试`)
          return
        }
        this.$dialog.message.success(`保存成功`)
      } else {
        const { errorRaw } = await this.postAsync(
          `/business/crew/labelContract/update`,
          this.detailInfo,
        )
        if (errorRaw) {
          this.$dialog.message.error(`修改失败，请重试`)
          return
        }
        this.$dialog.message.success(`修改成功`)
      }
      goBack()
    },
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    async getDetailInfo() {
      if (this.newCard !== `new`) {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/labelContract/detail`,
          { id: this.$route.params.id },
        )
        if (errorRaw) {
          return
        }
        this.title = data.name + '---劳动合同详情'
        this.detailInfo = data
        this.initSelected = { name: data.name, userId: data.creId }
      }
    },
    async getComThree() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/thirdProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.comItems = data
    },
    selectCrew(val) {
      this.detailInfo.idNo = val.idCard
    },
  },

  mounted() {},
}
</script>

<style></style>
