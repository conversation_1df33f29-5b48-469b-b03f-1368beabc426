<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :searchRemain="searchRemain"
      :req-url="reqUrl"
      :push-params="pushParams"
      :fix-header="false"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            dense
            outlined
            v-model="searchRemain.name"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <vs-date-picker
            outlined
            dense
            label="合同起始时间"
            v-model="searchRemain.startDate"
          ></vs-date-picker>
        </v-col>
        <v-col cols="12" md="2">
          <vs-date-picker
            outlined
            dense
            label="合同结束时间"
            v-model="searchRemain.endDate"
          ></vs-date-picker>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="合同状态"
            clearable
            outlined
            dense
            :items="[
              { text: '大于两个月到期', value: 1 },
              { text: '小于两个月到期', value: 2 },
              { text: '已过期', value: 3 },
              { text: '无固定期限', value: 4 },
            ]"
            v-model="searchRemain.contractStatus"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-account-information-labor-contract-detail/new"
          v-permission="['劳动合同查询:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="openHistory"
          v-permission="['劳动合同查询:展示历史合同']"
        >
          <v-icon left>mdi-eye-circle</v-icon>
          展示历史合同
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="blue"
          class="mx-1"
          :href="`/api/business/crew/labelContract/export?idNumber=${selected.idNo}&name=${selected.name}`"
          target="_blank"
          v-permission="['劳动合同查询:导出历史合同']"
        >
          <v-icon left>mdi-arrow-down-bold</v-icon>
          导出历史合同
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['劳动合同查询:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.contractStatus`]="{ item }">
        <!-- {{
          item.statuscontractStatus === 1
            ? '大于两个月到期'
            : item.contractStatus === 2
            ? '小于两个月到期'
            : '已到期'
        }} -->
        <v-chip small dark color="info" v-if="item.contractStatus === 1">
          大于两个月到期
        </v-chip>
        <v-chip
          small
          dark
          color="warning"
          v-else-if="item.contractStatus === 2"
        >
          小于两个月到期
        </v-chip>
        <v-chip
          small
          dark
          color="success"
          v-else-if="item.contractStatus === 4"
        >
          无固定期限
        </v-chip>
        <v-chip v-else small dark color="error">已过期</v-chip>
      </template>
    </v-table-searchable>
    <v-dialog v-model="dialog" hide-overlay max-width="1200">
      <v-card>
        <v-card-title>
          历史合同查询
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-table-list
            :headers="historyHeaders"
            :items="selected.historyRecords"
          >
            <!-- <template v-slot:[`item.contractStatus`]="{ item }">
              {{
                item.statuscontractStatus === 1
                  ? '大于两个月到期'
                  : item.contractStatus === 2
                  ? '小于两个月到期'
                  : '已到期'
              }}
            </template> -->
            <template v-slot:[`item.contractStatus`]="{ item }">
              {{
                item.contractStatus === 1
                  ? '大于两个月到期'
                  : item.contractStatus === 2
                  ? '小于两个月到期'
                  : item.contractStatus === 3
                  ? '已到期'
                  : item.contractStatus === 4
                  ? '无固定期限'
                  : '未知状态'
              }}
            </template>
          </v-table-list>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
export default {
  name: 'contract-management',
  created() {
    this.tableName = '劳动合同查询'
    this.reqUrl = '/business/crew/labelContract/page'
    this.headers = [
      { text: '合同编码', value: 'contractCode' },
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idNo' },
      { text: '经办人', value: 'handler' },
      { text: '签约公司', value: 'signedCompany' },
      { text: '签署时间', value: 'signedDate' },
      { text: '起始时间', value: 'startDate' },
      { text: '结束日期', value: 'endDate' },
      { text: '预警时间', value: 'warningDate' },
      { text: '存放位置', value: 'storePlace' },
      { text: '签订合同次数', value: 'contractNum' },
      { text: '合同状态', value: 'contractStatus' },
    ]
    this.historyHeaders = [
      { text: '合同编码', value: 'contractCode' },
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idNo' },
      { text: '经办人', value: 'handler' },
      { text: '签约公司', value: 'signedCompany' },
      { text: '签署时间', value: 'signedDate' },
      { text: '起始时间', value: 'startDate' },
      { text: '结束日期', value: 'endDate' },
      { text: '预警时间', value: 'warningDate' },
      { text: '签订合同次数', value: 'contractNum' },
      { text: '合同状态', value: 'contractStatus' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'contract-detail',
    }
  },

  data() {
    return {
      searchRemain: {},
      selected: false,
      dialog: false,
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/labelContract/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
    },
    openHistory() {
      this.dialog = true
    },
    closeForm() {
      this.dialog = false
    },
  },

  mounted() {},
}
</script>

<style></style>
