<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船东考核标准:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #船东考核标准基本信息>
        <v-form ref="form">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="标准配员人数"
                  outlined
                  dense
                  v-model="detailInfo.personnelNumber"
                  type="number"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="伙食费标准(/天/人)"
                  v-model="detailInfo.mealStandard"
                  outlined
                  dense
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="管理费标准(/月/人)"
                  outlined
                  dense
                  v-model="detailInfo.managementStandard"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="实习生管理费标准(/月/人)"
                  outlined
                  dense
                  v-model="detailInfo.internManagementStandard"
                  type="number"
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  label="集结费标准(/年/人)"
                  outlined
                  dense
                  v-model="detailInfo.gatheringStandard"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="协议有效期开始时间"
                  outlined
                  dense
                  v-model="detailInfo.validityTermFrom"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="协议有效期结束时间"
                  outlined
                  dense
                  v-model="detailInfo.validityTermEnd"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="是否发送OA"
                  outlined
                  dense
                  :items="[
                    { text: '已发送OA', value: 1 },
                    { text: '未发送OA', value: 0 },
                  ]"
                  v-model="detailInfo.oaStatus"
                  readonly
                ></v-select>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
    </v-detail-view>
    <owner-expenses-dialog
      v-model="dialog"
      :initialData="initialData"
      @success="success"
    ></owner-expenses-dialog>
  </v-container>
</template>
<script>
import ownerExpensesDialog from './owner-expenses-dialog.vue'
export default {
  components: { ownerExpensesDialog },
  name: 'owner-expenses-detail',
  created() {
    this.backRouteName = 'owner-expenses-management'
    this.subtitles = ['船东考核标准基本信息']
    this.headers = [
      { text: '岗位', value: 'position' },
      { text: '岗位人数', value: 'num' },
      { text: '月工资标准（$）', value: 'standard' },
    ]
  },
  data() {
    return {
      title: '新增船东费用月结详情',
      detailInfo: {
        oaStatus: 0,
        wagesStandard: [],
      },
      initialData: {},
      list: [],
      selected: false,
      dialog: false,
    }
  },
  watch: {
    'detailInfo.shipCode'(newVal, oldVal) {
      console.log(`shipCode 变化: ${oldVal} -> ${newVal}`)
      this.changeCount()
    },
  },

  methods: {
    async changeCount() {
      // 发送请求
      const data = await this.getAsync(
        `/ownerMonthCharge/standard/personNumberByShipCode`,
        { shipCode: this.detailInfo.shipCode },
      )
      console.log(data.data)
      this.detailInfo.personnelNumber = data.data
      console.log(this.detailInfo.personnelNumber)
      this.$nextTick(() => {
        console.log('Vue DOM 已更新', this.detailInfo.personnelNumber)
      })
    },
    async save(goBack) {
      if (this.detailInfo.oaStatus === 1) {
        goBack()
        return
      }
      const { errorRaw } = await this.postAsync(
        `/ownerMonthCharge/standard/saveOrUpdate`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message
      goBack()
    },
    async getDetailInfo() {
      if (this.$route.params.id === `new`) return
      const { errorRaw, data } = await this.getAsync(
        `/ownerMonthCharge/standard/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = data
      this.title = `${data.shipName}-船东考核标准详情`
    },
    delAudit() {
      const len = this.detailInfo.wagesStandard.length
      this.detailInfo.wagesStandard = this.detailInfo.wagesStandard.filter(
        (ele) => ele.position !== this.selected.position,
      )
      if (len === this.detailInfo.wagesStandard.length) {
        this.$dialog.message.error('删除失败')
      } else {
        this.$dialog.message.success('删除成功')
      }
      this.selected = false
    },
    success(val) {
      if (this.detailInfo.wagesStandard === null) {
        this.detailInfo.wagesStandard = []
      }
      const len = this.detailInfo?.wagesStandard.length
      if (
        this.detailInfo.wagesStandard.find(
          (ele) => ele.position === val.position,
        )
      ) {
        this.$dialog.message.error('该岗位已存在')
        return
      }
      this.detailInfo.wagesStandard.unshift(val)
      if (len === this.detailInfo?.wagesStandard.length) {
        this.$dialog.message.error('添加失败')
      } else {
        this.$dialog.message.success('添加成功')
      }
    },
    create() {
      this.initialData = {}
      this.dialog = true
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
