<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="isShow">
        <v-card-title>
          {{ isEdit ? '新增' : '修改' }}---船东考核标准
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
            </v-col>
            <!-- <v-col cols="12" md="2">
              <v-text-field
                label="标准配员人数"
                outlined
                dense
                v-model="detailInfo.personnelNumber"
                type="number"
              ></v-text-field>
            </v-col> -->
            <v-col cols="12" md="2">
              <v-text-field
                label="伙食费标准(/天/人)"
                v-model="detailInfo.mealStandard"
                outlined
                dense
                type="number"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="管理费标准(/月/人)"
                outlined
                dense
                v-model="detailInfo.managementStandard"
                type="number"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="实习生管理费标准(/月/人)"
                outlined
                dense
                v-model="detailInfo.internManagementStandard"
                type="number"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="2">
              <v-text-field
                label="集结费标准(/年/人)"
                outlined
                dense
                v-model="detailInfo.gatheringStandard"
                type="number"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                label="协议有效期开始时间"
                outlined
                dense
                v-model="detailInfo.validityTermFrom"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                label="协议有效期结束时间"
                outlined
                dense
                v-model="detailInfo.validityTermEnd"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否发送OA"
                outlined
                dense
                :items="[
                  { text: '已发送OA', value: 1 },
                  { text: '未发送OA', value: 0 },
                ]"
                v-model="detailInfo.oaStatus"
                readonly
              ></v-select>
            </v-col>
          </v-row>
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            :loading="loading1"
            @click="save"
            block
            v-permission="['船东考核标准:保存']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            保存
          </v-btn>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      @dbclick="editElectronicchart"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-select
            label="船舶所属船员公司"
            outlined
            dense
            required
            :items="companys"
            item-text="dictLabel"
            item-value="dictValue"
            v-model="searchRemain.creComCode"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="add"
          v-permission="['船东考核标准:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['船东考核标准:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.oaStatus`]="{ item }">
        <v-chip v-if="item.oaStatus" color="success" small dark>
          已发送OA
        </v-chip>
        <v-chip v-else color="" small dark>未发送</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'owner-expenses-management',
  created() {
    this.tableName = '船东考核标准'
    this.reqUrl = '/ownerMonthCharge/standard/page'
    this.headers = [
      { text: '船名', value: 'shipName' },
      {
        text: '标准配员人数',
        value: 'personnelNumber',
      },
      { text: '伙食费标准(/天/人)', value: 'mealStandard' },
      { text: '管理费标准(/月/人)', value: 'managementStandard' },
      { text: '集结费标准(/年/人)', value: 'gatheringStandard' },
      { text: '协议有效期开始时间', value: 'validityTermFrom' },
      { text: '协议有效期结束时间', value: 'validityTermEnd' },
      { text: '是否发送OA', value: 'oaStatus' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'owner-expenses-detail' }
  },

  data() {
    return {
      selected: false,
      isShow: false,
      detailInfo: {},
      companys: [],
      searchRemain: {},
    }
  },

  methods: {
    async save() {
      if (this.detailInfo.oaStatus === 1) {
        this.$dialog.message.error('已发送状态无法编辑')
        return
      }
      const { errorRaw } = await this.postAsync(
        `/ownerMonthCharge/standard/saveOrUpdate`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      this.selected = false
      this.isShow = false
      this.detailInfo = {}
      await this.$refs.table.loadTableData()
    },
    editElectronicchart() {
      this.detailInfo = { ...this.selected }
      this.isEdit = false
      this.isShow = true
    },
    closeForm() {
      this.selected = []
      this.detailInfo = {}
      this.isEdit = false
      this.isShow = false
    },
    add() {
      this.selected = false
      this.salaryItem = {}
      this.isEdit = true
      this.isShow = true
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/CreCompanyDic/list`,
      )
      if (errorRaw) {
        return
      }
      this.companys = data
      console.log('companys', this.companys)
    },
    async del() {
      if (this.selected.oaStatus === 1) {
        this.$dialog.message.error('该记录已发送OA，无法删除')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        `/ownerMonthCharge/standard/delete?id=${this.selected.id}`,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  async mounted() {
    await this.getCreFirst()
  },
}
</script>

<style></style>
