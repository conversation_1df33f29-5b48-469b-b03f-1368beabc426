<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船员公司收入:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-save="false"
    >
      <template #船员公司收入基本信息>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-text-field
                label="公司名称"
                outlined
                readonly
                dense
                v-model="detailInfo.company"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="所属年月"
                outlined
                readonly
                dense
                v-model="detailInfo.yearAndMonth"
              ></v-text-field>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <template #费用>
        <v-table-searchable
          ref="table"
          :table-name="tableName"
          v-model="selected"
          :single-select="false"
          :fuzzy-label="fuzzyLabel"
          :headers="headers"
          :req-url="reqUrl"
          :search-remain="searchRemain"
          :fix-header="false"
          :push-params="pushParams"
        ></v-table-searchable>
      </template>
    </v-detail-view>
  </v-container>
</template>

<script>
export default {
  name: 'company-income-detail',
  created() {
    this.backRouteName = 'company-income'
    this.subtitles = ['船员公司收入基本信息', '费用']
    this.tableName = '收入明细'
    this.reqUrl = '/business/crew/company/income/detail/page'
    this.searchRemain = {
      parentId: this.$route.params.id,
    }
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '金额', value: 'alloSum' },
    ]
    this.fuzzyLabel = ''
  },
  data() {
    return {
      itemDetailList: [],
      totalDialog: false,
      loading2: false,
      title: '船员公司收入详情',
      detailInfo: {
        detail2: [],
      },
      itemList: [],
      items: [],
      detail1Headers: [],
      detailHeaders: [],
      dialog: false,
      alloDialog: false,
      selected: false,
      loading: false,
    }
  },
  methods: {
    async save(goBack) {
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/allocation/add`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async getIncome() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/company/income/one`,
        {
          id: this.$route.params.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = data
    },
  },
  async mounted() {
    await this.getIncome()
  },
}
</script>

<style></style>
