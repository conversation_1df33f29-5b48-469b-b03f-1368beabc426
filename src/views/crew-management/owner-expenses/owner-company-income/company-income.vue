<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="canAdd">
        <v-card-title>
          新增外部船员公司费用账单
          <v-spacer></v-spacer>
          <v-icon left @click="close">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-ship-select v-model="newInfo.company"></v-ship-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-year-month-picker
                outlined
                dense
                v-model="newInfo.yearAndMonth"
                :clearable="false"
              ></v-year-month-picker>
            </v-col>
          </v-row>
          <v-col cols="12">
            <v-btn
              outlined
              tile
              color="success"
              class="mx-1"
              @click="save"
              v-permission="['船员公司收入:创建']"
              block
            >
              <v-icon left>mdi-plus-circle</v-icon>
              创建
            </v-btn>
          </v-col>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :single-select="false"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :search-remain="searchRemain"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-year-month-picker
            outlined
            dense
            :use-current="false"
            v-model="searchRemain.yearAndMonth"
          ></v-year-month-picker>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color=""
          class="mx-1"
          @click="init"
          v-permission="['船员公司收入:初始化']"
        >
          <v-icon left>mdi-card-text-outline</v-icon>
          初始化
        </v-btn>
        <!--        <v-btn-->
        <!--          outlined-->
        <!--          tile-->
        <!--          color=""-->
        <!--          class="mx-1"-->
        <!--          :loading="loading3"-->
        <!--          @click="withDraw"-->
        <!--          v-permission="['船员公司收入:取回']"-->
        <!--        >-->
        <!--          <v-icon left>mdi-card-text-outline</v-icon>-->
        <!--          取回-->
        <!--        </v-btn>-->
        <!--        <v-btn-->
        <!--          :disabled="!selected"-->
        <!--          outlined-->
        <!--          tile-->
        <!--          color="success"-->
        <!--          class="mx-1"-->
        <!--          @click="complete"-->
        <!--          :loading="loading1"-->
        <!--          v-permission="['船员公司收入:分摊完成']"-->
        <!--        >-->
        <!--          <v-icon left>mdi-account-check</v-icon>-->
        <!--          分摊完成-->
        <!--        </v-btn>-->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          :loading="loading2"
          v-permission="['船员公司收入:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.approveSubmitFlag`]="{ item }">
        <v-chip v-if="!item.approveSubmitFlag" small dark>未发起审批</v-chip>
        <v-chip v-else small color="success" dark>已发起审批</v-chip>
      </template>
      <template v-slot:[`item.sumFlag`]="{ item }">
        <v-chip v-if="!item.sumFlag" small dark>未完成</v-chip>
        <v-chip v-else small color="success" dark>已完成</v-chip>
      </template>
    </v-table-searchable>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="dialog"
    >
      <v-card>
        <v-card-title>
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="3">
                  <v-year-month-picker
                    v-model="yearMonth"
                    outlined
                    dense
                    :clearable="false"
                  ></v-year-month-picker>
                </v-col>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="companys"
                    :items="companyOption"
                    label="公司选择"
                    multiple
                    outlined
                    dense
                    clearable
                  ></v-select>
                </v-col>
                <v-col cols="12">
                  <v-btn
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                    :loading="loading"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    初始化
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
export default {
  name: 'company-income',
  created() {
    this.tableName = '船员公司收入'
    this.reqUrl = '/business/crew/company/income/page'
    this.headers = [
      { text: '公司名称', value: 'company' },
      { text: '账单所属年月', value: 'yearAndMonth' },
      { text: '总额', value: 'sum' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'company-income-detail' }
  },

  data() {
    return {
      selected: [],
      companyOption: [],
      searchRemain: {
        yearAndMonth: '',
      },
      yearMonth: '',
      companys: [],
      newInfo: {},
      canAdd: false,
      dialog: false,
      loading: false,
      loading1: false,
      loading2: false,
      loading3: false,
    }
  },

  methods: {
    close() {
      this.canAdd = false
      this.newInfo = {}
    },
    async init() {
      await this.getCreFirst()
      this.dialog = true
    },
    async save() {
      this.loading = true
      if (!this.$refs.form.validate()) {
        return
      }
      const salaryMonthMakeInitParam = {
        companys: this.companys,
        yearAndMonth: this.yearMonth,
      }
      const { errorRaw } = await this.postAsync(
        `/business/crew/company/income/init`,
        salaryMonthMakeInitParam,
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.loading = false
      this.closeForm()
      await this.$refs.table.loadTableData()
    },
    closeForm() {
      this.dialog = false
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.companyOption = data
    },
    async del() {
      if (this.selected.length === 0) {
        this.$dialog.message.error('请选择记录')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.loading2 = true
      const ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/company/income/deleteBatch`,
        ids,
      )
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
