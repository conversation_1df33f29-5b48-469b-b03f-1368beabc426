<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船东费用月结审批:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template
        v-slot:topcontent
        v-if="auditParams && auditParams.processInstanceId"
      >
        <v-card-text class="mt-2 pb-0">
          <v-audit ref="audit" :auditParams="auditParams"></v-audit>
        </v-card-text>
      </template>
      <template v-for="(name, index) in subtitles" v-slot:[name]>
        <v-card-text :key="name">
          <v-row>
            <v-col cols="12" md="2">
              <v-ship-select
                v-model="detailInfo.detailList[index].shipCode"
              ></v-ship-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-year-month-picker
                :use-current="false"
                outlined
                dense
                v-model="detailInfo.detailList[index].yearAndMonth"
                :clearable="false"
              ></v-year-month-picker>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                outlined
                dense
                label="开始时间"
                v-model="detailInfo.detailList[index].createTime"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                outlined
                dense
                label="结束时间"
                v-model="detailInfo.detailList[index].updateTime"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否发送OA"
                outlined
                dense
                v-model="detailInfo.detailList[index].oaSendFlag"
                :items="[
                  { text: '未发送OA', value: false },
                  { text: '已发送OA', value: true },
                ]"
                readonly
              ></v-select>
            </v-col>
          </v-row>
          <v-col>其他费用</v-col>
          <v-table-list
            :headers="detail1Headers"
            :items="detailInfo.detailList[index].detail1"
            item-key="itemName"
            use-page
          ></v-table-list>
          <v-col>管理费用</v-col>
          <v-table-list
            :headers="detail2Headers"
            :items="detailInfo.detailList[index].detail2"
            use-page
          ></v-table-list>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'owner-expenses-approve-detail',
  created() {
    this.backRouteName = 'owner-expenses-approve-list'
    this.detail1Headers = [
      { text: '费用', value: 'itemName' },
      { text: '金额', value: 'itemMoney' },
    ]
    this.detail2Headers = [
      { text: '序号', value: 'index' },
      { text: '职务', value: 'position' },
      { text: '天数', value: 'days' },
      { text: '工资', value: 'salary' },
      { text: '伙食费', value: 'mealsMoney' },
      { text: '社保', value: 'insurance' },
      { text: '集结费', value: 'gathering' },
      { text: '管理费', value: 'manageCost' },
      { text: '总额', value: 'rentTotal' },
    ]
  },
  data() {
    return {
      title: '待审批信息',
      detailInfo: {
        detailList: [],
      },
      auditParams: false,
      subtitles: [],
    }
  },

  methods: {
    async save(goBack) {
      if (this.detailInfo.status === 3) return
      await this.$refs.audit.submit()

      goBack()
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/ownerMonthCharge/approval/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.auditParams = data.auditParams
      for (let i = 0; i < data.detailList.length; i++) {
        this.subtitles.push(`第${i + 1}条待审批信息`)
      }
      this.title = `${data.yearAndMonth}-待审批信息详情`
      this.detailInfo = data
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
