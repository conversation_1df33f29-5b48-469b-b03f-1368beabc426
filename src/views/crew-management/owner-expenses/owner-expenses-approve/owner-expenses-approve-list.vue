<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      use-status
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          tile
          color="info"
          outlined
          dense
          class="mx-1"
          :loading="loading1"
          @click="SendOA"
          v-permission="['船东费用月结审批:发送OA']"
        >
          <v-icon left>mdi-email-arrow-right-outline</v-icon>
          发送OA
        </v-btn>
      </template>
      <template v-slot:[`item.oaSent`]="{ item }">
        <v-chip v-if="item.oaSent" small dark color="success">已发送</v-chip>
        <v-chip v-else small dark color="">未发送</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'owner-expenses-approve-list',
  created() {
    this.tableName = '船东费用月结审批'
    this.reqUrl = '/ownerMonthCharge/approval/page'
    this.headers = [
      { text: '经办人', value: 'handler' },
      { text: '生成时间', value: 'createTime' },
      { text: '审批状态', value: 'status' },
      { text: '是否发送OA', value: 'oaSent' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '起始时间',
      interval: true,
    }
    this.pushParams = {
      name: 'owner-expenses-approve-detail',
    }
  },

  data() {
    return {
      selected: false,
      loading1: false,
    }
  },

  methods: {
    async SendOA() {
      if (this.selected.oaSent) {
        this.$dialog.message.error('该记录已发送OA，请问重复发送')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('是否将寻找记录发送OA？'))) return
      this.loading1 = true
      const { errorRaw } = await this.getAsync(
        `/ownerMonthCharge/approval/sendOA`,
        { id: this.selected.id },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('发送成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
