<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船东费用月结账单:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #船东费用月结账单基本信息>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-year-month-picker
                outlined
                dense
                v-model="detailInfo.yearAndMonth"
              ></v-year-month-picker>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否发起审批"
                outlined
                dense
                readonly
                :items="[
                  { text: '已发起审批', value: true },
                  { text: '未发起审批', value: false },
                ]"
                v-model="detailInfo.approveSubmitFlag"
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否发送OA"
                outlined
                dense
                readonly
                :items="[
                  { text: '已发送OA', value: true },
                  { text: '未发送OA', value: false },
                ]"
                v-model="detailInfo.oaSendFlag"
              ></v-select>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <template #总额表>
        <v-table-list :headers="detail3Headers" :items="items3">
          <template
            v-for="h in detail3Headers"
            v-slot:[`item.${h.value}`]="{ item }"
          >
            <v-text-field
              :key="h.value"
              dense
              single-line
              readonly
              v-model="item[h.value]"
              type="number"
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template #按船汇总表>
        <v-table-list :headers="detail1Headers" :items="items">
          <template
            v-for="h in detail1Headers"
            v-slot:[`item.${h.value}`]="{ item }"
          >
            <v-text-field
              :key="h.value"
              dense
              readonly
              single-line
              v-model="item[h.value]"
              type="number"
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template #管理费用按钮>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="create"
          v-permission="['管理费用:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['管理费用:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #按岗位明细表>
        <v-table-list
          item-key="index"
          :headers="detail2Headers"
          :items="detailInfo.detail2"
          v-model="selected"
        >
          <template v-slot:[`item.insurance`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.insurance"
              type="number"
            ></v-text-field>
          </template>
          <template v-slot:[`item.gathering`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.gathering"
              type="number"
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template #实习见习负数冲销表>
        <v-table-list
          item-key="index"
          :headers="detail4Headers"
          :items="detailInfo.detail4"
          v-model="selected"
        >
          <template v-slot:[`item.insurance`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.insurance"
              type="number"
            ></v-text-field>
          </template>
          <template v-slot:[`item.gathering`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.gathering"
              type="number"
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template #外包船员数据明细表>
        <v-table-list
          item-key="index"
          :headers="detail5Headers"
          :items="detailInfo.detail5"
          v-model="selected"
        >
          <template v-slot:[`item.insurance`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.insurance"
              type="number"
            ></v-text-field>
          </template>
          <template v-slot:[`item.gathering`]="{ item }">
            <v-text-field
              single-line
              dense
              v-model="item.gathering"
              type="number"
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <add-manage-fee-dialog
      v-model="dialog"
      @success="success"
    ></add-manage-fee-dialog>
  </v-container>
</template>
<script>
import addManageFeeDialog from './add-manage-fee-dialog.vue'
export default {
  components: { addManageFeeDialog },
  name: 'owner-expenses-invoice-detail',
  created() {
    this.backRouteName = 'owner-expenses-invoice-list'
    this.subtitles = [
      '船东费用月结账单基本信息',
      '总额表',
      '按船汇总表',
      '按岗位明细表',
      '实习见习负数冲销表',
      '外包船员数据明细表',
    ]
    this.detail2Headers = [
      { text: '序号', value: 'index' },
      { text: '职务', value: 'position' },
      { text: '当月标准配员天数', value: 'days' },
      { text: '实际船员在船', value: 'actualDays' },
      { text: '船东工资', value: 'salary' },
      { text: '实际工资', value: 'actualSalary' },
      { text: '补发', value: 'adjustAdd' },
      { text: '补扣', value: 'adjustSub' },
      { text: '工资差额', value: 'differenceSalary' },
      { text: '伙食费', value: 'mealsMoney' },
      { text: '管理费', value: 'manageCost' },
    ]
    this.detail4Headers = [
      { text: '序号', value: 'index' },
      { text: '职务', value: 'position' },
      { text: '当月标准配员天数', value: 'days' },
      { text: '实际船员在船', value: 'actualDays' },
      { text: '船东工资', value: 'salary' },
      { text: '实际工资', value: 'actualSalary' },
      { text: '补发', value: 'adjustAdd' },
      { text: '补扣', value: 'adjustSub' },
      { text: '工资差额', value: 'differenceSalary' },
      { text: '伙食费', value: 'mealsMoney' },
      { text: '管理费', value: 'manageCost' },
    ]

    this.detail5Headers = [
      { text: '序号', value: 'index' },
      { text: '职务', value: 'position' },
      { text: '当月标准配员天数', value: 'days' },
      { text: '实际船员在船', value: 'actualDays' },
      { text: '船东工资', value: 'salary' },
      { text: '实际工资', value: 'actualSalary' },
      { text: '补发', value: 'adjustAdd' },
      { text: '补扣', value: 'adjustSub' },
      { text: '工资差额', value: 'differenceSalary' },
      { text: '伙食费', value: 'mealsMoney' },
      { text: '管理费', value: 'manageCost' },
    ]
  },
  data() {
    return {
      title: '船东费用月结账单详情',
      detailInfo: {
        detail2: [],
        detail4: [],
        detail5: [],
      },
      items: [],
      items3: [],
      detail1Headers: [],
      detail3Headers: [],
      dialog: false,
      selected: false,
    }
  },
  watch: {
    items: {
      handler(val) {
        for (let name in val[0]) {
          const hName = name.slice(9)
          this.detailInfo.detail1.forEach((ele) => {
            if (ele.itemName === hName) {
              ele.itemMoney = val[0][name]
            }
          })
        }
      },
      deep: true,
      immediate: true,
    },
    items3: {
      handler(val) {
        for (let name in val[0]) {
          const hName = name.slice(9)
          this.detailInfo.detail3.forEach((ele) => {
            if (ele.itemName === hName) {
              ele.itemMoney = val[0][name]
            }
          })
        }
      },
      deep: true,
      immediate: true,
    },
    'detailInfo.detail2': {
      handler(val) {
        for (let i = 0; i < val.length; i++) {
          val[i].index = i + 1
          // 先计算出总和
          const total =
            Number(val[i].salary) +
            Number(val[i].mealsMoney) +
            Number(val[i].insurance) +
            Number(val[i].gathering) +
            Number(val[i].manageCost)

          // 使用 toFixed(3) 保留三位小数，再转成数值类型
          val[i].rentTotal = Number(total.toFixed(3))

          // 如果想直接保存字符串形式 (如 "123.456")，可直接：
          // val[i].rentTotal = total.toFixed(3)
        }
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async save(goBack) {
      const { errorRaw } = await this.postAsync(
        `/ownerMonthCharge/fee/add`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/ownerMonthCharge/fee/detail`,
        {
          id: this.$route.params.id,
        },
      )
      if (errorRaw) {
        return
      }
      this.detail1Headers = data.detail1.map((ele) => {
        return {
          text: ele.itemName,
          value: `itemMoney${ele.itemName}`,
        }
      })
      this.detail3Headers = data.detail3.map((ele) => {
        return {
          text: ele.itemName,
          value: `itemMoney${ele.itemName}`,
        }
      })
      if (this.detail1Headers.length) {
        const item = {}
        for (let i = 0; i < data.detail1.length; i++) {
          item[`itemMoney${data.detail1[i].itemName}`] =
            data.detail1[i].itemMoney
        }
        this.items.push(item)
      }
      if (this.detail3Headers.length) {
        const item1 = {}
        for (let i = 0; i < data.detail3.length; i++) {
          item1[`itemMoney${data.detail3[i].itemName}`] =
            data.detail3[i].itemMoney
        }
        this.items3.push(item1)
      }
      this.title = `${data.shipName}-${data.yearAndMonth}-船东费用月结账单详情`
      this.detailInfo = data
    },
    delAudit() {
      const len = this.detailInfo.detail2.length
      this.detailInfo.detail2 = this.detailInfo.detail2.filter(
        (ele) => ele.index !== this.selected.index,
      )
      if (len === this.detailInfo.detail2.length) {
        this.$dialog.message.error('删除失败')
      } else {
        this.$dialog.message.success('删除成功')
      }
    },
    create() {
      this.dialog = true
    },
    success(val) {
      if (
        this.detailInfo.detail2.find((ele) => ele.position === val.position)
      ) {
        this.$dialog.message.error('该岗位已存在，请勿重复添加')
        return
      }
      this.detailInfo.detail2.push(val)
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
