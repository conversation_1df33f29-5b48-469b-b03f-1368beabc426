<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="canAdd">
        <v-card-title>
          新增外部船员公司费用账单
          <v-spacer></v-spacer>
          <v-icon left @click="close">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-ship-select v-model="newInfo.shipCode"></v-ship-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-year-month-picker
                outlined
                dense
                v-model="newInfo.yearAndMonth"
                :clearable="false"
              ></v-year-month-picker>
            </v-col>
          </v-row>
          <v-col cols="12">
            <v-btn
              outlined
              tile
              color="success"
              class="mx-1"
              :loading="loading2"
              @click="save"
              v-permission="['船员公司绩效考核:创建']"
              block
            >
              <v-icon left>mdi-plus-circle</v-icon>
              创建
            </v-btn>
          </v-col>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :single-select="false"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :search-remain="searchRemain"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-year-month-picker
            outlined
            dense
            :use-current="false"
            v-model="searchRemain.yearAndMonth"
          ></v-year-month-picker>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船舶所属船员公司"
            outlined
            dense
            required
            :items="companys"
            item-text="dictLabel"
            item-value="dictValue"
            v-model="searchRemain.creComCode"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          @click="exportExcel"
          v-permission="['船员公司绩效考核:初始化内部船员公司费用账单']"
        >
          <v-icon left>mdi-download</v-icon>
          导出费用账单表
        </v-btn>
        <v-btn
          outlined
          tile
          color=""
          class="mx-1"
          @click="init"
          v-permission="['船员公司绩效考核:初始化内部船员公司费用账单']"
        >
          <v-icon left>mdi-card-text-outline</v-icon>
          初始化内部船员公司费用账单
        </v-btn>
        <v-btn
          tile
          outlined
          color="info"
          class="mx-1"
          @click="sendApprove"
          :loading="loading1"
          v-permission="['船员公司绩效考核:提交审批']"
        >
          <v-icon left>mdi-arrow-up-thick</v-icon>
          提交审批
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="canAdd = true"
          v-permission="['船员公司绩效考核:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          :loading="loading3"
          @click="del"
          v-permission="['船员公司绩效考核:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.approveSubmitFlag`]="{ item }">
        <v-chip v-if="!item.approveSubmitFlag" small dark>未发起审批</v-chip>
        <v-chip v-else small color="success" dark>已发起审批</v-chip>
      </template>
      <template v-slot:[`item.oaSendFlag`]="{ item }">
        <v-chip v-if="!item.oaSendFlag" small dark>未发起OA</v-chip>
        <v-chip v-else small color="success" dark>已发起OA</v-chip>
      </template>
    </v-table-searchable>
    <owner-expenses-invoice-dialog
      v-model="dialog"
    ></owner-expenses-invoice-dialog>
  </v-container>
</template>
<script>
import ownerExpensesInvoiceDialog from './owner-expenses-invoice-dialog.vue'
export default {
  components: { ownerExpensesInvoiceDialog },
  name: 'owner-expenses-invoice-list',
  created() {
    this.tableName = '船员公司绩效考核'
    this.reqUrl = '/ownerMonthCharge/fee/page'
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '账单所属年月', value: 'yearAndMonth' },
      { text: '是否发起审批', value: 'approveSubmitFlag' },
      { text: '是否发送OA', value: 'oaSendFlag' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'owner-expenses-invoice-detail' }
  },

  data() {
    return {
      selected: [],
      searchRemain: {
        yearAndMonth: '',
      },
      companys: [],
      newInfo: {},
      canAdd: false,
      dialog: false,
      loading1: false,
      loading2: false,
      loading3: false,
    }
  },

  methods: {
    async exportExcel() {
      if (!this.searchRemain.yearAndMonth) {
        this.$dialog.message.error('请选择导出年月')
        return
      }
      if (!this.searchRemain.creComCode) {
        this.$dialog.message.error('请选择导出船舶所属船员公司')
        return
      }
      const fileName =
        this.searchRemain.yearAndMonth +
        '月份' +
        this.searchRemain.creComCode +
        '费用账单表'
      const { errorRaw } = await this.getBlobDownload(
        `/ownerMonthCharge/fee/export`,
        {
          yearAndMonth: this.searchRemain.yearAndMonth,
          creCompany: this.searchRemain.creComCode,
        },
        `${fileName}导出.xlsx`,
      )
      if (errorRaw) {
        return
      }
    },
    close() {
      this.canAdd = false
      this.newInfo = {}
    },
    init() {
      this.dialog = true
    },
    async save() {
      this.loading2 = true
      const { errorRaw } = await this.postAsync(
        `/ownerMonthCharge/fee/add`,
        this.newInfo,
      )
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('创建成功')
      this.newInfo = {}
      this.canAdd = false
      await this.$refs.table.loadTableData()
    },
    async sendApprove() {
      if (!(await this.$dialog.msgbox.confirm('确定将选中记录提交审批？')))
        return
      this.loading1 = true
      const ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/ownerMonthCharge/fee/submit`,
        {
          ids: ids,
        },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('提交成功')
    },
    async del() {
      if (this.selected.some((ele) => ele.approveSubmitFlag)) {
        this.$dialog.message.error('已有记录发送审批，无法删除')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.loading3 = true
      const ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/ownerMonthCharge/fee/deleteBatch`,
        ids,
      )
      this.loading3 = false
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/CreCompanyDic/list`,
      )
      if (errorRaw) {
        return
      }
      this.companys = data
    },
  },

  async mounted() {
    await this.getCreFirst()
  },
}
</script>

<style></style>
