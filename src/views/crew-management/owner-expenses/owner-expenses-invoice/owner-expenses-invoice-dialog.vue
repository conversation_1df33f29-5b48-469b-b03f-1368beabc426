<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-year-month-picker
                  v-model="yearMonth"
                  outlined
                  dense
                  :clearable="false"
                ></v-year-month-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="船舶所属船员公司"
                  outlined
                  dense
                  required
                  :items="companys"
                  item-text="dictLabel"
                  item-value="dictValue"
                  v-model="creComCode"
                  clearable
                ></v-select>
              </v-col>
              <v-col cols="12" md="4">
                <v-autocomplete
                  outlined
                  dense
                  label="船舶选择"
                  item-text="dictLabel"
                  item-value="dictValue"
                  :items="items"
                  v-model="chips"
                  multiple
                  select-all
                  clearable
                >
                  <template v-slot:selection="{ item, index }">
                    <v-chip v-if="index === 0">
                      <span>{{ item.dictLabel }}</span>
                    </v-chip>
                    <span v-if="index === 1" class="grey--text text-caption">
                      (+{{ chips.length - 1 }} others)
                    </span>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="12" md="2">
                <v-btn outlined dense @click="selectAll">选中所有船舶</v-btn>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  :loading="loading"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'owner-expenses-invoice-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      companys: [],
      componentKey: 0,
      chips: [],
      creComCode: '',
      items: [],
      yearMonth: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      loading: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
    yearMonth: {
      async handler() {
        await this.getShipList()
      },
    },
    chips(val) {
      console.log('chips', val)
    },
    creComCode: {
      async handler(val) {
        await this.dispatchingCompany(val)
      },
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    async dispatchingCompany(val) {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/ships`,
        {
          dispatchCompany: val,
        },
      )
      if (errorRaw) {
        return
      }
      this.items = data.map((ele) => {
        return {
          dictLabel: ele.chShipName,
          dictValue: ele.shipCode,
        }
      })
    },
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      this.loading = true
      if (!this.$refs.form.validate()) {
        return
      }
      const salaryMonthMakeInitParam = {
        shipCodes: this.chips,
        yearAndMonth: this.yearMonth,
      }
      const { errorRaw } = await this.postAsync(
        `/ownerMonthCharge/fee/initInner`,
        salaryMonthMakeInitParam,
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.$emit('change', false)
      this.$emit('success')
      this.loading = false
    },
    async getShipList() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/salary/salaryMake/todoBillShip/list`,
        { yearMonth: this.yearMonth },
      )
      if (errorRaw) {
        return
      }
      this.items = data.map((ele) => {
        return {
          text: ele.chShipName,
          value: ele.shipCode,
        }
      })
    },
    selectAll() {
      this.chips = []
      for (let i = 0; i < this.items.length; i++) {
        this.chips[i] = this.items[i].value
      }
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/CreCompanyDic/list`,
      )
      if (errorRaw) {
        return
      }
      this.companys = data
    },
  },

  async mounted() {
    await this.getCreFirst()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
