<template>
  <v-autocomplete
    ref="select"
    dense
    outlined
    :clearable="clearable"
    v-model="val"
    :label="label"
    :items="items"
    :loading="loading"
    :rules="rules"
    :disabled="disabled"
    :readonly="readonly"
    item-text="dictLabel"
    item-value="dictValue"
    @change="change"
  ></v-autocomplete>
</template>
<script>
import { cacheGetDefault } from '@/util/cache'
export default {
  name: 'v-position-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  props: {
    value: String,
    rules: Array,
    clearable: {
      type: Boolean,
      default: false,
    },
    disabled: [<PERSON>olean, String],
    readonly: [<PERSON>ole<PERSON>, String],
    usePostCode: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '岗位',
    },
  },
  data() {
    return {
      loading: false,
      items: [],
      val: null,
    }
  },
  watch: {
    value(val) {
      this.val = val
    },
  },
  methods: {
    change(v) {
      this.$emit('update', v)
    },
    validate(force, value) {
      return this.$refs.select.validate(force, value)
    },
    reset() {
      this.$refs.select.reset()
    },
    resetValidation() {
      this.$refs.select.resetValidation()
    },
    async getPosList() {
      let that = this
      const data = await cacheGetDefault('pos-list', async () => {
        const { data, errorRaw } = await that.getAsync(
          `/system/post/all`,
          {},
          false,
        )
        if (errorRaw) {
          that.$dialog.message.error('职务列表获取失败，请重试')
          return null
        }
        if (data.length === 0) {
          that.$dialog.message.error('职务列表为空，部分功能受损')
        }
        return data
      })
      if (this.usePostCode) {
        this.items = data.map((item) => {
          return {
            dictValue: item.postCode,
            dictLabel: item.postName,
          }
        })
      } else {
        this.items = data.map((item) => {
          return {
            dictValue: item.postName,
            dictLabel: item.postName,
          }
        })
      }
    },
  },
  created() {
    this.form && this.form.register(this)
  },
  async beforeMount() {
    this.loading = true
    await this.getPosList()
    this.loading = false
    this.val = this.value
    this.resetValidation()
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },

  mounted() {},
}
</script>

<style></style>
