<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">培训内容</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="itemHeader"
      :items="list"
      hide-default-footer
      disable-pagination
    >
      <template v-slot:[`item.compulsory`]="{ item }">
        <span v-if="item.compulsory === '1'">必修</span>
        <span v-else-if="item.compulsory === '0'">选修</span>
      </template>
      <template v-slot:[`item.learningStatus`]="{ item }">
        <v-chip v-if="item.learningStatus === '2'" small color="success">
          已完成
        </v-chip>
        <v-chip v-else-if="item.learningStatus === '1'" small color="info">
          未完成
        </v-chip>
        <v-chip v-else-if="item.learningStatus === '0'" small color="gray">
          未参加
        </v-chip>
      </template>
    </v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
export default {
  name: 'v-crew-train-item',
  created() {
    this.itemHeader = [
      { text: '内容名称', value: 'contentName' },
      { text: '类型', value: 'compulsory' },
      { text: '学习时长/成绩', value: 'timeOrScore' },
      { text: '完成状态', value: 'learningStatus' },
    ]
  },
  props: {
    list: Array,
  },
  data() {
    return {
      itemHeader: [],
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
