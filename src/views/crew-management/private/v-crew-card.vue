<template>
  <v-dialog
    attach="#mask"
    width="1600"
    hide-overlay
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-toolbar flat color="primary" dark>
        <v-toolbar-title>船员卡片信息</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-toolbar>
      <v-tabs vertical>
        <v-tab>船员基本信息</v-tab>
        <v-tab>船员证书信息</v-tab>
        <v-tab>船员培训记录</v-tab>
        <v-tab>船员调配记录</v-tab>
        <v-tab>职务变动信息</v-tab>
        <v-tab>船员履历信息</v-tab>
        <v-tab>船员工资标准</v-tab>
        <v-tab-item>
          <v-card>
            <v-card-title>船员基本信息</v-card-title>
            <v-card-text>
              <v-form ref="form">
                <v-container>
                  <v-row>
                    <v-crew-pic
                      :ImagePicture="
                        detailInfo.baseInfo.identificationPhotoAttachment
                      "
                      :upLoad="false"
                    ></v-crew-pic>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        dense
                        label="船员姓名"
                        readonly
                        outlined
                        v-model="detailInfo.baseInfo.chName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        outlined
                        readonly
                        dense
                        v-model="detailInfo.baseInfo.enName"
                        label="英文姓名"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="3">
                      <vs-date-picker
                        label="出生年月"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.birthDate"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="民族"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.nation"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="性别"
                        outlined
                        dense
                        readonly
                        :items="[
                          { text: '男', value: '1' },
                          { text: '女', value: '2' },
                        ]"
                        v-model="detailInfo.baseInfo.gender"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="3">
                      <v-text-field
                        label="身份证号"
                        dense
                        outlined
                        v-model="detailInfo.baseInfo.idCard"
                        readonly
                      ></v-text-field>
                    </v-col>

                    <v-col cols="12" md="2">
                      <v-text-field
                        label="国家"
                        v-model="detailInfo.baseInfo.country"
                        outlined
                        readonly
                        dense
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="籍贯"
                        outlined
                        readonly
                        dense
                        v-model="detailInfo.baseInfo.nativePlace"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="婚姻状况"
                        outlined
                        dense
                        readonly
                        :items="['未婚', '已婚', '离异']"
                        v-model="detailInfo.baseInfo.maritalStatus"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="政治面貌"
                        outlined
                        dense
                        readonly
                        :items="[
                          '共产党员',
                          '共青团员',
                          '群众',
                          '预备党员',
                          '民主党派成员',
                        ]"
                        v-model="detailInfo.baseInfo.politicsStatus"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="宗教信仰"
                        outlined
                        dense
                        v-model="detailInfo.baseInfo.religion"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="身高"
                        outlined
                        dense
                        suffix="cm"
                        readonly
                        v-model="detailInfo.baseInfo.height"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="体重"
                        outlined
                        dense
                        readonly
                        suffix="kg"
                        v-model="detailInfo.baseInfo.weight"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作服尺码"
                        outlined
                        dense
                        :items="[160, 165, 170, 175, 180, 185, 190, 195, 200]"
                        v-model="detailInfo.baseInfo.workClothesSize"
                        readonly
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作鞋尺码"
                        outlined
                        dense
                        :items="[39, 40, 41, 42, 43, 44, 45, 46]"
                        v-model="detailInfo.baseInfo.workShoeSize"
                        readonly
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        outlined
                        dense
                        label="血型"
                        :items="['A', 'AB', 'B', 'O']"
                        readonly
                        v-model="detailInfo.baseInfo.bloodType"
                      ></v-select>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="最高学历"
                        dense
                        outlined
                        v-model="detailInfo.baseInfo.highestDegree"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="毕业院校"
                        outlined
                        dense
                        v-model="detailInfo.baseInfo.graduateSchool"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="外语语种"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.foreignLanguages"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="外语水平"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.foreignLevel"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="联系电话"
                        outlined
                        dense
                        v-model="detailInfo.baseInfo.phone"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="邮箱"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.email"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="6">
                      <v-textarea
                        label="邮寄地址"
                        outlined
                        dense
                        v-model="detailInfo.baseInfo.mailingAddress"
                        readonly
                      ></v-textarea>
                    </v-col>
                    <v-col cols="12" md="6">
                      <v-textarea
                        label="家庭住址"
                        outlined
                        dense
                        v-model="detailInfo.baseInfo.homeAddress"
                        readonly
                      ></v-textarea>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="实际职务"
                        v-model="detailInfo.baseInfo.actualPosition"
                        readonly
                      ></v-ship-station>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="证书职务"
                        v-model="detailInfo.baseInfo.certificatePosition"
                      ></v-ship-station>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="工龄（岁）"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.workAge"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-textarea
                    outlined
                    dense
                    label="有无病史"
                    v-model="detailInfo.baseInfo.medicalHistory"
                  ></v-textarea>
                </v-container>
              </v-form>
            </v-card-text>
            <v-card-title>家庭成员</v-card-title>
            <v-card-text>
              <v-table-list
                ref="table"
                v-model="selected"
                :headers="headers"
                :items="detailInfo.baseInfo.familyMember"
                :show-select="false"
                item-key="name"
              ></v-table-list>
            </v-card-text>
            <v-card-title>船员属性</v-card-title>
            <v-card-text>
              <v-form ref="form">
                <v-container>
                  <v-row>
                    <v-col cols="12" md="6">
                      <v-text-field
                        label="调配公司"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.creProperty.creCompany"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员类型"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.creProperty.creFeature"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="签署公司"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.baseInfo.creProperty.creType"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-container>
              </v-form>
            </v-card-text>
          </v-card>
        </v-tab-item>
        <v-tab-item>
          <v-card>
            <v-card-title>船员证书信息</v-card-title>
            <v-card-text>
              <v-table-list
                :headers="headers1"
                :show-select="false"
                :items="detailInfo.certificateInfo.certificateOutputDTOList"
                item-key="code"
                use-page
              >
                <template v-slot:[`item.category`]="{ item }">
                  {{ getCategory(item.category) }}
                </template>
                <template v-slot:[`item.operta`]="{ item }">
                  <v-btn
                    outlined
                    tile
                    small
                    class="mx-1"
                    color="info"
                    @click="downloadPdf(item)"
                  >
                    下载附件
                  </v-btn>
                </template>
              </v-table-list>
            </v-card-text>
          </v-card>
        </v-tab-item>
        <v-tab-item>
          <v-card>
            <v-card-title>船员培训记录</v-card-title>
            <v-card-text>
              <v-table-searchable
                ref="table"
                :showTableName="false"
                :fuzzy-label="fuzzyLabel"
                :show-select="false"
                :headers="headers2"
                :req-url="reqUrl"
                :fix-header="false"
                show-expand
                :searchRemain="searchRemain"
              >
                <template v-slot:[`item.status`]="{ item }">
                  <v-chip v-if="item.status === '0'" small color="gray">
                    未参加
                  </v-chip>
                  <v-chip v-else-if="item.status === '1'" small color="info">
                    进行中
                  </v-chip>
                  <v-chip v-else-if="item.status === '2'" small color="success">
                    已完成
                  </v-chip>
                  <v-chip v-else small color="error">未完成</v-chip>
                </template>
                <template v-slot:expanded-item="{ headers, item }">
                  <td :colspan="headers.length">
                    <v-crew-train-item :list="item.trainContent" />
                  </td>
                </template>
              </v-table-searchable>
            </v-card-text>
          </v-card>
        </v-tab-item>
        <v-tab-item>
          <v-card>
            <v-card-title>船员调配记录</v-card-title>
            <v-card-text>
              <v-table-list
                :headers="headers3"
                :show-select="false"
                :items="detailInfo.deployInfo.deployHistory"
                use-page
              >
                <template v-slot:[`item.deployResult`]="{ item }">
                  {{ item.deployResult ? '成功' : '失败' }}
                </template>
              </v-table-list>
            </v-card-text>
          </v-card>
        </v-tab-item>
        <v-tab-item>
          <v-card>
            <v-card-title>职务变动信息</v-card-title>
            <v-card-text>
              <v-table-list
                :headers="headers4"
                :show-select="false"
                :items="detailInfo.jobChangeInfo.positionChangeHistory"
                use-page
              >
                <template v-slot:[`item.status`]="{ item }">
                  {{ item.status === 4 ? '已完成' : '驳回' }}
                </template>
              </v-table-list>
            </v-card-text>
          </v-card>
        </v-tab-item>
        <v-tab-item>
          <v-card>
            <v-card-title>船员履历信息</v-card-title>
            <v-card-text>
              <v-table-list
                :headers="headers5"
                :show-select="false"
                :items="detailInfo.cardWork.workHistory"
                use-page
              >
                <template v-slot:[`item.selfShipFlag`]="{ item }">
                  {{ item.selfShipFlag === 1 ? '本公司的船' : '其他公司的船' }}
                </template>
              </v-table-list>
            </v-card-text>
          </v-card>
        </v-tab-item>
        <v-tab-item>
          <v-card>
            <v-card-title>船员工资标准</v-card-title>
            <v-card-text>
              <v-table-list
                :headers="headers6"
                :show-select="false"
                :items="detailInfo.deploySalaryInfo.deploySalaryHistory"
                item-key="planCode"
                use-page
              ></v-table-list>
            </v-card-text>
          </v-card>
        </v-tab-item>
      </v-tabs>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <a v-if="true" :href="downPDF" ref="downPDFHref"></a>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import VTableList from '@/components/v-table-list.vue'
import vCrewPic from '../crew-information/private/v-crewPic.vue'
import VCrewTrainItem from '@/views/crew-management/private/v-crew-train-item.vue'

export default {
  components: {
    VCrewTrainItem,
    vCrewPic,
    VTableList,
  },

  name: 'v-crew-card',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/crew/baseInfo/cultivate/page'
    this.headers1 = [
      { text: '证书编号', value: 'code' },
      { text: '证书名称', value: 'certificateName' },
      { text: '证书职务', value: 'certificatePost' },
      { text: '证书类别', value: 'category' },
      { text: '海事局', value: 'msa' },
      { text: '船员', value: 'creName' },
      { text: '船舶种类描述', value: 'shipCategory' },
      { text: '国家地区', value: 'countryPart' },
      { text: '申办单位', value: 'applicant' },
      { text: '签发时间', value: 'signDate' },
      { text: '签发地点', value: 'signPlace' },
      { text: '到期时间', value: 'expireDate' },
      { text: '附件', value: 'attachmentRecords', sortable: false },
    ]
    this.headers2 = [
      { text: '培训项目名称', value: 'trainName' },
      { text: '培训项目分类', value: 'trainType' },
      { text: '状态', value: 'status' },
      { text: '完成时间', value: 'completedTime' },
    ]
    this.headers3 = [
      { text: '姓名', value: 'crewName' },
      { text: '船舶', value: 'shipName' },
      { text: '调配时间', value: 'deployDate' },
      { text: '调配主管', value: 'deployDirectorName' },
      { text: '调配结果', value: 'deployResult' },
      { text: '调配失败原因', value: 'deployFailureReason' },
      { text: '备注', value: 'remark' },
    ]
    this.headers4 = [
      { text: '船员姓名', value: 'crewName' },
      { text: '原本职务', value: 'originPost' },
      { text: '申请晋升职务', value: 'postName' },
      { text: '申请船舶', value: 'shipName' },
      { text: '职务晋升申请时间', value: 'applyTime' },
      { text: '职务晋升生效时间', value: 'changeTime' },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.headers5 = [
      { text: '船名', value: 'shipName' },
      { text: '船舶类型', value: 'shipType' },
      { text: '职务', value: 'officeJob' },
      { text: '开始时间', value: 'startDate' },
      { text: '上船地点', value: 'upBoardPlace' },
      { text: '结束时间', value: 'endDate' },
      { text: '下船地点', value: 'offBoardPlace' },
      { text: '结束原因', value: 'leaveReason' },
      { text: '在职天数', value: 'officeDay' },
      { text: '船舶归属', value: 'selfShipFlag' },
      { text: '载重吨（DWT）', value: 'dwt' },
      { text: '总吨（GT）', value: 'gt' },
    ]
    this.headers6 = [
      { text: '船员名称', value: 'creName' },
      { text: '船舶名称', value: 'shipName' },
      { text: '在船岗位', value: 'position' },
      { text: '岗位工资', value: 'actualSalary' },
      { text: '工资开始日期', value: 'actualSalaryBeginDate' },
      { text: '工资结束日期', value: 'actualSalaryEndDate' },
    ]
    this.fuzzyLabel = ''
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      searchRemain: {},
      formData: {},
      detailInfo: {
        baseInfo: { creProperty: {} },
        certificateInfo: {},
        cultivateInfo: {},
        deployInfo: {},
        jobChangeInfo: {},
        cardWork: {},
        deploySalaryInfo: {},
      },
      selected: false,
      headers: [
        { text: '姓名', value: 'name' },
        { text: '电话号码', value: 'phoneNo' },
        { text: '与本人关系', value: 'relation' },
      ],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.searchRemain.userId = this.initialData.userId
    },
    dialog(val) {
      if (val) {
        this.getDetailInfo()
        this.$refs.table.resetTableData()
        this.$refs.table.loadTableData()
      }
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    async downloadPdf(item) {
      if (!(await this.$dialog.msgbox.confirm('是否导出所选证书？'))) return
      let arr = []
      arr.push(item.certificateId)
      const { data } = await this.postAsync(
        '/business/crew/certificate/exportCrewCertificate',
        arr,
      )
      console.log(data)
      if (data) {
        data.forEach((item) => {
          this.dowloadPDFs(item)
        })
      }
    },
    async dowloadPDFs(item) {
      this.downPDF = `/api/system/file/download?fileName=${encodeURIComponent(
        item.fileName,
      )}&filePath=${item.filePath}`
      console.log(this.downPDF)

      const link = this.$refs.downPDFHref
      link.href = this.downPDF
      link.download = this.extractFilename(this.downPDF)
      link.style.display = 'none'
      document.body.appendChild(link)

      // 模拟点击<a>标签以触发下载
      link.click()
    },
    extractFilename(url) {
      return url.substring(url.lastIndexOf('/') + 1)
    },
    closeForm() {
      this.$emit('change', false)
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/ifoCard/ByUserId?userId=${this.formData.userId}`,
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = data
    },
    getCategory(item) {
      const map = new Map([
        [0, '海员证'],
        [1, '培训合格证书'],
        [2, '船员服务簿'],
        [3, '适任证书'],
        [4, '海事局健康证明'],
        [5, '护照'],
        [6, '香港适任证书'],
        [7, '巴拿马适任证书'],
        [8, '疫苗接种或预防措施国际证书'],
        [9, '国际旅行健康检查证明书'],
      ])
      return map.get(item)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
