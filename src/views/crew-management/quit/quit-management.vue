<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :search-date="searchDate"
      :push-params="pushParams"
      :single-select="false"
      :fix-header="false"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-select
            label="离职选项"
            outlined
            dense
            required
            v-model="searchRemain.quitOption"
            clearable
            :items="quitOption"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="当前状态"
            outlined
            dense
            :items="[
              { text: '未开始', value: 0 },
              { text: '进行中', value: 1 },
              { text: '已完成', value: 2 },
              { text: '驳回', value: 3 },
            ]"
            v-model="searchRemain.status"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="SendOA"
        >
          <v-icon left>mdi-upload</v-icon>
          发送OA
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-account-entry/account-quit-detail/new"
          v-permission="['船员离职管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          :loading="loading1"
          @click="delAudit"
          v-permission="['船员离职管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.quitOption`]="{ item }">
        {{ item.quitOption === 0 ? '主动离职' : '公司辞退' }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <!-- {{
          item.status === 0
            ? '未开始'
            : item.status === 1
            ? '进行中'
            : item.status === 2
            ? '已完成'
            : '驳回'
        }} -->
        <v-chip v-if="item.status === 0" color="" small dark>未开始</v-chip>
        <v-chip v-else-if="item.status === 1" color="info" small dark>
          进行中
        </v-chip>
        <v-chip v-else-if="item.status === 2" color="success" small dark>
          已完成
        </v-chip>
        <v-chip v-else color="error" small dark>驳回</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'quit-management',
  created() {
    this.tableName = '离职信息管理'
    this.reqUrl = '/business/crew/quit/page'
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '经办人', value: 'handlerName' },
      { text: '预计离职时间', value: 'expectedQuitTime' },
      { text: '实际离职时间', value: 'quitTime' },
      { text: '离职选项', value: 'quitOption' },
      { text: '公积金', value: 'accumulationBonus' },
      { text: '社保', value: 'socialBonus' },
      { text: '当前状态', value: 'status' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.quitOption = [
      { text: '主动离职', value: '0' },
      { text: '公司辞退', value: '1' },
    ]
    this.pushParams = {
      name: 'quit-detail',
    }
    this.searchDate = {
      interval: true,
      label: '培训起始时间',
    }
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: [],
      loading1: false,
      searchRemain: {},
    }
  },

  methods: {
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('是否删除选中记录？'))) return
      for (let ele of this.selected) {
        if (ele.status === 1 || ele.status === 2) {
          this.$dialog.message.error('只能删除被驳回和未开始的记录信息')
          return
        }
      }
      this.loading1 = true
      const selectedIds = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/quit/deleteCrewQuit`,
        selectedIds,
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
    },
    async SendOA() {
      if (!(await this.$dialog.msgbox.confirm('选中记录是否发送OA？'))) return
      const selectedIds = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(`/business/crew/quit/sendOA`, {
        ids: selectedIds,
      })
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('发送OA成功')
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
