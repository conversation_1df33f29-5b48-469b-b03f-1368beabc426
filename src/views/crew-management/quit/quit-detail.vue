<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船员离职管理:编辑']"
      :title="titleName"
      :tooltip="titleName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!auditParams || !auditParams.taskId || auditParams.isReject"
      @save="save"
      @submit="submit"
    >
      <template
        v-if="auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-audit ref="audit" :auditParams="auditParams"></v-audit>
        </v-card-text>
      </template>
      <template v-slot:离职详情信息>
        <v-form ref="form">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-dialog-select
                  v-model="detailInfo.creId"
                  item-value="userId"
                  item-text="name"
                  req-url="/business/crew/baseInfo/simple/page"
                  :rules="[rules.required]"
                  label="申请船员"
                  :headers="headers"
                  :searchRemain="creSearchRemain"
                  :init-selected="initSelected"
                  :readonly="newCard !== `new`"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        v-model="creSearchRemain.name"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="身份证号"
                        outlined
                        dense
                        v-model="creSearchRemain.idCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="船员岗位"
                        v-model="creSearchRemain.position"
                        clearable
                      ></v-ship-station>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-handler
                  :readonly="detailInfo.status !== 0 && newCard !== `new`"
                  v-model="detailInfo.handler"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="离职类别"
                  outlined
                  dense
                  :items="[
                    { text: '主动离职', value: 0 },
                    { text: '公司辞退', value: 1 },
                  ]"
                  :rules="[rules.required]"
                  v-model="detailInfo.quitOption"
                  :readonly="detailInfo.status !== 0 && newCard !== `new`"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="预计离职日期"
                  outlined
                  dense
                  :rules="[rules.required]"
                  v-model="detailInfo.expectedQuitTime"
                  :readonly="detailInfo.status !== 0 && newCard !== `new`"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="实际离职日期"
                  outlined
                  dense
                  v-model="detailInfo.quitTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2" v-if="newCard !== `new`">
                <v-text-field
                  label="公积金"
                  :readonly="detailInfo.status !== 0"
                  outlined
                  dense
                  type="number"
                  v-model="detailInfo.accumulationBonus"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2" v-if="newCard !== `new`">
                <v-text-field
                  label="社保"
                  outlined
                  :readonly="detailInfo.status !== 0"
                  dense
                  type="number"
                  v-model="detailInfo.socialBonus"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2" v-if="newCard !== `new`">
                <v-select
                  label="当前状态"
                  outlined
                  dense
                  :items="[
                    { text: '未开始', value: 0 },
                    { text: '进行中', value: 1 },
                    { text: '已完成', value: 2 },
                    { text: '已驳回', value: 3 },
                  ]"
                  readonly
                  v-model="detailInfo.status"
                ></v-select>
              </v-col>
            </v-row>
          </v-card-text>
          <v-attach-list
            :attachments="detailInfo.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import vHandler from '@/components/v-handler.vue'
export default {
  components: { vHandler },
  name: 'quit-detail',
  created() {
    this.backRouteName = 'quit-management'
    this.subtitles = ['离职详情信息']
    this.newCard = this.$route.params.id
    this.quitOption = [
      { text: '主动离职', value: '0' },
      { text: '公司辞退', value: '1' },
    ]
    this.headers = [
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.initSelected = {}
  },
  data() {
    return {
      creSearchRemain: {
        // status: 3,
      },
      IdParams: '',
      auditParams: false,
      detailInfo: {
        handler: this.$local.data.get('userInfo').id,
        handlerName: this.$local.data.get('userInfo').username,
        attachmentRecords: [],
      },
      titleName: '离职信息填写',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    async submit(goBack) {
      const { errorRaw, data } = await this.postAsync(
        `/business/crew/quit/modifyCrewQuit`,
        this.detailInfo,
      )
      if (!errorRaw) {
        this.$dialog.message.success(`保存成功!`)
        const { errorRaw } = await this.getAsync(
          `/business/crew/quit/process/start`,
          { id: data },
        )
        if (errorRaw) {
          this.$dialog.message.error(`审批失败，请重试`)
          return
        }
        goBack()
      } else {
        this.$dialog.message.error(`保存失败!，审批未启动，请重试`)
        return
      }
    },
    async save(goBack) {
      const { errorRaw, data } = await this.postAsync(
        `/business/crew/quit/modifyCrewQuit`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      console.log(data)
      this.detailInfo = {
        handlerName: this.$local.data.get('userInfo').username,
        attachmentRecords: [],
      }

      goBack()
    },
    async getDetailInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/quit/getById/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        this.titleName = this.detailInfo.creName + '---离职信息填写'
        this.auditParams = data.auditParams
        this.initSelected = { name: data.creName, userId: data.creId }
      }
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
