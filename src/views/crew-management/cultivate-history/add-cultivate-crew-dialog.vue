<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}培训船员
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-dialog-select
                  label="船员"
                  dense
                  outlined
                  table-name="船员选择"
                  :headers="creHeaders"
                  :reqUrl="`/business/crew/baseInfo/simple/page`"
                  itemText="name"
                  itemValue="userId"
                  :search-remain="searchRemain"
                  v-model="formData.crewId"
                  :initSelected="initSelected"
                  @select="select"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.name"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="身份证号"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.idCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="船员职务"
                        clearable
                        v-model="searchRemain.position"
                      ></v-ship-station>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="身份证"
                  outlined
                  dense
                  v-model="formData.creIdNumber"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-station v-model="formData.post"></v-ship-station>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="住宿费"
                  outlined
                  dense
                  type="number"
                  v-model="formData.accomodationCost"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="培训费"
                  outlined
                  dense
                  type="number"
                  v-model="formData.cultivationCost"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="交通费"
                  outlined
                  dense
                  type="number"
                  v-model="formData.transportationCost"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="其他费用"
                  outlined
                  dense
                  type="number"
                  v-model="formData.otherCost"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  label="核实人"
                  v-model="formData.checkPerson"
                  :initUser="initUser"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="核实结果"
                  outlined
                  dense
                  v-model="formData.checkResult"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="5">
                <v-text-field
                  label="备注"
                  outlined
                  dense
                  v-model="formData.remark"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'add-cultivate-crew-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.creHeaders = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '船员属性', value: 'crePropertyId' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    initSelected: {
      type: Object,
      default: () => {},
    },
    initUser: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchRemain: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      console.log(this.formData)
      this.$emit('change', false)
    },
    select(val) {
      this.formData.creIdNumber = val.idCard
      this.formData.post = val.position
      this.formData.creName = val.name
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = `/business/crew/cultivateHistory/modifyCultivateHistoryRecord`
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
