<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['培训记录:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #培训记录基本信息>
        <v-form ref="form">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="培训编号"
                  outlined
                  dense
                  v-model="detailInfo.planCode"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="培训科目"
                  outlined
                  dense
                  :items="[
                    '船员履约培训',
                    '船员休假期间培训',
                    '毕业生入职培训',
                    '职务晋升培训',
                    '船员任职前培训',
                    '上船前岗位培训',
                  ]"
                  v-model="detailInfo.subject"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="教员姓名"
                  outlined
                  dense
                  v-model="detailInfo.teacher"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="培训地点"
                  outlined
                  dense
                  v-model="detailInfo.place"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="开始日期"
                  outlined
                  dense
                  v-model="detailInfo.startTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="结束日期"
                  outlined
                  dense
                  v-model="detailInfo.endTime"
                ></vs-date-picker>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-textarea
                  label="培训内容"
                  outlined
                  dense
                  v-model="detailInfo.content"
                ></v-textarea>
              </v-col>
              <v-col>
                <v-textarea
                  label="备注"
                  outlined
                  dense
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
            </v-row>
            <v-col cols="12">
              <v-attach-list
                :attachments="detailInfo.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-card-text>
        </v-form>
      </template>
      <template #培训船员信息按钮 v-if="newCard !== `new`">
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="create"
          v-permission="['培训船员信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          :loading="loading"
          v-permission="['培训船员信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #培训船员信息>
        <v-table-list
          :headers="headers"
          :items="detailInfo.list"
          v-model="selected"
        >
          <template v-slot:[`item.operta`]="{ item }">
            <v-btn
              small
              color="info"
              dark
              outlined
              class="mx-1"
              @click="changeInfo(item)"
              v-permission="['培训船员信息:修改信息']"
            >
              修改信息
            </v-btn>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <add-cultivate-crew-dialog
      v-model="dialog"
      :initialData="initialData"
      :initSelected="initSelected"
      :initUser="initUser"
      @success="success"
    ></add-cultivate-crew-dialog>
  </v-container>
</template>
<script>
import addCultivateCrewDialog from './add-cultivate-crew-dialog.vue'
export default {
  components: { addCultivateCrewDialog },
  name: 'cultivate-history-detail',
  created() {
    this.backRouteName = 'cultivate-history-management'
    this.subtitles = ['培训记录基本信息', '培训船员信息']
    this.headers = [
      { text: '船员', value: 'creName' },
      { text: '身份证', value: 'creIdNumber' },
      { text: '职务', value: 'post' },
      { text: '住宿费', value: 'accomodationCost' },
      { text: '培训费', value: 'cultivationCost' },
      { text: '交通费', value: 'transportationCost' },
      { text: '其他费用', value: 'otherCost' },
      { text: '核实人', value: 'checkName' },
      { text: '核实结果', value: 'checkResult' },
      { text: '备注', value: 'remark' },
      { text: '操作', value: 'operta' },
    ]
    this.newCard = this.$route.params.id
  },
  data() {
    return {
      title: '新增培训记录',
      detailInfo: {},
      dialog: false,
      initialData: {},
      initSelected: {},
      initUser: {},
      selected: false,
      loading: false,
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    create() {
      this.initialData = {
        cultivationHistoryId: this.$route.params.id,
      }
      this.initUser = {
        id: this.$local.data.get('userInfo').id,
        nickName: this.$local.data.get('userInfo').nickName,
      }
      this.dialog = true
    },
    async save(goBack) {
      const { errorRaw } = await this.postAsync(
        `/business/crew/cultivateHistory/modifyCultivateHistory`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      goBack()
    },
    async getDetailInfo() {
      if (this.$route.params.id === `new`) return
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/cultivateHistory/getById/${this.$route.params.id}`,
      )
      if (errorRaw) {
        return
      }
      this.title = `${data.planCode}-${data.subject}`
      this.detailInfo = data
    },
    async success() {
      this.$dialog.message.success('保存成功')
      await this.getDetailInfo()
    },
    changeInfo(item) {
      this.initialData = item
      this.initSelected = {
        name: item.creName,
        userId: item.crewId,
      }
      this.initUser = {
        id: item.checkPerson,
        nickName: item.checkName,
      }
      this.dialog = true
    },
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        `/business/crew/cultivateHistory/deleteCultivateHistoryRecord`,
        [this.selected.id],
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.loading = false
      this.$dialog.message.success('删除成功')
      this.selected = false
      await this.getDetailInfo()
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
