<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          Excel---新增---培训计划表
          <v-spacer></v-spacer>
          <v-icon @click="close">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0">
              <v-row>
                <v-col cols="12" md="2">
                  <v-text-field
                    outlined
                    dense
                    label="计划编号（可选填）"
                    v-model="plan.planCode"
                  ></v-text-field>
                </v-col>
                <v-spacer></v-spacer>
                <v-col cols="12" md="2">
                  <v-import-btn
                    :import-url="importUrl"
                    @importSuccess="importSuccess"
                    :otherParams="plan"
                  ></v-import-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-select
            label="培训科目"
            outlined
            dense
            :items="[
              '船员履约培训',
              '船员休假期间培训',
              '毕业生入职培训',
              '职务晋升培训',
              '船员任职前培训',
              '上船前岗位培训',
            ]"
            v-model="searchRemain.subject"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="#26A69A"
          class="mx-1"
          @click="open"
          v-permission="['培训记录培训:导入excel模板']"
        >
          <v-icon left>mdi-microsoft-excel</v-icon>
          导入excel模板
        </v-btn>
        <v-btn
          outlined
          tile
          color="#004D40"
          class="mx-1"
          :href="`/api/business/crew/cultivatePlan/downloadTemplate`"
          target="_blank"
          v-permission="['培训记录培训:下载导入模板']"
        >
          <v-icon left>mdi-microsoft-excel</v-icon>
          下载导入模板
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-account-daily-training/cultivate-history-detail/new"
          v-permission="['培训记录:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['培训记录:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
const debounce = (fn, delay = 300) => {
  let timer = null
  return function () {
    let context = this
    let args = arguments
    clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}

export default {
  name: 'cultivate-history-management',
  created() {
    this.tableName = '培训记录'
    this.reqUrl = '/business/crew/cultivateHistory/page'
    this.headers = [
      { text: '培训编号', value: 'planCode' },
      { text: '培训科目', value: 'subject' },
      { text: '教员姓名', value: 'teacher' },
      { text: '培训地点', value: 'place' },
      { text: '培训内容', value: 'content' },
      { text: '开始时间', value: 'startTime' },
      { text: '结束时间', value: 'endTime' },
      { text: '备注', value: 'remark' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '培训起始日期',
      interval: true,
    }
    this.pushParams = {
      name: 'cultivate-history-detail',
    }
    this.importUrl = `/business/crew/cultivatePlan/excelImport`
  },

  data() {
    return {
      formShow: false,
      selected: false,
      plan: {},
      searchRemain: {},
    }
  },
  watch: {},

  methods: {
    debounceLoadTable: debounce(function (that) {
      that.searchRemain.teacher = that.teacher
    }, 500),
    close() {
      this.formShow = false
      this.$refs.table.disabled = false
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
      this.formShow = false
      this.$refs.table.disabled = false
      this.plan = {}
      await this.$nextTick()
    },
    async open() {
      this.formShow = !this.formShow
      this.plan = {}
    },
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        `/business/crew/cultivateHistory/deleteCultivateHistory`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.selected = false
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
