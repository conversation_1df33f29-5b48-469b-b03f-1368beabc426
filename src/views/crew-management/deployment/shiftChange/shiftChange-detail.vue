<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['交班下船计划管理:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
    >
      <template v-slot:titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
          v-permission="['交班下船计划管理:返回列表']"
          :loading="loading"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <v-btn
          v-if="detailInfo.status !== 2"
          width="60"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          :loading="loading"
          v-permission="['交班下船计划管理:保存']"
        >
          保存
        </v-btn>
      </template>
      <template v-slot:下船计划详情>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  :disabled="detailInfo.status === 2"
                  v-model="detailInfo.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  outlined
                  dense
                  clearable
                  v-model="detailInfo.captain"
                  :disabled="detailInfo.status === 2"
                  label="船长姓名"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="计划下船时间"
                  dense
                  outlined
                  :disabled="detailInfo.status === 2"
                  v-model="detailInfo.planTime"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="计划下船地点"
                  outlined
                  dense
                  :disabled="detailInfo.status === 2"
                  v-model="detailInfo.planPlace"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="是否仅下船"
                  outlined
                  dense
                  :items="[
                    { text: '单向下船', value: 1 },
                    { text: '非单向下船', value: 0 },
                  ]"
                  :rules="[rules.required]"
                  v-model="detailInfo.onlyOff"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2" v-if="detailInfo.handler">
                <v-text-field
                  label="提交人"
                  :disabled="detailInfo.status === 2"
                  outlined
                  dense
                  v-model="detailInfo.handler"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="计划状态"
                  readonly
                  :items="[
                    { text: '制定中', value: 1 },
                    { text: '已提交', value: 2 },
                  ]"
                  outlined
                  dense
                  v-model="detailInfo.status"
                ></v-select>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:下船计划船员名单>
        <v-card-text>
          <v-divider></v-divider>
          <v-data-table
            :headers="detailHeaders"
            :items="details"
            v-model="selected"
            item-key="cid"
            show-select
            dense
            class="use-divider"
            disable-pagination
            hide-default-footer
            @click:row="selectRow"
          ></v-data-table>
        </v-card-text>
      </template>
      <template v-slot:下船计划船员名单按钮 v-if="newCard !== `new`">
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="openAddDialog"
          :loading="sloading"
          v-if="detailInfo.status && detailInfo.status !== 2"
          v-permission="['下船计划船员名单:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          :loading="sloading"
          v-if="detailInfo.status && detailInfo.status !== 2"
          v-permission="['下船计划船员名单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <v-dialog-crews
      :initialData="crewsInfo"
      v-model="dialog"
      @update="updateVal"
    ></v-dialog-crews>
    <chang-crew-dialog
      v-model="dialogChange"
      reqUrl="/business/crew/baseInfo/page"
      :headers="testHeaders"
      :searchRemain="searchRemain"
      :initSelected="initSelected"
      item-key="userId"
      @success="success"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            outlined
            dense
            v-model="searchRemain.name"
            label="船员姓名"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            v-model="searchRemain.actualPosition"
            clearable
          ></v-ship-station>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            v-model="searchRemain.status"
            label="船员状态"
            :items="[
              { text: '公休', value: 2 },
              { text: '新船员', value: 1 },
            ]"
          ></v-select>
        </v-col>
      </template>
      <template v-slot:[`item.creProperty`]="{ item }">
        {{ item.creProperty && item.creProperty.creFeature }}
      </template>
    </chang-crew-dialog>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import vDialogCrews from './v-dialog-crews.vue'
import ChangCrewDialog from './chang-crew-dialog.vue'
export default {
  components: { vDialogCrews, ChangCrewDialog },
  name: 'shiftChange-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'shiftChange-management'
    this.subtitles = ['下船计划详情', '下船计划船员名单']
    this.detailHeaders = [
      { text: '船员姓名', value: 'creName' },
      { text: '船员职务', value: 'post' },
      { text: '换班职务', value: 'exchangePost' },
    ]
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '上船船名', value: 'shipName' },
      { text: '船员类型', value: 'creType' },
      { text: '上船时间', value: 'onBoardTime' },
      { text: '在船职务', value: 'post' },
    ]
    this.testHeaders = [
      { text: '船员', value: 'chName' },
      { text: '证书职务', value: 'certificatePosition' },
      { text: '实际职务', value: 'actualPosition' },
      { text: '船员属性', value: 'creProperty' },
    ]
    this.newCard = this.$route.params.id
  },
  data() {
    return {
      title: '新增下船计划',
      detailInfo: {
        details: [],
        captain: this.$local.data.get('userInfo').nickName,
        status: 1,
      },
      crewsInfo: {},
      selected: [],
      dialog: false,
      dialogChange: false,
      update: false,
      searchRemain: {},
      loading: false,
      sloading: false,
      initSelected: {},
      details: [],
      rules: {
        required: (v) => !!v || v === 0 || v === false || '必填项不能为空',
      },
    }
  },
  methods: {
    selectRow(_, { isSelected, item }) {
      console.log('isSelected', isSelected)
      console.log('item', item)
      if (!isSelected) {
        this.selected.push(item)
      } else {
        this.selected = this.selected.filter((ele) => ele.id !== item.id)
      }
    },
    async save(useBack = true) {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      this.loading = true
      this.detailInfo.userId = this.$local.data.get('userInfo').id
      console.log('detailInfo', this.detailInfo)
      const url =
        this.newCard === `new`
          ? `/business/crew/deploy/offBoardPlan/save`
          : `/business/crew/deploy/offBoardPlan/update`
      const { errorRaw } = await this.postAsync(url, {
        ...this.detailInfo,
        handler: this.$local.data.get('userInfo').nickName,
      })
      if (errorRaw) {
        this.loading = false
        return
      }
      if (this.newCard !== `new`) await this.saveOffBoatItems()
      this.$dialog.message.success('保存成功')
      if (useBack) {
        this.closeAndTo(this.backRouteName, {})
        this.loading = false
      }
    },
    async submit() {
      const useBack = false
      await this.save(useBack)
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/offBoardPlan/ensure`,
        { id: this.newCard.toString() },
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.$dialog.message.success('下船计划提交成功')
      this.closeAndTo(this.backRouteName, {})
      this.loading = false
    },
    async getDetailInfo() {
      if (this.$route.params.id !== `new`) {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/deploy/offBoardPlan/getById/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        this.details = data.details
        this.details.forEach((ele) => (ele.cid = ele.id))
        this.title = `${data.shipName}---下船计划详情`
      }
    },
    openAddDialog() {
      this.crewsInfo = {
        shipCode: this.detailInfo.shipCode,
        parentId: this.$route.params.id,
      }
      this.dialog = true
    },
    closeForm() {
      this.dialog = false
      this.sloading = false
    },
    async updateVal() {
      await this.getDetailInfo()
      this.sloading = false
    },
    async saveCrewsInfo() {
      if (this.isEdit) {
        const { errorRaw } = await this.postAsync(
          `/business/crew/deploy/offBoardPlan/detail/save`,
          { ...this.crewsInfo, parentId: this.$route.params.id },
        )
        if (errorRaw) {
          return
        }
        this.$dialog.message.success(`添加成功`)
        this.dialog = false
        await this.getDetailInfo()
      } else {
        const { errorRaw } = await this.postAsync(
          `/business/crew/deploy/offBoardPlan/detail/update`,
          { ...this.crewsInfo, parentId: this.$route.params.id },
        )
        if (errorRaw) {
          return
        }
        this.$dialog.message.success(`修改成功`)
        this.dialog = false
        await this.getDetailInfo()
      }
    },
    async delAudit() {
      this.sloading = true
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) {
        this.sloading = false
        return
      }
      const detailIdList = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/offBoardPlan/detail/deleteBatch`,
        { detailIdList: detailIdList, parentId: this.$route.params.id },
      )
      if (errorRaw) {
        this.sloading = false
        return
      }
      await this.getDetailInfo()
      this.$dialog.message.success(`删除成功`)
      this.selected = []
      this.sloading = false
    },
    async saveOffBoatItems() {
      this.detailInfo?.details.forEach(
        (ele) => (ele.parentId = this.$route.params.id),
      )
      const updateParam = {
        id: this.$route.params.id,
        details: [...this.detailInfo.details],
      }
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/offBoard/batchUpdate`,
        updateParam,
      )
      if (errorRaw) {
        return
      }
    },

    success(val) {
      let arr = [...this.details]
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].cid === val.cid) {
          arr[i].id = val.cid
          arr[i].acreUserId = val.userId
          arr[i].creName = val.chName
          arr[i].post = val.certificatePosition
          arr[i].exchangePost = val.exchangePost
          arr[i].parentId = val.parentId
          return
        }
      }
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
