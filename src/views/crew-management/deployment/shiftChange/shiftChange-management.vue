<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-date="searchDate"
      :searchRemain="searchRemain"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select
            outlined
            clearable
            dense
            v-model="searchRemain.shipCode"
          ></v-ship-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="船长姓名"
            outlined
            clearable
            dense
            v-model="searchRemain.captain"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="计划状态"
            :items="[
              { text: '制定中', value: 1 },
              { text: '已提交', value: 2 },
            ]"
            outlined
            dense
            v-model="searchRemain.status"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="blue"
          class="mx-1"
          @click="openDetailInfo"
          :disabled="!selected"
          v-permission="['交班下船计划管理:查看船员名单']"
        >
          <v-icon>mdi-eye</v-icon>
          查看船员名单
        </v-btn>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :disabled="!selected"
          @click="submitAudit"
          v-permission="['交班下船计划管理:发起对应上船计划']"
        >
          <v-icon>mid-upload</v-icon>
          发起对应上船计划
        </v-btn>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :disabled="!selected"
          @click="upLoad"
          v-permission="['交班下船计划管理:提交下船计划']"
        >
          <v-icon>mid-upload</v-icon>
          提交下船计划
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-deployment-management/crew-shift-change-detail/new"
          v-permission="['交班下船计划管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['交班下船计划管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small color="" dark v-if="item.status === 1">制定中</v-chip>
        <v-chip small color="success" dark v-else>已提交</v-chip>
      </template>
    </v-table-searchable>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="dialog"
    >
      <v-card>
        <v-card-title>
          下船船员名单
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-table-list
            :headers="detailHeaders"
            :items="selected.details"
            :show-select="false"
          ></v-table-list>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
export default {
  name: 'shiftChange-management',
  created() {
    this.tableName = '下船计划查询'
    this.reqUrl = '/business/crew/deploy/offBoardPlan/page'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '船长姓名', value: 'captain' },
      { text: '提交人', value: 'handler' },
      { text: '计划下船时间', value: 'planTime' },
      { text: '计划下船地点', value: 'planPlace' },
      { text: '下船计划制定状态', value: 'status' },
    ]
    this.detailHeaders = [
      { text: '船员姓名', value: 'creName' },
      { text: '船员职务', value: 'post' },
      { text: '换班职务', value: 'exchangePost' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'shiftChange-detail',
    }
    this.searchDate = {
      interval: true,
      label: '交班计划下船起末时间',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      dialog: false,
    }
  },

  methods: {
    openDetailInfo() {
      this.dialog = true
    },
    closeForm() {
      this.dialog = false
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/offBoardPlan/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.selected = false
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
    },
    async upLoad() {
      if (!(await this.$dialog.msgbox.confirm('是否提交选中下船记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/offBoardPlan/ensure`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交完成')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async submitAudit() {
      if (!(await this.$dialog.msgbox.confirm('是否发起对应的上船计划？')))
        return
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/upBoard/saveByOffBoard`,
        { offBoradId: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交完成')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
