<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新增下船船员
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-row>
            <v-col cols="12" md="4">
              <v-dialog-select
                label="船员选择"
                dense
                req-url="/business/crew/baseInfo/page"
                :headers="headers"
                v-model="formData.creName"
                :search-remain="searchRemain"
                item-value="userId"
                item-text="chName"
                @select="getSelect"
              >
                <template #searchflieds>
                  <v-col cols="12" md="2">
                    <v-text-field
                      outlined
                      dense
                      v-model="searchRemain.name"
                      label="船员姓名"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="2">
                    <v-ship-station
                      v-model="searchRemain.actualPosition"
                      clearable
                    ></v-ship-station>
                  </v-col>
                  <v-col cols="12" md="2">
                    <v-select
                      outlined
                      dense
                      v-model="searchRemain.status"
                      label="船员状态"
                      :items="[
                        { text: '公休', value: 2 },
                        { text: '新船员', value: 1 },
                      ]"
                    ></v-select>
                  </v-col>
                </template>
                <template v-slot:[`item.creProperty`]="{ item }">
                  {{ item.creProperty && item.creProperty.creFeature }}
                </template>
              </v-dialog-select>
            </v-col>
            <v-col cols="12" md="4">
              <v-ship-station
                v-model="formData.post"
                label="船员岗位"
                :rulse="[rules.required]"
                required
              ></v-ship-station>
            </v-col>
            <v-col cols="12" md="4">
              <v-ship-station
                v-model="formData.exchangePost"
                label="换班岗位"
                :rulse="[rules.required]"
                required
              ></v-ship-station>
            </v-col>
          </v-row>
          <v-col cols="12">
            <v-btn
              outlined
              tile
              color="success"
              class="mx-1"
              @click="save"
              block
            >
              <v-icon left>mdi-plus-circle</v-icon>
              新增
            </v-btn>
          </v-col>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-dialog-crews',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.detailHeaders = [
      { text: '船员姓名', value: 'creName' },
      { text: '船员职务', value: 'post' },
      { text: '换班职务', value: 'exchangePost' },
    ]
    this.headers = [
      { text: '船员', value: 'chName' },
      { text: '证书职务', value: 'certificatePosition' },
      { text: '实际职务', value: 'actualPosition' },
      { text: '船员属性', value: 'creProperty' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchRemain: {
        status: 2,
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
    'formData.exchangePost': {
      handler(val) {
        this.searchRemain = {
          shipCode: this.initialData.shipCode,
          post: val,
          status: 2,
        }
      },
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/offBoardPlan/detail/save`,
        this.formData,
      )
      if (errorRaw) {
        return
      }
      this.$emit('change', false)
      this.$emit('update')
    },
    getSelect(val) {
      this.formData.creName = val.chName
      this.formData.post = val.certificatePosition
      this.formData.creUserId = val.userId
      this.formData.parentId = this.initialData.parentId
      console.log(this.formData)
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
