<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-text>
        <v-card-title>{{ tableName }}</v-card-title>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :fixed-header="false"
          :headers="headers"
          :req-url="reqUrl"
          :search-remain="searchRemain"
          :item-key="itemValue"
          :filter-func="filterFunc"
          outlined
        >
          <template #searchflieds>
            <slot name="searchflieds">
              <!-- 剩余参数插槽 -->
            </slot>
          </template>
          <template v-for="h in headers" v-slot:[`item.${h.value}`]="{ item }">
            <slot :item="item" :name="`item.${h.value}`"></slot>
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'chang-crew-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    tableName: {
      type: String,
      default: '请选择',
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    filterFunc: Function,
    reqUrl: {
      type: String,
      // required: true,
    },
    headers: {
      type: Array,
      required: true,
    },
    itemValue: String,
    initSelected: Object,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      selected: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit ? '' : ''
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
    confirm() {
      this.$emit('change', false)
      this.$emit('success', {
        ...this.initSelected,
        ...this.selected,
      })
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
