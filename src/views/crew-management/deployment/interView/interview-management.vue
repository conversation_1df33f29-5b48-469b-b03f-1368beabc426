<template>
  <v-container fluid>
    <!-- :search-date="searchDate" -->
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      :push-params="pushParams"
      :single-select="false"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="1">
          <v-switch
            class="mt-1"
            dense
            v-model="searchRemain.isMe"
            label="待我审批"
            @change="isMeChange"
            color="success"
          ></v-switch>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="业务编码"
            outlined
            dense
            clearable
            v-model="searchRemain.bizCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="中文姓名"
            outlined
            dense
            v-model="searchRemain.name"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2" v-if="showSelect">
          <v-ship-select v-model="searchRemain.planShipCode"></v-ship-select>
        </v-col>
        <v-col cols="12" md="2" v-if="showSelect">
          <v-ship-station
            label="岗位职务"
            clearable
            v-model="searchRemain.post"
          ></v-ship-station>
        </v-col>
        <!-- <v-col cols="12" md="2">
          <v-select
            label="面试方式"
            v-model="searchRemain.onSite"
            clearable
            :items="[
              { text: '在线审批面试', value: 0 },
              { text: '现场面试', value: 1 },
            ]"
            dense
            outlined
          ></v-select>
        </v-col> -->
        <v-col cols="12" md="2" v-if="showSelect">
          <v-select
            label="面试结果"
            clearable
            v-model="searchRemain.result"
            :items="[
              { text: '面试中', value: 0 },
              { text: '不合格未通过', value: 1 },
              { text: '合格录用', value: 2 },
              { text: '不合格录用', value: 3 },
            ]"
            dense
            outlined
          ></v-select>
        </v-col>

        <v-col cols="12" md="2" v-if="showSelect">
          <v-select
            label="面试状态"
            clearable
            v-model="searchRemain.status"
            :items="[
              { text: '面试中', value: 1 },
              { text: '面试完成', value: 2 },
              { text: '已进入待上船名单', value: 3 },
              { text: '已退回', value: 4 },
            ]"
            dense
            outlined
          ></v-select>
        </v-col>
        <v-col cols="12" md="2" v-if="!showSelect">
          <v-select
            label="面试船员级别"
            clearable
            v-model="searchRemain.businessType"
            :items="[
              { text: '甲板部高级船员', value: 'deck_officer_interview' },
              { text: '甲板部普通船员', value: 'deck_rating_interview' },
              { text: '轮机部高级船员', value: 'engine_officer_interview' },
              { text: '轮机部普通船员', value: 'engine_rating_interview' },
              { text: '大厨', value: 'chef_interview' },
              { text: '轮机长', value: 'chief_engineer_interview' },
              { text: '船长', value: 'captain_interview' },
            ]"
            dense
            outlined
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!--        <v-btn-->
        <!--          outlined-->
        <!--          dense-->
        <!--          tile-->
        <!--          color="success"-->
        <!--          :disabled="selected.length === 0"-->
        <!--          class="mx-1"-->
        <!--          @click="startTakeBack"-->
        <!--          v-permission="['上船面试名单管理:取回']"-->
        <!--        >-->
        <!--          <v-icon left>mdi-tray-arrow-up</v-icon>-->
        <!--          取回-->
        <!--        </v-btn>-->
        <v-btn
          outlined
          dense
          tile
          color="success"
          :disabled="selected.length === 0"
          class="mx-1"
          :loading="loading2"
          @click="changeApproveStatus"
          v-permission="['上船面试名单管理:审核']"
        >
          <v-icon left>mdi-magnify-plus-outline</v-icon>
          审核
        </v-btn>
        <v-btn
          outlined
          dense
          tile
          color="error"
          :disabled="selected.length === 0"
          class="mx-1"
          @click="withDraw"
          v-permission="['上船面试名单管理:退回']"
        >
          <v-icon left>mdi-arrow-u-right-bottom</v-icon>
          退回
        </v-btn>
        <v-btn
          outlined
          dense
          tile
          color="blue"
          :disabled="selected.length === 0"
          class="mx-1"
          @click="upLoad"
          v-permission="['上船面试名单管理:提交到待上船船员名单']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          提交到待上船船员名单
        </v-btn>
      </template>
      <template v-slot:[`item.onSite`]="{ item }">
        {{ item.onSite === 0 ? '在线审批面试' : '现场面试' }}
      </template>
      <template v-slot:[`item.result`]="{ item }">
        <v-chip v-if="item.result === 0" small dark color="info">面试中</v-chip>
        <v-chip v-else-if="item.result === 1" small dark color="error">
          不合格未通过
        </v-chip>
        <v-chip v-else-if="item.result === 2" small dark color="success">
          合格录用
        </v-chip>
        <v-chip v-else small dark color="warning">不合格录用</v-chip>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 1" small dark color="info">
          面试进行中
        </v-chip>
        <v-chip v-else-if="item.status === 2" small dark color="success">
          面试完成
        </v-chip>
        <v-chip v-else-if="item.status === 3" small dark color="#00695C">
          已进入待上船名单
        </v-chip>
        <v-chip v-else-if="item.status === 4" small dark color="#000000">
          已退回
        </v-chip>
      </template>
      <template v-slot:[`item.moStatus`]="{ item }">
        <v-chip v-if="item.moStatus === '2'" small dark color="success">
          已完成
        </v-chip>
        <v-chip
          v-else-if="
            item.moStatus === '0' ||
            item.moStatus === '1' ||
            item.moStatus === '3'
          "
          small
          dark
          color="error"
        >
          未完成
        </v-chip>
      </template>
      <template v-slot:[`item.approveStatus`]="{ item }">
        <v-chip v-if="item.approveStatus === 1" small dark color="success">
          已审核
        </v-chip>
        <v-chip v-else-if="item.approveStatus === 0" small dark color="error">
          未审核
        </v-chip>
      </template>
    </v-table-searchable>
    <template>
      <v-dialog v-model="isdialog" width="1000" hide-overlay attach="#mask">
        <v-card>
          <v-card-title>退回原因</v-card-title>
          <v-card-text>
            <v-textarea
              v-model="backReason"
              row-height="15"
              rows="4"
              dense
              outlined
            ></v-textarea>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn depressed @click="close">取消</v-btn>
            <v-btn
              depressed
              color="primary"
              :loading="loading1"
              @click="submit"
            >
              提交
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <!--      <v-dialog v-model="isdialog1" width="1000" hide-overlay attach="#mask">-->
      <!--        <v-card>-->
      <!--          <v-card-title>取回原因</v-card-title>-->
      <!--          <v-card-text>-->
      <!--            <v-textarea-->
      <!--              v-model="comment"-->
      <!--              row-height="15"-->
      <!--              rows="4"-->
      <!--              dense-->
      <!--              outlined-->
      <!--            ></v-textarea>-->
      <!--          </v-card-text>-->
      <!--          <v-card-actions>-->
      <!--            <v-spacer></v-spacer>-->
      <!--            <v-btn depressed @click="close">取消</v-btn>-->
      <!--            <v-btn-->
      <!--              depressed-->
      <!--              color="primary"-->
      <!--              @click="takeBack"-->
      <!--              :loading="sloading"-->
      <!--            >-->
      <!--              取回-->
      <!--            </v-btn>-->
      <!--          </v-card-actions>-->
      <!--        </v-card>-->
      <!--      </v-dialog>-->
    </template>
  </v-container>
</template>
<script>
export default {
  name: 'interview-management',
  created() {
    this.tableName = '上船前面试名单管理'
    this.reqUrl = '/business/crew/deploy/interview/page'
    this.headers = [
      { text: '业务编码', value: 'bizCode' },
      { text: '拟上船船舶', value: 'planShipName', sortable: false },
      { text: '船员姓名', value: 'crewUserName', sortable: false },
      { text: '岗位职务', value: 'post' },
      { text: '面试方式', value: 'onSite' },
      { text: '面试时间', value: 'happenTime' },
      { text: '面试内容', value: 'content' },
      { text: '面试官', value: 'interviewer' },
      { text: '发起人', value: 'submitter' },
      { text: '备注', value: 'remark' },
      { text: '面试结果', value: 'result' },
      { text: '面试状态', value: 'status' },
      { text: '培训状态', value: 'moStatus' },
      { text: '审核状态', value: 'approveStatus' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '面试时间',
      value: 'happenTime',
    }
    this.pushParams = {
      name: 'interview-detail',
    }
  },

  data() {
    return {
      isdialog: false,
      isdialog1: false,
      loading1: false,
      loading2: false,
      sloading: false,
      backReason: '',
      comment: '',
      selected: [],
      showSelect: true, // 控制v-col的可见性
      searchRemain: {
        isMe: false,
        businessType: 'deck_rating_interview',
        status: 1,
      },
    }
  },

  methods: {
    // checkForDuplicates(array) {
    //   const arr = array.map((ele) => ele.post)
    //   return new Set(arr).size !== arr.length
    // },

    async upLoad() {
      let a = this.areAllAppStatusPass()
      if (!a) {
        this.$dialog.message.error('请选择已审核的人员进行提交')
        return
      }
      if (
        !(await this.$dialog.msgbox.confirm(
          '是否将选中记录提交到待上船计划中？',
        ))
      )
        return

      // const shipFlag = this.selected[0].bizCode
      // console.log(this.selected)
      // let flag = this.selected.find((ele) => ele.bizCode !== shipFlag)
      // if (flag) {
      //   this.$dialog.message.error('请选择同一调配的船员')
      //   return
      // }
      // if (this.checkForDuplicates(this.selected)) {
      //   this.$dialog.message.error('请勿选择重复岗位')
      //   return
      // }
      let arr = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/interview/addToPrepare/batch`,
        arr,
      )
      if (errorRaw) {
        return
      }

      this.$dialog.message.success('提交成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    areAllStatusTwo() {
      return this.selected.every((item) => item.status === 2)
    },
    areAllResultPass() {
      return this.selected.every(
        (item) => item.result === 2 || item.result === 3,
      )
    },
    areAllAppStatusPass() {
      return this.selected.every((item) => item.approveStatus === 1)
    },
    areAllStatusOneTwo() {
      return this.selected.every(
        (item) => item.status === 2 || item.status === 1,
      )
    },
    areAllStatusNotOne() {
      return this.selected.every((item) => item.status === 1)
    },
    isMeChange(newValue) {
      if (newValue) {
        // 清空v-select的数据
        this.searchRemain.result = null
        this.searchRemain.post = null
        this.searchRemain.planShipCode = null
        // 隐藏v-col
        this.showSelect = false
      } else {
        // 根据需要重置逻辑或保持显示
        this.showSelect = true
      }
    },
    withDraw() {
      let checkStatus = this.areAllStatusOneTwo()
      if (checkStatus) {
        this.isdialog = true
        return
      }
      this.$dialog.message.error('仅面试完成和面试中才可退回')
    },
    async changeApproveStatus() {
      let isAllStatusThree = this.areAllStatusTwo()
      let isAllResultPass = this.areAllResultPass()
      if (!(isAllStatusThree && isAllResultPass)) {
        this.$dialog.message.error('请选择面试完成且结果为录用的人员进行操作')
        return
      }
      if (
        !(await this.$dialog.msgbox.confirm('是否将选中记录变更为已审核？'))
      ) {
        return
      }
      this.loading2 = true
      let ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/interview/ChangeAppSta/batch`,
        ids,
      )
      this.loading2 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('操作成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    startTakeBack() {
      let checkStatus = this.areAllStatusNotOne()
      if (checkStatus) {
        this.isdialog1 = true
        return
      }
      this.$dialog.message.error('仅面试中的才可取回')
    },
    async takeBack() {
      if (!(await this.$dialog.msgbox.confirm('是否确认取回？'))) return
      this.sloading = true
      let ids = this.selected.map((ele) => ele.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/interview/takeBack/batch`,
        {
          ids: ids,
          comment: this.comment,
          adopt: true,
          params: {},
        },
      )
      if (errorRaw) {
        this.sloading = false
        return
      }
      this.backReason = ''
      this.isdialog1 = false
      this.sloading = false
      this.$dialog.message.success('取回成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    close() {
      this.isdialog = false
      this.isdialog1 = false
      this.backReason = ''
      this.comment = ''
    },
    async submit() {
      if (!(await this.$dialog.msgbox.confirm('是否确认提交？'))) return
      let ids = this.selected.map((ele) => ele.id)
      this.loading1 = true
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/interview/withDraw/batch`,
        {
          ids: ids,
          backReason: this.backReason,
        },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.backReason = ''
      this.isdialog = false
      this.$dialog.message.success('成功提交')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
