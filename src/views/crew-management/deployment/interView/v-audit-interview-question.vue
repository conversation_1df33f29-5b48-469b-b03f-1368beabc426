<template>
  <v-card outlined>
    <v-card-title class="py-1">
      审批历史
      <v-spacer></v-spacer>

      <span class="text-h20">当前审批人:{{ approvedManagersList }}</span>
      <!-- 待审批人 -->

      <v-btn
        text
        :loading="loading"
        v-if="!img"
        color="primary"
        @click="loadPic"
      >
        显示流程图
      </v-btn>
      <v-btn text v-else color="primary" @click="img = null">隐藏流程图</v-btn>
    </v-card-title>
    <v-card-text class="pb-0">
      <v-img v-if="img" max-height="500" max-width="1000" :src="img"></v-img>
      <v-divider></v-divider>
      <v-data-table
        :headers="hisTaskHeaders"
        :items="hisTask"
        disable-pagination
        dense
        class="use-divider"
        hide-default-footer
      ></v-data-table>
      <v-divider></v-divider>
    </v-card-text>
    <v-card-text v-if="isExist()" class="py-2">
      <v-btn color="primary" @click="openQuestion">问卷填写</v-btn>
      <v-btn style="left: 12px" color="success" @click="exportQuestion">
        <v-icon left>mdi-file-pdf-box</v-icon>
        问卷导出
      </v-btn>
    </v-card-text>
    <v-card-text v-if="auditParams && showParams" class="py-0">
      <v-radio-group
        :rules="[rules.radio]"
        ref="radio"
        class="pb-0"
        v-model="params"
        row
      >
        <v-radio label="不合格未通过" value="notPassNotHire"></v-radio>
        <v-radio label="合格录用" value="passAndHire"></v-radio>
        <v-radio label="不合格录用" value="notPassButHire"></v-radio>
      </v-radio-group>
      <v-textarea
        class="mt-1"
        ref="textarea"
        label="批注"
        outlined
        height="80"
        :rules="params === 'passAndHire' ? [] : [rules.required]"
        v-model="comment"
      ></v-textarea>
    </v-card-text>
    <v-dialog
      v-if="isExist()"
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="dialog"
    >
      <v-card>
        <v-card-title>
          问卷填写
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row v-for="(item, value) in questionList" :key="value">
                <v-col cols="12">
                  <span style="color: #111659" class="text-h6">
                    {{ item.question }}
                  </span>
                  <p style="color: #111659" class="text-h7">
                    {{ getOperater(item) }}
                  </p>
                  <v-text-field
                    style="padding-left: 24px; padding-top: 8px"
                    v-if="currentRole.includes(item.operatorPosition)"
                    :rules="[rules.quesRequired]"
                    outlined
                    dense
                    v-model="item.answer"
                  ></v-text-field>
                  <p
                    v-else
                    style="
                      color: #416a85;
                      font-size: 1.25rem;
                      padding-left: 24px;
                      padding-top: 8px;
                    "
                    class="text-h6"
                  >
                    {{ item.answer }}
                  </p>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-btn
                    v-if="canSave"
                    outlined
                    tile
                    color="success"
                    class="mx-1"
                    @click="save"
                    block
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    保存
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-card>
</template>
<script>
export default {
  name: 'v-audit-interview-question',
  props: {
    auditParams: {
      type: Object,
      default: () => ({
        taskId: '',
        proccessInstanceId: '',
      }),
    },
    viewId: {
      type: String,
      default: '',
    },
    showParams: {
      type: [Object, String, Boolean],
      default: true,
    },
    shipCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      img: null,
      comment: '',
      hisTask: [],
      params: '',
      rules: {
        radio: (v) => v !== '' || '请选择审批意见',
        required: (v) => !!v || '请输入批注',
        quesRequired: (v) => !!v || '不能为空',
      },
      approvedManagersList: '',
      dialog: false,
      questionList: [],
      saveParams: {},
      currentRole: '',
      canSave: false,
    }
  },
  created() {
    this.hisTaskHeaders = [
      { text: '环节名称', value: 'activityName' },
      { text: '审批人', value: 'assignee' },
      { text: '审批时间', value: 'endTime' },
      { text: '审批意见', value: 'comment' },
    ]
  },
  inject: {
    form: { default: null },
  },
  methods: {
    async loadHisTask() {
      const { data } = await this.getAsync(
        '/flow/task/getHisTaskInsListByProInsId',
        {
          processInstanceId: this.auditParams.processInstanceId,
        },
      )
      this.hisTask = data
    },
    async submit() {
      if (!this.auditParams.taskId) return null
      const { errorRaw } = await this.postAsync(
        '/flow/task/completeTaskAndCommentAndSetVar',
        {
          taskId: this.auditParams.taskId,
          comment: this.comment,
          adopt: true,
          params: { opinion: this.params },
        },
      )
      return errorRaw
    },
    arrayBufferToBase64(buffer) {
      let binary = ''
      let bytes = new Uint8Array(buffer)
      let len = bytes.byteLength
      for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
      }
      return `data:image/bmp;base64,${window.btoa(binary)}`
    },
    async loadPic() {
      this.loading = true
      const result = await this.getArrayBuffer(
        '/flow/diagram/getTracePicByProcessInstanceId',
        { processInstanceId: this.auditParams.processInstanceId },
      )
      this.img = this.arrayBufferToBase64(result.data)
      this.loading = false
    },
    async loadPerson() {
      if (this.auditParams.processInstanceId) {
        const manager = await this.getAsync(
          '/business/common/ship/managementOwner/page',
          { size: 100, shipCode: this.shipCode },
        )
        // console.log('船舶分管人员：', manager.data.records)
        const approved = await this.getAsync('/flow/task/getPersonByTaskId', {
          taskId: this.auditParams.processInstanceId,
        })
        // console.log('可审批人员：', approved.data)
        // approved.data已经是一个包含姓名的数组
        const approvedNames = approved.data
        // 初始化一个数组来存储已批准的managerName
        let approvedManagers = []

        // 遍历manager.data.records数组，并检查每个managerName是否存在于approvedNames中
        if (
          Array.isArray(approvedNames) &&
          manager &&
          manager.data &&
          manager.data.records &&
          Array.isArray(manager.data.records)
        ) {
          manager.data.records.forEach((record) => {
            if (
              record &&
              record.managerName &&
              approvedNames.includes(record.managerName)
            ) {
              const useInfo = JSON.parse(localStorage.getItem('userInfo'))
              this.currentRole = useInfo.roleName
              this.$emit('getRole', this.currentRole)
              // console.log('currentRole------------->')
              // console.log(this.currentRole)

              approvedManagers.push(record.managerName)
            }
          })
        } else {
          console.log('Data is not valid or records are not available')
        }

        // 如果有已批准的managerName，将它们转换为一个逗号分隔的字符串并打印出来
        this.approvedManagersList =
          approvedManagers.length > 0 ? approvedManagers.join(', ') : '无'
      } else {
        this.approvedManagersList = '无'
      }
    },

    validate(force, value) {
      if (!this.$refs.radio) return true
      if (this.$refs.radio.validate(force, value)) {
        return this.$refs.textarea.validate(force, value)
      }
      return true
    },
    closeForm() {
      this.dialog = false
    },
    async save() {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请全部填写')
        return
      }
      const qList = []
      this.questionList.forEach((item) => {
        if (this.currentRole.includes(item.operatorPosition)) {
          qList.push(item)
        }
      })
      // console.log('qList', qList)
      this.saveParams.quesRecordList = qList
      this.saveParams.viewId = this.viewId
      // console.log('saveParams----------->')
      // console.log(this.saveParams)
      const { errorRaw } = await this.postAsync(
        '/business/crew/deploy/interview/questionRecord/save',
        this.saveParams,
      )
      if (errorRaw) {
        return
      }
      this.$emit('getValue', true)
      this.$refs.form.reset()
      this.closeForm()
      this.$dialog.message.success('操作成功')
      await this.getQuestionList()
    },
    openQuestion() {
      this.dialog = true
      // console.log('auditParams------------->')
      // console.log(this.auditParams)
    },
    async getQuestionList() {
      const { errorRaw, data } = await this.getAsync(
        '/business/crew/deploy/interview/getQuestion',
        { viewId: this.viewId },
      )
      if (errorRaw) {
        return
      }
      this.questionList = data
      if (this.questionList !== null) {
        let res = new Map()
        let arr = this.questionList
        let arr1 = arr.filter(
          (arr) =>
            !res.has(arr.operatorPosition) &&
            res.set(arr.operatorPosition, '1'),
        )
        // console.log('position', arr1)
        arr1.forEach((item) => {
          if (this.currentRole.includes(item.operatorPosition)) {
            this.canSave = true
          }
        })
      }
      // console.log('CanSave', this.canSave)
      // console.log('currentRole------------->')
      // console.log(this.currentRole)
      this.$emit('getCanSave', this.canSave)
      // console.log('questionList----------->')
      // console.log(this.questionList)
    },
    isExist() {
      if (this.questionList === null || this.questionList.length <= 0) {
        return false
      }
      // console.log('isExist,false')
      return true
    },
    getOperater(item) {
      let name = ''
      let date = ''
      if (item.operatorName === undefined) {
        return null
      }
      name = item.operatorName
      if (item.updateTime !== undefined || item.updateTime !== null) {
        date = item.updateTime
      }
      return '填写人：' + name + ' 时间:' + date
    },
    async exportQuestion() {
      await this.getBlobDownload(
        '/business/crew/deploy/interview/exportQuestion',
        { viewId: this.viewId },
      )
    },
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
  mounted() {
    this.loadHisTask()
    this.loadPerson().then(() => {
      this.getQuestionList()
    })
  },
}
</script>

<style></style>
