<template>
  <v-card outlined>
    <v-card-title class="py-1">
      审批历史
      <v-spacer></v-spacer>

      <span class="text-h20">当前审批人:{{ approvedManagersList }}</span>
      <!-- 待审批人 -->

      <v-btn
        text
        :loading="loading"
        v-if="!img"
        color="primary"
        @click="loadPic"
      >
        显示流程图
      </v-btn>
      <v-btn text v-else color="primary" @click="img = null">隐藏流程图</v-btn>
    </v-card-title>
    <v-card-text class="pb-0">
      <v-img v-if="img" max-height="500" max-width="1000" :src="img"></v-img>
      <v-divider></v-divider>
      <v-data-table
        :headers="hisTaskHeaders"
        :items="hisTask"
        disable-pagination
        dense
        class="use-divider"
        hide-default-footer
      ></v-data-table>
      <v-divider></v-divider>
    </v-card-text>
    <v-card-text
      v-if="auditParams && showParams && isRoleManagerNew"
      class="py-0"
    >
      <v-radio-group
        :rules="[rules.radio]"
        ref="radio"
        class="pb-0"
        v-model="params"
        row
      >
        <v-radio label="不合格未通过" value="notPassNotHire"></v-radio>
        <v-radio label="合格录用" value="passAndHire"></v-radio>
        <v-radio label="不合格录用" value="notPassButHire"></v-radio>
      </v-radio-group>
      <v-textarea
        class="mt-1"
        ref="textarea"
        label="批注"
        outlined
        height="80"
        :rules="params === 'passAndHire' ? [] : [rules.required]"
        v-model="comment"
      ></v-textarea>
    </v-card-text>
  </v-card>
</template>
<script>
export default {
  name: 'v-audit-interview',
  props: {
    auditParams: {
      type: Object,
      default: () => ({
        taskId: '',
        proccessInstanceId: '',
      }),
    },
    showParams: {
      type: [Object, String, Boolean],
      default: true,
    },
    shipCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      img: null,
      comment: '',
      hisTask: [],
      params: '',
      rules: {
        radio: (v) => v !== '' || '请选择审批意见',
        required: (v) => !!v || '请输入批注',
      },
      approvedManagersList: '',
      isRoleManagerNew: true,
    }
  },
  created() {
    this.hisTaskHeaders = [
      { text: '环节名称', value: 'activityName' },
      { text: '审批人', value: 'assignee' },
      { text: '审批时间', value: 'endTime' },
      { text: '审批意见', value: 'comment' },
    ]
  },
  inject: {
    form: { default: null },
  },
  methods: {
    async loadHisTask() {
      const { data } = await this.getAsync(
        '/flow/task/getHisTaskInsListByProInsId',
        {
          processInstanceId: this.auditParams.processInstanceId,
        },
      )
      this.hisTask = data
    },
    async submit() {
      if (!this.auditParams.taskId) return null
      const { errorRaw } = await this.postAsync(
        '/flow/task/completeTaskAndCommentAndSetVar',
        {
          taskId: this.auditParams.taskId,
          comment: this.comment,
          adopt: true,
          params: { opinion: this.params },
        },
      )
      return errorRaw
    },
    arrayBufferToBase64(buffer) {
      let binary = ''
      let bytes = new Uint8Array(buffer)
      let len = bytes.byteLength
      for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
      }
      return `data:image/bmp;base64,${window.btoa(binary)}`
    },
    async loadPic() {
      this.loading = true
      const result = await this.getArrayBuffer(
        '/flow/diagram/getTracePicByProcessInstanceId',
        { processInstanceId: this.auditParams.processInstanceId },
      )
      this.img = this.arrayBufferToBase64(result.data)
      this.loading = false
    },
    async loadPerson() {
      if (this.auditParams.processInstanceId) {
        const manager = await this.getAsync(
          '/business/common/ship/managementOwner/pageNew',
          { size: 100, shipCode: this.shipCode },
        )
        console.log('船舶分管人员：', manager.data.records)
        const approved = await this.getAsync(
          '/flow/task/getPersonByTaskIdAndShipCode',
          {
            taskId: this.auditParams.processInstanceId,
            shipCode: this.shipCode,
          },
        )
        console.log('可审批人员：', approved.data)
        // approved.data已经是一个包含姓名的数组
        const approvedNames = approved.data
        // 初始化一个数组来存储已批准的managerName
        let approvedManagers = []

        // 遍历manager.data.records数组，并检查每个managerName是否存在于approvedNames中
        if (
          Array.isArray(approvedNames) &&
          manager &&
          manager.data &&
          manager.data.records &&
          Array.isArray(manager.data.records)
        ) {
          manager.data.records.forEach((record) => {
            if (
              record &&
              record.managerName &&
              approvedNames.includes(record.managerName)
            ) {
              approvedManagers.push(record.managerName)
            }
          })
        } else {
          console.log('Data is not valid or records are not available')
        }

        // 如果有已批准的managerName，将它们转换为一个逗号分隔的字符串并打印出来
        this.approvedManagersList =
          approvedManagers.length > 0 ? approvedManagers.join(', ') : '无'
      } else {
        this.approvedManagersList = '无'
      }
      // 校验是否最新分配的一个船舶分管主管
      if (
        this.approvedManagersList.includes(
          this.$local.data.get('userInfo').nickName,
        ) ||
        this.$local.data.get('userInfo').roleName == '管理员'
      ) {
        this.isRoleManagerNew = true
      } else {
        this.isRoleManagerNew = false
      }
    },

    validate(force, value) {
      if (!this.$refs.radio) return true
      if (this.$refs.radio.validate(force, value)) {
        return this.$refs.textarea.validate(force, value)
      }
      return false
    },
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
  mounted() {
    this.loadHisTask()
    this.loadPerson()
  },
}
</script>

<style></style>
