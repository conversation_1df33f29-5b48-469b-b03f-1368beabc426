<template>
  <v-container fluid>
    <v-detail-view
      :title="title"
      :tooltip="detailInfo.bizCode"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      v-permission="['上船面试名单管理:编辑']"
    >
      <template v-slot:topcontent v-if="detailInfo.auditParams">
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit-interview-question
              @getValue="getValue"
              @getRole="getRole"
              @getCanSave="getCanSave"
              ref="audit"
              :auditParams="auditParams"
              :view-id="viewId"
              :showParams="isTrue"
              :shipCode="detailInfo.planShipCode"
            ></v-audit-interview-question>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
          v-permission="['上船面试名单管理:返回列表']"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <v-btn
          width="60"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          :loading="loading"
          v-permission="['上船面试名单管理:保存']"
        >
          保存
        </v-btn>
      </template>
      <template v-slot:面试基本信息>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="拟上船舶"
                  dense
                  readonly
                  outlined
                  v-model="detailInfo.planShipName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="岗位职务"
                  dense
                  readonly
                  outlined
                  v-model="detailInfo.post"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="面试方式"
                  readonly
                  v-model="detailInfo.onSite"
                  :items="[
                    { text: '在线审批面试', value: 0 },
                    { text: '现场面试', value: 1 },
                  ]"
                  dense
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="2" v-if="detailInfo.interviewer">
                <v-text-field
                  label="面试官"
                  dense
                  readonly
                  outlined
                  v-model="detailInfo.interviewer"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  dense
                  outlined
                  readonly
                  label="面试时间"
                  v-model="detailInfo.happenTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="面试结果"
                  v-model="detailInfo.result"
                  :items="[
                    { text: '面试中', value: 0 },
                    { text: '不合格未通过', value: 1 },
                    { text: '合格录用', value: 2 },
                    { text: '不合格录用', value: 3 },
                  ]"
                  dense
                  readonly
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="专职见习费用承担"
                  dense
                  readonly
                  v-if="isIntern"
                  outlined
                  v-model="detailInfo.costBearing"
                  :items="[
                    { text: '船员公司承担', value: 0 },
                    { text: '船东公司承担', value: 1 },
                  ]"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="2">
                <v-select
                  label="特殊情况"
                  outlined
                  dense
                  :error="detailInfo.userInfo.inSpeciallist !== 0"
                  :items="[
                    { text: '无', value: 0 },
                    { text: '警告', value: 1 },
                    { text: '慎用', value: 2 },
                  ]"
                  v-model="detailInfo.userInfo.inSpeciallist"
                  readonly
                ></v-select>
              </v-col>
              <v-col cols="12" md="10">
                <v-text-field
                  label="特殊情况说明"
                  dense
                  outlined
                  required
                  :error="detailInfo.userInfo.inSpeciallist !== 0"
                  v-model="detailInfo.userInfo.specialRemark"
                  readonly
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-textarea
                  dense
                  outlined
                  readonly
                  label="面试内容"
                  v-model="detailInfo.content"
                ></v-textarea>
              </v-col>
              <v-col>
                <v-textarea
                  dense
                  readonly
                  outlined
                  label="备注"
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
              <v-col>
                <v-textarea
                  dense
                  readonly
                  outlined
                  label="退回原因"
                  v-model="detailInfo.backReason"
                ></v-textarea>
              </v-col>
            </v-row>
            <v-attach-list
              :ship-code="detailInfo.shipCode"
              :disabled="detailInfo.status === 2 || detailInfo.status === 1"
              :attachments="detailInfo.attachmentRecords"
              @change="changeAttachment"
            ></v-attach-list>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:船员个人信息按钮>
        <v-btn tile outlined dense color="info" @click="openCard">
          <v-icon left>mdi-eye</v-icon>
          查看船员详细信息
        </v-btn>
      </template>
      <template v-slot:船员个人信息>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-crew-pic-interview
                :ImagePicture="
                  detailInfo.userInfo.identificationPhotoAttachment
                "
              ></v-crew-pic-interview>
            </v-row>
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="船员姓名"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.chName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2" v-if="detailInfo.userInfo.idCard">
                <v-text-field
                  label="身份证号"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.idCard"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="性别"
                  outlined
                  dense
                  :items="[
                    { text: '男', value: '1' },
                    { text: '女', value: '2' },
                  ]"
                  v-model="detailInfo.userInfo.gender"
                ></v-select>
              </v-col>
              <v-col cols="2" md="2">
                <v-text-field
                  label="国家"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.country"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="年龄（岁）"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.age"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="外语语种"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.foreignLanguages"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="外语水平"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.foreignLevel"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="电话"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.phone"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="证书职务"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.certificatePosition"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="实际职务"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.actualPosition"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="最高职称"
                  outlined
                  dense
                  v-model="detailInfo.userInfo.highestTitle"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="船员类型"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.creProperty.creFeature"
                ></v-text-field>
              </v-col>
              <v-col cols="2" md="4">
                <v-text-field
                  label="船员调配公司"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.creProperty.creCompany"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="合同公司"
                  dense
                  outlined
                  v-model="detailInfo.userInfo.creProperty.creType"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-text>
          <v-attachment-only
            title="船员身份证复印件"
            :attachment="detailInfo.userInfo.crewAttachment.idCardCopy"
            accept="all"
            :disabled="true"
          ></v-attachment-only>
          <v-attachment-only
            title="无工作或无缴纳社保证明"
            :attachment="detailInfo.userInfo.crewAttachment.proveNoWork"
            accept="all"
            :disabled="true"
          ></v-attachment-only>
          <v-attachment-only
            title="船员简历表"
            :attachment="detailInfo.userInfo.crewAttachment.resume"
            accept="all"
            :disabled="true"
          ></v-attachment-only>
          <v-attachment-only
            title="船员工作考评表（或业务部门推荐）"
            :attachment="detailInfo.userInfo.crewAttachment.workScoreTable"
            accept="all"
            :disabled="true"
          ></v-attachment-only>
        </v-card-text>
      </template>
    </v-detail-view>
    <v-crew-card :initialData="initialData" v-model="dialog"></v-crew-card>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import VAuditInterviewQuestion from './v-audit-interview-question.vue'
// import VAuditInterview from './v-audit-interview.vue'
import vCrewPicInterview from './v-crewPic-interview.vue'
import VCrewCard from '../../private/v-crew-card.vue'
import VAttachmentOnly from '../../crew-information/private/v-attachmentOnly.vue'
export default {
  components: {
    vCrewPicInterview,
    VAuditInterviewQuestion,
    // VAuditInterview,
    VCrewCard,
    VAttachmentOnly,
  },
  name: 'interview-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'interview-management'
    this.subtitles = ['面试基本信息', '船员个人信息']
    this.getDetailInfo()
  },
  data() {
    return {
      title: '上船名单详情',
      attachmentRecords: [],
      auditParams: false,
      viewId: '',
      detailInfo: {
        userInfo: {
          creProperty: {},
          crewAttachment: {},
        },
        attachmentIds: [],
      },
      loading: false,
      params: '',
      dialog: false,
      initialData: {},
      quesCommit: false,
      currentRole: '',
      canSave: false,
      isIntern: false,
    }
  },
  computed: {
    isTrue: function () {
      return (
        this.detailInfo.auditParams.taskId &&
        !this.detailInfo.auditParams.isReject
      )
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/deploy/interview/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = data
      // console.log('detailInfo------------>')
      // console.log(this.detailInfo)
      this.title = `${data.bizCode}--${this.title}`
      this.auditParams = data.auditParams
      this.viewId = data.id
      console.log(this.detailInfo.post)
      if (this.detailInfo.post.indexOf('专职见习') >= 0) {
        this.isIntern = true
      }
      const special = this.detailInfo.userInfo?.inSpeciallist
      const remark = this.detailInfo.userInfo?.specialRemark
      console.log(special == 1)
      console.log(remark)
      if (special === 1 && remark) {
        this.$dialog.msgbox.confirm(
          '当前船员为警告名单船员，请查看特殊情况说明。',
        )
      }
      if (special === 2 && remark) {
        this.$dialog.msgbox.confirm(
          '当前船员为慎用名单船员，请查看特殊情况说明。',
        )
      }
    },
    async save() {
      this.loading = true
      if (!this.quesCommit) {
        if (this.canSave) {
          this.loading = false
          this.$dialog.message.error('请填写问卷并提交')
          return
        }
      }
      if (this.$refs.aform && !this.$refs.aform.validate()) {
        this.loading = false
        return
      }
      if (!this.$refs.form.validate()) {
        this.loading = false
        return
      }
      const errorRaw = await this.$refs.audit.submit()
      this.loading = false
      if (!errorRaw) {
        this.$dialog.message.success('操作成功')
        this.closeAndTo(this.backRouteName, {})
      }
    },
    openCard() {
      this.initialData = { userId: this.detailInfo.userInfo.userId }
      this.dialog = true
    },
    getValue(quesCommit) {
      this.quesCommit = quesCommit
      // console.log('quesCommit')
      // console.log(this.quesCommit)
    },
    getRole(currentRole) {
      this.currentRole = currentRole
      // console.log('currentRole')
      // console.log(this.currentRole)
    },
    getCanSave(canSave) {
      this.canSave = canSave
      // console.log('detail-canSave')
      // console.log(this.canSave)
    },
  },

  mounted() {},
}
</script>

<style></style>
