<template>
  <v-sheet class="my-3">
    <v-divider></v-divider>
    <v-data-table
      :items="items"
      hide-default-footer
      show-select
      dense
      v-model="selected"
      @click:row="selectRow"
      @dblclick:row="dbclick"
      :headers="船员基本信息"
      disable-pagination
    >
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" dark small color="">未提交面试</v-chip>
        <v-chip v-else-if="item.status === 2" dark small color="error">
          已退回
        </v-chip>
        <v-chip v-else dark small color="success">已提交面试</v-chip>
      </template>
      <template v-slot:[`item.operation`]="{ item }">
        <v-btn
          small
          color="info"
          dense
          outlined
          single-line
          @click="upLoad(item)"
          :loading="loading"
        >
          发起面试
        </v-btn>
      </template>
    </v-data-table>
    <v-divider></v-divider>
    <interview-dialog-info
      v-bind="$attrs"
      v-on="$listeners"
      v-model="dialog"
      :initialData="params"
      @loadingChange="loadingChange"
    ></interview-dialog-info>
    <v-crew-card :initialData="initialData" v-model="dialogTwo"></v-crew-card>
  </v-sheet>
</template>
<script>
import VCrewCard from '../../private/v-crew-card.vue'
let time = null

import interviewDialogInfo from './interview-dialog-info.vue'
export default {
  components: { interviewDialogInfo, VCrewCard },
  name: 'position-crew-match',
  props: {
    items: {
      type: [Array, Object],
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.船员基本信息 = [
      { text: '船员姓名', value: 'userName' },
      { text: '船员联系方式', value: 'phoneNo' },
      { text: '船员面试状况', value: 'status' },
      { text: '按钮', value: 'operation' },
    ]
  },
  data() {
    return {
      selected: [],
      dialog: false,
      params: {},
      loading: false,
      initialData: {},
      dialogTwo: false,
    }
  },
  watch: {
    selected(val) {
      this.$emit('update', val)
    },
    isLoading(val) {
      this.loading = val
    },
  },

  methods: {
    selectRow(_, { isSelected, item }) {
      clearTimeout(time)
      time = setTimeout(() => {
        this.selected = isSelected ? [] : [item]
      }, 150)
    },
    dbclick(_, { item }) {
      clearTimeout(time)
      this.selected = [item]
      this.initialData = this.selected[0]
      this.dialogTwo = true
    },
    upLoad(item) {
      this.selected[0] = item
      if (item.status === 1) {
        this.$dialog.message.error('当前船员已提交面试')
        return
      }
      this.loading = true
      this.$emit('changeLoading', true)
      console.log('specUserList', this.$attrs.specUserList)
      console.log('item.userId', item.userId)

      let userIdExist = this.$attrs.specUserList.includes('true')
      console.log('不是最大公休人员id', this.$attrs.specUserList)
      console.log('已选择人员', item)
      console.log('判断是否为最大公休天数人员', userIdExist)
      this.params = {
        isBatch: false,
        shipCode: this.$attrs.shipCode,
        position: this.$attrs.position,
        crewUserId: item.userId,
        parentId: item.parentId,
        bizCode: this.$attrs.bizCode,
        specUserList: this.$attrs.specUserList,
        deployMatchDetailId: item.deployMatchDetailId,
        userIdExist: userIdExist,
      }
      this.dialog = true
    },
    loadingChange() {
      this.loading = false
    },
  },

  mounted() {},
}
</script>

<style></style>
