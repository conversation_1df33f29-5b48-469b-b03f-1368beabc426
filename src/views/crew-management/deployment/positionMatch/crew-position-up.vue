<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新增报名船员
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :dense="false"
          :headers="headers"
          req-url="/business/crew/baseInfo/simple/page"
          :search-remain="searchRemain"
          item-key="userId"
          outlined
          @dbclick="confirm"
        >
          <template #searchflieds>
            <slot name="searchflieds">
              <!-- 剩余参数插槽 -->
            </slot>
            <!-- <v-col cols="12" md="4">
              <v-text-field
                label="身份证号"
                outlined
                dense
                clearable
                v-model="searchRemain.idCard"
              ></v-text-field>
            </v-col> -->
          </template>
          <template v-for="h in headers" v-slot:[`item.${h.value}`]="{ item }">
            <slot :item="item" :name="`item.${h.value}`"></slot>
          </template>
          <template v-slot:[`item.crePropertyFeature`]="{ item }">
            {{ item.creProperty && item.creProperty.creFeature }}
          </template>
          <template v-slot:[`item.crePropertyType`]="{ item }">
            {{ item.creProperty && item.creProperty.creType }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'crew-position-up',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.headers = [
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
      { text: '船员所属公司', value: 'creCompany' },
      { text: '船员类型', value: 'crePropertyFeature' },
      { text: '劳动合同签署公司', value: 'crePropertyType' },
      { text: '已公休天数', value: 'onHolidays' },
    ]
  },
  data() {
    return {
      dialog: false,
      formData: {},
      selected: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async confirm() {
      if (this.selected.inSpeciallist == 1) {
        if (
          !(await this.$dialog.msgbox.confirm('该船员在警告名单中，是否添加'))
        )
          return
      }
      if (this.selected.inSpeciallist == 2) {
        if (
          !(await this.$dialog.msgbox.confirm('该船员在慎用名单中，是否添加'))
        )
          return
      }
      console.log('this.selected.onHolidays', this.selected.onHolidays)
      console.log('this.selected.maxHolidays', this.selected.maxHolidays)

      // if (this.selected.onHolidays != this.selected.maxHolidays) {
      //   //不是最大天数type为0，是最大天数type为1
      //   this.selected.type = 0
      // } else {
      //   this.selected.type = 1
      // }
      console.log('最大天数', this.selected.maxHolidays)
      console.log('已休天数', this.selected.onHolidays)

      this.$emit('change', false)
      this.$emit('success', this.selected)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
