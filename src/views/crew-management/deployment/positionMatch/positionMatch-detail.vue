<template>
  <v-container fluid>
    <v-detail-view
      :title="title"
      :tooltip="detailInfo.bizCode"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['岗位匹配管理:编辑']"
    >
      <template v-slot:titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
          v-permission="['岗位匹配管理:返回列表']"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
      </template>
      <template v-slot:岗位匹配基本信息>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  label="船舶名称"
                  outlined
                  dense
                  v-model="detailInfo.shipName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="上船地点"
                  dense
                  outlined
                  v-model="detailInfo.upBoardPlace"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="上船时间"
                  dense
                  outlined
                  v-model="detailInfo.upBoardDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="操作人"
                  dense
                  outlined
                  v-model="detailInfo.operator"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="初始化时间"
                  dense
                  outlined
                  v-model="detailInfo.startTime"
                ></vs-date-picker>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-textarea
                  dense
                  outlined
                  label="航线"
                  v-model="detailInfo.shipRoute"
                  rows="3"
                ></v-textarea>
              </v-col>
              <v-col>
                <v-textarea
                  dense
                  outlined
                  label="备注"
                  v-model="detailInfo.mark"
                  rows="3"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:船员匹配详情>
        <v-divider></v-divider>
        <v-data-table
          show-select
          :headers="headers"
          :items="detailInfo.detailList"
          single-select
          single-expand
          show-expand
          :expanded.sync="expanded"
          dense
          item-key="id"
          class="use-divider"
          @click:row="selectRow"
          v-model="selected"
          :items-per-page="5"
        >
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length">
              <position-crew-match
                ref="match"
                :items="item.userList"
                :crewsNumber="headers.length"
                :shipCode="detailInfo.shipCode"
                :position="item.postName"
                :isLoading="loading"
                :specUserList="item.specUserList"
                :bizCode="detailInfo.bizCode"
                @update="update"
                @successtwo="successtwo"
                @changeLoading="changeLoading"
                @loadingChange="loadingChange"
              ></position-crew-match>
            </td>
          </template>
        </v-data-table>
      </template>
      <template v-slot:船员匹配详情按钮>
        <v-btn
          v-if="detailInfo.status !== 2"
          :disabled="crewSelectedList.length < 2"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="opneInterview"
          :loading="loading"
        >
          批量发起面试
        </v-btn>
        <v-btn
          v-if="detailInfo.status !== 2"
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="!selected.length"
          @click="openDialogOne"
          :loading="sloading"
          v-permission="['船员匹配详情:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          v-if="detailInfo.status !== 2"
          :disabled="crewSelectedList.length !== 1"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船员匹配详情:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <crew-position-up
      v-model="dialogone"
      @change="change"
      @success="success"
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            clearable
            v-model="searchRemain.name"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="4">
          <v-text-field
            label="身份证号"
            outlined
            dense
            clearable
            v-model="searchRemain.idCard"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="船员职务"
            clearable
            v-model="searchRemain.position"
          ></v-ship-station>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            v-model="searchRemain.status"
            label="船员状态"
            :items="[
              { text: '公休', value: 2 },
              { text: '新船员', value: 1 },
            ]"
          ></v-select>
        </v-col>
        <!--        <v-col cols="12" md="2">-->
        <!--          <v-select-->
        <!--            label="船员管理公司"-->
        <!--            outlined-->
        <!--            dense-->
        <!--            required-->
        <!--            :items="newInfo"-->
        <!--            v-model="searchRemain.creCompany"-->
        <!--            @change="getCreThird"-->
        <!--            clearable-->
        <!--          ></v-select>-->
        <!--        </v-col>-->
        <!--        <v-col cols="12" md="2">-->
        <!--          <v-select-->
        <!--            label="船员性质"-->
        <!--            outlined-->
        <!--            dense-->
        <!--            required-->
        <!--            :items="secondInfo"-->
        <!--            v-model="searchRemain.creFeature"-->
        <!--            @change="getCreThird"-->
        <!--            clearable-->
        <!--          ></v-select>-->
        <!--        </v-col>-->
        <!--        <v-col cols="12" md="2">-->
        <!--          <v-select-->
        <!--            outlined-->
        <!--            dense-->
        <!--            label="劳动合同签署公司"-->
        <!--            required-->
        <!--            :items="creThird"-->
        <!--            v-model="searchRemain.creContract"-->
        <!--            clearable-->
        <!--          ></v-select>-->
        <!--        </v-col>-->
      </template>
    </crew-position-up>
    <interview-dialog-info
      v-model="dialogTwo"
      :initialData="initialDataBatch"
      @success="batchInterView"
      @loadingChange="loadingChange"
    ></interview-dialog-info>
  </v-container>
</template>
<script>
import crewPositionUp from './crew-position-up.vue'
import routerControl from '@/mixin/routerControl'
import PositionCrewMatch from './position-crew-match.vue'
import InterviewDialogInfo from './interview-dialog-info.vue'
export default {
  components: {
    crewPositionUp,
    PositionCrewMatch,
    InterviewDialogInfo,
  },
  mixins: [routerControl],
  name: 'positionMatch-detail',
  created() {
    this.backRouteName = 'positionMatch-management'
    this.subtitles = ['岗位匹配基本信息', '船员匹配详情']
    this.headers = [
      { text: '岗位名称', value: 'postName' },
      { text: '	岗位需求数量', value: 'requireNum' },
      { text: '船员报名数量', value: 'EnrollmentNum' },
      { text: '', value: 'data-table-expand' },
    ]
  },
  data() {
    return {
      title: '匹配新增',
      detailInfo: {},
      items: [],
      selected: [],
      expanded: [],
      // creThird: [],
      // secondInfo: [],
      // newInfo: {},
      crewSelected: false,
      crewSelectedList: [],
      dialogone: false,
      dialogTwo: false,
      initialData: {},
      initialDataBatch: {},
      initialDataTwo: {},
      searchRemain: {
        name: '',
        idCard: '',
        position: '',
        status: 2,
      },
      detailList: {},
      loading: false,
      sloading: false,
    }
  },
  watch: {
    selected(val) {
      if (val.length) {
        this.searchRemain.position = val[0].postName
      }
    },
  },
  methods: {
    // async getCreFirst() {
    //   const { errorRaw, data } = await this.getAsync(
    //     `/business/crew/crewProperty/firstProperty/list`,
    //   )
    //   if (errorRaw) {
    //     return
    //   }
    //   this.newInfo = data
    // },
    // async getCreSecond() {
    //   const { errorRaw, data } = await this.getAsync(
    //     `/business/crew/crewProperty/secondProperty/list`,
    //   )
    //   if (errorRaw) {
    //     return
    //   }
    //   this.secondInfo = data
    // },
    // async getCreThird() {
    //   const { errorRaw, data } = await this.getAsync(
    //     `/business/crew/crewProperty/list`,
    //     {
    //       creCompany: this.searchRemain.creCompany,
    //       creFeature: this.searchRemain.creFeature,
    //     },
    //   )
    //   if (errorRaw) {
    //     return
    //   }
    //   console.log(this.creThird)
    //   this.creThird = data.records.map((val) => {
    //     return { text: val.creType, value: val.id }
    //   })
    //   // this.creThirdAll = data.records
    // },
    selectRow(item) {
      this.selected = [item]
    },
    openDialogOne() {
      this.sloading = true
      this.dialogone = true
    },
    opneInterview() {
      console.log('this.detailInfo.detailList', this.detailInfo.detailList)
      let CrewArray = this.detailInfo.detailList.find((ele) =>
        ele.userList.some((val) => val.id === this.crewSelectedList[0].id),
      )
      const hasInterviewer = this.crewSelectedList.find(
        (ele) => ele.status === 1,
      )

      const allSamePosition = this.crewSelectedList.every(
        (crew, i, arr) => i === 0 || crew.position === arr[i - 1].position,
      )
      console.log('crewSelectedList', this.crewSelectedList)

      // 如果所有元素的 position 不相同，就显示提示信息并返回
      if (!allSamePosition) {
        this.$dialog.message.error('所有选中的船员职位需要相同，请重新勾选')
        return
      }

      if (hasInterviewer) {
        this.$dialog.message.error('已有船员进入面试， 请勿重复发起')
        return
      }

      this.loading = true
      this.initialDataBatch = {}
      console.log('detailInfo', this.detailInfo)
      // // 查找detailList中postName与crewSelectedList中的position相同的元素
      // let matchingDetail = this.detailInfo.detailList.find(
      //   (ele) => ele.postName === this.crewSelectedList[0].position,
      // )

      // if (matchingDetail && matchingDetail.specUserList) {
      //   // 如果specUserList中存在crewSelectedList中任何元素的userId
      //   // 则设置this.initialDataBatch.userIdExist为true
      //   this.initialDataBatch.userIdExist = this.crewSelectedList.some((crew) =>
      //     matchingDetail.specUserList.includes(crew.userId),
      //   )
      // } else {
      //   // 如果没有匹配的详情或specUserList，则设置为false
      //   this.initialDataBatch.userIdExist = false
      // }

      const positionMatch = this.detailInfo.detailList.find(
        (ele) => ele.postName === this.crewSelectedList[0].position,
      )
      if (positionMatch && positionMatch.specUserList.includes('true')) {
        this.initialDataBatch.userIdExist = true
      } else {
        this.initialDataBatch.userIdExist = false
      }
      this.initialDataBatch.isBatch = true
      this.initialDataBatch.shipCode = this.detailInfo.shipCode
      this.initialDataBatch.position = CrewArray.postName
      this.dialogTwo = true
    },
    async batchInterView(val) {
      let CrewArray = this.detailInfo.detailList.find((ele) =>
        ele.userList.some((val) => val.id === this.crewSelectedList[0].id),
      )
      const batchInterviewers = this.crewSelectedList.map((ele) => {
        return {
          ...val,
          crewUserId: ele.userId,
          deployMatchDetailId: CrewArray.id,
          parentId: CrewArray.parentId,
          post: CrewArray.postName,
        }
      })
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/postMatch/interview/batchSubmit`,
        { records: batchInterviewers },
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.crewSelectedList = []
      this.$refs.match.selected = []
      this.$dialog.message.success('面试提交成功')
      this.loading = false
      await this.getDetailInfo()
    },
    change(val) {
      this.dialogone = val
      this.sloading = false
    },
    update(val) {
      this.crewSelectedList = val
    },
    async success(val) {
      let matchDetail = {
        matchedCrew: [],
        specUserList: [],
        ...this.selected[0],
      }
      console.log('判断是否为最大公休天数的人', val.maxHolidays)
      //不是最大天数type为0，是最大天数type为1
      if (val.maxHolidays == true) {
        matchDetail.specUserList.push(val.maxHolidays)
      } else {
        matchDetail.specUserList.push(false)
      }
      this.selected[0].userList.forEach((element) => {
        matchDetail.matchedCrew.push(element.userId)
      })
      let flag = matchDetail.matchedCrew.some((ele) => {
        return ele == val.userId
      })
      if (!flag) {
        matchDetail.matchedCrew.push(val.userId)
      } else {
        this.$dialog.message.error('该船员已在面试清单中')
        return
      }
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/postMatch/detail/update`,
        matchDetail,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('添加船员成功！')
      this.sloading = false
      this.selected = []
      await this.getDetailInfo()
    },
    // 发起面试单人
    async successtwo(errorRaw) {
      // console.log('val', val)
      // let CrewArray = this.detailInfo.detailList.find((ele) =>
      //   ele.userList.some((val) => val.id === this.crewSelectedList[0].id),
      // )
      // let updata = {
      //   ...val,
      //   crewUserId: this.crewSelectedList[0].userId,
      //   deployMatchDetailId: CrewArray.id,
      //   parentId: CrewArray.parentId,
      //   post: CrewArray.postName,
      // }
      // console.log('updata', updata)
      // const { errorRaw } = await this.postAsync(
      //   `/business/crew/deploy/postMatch/interview/submit`,
      //   updata,
      // )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.crewSelectedList = []
      this.$refs.match.selected = []
      this.loading = false
      this.$dialog.message.success('面试提交成功')
      await this.getDetailInfo()
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/deploy/postMatch/oneToN/${this.$route.params.id}`,
      )
      if (errorRaw) {
        return
      }
      this.title = `${data.shipName}-${data.upBoardPlace}-岗位匹配详情-${data.bizCode}`
      this.detailInfo = data
      this.detailInfo.detailList = this.detailInfo.detailList.map((ele) =>
        Object.defineProperty(ele, 'EnrollmentNum', {
          value: ele.userList.length,
          writable: true,
        }),
      )
      this.detailInfo.detailList.forEach((ele) => {
        ele.userList.forEach((val) => {
          Object.defineProperties(val, {
            id: {
              value: `${val.userId}${ele.id}`,
              writable: true,
            },
            position: {
              value: ele.postName,
              writable: true,
            },
            parentId: {
              value: ele.parentId,
              writable: true,
            },
            deployMatchDetailId: {
              value: ele.id,
              writable: true,
            },
          })
        })
      })
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return

      const newCrewArray = this.detailInfo.detailList.find((ele) =>
        ele.userList.some((val) => val.id === this.crewSelectedList[0].id),
      )
      const arrLength = newCrewArray.userList.length
      const deleteArray = newCrewArray.userList.filter((ele) => {
        if (ele.id !== this.crewSelectedList[0].id) {
          return true
        } else {
          if (this.crewSelectedList[0].status === 1) return true
        }
      })
      if (arrLength === deleteArray.length) {
        this.$dialog.message.error('该船员已进入面试，删除失败')
        return
      }
      newCrewArray.matchedCrew = deleteArray.map((ele) => ele.userId)
      let matchDetail = {
        id: this.detailInfo.id,
        ...newCrewArray,
      }
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/postMatch/detail/update`,
        matchDetail,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.crewSelectedList = []
      this.$refs.match.selected = []
      await this.getDetailInfo()
    },

    loadingChange() {
      this.loading = false
    },
    changeLoading(val) {
      this.loading = val
    },
    save() {},
  },

  async mounted() {
    await this.getDetailInfo()
    // await this.getCreFirst()
    // await this.getCreThird()
    // await this.getCreSecond()
  },
}
</script>

<style></style>
