<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select v-model="searchRemain.shipCode"></v-ship-select>
        </v-col>
        <!-- <v-col cols="12" md="2">
          <v-select
            label="航线"
            outlined
            clearable
            dense
            :items="routesArray"
            v-model="searchRemain.shipRoute"
          ></v-select>
        </v-col> -->
        <v-col cols="12" md="2">
          <v-text-field
            label="业务编码"
            dense
            outlined
            v-model="searchRemain.bizCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="上船地点"
            dense
            outlined
            v-model="searchRemain.upBoardPlace"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="在船职务"
            v-model="searchRemain.post"
            clearable
          ></v-ship-station>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="匹配状态"
            outlined
            dense
            clearable
            :items="[
              { text: '匹配中', value: 1 },
              { text: '匹配完成', value: 2 },
            ]"
            v-model="searchRemain.status"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          dense
          tile
          @click="statusChange"
          class="mx-1"
          :disabled="!selected"
          color="success"
          v-permission="['岗位匹配管理:匹配完成']"
        >
          <v-icon>mdi-file-arrow-left-right</v-icon>
          匹配完成
        </v-btn>
        <v-btn
          outlined
          dense
          class="mx-1"
          tile
          @click="changeDialog"
          v-permission="['岗位匹配管理:根据最近交班上下船计划初始化']"
        >
          <v-icon left>mdi-card-text-outline</v-icon>
          根据最近交班上下船计划初始化
        </v-btn>
      </template>
      <template v-slot:[`item.shipCode`]="{ item }">
        {{ getShipLabel(item.shipCode) }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 1" color="" small dark>匹配中</v-chip>
        <v-chip v-else-if="item.status === 2" color="success" small dark>
          匹配完成
        </v-chip>
      </template>
      <template v-slot:[`item.shipRoute`]="{ item }">
        {{ getLabel(item.shipRoute) }}
      </template>
    </v-table-searchable>
    <near-position
      v-model="dialog"
      :shipRoutes="shipRoutes"
      table-name="近期上船计划"
      @success="success"
    ></near-position>
  </v-container>
</template>
<script>
import { cacheGetDefault } from '@/util/cache'
import dictHelper from '@/mixin/dictHelper'
import nearPosition from '../positionSignUp/near-position.vue'
export default {
  components: { nearPosition },
  name: 'positionMatch-management',
  mixins: [dictHelper],
  created() {
    this.tableName = '岗位匹配管理'
    this.reqUrl = '/business/crew/deploy/postMatch/page'
    this.headers = [
      { text: '业务编码', value: 'bizCode' },
      { text: '船舶名称', value: 'shipCode' },
      { text: '上船时间', value: 'upBoardDate' },
      { text: '上船地点', value: 'upBoardPlace' },
      { text: '初始化时间', value: 'startTime' },
      { text: '操作人', value: 'operator' },
      { text: '匹配状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      interval: true,
      label: '上船起末时间',
    }
    this.pushParams = {
      name: 'positionMatch-detail',
    }
  },

  data() {
    return {
      dialog: false,
      selected: false,
      searchRemain: {
        status: 1, // 这里设置默认值为1，即“匹配中”
      },
      shipRoutes: [],
      routesArray: [],
      shipName: [],
    }
  },

  methods: {
    changeDialog() {
      this.dialog = true
    },
    async statusChange() {
      if (
        !(await this.$dialog.msgbox.confirm(
          '请确认：该船舶所有的岗位都已匹配完成并发往面试。（注：该操作无法撤销）',
        ))
      )
        return
      if (this.selected.status === 2) {
        this.$dialog.message.error('该记录已匹配完成，请问重复操作')
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/postMatch/changeStatus`,
        { id: this.selected.id, status: 2 },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交成功')
      await this.$refs.table.loadTableData()
    },
    async success() {
      await this.$refs.table.loadTableData()
    },
    routeArrayGet() {
      this.routesArray = this.shipRoutes.map((val) => val?.dictLabel)
    },
    getLabel(value) {
      return this.shipRoutes.find((item) => item?.dictValue === value)
        ?.dictLabel
    },
    getShipLabel(val) {
      return this.shipName.find((item) => item?.shipCode === val)?.chShipName
    },
    async getShipName() {
      let that = this
      const data = await cacheGetDefault('ship-list', async () => {
        const { data, errorRaw } = await that.getAsync(
          '/business/common/ship/simpleInfo/list',
          {},
          false,
        )
        if (errorRaw) {
          that.$dialog.message.error('船舶列表获取失败，请重试')
          return null
        }
        if (data.length === 0) {
          that.$dialog.message.error('船舶列表为空，部分功能受损')
        }
        return data
      })
      this.shipName = data
    },
  },

  async mounted() {
    this.shipRoutes = await this.getDictByType('sea_way_type')
    this.routeArrayGet()
    await this.getShipName()
  },
}
</script>

<style></style>
