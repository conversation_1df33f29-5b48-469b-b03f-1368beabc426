<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        面试船员信息填写
        <v-spacer></v-spacer>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col>
                <v-textarea
                  label="面试内容"
                  outlined
                  dense
                  v-model="formData.content"
                ></v-textarea>
              </v-col>
              <v-col>
                <v-textarea
                  label="备注"
                  outlined
                  dense
                  :rules="!formData.userIdExist ? [specialRules.required] : []"
                  v-model="formData.remark"
                ></v-textarea>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="4">
                <vs-date-picker
                  dense
                  label="面试时间"
                  outlined
                  :rules="[rules.required]"
                  v-model="formData.happenTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="4">
                <v-select
                  v-model="formData.onSite"
                  label="是否线下面试"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :items="[
                    { text: '在线审批面试', value: 0 },
                    { text: '现场面试', value: 1 },
                  ]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="4">
                <v-autocomplete
                  label="面试官"
                  v-model="formData.interviewer"
                  dense
                  :rules="[rules.required]"
                  :items="interviewList"
                  outlined
                ></v-autocomplete>
              </v-col>
            </v-row>
            <v-attach-list @change="changeAttachment"></v-attach-list>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" @click="confirm">确定</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'interview-dialog-info',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      attachmentRecords: [],
      formData: {
        onSite: 0,
      },
      interviewList: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      specialRules: {
        required: (v) => !!v || v === 0 || '请在此处填写选择船员原因',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      // if (this.initialData.userIdExist === false) {
      //   this.formData.remark = '请在此处填写选择船员原因。'
      // }
      if (val) {
        this.getinterviewerList()
      }
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  mounted() {},

  methods: {
    closeForm() {
      this.$emit('change', false)
      this.$emit('loadingChange', false)
    },
    changeAttachment(attachmentIds) {
      console.log(attachmentIds)
      this.formData.attachmentIds = attachmentIds
      console.log(this.formData)
    },
    async confirm() {
      console.log(this.formData)
      if (!this.$refs.form.validate()) {
        return
      }
      this.$emit('change', false)
      if (!this.initialData.isBatch) {
        const data = {
          ...this.formData,
          bizCode: this.initialData.bizCode,
          crewUserId: this.initialData.crewUserId,
          deployMatchDetailId: this.initialData.deployMatchDetailId,
          parentId: this.initialData.parentId,
          post: this.initialData.position,
        }
        const { errorRaw } = await this.postAsync(
          `/business/crew/deploy/postMatch/interview/submit`,
          data,
        )
        this.$emit('successtwo', errorRaw)
      } else {
        this.$emit('success', this.formData)
      }
    },
    async getinterviewerList() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/deploy/postMatch/interview/interviewer`,
        {
          shipCode: this.initialData.shipCode,
          position: this.initialData.position,
        },
      )
      if (errorRaw) {
        return
      }
      this.interviewList = [data]
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
