<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title></v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-table-searchable
              ref="table"
              table-name="船东工资标准选择"
              v-model="selected"
              :headers="headers"
              :searchRemain="searchRemain"
              :req-url="reqUrl"
              outlined
            >
              <!-- <template #searchflieds>
                <v-col cols="12" md="4">
                  <v-ship-select v-model="formData.shipCode"></v-ship-select>
                </v-col>
              </template> -->
            </v-table-searchable>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
// import vShipSelect from '@/components/v-ship-select.vue'
export default {
  // components: { vShipSelect },
  name: 'v-dialig-shipowner-wage',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/crew/salaryStandard/page'
    this.headers = [
      { text: '工资标准编码', value: 'code' },
      { text: '工资标准名称', value: 'name' },
      { text: '创建时间', value: 'createTime' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      searchRemain: {
        type: 3,
      },
      selected: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      this.$emit('change', false)
      this.$emit('success', this.selected.id)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
