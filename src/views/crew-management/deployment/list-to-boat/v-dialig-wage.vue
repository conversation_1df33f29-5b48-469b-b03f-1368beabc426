<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title></v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-table-searchable
              ref="table"
              table-name="工资标准选择"
              v-model="selected"
              :headers="headers"
              :search-remain="formData"
              :req-url="reqUrl"
              outlined
            >
              <template #searchflieds>
                <v-col cols="12" md="4">
                  <v-ship-select
                    disabled
                    v-model="formData.shipCode"
                  ></v-ship-select>
                </v-col>
                <v-col cols="12" md="4">
                  <vs-date-picker
                    label="计划上船时间"
                    outlined
                    dense
                    v-model="formData.planTime"
                    disabled
                  ></vs-date-picker>
                </v-col>
              </template>
              <template v-slot:[`item.oaStatus`]="{ item }">
                <v-chip small color="" dark v-if="item.oaStatus === 1">
                  草稿
                </v-chip>
                <v-chip
                  small
                  color="success"
                  dark
                  v-else-if="item.oaStatus === 2"
                >
                  生效中
                </v-chip>
                <v-chip small color="error" dark v-else>已失效</v-chip>
              </template>
            </v-table-searchable>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import vShipSelect from '@/components/v-ship-select.vue'
export default {
  components: { vShipSelect },
  name: 'v-dialig-wage',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/crew/salaryStandard/page'
    this.headers = [
      { text: '工资标准编码', value: 'code' },
      { text: '工资标准名称', value: 'name' },
      // { text: '船舶名称', value: 'shipName' },
      { text: '工资标准生效日期', value: 'startDate' },
      { text: '工资标准失效日期', value: 'expireDate' },
      { text: '工资标准状态', value: 'oaStatus' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      selected: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      console.log(this.formData.planTime < this.selected.startDate)
      if (
        this.formData.planTime < this.selected.startDate ||
        this.formData.planTime > this.selected.expireDate
      ) {
        this.$dialog.message.error('计划上船时间不在工资标准日期范围内')
        return
      } else {
        this.$emit('change', false)
        this.$emit('success', this.selected.id)
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
