<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['待上船船员名单:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      :canSubmit="canSubmit"
      :canSave="canSave"
    >
      <template #topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-audit
            v-if="approvalInfo.auditParams"
            ref="audit"
            :auditParams="approvalInfo.auditParams"
          ></v-audit>
        </v-card-text>
      </template>
      <template #titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
          v-permission="['待上船船员名单信息维护:返回列表']"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <!-- <v-btn
          width="90"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          :loading="loading"
          v-permission="['待上船船员名单信息维护:基本信息保存']"
        >
          基本信息保存
        </v-btn> -->
        <v-btn
          v-if="approvalInfo.content.length !== 0"
          width="80"
          tile
          @click="submit"
          color="success"
          small
          class="mx-1"
          :loading="sloading"
          v-permission="['待上船船员名单信息维护:提交审批']"
        >
          提交审批
        </v-btn>
      </template>
      <template v-slot:待上船船员名单基本信息>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                outlined
                dense
                label="上船港口"
                v-model="detailInfo.port"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                outlined
                dense
                v-model="detailInfo.route"
                label="航线"
                :items="routesArray"
              ></v-select>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                label="计划上船时间"
                outlined
                dense
                v-model="detailInfo.planTime"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="代理的联系方式"
                outlined
                dense
                v-model="detailInfo.proxyTel"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                outlined
                dense
                label="业务部门电话"
                v-model="detailInfo.bizDeptTel"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                outlined
                dense
                label="船上联系方式"
                v-model="detailInfo.shipTel"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="确认状态"
                outlined
                dense
                readonly
                :items="[
                  { text: '暂未同步', value: 0 },
                  { text: '已同步', value: 1 },
                ]"
                v-model="detailInfo.submitStatus"
              ></v-select>
            </v-col>
          </v-row>
          <v-row>
            <v-textarea
              outlined
              dense
              v-model="detailInfo.remark"
              label="备注"
            ></v-textarea>
          </v-row>
        </v-card-text>
      </template>
      <template #实际工资变动船员名单>
        <v-table-list :headers="apporvalHeaders" :items="approvalInfo.content">
          <template v-slot:[`item.salaryStatus`]="{ item }">
            <v-chip v-if="item.salaryStatus === 2" color="warning" small dark>
              工资变动未超出浮动
            </v-chip>
            <v-chip
              v-else-if="item.salaryStatus === 3"
              color="error"
              small
              dark
            >
              工资变动超出浮动
            </v-chip>
          </template>
        </v-table-list>
      </template>
      <template v-slot:待上船船员名单>
        <v-divider></v-divider>
        <v-data-table
          :items="detailInfo.records"
          v-model="selected"
          outlined
          show-select
          dense
          single-select
          :headers="headers"
          class="use-divider"
          disable-pagination
          hide-default-footer
          @click:row="selectRow"
          @dblclick:row="dbclick"
        ></v-data-table>
      </template>
      <template v-slot:待上船船员名单按钮>
        <v-btn
          outlined
          dense
          tile
          color="error"
          class="mx-1"
          :disabled="selected.length !== 1"
          @click="isShow"
          v-permission="['待上船船员名单:调配失败']"
        >
          <v-icon left>mdi-account-multiple-remove</v-icon>
          调配失败
        </v-btn>
        <!-- <v-btn
          v-if="!approvalInfo.auditParams"
          outlined
          tile
          color="#E65100"
          @click="updatbStandard"
          v-permission="['待上船船员名单:初始化船东标准工资']"
        >
          <v-icon left>mdi-pencil</v-icon>
          初始化船东标准工资
        </v-btn> -->
        <v-btn
          v-if="!approvalInfo.auditParams || approvalInfo.auditParams.isReject"
          outlined
          tile
          color="#E65100"
          @click="updataStandard"
          v-permission="['待上船船员名单:初始化标准工资']"
        >
          <v-icon left>mdi-pencil</v-icon>
          初始化标准工资
        </v-btn>
        <v-btn
          v-if="!approvalInfo.auditParams || approvalInfo.auditParams.isReject"
          :disabled="selected.length === 0"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updataTrue"
          v-permission="['待上船船员名单:编辑船员实际工资']"
        >
          <v-icon left>mdi-pencil</v-icon>
          编辑船员实际工资
        </v-btn>
        <v-btn
          :disabled="selected.length === 0"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['待上船船员名单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <v-dialog-truewage
        :initialData="initialData"
        v-model="dialog"
        @success="success"
      ></v-dialog-truewage>
      <v-dialig-shipowner
        v-model="dialogFive"
        @success="InitSuccess"
        :initialData="shipNameSelect"
      ></v-dialig-shipowner>
      <v-dialig-wage
        v-model="dialogTwo"
        @success="InitSuccess"
        :initialData="shipNameSelect"
      ></v-dialig-wage>
    </v-detail-view>
    <fail-reason-dialog
      :initialData="selected[0]"
      v-model="dialogThree"
    ></fail-reason-dialog>
    <v-crew-card v-model="dialogFour" :initialData="crewInfo"></v-crew-card>
  </v-container>
</template>
<script>
let time = null // 双击

import dictHelper from '@/mixin/dictHelper'
import vDialogTruewage from './v-dialog-truewage.vue'
import routerControl from '@/mixin/routerControl'
import VDialigWage from './v-dialig-wage.vue'
import VDialigShipowner from './v-dialig-shipowner-wage.vue'
import FailReasonDialog from './fail-reason-dialog.vue'
import VCrewCard from '../../private/v-crew-card.vue'
export default {
  components: {
    vDialogTruewage,
    VDialigWage,
    FailReasonDialog,
    VCrewCard,
    VDialigShipowner,
  },
  mixins: [dictHelper, routerControl],
  name: 'list-boat-detail',
  created() {
    this.backRouteName = 'list-boat-management'
    this.subtitles = [
      '待上船船员名单基本信息',
      '实际工资变动船员名单',
      '待上船船员名单',
    ]
    this.headers = [
      { text: '待上船船员姓名', value: 'crewUserName' },
      { text: '船员身份证号码', value: 'crewIdNo' },
      { text: '换班岗位', value: 'position' },
      { text: '标准工资', value: 'standardSalary' },
      { text: '实际工资', value: 'actualSalary' },
    ]
    this.apporvalHeaders = [
      { text: '待上船船员姓名', value: 'crewUserName' },
      { text: '船员身份证号码', value: 'crewIdNo' },
      { text: '船员联系方式', value: 'crewPhoneNum' },
      { text: '换班岗位', value: 'position' },
      { text: '标准工资', value: 'standardSalary' },
      { text: '实际工资', value: 'actualSalary' },
      { text: '实际工资状态', value: 'salaryStatus' },
    ]
  },
  data() {
    return {
      title: '待上船船员名单信息维护',
      detailInfo: {},
      initialData: {},
      shipNameSelect: {},
      selected: [],
      dialog: false,
      dialogTwo: false,
      dialogThree: false,
      dialogFour: false,
      dialogFive: false,
      shipRoutes: [],
      routesArray: [],
      isTrueWage: false,
      approvalInfo: {
        content: [],
      },
      loading: false,
      sloading: false,
      crewInfo: {},
    }
  },

  methods: {
    canSubmit() {
      return this.detailInfo?.status == 0 && this.detailInfo?.status == 1
    },
    canSave() {
      return this.detailInfo?.status == 0
    },
    async save() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/upAndDown/prepare/editBaseInfo`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('基本信息编辑成功')
      this.closeAndTo(this.backRouteName, {})
    },
    async saveNotClose() {
      //初始化标准工资时，提前保存主表字段
      const { errorRaw } = await this.postAsync(
        `/business/crew/upAndDown/prepare/editBaseInfo`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
    },
    async success() {
      this.getDetailInfo()
      this.getApprovalInfo()
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/upAndDown/prepare/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.title = `${data.bizCode}-待上船船员名单信息维护`
      this.detailInfo = data
    },
    routeArrayGet() {
      this.routesArray = this.shipRoutes.map((val) => {
        return { text: val.dictLabel, value: val.dictValue }
      })
    },
    updataTrue() {
      this.initialData = this.selected[0]
      this.dialog = true
      this.isTrueWage = true
    },
    updataStandard() {
      this.shipNameSelect = {
        shipCode: this.detailInfo.shipCode,
        planTime: this.detailInfo.planTime,
      }
      if (!this.detailInfo.planTime) {
        this.$dialog.message.error('请先填写计划上船时间')
        return
      }
      this.dialogTwo = true
    },
    updatbStandard() {
      // this.shipNameSelect = { shipCode: this.detailInfo.shipCode }
      this.dialogFive = true
    },
    async check() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/upAndDown/prepare/approval/check`,
        {
          bizCode: this.detailInfo.bizCode,
        },
      )
      if (errorRaw) {
        this.$dialog.message.error('提交失败')
        return false
      }
      console.log('data', data)
      if (data.length > 0) {
        this.$dialog.message.error(`不可提交, ${data}为面试中或面试完成`)
        return false
      }
      console.log('1111')
      return true
    },
    async submit() {
      if (!this.approvalInfo.auditParams) {
        if (!(await this.$dialog.msgbox.confirm('是否确认提交审批？'))) return
        this.sloading = true
        const FLAG = this.approvalInfo.content.findIndex(
          (ele) => ele.salaryStatus === 3,
        )
        if (!(await this.check())) {
          this.sloading = false
          return
        }
        const { errorRaw } = await this.getAsync(
          `/business/crew/upAndDown/prepare/approval/submit`,
          {
            id: this.$route.params.id,
            type: FLAG !== -1 ? 3 : 2,
            shipCode: this.detailInfo.shipCode,
            bizCode: this.detailInfo.bizCode,
          },
        )
        if (errorRaw) {
          this.sloading = false
          return
        }
        this.$dialog.message.success('成功提交审批')
      } else {
        const errorRaw = await this.$refs.audit.submit()
        if (errorRaw) {
          this.sloading = false
          return
        }
        this.$dialog.message.success('成功提交审批')
      }
      this.sloading = false
      this.closeAndTo(this.backRouteName, {})
    },
    async InitSuccess(id) {
      this.saveNotClose()
      const { errorRaw } = await this.postAsync(
        `/business/crew/upAndDown/prepare/handleStandard`,
        { id: this.$route.params.id, salaryStandardId: id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('初始化成功！')
      this.getDetailInfo()
    },
    async getApprovalInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/upAndDown/prepare/approval/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.approvalInfo = data
      console.log('approvalInfo', this.approvalInfo)
    },
    selectRow(_, { isSelected, item }) {
      clearTimeout(time)
      time = setTimeout(() => {
        this.selected = isSelected ? [] : [item]
      }, 150)
    },
    dbclick(_, { item }) {
      clearTimeout(time)
      this.selected = [item]
      this.crewInfo = { userId: this.selected[0].crewUserId }
      this.dialogFour = true
    },
    isShow() {
      this.dialogThree = true
    },
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/upAndDown/prepare/detail/delete`,
        { id: this.selected[0].id },
      )
      if (errorRaw) {
        return
      }
      this.selected = []
      this.$dialog.message.success('删除成功')
      await this.getDetailInfo()
    },
  },

  async mounted() {
    this.shipRoutes = await this.getDictByType('sea_way_type')
    this.routeArrayGet()
    await this.getApprovalInfo()
    await this.getDetailInfo()
  },
}
</script>

<style></style>
