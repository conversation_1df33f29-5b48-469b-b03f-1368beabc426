<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :search-remain="searchRemain"
      :push-params="pushParams"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship="true"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="业务编码"
            outlined
            dense
            clearable
            v-model="searchRemain.bizCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="上船港口"
            dense
            outlined
            clearable
            v-model="searchRemain.port"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="确认状态"
            v-model="searchRemain.submitStatus"
            clearable
            :items="[
              { text: '暂未同步到实际上下船', value: 0 },
              { text: '已同步到实际上下船', value: 1 },
            ]"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="面试船员级别"
            clearable
            v-model="searchRemain.businessType"
            :items="[
              {
                text: '上船前实际工资审批（未超过浮动）',
                value: 'actual_salary_approval1',
              },
              {
                text: '上船前实际工资审批（超过浮动）',
                value: 'actual_salary_approval2',
              },
            ]"
            dense
            outlined
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchRemain.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          @click="openInNewWindow"
          v-permission="['待上船船员名单:导出名单']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          导出名单
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          :disabled="!selected"
          @click="sendInfo"
          v-permission="['待上船船员名单:发送上船信息']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          发送上船信息
        </v-btn>
        <v-btn
          tile
          class="mx-1"
          outlined
          :disabled="!selected"
          color="#004D40"
          @click="upload"
          v-permission="['待上船船员名单:提交到实际交接班']"
        >
          <v-icon left>mdi-arrow-up-bold</v-icon>
          提交到实际交接班
        </v-btn>
      </template>
      <template v-slot:[`item.submitStatus`]="{ item }">
        <v-chip v-if="item.submitStatus === 0" small dark color="">
          暂未同步到实际上下船
        </v-chip>
        <v-chip v-else small dark color="success">已同步到实际上下船</v-chip>
      </template>
      <template v-slot:[`item.readyStatus`]="{ item }">
        <v-chip v-if="item.readyStatus === 0" small dark color="">
          工资无变动可提交
        </v-chip>
        <v-chip v-else-if="item.readyStatus === 1" small color="yellow">
          工资变动审批中
        </v-chip>
        <v-chip v-else-if="item.readyStatus === 2" small dark color="success">
          工资变动审批完毕
        </v-chip>
        <v-chip v-else-if="item.readyStatus === 3" small dark color="#C62828">
          工资变动退回
        </v-chip>
        <v-chip v-else small dark color="error">未知状态</v-chip>
      </template>
      <template v-slot:[`item.route`]="{ item }">
        {{ getroute(item.route) }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'

export default {
  name: 'list-boat-management',
  mixins: [dictHelper],
  created() {
    this.tableName = '待上船船员名单查询'
    this.reqUrl = '/business/crew/upAndDown/prepare/page'
    this.headers = [
      { text: '业务标识编码', value: 'bizCode' },
      { text: '拟上船船舶', value: 'shipName' },
      { text: '上船港口', value: 'port' },
      // { text: '航线', value: 'route' },
      { text: '计划上船时间', value: 'planTime' },
      { text: '代理联系方式', value: 'proxyTel' },
      { text: '业务部门电话', value: 'bizDeptTel' },
      { text: '船上联系方式', value: 'shipTel' },
      { text: '备注', value: 'remark' },
      { text: '审批情况', value: 'readyStatus' },
      { text: '确认状况', value: 'submitStatus' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '计划上船时间',
      interval: true,
    }
    this.pushParams = {
      name: 'list-boat-detail',
    }
  },
  data() {
    return {
      selected: false,
      searchRemain: { submitStatus: 0 },
      shipRoutes: [],
      routesArray: [],
      //FineReport地址
      baseURL:
        'https://jk.sitc.com/webroot/decision/view/report?viewlet=Test%252FADMIN_CENTER%252FonBoard.cpt&',
    }
  },

  methods: {
    async sendInfo() {
      if (!(await this.$dialog.msgbox.confirm('是否发送选中记录？'))) return

      const { errorRaw } = await this.getAsync(
        `/business/crew/upAndDown/prepare/sendMessage`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.selected = false
      this.$dialog.message.success('消息发送成功')
      await this.$refs.table.loadTableData()
    },

    routeArrayGet() {
      this.routesArray = this.shipRoutes.map((val) => val?.dictLabel)
    },
    getroute(item) {
      const obj = this.shipRoutes.find((ele) => ele?.dictValue === item)
      return obj?.dictLabel
    },
    async upload() {
      if (this.selected.submitStatus != 0) {
        this.$dialog.message.error('请选择暂未同步到实际上下船的数据')
      }
      if (this.selected.readyStatus != 0 && this.selected.readyStatus != 2) {
        this.$dialog.message.error('未审批完，请等待审批完后提交')
        return
      }
      if (
        !(await this.$dialog.msgbox.confirm('确定将选中记录提交到实际交接班？'))
      )
        return

      const { errorRaw } = await this.getAsync(
        `/business/crew/upAndDown/prepare/submitToCheckin`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.selected = false
      this.$dialog.message.success('提交成功')
      await this.$refs.table.loadTableData()
    },
    openInNewWindow() {
      let url = this.baseURL + '__bypagesize__=false&'
      let shipcode = this.$refs.table.ship
      url = url + 'shipcode=' + shipcode
      window.open(url, '_blank')
    },
  },

  async mounted() {
    this.shipRoutes = await this.getDictByType('sea_way_type')
    this.routeArrayGet()
  },
}
</script>

<style></style>
