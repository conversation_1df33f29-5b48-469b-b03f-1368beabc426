<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      @dbclick="openCrewInfo"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            clearable
            v-model="searchRemain.crewName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="身份证号码"
            outlined
            dense
            v-model="searchRemain.idNo"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员管理公司"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.creCompany"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员性质"
            outlined
            dense
            required
            :items="secondInfo"
            v-model="searchRemain.creFeature"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="劳动合同签署公司"
            required
            :items="creThird"
            v-model="searchRemain.creContract"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns></template>
      <template v-slot:[`item.crePropertyFeature`]="{ item }">
        {{ item.creProperty && item.creProperty.creFeature }}
      </template>
      <template v-slot:[`item.crePropertyCreCompany`]="{ item }">
        {{ item.creProperty && item.creProperty.creCompany }}
      </template>
      <template v-slot:[`item.crePropertyType`]="{ item }">
        {{ item.creProperty && item.creProperty.creType }}
      </template>
    </v-table-searchable>
    <v-crew-card v-model="dialog" :initialData="initialData"></v-crew-card>
  </v-container>
</template>
<script>
import vCrewCard from '../../private/v-crew-card.vue'
export default {
  components: { vCrewCard },
  name: 'service-period-not-expired',
  created() {
    this.tableName = '未满服务期船员查询'
    this.reqUrl = '/business/crew/upAndDown/notFullServe/page'
    this.headers = [
      { text: '船员姓名', value: 'crewName' },
      { text: '身份证号', value: 'idNo' },
      { text: '船员电话号码', value: 'tel' },
      { text: '船员管理公司', value: 'crePropertyCreCompany' },
      { text: '船员类型', value: 'crePropertyFeature' },
      { text: '在船服务天数', value: 'serveDays' },
      { text: '劳动合同签署公司', value: 'crePropertyType' },
      { text: '上船时间', value: 'onBoardTime' },
      { text: '下船时间', value: 'offBoardTime' },
      { text: '船舶名称', value: 'shipName' },
    ]
    this.fuzzyLabel = ''
    // this.searchDate = {
    //   label: '',
    //   value: '',
    // }
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      initialData: {},
      dialog: false,
      newInfo: {},
      secondInfo: [],
      creThird: [],
    }
  },

  methods: {
    openCrewInfo(val) {
      this.initialData = { userId: val.crewUserId }
      this.dialog = true
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data
    },
    async getCreSecond() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/secondProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.secondInfo = data
    },
    async getCreThird() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/list`,
        {
          creCompany: this.searchRemain.creCompany,
          creFeature: this.searchRemain.creFeature,
        },
      )
      if (errorRaw) {
        return
      }
      this.creThird = []
      console.log(this.creThird)
      this.creThird = data.records.map((val) => {
        return { text: val.creType, value: val.id }
      })
      // this.creThirdAll = data.records
    },
  },

  async mounted() {
    await this.getCreFirst()
    await this.getCreThird()
    await this.getCreSecond()
  },
}
</script>

<style></style>
