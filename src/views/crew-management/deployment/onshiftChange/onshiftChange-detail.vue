<template>
  <v-container fluid>
    <v-detail-view
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['交班上船计划管理:编辑']"
    >
      <template v-slot:titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
          v-permission="['交班上船计划管理:返回列表']"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <v-btn
          v-if="detailInfo.publishFlag !== true"
          width="60"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          v-permission="['交班上船计划管理:保存']"
        >
          保存
        </v-btn>
        <v-btn
          v-if="detailInfo.publishFlag === false && newCard !== `new`"
          width="80"
          tile
          @click="submit"
          color="success"
          small
          class="mx-1"
          :loading="loading"
          v-permission="['交班上船计划管理:保存并提交']"
        >
          保存并提交
        </v-btn>
      </template>
      <template v-slot:交班上船计划详情>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="航线"
                  outlined
                  clearable
                  dense
                  :items="routesArray"
                  v-model="detailInfo.shipRoute"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="上船时间"
                  outlined
                  dense
                  v-model="detailInfo.upBoardDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="上船地点"
                  dense
                  outlined
                  v-model="detailInfo.upBoardPlace"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2" v-if="detailInfo.publisher">
                <v-text-field
                  label="发布人姓名"
                  outlined
                  dense
                  v-model="detailInfo.publisher"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2" v-if="detailInfo.publishFlag">
                <vs-date-picker
                  label="发布时间"
                  outlined
                  dense
                  v-model="detailInfo.publishTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2" v-if="detailInfo.publishFlag">
                <vs-date-picker
                  label="岗位失效时间"
                  outlined
                  dense
                  v-model="detailInfo.expireTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2" v-if="newCard !== `new`">
                <v-select
                  label="计划岗位发布状态"
                  outlined
                  dense
                  readonly
                  :items="[
                    { text: '未发布', value: false },
                    { text: '已发布', value: true },
                  ]"
                  v-model="detailInfo.publishFlag"
                ></v-select>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:交班上船计划岗位需求>
        <v-card-text>
          <v-container fluid>
            <v-form ref="form">
              <v-table-list
                :items="detailInfo.details"
                :headers="headers"
                v-model="selected"
              >
                <!-- <template v-slot:[`item.status`]="{ item }">
                  <v-chip v-if="item.status === 1" color="" dark small>
                    未匹配
                  </v-chip>
                  <v-chip
                    v-else-if="item.status === 2"
                    color="success"
                    dark
                    small
                  >
                    已匹配
                  </v-chip>
                  <v-chip v-else color="blue" dark small>
                    匹配船员成功上船
                  </v-chip>
                </template> -->
                <template v-slot:[`item.requireNum`]="{ item }">
                  <v-text-field
                    dense
                    single-line
                    v-model="item.requireNum"
                    type="number"
                  ></v-text-field>
                </template>
                <template v-slot:[`item.requirement`]="{ item }">
                  <v-text-field
                    dense
                    single-line
                    v-model="item.requirement"
                  ></v-text-field>
                </template>
              </v-table-list>
            </v-form>
          </v-container>
        </v-card-text>
      </template>
      <template v-slot:交班上船计划岗位需求按钮>
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          v-if="!isCanEdit"
          @click="handleClick"
          v-permission="['交班上船计划岗位需求:查看对应的下船计划']"
        >
          <v-icon left>mdi-eye</v-icon>
          查看对应的下船计划
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="openDialog"
          v-if="!isCanEdit"
          v-permission="['交班上船计划岗位需求:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          v-if="!isCanEdit"
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['交班上船计划岗位需求:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <v-dialog-crew-position
      v-model="dialog"
      :initialData="otherSelected"
      @success="success"
      :items="detailInfo.details"
    ></v-dialog-crew-position>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import routerControl from '@/mixin/routerControl'
import vDialogCrewPosition from './v-dialog-crewPosition.vue'
export default {
  components: { vDialogCrewPosition },
  name: 'onshiftChange-detail',
  mixins: [dictHelper, routerControl],
  created() {
    this.backRouteName = 'onshiftChange-management'
    this.subtitles = ['交班上船计划详情', '交班上船计划岗位需求']
    this.headers = [
      { text: '岗位名称', value: 'postName' },
      { text: '岗位需求数量', value: 'requireNum' },
      { text: '岗位要求', value: 'requirement' },
    ]
    this.newCard = this.$route.params.id
    this.getDetailInfo()
  },
  data() {
    return {
      title: '新增上船计划',
      selected: false,
      otherSelected: {},
      dialog: false,
      updata: false,
      detailInfo: {
        publishFlag: false,
      },
      shipRoutes: [],
      routesArray: [],
      loading: false,
      baseURL:
        '/crew-management/crew-deployment-management/crew-shift-change-detail/',
    }
  },
  computed: {
    isCanEdit: function () {
      return this.newCard === `new` || this.detailInfo.publishFlag
    },
  },
  methods: {
    handleClick() {
      let url = this.baseURL + this.detailInfo.offBoardId
      this.$router.push(url)
    },
    openDialog() {
      this.otherSelected = {}
      this.dialog = true
    },
    // editElectronicchart() {
    //   this.otherSelected = this.selected
    //   this.dialog = true
    // },
    async success() {
      await this.getDetailInfo()
    },
    async save(useBack = true) {
      if (!(await this.$dialog.msgbox.confirm('提交信息是否确认无误？'))) return

      const url =
        this.$route.params.id === `new`
          ? `/business/crew/deploy/upBoard/save`
          : `/business/crew/deploy/upBoard/update`
      const { errorRaw } = await this.postAsync(url, this.detailInfo)
      if (errorRaw) return
      if (this.newCard !== `new`) await this.saveDetailsItem()
      if (useBack) this.closeAndTo(this.backRouteName, {})
    },
    async submit() {
      this.loading = true
      await this.save(false)
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/upBoard/post/publish`,
        { id: this.detailInfo.id, publishFlag: true },
      )
      if (errorRaw) {
        this.$dialog.message.error(`岗位状态变更失败`)
        return
      }
      this.loading = false
      this.closeAndTo(this.backRouteName, {})
      this.$dialog.message.success(`岗位状态变更成功`)
    },
    async getDetailInfo() {
      this.shipRoutes = await this.getDictByType('sea_way_type')
      this.routesArray = this.shipRoutes.map((val) => {
        return { text: val?.dictLabel, value: val?.dictValue }
      })
      if (this.$route.params.id !== `new`) {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/deploy/upBoardPlan/getById/${this.$route.params.id}`,
        )

        if (errorRaw) {
          return
        }
        this.detailInfo = data
        this.title = `${data.bizCode}-${data.shipName}-上船计划详情`
      }
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/upBoard/detail/delete`,
        { parentId: this.$route.params.id, detailId: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error('删除失败')
        return
      }
      this.$dialog.message.success('删除成功')
      await this.getDetailInfo()
    },
    async saveDetailsItem() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/upBoard/batch/updateDetail`,
        {
          id: this.$route.params.id,
          details: this.detailInfo.details,
        },
      )
      if (errorRaw) {
        return false
      }
      return true
    },
  },

  mounted() {},
}
</script>

<style></style>
