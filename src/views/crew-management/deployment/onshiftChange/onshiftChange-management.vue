<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :searchRemain="searchRemain"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select
            outlined
            clearable
            dense
            v-model="searchRemain.shipCode"
          ></v-ship-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="上船地点"
            outlined
            clearable
            dense
            v-model="searchRemain.upBoardPlace"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="航线"
            outlined
            clearable
            dense
            :items="routesArray"
            v-model="searchRemain.shipRoute"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="计划岗位发布状态"
            outlined
            clearable
            dense
            :items="[
              { text: '未发布', value: 0 },
              { text: '已发布', value: 1 },
            ]"
            v-model="searchRemain.publishFlag"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="blue"
          class="mx-1"
          :disabled="!selected"
          @click="viewProgress"
          v-permission="['交班上船计划管理:发布选择计划']"
        >
          <v-icon left>mdi-calendar-blank-outline</v-icon>
          查看调配进展
        </v-btn>
        <v-btn
          outlined
          tile
          color="blue"
          class="mx-1"
          :disabled="!selected"
          @click="pushWechat"
          v-permission="['交班上船计划管理:企微推送']"
        >
          <v-icon left>mdi-email-arrow-right-outline</v-icon>
          企微推送岗位信息
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="#29B6F6"
          class="mx-1"
          @click="dialog = true"
          v-permission="['交班上船计划管理:编辑推送信息']"
        >
          编辑推送信息
        </v-btn>
        <v-btn
          outlined
          tile
          color="blue"
          class="mx-1"
          :disabled="!selected"
          @click="pushInfo"
          v-permission="['交班上船计划管理:推送岗位信息']"
        >
          <v-icon left>mdi-email-arrow-right-outline</v-icon>
          推送岗位信息
        </v-btn> -->

        <v-btn
          outlined
          tile
          color="blue"
          class="mx-1"
          :disabled="!selected"
          @click="statusChange"
          v-permission="['交班上船计划管理:发布选择计划']"
        >
          <v-icon left>mdi-cursor-pointer</v-icon>
          发布选择计划
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-deployment-management/crew-onshift-change-detail/new"
          v-permission="['交班上船计划管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['交班上船计划管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipRoute`]="{ item }">
        {{ getLabel(item.shipRoute) }}
      </template>
      <template v-slot:[`item.publishFlag`]="{ item }">
        <v-chip v-if="item.publishFlag === false" color="" small dark>
          岗位未发布
        </v-chip>
        <v-chip v-else color="success" small dark>岗位已发布</v-chip>
      </template>
    </v-table-searchable>
    <v-dialog-send-info v-model="dialog"></v-dialog-send-info>
    <v-dialog
      attach="#mask"
      hide-overlay
      width="1000"
      persistent
      v-model="progressDialog"
    >
      <v-card>
        <v-card-title>
          调配进展(点击状态跳转)
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-table-list
            :items="items"
            :headers="progressHeaders"
            :show-select="false"
          >
            <template v-slot:[`item.processX`]="{ item }">
              <v-btn v-if="item.codeX == 0" large color="error" elevation="0">
                未开始
              </v-btn>
              <v-btn
                v-if="item.codeX == 1"
                large
                color="info"
                elevation="0"
                @click="jumpTo((index = 1))"
              >
                进行中
              </v-btn>
              <v-btn
                v-if="item.codeX == 2"
                large
                color="success"
                elevation="0"
                @click="jumpTo((index = 1))"
              >
                已完成
              </v-btn>
            </template>
            <template v-slot:[`item.processA`]="{ item }">
              <v-btn v-if="item.codeA == 0" large color="error" elevation="0">
                未开始
              </v-btn>
              <v-btn
                v-if="item.codeA == 1"
                large
                color="info"
                elevation="0"
                @click="jumpTo((index = 2))"
              >
                进行中
              </v-btn>
              <v-btn
                v-if="item.codeA == 2"
                large
                color="success"
                elevation="0"
                @click="jumpTo((index = 2))"
              >
                已完成
              </v-btn>
            </template>
            <template v-slot:[`item.processB`]="{ item }">
              <v-btn v-if="items.codeB == 0" large color="error" elevation="0">
                未开始
              </v-btn>
              <v-btn
                v-if="item.codeB == 1"
                large
                color="info"
                elevation="0"
                @click="jumpTo((index = 3))"
              >
                进行中
              </v-btn>
              <v-btn
                v-if="item.codeB == 2"
                large
                color="success"
                elevation="0"
                @click="jumpTo((index = 3))"
              >
                已完成
              </v-btn>
            </template>
            <template v-slot:[`item.processC`]="{ item }">
              <v-btn v-if="item.codeC == 0" large color="error" elevation="0">
                未开始
              </v-btn>
              <v-btn
                v-if="item.codeC == 1"
                large
                color="info"
                elevation="0"
                @click="jumpTo((index = 4))"
              >
                进行中
              </v-btn>
              <v-btn
                v-if="item.codeC == 2"
                large
                color="success"
                elevation="0"
                @click="jumpTo((index = 4))"
              >
                已完成
              </v-btn>
            </template>
            <template v-slot:[`item.processD`]="{ item }">
              <v-btn v-if="item.codeD == 0" large color="error" elevation="0">
                未开始
              </v-btn>
              <v-btn
                v-if="item.codeD == 1"
                large
                color="info"
                elevation="0"
                @click="jumpTo((index = 5))"
              >
                进行中
              </v-btn>
              <v-btn
                v-if="item.codeD == 2"
                large
                color="success"
                elevation="0"
                @click="jumpTo((index = 5))"
              >
                已完成
              </v-btn>
            </template>
            <template v-slot:[`item.processE`]="{ item }">
              <v-btn v-if="item.codeE == 0" large color="error" elevation="0">
                未开始
              </v-btn>
              <v-btn
                v-if="item.codeE == 1"
                large
                color="info"
                elevation="0"
                @click="jumpTo((index = 6))"
              >
                进行中
              </v-btn>
              <v-btn
                v-if="item.codeE == 2"
                large
                color="success"
                elevation="0"
                @click="jumpTo((index = 6))"
              >
                已完成
              </v-btn>
            </template>
          </v-table-list>
        </v-card-text>
      </v-card>
      <v-card>
        <v-card-title>面试进展进展(点击状态跳转)</v-card-title>
        <v-card-text>
          <v-table-list
            :items="listItems"
            :headers="listHeaders"
            :show-select="false"
          >
            <template v-slot:[`item.status`]="{ item }">
              <v-btn v-if="item.status == 0" large color="error" elevation="0">
                未开始
              </v-btn>
              <v-btn
                v-if="item.status == 1"
                large
                color="info"
                elevation="0"
                @click="jumpToMeetingDetail(item.id)"
              >
                进行中
              </v-btn>
              <v-btn
                v-if="item.status == 2"
                large
                color="success"
                elevation="0"
                @click="jumpToMeetingDetail(item.id)"
              >
                已完成
              </v-btn>
            </template>
          </v-table-list>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import vDialogSendInfo from './v-dialog-send-info.vue'
export default {
  components: { vDialogSendInfo },
  name: 'onshiftChange-management',
  mixins: [dictHelper],
  created() {
    this.tableName = '交班上船计划查询'
    this.reqUrl = '/business/crew/deploy/upBoard/page'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      // { text: '航线', value: 'shipRoute' },
      { text: '上船地点', value: 'upBoardPlace' },
      { text: '上船时间', value: 'upBoardDate' },
      { text: '发布人姓名', value: 'publisher' },
      { text: '发布时间', value: 'publishTime' },
      { text: '岗位失效时间', value: 'expireTime' },
      { text: '岗位发布情况', value: 'publishFlag' },
    ]
    this.progressHeaders = [
      { text: '上船计划表', value: 'processX' },
      { text: '岗位匹配', value: 'processA' },
      { text: '上船面试', value: 'processB' },
      { text: '待上船', value: 'processC' },
      { text: '上船前工资标准审批', value: 'processD' },
      { text: '交接班上下船', value: 'processE' },
    ]
    this.listHeaders = [
      { text: '姓名', value: 'crewUserName' },
      { text: '职位', value: 'post' },
      { text: '状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      interval: true,
      label: '交班上船计划起末时间',
    }
    this.pushParams = {
      name: 'onshiftChange-detail',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      shipRoutes: [],
      routesArray: [],
      dialog: false,
      progressDialog: false,
      items: [],
      listItems: [],
    }
  },

  methods: {
    jumpToMeetingDetail(id) {
      let tableName = 'interview-detail'

      this.$router.push({
        name: tableName, // 使用路由的name属性
        params: {
          id: id, // 传递所需的id参数
        },
      })
    },
    jumpTo(index) {
      let tableName = ''
      let jumpId = ''
      if (index == 1) {
        console.log('items', this.items)
        console.log('xId', this.items[0].xId)
        tableName = 'onshiftChange-detail'
        jumpId = this.items[0].xId
      }
      if (index == 2) {
        tableName = 'positionMatch-detail'
        jumpId = this.items[0].aId
      }
      if (index == 3) {
        tableName = 'interview-management'
      }
      if (index == 4) {
        tableName = 'list-boat-detail'
        jumpId = this.items[0].cId
      }
      if (index == 5) {
        tableName = 'list-boat-detail'
        jumpId = this.items[0].cId
      }
      if (index == 6) {
        tableName = 'shift-overhand-detail'
        jumpId = this.items[0].eId
      }
      console.log('tableName', tableName)
      console.log('jumpId', jumpId)

      // this.$router.replace(baseUrl + realUrl)
      if (jumpId != '') {
        this.$router.push({
          name: tableName, // 使用路由的name属性
          params: {
            id: jumpId, // 传递所需的id参数
          },
        })
      } else {
        this.$router.push({
          name: tableName, // 使用路由的name属性
        })
      }
      // window.location.href = baseUrl + realUrl
    },
    closeForm() {
      this.progressDialog = false
    },
    async routeArrayGet() {
      this.routesArray = this.shipRoutes.map((val) => val?.dictLabel)
    },
    getLabel(value) {
      return this.shipRoutes.find((item) => item?.dictValue === value)
        ?.dictLabel
    },
    async statusChange() {
      if (!(await this.$dialog.msgbox.confirm('是否发布选中记录？'))) return

      if (this.selected.publishFlag) {
        this.$dialog.message.error('该岗位已发布， 请勿重复发布！')
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/upBoard/post/publish`,
        { id: this.selected.id, publishFlag: true },
      )
      if (errorRaw) {
        this.$dialog.message.error(`计划发布失败`)
        return
      }
      this.$dialog.message.success(`计划发布成功`)
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async pushInfo() {
      // this.dialog = true
      if (!this.selected.publishFlag) {
        this.$dialog.message.info('该岗位尚未制定完成，无法推送')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('是否推送选中记录的岗位信息？')))
        return
      const { errorRaw } = await this.getAsync(
        `/business/crew/upAndDown/prepare/sendMessage`,
        {
          id: this.selected.id,
        },
      )
      if (errorRaw) {
        this.$dialog.message.error(`推送失败`)
        return
      }
      this.$dialog.message.success(`推送成功`)
    },
    async viewProgress() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/deploy/upBoard/deployProcess`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.items = [
        {
          processX: data.processX,
          processA: data.processA,
          processB: data.processB,
          processC: data.processC,
          processD: data.processD,
          processE: data.processE,
          codeX: data.codeX,
          codeA: data.codeA,
          codeB: data.codeB,
          codeC: data.codeC,
          codeD: data.codeD,
          codeE: data.codeE,
          xId: data.xId,
          aId: data.aId,
          cId: data.cId,
          dId: data.dId,
          eId: data.eId,
        },
      ]
      this.listItems = data.detailsB
      this.progressDialog = true
    },
    async pushWechat() {
      if (!(await this.$dialog.msgbox.confirm('是否推送选中记录的岗位信息？')))
        return
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/upBoardPlan/weChatSend/${this.selected.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`推送失败`)
        return
      }
      this.$dialog.message.success(`推送成功`)
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/upBoard/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  async mounted() {
    this.shipRoutes = await this.getDictByType('sea_way_type')
    this.routeArrayGet()
  },
}
</script>

<style></style>
