<template>
  <v-container fluid>
    <v-detail-view
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['船员调配记录管理:编辑']"
    >
      <template #调配记录详情信息>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-dialog-select
                label="调配船员"
                dense
                outlined
                table-name="调配船员选择"
                :headers="creHeaders"
                :reqUrl="`/business/crew/baseInfo/simple/page`"
                itemText="name"
                itemValue="userId"
                :search-remain="searchRemain"
                v-model="detailInfo.crewId"
                :initSelected="initSelected"
              >
                <template #searchflieds>
                  <v-col cols="12" md="2">
                    <v-text-field
                      label="船员姓名"
                      outlined
                      dense
                      clearable
                      v-model="searchRemain.name"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="4">
                    <v-text-field
                      label="身份证号"
                      outlined
                      dense
                      clearable
                      v-model="searchRemain.idCard"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="2">
                    <v-ship-station
                      label="船员职务"
                      clearable
                      v-model="searchRemain.position"
                    ></v-ship-station>
                  </v-col>
                </template>
              </v-dialog-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-ship-select v-model="detailInfo.planShipCode"></v-ship-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-handler
                label="调配主管"
                v-model="detailInfo.deployDirectorName"
              ></v-handler>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                label="调配时间"
                v-model="detailInfo.deployDate"
                outlined
                dense
                clearable
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                outlined
                dense
                :items="[
                  { text: '调配成功', value: true },
                  { text: '调配失败', value: false },
                ]"
                label="调配结果"
                v-model="detailInfo.deployResult"
              ></v-select>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-textarea
                outlined
                dense
                label="失败原因"
                :disabled="detailInfo.deployResult"
                v-model="detailInfo.deployFailureReason"
              ></v-textarea>
            </v-col>
            <v-col>
              <v-textarea
                label="备注"
                outlined
                dense
                v-model="detailInfo.remark"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'deployment-detail',
  created() {
    this.backRouteName = 'deployment-history-management'
    this.subtitles = ['调配记录详情信息']
    this.creHeaders = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '船员属性', value: 'crePropertyId' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.getDetailInfo()
  },
  data() {
    return {
      title: '',
      detailInfo: {},
      searchRemain: {},
      initSelected: {},
    }
  },

  methods: {
    async save(goBack) {
      const url =
        this.$route.params.id === `new`
          ? '/business/crew/deploy/deploy/deployHistory/save'
          : '/business/crew/deploy/deploy/deployHistory/update'
      const { errorRaw } = await this.postAsync(url, this.detailInfo)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async getDetailInfo() {
      if (this.$route.params.id !== `new`) {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/deploy/deploy/deployHistory/detail`,
          { id: this.$route.params.id },
        )
        if (errorRaw) {
          return
        }
        this.title = data.crewName + `---${data.shipName}` + '---调配记录详情'
        this.detailInfo = data
        this.initSelected = { userId: data.crewId, name: data.crewName }
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
