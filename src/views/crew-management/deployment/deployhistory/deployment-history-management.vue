<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            clearable
            v-model="searchRemain.crewName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-select v-model="searchRemain.planShipCode"></v-ship-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="调配结果"
            dense
            outlined
            clearable
            :items="[
              { text: '调配成功', value: true },
              { text: '调配失败', value: false },
            ]"
            v-model="searchRemain.deployResult"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-deployment-management/deployment-detail/new"
          v-permission="['船员调配记录管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船员调配记录管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.deployResult`]="{ item }">
        {{ item.deployResult ? '成功' : '失败' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import vShipSelect from '@/components/v-ship-select.vue'
export default {
  components: { vShipSelect },
  name: 'deployment-history-management',
  mixins: [dictHelper],
  created() {
    this.tableName = '调配历史记录管理'
    this.reqUrl = '/business/crew/deploy/deploy/deployHistory/page'
    this.headers = [
      { text: '船员姓名', value: 'crewName' },
      { text: '拟派船舶', value: 'shipName' },
      { text: '调配主管', value: 'deployDirectorName' },
      { text: '调配时间', value: 'deployDate' },
      { text: '调配结果', value: 'deployResult' },
      { text: '调配失败原因', value: 'deployFailureReason' },
      { text: '备注', value: 'remark' },
    ]
    this.creHeaders = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '船员属性', value: 'crePropertyId' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      interval: true,
      label: '调配时间',
    }
    this.pushParams = {
      name: 'deployment-detail',
    }
  },

  data() {
    return {
      selected: false,
      detailInfo: {
        deployResult: true,
      },
      searchRemain: {},
      shipName: [],
    }
  },

  methods: {
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/deploy/deployHistory/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.isShow = false
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
