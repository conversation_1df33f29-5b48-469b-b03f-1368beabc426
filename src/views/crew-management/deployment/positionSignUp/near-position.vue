<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        近期上船计划
        <v-spacer></v-spacer>
      </v-card-title>
      <v-card-text>
        <v-table-list
          :items="items"
          :headers="headers"
          v-model="selected"
          item-key="bizCode"
          use-page
        >
          <template v-slot:[`item.shipRoute`]="{ item }">
            {{ getLabel(item.shipRoute) }}
          </template>
        </v-table-list>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'near-position',
  model: {
    prop: 'open',
    event: 'change',
  },
  created() {
    this.headers = [
      { text: '调配业务编码', value: 'bizCode' },
      { text: '船名', value: 'shipName' },
      { text: '航线', value: 'shipRoute' },
      { text: '上船时间', value: 'upBoardDate' },
      { text: '上船地点', value: 'upBoardPlace' },
    ]
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    shipRoutes: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      items: [],
      selected: false,
    }
  },
  watch: {
    async open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      if (val) {
        await this.getShipPlan()
      }
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    async getShipPlan() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/deploy/postMatch/recentUpBoardPlan/simpleInfoList`,
      )
      if (errorRaw) {
        this.$dialog.message.error('上船计划获取失败！')
        return
      }
      this.items = data
    },
    closeForm() {
      this.$emit('change', false)
    },
    async confirm() {
      if (!(await this.$dialog.msgbox.confirm('请确认当前记录是否报名结束？')))
        return

      const { errorRaw } = await this.getAsync(
        `/business/crew/deploy/postMatch/init`,
        { bizCode: this.selected.bizCode },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('初始化成功')
      this.$emit('change', false)
      this.$emit('success')
    },
    getLabel(value) {
      return this.shipRoutes.find((item) => item?.dictValue === value)
        ?.dictLabel
    },
    hello() {
      console.log(true)
    },
  },
  async mounted() {
    await this.getShipPlan()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
