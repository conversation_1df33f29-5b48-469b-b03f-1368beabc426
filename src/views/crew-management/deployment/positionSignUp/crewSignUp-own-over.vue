<template>
  <v-container fluid>
    <!-- <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            outlined
            dense
            label="船员姓名"
            v-model="crewName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station v-model="searchRemain.postName"></v-ship-station>
        </v-col>
      </template> -->
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
    >
      <template #btns></template>
      <template v-slot:[`item.shipRoute`]="{ item }">
        {{ getLabel(item.shipRoute) }}
      </template>
      <template v-slot:[`item.mark`]="{ item }">
        <v-chip v-if="item.mark == 99" color="" small dark>
          还未被选中面试
        </v-chip>
        <v-chip v-else-if="item.mark == 1" color="info" small dark>
          面试中
        </v-chip>
        <v-chip v-else-if="item.mark == 2" color="info" small dark>
          合格录用
        </v-chip>
        <v-chip v-else-if="item.mark == 3" color="error" small dark>
          不合格录用
        </v-chip>
        <v-chip v-else-if="item.mark == 4" color="info" small dark>
          已在待上船名单内
        </v-chip>
        <v-chip v-else-if="item.mark == 5" color="info" small dark>
          上下船待确认
        </v-chip>
        <v-chip v-else-if="item.mark == 6" color="success" small dark>
          已完成上下船换班
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
const debounce = (fn, delay = 300) => {
  let timer = null
  return function () {
    let context = this
    let args = arguments
    if (timer) clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'crewSignUp-own-over',
  mixins: [dictHelper],
  created() {
    this.tableName = '岗位报名结果情况'
    this.reqUrl = '/business/crew/deploy/signedUpResult/page'
    this.headers = [
      { text: '姓名', value: 'crewName' },
      { text: '船舶名称', value: 'shipName' },
      { text: '岗位名称', value: 'postName' },
      { text: '航线', value: 'shipRoute' },
      { text: '上船地点', value: 'upBoardPlace' },
      { text: '上船时间', value: 'upBoardDate' },
      { text: '报名时间', value: 'signUpTime' },
      { text: '报名面试结果时间', value: 'mark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      interval: true,
      label: '上船时间',
    }
  },
  watch: {
    crewName: {
      handler() {
        this.setCrewName(this)
      },
    },
  },

  data() {
    return {
      selected: false,
      searchRemain: {
        creName: '',
      },
      dialog: false,
      shipRoutes: [],
      crewName: '',
    }
  },
  methods: {
    getLabel(value) {
      return this.shipRoutes.find((item) => item?.dictValue === value)
        ?.dictLabel
    },
    setCrewName: debounce(function (that) {
      that.searchRemain.creName = that.crewName
    }, 500),
  },

  async mounted() {
    this.shipRoutes = await this.getDictByType('sea_way_type')
  },
}
</script>

<style></style>
