<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <!-- <v-col cols="12" md="2">
          <v-ship-select v-model="searchRemain.shipCode"></v-ship-select>
        </v-col> -->
        <v-col cols="12" md="2">
          <v-ship-station v-model="searchRemain.post"></v-ship-station>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          dense
          tile
          color="blue"
          :disabled="!selected"
          @click="signUpPosition"
          v-permission="['上船前岗位查询及报名:报名选择岗位']"
        >
          <v-icon left>mdi-signal-hspa-plus</v-icon>
          报名选择岗位
        </v-btn>
      </template>
      <template v-slot:[`item.route`]="{ item }">
        {{ getLabel(item.route) }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'crewSignUp-management',
  mixins: [dictHelper],
  created() {
    this.tableName = '发布岗位查询'
    this.reqUrl = '/business/crew/deploy/upBoard/post/page'
    this.headers = [
      { text: '岗位名称', value: 'postName' },
      { text: '岗位数量', value: 'requireNum' },
      { text: '岗位要求', value: 'requirement' },
      { text: '待上船舶', value: 'shipName' },
      { text: '航线', value: 'route' },
      { text: '上船地点', value: 'upBoardPlace' },
      { text: '上船时间', value: 'upBoardTime' },
      { text: '失效时间', value: 'expireTime' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      interval: true,
      label: '上船时间',
    }
    this.getRoute()
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      shipRoutes: [],
    }
  },

  methods: {
    async signUpPosition() {
      if (!(await this.$dialog.msgbox.confirm('是否报名当前选中岗位？'))) return

      const { errorRaw } = await this.postAsync(
        `/business/crew/deploy/upBoard/post/signUp`,
        { bizCode: this.selected.bizCode, postId: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error('报名失败')
        return
      }
      this.$dialog.message.success('报名成功')
      this.selected = false
      await this.$refs.table.loaaTable()
    },
    async getRoute() {
      this.shipRoutes = await this.getDictByType('sea_way_type')
    },
    getLabel(value) {
      return this.shipRoutes.find((item) => item?.dictValue === value)
        ?.dictLabel
    },
  },

  mounted() {},
}
</script>

<style></style>
