<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="跟踪船员姓名"
            dense
            outlined
            v-model="searchRemain.crewName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="海务/机务主管姓名"
            dense
            outlined
            v-model="searchRemain.directorName"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="跟踪类型"
            dense
            outlined
            :items="[
              { text: '不符合船员', value: 1 },
              { text: '新任职船员', value: 2 },
            ]"
            v-model="searchRemain.followType"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-deployment-management/deployment-track-detail/new"
          v-permission="['船员跟踪记录管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船员跟踪记录管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.followType`]="{ item }">
        {{ item.followType === 1 ? '不符合船员' : '新任职船员' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'track-management',
  created() {
    this.tableName = '船员跟踪记录管理'
    this.reqUrl = '/business/crew/follow/page'
    this.headers = [
      { text: '跟踪船员姓名', value: 'crewName' },
      { text: '职务', value: 'post' },
      { text: '海务/机务主管姓名', value: 'directorName' },
      { text: '跟踪开始时间', value: 'startTime' },
      { text: '跟踪结束时间', value: 'endTime' },
      { text: '跟踪类型', value: 'followType' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      interval: true,
      label: '跟踪起末时间',
    }
    this.pushParams = {
      name: 'track-detail',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
    }
  },

  methods: {
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(`/business/crew/follow/delete`, {
        id: this.selected.id,
      })
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
