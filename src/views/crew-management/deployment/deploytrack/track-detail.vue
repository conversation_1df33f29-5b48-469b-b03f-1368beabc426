<template>
  <v-container fluid>
    <v-detail-view
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['船员跟踪记录管理:编辑']"
    >
      <template v-slot:调配记录的基本信息>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-dialog-select
                label="跟踪船员"
                dense
                outlined
                table-name="跟踪船员选择"
                :headers="creHeaders"
                :reqUrl="`/business/crew/baseInfo/simple/page`"
                itemText="name"
                itemValue="userId"
                :search-remain="searchRemain"
                v-model="detailInfo.crewId"
                :initSelected="initSelected"
              >
                <template #searchflieds>
                  <v-col cols="12" md="2">
                    <v-text-field
                      label="船员姓名"
                      outlined
                      dense
                      clearable
                      v-model="searchRemain.name"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="4">
                    <v-text-field
                      label="身份证号"
                      outlined
                      dense
                      clearable
                      v-model="searchRemain.idCard"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="2">
                    <v-ship-station
                      label="船员职务"
                      clearable
                      v-model="searchRemain.position"
                    ></v-ship-station>
                  </v-col>
                </template>
              </v-dialog-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-ship-station v-model="detailInfo.post"></v-ship-station>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="海务/机务主管姓名"
                outlined
                dense
                v-model="detailInfo.directorName"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                label="跟踪开始时间"
                outlined
                dense
                v-model="detailInfo.startTime"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <vs-date-picker
                label="跟踪结束时间"
                outlined
                dense
                v-model="detailInfo.endTime"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="跟踪类型"
                outlined
                dense
                v-model="detailInfo.followType"
                :items="[
                  { text: '不符合船员', value: 1 },
                  { text: '新任职船员', value: 2 },
                ]"
              ></v-select>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-attach-list
                :attachments="detailInfo.attachmentRecords"
                @change="changeAttachment"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <template v-slot:船员跟踪历史>
        <v-card-text>
          <v-table-list
            :headers="headers"
            v-model="selected"
            :items="items"
          ></v-table-list>
        </v-card-text>
      </template>
      <template v-slot:船员跟踪历史按钮 v-if="newCard !== `new`">
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="openCard"
          v-permission="['船员跟踪历史:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editElectronicchart"
          v-permission="['船员跟踪历史:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船员跟踪历史:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <track-evaluate
      v-model="dialog"
      @change="change"
      :initialData="initialData"
      @success="success"
    ></track-evaluate>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import routerControl from '@/mixin/routerControl'
import trackEvaluate from './track-evaluate.vue'
export default {
  components: { trackEvaluate },
  name: 'track-detail',
  mixins: [dictHelper, routerControl],
  created() {
    this.backRouteName = 'track-management'
    this.subtitles = ['调配记录的基本信息', '船员跟踪历史']
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '航线', value: 'route' },
      { text: '签约时间', value: 'issureTime' },
      { text: '填写人姓名', value: 'handler' },
      { text: '海务/机务主管评语', value: 'directorComment' },
      { text: '海务小组长/总船长评语', value: 'upDirectorComment' },
      { text: '跟踪指导主要记录', value: 'mainSummary' },
    ]
    this.creHeaders = [
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.newCard = this.$route.params.id
    this.getDetailInfo()
  },
  data() {
    return {
      title: '调度跟踪记录新增',
      selected: false,
      detailInfo: {
        details: {},
        attachment: [],
      },
      dialog: false,
      routesArray: [],
      shipRoutes: [],
      initialData: {},
      searchRemain: {},
      initSelected: {},
      items: [],
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detailInfo.attachmentIds = attachmentIds
    },
    change() {
      this.dialog = false
    },
    async success() {
      await this.getDetailInfo()
    },
    async getDetailInfo() {
      this.shipRoutes = await this.getDictByType('sea_way_type')
      this.routesArray = this.shipRoutes.map((val) => {
        return { text: val?.dictLabel, value: val?.dictValue }
      })
      if (this.$route.params.id !== `new`) {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/follow/detail`,
          {
            id: this.$route.params.id,
          },
        )
        if (errorRaw) {
          return
        }
        this.initSelected = { name: data.crewName, userId: data.crewId }
        this.detailInfo = data
        this.items = data.details
      }
    },
    async save(goBack) {
      const url =
        this.$route.params.id === `new`
          ? '/business/crew/follow/save'
          : '/business/crew/follow/update'
      const { errorRaw } = await this.postAsync(url, this.detailInfo)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    openCard() {
      this.initialData = { parentId: this.detailInfo.id }
      this.dialog = true
    },
    editElectronicchart() {
      this.initialData = this.selected
      this.dialog = true
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/follow/detail/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      await this.getDetailInfo()
    },
  },

  mounted() {},
}
</script>

<style></style>
