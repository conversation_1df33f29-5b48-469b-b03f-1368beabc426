<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}---船员跟踪记录
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select v-model="formData.shipCode"></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  label="航线"
                  outlined
                  clearable
                  dense
                  :items="routesArray"
                  v-model="formData.route"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="填写人姓名"
                  dense
                  outlined
                  v-model="formData.handler"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="签发日期"
                  dense
                  outlined
                  v-model="formData.issureTime"
                ></vs-date-picker>
              </v-col>
              <v-col>
                <v-textarea
                  dense
                  outlined
                  label="海务/机务主管评语"
                  v-model="formData.directorComment"
                ></v-textarea>
              </v-col>
              <v-col>
                <v-textarea
                  dense
                  outlined
                  label="海务小组长/总船长评语"
                  v-model="formData.upDirectorComment"
                ></v-textarea>
              </v-col>
              <v-col>
                <v-textarea
                  dense
                  outlined
                  label="跟踪指导主要记录"
                  v-model="formData.mainSummary"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'track-evaluate',
  model: {
    prop: 'open',
    event: 'change',
  },
  mixins: [dictHelper],
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      shipRoutes: [],
      routesArray: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    async getRoutes() {
      this.shipRoutes = await this.getDictByType('sea_way_type')
      this.routesArray = this.shipRoutes.map((val) => {
        return { text: val?.dictLabel, value: val?.dictValue }
      })
    },
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url =
        this.formData.id !== ''
          ? '/business/crew/follow/detail/update'
          : '/business/crew/follow/detail/save'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
  beforeMount() {
    this.getRoutes()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
