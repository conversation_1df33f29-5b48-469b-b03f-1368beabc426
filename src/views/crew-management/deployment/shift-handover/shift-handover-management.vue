<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :search-remain="searchRemain"
      :req-url="reqUrl"
      use-ship
      :push-params="pushParams"
      :fix-header="false"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="业务编码"
            outlined
            dense
            v-model="searchRemain.bizCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="上船港口"
            outlined
            dense
            v-model="searchRemain.port"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="确认状态"
            outlined
            dense
            :items="[
              { text: '尚未全部确认', value: 0 },
              { text: '已全部确认', value: 1 },
            ]"
            v-model="searchRemain.status"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="业务提交状态"
            outlined
            dense
            :items="[
              { text: '已提交', value: true },
              { text: '未提交', value: false },
            ]"
            v-model="searchRemain.submitFlag"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          @click="exportBoard"
          v-permission="['交接班上下船名单:导出上船船员名单']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          导出上船船员名单
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          @click="exportOffBoard"
          v-permission="['交接班上下船名单:导出下船船员名单']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          导出下船船员名单
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['交接班上下船名单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 0" small dark color="warning">
          尚未全部确认
        </v-chip>
        <v-chip v-else small dark color="success">已全部确认</v-chip>
      </template>
      <template v-slot:[`item.route`]="{ item }">
        {{ getRoute(item.route) }}
      </template>
      <template v-slot:[`item.submitFlag`]="{ item }">
        <v-chip v-if="item.submitFlag" small dark color="success">
          已提交
        </v-chip>
        <v-chip v-else small dark color="">未提交</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'shift-handover-management',
  mixins: [dictHelper],
  created() {
    this.tableName = '交接班记录管理'
    this.reqUrl = '/business/crew/upAndDown/checkin/page'
    this.headers = [
      { text: '业务标识编码', value: 'bizCode' },
      { text: '换班船舶', value: 'shipName' },
      { text: '上船港口', value: 'port' },
      // { text: '航线', value: 'route' },
      { text: '实际上船时间', value: 'actualTime' },
      // { text: '交接确认人1', value: 'ensurePerson1' },
      // { text: '确认时间 1', value: 'ensureTime1' },
      // { text: '交接确认人2', value: 'ensurePerson2' },
      // { text: '确认时间 2', value: 'ensureTime2' },
      // { text: '交接确认人3', value: 'ensurePerson3' },
      // { text: '确认时间 3', value: 'ensureTime3' },
      { text: '备注', value: 'remark' },
      { text: '确认状态', value: 'status' },
      { text: '业务提交状态', value: 'submitFlag' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '实际上船时间',
      interval: true,
    }
    this.pushParams = {
      name: 'shift-handover-detail',
    }
  },

  data() {
    return {
      selected: false,
      searchRemain: {
        submitFlag: false,
      },
      shipRoutes: [],
      baseURL:
        'https://jk.sitc.com/webroot/decision/view/report?viewlet=Test%252FADMIN_CENTER%252FActualBoard.cpt&',
      baseOffURL:
        'https://jk.sitc.com/webroot/decision/view/report?viewlet=Test%252FADMIN_CENTER%252FActualOffBoard.cpt&',
    }
  },

  methods: {
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/upAndDown/checkin/delete`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
    },
    getRoute(item) {
      return this.shipRoutes.find((ele) => {
        if (ele?.dictValue === item) {
          return ele.dictLabel
        }
      })?.dictLabel
    },
    exportBoard() {
      this.openInNewWindow(1)
    },
    exportOffBoard() {
      this.openInNewWindow(2)
    },
    openInNewWindow(chosen) {
      this.$nextTick(() => {
        // 确保 DOM 已经渲染
        if (this.$refs.table && this.$refs.table.dates) {
          let endDate = this.formatDate(this.$refs.table.dates.end)
          let startDate = this.formatDate(this.$refs.table.dates.start)
          console.log(endDate, startDate)
          let url
          if (chosen == 1) {
            url = this.baseURL
          } else if (chosen == 2) {
            url = this.baseOffURL
          }
          url = url + '__bypagesize__=false&'
          let shipcode = this.$refs.table.ship

          if (endDate != '') {
            url = url + 'end_time=' + endDate + '&'
          }
          if (startDate != '') {
            url = url + 'start_time=' + startDate + '&'
          }
          url = url + 'shipcode=' + shipcode + '&'
          window.open(url, '_blank')
        } else {
          this.$dialog.message.error('请等待数据加载，稍后请重试。')
          location.reload()
        }
      })
    },
    formatDate(dateString) {
      if (dateString != null) {
        const date = new Date(dateString)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0') // Months are 0-indexed in JS
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      } else {
        return ''
      }
    },
  },

  async mounted() {
    this.shipRoutes = await this.getDictByType('sea_way_type')
  },
}
</script>

<style></style>
