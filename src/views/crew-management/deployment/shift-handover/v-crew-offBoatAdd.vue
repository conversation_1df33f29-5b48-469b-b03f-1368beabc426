<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        下船船员信息新增
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-dialog-select
                  label="下船船员姓名"
                  dense
                  outlined
                  table-name="下船船员选择"
                  :headers="creHeaders"
                  :reqUrl="`/business/crew/baseInfo/simpleByShip/page`"
                  itemText="name"
                  itemValue="userId"
                  @select="select"
                  :search-remain="searchRemain"
                  v-model="formData.crewUserId"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.name"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="身份证号"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.idCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="船员职务"
                        clearable
                        v-model="searchRemain.position"
                      ></v-ship-station>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="4">
                <v-ship-station
                  label="换班岗位"
                  v-model="formData.post"
                  disabled
                ></v-ship-station>
              </v-col>
              <v-col cols="12" md="4">
                <vs-date-picker
                  outlined
                  dense
                  label="登记时间"
                  v-model="formData.registerTime"
                  :max-date="today"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  required
                  label="交接班港口"
                  outlined
                  dense
                  v-model="formData.port"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-crew-offBoat-add',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
    console.log('dialog---------->')
    console.log(this.dialog)
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    initSelected: {
      type: Object,
      default: () => {},
    },
  },
  created() {
    this.creHeaders = [
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchRemain: {},
      today: new Date().toISOString().substr(0, 10),
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.searchRemain.shipCode = this.initialData.shipCode
      this.formData.parentId = this.initialData.parentId
      // console.log('formData------------>')
      // console.log(this.formData)
      // console.log('searchRemain------------>')
      // console.log(this.searchRemain)
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    select(val) {
      this.formData.post = val.position
      // console.log('formData------------->')
      // console.log(this.formData)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = `/business/crew/upAndDown/checkin/detail/save`
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        this.$dialog.message.success('保存成功')
        return
      }
      this.$dialog.message.error('保存失败')
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
