<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['交接班上下船名单:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="canSubmit"
    >
      <template #titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {})"
          color="secondary"
          small
          tile
          class="mx-1"
          v-permission="['交接班上下船名单:返回列表']"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <v-btn
          width="60"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          :loading="loading"
          v-if="!detailInfo.submitFlag"
          v-permission="['交接班上下船名单:保存']"
        >
          保存
        </v-btn>
        <v-btn
          width="80"
          tile
          @click="submit"
          color="success"
          small
          class="mx-1"
          :loading="sloading"
          v-if="!detailInfo.submitFlag"
          v-permission="['交接班上下船名单:业务提交']"
        >
          业务提交
        </v-btn>
      </template>
      <template v-slot:交接班记录基本信息>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select v-model="detailInfo.shipCode"></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="航线"
                  outlined
                  clearable
                  dense
                  :items="routesArray"
                  v-model="detailInfo.route"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="交接班港口"
                  outlined
                  dense
                  v-model="detailInfo.port"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="实际交接班时间"
                  outlined
                  dense
                  v-model="detailInfo.actualTime"
                  :max-date="today"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="业务提交状态"
                  readonly
                  outlined
                  dense
                  v-model="detailInfo.submitFlag"
                  :items="[
                    { text: '已提交', value: true },
                    { text: '未提交', value: false },
                  ]"
                ></v-select>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-textarea
                  label="备注"
                  outlined
                  dense
                  v-model="detailInfo.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template v-slot:信息确认>
        <v-card-text>
          <v-table-list :items="items" :headers="headers" :show-select="false">
            <template v-slot:[`item.ensurePerson1`]="{ item }">
              <v-btn
                v-if="item.ensureTime1 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 1))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson1}已确认` }}
              </v-btn>
            </template>
            <template v-slot:[`item.ensurePerson2`]="{ item }">
              <v-btn
                v-if="item.ensureTime2 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 2))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson2}已确认` }}
              </v-btn>
            </template>
            <template v-slot:[`item.ensurePerson3`]="{ item }">
              <v-btn
                v-if="item.ensureTime3 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 3))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson3}已确认` }}
              </v-btn>
            </template>
          </v-table-list>
        </v-card-text>
        <v-card-text>
          <v-table-list :items="items" :headers="headers1" :show-select="false">
            <template v-slot:[`item.ensurePerson4`]="{ item }">
              <v-btn
                v-if="item.ensureTime4 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 4))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson4}已确认` }}
              </v-btn>
            </template>
            <template v-slot:[`item.ensurePerson5`]="{ item }">
              <v-btn
                v-if="item.ensureTime5 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 5))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson5}已确认` }}
              </v-btn>
            </template>
            <template v-slot:[`item.ensurePerson6`]="{ item }">
              <v-btn
                v-if="item.ensureTime6 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 6))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson6}已确认` }}
              </v-btn>
            </template>
          </v-table-list>
        </v-card-text>
        <v-card-text>
          <v-table-list :items="items" :headers="headers2" :show-select="false">
            <template v-slot:[`item.ensurePerson7`]="{ item }">
              <v-btn
                v-if="item.ensureTime7 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 7))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson7}已确认` }}
              </v-btn>
            </template>
            <template v-slot:[`item.ensurePerson8`]="{ item }">
              <v-btn
                v-if="item.ensureTime8 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 8))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson8}已确认` }}
              </v-btn>
            </template>
            <template v-slot:[`item.ensurePerson9`]="{ item }">
              <v-btn
                v-if="item.ensureTime9 === null"
                x-small
                color="primary"
                elevation="0"
                @click="enSureFirst((index = 9))"
              >
                尚未确认
              </v-btn>
              <v-btn v-else x-small color="success" elevation="0">
                {{ `${detailInfo.ensurePerson9}已确认` }}
              </v-btn>
            </template>
          </v-table-list>
        </v-card-text>
      </template>
      <!-- <template #下船船员详情信息>
        <v-card-text>
          <v-table-list
            :items="detailInfo.offBoardDetail"
            :headers="CrewHeaders"
            v-model="offselected"
          >
            <template v-slot:[`item.isSuccess`]="{ item }">
              <v-chip v-if="item.isSuccess" color="success" small dark>
                下船成功
              </v-chip>
              <v-chip v-else color="error" small dark>尚未下船</v-chip>
            </template>
          </v-table-list>
        </v-card-text>
      </template> -->
      <template v-slot:下船船员详情信息>
        <v-card-text>
          <v-divider></v-divider>
          <v-data-table
            :headers="CrewHeaders"
            :items="detailInfo.offBoardDetail"
            v-model="offselected"
            item-key="id"
            show-select
            dense
            disable-pagination
            hide-default-footer
            @click:row="selectRow"
          >
            <template v-slot:[`item.isSuccess`]="{ item }">
              <v-chip v-if="item.isSuccess" color="success" small dark>
                下船成功
              </v-chip>
              <v-chip v-else color="error" small dark>尚未下船</v-chip>
            </template>
          </v-data-table>
        </v-card-text>
      </template>
      <template #下船船员详情信息按钮>
        <v-btn
          :disabled="!offselected"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="add_offBoat"
          v-if="!detailInfo.submitFlag"
          v-permission="['下船船员详情信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!offselected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="update_offBoat"
          v-if="!detailInfo.submitFlag"
          v-permission="['下船船员详情信息:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!offselected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del_offBoat"
          v-if="!detailInfo.submitFlag"
          v-permission="['下船船员详情信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <!-- <template #上船船员详情信息>
        <v-card-text>
          <v-table-list
            :items="detailInfo.upBoardDetail"
            :headers="CrewHeaders"
            v-model="onselected"
            :single-select="false"
          >
            <template v-slot:[`item.isSuccess`]="{ item }">
              <v-chip v-if="item.isSuccess" color="success" small dark>
                上船成功
              </v-chip>
              <v-chip v-else color="error" small dark>尚未上船</v-chip>
            </template>
          </v-table-list>
        </v-card-text>
      </template> -->
      <template v-slot:上船船员详情信息>
        <v-card-text>
          <v-divider></v-divider>
          <v-data-table
            :headers="CrewHeaders"
            :items="detailInfo.upBoardDetail"
            v-model="onselected"
            item-key="id"
            show-select
            dense
            disable-pagination
            hide-default-footer
            @click:row="selectRow"
          >
            <template v-slot:[`item.isSuccess`]="{ item }">
              <v-chip v-if="item.isSuccess" color="success" small dark>
                上船成功
              </v-chip>
              <v-chip v-else color="error" small dark>尚未上船</v-chip>
            </template>
          </v-data-table>
        </v-card-text>
      </template>
      <template #上船船员详情信息按钮>
        <v-btn
          :disabled="onselected.length === 0"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="update_onBoat"
          v-if="!detailInfo.submitFlag"
          v-permission="['上船船员详情信息:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="onselected.length === 0"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="del_onBoat"
          v-if="!detailInfo.submitFlag"
          v-permission="['上船船员详情信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-detail-view>
    <v-crew-off-boat
      v-model="offBoat_dialog"
      :initialData="initialData_off_boat"
      :initSelected="initSelected_offBoat"
      @success="success_off_boat"
    ></v-crew-off-boat>
    <v-crew-off-boat-add
      v-model="offBoatAdd_dialog"
      :initialData="initParam_offBoatAdd"
      @success="success_off_boat"
    ></v-crew-off-boat-add>
    <v-crew-on-boat
      v-model="onBoat_dialog"
      :initialData="initialData_on_boat"
      :initSelected="initSelected_onBoat"
      @success="success_on_boat"
    ></v-crew-on-boat>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
import routerControl from '@/mixin/routerControl'
import vCrewOffBoat from './v-crew-offBoat.vue'
import vCrewOffBoatAdd from './v-crew-offBoatAdd.vue'
import VCrewOnBoat from './v-crew-onBoat.vue'
export default {
  components: { vCrewOffBoat, VCrewOnBoat, vCrewOffBoatAdd },
  name: 'shift-handover-detail',
  created() {
    this.backRouteName = 'shift-handover-management'
    this.subtitles = [
      '交接班记录基本信息',
      '信息确认',
      '下船船员详情信息',
      '上船船员详情信息',
    ]
    this.CrewHeaders = [
      { text: '船员姓名', value: 'crewName' },
      { text: '身份证号', value: 'idCard' },
      { text: '换班职务', value: 'post' },
      { text: '登记时间', value: 'registerTime' },
      { text: '交接班港口', value: 'port' },
      { text: '是否上下船成功', value: 'isSuccess' },
    ]
  },
  mixins: [dictHelper, routerControl],
  data() {
    return {
      title: '',
      detailInfo: {},
      routesArray: [],
      shipRoutes: [],
      items: [],
      offselected: [],
      onselected: [],
      loading: false,
      sloading: false,
      offBoat_dialog: false,
      offBoatAdd_dialog: false,
      onBoat_dialog: false,
      initialData_off_boat: {},
      initialData_on_boat: {},
      initSelected_offBoat: {},
      initParam_offBoatAdd: {},
      initSelected_onBoat: {},
      today: new Date().toISOString().substr(0, 10),
      headers: [
        {
          text: '调配',
          value: 'ensurePerson1',
        },
        { text: '调配 确认时间', value: 'ensureTime1' },
        {
          text: '船员负责人',
          value: 'ensurePerson2',
        },
        { text: '船员负责人 确认时间', value: 'ensureTime2' },
        {
          text: '机务主管',
          value: 'ensurePerson3',
        },
        { text: '机务主管 确认时间', value: 'ensureTime3' },
      ],
      headers1: [
        {
          text: '海务主管',
          value: 'ensurePerson4',
        },
        { text: '海务主管 确认时间', value: 'ensureTime4' },
        {
          text: '总轮机长',
          value: 'ensurePerson5',
        },
        { text: '总轮机长 确认时间', value: 'ensureTime5' },
        {
          text: '总船长',
          value: 'ensurePerson6',
        },
        { text: '总船长 确认时间', value: 'ensureTime6' },
      ],
      headers2: [
        {
          text: '船员管理专员',
          value: 'ensurePerson7',
        },
        { text: '船员管理专员 确认时间', value: 'ensureTime7' },
        {
          text: '船员事务主管',
          value: 'ensurePerson8',
        },
        { text: '船员事务主管 确认时间', value: 'ensureTime8' },
        {
          text: '组长',
          value: 'ensurePerson9',
        },
        { text: '组长 确认时间', value: 'ensureTime9' },
      ],
    }
  },

  methods: {
    updateHeadersBasedOnArea() {
      // 检查area值并更新headers中相应的text
      console.log(this.detailInfo.area)
      console.log(this.detailInfo)
      if (this.detailInfo.area === '山东') {
        // 更新船管商务负责人的标题
        this.headers2 = this.headers2.map((header) => {
          if (header.value === 'ensurePerson9') {
            return { ...header, text: '山东航运负责人' }
          }
          if (header.value === 'ensureTime9') {
            return { ...header, text: '山东航运负责人 确认时间' }
          }
          if (header.value === 'ensurePerson7') {
            return { ...header, text: '体系主管' }
          }
          if (header.value === 'ensureTime7') {
            return { ...header, text: '体系主管 确认时间' }
          }
          return header
        })
      }
    },

    selectRow(_, { isSelected, item }) {
      console.log('isSelected', isSelected)
      console.log('item', item)

      if (!isSelected) {
        this.onselected.push(item)
      } else {
        this.onselected = this.onselected.filter((ele) => ele.id !== item.id)
      }
    },
    // async del_onBoat() {
    //   if (this.onselected.isSuccess) {
    //     this.$dialog.message.error('已上船船员禁止删除！')
    //     return
    //   }
    //   if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
    //   const { errorRaw } = await this.postAsync(
    //     `/business/crew/upAndDown/checkin/detail/delete`,
    //     this.onselected,
    //   )
    //   if (errorRaw) {
    //     return
    //   }
    //   this.$dialog.message.success('删除成功')
    //   this.getDetailInfo()
    // },del_onBoat
    async del_onBoat() {
      if (this.onselected.isSuccess) {
        this.$dialog.message.error('已上船船员禁止删除！')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      //const detailIdList = this.onselected.map((ele) => ele)
      const detailIdList = Object.values(this.onselected)
      const { errorRaw } = await this.postAsync(
        `/business/crew/upAndDown/checkin/detail/delete`,
        detailIdList,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.getDetailInfo()
    },
    async del_offBoat() {
      if (this.offselected.isSuccess) {
        this.$dialog.message.error('已下船船员禁止删除！')
        return
      }
      if (this.offselected.length < 1) {
        this.$dialog.message.error('请选择船员')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      // const { errorRaw } = await this.postAsync(
      //   `/business/crew/upAndDown/checkin/detail/delete`,
      //   this.offselected,
      // )
      const detailIdList = Object.values(this.offselected)
      const { errorRaw } = await this.postAsync(
        `/business/crew/upAndDown/checkin/detail/delete`,
        detailIdList,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.getDetailInfo()
    },
    update_onBoat() {
      console.log('onselected', this.onselected)
      if (this.onselected.length > 1) {
        this.$dialog.message.error('只能选择一个船员信息进行修改')
        return
      }
      this.onBoat_dialog = true
      console.log('0', this.onselected[0])

      this.initialData_on_boat = this.onselected[0]
      this.initSelected_onBoat = {
        name: this.onselected[0].crewName,
        userId: this.onselected[0].crewUserId,
      }
    },
    update_offBoat() {
      if (this.offselected.length > 1) {
        this.$dialog.message.error('只能选择一个船员信息进行修改')
        return
      }
      if (this.offselected.length == 0) {
        this.$dialog.message.error('请选择一个船员信息进行修改')
        return
      }
      this.offBoat_dialog = true
      this.initialData_off_boat = this.offselected[0]
      this.initSelected_offBoat = {
        shipCode: this.detailInfo.shipCode,
        name: this.offselected[0].crewName,
        userId: this.offselected[0].crewUserId,
      }
    },
    add_offBoat() {
      this.offBoatAdd_dialog = true
      // console.log('detailInfo------------>')
      // console.log(this.detailInfo)
      this.initParam_offBoatAdd = {
        shipCode: this.detailInfo.shipCode,
        parentId: this.detailInfo.id,
      }
      // console.log('initParam_offBoatAdd---------->')
      // console.log(this.initParam_offBoatAdd)
    },
    async success_off_boat() {
      await this.getDetailInfo()
    },
    async success_on_boat() {
      await this.getDetailInfo()
    },
    async save() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/upAndDown/checkin/update`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('更新成功')
      this.closeAndTo(this.backRouteName, {})
    },
    routeArrayGet() {
      this.routesArray = this.shipRoutes.map((val) => {
        return { text: val.dictLabel, value: val.dictValue }
      })
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/upAndDown/checkin/detail`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      console.log('data------------>')
      console.log(data)
      this.title = `${data.bizCode}-交接班记录详情`
      this.detailInfo = data
      this.items = [
        {
          ensurePerson1: data.ensurePerson1,
          ensurePerson2: data.ensurePerson2,
          ensurePerson3: data.ensurePerson3,
          ensurePerson4: data.ensurePerson4,
          ensurePerson5: data.ensurePerson5,
          ensurePerson6: data.ensurePerson6,
          ensurePerson7: data.ensurePerson7,
          ensurePerson8: data.ensurePerson8,
          ensurePerson9: data.ensurePerson9,
          ensureTime1: data.ensureTime1,
          ensureTime2: data.ensureTime2,
          ensureTime3: data.ensureTime3,
          ensureTime4: data.ensureTime4,
          ensureTime5: data.ensureTime5,
          ensureTime6: data.ensureTime6,
          ensureTime7: data.ensureTime7,
          ensureTime8: data.ensureTime8,
          ensureTime9: data.ensureTime9,
        },
      ]
      // console.log('items-------------->')
      // console.log(this.items)
      this.updateHeadersBasedOnArea()
    },
    async enSureFirst(index) {
      if (!(await this.$dialog.msgbox.confirm('信息是否确认无误？'))) return
      const userId = this.$local.data.get('userInfo').id
      const roleName = this.$local.data.get('userInfo').roleName
      console.log(userId)
      console.log(roleName)
      if (
        index == 1 &&
        roleName !== '船员事务主管,事务专员' &&
        roleName !== '船员调配主管' &&
        roleName !== '外包公司账号'
      ) {
        this.$dialog.message.error('无确认权限')
        return
      }
      if (
        index !== 1 &&
        userId !== '1170896100656156674' &&
        // userId !== this.detailInfo.ensurePerson1 &&
        // userId !== this.detailInfo.ensurePerson2 &&
        userId !== this.detailInfo.ensurePerson3 &&
        userId !== this.detailInfo.ensurePerson4 &&
        userId !== this.detailInfo.ensurePerson5 &&
        userId !== this.detailInfo.ensurePerson6 &&
        userId !== this.detailInfo.ensurePerson7 &&
        userId !== this.detailInfo.ensurePerson8 &&
        userId !== this.detailInfo.ensurePerson9
      ) {
        this.$dialog.message.error('无确认权限')
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/crew/upAndDown/checkin/ensureTime`,
        { id: this.$route.params.id, index: index, remarkPart: '通过' },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('确认成功')
      this.getDetailInfo()
    },
    async submit() {
      if (this.detailInfo.submitFlag) {
        this.$dialog.message.error('该业务已提交，请勿重复提交')
        return
      }
      if (!this.detailInfo.ensureTime1 || !this.detailInfo.ensureTime2) {
        this.$dialog.message.error('信息尚未全部确认，无法提交')
        return
      }

      if (!(await this.$dialog.msgbox.confirm('是否确认无误？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/upAndDown/checkin/submit`,
        { id: this.$route.params.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交成功')
      this.closeAndTo(this.backRouteName, {})
    },
  },

  async mounted() {
    this.shipRoutes = await this.getDictByType('sea_way_type')
    this.routeArrayGet()
    await this.getDetailInfo()
  },
}
</script>

<style></style>
