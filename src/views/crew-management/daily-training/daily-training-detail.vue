<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船员日常培训:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="detailInfo.status === '' || detailInfo.status === '未发布'"
      @save="save"
      @submit="submit"
    >
      <template v-slot:日常培训基础信息>
        <v-card-text>
          <v-form ref="form">
            <v-container>
              <v-row>
                <v-col cols="12" md="2">
                  <v-select
                    label="培训科目"
                    outlined
                    dense
                    :rules="[rules.required]"
                    :items="[
                      '船员履约培训',
                      '船员休假期间培训',
                      '毕业生入职培训',
                      '职务晋升培训',
                    ]"
                    v-model="detailInfo.subject"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="培训地点"
                    outlined
                    dense
                    :rules="[rules.required]"
                    v-model="detailInfo.place"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="培训课时"
                    outlined
                    dense
                    :rules="[rules.required]"
                    type="number"
                    v-model="detailInfo.lessons"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="计划培训人数"
                    outlined
                    dense
                    :rules="[rules.required]"
                    type="number"
                    v-model="detailInfo.planCreNum"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="计划开始日期"
                    outlined
                    dense
                    v-model="detailInfo.planStartTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <vs-date-picker
                    label="计划结束日期"
                    outlined
                    dense
                    v-model="detailInfo.planEndTime"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="培训费用"
                    outlined
                    dense
                    :rules="[rules.required]"
                    v-model="detailInfo.planCost"
                    suffix="元"
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-textarea
                    dense
                    outlined
                    label="培训内容"
                    :rules="[rules.required]"
                    v-model="detailInfo.content"
                  ></v-textarea>
                </v-col>
                <v-col>
                  <v-textarea
                    dense
                    outlined
                    label="备注"
                    v-model="detailInfo.remark"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:船员报名情况按钮 v-if="detailInfo.status === 2">
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="createInfo"
          v-permission="['船员报名情况:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          outlined
          dense
          :loading="loading"
          color="error"
          class="mx-1"
          :disabled="!selected"
          @click="delInfo"
          v-permission="['船员报名情况:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:船员报名情况>
        <v-table-list :headers="headers" :items="items" v-model="selected">
          <template v-slot:[`item.way`]="{ item }">
            {{ item.way === 1 ? '船员报名' : '手动添加' }}
          </template>
          <template v-slot:[`item.operate`]="{ item }">
            <v-btn
              v-if="detailInfo.status === 2"
              outlined
              dense
              color="info"
              tile
              small
              @click="editInfo(item)"
            >
              更换船员
            </v-btn>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <enroll-crew-dialog
      v-model="dialog"
      :initialData="initialData"
      @success="success"
      :initSelected="initSelected"
    ></enroll-crew-dialog>
  </v-container>
</template>
<script>
import enrollCrewDialog from './enroll-crew-dialog.vue'
export default {
  components: { enrollCrewDialog },
  name: 'daily-training-detail',
  created() {
    this.backRouteName = 'daily-training-management'
    this.subtitles = ['日常培训基础信息', '船员报名情况']
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '船员职务', value: 'post' },
      { text: '报名时间', value: 'applyTime' },
      { text: '报名途径', value: 'way' },
      { text: '操作', value: 'operate' },
    ]
    this.newCard = this.$route.params.id
  },
  data() {
    return {
      selected: false,
      isEdit: false,
      loading: false,
      planId: '',
      items: [],
      detailInfo: {
        status: '',
      },
      crewInfo: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      dialog: false,
      title: '新增日常计划',
      initialData: {},
      initSelected: {},
    }
  },

  methods: {
    async submit(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw, data } = await this.postAsync(
        `/business/crew/cultivatePlan/modifyCultivatePlan`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.planId = data
      const newObject = await this.getAsync(
        `/business/crew/cultivatePlan/releaseCultivatePlan`,
        { id: data },
      )
      if (newObject.errorRaw) {
        return
      }
      this.$dialog.message.success(`提交成功`)
      goBack()
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw, data } = await this.postAsync(
        `/business/crew/cultivatePlan/modifyCultivatePlan`,
        this.detailInfo,
      )
      if (errorRaw) {
        return
      }
      this.planId = data
      this.$dialog.message.success(`添加成功`)
      goBack()
    },
    async getDetailInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/cultivatePlan/getById/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.detailInfo = data
        this.title = data.planCode + '---' + data.subject
        this.items = data.list
      }
    },
    editInfo(item) {
      this.initialData = item
      this.initSelected = {
        name: item.creName,
        userId: item.creId,
      }
      this.dialog = true
    },
    createInfo() {
      this.initialData = {
        planId: this.$route.params.id,
        way: 2,
        applyTime: new Date().toISOString().slice(0, 10),
      }
      this.dialog = true
    },
    async delInfo() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/cultivatePlan/deleteCultivateApply`,
        [this.selected.id],
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.getDetailInfo()
    },
    async success() {
      await this.getDetailInfo()
    },
  },

  async mounted() {
    await this.getDetailInfo()
    if (this.$route.params.id == 'new') {
      this.save()
    }
  },
}
</script>

<style></style>
