<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="isShow">
        <v-card-title>
          {{ isEdit ? '新增' : '修改' }}---船员岗前培训信息
          <v-spacer></v-spacer>
          <v-icon @click="close">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2">
              <v-dialog-select
                v-model="detailInfo.creId"
                item-value="userId"
                item-text="name"
                req-url="/business/crew/baseInfo/simple/page"
                label="申请船员"
                :rules="[rules.required]"
                :headers="creHeaders"
                :init-selected="initSelected"
              ></v-dialog-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-ship-select
                label="拟上船船舶"
                v-model="detailInfo.shipCode"
              ></v-ship-select>
            </v-col>
            <v-col cols="12" md="2">
              <v-text-field
                label="上船职位"
                v-model="detailInfo.post"
                outlined
                dense
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3">
              <vs-date-picker
                outlined
                dense
                label="	加入岗前培训名单时间"
                v-model="detailInfo.addTime"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2">
              <v-select
                label="是否已培训"
                outlined
                dense
                :items="[
                  { text: '是', value: true },
                  { text: '否', value: false },
                ]"
                v-model="detailInfo.cultivatedFlag"
              ></v-select>
            </v-col>
          </v-row>
          <v-col cols="12">
            <v-btn
              outlined
              tile
              :loading="loading1"
              color="success"
              class="mx-1"
              @click="save"
              v-permission="['岗前培训:编辑']"
              block
            >
              <v-icon left>mdi-plus-circle</v-icon>
              {{ isEdit ? '新增' : '更新' }}
            </v-btn>
          </v-col>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-station
            v-model="searchRemain.post"
            clearable
          ></v-ship-station>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            v-model="searchRemain.cultivatedFlag"
            label="是否已培训"
            outlined
            dense
            clearable
            :items="[
              { text: '是', value: true },
              { text: '否', value: false },
            ]"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="addCard"
          v-permission="['岗前培训:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editElectronicchart"
          v-permission="['岗前培训:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['岗前培训:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.cultivatedFlag`]="{ item }">
        {{ item.cultivatedFlag === false ? '否' : '是' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'pre-job-training',
  created() {
    this.tableName = '岗前培训待选名单'
    this.reqUrl = '/business/crew/cultivateBeforePost/page'
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '拟上船船舶名', value: 'shipName' },
      { text: '上船职务', value: 'post' },
      { text: '加入岗前培训名单时间', value: 'addTime' },
      { text: '是否已经培训', value: 'cultivatedFlag' },
    ]
    this.creHeaders = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '船员属性', value: 'crePropertyId' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
    this.initSelected = {}
    this.fuzzyLabel = ''
  },

  data() {
    return {
      detailInfo: {},
      selected: false,
      loading1: false,
      searchRemain: {},
      isShow: false,
      isEdit: true,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      state: [],
    }
  },
  watch: {
    selected: {
      handler(val) {
        if (val) {
          this.initSelected = {
            name: this.selected.creName,
            userId: this.selected.creId,
          }
          this.detailInfo = this.selected
        } else {
          this.detailInfo = {}
          this.isShow = false
          this.initSelected = {}
        }
      },
    },
  },

  methods: {
    addCard() {
      if (this.selected) {
        this.detailInfo = {}
        this.initSelected = {}
        this.isShow = !this.isShow
        this.isEdit = true
      } else {
        this.isShow = !this.isShow
        this.isEdit = true
      }
    },
    editElectronicchart() {
      this.isShow = !this.isShow
      this.isEdit = false
      this.detailInfo = this.selected
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        `/business/crew/cultivateBeforePost/deleteCultivateBeforePost`,
        [this.selected.id],
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败！`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    close() {
      this.isShow = false
      this.detailInfo = {}
    },
    async save() {
      this.loading1 = true
      const { errorRaw } = await this.postAsync(
        `/business/crew/cultivateBeforePost/modifyCultivateBeforePost`,
        this.detailInfo,
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      if (this.isEdit) {
        this.$dialog.message.success(`新增成功`)
      } else {
        this.$dialog.message.success(`修改成功`)
      }
      this.isShow = false
      await this.$refs.table.loadTableData()
    },
    async getCrewInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/simple/page`,
      )
      if (errorRaw) {
        return
      }
      this.state = data.records.map((val) => {
        return val.userId + '---' + val.name
      })
    },
  },

  mounted() {
    this.getCrewInfo()
  },
}
</script>

<style></style>
