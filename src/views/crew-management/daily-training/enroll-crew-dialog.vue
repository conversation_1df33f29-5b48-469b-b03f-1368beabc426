<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}报名船员
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-dialog-select
                  label="船员"
                  dense
                  outlined
                  table-name="船员选择"
                  :headers="creHeaders"
                  :reqUrl="`/business/crew/baseInfo/simple/page`"
                  itemText="name"
                  itemValue="userId"
                  :search-remain="searchRemain"
                  v-model="formData.creId"
                  :initSelected="initSelected"
                  @select="select"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.name"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="身份证号"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.idCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="船员职务"
                        clearable
                        v-model="searchRemain.position"
                      ></v-ship-station>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-station
                  label="船员职务"
                  :rules="[rules.required]"
                  v-model="formData.post"
                ></v-ship-station>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  outlined
                  label="报名时间"
                  :rules="[rules.required]"
                  v-model="formData.applyTime"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  dense
                  outlined
                  label="报名途径"
                  :rules="[rules.required]"
                  :items="[
                    { text: '船员报名', value: 1 },
                    { text: '手动添加', value: 2 },
                  ]"
                  v-model="formData.way"
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'enroll-crew-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.creHeaders = [
      { text: '船员ID', value: 'creId' },
      { text: '船员姓名', value: 'name' },
      { text: '船员属性', value: 'crePropertyId' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    initSelected: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchRemain: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    select(val) {
      this.formData.post = val.position
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = `/business/crew/cultivatePlan/modifyCultivateApply`
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
