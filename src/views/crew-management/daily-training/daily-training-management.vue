<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-select
            label="培训科目"
            outlined
            dense
            :items="[
              '船员履约培训',
              '船员休假期间培训',
              '毕业生入职培训',
              '职务晋升培训',
            ]"
            v-model="searchRemain.subject"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="培训计划状态"
            outlined
            dense
            required
            :items="[
              { text: '未发布', value: '1' },
              { text: '已发布', value: '2' },
              { text: '已申请预算', value: '3' },
              { text: '已完成', value: '4' },
            ]"
            v-model="searchRemain.status"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          class="mx-1"
          color="#2962FF"
          :disabled="!selected"
          @click="planOn"
          v-permission="['船员日常培训:发布计划']"
        >
          <v-icon left>mdi-send-outline</v-icon>
          发布计划
        </v-btn>
        <v-btn
          outlined
          tile
          class="mx-1"
          :disabled="!selected"
          @click="budget"
          v-permission="['船员日常培训:发起预算申请']"
        >
          <v-icon left>mdi-cash</v-icon>
          发起预算申请
        </v-btn>

        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-account-daily-training/crew-daily-traing-detail/new"
          v-permission="['船员日常培训:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船员日常培训:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{
          item.status === 1
            ? '未发布'
            : item.status === 2
            ? '已发布'
            : item.status === 3
            ? '已申请预算'
            : '已完成'
        }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'daily-training-management',
  created() {
    this.tableName = '日常培训计划方案'
    this.reqUrl = '/business/crew/cultivatePlan/page'
    this.headers = [
      { text: '培训编号', value: 'planCode' },
      { text: '培训科目', value: 'subject' },
      { text: '培训内容', value: 'content' },
      { text: '培训地点', value: 'place' },
      { text: '培训课时', value: 'lessons' },
      { text: '计划培训人数', value: 'planCreNum' },
      { text: '计划费用', value: 'planCost' },
      { text: '发起预算申请费用', value: 'applyCost' },
      { text: '计划开始时间', value: 'planStartTime' },
      { text: '计划结束时间', value: 'planEndTime' },
      { text: '备注', value: 'remark' },
      { text: '状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'daily-training-detail',
    }
    this.searchDate = {
      interval: true,
      label: '培训起始时间',
    }
  },

  data() {
    return {
      formShow: false,
      searchRemain: {},
      selected: false,
    }
  },

  methods: {
    async planOn() {
      const { errorRaw } = await this.getAsync(
        `/business/crew/cultivatePlan/releaseCultivatePlan`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.selected = false
      await this.$refs.table.loadTableData()
      this.$dialog.message.success(`发布成功`)
    },
    async budget() {
      if (!(await this.$dialog.msgbox.confirm('是否发起预算申请？'))) return
      if (this.selected.status === 3) {
        this.$dialog.message.info('该记录已发起预算申请，请勿重复申请')
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/crew/cultivatePlan/applyBudget`,
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.selected = false
      this.$dialog.message.success(`预算发起成功`)
      await this.$refs.table.loadTableData()
    },

    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        `/business/crew/cultivatePlan/deleteCultivatePlan`,
        [this.selected.id],
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.selected = false
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
