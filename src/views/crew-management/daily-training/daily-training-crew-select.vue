<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-select
            label="培训科目"
            outlined
            dense
            :items="[
              '船员履约培训',
              '船员休假期间培训',
              '毕业生入职培训',
              '职务晋升培训',
            ]"
            v-model="searchRemain.subject"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          :loading="loading1"
          tile
          class="mx-1"
          color="blue"
          :disabled="!selected"
          @click="entryPlan"
        >
          <v-icon left>mdi-button-pointer</v-icon>
          参加此次培训
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'daily-training-crew-select',
  created() {
    this.tableName = '日常培训计划方案'
    this.reqUrl = '/business/crew/cultivatePlan/page'
    this.headers = [
      { text: '培训编号', value: 'planCode' },
      { text: '培训科目', value: 'subject' },
      { text: '培训内容', value: 'content' },
      { text: '培训地点', value: 'place' },
      { text: '培训课时', value: 'lessons' },
      { text: '计划开始时间', value: 'planStartTime' },
      { text: '计划结束时间', value: 'planEndTime' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      interval: true,
      label: '培训起始时间',
    }
  },

  data() {
    return {
      loading1: false,
      selected: false,
      searchRemain: {
        status: 2,
      },
    }
  },

  methods: {
    async entryPlan() {
      this.loading1 = true
      if (!(await this.$dialog.msgbox.confirm('是否报名选中培训计划？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/crew/cultivatePlan/applyCultivatePlan`,
        { id: this.selected.id },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`报名成功`)
    },
  },

  mounted() {},
}
</script>

<style></style>
