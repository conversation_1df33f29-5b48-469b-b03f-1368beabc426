<template>
  <v-container fluid>
    <v-detail-view
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :canSave="false"
    >
      <template #在船船员工资明细>
        <v-card-text>
          <v-table-list
            :items="detailInfo"
            :headers="headers"
            v-model="offselected"
          ></v-table-list>
        </v-card-text>
      </template>
      <template #在船船员工资明细按钮>
        <v-btn
          :disabled="!offselected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editElectronicchart"
          v-permission="['在船船员工资明细:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
      </template>
    </v-detail-view>
    <v-crew-onBoatSalary
      v-model="salary_dialog"
      :initialData="initialData_salary"
      :initSelected="initSelected_salary"
      @success="success_onBoatSalary"
    ></v-crew-onBoatSalary>
  </v-container>
</template>
<script>
import vCrewOnBoatSalary from './v-crew-onBoatSalary.vue'
export default {
  components: { vCrewOnBoatSalary },
  name: 'on-boat-salary-detail',
  created() {
    this.backRouteName = 'on-broad-management'
    this.subtitles = ['在船船员工资明细']
    this.headers = [
      { text: '船员', value: 'creName' },
      { text: '船名', value: 'shipName' },
      { text: '职务', value: 'position' },
      { text: '实际工资', value: 'actualSalary' },
      { text: '实际工资开始时间', value: 'actualSalaryBeginDate' },
      { text: '上一次的实际工资', value: 'lastActualSalary' },
    ]
  },
  data() {
    return {
      detailInfo: [],
      title: '',
      offselected: false,
      initialData_salary: {},
      initSelected_salary: {},
      salary_dialog: false,
    }
  },

  methods: {
    async save(goBack) {
      const { errorRaw } = await this.postAsync(
        `/business/crew/salary/salaryMake/remark`,
        {
          id: this.$route.params.id,
          remark: this.detailInfo.remark,
        },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      goBack()
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/osmOnShipCrew/salaryList/${this.$route.params.id}`,
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = data
      console.log(this.detailInfo)
    },
    editElectronicchart() {
      this.salary_dialog = true
      this.initialData_salary = this.offselected
      this.initSelected_salary = {
        name: this.offselected.creName,
        actualSalary: this.offselected.actualSalary,
      }
      console.log(this.initSelected_salary)
    },
    closeForm() {
      this.offselected = false
      this.boatmanItem = {}
      this.isShow = false
    },
    async success_onBoatSalary() {
      await this.getDetailInfo()
    },
  },

  mounted() {
    this.getDetailInfo()
  },
}
</script>

<style></style>
