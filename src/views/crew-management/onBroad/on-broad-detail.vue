<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['当前在船船员信息:编辑']"
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #个人信息>
        <v-container fluid>
          <v-expansion-panels multiple accordion v-model="panel_1" focusable>
            <v-expansion-panel>
              <v-expansion-panel-header>基本信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2" v-if="detailInfo.empId">
                      <v-text-field
                        label="工号"
                        outlined
                        dense
                        v-model="detailInfo.empId"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="中文姓名"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.chName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="英文姓名"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.enName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="身份证号码"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.idCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="民族"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.nation"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <vs-date-picker
                        outlined
                        dense
                        label="出生日期"
                        readonly
                        v-model="detailInfo.birthDate"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="性别"
                        outlined
                        dense
                        :items="[
                          { text: '男', value: '1' },
                          { text: '女', value: '2' },
                        ]"
                        readonly
                        v-model="detailInfo.gender"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="国家"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.country"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="国籍"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.countryCode"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="籍贯"
                        dense
                        outlined
                        readonly
                        v-model="detailInfo.nativePlace"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="婚姻状况"
                        outlined
                        dense
                        :items="['未婚', '已婚', '离异']"
                        readonly
                        v-model="detailInfo.maritalStatus"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="政治面貌"
                        outlined
                        dense
                        :items="[
                          '共产党员',
                          '共青团员',
                          '群众',
                          '预备党员',
                          '民主党派成员',
                        ]"
                        readonly
                        v-model="detailInfo.politicsStatus"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="宗教信仰"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.religion"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="外语语种"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.foreignLanguages"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="外语水平"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.foreignLevel"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-textarea
                    outlined
                    dense
                    label="有无病史"
                    readonly
                    v-model="detailInfo.medicalHistory"
                  ></v-textarea>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>体貌特征</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="身高"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.height"
                        suffix="cm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="体重"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.weight"
                        suffix="kg"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="血型"
                        outlined
                        dense
                        readonly
                        :items="['A', 'AB', 'B', 'O']"
                        v-model="detailInfo.bloodType"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作服尺码"
                        outlined
                        dense
                        readonly
                        :items="[160, 165, 170, 175, 180, 185, 190, 195, 200]"
                        v-model="detailInfo.workClothesSize"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作鞋尺码"
                        outlined
                        dense
                        readonly
                        :items="[39, 40, 41, 42, 43, 44, 45, 46]"
                        v-model="detailInfo.workShoeSize"
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>联系方式</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="手机号"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.phone"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="邮箱"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.email"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col md="6">
                      <v-textarea
                        label="邮寄地址"
                        outlined
                        readonly
                        dense
                        v-model="detailInfo.mailingAddress"
                      ></v-textarea>
                    </v-col>
                    <v-col md="6">
                      <v-textarea
                        label="家庭住址"
                        outlined
                        readonly
                        dense
                        v-model="detailInfo.homeAddress"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>家庭成员</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card>
                  <v-table-list
                    ref="table"
                    v-model="selected"
                    :headers="headers"
                    :items="detailInfo.familyMember"
                    item-key="name"
                  ></v-table-list>
                </v-card>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>紧急联系人</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="紧急联系人"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.emergencyName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="电话"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.emergencyPhone"
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <v-text-field
                        label="地址"
                        outlined
                        readonly
                        dense
                        v-model="detailInfo.emergencyAddress"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>教育经历</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="最高学历"
                        outlined
                        readonly
                        dense
                        v-model="detailInfo.highestDegree"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="毕业院校"
                        outlined
                        readonly
                        dense
                        v-model="detailInfo.graduateSchool"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <vs-date-picker
                        dense
                        outlined
                        label="毕业时间"
                        readonly
                        v-model="detailInfo.graduationDate"
                      ></vs-date-picker>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>职务信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="实际职务"
                        readonly
                        v-model="detailInfo.actualPosition"
                      ></v-ship-station>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="证书职务"
                        readonly
                        v-model="detailInfo.certificatePosition"
                      ></v-ship-station>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>银行卡信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币开户行"
                        outlined
                        dense
                        v-model="detailInfo.rmbBank"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币开户银行分支机构"
                        outlined
                        dense
                        v-model="detailInfo.rmbBankBranch"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币卡号"
                        outlined
                        dense
                        v-model="detailInfo.rmbCard"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币开户行人姓名"
                        outlined
                        dense
                        v-model="detailInfo.rmbName"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="开户地"
                        outlined
                        dense
                        v-model="detailInfo.rmbAddress"
                        readonly
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元开户行"
                        dense
                        outlined
                        v-model="detailInfo.usdBank"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元开户银行分支机构中文名称"
                        outlined
                        dense
                        v-model="detailInfo.usdBankBranch"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元开户银行分支机构英文名称"
                        outlined
                        dense
                        v-model="detailInfo.usdBankBranchEn"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元卡号"
                        outlined
                        dense
                        v-model="detailInfo.usdCard"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元开户人姓名"
                        outlined
                        dense
                        v-model="detailInfo.usdName"
                        readonly
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="SWIFT CODE"
                        dense
                        outlined
                        v-model="detailInfo.swiftCode"
                        readonly
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-attachment-only
                    title="银行卡照片附件美元银行卡图片附件"
                    :attachment="detailInfo.bankCardPicAttachment"
                    readonly
                    disabled
                  ></v-attachment-only>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>照片附件</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-crew-pic
                  :upLoad="false"
                  :ImagePicture="detailInfo.identificationPhotoAttachment"
                ></v-crew-pic>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>其他信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-select
                        label="是否由公司保存档案"
                        outlined
                        dense
                        :items="[
                          { text: '是', value: true },
                          { text: '否', value: false },
                        ]"
                        v-model="detailInfo.archiveFlag"
                        readonly
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="工龄"
                        outlined
                        dense
                        v-model="detailInfo.workAge"
                        readonly
                        suffix="年"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="档案存放地点"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.archivePlace"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-attachment-only
                    title="含船员手签名的加入申请（加盖手印）"
                    :attachment="detailInfo.crewAttachment.handleApply"
                    accept="all"
                    disabled
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员身份证复印件"
                    :attachment="detailInfo.crewAttachment.idCardCopy"
                    accept="all"
                    disabled
                  ></v-attachment-only>
                  <v-attachment-only
                    title="无工作或无缴纳社保证明"
                    :attachment="detailInfo.crewAttachment.proveNoWork"
                    accept="all"
                    disabled
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员简历表"
                    :attachment="detailInfo.crewAttachment.resume"
                    accept="all"
                    disabled
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员工作考评表（或业务部门推荐）"
                    :attachment="detailInfo.crewAttachment.workScoreTable"
                    accept="all"
                    disabled
                  ></v-attachment-only>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-container>
      </template>
      <template #公司信息>
        <v-container fluid>
          <v-expansion-panels multiple accordion v-model="panel_2" focusable>
            <v-expansion-panel>
              <v-expansion-panel-header>基本信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="企业微信号"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.wechatNumber"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>社保信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="社保信息"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.socialSecurity"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>船员属性</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="4">
                      <v-select
                        outlined
                        dense
                        label="调配公司"
                        :items="creFirst"
                        readonly
                        v-model="detailInfo.creProperty.creCompany"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        outlined
                        dense
                        label="船员性质"
                        :items="['自有船员', '外聘船员']"
                        readonly
                        v-model="detailInfo.creProperty.creFeature"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        outlined
                        dense
                        label="签署公司"
                        readonly
                        :items="creThird"
                        v-model="detailInfo.creProperty.creType"
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>测评结果</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-textarea
                    label="测评结果"
                    outlined
                    dense
                    readonly
                    v-model="detailInfo.testResult"
                  ></v-textarea>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-container>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import vCrewPic from '@/views/crew-management/crew-information/private/v-crewPic.vue'
import VAttachmentOnly from '@/views/crew-management/crew-information/private/v-attachmentOnly.vue'
export default {
  name: 'on-broad-detail',
  components: { vCrewPic, VAttachmentOnly },
  created() {
    this.backRouteName = 'on-broad-management'
    this.subtitles = ['个人信息', '公司信息']
    this.headers = [
      { text: '姓名', value: 'name' },
      { text: '电话号码', value: 'phoneNo' },
      { text: '与本人关系', value: 'relation' },
    ]
  },
  data() {
    return {
      title: '',
      detailInfo: {
        crewAttachment: {},
        creProperty: {},
      },
      panel_1: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      panel_2: [0, 1, 2, 3],
      selected: false,
      creFirst: [],
      creThird: [],
    }
  },
  methods: {
    async save(goBack) {
      goBack()
    },
    // 获取船员信息
    async getCrewInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/recordByUserId/${this.$route.params.creId}`,
      )
      if (errorRaw) {
        return
      }
      this.detailInfo = data
      this.title = data.chName + '-----个人信息详情'
    },
    //获取船员属性信息
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.creFirst = data
    },
    async getCreThird() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/list`,
        {
          creCompany: this.detailInfo.creProperty.creCompany,
          creFeature: this.detailInfo.creProperty.creFeature,
        },
      )
      if (errorRaw) {
        return
      }
      this.creThird = data.records.map((val) => val.creType)
      this.creThirdAll = data.records
    },
  },

  mounted() {
    this.getCrewInfo()
    this.getCreFirst()
    this.getCreThird()
  },
}
</script>

<style></style>
