<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员工号"
            outlined
            dense
            required
            v-model="searchRemain.id"
            class="d-none"
            clearable
          ></v-text-field>
        </v-col>
      </template>
      <template #btns></template>
      <template v-slot:[`item.creType`]="{ item }">
        {{ item.creType === 1 ? '自有' : '外聘' }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ item.status ? '是' : '否' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'on-broad-crew',
  created() {
    this.tableName = '船员个人在船信息查询'
    this.reqUrl = '/business/crew/osmOnShipCrew/getCrewRecord'
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '上船船名', value: 'shipName' },
      { text: '船员类型', value: 'creType' },
      { text: '上船时间', value: 'onBoardTime' },
      { text: '下船时间', value: 'offBoardTime' },
      { text: '累计公休天数', value: 'numHoliday' },
      { text: '在船职务', value: 'post' },
      { text: '超期休假原因', value: 'reason' },
      { text: '是否休假', value: 'status' },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      searchRemain: {
        id: this.$local.data.get('userInfo').id,
      },
      selected: false,
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
