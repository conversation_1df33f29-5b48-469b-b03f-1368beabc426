<template>
  <v-container fluid>
    <v-card v-if="isShow">
      <v-card-title>
        修改船员在船职务
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="2">
            <v-text-field
              label="船员姓名"
              outlined
              dense
              disabled
              v-model="boatmanItem.creName"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="2">
            <v-ship-station
              label="当前职务"
              outlined
              dense
              v-model="boatmanItem.post"
            ></v-ship-station>
          </v-col>
        </v-row>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          block
          v-permission="['船员在船信息:编辑']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          保存
        </v-btn>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      :showExportButton="true"
      useShip
      @dbclick="dbclick"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            required
            v-model="searchRemain.creName"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员身份证号"
            outlined
            dense
            required
            v-model="searchRemain.idNumber"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="在船职务"
            v-model="searchRemain.post"
            clearable
          ></v-ship-station>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员管理公司"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.creCompany"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员性质"
            outlined
            dense
            required
            :items="secondInfo"
            v-model="searchRemain.creFeature"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="劳动合同签署公司"
            required
            :items="creThird"
            v-model="searchRemain.creContract"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          @click="openNewWindow"
          v-permission="['当前在船船员:导出工资标准名单']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          导出工资标准名单
        </v-btn>
        <v-import-more-btn
          importUrl="/business/crew/osmOnShipCrew/salaryDetailImport"
          buttonLabel="批量导入工资标准"
          @importSuccess="importSuccess"
          v-permission="['当前在船船员:导出工资标准名单']"
        ></v-import-more-btn>
        <!-- 根据要求已弃用该功能 -->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="blue"
          class="mx-1"
          @click="GoTOSalary"
          v-permission="['当前在船船员:修改工资']"
        >
          <v-icon left>mdi-eye</v-icon>
          查看船员工资
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editElectronicchart"
          v-permission="['当前在船船员:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改船员在船职务
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          @click="openInNewWindow"
          v-permission="['当前在船船员:导出名单']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          导出名单
        </v-btn>
      </template>
      <!--      <template v-slot:[`item.creFeature`]="{ item }">-->
      <!--        {{ item.creFeature }}-->
      <!--      </template>-->
      <template v-slot:[`item.crePropertyFeature`]="{ item }">
        {{ item.creProperty && item.creProperty.creFeature }}
      </template>
      <template v-slot:[`item.crePropertyType`]="{ item }">
        {{ item.creProperty && item.creProperty.creType }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ item.status ? '是' : '否' }}
      </template>
    </v-table-searchable>
    <v-crew-card v-model="dialog" :initialData="initialData"></v-crew-card>
    <on-broad-management-salary-detail
      v-model="dialog"
      :initialData="initialData"
    ></on-broad-management-salary-detail>
  </v-container>
</template>
<script>
import vCrewCard from '../private/v-crew-card.vue'
export default {
  components: { vCrewCard },
  name: 'on-broad-management',
  created() {
    this.tableName = '当前在船船员信息查询'
    this.reqUrl = '/business/crew/osmOnShipCrew/page'
    this.headers = [
      { text: '序号', value: 'seq' },
      { text: '在船职务', value: 'post' },
      { text: '证书职务', value: 'certificatePosition' },
      { text: '船员姓名', value: 'creName' },
      { text: '身份证号码', value: 'creIdNo' },
      { text: '上船船名', value: 'shipName' },
      { text: '船员管理公司', value: 'creCompany' },
      { text: '船员类型', value: 'crePropertyFeature' },
      { text: '劳动合同签署公司', value: 'crePropertyType' },
      { text: '上船时间', value: 'onBoardTime' },
      { text: '累计在船天数', value: 'onShipDays' },
      { text: '手机号', value: 'telephone', hideDefault: true },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      secondInfo: [],
      creThird: [],
      formShow: false,
      searchRemain: {},
      newInfo: {},
      isShow: false,
      initialData: {},
      boatmanItem: {},
      selected: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      dialog: false,
      baseURL:
        'https://jk.sitc.com/webroot/decision/view/report?viewlet=Test%252FADMIN_CENTER%252FosmBoard.cpt&',
      salaryURL:
        'https://jk.sitc.com/webroot/decision/view/report?viewlet=Test%252FADMIN_CENTER%252FonBoardSalary.cpt&bypagesize__=false',
    }
  },

  methods: {
    dbclick(item) {
      this.initialData = { userId: item.creId }
      this.dialog = true
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data
    },
    async getCreSecond() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/secondProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.secondInfo = data
    },
    async getCreThird() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/list`,
        {
          creCompany: this.searchRemain.creCompany,
          creFeature: this.searchRemain.creFeature,
        },
      )
      if (errorRaw) {
        return
      }
      this.creThird = []
      console.log(this.creThird)
      this.creThird = data.records.map((val) => {
        return { text: val.creType, value: val.id }
      })
      // this.creThirdAll = data.records
    },
    openInNewWindow() {
      let url = this.baseURL + '__bypagesize__=false&'
      let shipcode = this.$refs.table.ship
      url = url + 'shipcode=' + shipcode
      window.open(url, '_blank')
    },
    openNewWindow() {
      let url = this.salaryURL
      window.open(url, '_blank')
    },
    editElectronicchart() {
      this.boatmanItem = { ...this.selected }

      this.isShow = true
    },
    closeForm() {
      this.selected = false
      this.boatmanItem = {}
      this.isShow = false
    },
    async save() {
      if (!(await this.$dialog.msgbox.confirm('是否确认修改？'))) return
      const url = '/business/crew/osmOnShipCrew/modifyOsmOnShipCrew'
      const { errorRaw } = await this.postAsync(url, this.boatmanItem)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      this.selected = false
      this.isShow = false
      this.boatmanItem = {}
      await this.$refs.table.loadTableData()
    },
    GoTOSalary() {
      console.log(this.selected)
      this.$router.push({
        name: 'on-broad-salary-detail',
        params: {
          id: this.selected.id,
        },
      })
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
    },
  },

  async mounted() {
    await this.getCreFirst()
    await this.getCreThird()
    await this.getCreSecond()
  },
}
</script>

<style></style>
