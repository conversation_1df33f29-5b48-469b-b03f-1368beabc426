<template>
  <v-container fluid>
    <v-card v-if="isShow">
      <v-card-title>
        休假原因及备注填写
        <v-spacer></v-spacer>
        <v-icon @click="isShow = false">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-col>
          <v-textarea
            label="超期公休原因"
            outlined
            dense
            v-model="reason"
          ></v-textarea>
        </v-col>
        <v-col>
          <v-textarea
            label="备注"
            outlined
            dense
            v-model="remark2"
          ></v-textarea>
        </v-col>
        <v-col cols="12">
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            :loading="loading1"
            @click="save"
            block
            v-permission="['公休船员名单:保存']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            保存
          </v-btn>
        </v-col>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :specialHeaders="specialHeaders"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :search-date="searchDate"
      useShip
      @dbclick="dbclick"
      :showExportButton="true"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            v-model="searchRemain.creName"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员身份证号"
            outlined
            dense
            v-model="searchRemain.idNumber"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="在船职务"
            v-model="searchRemain.post"
            clearable
          ></v-ship-station>
        </v-col>
        <!-- <v-col cols="12" md="2">
          <v-select
            label="船员类型"
            outlined
            dense
            v-model="searchRemain.creType"
            :items="[
              { text: '自有', value: 1 },
              { text: '外聘', value: 2 },
            ]"
            clearable
          ></v-select>
        </v-col> -->
        <v-col cols="12" md="2">
          <v-select
            label="船员管理公司"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.creCompany"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员性质"
            outlined
            dense
            required
            :items="secondInfo"
            v-model="searchRemain.creFeature"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="劳动合同签署公司"
            required
            :items="creThird"
            v-model="searchRemain.creContract"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="公休时间是否超出120天"
            outlined
            dense
            :items="[{ text: '是', value: true }]"
            clearable
            v-model="searchRemain.over120Days"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          @click="openInNewWindow"
          v-permission="['公休船员名单:导出名单']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          导出名单
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="isShow = true"
          v-permission="['公休船员名单:超法定假期原因填写']"
        >
          <v-icon left>mdi-pencil</v-icon>
          休假原因及备注填写
        </v-btn>
      </template>
      <template v-slot:[`item.crePropertyFeature`]="{ item }">
        {{ item.creProperty && item.creProperty.creFeature }}
      </template>
      <template v-slot:[`item.crePropertyType`]="{ item }">
        {{ item.creProperty && item.creProperty.creType }}
      </template>
      <template v-slot:[`item.delay`]="{ item }">
        <v-chip v-if="item.delay" color="error" small dark>
          超出法定公休天数
        </v-chip>
        <v-chip v-else color="success" small dark>未超出法定公休天数</v-chip>
      </template>
    </v-table-searchable>
    <v-crew-card v-model="dialog" :initialData="initialData"></v-crew-card>
  </v-container>
</template>
<script>
import vCrewCard from '../private/v-crew-card.vue'
export default {
  components: { vCrewCard },
  name: 'vacation-crew',
  created() {
    this.reqUrl = '/business/crew/osmOnShipCrew/holiday/getHolidayCrew'
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '身份证号码', value: 'creIdNo' },
      //{ text: '船员类型', value: 'creType' },
      { text: '船员管理公司', value: 'creCompany' },
      { text: '船员类型', value: 'crePropertyFeature' },
      { text: '劳动合同签署公司', value: 'crePropertyType' },
      { text: '下船船舶', value: 'shipName' },
      { text: '在船职务', value: 'post' },
      { text: '上船时间', value: 'onBoardTime' },
      { text: '下船时间', value: 'offBoardTime' },
      { text: '在船天数', value: 'onShipDays' },
      { text: '已公休天数', value: 'onHolidays' },
      { text: '法定公休天数', value: 'numHoliday' },
      { text: '是否超出法定公休', value: 'delay' },
      { text: '超期休假原因', value: 'reason' },
      { text: '备注', value: 'remark2' },
    ]
    this.specialHeaders = [
      {
        text: 'delay',
        value: [
          { text: true, value: '超出法定公休天数' },
          { text: false, value: '未超出法定公休天数' },
        ],
      },
    ]
    this.searchDate = {
      label: '下船日期',
      interval: true,
    }
    this.fuzzyLabel = ''
  },

  data() {
    return {
      secondInfo: [],
      creThird: [],
      newInfo: {},
      tableName: '',
      selected: false,
      loading1: false,
      searchRemain: {},
      isShow: false,
      reason: '',
      remark2: '',
      dialog: false,
      initialData: {},
      number: 0,
      baseURL:
        'https://jk.sitc.com/webroot/decision/view/report?viewlet=Test%252FADMIN_CENTER%252FosmHoliday.cpt&',
      specialHeaders: [
        {
          label: 'delay',
          options: [
            { key: true, label: '超出法定公休天数' },
            { key: false, label: '未超出法定公休天数' },
          ],
        },
      ],
    }
  },

  methods: {
    async save() {
      if (!this.selected.delay && this.reason) {
        this.$dialog.message.error('该船员未超期法定休假日期！')
        return
      }
      this.loading1 = true
      const { errorRaw } = await this.getAsync(
        `/business/crew/osmOnShipCrew/holiday/addReason`,
        { id: this.selected.id, reason: this.reason, remark2: this.remark2 },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('提交成功')
      this.isShow = false
      await this.$refs.table.loadTableData()
    },
    async getVacNumber() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/osmOnShipCrew/total`,
        { type: 2 },
      )
      if (errorRaw) {
        return
      }
      this.tableName = `公休船员名单管理---公休船员${data}人`
    },
    dbclick(item) {
      this.initialData = { userId: item.creId }
      this.dialog = true
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data
    },
    async getCreSecond() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/secondProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.secondInfo = data
    },
    async getCreThird() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/list`,
        {
          creCompany: this.searchRemain.creCompany,
          creFeature: this.searchRemain.creFeature,
        },
      )
      if (errorRaw) {
        return
      }
      this.creThird = []
      console.log(this.creThird)
      this.creThird = data.records.map((val) => {
        return { text: val.creType, value: val.id }
      })
      // this.creThirdAll = data.records
    },
    openInNewWindow() {
      let url = this.baseURL + '__bypagesize__=false&'
      let shipcode = this.$refs.table.ship
      url = url + 'shipcode=' + shipcode
      window.open(url, '_blank')
    },
  },

  // mounted() {
  //   this.getVacNumber()
  // },
  async mounted() {
    await this.getCreFirst()
    await this.getCreThird()
    await this.getCreSecond()
    this.getVacNumber()
  },
}
</script>

<style></style>
