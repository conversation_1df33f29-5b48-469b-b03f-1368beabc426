<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="船员姓名"
                  outlined
                  dense
                  disabled
                  v-model="formData.creName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="实际工资"
                  outlined
                  dense
                  v-model="formData.actualSalary"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="20" md="5">
                <vs-date-picker
                  v-model="formData.actualSalaryBeginDate"
                  label="实际工资开始时间"
                  outlined
                  dense
                  required
                ></vs-date-picker>
              </v-col>
              <v-col cols="50" md="12">
                <v-text-field
                  label="实际工资变动原因"
                  outlined
                  dense
                  v-model="formData.changeSalaryReason"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-crew-onBoatSalary',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    initSelected: {
      type: Object,
      default: () => {},
    },
  },
  created() {},
  data() {
    return {
      dialog: false,
      formData: {},
      searchRemain: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()

      this.formData = this.initialData
      console.log('-----------')

      console.log(this.formData)
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = `/business/crew/osmOnShipCrew/salaryDetailUpdate`
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      if (errorRaw) {
        this.$emit('change', false)

        return
      }
      this.$dialog.message.success('修改成功')
      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
