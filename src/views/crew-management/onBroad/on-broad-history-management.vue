<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      :search-date="searchDate"
      useShip
      @dbclick="dbclick"
      :showExportButton="true"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            required
            v-model="searchRemain.creName"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员身份证号"
            outlined
            dense
            required
            v-model="searchRemain.idNumber"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="在船职务"
            v-model="searchRemain.post"
            clearable
          ></v-ship-station>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员管理公司"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.creCompany"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员性质"
            outlined
            dense
            required
            :items="secondInfo"
            v-model="searchRemain.creFeature"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="劳动合同签署公司"
            required
            :items="creThird"
            v-model="searchRemain.creContract"
            clearable
          ></v-select>
        </v-col>
        <!-- <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="船员类型"
            :items="[
              { text: '自有', value: 1 },
              { text: '外聘', value: 2 },
            ]"
            clearable
            v-model="searchRemain.creType"
          ></v-select>
        </v-col> -->
        <v-col cols="12" md="2">
          <v-select
            label="是否休假"
            outlined
            dense
            required
            v-model="searchRemain.status"
            clearable
            :items="[
              { text: '是', value: true },
              { text: '否', value: false },
            ]"
          ></v-select>
        </v-col>
      </template>
      <template #btns></template>
      <template v-slot:[`item.crePropertyFeature`]="{ item }">
        {{ item.creProperty && item.creProperty.creFeature }}
      </template>
      <template v-slot:[`item.crePropertyType`]="{ item }">
        {{ item.creProperty && item.creProperty.creType }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ item.status ? '是' : '否' }}
      </template>
    </v-table-searchable>
    <v-crew-card :initialData="initialData" v-model="dialog"></v-crew-card>
  </v-container>
</template>
<script>
import vCrewCard from '../private/v-crew-card.vue'
export default {
  components: { vCrewCard },
  name: 'on-broad-history-management',
  created() {
    this.tableName = '历史在船船员信息查询'
    this.reqUrl = '/business/crew/osmOnShipCrew/historyOnShip/page'
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '身份证号码', value: 'creIdNo' },
      { text: '上船船名', value: 'shipName' },
      { text: '船员管理公司', value: 'creCompany' },
      { text: '船员类型', value: 'crePropertyFeature' },
      { text: '劳动合同签署公司', value: 'crePropertyType' },
      //{ text: '船员类型', value: 'creType' },
      { text: '上船时间', value: 'onBoardTime' },
      { text: '下船时间', value: 'offBoardTime' },
      { text: '累计公休天数', value: 'numHoliday' },
      { text: '在船职务', value: 'post' },
    ]
    this.searchDate = {
      label: '在船期间',
      interval: true,
    }
    this.fuzzyLabel = ''
  },

  data() {
    return {
      formShow: false,
      secondInfo: [],
      creThird: [],
      searchRemain: {},
      initialData: {},
      newInfo: {},
      selected: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      dialog: false,
    }
  },

  methods: {
    dbclick(item) {
      this.initialData = { userId: item.creId }
      this.dialog = true
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data
    },
    async getCreSecond() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/secondProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.secondInfo = data
    },
    async getCreThird() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/list`,
        {
          creCompany: this.searchRemain.creCompany,
          creFeature: this.searchRemain.creFeature,
        },
      )
      if (errorRaw) {
        return
      }
      this.creThird = []
      console.log(this.creThird)
      this.creThird = data.records.map((val) => {
        return { text: val.creType, value: val.id }
      })
      // this.creThirdAll = data.records
    },
  },

  async mounted() {
    await this.getCreFirst()
    await this.getCreThird()
    await this.getCreSecond()
  },
}
</script>

<style></style>
