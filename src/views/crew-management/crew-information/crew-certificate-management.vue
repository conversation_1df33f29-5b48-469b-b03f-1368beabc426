<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      table-name="船员个人证书管理"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      :push-params="pushParams"
      item-key="id"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            v-model="searchRemain.name"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="身份证号"
            outlined
            dense
            v-model="searchRemain.idCard"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="证书类型"
            :items="category"
            v-model="searchRemain.certificateType"
            clearable
          ></v-select>
        </v-col>
      </template>

      <template #btns>
        <v-btn
          color="warning"
          @click="submitAudit"
          :disabled="selected.length === 0"
          v-permission="['船员个人证书管理详情:列表']"
        >
          <v-icon left>mdi-send-check</v-icon>
          提交审批
        </v-btn>
        <v-btn color="primary" @click="addCertificate">
          <v-icon left>mdi-plus</v-icon>
          新增证书
        </v-btn>
      </template>

      <template v-slot:[`item.expiryDate`]="{ item }">
        <v-chip :color="getExpiryColor(item.expiryDate)" small dark>
          {{ item.expiryDate }}
        </v-chip>
      </template>
      <template v-slot:[`item.remaining_months`]="{ item }">
        <v-chip
          color="red"
          v-if="
            item.remaining_months <= 6 &&
            item.remaining_months > 0 &&
            item.availableFlag
          "
          dark
          small
        >
          {{ item.remaining_months }}个月
        </v-chip>
        <v-chip
          color="orange"
          v-else-if="
            item.remaining_months > 6 &&
            item.remaining_months <= 12 &&
            item.availableFlag
          "
          dark
          small
        >
          {{ item.remaining_months }}个月
        </v-chip>
        <v-chip
          color=""
          v-else-if="item.remaining_months < 0 && item.availableFlag"
          dark
          small
        >
          证书过期
        </v-chip>
        <v-chip color="green" v-else-if="item.availableFlag" dark small>
          {{ item.remaining_months }}个月
        </v-chip>
        <v-chip v-else></v-chip>
      </template>
      <template v-slot:[`item.approvalStatus`]="{ item }">
        <v-chip v-if="item.approvalStatus === 0" small dark>草稿</v-chip>
        <v-chip v-else-if="item.approvalStatus === 1" small dark color="info">
          审核中
        </v-chip>
        <v-chip
          v-else-if="item.approvalStatus === 2"
          small
          dark
          color="success"
        >
          审核完成
        </v-chip>
        <v-chip v-else-if="item.approvalStatus === -1" small dark color="error">
          审核不通过
        </v-chip>

        <v-chip v-else small dark color="error">错误状态</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>

<script>
export default {
  name: 'crew-certificate-management',
  data() {
    return {
      selected: [],
      searchRemain: {
        name: '',
        idCard: '',
        certificateType: '',
      },
      category: [
        { text: '海员证', value: 'SEAMAN' },
        { text: '培训合格证书', value: 'TRAINING' },
        { text: '服务簿', value: 'CREW_SERVICE' },
        { text: '适任证书', value: 'COMPETENCY' },
        { text: '海事局健康证书', value: 'MARITIME_HEALTH' },
        { text: '护照', value: 'PASSPORT' },
        { text: '香港适任证书', value: 'HONG_KONG_COMPETENCY' },
        { text: '巴拿马证书', value: 'PANAMA_COMPETENCY' },
        { text: '巴拿马GMDSS证书', value: 'PANAMA_COMPETENCY_GMDSS' },
        { text: '巴拿马保安员背书SSO', value: 'PANAMA_COMPETENCY_SSO' },
        { text: '巴拿马保安背书SDSD', value: 'PANAMA_COMPETENCY_SDSD' },
        { text: '巴拿马大厨背书', value: 'PANAMA_CHIEF' },
        { text: '船上厨师培训合格证明', value: 'MCL_CHIEF' },
        { text: '卫检健康证', value: 'HEALTH_INSPECTION' },
        { text: '黄热', value: 'YELLOW_FEVER_VACCINE' },
        { text: '霍乱', value: 'CHOLERA' },
      ],
      headers: [
        { text: '证书编号', value: 'code' },
        { text: '证书名称', value: 'category' },
        {
          text: '证书有效期限',
          value: 'remaining_months',
        },
        { text: '签发日期', value: 'signDate' },
        { text: '到期日期', value: 'expireDate' },
        { text: '证书状态', value: 'status' },
        { text: '审批状态', value: 'approvalStatus' },
        { text: '审批人', value: 'approvalUserName' },

        { text: '附件', value: 'attachmentRecords', sortable: false },
      ],
      reqUrl: '/business/crew/certificate/personalPage',
      fuzzyLabel: '',
      pushParams: {
        name: 'crew-certificate-management-detail',
      },
    }
  },
  methods: {
    getExpiryColor(expiryDate) {
      const today = new Date()
      const expiry = new Date(expiryDate)
      const diffTime = expiry - today
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays < 0) return 'error' // 已过期
      if (diffDays < 30) return 'warning' // 即将过期
      return 'success' // 有效
    },
    addCertificate() {
      this.$router.push({
        name: 'crew-certificate-management-detail',
        params: { id: 'new' },
      })
    },
    async submitAudit() {
      console.log(this.selected)
      try {
        let newSelected = []
        newSelected.push(this.selected.id)
        console.log(newSelected)
        const { errorRaw } = await this.postAsync(
          '/business/crew/certificate/submitAudit',
          newSelected,
        )
        if (errorRaw) {
          this.$dialog.message.error(errorRaw.message)
          return
        }
        this.$dialog.message.success('提交审批成功')
        await this.$refs.table.loadTableData()
      } catch (error) {
        console.error('提交失败:', error)
        this.$dialog.message.error('提交失败，请检查网络或数据')
      }
    },
  },
}
</script>

<style scoped>
/* 可根据需要添加样式 */
</style>
