<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :showExportButton="true"
      :fix-header="false"
      :search-remain="searchRemain"
      @dbclick="openCrewInfo"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            clearable
            v-model="searchRemain.name"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="电话号码"
            outlined
            dense
            clearable
            v-model="searchRemain.phoneNumber"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="实际职务"
            clearable
            v-model="searchRemain.actualPosition"
          ></v-ship-station>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="handleSpecial"
          v-permission="['船员特殊管理:操作']"
        >
          <v-icon left>mdi-alert-circle</v-icon>
          移除特殊管理
        </v-btn>
      </template>
      <template v-slot:[`item.inSpeciallist`]="{ item }">
        <v-chip v-if="item.inSpeciallist === 0" small dark>草稿</v-chip>
        <v-chip v-else-if="item.inSpeciallist === 1" small dark color="warning">
          警告
        </v-chip>
        <v-chip v-else-if="item.inSpeciallist === 2" small dark color="error">
          慎用
        </v-chip>

        <v-chip v-else small dark color="error">错误状态</v-chip>
      </template>
    </v-table-searchable>
    <v-crew-card v-model="dialog" :initialData="initialData"></v-crew-card>
  </v-container>
</template>

<script>
import vCrewCard from '../private/v-crew-card.vue'

export default {
  components: { vCrewCard },
  name: 'special-crew',
  created() {
    this.tableName = '船员特殊管理'
    this.reqUrl = '/business/crew/baseInfo/specialList'
    this.headers = [
      { text: '中文姓名', value: 'chName' },
      { text: '身份证号', value: 'idCard' },
      { text: '出生日期', value: 'birthDate' },
      { text: '手机号', value: 'phone' },
      { text: '船员特殊情况', value: 'inSpeciallist' },
      { text: '船员特殊情况说明', value: 'specialRemark' },
      { text: '实际职务', value: 'actualPosition' },
      { text: '证书职务', value: 'certificatePosition' },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      initialData: {},
      dialog: false,
    }
  },

  methods: {
    async handleSpecial() {
      if (!(await this.$dialog.msgbox.confirm('确定执行该特殊操作？'))) return

      const { errorRaw } = await this.getAsync(
        '/business/crew/baseInfo/specialManagement',
        {
          crewId: this.selected.id,
          operation: false,
          specialStatus: 0,
          specialRemark: '',
        },
      )

      if (!errorRaw) {
        this.$dialog.message.success('操作成功')
        await this.$refs.table.loadTableData()
      }
    },

    openCrewInfo(val) {
      this.initialData = { userId: val.userId }
      this.dialog = true
    },
  },

  mounted() {},
}
</script>

<style></style>
