<template>
  <v-sheet outlined>
    <v-data-table
      :headers="headers"
      hide-default-footer
      item-key="id"
      dense
      :items="items"
    >
      <template v-slot:top>
        <v-card-title class="py-1" flat>
          <v-toolbar-title>
            {{ title }}
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <input
            ref="uploader"
            class="d-none"
            type="file"
            :accept="accept"
            @change="onFileChanged"
          />
        </v-card-title>
      </template>
      <template v-slot:[`item.name`]="{ item }">
        <div v-if="item.isDelete" class="text-decoration-line-through">
          {{ item.name }}
        </div>
        <div v-else-if="!item.initial" class="blue--text">
          {{ item.name }}-新增
        </div>
        <div v-else>{{ item.name }}</div>
      </template>
      <template v-slot:[`item.action`]="{ item }">
        <v-btn
          :href="`/api/system/file/download?fileName=${encodeURIComponent(
            item.name,
          )}&filePath=${item.filePath}`"
          target="_blank"
          icon
          small
          class="mr-1"
        >
          <v-icon>mdi-download</v-icon>
        </v-btn>
      </template>
    </v-data-table>
  </v-sheet>
</template>
<script>
export default {
  name: 'v-attachmentReadOnly',
  data() {
    return {
      headers: [
        { text: '名称', value: 'name' },
        { text: '大小(kb)', value: 'fileSize' },
        { text: '上传时间', value: 'createTime' },
        { text: '上传人', value: 'userName' },
        { text: '操作', value: 'action', sortable: false },
      ],
      items: [],
      isSelecting: false,
    }
  },
  props: {
    title: {
      type: String,
      default: '附件选择',
    },
    attachment: {
      type: Object,
      default: () => {},
    },
    disabled: {
      type: [Boolean, String],
      default: false,
    },
    readonly: {
      type: [Boolean, String],
      default: false,
    },
    accept: {
      type: String,
      default: '.jpg, .jpeg, .png',
    },
  },
  watch: {
    attachment: {
      handler(val) {
        if (val) {
          this.items = [
            {
              ...val,
              fileSize: val?.fileSize,
              isDelete: false,
              initial: true,
            },
          ]
        }
      },
    },
    items: {
      handler(val) {
        if (val.length === 0) {
          this.$emit('change', 0)
        } else {
          const attachmentId = val[0].id
          this.$emit('change', attachmentId)
        }
      },
    },
  },

  methods: {
    async onFileChanged(e) {
      this.isSelecting = true
      let formData = new FormData()
      formData.append('file', e.target.files[0])
      const { errorRaw, data } = await this.postAsync(
        '/system/file/upload',
        formData,
      )
      if (!errorRaw) {
        this.items.push({
          ...data,
          fileSize: data.fileSize / 1024,
          initial: false,
          isDelete: false,
        })
      }
      this.isSelecting = false
    },
  },

  computed: {},

  mounted() {},
}
</script>

<style></style>
