<template>
  <v-container fluid>
    <v-btn @click="onButtonClick" :loading="isSelecting" v-if="upLoad">
      <v-icon left>mdi-plus-circle</v-icon>
      上传
    </v-btn>
    <input
      type="file"
      class="d-none"
      ref="uploader"
      accept=".jpg, .jpeg, .png"
      @change="onFileChanged"
    />
    <v-img
      v-if="!srcGet"
      src="@/assets/crewDefault.png"
      height="200"
      width="200"
      alt="图片预览"
    ></v-img>
    <v-img v-else :src="srcGet" height="200" width="200" alt="图片预览"></v-img>
  </v-container>
</template>
<script>
export default {
  name: 'v-crewPic',
  props: {
    ImagePicture: {
      type: Object,
      default: () => {},
    },
    upLoad: {
      type: [String, Boolean],
      default: true,
    },
  },
  data() {
    return {
      srcGet: '',
      srcId: '',
      isSelecting: false,
    }
  },

  methods: {
    onButtonClick() {
      this.$refs.uploader.click()
    },
    async onFileChanged(e) {
      this.isSelecting = true
      let formData = new FormData()
      formData.append('file', e.target.files[0])
      const { errorRaw, data } = await this.postAsync(
        '/system/file/upload',
        formData,
      )
      if (!errorRaw) {
        this.srcGet = data.url
        this.srcId = data.id
      }
      this.isSelecting = false
    },
  },

  watch: {
    ImagePicture: {
      handler(val) {
        this.srcGet = val?.url
      },
      deep: true,
    },
    srcId: {
      handler(val) {
        this.$emit('change', val)
      },
    },
  },

  mounted() {},
}
</script>

<style></style>
