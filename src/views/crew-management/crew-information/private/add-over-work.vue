<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ isEdit ? '新增' : '修改' }}--船员履历信息1
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="船名"
                  outlined
                  dense
                  v-model="formData.shipName"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="船舶类型"
                  outlined
                  dense
                  v-model="formData.shipType"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="上船时间"
                  outlined
                  dense
                  v-model="formData.startDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="上船地点"
                  outlined
                  dense
                  v-model="formData.upBoardPlace"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="下船时间"
                  outlined
                  dense
                  v-model="formData.endDate"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="下船地点"
                  outlined
                  dense
                  v-model="formData.offBoardPlace"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  label="下船原因"
                  outlined
                  dense
                  v-model="formData.leaveReason"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  label="在职天数"
                  outlined
                  dense
                  type="number"
                  v-model="formData.officeDay"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="任职部门"
                  outlined
                  dense
                  :items="['甲板部', '轮机部']"
                  v-model="formData.officeDepartment"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-ship-station
                  label="任职岗位"
                  v-model="formData.officeJob"
                ></v-ship-station>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  label="船舶归属"
                  outlined
                  dense
                  :items="[
                    { text: '本公司的船', value: 1 },
                    { text: '其他公司的船', value: 2 },
                  ]"
                  v-model="formData.selfShipFlag"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="载重吨（DWT）"
                  outlined
                  dense
                  v-model="formData.dwt"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="总吨（GT）"
                  outlined
                  dense
                  v-model="formData.gt"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="主机类型"
                  outlined
                  dense
                  v-model="formData.hostType"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="主机功率"
                  outlined
                  dense
                  type="number"
                  v-model="formData.hostPower"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="船员隶属公司"
                  outlined
                  dense
                  v-model="formData.crewCompany"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="船东评价"
                  outlined
                  dense
                  rows="3"
                  v-model="formData.opinion"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'add-over-work',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
    'formData.startDate': function () {
      this.calculateDates()
    },
    'formData.endDate': function () {
      this.calculateDates()
    },
  },
  methods: {
    calculateDates() {
      console.log('执行')
      if (!this.formData.startDate || !this.formData.endDate) {
        return
      }
      console.log(this.formData)
      this.formData.officeDay =
        this.$moment(this.formData.endDate).diff(
          this.formData.startDate,
          'days',
        ) + 1
    },
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.$emit('change', false)
      this.$emit('success', this.formData)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
