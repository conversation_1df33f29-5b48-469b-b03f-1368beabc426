<template>
  <v-dialog attach="#mask" hide-overlay v-model="dialog" max-width="1100">
    <template v-slot:activator="{ on, attrs }">
      <v-btn
        v-on="on"
        v-bind="attrs"
        outlined
        tile
        color="success"
        :disabled="disabled"
        class="mx-1"
        dense
      >
        <v-icon left>mdi-plus-circle</v-icon>
        {{ tag ? '新增' : '修改' }}
      </v-btn>
    </template>
    <v-card>
      <v-card-title>{{ tableName }}</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6" md="3">
            <v-select
              label="调配公司"
              outlined
              dense
              :items="['山东船员', '上海船员', '厦门船员']"
              v-model="searchRemain.creCompany"
            ></v-select>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-select
              label="船员性质"
              outlined
              dense
              :items="['自有船员', '外聘船员']"
              v-model="searchRemain.creFeature"
            ></v-select>
          </v-col>
        </v-row>
        <v-table-list
          ref="table"
          v-model="selected"
          :headers="headers"
          :items="items"
        >
          <template v-for="h in headers" v-slot:[`item.${h.value}`]="{ item }">
            <slot :item="item" :name="`item.${h.value}`"></slot>
          </template>
        </v-table-list>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="dialog = false">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
export default {
  name: 'v-CreproDialog',
  data() {
    return {
      selected: false,
      dialog: false,
      items: [],
      searchRemain: {},
    }
  },
  props: {
    tableName: {
      type: String,
    },
    disabled: {
      type: [String, Boolean],
      default: false,
    },
    reqUrl: {
      type: String,
    },
    headers: {
      type: Array,
    },
    itemKey: {
      type: String,
      default: 'id',
    },
    tag: {
      type: [String, Boolean],
      default: true,
    },
  },

  watch: {
    searchRemain: {
      handler() {
        this.getInfo()
      },
      deep: true,
    },
  },

  methods: {
    async getInfo() {
      const { errorRaw, data } = await this.getAsync(
        '/business/crew/crewProperty/list',
        {
          ...this.searchRemain,
          status: true,
        },
      )
      if (!errorRaw) {
        this.items = data.records
      }
    },
    confirm() {
      this.$emit('update', this.selected)
      this.dialog = false
    },
  },

  mounted() {},
}
</script>

<style></style>
