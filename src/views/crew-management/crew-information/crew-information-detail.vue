<template>
  <v-container fluid>
    <v-form ref="form">
      <v-detail-view
        v-permission="['船员信息管理:编辑']"
        :title="title"
        :tooltip="title"
        :backRouteName="backRouteName"
        :subtitles="subtitles"
        :can-submit="detailInfo.id && canSave"
        :can-save="!detailInfo.id"
        @save="save"
        @submit="submit"
      >
        <template v-slot:个人填写>
          <v-container fluid>
            <v-expansion-panels multiple accordion v-model="panel_1" focusable>
              <v-expansion-panel>
                <v-expansion-panel-header>基本信息</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="2" v-if="detailInfo.empId">
                        <v-text-field
                          label="工号"
                          outlined
                          dense
                          :rules="[rules.required]"
                          v-model="detailInfo.empId"
                          required
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="中文姓名"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.chName"
                          @change="
                            differ(detailInfoCopy1.chName, detailInfo.chName)
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="英文姓名"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.enName"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="身份证号码"
                          outlined
                          dense
                          :rules="[rules.idcard]"
                          required
                          v-model="detailInfo.idCard"
                          @change="
                            differ(detailInfoCopy1.idCard, detailInfo.idCard)
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="民族"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.nation"
                          @change="
                            differ(detailInfoCopy1.nation, detailInfo.nation)
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <vs-date-picker
                          outlined
                          dense
                          readonly
                          label="出生日期"
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.birthDate"
                        ></vs-date-picker>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          label="性别"
                          outlined
                          dense
                          :items="[
                            { text: '男', value: '1' },
                            { text: '女', value: '2' },
                          ]"
                          :rules="[rules.required]"
                          v-model="detailInfo.gender"
                          @change="
                            differ(detailInfoCopy1.gender, detailInfo.gender)
                          "
                          required
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          label="国家"
                          outlined
                          dense
                          :items="coutryItems"
                          :rules="[rules.required]"
                          v-model="detailInfo.country"
                          @change="
                            differ(detailInfoCopy1.country, detailInfo.country)
                          "
                          required
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="国籍"
                          outlined
                          dense
                          :rules="[rules.required]"
                          readonly
                          required
                          v-model="detailInfo.countryCode"
                          @change="
                            differ(
                              detailInfoCopy1.countryCode,
                              detailInfo.countryCode,
                            )
                          "
                        ></v-text-field>
                      </v-col>

                      <v-col cols="12" md="2">
                        <v-select
                          label="婚姻状况"
                          outlined
                          dense
                          :items="['未婚', '已婚', '离异']"
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.maritalStatus"
                          @change="
                            differ(
                              detailInfoCopy1.maritalStatus,
                              detailInfo.maritalStatus,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          label="政治面貌"
                          outlined
                          dense
                          :items="[
                            '共产党员',
                            '共青团员',
                            '群众',
                            '预备党员',
                            '民主党派成员',
                          ]"
                          v-model="detailInfo.politicsStatus"
                          @change="
                            differ(
                              detailInfoCopy1.politicsStatus,
                              detailInfo.politicsStatus,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="宗教信仰"
                          outlined
                          dense
                          v-model="detailInfo.religion"
                          @change="
                            differ(
                              detailInfoCopy1.religion,
                              detailInfo.religion,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="外语语种"
                          outlined
                          dense
                          v-model="detailInfo.foreignLanguages"
                          @change="
                            differ(
                              detailInfoCopy1.foreignLanguages,
                              detailInfo.foreignLanguages,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="外语水平"
                          outlined
                          dense
                          v-model="detailInfo.foreignLevel"
                          @change="
                            differ(
                              detailInfoCopy1.foreignLevel,
                              detailInfo.foreignLevel,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="籍贯"
                          dense
                          outlined
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.nativePlace"
                          @change="
                            differ(
                              detailInfoCopy1.nativePlace,
                              detailInfo.nativePlace,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          label="特殊情况"
                          outlined
                          dense
                          :items="[
                            { text: '无', value: 0 },
                            { text: '警告', value: 1 },
                            { text: '慎用', value: 2 },
                          ]"
                          v-model="detailInfo.inSpeciallist"
                          readonly
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="特殊情况说明"
                          dense
                          outlined
                          required
                          v-model="detailInfo.specialRemark"
                          readonly
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-textarea
                      outlined
                      dense
                      label="有无病史(个人填写)"
                      v-model="detailInfo.medicalHistory"
                      @change="
                        differ(
                          detailInfoCopy1.medicalHistory,
                          detailInfo.medicalHistory,
                        )
                      "
                    ></v-textarea>
                    <v-row>
                      <v-col cols="12" md="2">
                        <v-select
                          label="有无结石"
                          outlined
                          required
                          dense
                          :items="[
                            { text: '有', value: 'true' },
                            { text: '无', value: 'false' },
                          ]"
                          :rules="[rules.required]"
                          v-model="detailInfo.stoneFlag"
                          @change="
                            differ(
                              detailInfoCopy1.stoneFlag,
                              detailInfo.stoneFlag,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="10">
                        <v-text-field
                          label="结石情况说明"
                          dense
                          outlined
                          required
                          v-model="detailInfo.stoneRemark"
                          @change="
                            differ(
                              detailInfoCopy1.stoneRemark,
                              detailInfo.stoneRemark,
                            )
                          "
                          readonly
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col cols="12" md="2">
                        <v-select
                          label="有无因病下船史"
                          outlined
                          required
                          dense
                          :items="[
                            { text: '有', value: 'true' },
                            { text: '无', value: 'false' },
                          ]"
                          :rules="[rules.required]"
                          v-model="detailInfo.illnessFlag"
                          @change="
                            differ(
                              detailInfoCopy1.illnessFlag,
                              detailInfo.illnessFlag,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="10">
                        <v-text-field
                          label="因病下船情况说明"
                          dense
                          outlined
                          required
                          v-model="detailInfo.illnessRemark"
                          @change="
                            differ(
                              detailInfoCopy1.illnessRemark,
                              detailInfo.illnessRemark,
                            )
                          "
                          readonly
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <!-- <v-textarea
                      outlined
                      dense
                      label="有无病史(公司填写)"
                      v-model="detailInfo.medicalHistoryCom"
                      @change="
                        differ(
                          detailInfoCopy1.medicalHistoryCom,
                          detailInfo.medicalHistoryCom,
                        )
                      "
                    ></v-textarea> -->
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>体貌特征</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="身高"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.height"
                          @change="
                            differ(detailInfoCopy1.height, detailInfo.height)
                          "
                          suffix="cm"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="体重"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.weight"
                          @change="
                            differ(detailInfoCopy1.weight, detailInfo.weight)
                          "
                          suffix="kg"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="BMI"
                          outlined
                          dense
                          readonly
                          v-model="detailInfo.bmi"
                          :value="bmiValue"
                          suffix="kg/m²"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          label="血型"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          :items="['A', 'AB', 'B', 'O', '未知']"
                          v-model="detailInfo.bloodType"
                          @change="
                            differ(
                              detailInfoCopy1.bloodType,
                              detailInfo.bloodType,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          label="工作服尺码"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          :items="[160, 165, 170, 175, 180, 185, 190, 195, 200]"
                          v-model="detailInfo.workClothesSize"
                          @change="
                            differ(
                              detailInfoCopy1.workClothesSize,
                              detailInfo.workClothesSize,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          label="工作鞋尺码"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          :items="[
                            35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46,
                          ]"
                          v-model="detailInfo.workShoeSize"
                          @change="
                            differ(
                              detailInfoCopy1.workShoeSize,
                              detailInfo.workShoeSize,
                            )
                          "
                        ></v-select>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>联系方式</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="手机号"
                          outlined
                          dense
                          :rules="[rules.bandPhone]"
                          required
                          v-model="detailInfo.phone"
                          @change="
                            differ(detailInfoCopy1.phone, detailInfo.phone)
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="邮箱"
                          outlined
                          dense
                          v-model="detailInfo.email"
                          @change="
                            differ(detailInfoCopy1.email, detailInfo.email)
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col md="6">
                        <v-textarea
                          label="邮寄地址"
                          outlined
                          required
                          dense
                          v-model="detailInfo.mailingAddress"
                          @change="
                            differ(
                              detailInfoCopy1.mailingAddress,
                              detailInfo.mailingAddress,
                            )
                          "
                        ></v-textarea>
                      </v-col>
                      <v-col md="6">
                        <v-textarea
                          label="家庭住址（请精确到门牌号）"
                          outlined
                          required
                          dense
                          v-model="detailInfo.homeAddress"
                          @change="
                            differ(
                              detailInfoCopy1.homeAddress,
                              detailInfo.homeAddress,
                            )
                          "
                        ></v-textarea>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>家庭成员</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card>
                    <v-card-title>
                      <div></div>
                      <v-spacer></v-spacer>
                      <v-btn
                        outlined
                        tile
                        color="success"
                        class="mx-1"
                        @click="addFamily"
                        v-permission="['家庭成员:新增']"
                      >
                        <v-icon left>mdi-plus-circle</v-icon>
                        新增
                      </v-btn>
                      <v-btn
                        :disabled="!selected"
                        outlined
                        tile
                        color="warning"
                        class="mx-1"
                        @click="change"
                        v-permission="['家庭成员:修改']"
                      >
                        <v-icon left>mdi-pencil</v-icon>
                        修改
                      </v-btn>
                      <v-btn
                        :disabled="!selected"
                        outlined
                        tile
                        color="error"
                        class="mx-1"
                        @click="deleteItem"
                        v-permission="['家庭成员:删除']"
                      >
                        <v-icon left>mdi-delete-empty</v-icon>
                        删除
                      </v-btn>
                    </v-card-title>
                    <v-table-list
                      ref="table"
                      v-model="selected"
                      :headers="headers"
                      :items="detailInfo.familyMember"
                      item-key="name"
                    ></v-table-list>
                  </v-card>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>紧急联系人</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="紧急联系人"
                          outlined
                          dense
                          required
                          :rules="[rules.required]"
                          v-model="detailInfo.emergencyName"
                          @change="
                            differ(
                              detailInfoCopy1.emergencyName,
                              detailInfo.emergencyName,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="电话"
                          outlined
                          dense
                          required
                          :rules="[rules.bandPhone]"
                          v-model="detailInfo.emergencyPhone"
                          @change="
                            differ(
                              detailInfoCopy1.emergencyPhone,
                              detailInfo.emergencyPhone,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col>
                        <v-text-field
                          label="地址"
                          outlined
                          required
                          dense
                          :rules="[rules.required]"
                          v-model="detailInfo.emergencyAddress"
                          @change="
                            differ(
                              detailInfoCopy1.emergencyAddress,
                              detailInfo.emergencyAddress,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>教育经历</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="2">
                        <v-select
                          label="最高学历"
                          outlined
                          required
                          dense
                          :items="[
                            '本科及以上',
                            '专科及以上',
                            '专科以下、高中毕业',
                            '高中毕业及以下',
                          ]"
                          :rules="[rules.required]"
                          v-model="detailInfo.highestDegree"
                          @change="
                            differ(
                              detailInfoCopy1.highestDegree,
                              detailInfo.highestDegree,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="3">
                        <v-text-field
                          label="毕业院校"
                          outlined
                          required
                          dense
                          :rules="[rules.required]"
                          v-model="detailInfo.graduateSchool"
                          @change="
                            differ(
                              detailInfoCopy1.graduateSchool,
                              detailInfo.graduateSchool,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="3">
                        <vs-date-picker
                          dense
                          outlined
                          :rules="[rules.required]"
                          label="毕业时间"
                          required
                          v-model="detailInfo.graduationDate"
                        ></vs-date-picker>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>职务信息</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="3">
                        <v-ship-station
                          label="实际职务"
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.actualPosition"
                        ></v-ship-station>
                      </v-col>
                      <v-col cols="12" md="3">
                        <v-ship-station
                          label="证书职务"
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.certificatePosition"
                        ></v-ship-station>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>银行卡信息</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="人民币开户行人姓名"
                          outlined
                          dense
                          readonly
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.rmbName"
                          @change="
                            differ(detailInfoCopy1.rmbName, detailInfo.rmbName)
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-select
                          label="人民币开户行"
                          outlined
                          dense
                          :items="bankList"
                          :rules="[rules.bankName]"
                          required
                          v-model="detailInfo.rmbBank"
                          @change="
                            differ(detailInfoCopy1.rmbBank, detailInfo.rmbBank)
                          "
                          @keyup.native="
                            detailInfo.rmbBank = detailInfo.rmbBank.replace(
                              /\s+/g,
                              '',
                            )
                          "
                          clearable
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="人民币开户银行分支机构"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.rmbBankBranch"
                          @change="
                            differ(
                              detailInfoCopy1.rmbBankBranch,
                              detailInfo.rmbBankBranch,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="人民币卡号"
                          outlined
                          dense
                          :rules="[rules.bandIdCard]"
                          required
                          v-model="detailInfo.rmbCard"
                          @change="
                            differ(detailInfoCopy1.rmbCard, detailInfo.rmbCard)
                          "
                          @keyup.native="
                            detailInfo.rmbCard = detailInfo.rmbCard.replace(
                              /\s+/g,
                              '',
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-select
                          label="开户地省份"
                          outlined
                          dense
                          :items="provinces"
                          v-model="rmbAddressHead"
                          :rules="[rules.required]"
                          @change="differ(rmbAddressHeadOri, rmbAddressHead)"
                          required
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-select
                          label="开户地城市(区)"
                          outlined
                          dense
                          :items="cityItems"
                          v-model="rmbAddressTail"
                          :rules="[rules.required]"
                          @change="differ(rmbAddressTailOri, rmbAddressTail)"
                          required
                        ></v-select>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="伙食费卡号"
                          outlined
                          dense
                          required
                          v-model="detailInfo.mealsCard"
                          @change="
                            differ(
                              detailInfoCopy1.mealsCard,
                              detailInfo.mealsCard,
                            )
                          "
                          @keyup.native="
                            detailInfo.mealsCard = detailInfo.mealsCard.replace(
                              /\s+/g,
                              '',
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="伙食费卡银行开户行"
                          outlined
                          dense
                          required
                          v-model="detailInfo.mealsBank"
                          @change="
                            differ(
                              detailInfoCopy1.mealsBank,
                              detailInfo.mealsBank,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-select
                          label="伙食费卡开户地省份"
                          outlined
                          dense
                          :items="provinces"
                          v-model="mealsAddressHead"
                          @change="
                            differ(mealsAddressHeadOri, mealsAddressHead)
                          "
                          required
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-select
                          label="伙食费卡开户地城市(区)"
                          outlined
                          dense
                          :items="mealsCityItems"
                          v-model="mealsAddressTail"
                          @change="
                            differ(mealsAddressTailOri, mealsAddressTail)
                          "
                          required
                        ></v-select>
                      </v-col>
                    </v-row>
                    <!--                    <v-row>-->
                    <!--                      <v-col cols="12" md="4">-->
                    <!--                        <v-text-field-->
                    <!--                          label="美元开户行"-->
                    <!--                          dense-->
                    <!--                          outlined-->
                    <!--                          required-->
                    <!--                          v-model="detailInfo.usdBank"-->
                    <!--                          @change="-->
                    <!--                            differ(detailInfoCopy1.usdBank, detailInfo.usdBank)-->
                    <!--                          "-->
                    <!--                        ></v-text-field>-->
                    <!--                      </v-col>-->
                    <!--                      <v-col cols="12" md="4">-->
                    <!--                        <v-text-field-->
                    <!--                          label="美元开户银行分支机构中文名称"-->
                    <!--                          outlined-->
                    <!--                          required-->
                    <!--                          dense-->
                    <!--                          v-model="detailInfo.usdBankBranch"-->
                    <!--                          @change="-->
                    <!--                            differ(-->
                    <!--                              detailInfoCopy1.usdBankBranch,-->
                    <!--                              detailInfo.usdBankBranch,-->
                    <!--                            )-->
                    <!--                          "-->
                    <!--                        ></v-text-field>-->
                    <!--                      </v-col>-->
                    <!--                      <v-col cols="12" md="4">-->
                    <!--                        <v-text-field-->
                    <!--                          label="美元开户银行分支机构英文名称"-->
                    <!--                          outlined-->
                    <!--                          dense-->
                    <!--                          required-->
                    <!--                          v-model="detailInfo.usdBankBranchEn"-->
                    <!--                          @change="-->
                    <!--                            differ(-->
                    <!--                              detailInfoCopy1.usdBankBranchEn,-->
                    <!--                              detailInfo.usdBankBranchEn,-->
                    <!--                            )-->
                    <!--                          "-->
                    <!--                        ></v-text-field>-->
                    <!--                      </v-col>-->
                    <!--                      <v-col cols="12" md="4">-->
                    <!--                        <v-text-field-->
                    <!--                          label="美元卡号"-->
                    <!--                          outlined-->
                    <!--                          dense-->
                    <!--                          required-->
                    <!--                          v-model="detailInfo.usdCard"-->
                    <!--                          @change="-->
                    <!--                            differ(detailInfoCopy1.usdCard, detailInfo.usdCard)-->
                    <!--                          "-->
                    <!--                        ></v-text-field>-->
                    <!--                      </v-col>-->
                    <!--                      <v-col cols="12" md="4">-->
                    <!--                        <v-text-field-->
                    <!--                          label="美元开户人姓名"-->
                    <!--                          outlined-->
                    <!--                          dense-->
                    <!--                          required-->
                    <!--                          v-model="detailInfo.usdName"-->
                    <!--                          @change="-->
                    <!--                            differ(detailInfoCopy1.usdName, detailInfo.usdName)-->
                    <!--                          "-->
                    <!--                        ></v-text-field>-->
                    <!--                      </v-col>-->
                    <!--                      <v-col cols="12" md="4">-->
                    <!--                        <v-text-field-->
                    <!--                          label="SWIFT CODE"-->
                    <!--                          dense-->
                    <!--                          outlined-->
                    <!--                          required-->
                    <!--                          v-model="detailInfo.swiftCode"-->
                    <!--                          @change="-->
                    <!--                            differ(-->
                    <!--                              detailInfoCopy1.swiftCode,-->
                    <!--                              detailInfo.swiftCode,-->
                    <!--                            )-->
                    <!--                          "-->
                    <!--                        ></v-text-field>-->
                    <!--                      </v-col>-->
                    <!--                    </v-row>-->
                    <v-attachment-only
                      title="银行卡照片附件美元银行卡图片附件"
                      :attachment="detailInfo.bankCardPicAttachment"
                      @change="changeCardPic"
                    ></v-attachment-only>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>照片附件</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-crew-pic
                    :ImagePicture="detailInfo.identificationPhotoAttachment"
                    @change="changePicture"
                  ></v-crew-pic>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>档案信息</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="2">
                        <v-select
                          label="是否由公司保存档案"
                          outlined
                          dense
                          :items="[
                            { text: '是', value: true },
                            { text: '否', value: false },
                          ]"
                          v-model="detailInfo.archiveFlag"
                          @change="
                            differ(
                              detailInfoCopy1.archiveFlag,
                              detailInfo.archiveFlag,
                            )
                          "
                          required
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <vs-date-picker
                          label="入职时间"
                          outlined
                          dense
                          v-model="detailInfo.entryTime"
                          @update="
                            differ(
                              detailInfoCopy1.entryTime,
                              detailInfo.entryTime,
                            )
                          "
                        ></vs-date-picker>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="工龄"
                          readonly
                          outlined
                          dense
                          v-model="detailInfo.workAge"
                          @change="
                            differ(detailInfoCopy1.workAge, detailInfo.workAge)
                          "
                          suffix="年"
                          required
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="档案存放地点"
                          outlined
                          dense
                          v-model="detailInfo.archivePlace"
                          @change="
                            differ(
                              detailInfoCopy1.archivePlace,
                              detailInfo.archivePlace,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>其他信息</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-attachment-only
                      title="含船员手签名的加入申请（加盖手印）"
                      :attachment="detailInfo.crewAttachment.handleApply"
                      accept="all"
                      @change="changeHandleApply"
                    ></v-attachment-only>
                    <v-attachment-only
                      title="船员身份证复印件"
                      :attachment="detailInfo.crewAttachment.idCardCopy"
                      accept="all"
                      @change="changeIdCardCopy"
                    ></v-attachment-only>
                    <v-attachment-only
                      title="无工作或无缴纳社保证明"
                      :attachment="detailInfo.crewAttachment.proveNoWork"
                      accept="all"
                      @change="changeProveNoWork"
                    ></v-attachment-only>
                    <v-attachment-only
                      title="船员简历表"
                      :attachment="detailInfo.crewAttachment.resume"
                      accept="all"
                      @change="changeResume"
                    ></v-attachment-only>
                    <v-attachment-only
                      title="船员工作考评表（或业务部门推荐）"
                      :attachment="detailInfo.crewAttachment.workScoreTable"
                      accept="all"
                      @change="changeWorkScoreTable"
                    ></v-attachment-only>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-container>
        </template>
        <template v-slot:公司填写>
          <v-container fluid>
            <v-expansion-panels multiple accordion v-model="panel_2" focusable>
              <v-expansion-panel>
                <v-expansion-panel-header>基本信息</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="企业微信号"
                          outlined
                          dense
                          required
                          v-model="detailInfo.wechatNumber"
                          @change="
                            differ(
                              detailInfoCopy1.wechatNumber,
                              detailInfo.wechatNumber,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>社保信息</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="4">
                        <v-text-field
                          label="社保信息"
                          outlined
                          dense
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.socialSecurity"
                          @change="
                            differ(
                              detailInfoCopy1.socialSecurity,
                              detailInfo.socialSecurity,
                            )
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>船员属性</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="4">
                        <v-select
                          outlined
                          dense
                          label="船员管理公司"
                          :items="creFirst"
                          :rules="[rules.required]"
                          required
                          v-model="detailInfo.creProperty.creCompany"
                          @change="
                            differ(
                              detailInfoCopy1.creProperty.creCompany,
                              detailInfo.creProperty.creCompany,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          outlined
                          dense
                          label="船员性质"
                          :items="['自有船员', '外聘船员', '外包船员']"
                          :rules="[rules.required]"
                          required
                          :readonly="isReadonly"
                          v-model="detailInfo.creProperty.creFeature"
                          @change="
                            differ(
                              detailInfoCopy1.creProperty.creFeature,
                              detailInfo.creProperty.creFeature,
                            )
                          "
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-select
                          outlined
                          dense
                          label="劳动合同签署公司"
                          :rules="[rules.required]"
                          required
                          :items="creThird"
                          :readonly="isReadonly"
                          v-model="detailInfo.creProperty.creType"
                          @change="
                            differ(
                              detailInfoCopy1.creProperty.creType,
                              detailInfo.creProperty.creType,
                            )
                          "
                        ></v-select>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel v-if="newCard !== `new`">
                <v-expansion-panel-header>船员履历</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card>
                    <v-card-title>
                      <v-spacer></v-spacer>
                      <v-btn
                        outlined
                        tile
                        color="success"
                        class="mx-1"
                        @click="openWorkInfo"
                      >
                        <v-icon left>mdi-plus-circle</v-icon>
                        新增
                      </v-btn>
                      <v-btn
                        :disabled="!workInfoSelected"
                        outlined
                        tile
                        color="warning"
                        class="mx-1"
                        @click="editOverWork"
                      >
                        <v-icon left>mdi-pencil</v-icon>
                        修改
                      </v-btn>
                      <v-btn
                        :disabled="!workInfoSelected"
                        outlined
                        tile
                        color="error"
                        class="mx-1"
                        @click="delAudit"
                      >
                        <v-icon left>mdi-delete-empty</v-icon>
                        删除
                      </v-btn>
                    </v-card-title>
                    <v-card-text>
                      <v-table-list
                        ref="table2"
                        :headers="headers5"
                        :items="workInfo"
                        v-model="workInfoSelected"
                        item-key="cid"
                      >
                        <template v-slot:[`item.selfShipFlag`]="{ item }">
                          {{
                            item.selfShipFlag == 1
                              ? '本公司船舶'
                              : '其他公司船舶'
                          }}
                        </template>
                      </v-table-list>
                    </v-card-text>
                  </v-card>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel v-if="newCard !== `new`">
                <v-expansion-panel-header>
                  船员证书信息
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card>
                    <a v-if="true" :href="downPDF" ref="downPDFHref"></a>
                    <v-card-text>
                      <v-table-list
                        :headers="headers1"
                        :show-select="false"
                        :items="
                          cardInfo.certificateInfo.certificateOutputDTOList
                        "
                        item-key="code"
                        use-page
                      >
                        <template v-slot:[`item.category`]="{ item }">
                          {{ getCategory(item.category) }}
                        </template>
                        <template v-slot:[`item.operta`]="{ item }">
                          <v-btn
                            outlined
                            tile
                            small
                            class="mx-1"
                            color="info"
                            @click="downloadPdf(item)"
                          >
                            下载附件
                          </v-btn>
                        </template>
                      </v-table-list>
                    </v-card-text>
                  </v-card>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel v-if="newCard !== `new`">
                <v-expansion-panel-header>
                  船员工资信息
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card>
                    <v-card-text>
                      <v-table-list
                        :headers="headers6"
                        :show-select="false"
                        :items="cardInfo.deploySalaryInfo.deploySalaryHistory"
                        item-key="planCode"
                        use-page
                      ></v-table-list>
                    </v-card-text>
                  </v-card>
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>测评结果</v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-card-text>
                    <v-textarea
                      label="测评结果"
                      outlined
                      dense
                      v-model="detailInfo.testResult"
                      @change="
                        differ(
                          detailInfoCopy1.testResult,
                          detailInfo.testResult,
                        )
                      "
                    ></v-textarea>
                  </v-card-text>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-container>
        </template>
      </v-detail-view>
      <v-dialog attach="#mask" v-model="dialog" width="500" hide-overlay>
        <v-card>
          <v-card-title>{{ isEdit ? '新增' : '修改' }}---家庭成员</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="4">
                <v-text-field
                  label="姓名"
                  dense
                  outlined
                  :rules="[rules.required]"
                  required
                  v-model="famliyPerson.name"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="电话"
                  dense
                  outlined
                  :rules="[rules.required]"
                  required
                  v-model="famliyPerson.phoneNo"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="与本人关系"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  v-model="famliyPerson.relation"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-card-actions>
              <v-btn
                @click="upData"
                outlined
                tile
                color="success"
                class="mx-1"
                block
                v-permission="['家庭成员:保存']"
              >
                保存
              </v-btn>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-form>
    <add-over-work
      v-model="dialogTwo"
      :initialData="initialData"
      :isEdit="isEdit"
      @success="updateWorkInfo"
    ></add-over-work>
  </v-container>
</template>
<script>
import vCrewPic from './private/v-crewPic.vue'
import VAttachmentOnly from './private/v-attachmentOnly.vue'
import AddOverWork from './private/add-over-work.vue'
import routerControl from '@/mixin/routerControl'
import dictHelper from '@/mixin/dictHelper'
import VShipStation from '@/components/v-ship-station.vue'
import vsDatePicker from '@/components/vs-date-picker.vue'
import cityData from '@/assets/province_city.json'
export default {
  components: {
    VShipStation,
    vCrewPic,
    VAttachmentOnly,
    AddOverWork,
    vsDatePicker,
  },
  name: 'crew-information-detail',
  inject: {
    form: { default: null },
  },
  created() {
    this.getProvinces()
    this.headers6 = [
      { text: '船员名称', value: 'creName' },
      { text: '船舶名称', value: 'shipName' },
      { text: '在船岗位', value: 'position' },
      { text: '岗位工资', value: 'actualSalary' },
      { text: '工资开始日期', value: 'actualSalaryBeginDate' },
    ]
    this.headers1 = [
      { text: '证书编号', value: 'code' },
      { text: '证书名称', value: 'certificateName' },
      { text: '证书职务', value: 'certificatePost' },
      { text: '证书类别', value: 'category' },
      { text: '海事局', value: 'msa' },
      { text: '船员', value: 'creName' },
      { text: '船舶种类描述', value: 'shipCategory' },
      { text: '国家地区', value: 'countryPart' },
      { text: '申办单位', value: 'applicant' },
      { text: '签发时间', value: 'signDate' },
      { text: '签发地点', value: 'signPlace' },
      { text: '到期时间', value: 'expireDate' },
      { text: '操作', value: 'operta' },
    ]
    this.backRouteName = 'crew-management'
    this.subtitles = ['个人填写', '公司填写']
    this.headers = [
      { text: '姓名', value: 'name' },
      { text: '电话号码', value: 'phoneNo' },
      { text: '与本人关系', value: 'relation' },
    ]
    this.crePropertyHeaders = [
      { text: '劳动合同签署公司', value: 'creType' },
      { text: '劳动合同签署公司描述', value: 'creDescribe' },
    ]
    this.headers5 = [
      { text: '船名', value: 'shipName' },
      { text: '船舶类型', value: 'shipType' },
      { text: '船舶归属', value: 'selfShipFlag' },
      { text: '船员隶属公司', value: 'crewCompany' },
      { text: '上船时间', value: 'startDate' },
      { text: '上船地点', value: 'upBoardPlace' },
      { text: '下船时间', value: 'endDate' },
      { text: '下船地点', value: 'offBoardPlace' },
      { text: '下船原因', value: 'leaveReason' },
      { text: '在职天数', value: 'officeDay' },
      { text: '任职部门', value: 'officeDepartment' },
      { text: '任职岗位', value: 'officeJob' },
      { text: '载重吨（DWT）', value: 'dwt' },
      { text: '总吨（GT）', value: 'gtun' },
      { text: '主机类型', value: 'hostType' },
      { text: '主机功率（kw）', value: 'hostPower' },
      { text: '船东评价', value: 'opinion' },
    ]
    this.newCard = this.$route.params.id
    this.bankList = [
      { text: '招商银行', value: '招商银行' },
      { text: '中国工商银行', value: '中国工商银行' },
      { text: '中国农业银行', value: '中国农业银行' },
      { text: '中国银行', value: '中国银行' },
      { text: '中国建设银行', value: '中国建设银行' },
      { text: '交通银行', value: '交通银行' },
      { text: '中信银行', value: '中信银行' },
      { text: '平安银行', value: '平安银行' },
      { text: '中国光大银行', value: '中国光大银行' },
      { text: '华夏银行', value: '华夏银行' },
      { text: '中国民生银行', value: '中国民生银行' },
      { text: '广发银行', value: '广发银行' },
      { text: '兴业银行', value: '兴业银行' },
      { text: '上海浦东发展银行', value: '上海浦东发展银行' },
      { text: '中国邮政储蓄银行', value: '中国邮政储蓄银行' },
    ]
  },
  mixins: [dictHelper, routerControl],
  data() {
    return {
      bankList: [],
      userId: '',
      title: '新增船员信息详情',
      dialog: false,
      isEdit: true,
      selected: false,
      CreproSelected: false,
      CreproDialog: false,
      creProperty: {},
      crePropertyId: '',
      isReadonly: false, // 控制 二三级属性readonly 状态
      canSave: false,
      detailInfo: {
        country: '中国',
        countryCode: 'CN',
        familyMember: [],
        creProperty: {},
        crewAttachment: {},
        idCard: '',
        birthDate: '',
        height: null,
        weight: null,
        bmi: null, // 新增BMI字段
      },
      detailInfoCopy: '',
      detailInfoCopy1: '',
      downPDF: '',
      famliyPerson: {},
      rules: {
        required: (v) => !!v || v === 0 || v === false || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        idcard: (v) => /^\d{17}(\d|x|X)$/.test(v) || '请正确输入身份证号',
        bankName: (v) => /银行$/.test(v) || '格式应为xx银行',
        bandIdCard: (v) => /^\d{16,}$/.test(v) || '卡号应为16位及以上',
        bandPhone: (v) => /^1[3456789]\d{9}$/.test(v) || '请输入正确的手机号',
      },
      panel_1: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      panel_2: [0, 1, 2, 3, 4],
      creFirst: [],
      creThird: [],
      creThirdAll: [],
      coutryItems: [],
      provinces: [],
      cityItems: [],
      mealsCityItems: [],
      country: [],
      rmbAddressHead: '',
      rmbAddressTail: '',
      rmbAddressHeadOri: '',
      rmbAddressTailOri: '',
      mealsAddressHead: '',
      mealsAddressTail: '',
      mealsAddressHeadOri: '',
      mealsAddressTailOri: '',
      workInfo: [],
      changeCount: 0,
      workInfoSelected: false,
      dialogTwo: false,
      initialData: {},
      initialUser: {},
      cardInfo: {
        baseInfo: { creProperty: {} },
        certificateInfo: {},
        cultivateInfo: {},
        deployInfo: {},
        jobChangeInfo: {},
        cardWork: {},
        deploySalaryInfo: {},
      },
    }
  },
  computed: {
    bmiValue() {
      if (!this.detailInfo.height || !this.detailInfo.weight) return null
      const heightMeters = this.detailInfo.height / 100
      return (this.detailInfo.weight / (heightMeters * heightMeters)).toFixed(2)
    },
  },
  watch: {
    'detailInfo.height': {
      handler() {
        this.detailInfo.bmi = this.bmiValue
      },
      immediate: true,
    },
    'detailInfo.weight': {
      handler() {
        this.detailInfo.bmi = this.bmiValue
      },
      immediate: true,
    },
    'detailInfo.idCard'(newVal) {
      if (newVal.length === 18) {
        // 从身份证号码中提取出生日期
        // 身份证的7-14位是出生年月日，格式为YYYYMMDD
        const year = newVal.substring(6, 10)
        const month = newVal.substring(10, 12)
        const day = newVal.substring(12, 14)
        const genderCode = newVal.charAt(16)
        this.detailInfo.gender = parseInt(genderCode) % 2 === 0 ? '2' : '1'
        this.detailInfo.socialSecurity = newVal
        // 设置出生日期字段的格式
        this.detailInfo.birthDate = `${year}-${month}-${day}`
      }
    },
    'detailInfo.chName': {
      handler(newValue) {
        this.getEnName(newValue)
        this.detailInfo.rmbName = this.detailInfo.chName
      },
    },
    'detailInfo.actualPosition': {
      handler(newValue) {
        this.differ(this.detailInfoCopy1.actualPosition, newValue)
      },
    },
    'detailInfo.certificatePosition': {
      handler(newValue) {
        this.differ(this.detailInfoCopy1.certificatePosition, newValue)
      },
    },
    'detailInfo.graduationDate': {
      handler(newValue) {
        this.differ(this.detailInfoCopy1.graduationDate, newValue)
      },
    },
    'detailInfo.creProperty': {
      async handler(newValue) {
        if (newValue.creCompany && newValue.creFeature) {
          await this.getCreThird()
        }
        if (newValue.creType) {
          // console.log('creThirdAllWatch', this.creThirdAll)
          // console.log('detailInfoWatchBefore.crePropertyId', this.crePropertyId)
          this.detailInfo.creProperty.id = this.creThirdAll.find(
            (val) => val.creType === newValue.creType,
          )?.id
          this.crePropertyId = this.creThirdAll.find(
            (val) => val.creType === newValue.creType,
          )?.id
          // console.log('detailInfoWatchAfter.crePropertyId', this.crePropertyId)
        }
      },
      deep: true,
    },
    'detailInfo.country': {
      handler(newValue) {
        this.detailInfo.countryCode = this.country.find(
          (ele) => ele.country === newValue,
        )?.twoCode
      },
    },
    rmbAddressHead: {
      async handler(newValue) {
        if (newValue) {
          await this.getCityItems(newValue)
        }
      },
    },
    mealsAddressHead: {
      async handler(newValue) {
        if (newValue) {
          await this.getMealsCityItems(newValue)
        }
      },
    },
  },

  methods: {
    async downloadPdf(item) {
      let arr = []
      arr.push(item.certificateId)
      const { data } = await this.postAsync(
        '/business/crew/certificate/exportCrewCertificate',
        arr,
      )
      // console.log(data)
      if (data) {
        data.forEach((item) => {
          this.dowloadPDFs(item)
        })
      }
    },
    async dowloadPDFs(item) {
      this.downPDF = `/api/system/file/download?fileName=${encodeURIComponent(
        item.fileName,
      )}&filePath=${item.filePath}`
      // console.log(this.downPDF)

      const link = this.$refs.downPDFHref
      link.href = this.downPDF
      link.download = this.extractFilename(this.downPDF)
      link.style.display = 'none'
      document.body.appendChild(link)

      // 模拟点击<a>标签以触发下载
      link.click()
    },
    extractFilename(url) {
      return url.substring(url.lastIndexOf('/') + 1)
    },
    getCategory(item) {
      const map = new Map([
        [0, '海员证'],
        [1, '培训合格证书'],
        [2, '船员服务簿'],
        [3, '适任证书'],
        [4, '海事局健康证明'],
        [5, '护照'],
        [6, '香港适任证书'],
        [7, '巴拿马适任证书'],
        [8, '疫苗接种或预防措施国际证书'],
        [9, '国际旅行健康检查证明书'],
      ])
      return map.get(item)
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }

      this.detailInfo.tempFlag = false

      this.detailInfo.tempFlag = false
      if (this.$route.params.id !== `new`) {
        this.detailInfo.tempFlag = true
        const { errorRaw } = await this.postAsync(
          `/business/crew/baseInfo/tempSave`,
          {
            ...this.detailInfo,
            rmbAddress: `${this.rmbAddressHead} ${this.rmbAddressTail}`,
            mealsAddress: `${this.mealsAddressHead} ${this.mealsAddressTail}`,
          },
        )
        if (errorRaw) {
          return
        }
        if (await this.upWorkInfo()) {
          this.$dialog.message.error('履历保存失败')
          return
        }
        goBack()
      } else {
        const { errorRaw } = await this.postAsync(
          `/business/crew/baseInfo/manage/add`,
          {
            ...this.detailInfo,
            rmbAddress: `${this.rmbAddressHead} ${this.rmbAddressTail}`,
            crePropertyId: this.detailInfo.creProperty.id,
          },
        )
        if (errorRaw) {
          return
        }
        if (await this.upWorkInfo()) {
          this.$dialog.message.error('履历保存失败')
          return
        }

        goBack()
      }
    },
    async submit() {
      if (!(await this.$dialog.msgbox.confirm('请确定当前信息是否填写无误')))
        return
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      this.detailInfo.tempFlag = true
      // await this.save(goBack)
      // console.log('提交时的属性id：', this.detailInfo.creProperty.id)
      // console.log('提交时的单个属性id：', this.detailInfo.creProperty.id)
      const { errorRaw } = await this.postAsync(
        `/business/crew/baseInfo/approve/manage/commit`,
        {
          ...this.detailInfo,
          crePropertyId: this.detailInfo.creProperty.id,
          rmbAddress: `${this.rmbAddressHead} ${this.rmbAddressTail}`,
          mealsAddress: `${this.mealsAddressHead} ${this.mealsAddressTail}`,
        },
      )
      if (errorRaw) {
        return
      }
      if (await this.upWorkInfo()) {
        this.$dialog.message.error('履历保存失败')
        return
      }
      this.$dialog.message.success(`审核提交成功！`)
      this.closeAndTo('crew-person-totalInfo-approve-list', {})
    },
    // 获取英文姓名
    async getEnName(newValue) {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/function/pinyin`,
        { chinese: newValue },
      )
      if (errorRaw) {
        return
      }
      this.detailInfo.enName = data[0]
    },
    // 附件相关函数
    changeCardPic(attachmentId) {
      this.detailInfo.bankCardPic = attachmentId
    },
    changeHandleApply(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.handleApply = attachmentId
    },
    changeIdCardCopy(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.idCardCopy = attachmentId
    },
    changeProveNoWork(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.proveNoWork = attachmentId
    },
    changeResume(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.resume = attachmentId
    },
    changeWorkScoreTable(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.workScoreTable = attachmentId
    },

    //图片上传
    changePicture(val) {
      this.detailInfo.identificationPhoto = val
    },
    //增加家庭成员的探窗开启函数
    addFamily() {
      this.isEdit = true
      this.dialog = true
    },
    //添加紧急联系人
    upData() {
      if (this.isEdit) {
        this.detailInfo.familyMember.push(this.famliyPerson)
        this.dialog = false
      } else {
        this.dialog = false
      }
    },
    //修改紧急联系人按钮
    change() {
      this.dialog = true
      this.isEdit = false
      this.famliyPerson = this.selected
    },
    //删除紧急联系人
    deleteItem() {
      let index = this.detailInfo.familyMember.indexOf(this.selected)
      this.detailInfo.familyMember.splice(index, 1)
    },
    //船员属性
    editElectronicchart() {
      this.CreproDialog = true
    },
    changeCrepro(val) {
      this.detailInfo.creProperty = val
      this.creProperty = [val]
      this.CreproSelected = false
    },
    delCrepro() {
      this.creProperty = []
      this.detailInfo.creProperty = {}
      this.CreproSelected = false
    },
    close() {
      this.CreproDialog = false
    },
    // 获取船员信息
    async getCrewInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/baseInfo/record/manage/${this.$route.params.id}`,
        )
        if (errorRaw) {
          return
        }
        this.userId = data.userId
        if (data.creProperty === null) {
          this.detailInfo = {
            crewUploadFileModifyDTO: {},
            crewAttachment: {},
            ...data,
            creProperty: {},
          }
          this.detailInfoCopy1 = {
            crewUploadFileModifyDTO: {},
            crewAttachment: {},
            ...data,
            creProperty: {},
          }
        } else {
          this.detailInfo = {
            crewUploadFileModifyDTO: {},
            crewAttachment: {},
            ...data,
          }
          this.detailInfoCopy1 = {
            crewUploadFileModifyDTO: {},
            crewAttachment: {},
            ...data,
          }
        }
        this.creProperty = [data.creProperty]
        if (data.rmbAddress) {
          const arr = data.rmbAddress.split(' ')
          if (arr.length === 2) {
            this.rmbAddressHead = arr[0]
            this.rmbAddressHeadOri = arr[0]
            this.rmbAddressTail = arr[1]
            this.rmbAddressTailOri = arr[1]
          }
        }
        if (data.mealsAddress) {
          const arr = data.mealsAddress.split(' ')
          if (arr.length === 2) {
            this.mealsAddressHead = arr[0]
            this.mealsAddressHeadOri = arr[0]
            this.mealsAddressTail = arr[1]
            this.mealsAddressTailOri = arr[1]
          }
        }
        this.detailInfoCopy1 = JSON.parse(JSON.stringify(this.detailInfo))
        await this.getCreThird()
        this.title = data.chName + '-----个人信息详情'
        if (!data.tempFlag) {
          this.title += '---(暂存状态)'
        } else {
          this.title += '---(已启用状态)'
        }
        this.detailInfoCopy = JSON.stringify(this.detailInfo)
      }
    },
    // 获取所有国家
    async getCountry() {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/function/country/list`,
      )
      if (errorRaw) {
        return
      }
      this.country = data
      this.coutryItems = data.map((val) => val.country)
    },
    //获取船员属性信息
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.creFirst = data
    },
    async getCreThird() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/list`,
        {
          creCompany: this.detailInfo.creProperty.creCompany,
          creFeature: this.detailInfo.creProperty.creFeature,
        },
      )
      if (errorRaw) {
        return
      }
      this.creThird = data.records.map((val) => val.creType)
      this.creThirdAll = data.records
    },
    // 获取船员履历信息
    async getWorkDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/workHistory/list`,
        {
          creId: this.userId,
        },
      )
      if (errorRaw) {
        return
      }
      this.workInfo = data
      for (let i = 0; i < this.workInfo.length; i++) {
        this.workInfo[i].cid = i + 1
      }
    },
    async getCardDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/ifoCard/ByUserId?userId=${this.userId}`,
      )
      if (errorRaw) {
        return
      }
      this.cardInfo = data
      // console.log(this.cardInfo.certificateInfo.certificateOutputDTOList)
    },
    openWorkInfo() {
      this.isEdit = true
      this.initialData = {}
      this.dialogTwo = true
    },
    editOverWork() {
      this.initialData = { ...this.workInfoSelected }
      this.isEdit = false
      this.dialogTwo = true
    },
    delAudit() {
      const len = this.workInfo.length
      const index = this.workInfo.findIndex(
        (ele) => ele.cid === this.workInfoSelected.cid,
      )
      this.workInfo.splice(index, 1)
      this.workInfoSelected = false
      for (let i = 0; i < this.workInfo.length; i++) {
        this.workInfo[i].cid = i + 1
      }
      if (len === this.workInfo.length) {
        this.$dialog.message.error('删除失败')
      } else {
        this.$dialog.message.success('删除成功')
      }
    },
    updateWorkInfo(val) {
      if (this.isEdit) {
        const len = this.workInfo.length
        let workParams = {
          ...val,
          creId: this.$route.params.id,
          cid: len + 1,
        }
        this.workInfo.push(workParams)
        if (this.workInfo.length === len) {
          this.$dialog.message.error('添加失败')
        } else {
          this.$dialog.message.success('添加成功')
        }
      } else {
        const index = this.workInfo.findIndex((ele) => ele.cid === val.cid)
        this.workInfo.splice(index, 1, val)
        this.$dialog.message.success('修改成功')
      }
    },
    async upWorkInfo() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/baseInfo/workHistory/batchSaveOrUpdate`,
        {
          modifyList: this.workInfo,
        },
      )
      if (errorRaw) {
        return errorRaw
      }
    },
    differ(ori, now) {
      // console.log('differ运行')
      // console.log('nowT', now)
      // console.log('oriT', ori)
      if (ori != now) {
        // console.log('nowT', now)
        // console.log('oriT', ori)
        if (typeof ori === typeof now) {
          this.changeCount++
          if (this.changeCount > 0) {
            this.canSave = true
            // console.log('changeCount', this.changeCount)
          }
          return
        }
        if (now === null || now.trim() === '') {
          if (ori === null || ori.trim() === '') {
            if (this.changeCount > 0) {
              this.changeCount--
              if (this.changeCount === 0) {
                this.canSave = false
                // console.log('changeCount', this.changeCount)
              }
            }
            return
          }
        }
        this.changeCount++
        if (this.changeCount > 0) {
          this.canSave = true
          // console.log('changeCount', this.changeCount)
        }
        return
      }
      // console.log('nowF', now)
      // console.log('oriF', ori)
      if (this.changeCount > 0) {
        this.changeCount--
        if (this.changeCount === 0) {
          this.canSave = false
          // console.log('changeCount', this.changeCount)
        }
      }
    },
    trimInput(value) {
      return value.replace(/\s*/g, '')
    },
    getProvinces() {
      this.provinces = cityData.map((ele) => {
        return {
          text: ele.name,
          value: ele.name,
        }
      })
      // console.log('provinces', this.provinces)
    },
    getCityItems(newValue) {
      let tempData = cityData.filter((ele) => ele.name === newValue)
      // console.log('tempData', tempData)
      if (tempData.length === 0) {
        return
      }
      this.cityItems = tempData[0].city.map((ele) => {
        return {
          text: ele.name,
          value: ele.name,
        }
      })
      // console.log('cityItems', this.cityItems)
    },
    getMealsCityItems(newValue) {
      let tempData = cityData.filter((ele) => ele.name === newValue)
      // console.log('tempData', tempData)
      if (tempData.length === 0) {
        return
      }
      this.mealsCityItems = tempData[0].city.map((ele) => {
        return {
          text: ele.name,
          value: ele.name,
        }
      })
      // console.log('cityItems', this.cityItems)
    },
  },
  async mounted() {
    await this.getCountry()
    await this.getCrewInfo()
    await this.getCreFirst()
    await this.getWorkDetailInfo()
    await this.getCardDetailInfo()
  },
}
</script>

<style></style>
