<template>
  <v-container fluid>
    <v-form ref="form">
      <v-card>
        <v-card-title>
          船员个人信息编辑
          <v-spacer></v-spacer>
          <v-col cols="6" md="2">
            <v-select
              :rules="[rules.required]"
              style="top: 14px"
              outlined
              dense
              label="选择调配主管"
              required
              :items="Operators"
              item-text="nickName"
              item-value="id"
              v-model="Operator"
              clearable
            ></v-select>
          </v-col>
          <v-btn
            v-if="!approveFlag"
            :loading="loading1"
            outlined
            tile
            class="mx-2"
            color="green"
            @click="commit"
            v-permission="['船员个人信息:保存']"
          >
            提交修改
          </v-btn>
          <v-btn
            v-else
            outlined
            tile
            class="mx-1"
            color="blue"
            @click="commitInfo"
            v-permission="['船员个人信息:提交']"
          >
            提交
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-expansion-panels multiple accordion v-model="panel" focusable>
            <v-expansion-panel>
              <v-expansion-panel-header>基本信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="中文姓名"
                        outlined
                        dense
                        :rules="[rules.required]"
                        required
                        v-model="detailInfo.chName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="英文姓名"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.enName"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <vs-date-picker
                        v-model="detailInfo.birthDate"
                        label="出生日期"
                        outlined
                        dense
                        :rules="[rules.required]"
                        required
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="民族"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.nation"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="性别"
                        outlined
                        dense
                        :items="[
                          { text: '男', value: '1' },
                          { text: '女', value: '2' },
                        ]"
                        :rules="[rules.required]"
                        required
                        v-model="detailInfo.gender"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="国家"
                        outlined
                        dense
                        :items="coutryItems"
                        :rules="[rules.required]"
                        v-model="detailInfo.country"
                        required
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="国籍"
                        outlined
                        dense
                        :rules="[rules.required]"
                        readonly
                        required
                        v-model="detailInfo.countryCode"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="婚姻状况"
                        outlined
                        dense
                        :items="['未婚', '已婚', '离异']"
                        :rules="[rules.required]"
                        v-model="detailInfo.maritalStatus"
                        required
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="政治面貌"
                        outlined
                        dense
                        :items="[
                          '共产党员',
                          '共青团员',
                          '群众',
                          '预备党员',
                          '民主党派成员',
                        ]"
                        v-model="detailInfo.politicsStatus"
                        required
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="宗教信仰"
                        outlined
                        dense
                        v-model="detailInfo.religion"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="外语语种"
                        outlined
                        dense
                        v-model="detailInfo.foreignLanguages"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="外语水平"
                        outlined
                        dense
                        v-model="detailInfo.foreignLevel"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="籍贯"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.nativePlace"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="身份证号码"
                        outlined
                        dense
                        :rules="[rules.idcard]"
                        v-model="detailInfo.idCard"
                        required
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-textarea
                    outlined
                    dense
                    label="有无病史"
                    v-model="detailInfo.medicalHistory"
                  ></v-textarea>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>体貌特征</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="身高"
                        outlined
                        dense
                        :rules="[rules.number]"
                        v-model="detailInfo.height"
                        suffix="cm"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="体重"
                        outlined
                        dense
                        :rules="[rules.number]"
                        v-model="detailInfo.weight"
                        suffix="kg"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="血型"
                        outlined
                        dense
                        :rules="[rules.required]"
                        :items="['A', 'AB', 'B', 'O']"
                        v-model="detailInfo.bloodType"
                        required
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作服尺码"
                        outlined
                        dense
                        :rules="[rules.required]"
                        :items="[160, 165, 170, 175, 180, 185, 190, 195, 200]"
                        v-model="detailInfo.workClothesSize"
                        required
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作鞋尺码"
                        outlined
                        dense
                        :rules="[rules.required]"
                        :items="[39, 40, 41, 42, 43, 44, 45, 46]"
                        v-model="detailInfo.workShoeSize"
                        required
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>联系方式</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="手机号"
                        outlined
                        dense
                        :rules="[rules.number]"
                        v-model="detailInfo.phone"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="邮箱"
                        outlined
                        dense
                        v-model="detailInfo.email"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col md="6">
                      <v-textarea
                        label="邮寄地址"
                        outlined
                        :rules="[rules.required]"
                        dense
                        v-model="detailInfo.mailingAddress"
                        required
                      ></v-textarea>
                    </v-col>
                    <v-col md="6">
                      <v-textarea
                        label="家庭住址"
                        outlined
                        :rules="[rules.required]"
                        dense
                        v-model="detailInfo.homeAddress"
                        required
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>家庭成员</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card>
                  <v-card-title>
                    <div></div>
                    <v-spacer></v-spacer>
                    <v-btn
                      outlined
                      tile
                      color="success"
                      class="mx-1"
                      @click="addFamily"
                      v-permission="['个人家庭成员:新增']"
                    >
                      <v-icon left>mdi-plus-circle</v-icon>
                      新增
                    </v-btn>
                    <v-btn
                      :disabled="!selected"
                      outlined
                      tile
                      color="warning"
                      class="mx-1"
                      @click="change"
                      v-permission="['个人家庭成员:修改']"
                    >
                      <v-icon left>mdi-pencil</v-icon>
                      修改
                    </v-btn>
                    <v-btn
                      :disabled="!selected"
                      outlined
                      tile
                      color="error"
                      class="mx-1"
                      @click="deleteItem"
                      v-permission="['个人家庭成员:删除']"
                    >
                      <v-icon left>mdi-delete-empty</v-icon>
                      删除
                    </v-btn>
                  </v-card-title>
                  <v-table-list
                    ref="table"
                    v-model="selected"
                    :headers="headers"
                    :items="detailInfo.familyMember"
                    item-key="name"
                  ></v-table-list>
                </v-card>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>紧急联系人</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="紧急联系人"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.emergencyName"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="电话"
                        outlined
                        :rules="[rules.required]"
                        dense
                        v-model="detailInfo.emergencyPhone"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <v-text-field
                        label="地址"
                        outlined
                        :rules="[rules.required]"
                        dense
                        v-model="detailInfo.emergencyAddress"
                        required
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>教育经历</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="最高学历"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.highestDegree"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="毕业院校"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.graduateSchool"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <vs-date-picker
                        dense
                        outlined
                        label="毕业时间"
                        :rules="[rules.required]"
                        v-model="detailInfo.graduationDate"
                        required
                      ></vs-date-picker>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>职务信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="实际职务"
                        :rules="[rules.required]"
                        v-model="detailInfo.actualPosition"
                        required
                      ></v-ship-station>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="证书职务"
                        :rules="[rules.required]"
                        v-model="detailInfo.certificatePosition"
                        required
                      ></v-ship-station>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>
                银行卡信息（仅限招商银行）
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="4">
                      <v-select
                        label="人民币开户行"
                        outlined
                        dense
                        :items="bankList"
                        :rules="[rules.bankName]"
                        required
                        v-model="detailInfo.rmbBank"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币开户银行分支机构"
                        outlined
                        dense
                        v-model="detailInfo.rmbBankBranch"
                        :rules="[rules.required]"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币卡号"
                        outlined
                        dense
                        v-model="detailInfo.rmbCard"
                        :rules="[rules.number]"
                        required
                        @keyup.native="
                          detailInfo.rmbCard = detailInfo.rmbCard.replace(
                            /\s+/g,
                            '',
                          )
                        "
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币开户行人姓名"
                        outlined
                        readonly
                        dense
                        v-model="detailInfo.rmbName"
                        :rules="[rules.required]"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-select
                        label="开户地省份"
                        outlined
                        dense
                        :items="provinces"
                        v-model="rmbAddressHead"
                        :rules="[rules.required]"
                        required
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-select
                        label="开户地城市(区)"
                        outlined
                        dense
                        :items="cityItems"
                        v-model="rmbAddressTail"
                        :rules="[rules.required]"
                        required
                      ></v-select>
                    </v-col>
                  </v-row>
                  <!--                  <v-row>-->
                  <!--                    <v-col cols="12" md="2">-->
                  <!--                      <v-text-field-->
                  <!--                        label="美元开户行"-->
                  <!--                        dense-->
                  <!--                        outlined-->
                  <!--                        v-model="detailInfo.usdBank"-->
                  <!--                        required-->
                  <!--                      ></v-text-field>-->
                  <!--                    </v-col>-->
                  <!--                    <v-col cols="12" md="2">-->
                  <!--                      <v-text-field-->
                  <!--                        label="美元开户银行分支机构中文名称"-->
                  <!--                        outlined-->
                  <!--                        dense-->
                  <!--                        v-model="detailInfo.usdBankBranch"-->
                  <!--                        :rules="[rules.required]"-->
                  <!--                        required-->
                  <!--                      ></v-text-field>-->
                  <!--                    </v-col>-->
                  <!--                    <v-col cols="12" md="2">-->
                  <!--                      <v-text-field-->
                  <!--                        label="美元开户银行分支机构英文名称"-->
                  <!--                        outlined-->
                  <!--                        dense-->
                  <!--                        v-model="detailInfo.usdBankBranchEn"-->
                  <!--                        required-->
                  <!--                      ></v-text-field>-->
                  <!--                    </v-col>-->
                  <!--                    <v-col cols="12" md="2">-->
                  <!--                      <v-text-field-->
                  <!--                        label="美元卡号"-->
                  <!--                        outlined-->
                  <!--                        dense-->
                  <!--                        v-model="detailInfo.usdCard"-->
                  <!--                        required-->
                  <!--                      ></v-text-field>-->
                  <!--                    </v-col>-->
                  <!--                    <v-col cols="12" md="2">-->
                  <!--                      <v-text-field-->
                  <!--                        label="美元开户人姓名"-->
                  <!--                        outlined-->
                  <!--                        dense-->
                  <!--                        v-model="detailInfo.usdName"-->
                  <!--                        required-->
                  <!--                      ></v-text-field>-->
                  <!--                    </v-col>-->
                  <!--                    <v-col cols="12" md="2">-->
                  <!--                      <v-text-field-->
                  <!--                        label="SWIFT CODE"-->
                  <!--                        dense-->
                  <!--                        outlined-->
                  <!--                        v-model="detailInfo.swiftCode"-->
                  <!--                        required-->
                  <!--                      ></v-text-field>-->
                  <!--                    </v-col>-->
                  <!--                  </v-row>-->
                  <v-attachment-only
                    title="银行卡照片附件美元银行卡图片附件"
                    :attachment="detailInfo.bankCardPicAttachment"
                    @change="changeCardPic"
                  ></v-attachment-only>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>照片附件</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-crew-pic
                  :ImagePicture="detailInfo.identificationPhotoAttachment"
                  @change="changePicture"
                ></v-crew-pic>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <!--            <v-expansion-panel>-->
            <!--              <v-expansion-panel-header>档案信息</v-expansion-panel-header>-->
            <!--              <v-expansion-panel-content>-->
            <!--                <v-card-text>-->
            <!--                  <v-row>-->
            <!--                    <v-col cols="12" md="2">-->
            <!--                      <v-select-->
            <!--                        label="是否由公司保存档案"-->
            <!--                        outlined-->
            <!--                        dense-->
            <!--                        :items="[-->
            <!--                          { text: '是', value: true },-->
            <!--                          { text: '否', value: false },-->
            <!--                        ]"-->
            <!--                        v-model="detailInfo.archiveFlag"-->
            <!--                        :rules="[rules.required]"-->
            <!--                        required-->
            <!--                      ></v-select>-->
            <!--                    </v-col>-->
            <!--                    <v-col cols="12" md="2">-->
            <!--                      <vs-date-picker-->
            <!--                        label="入职时间"-->
            <!--                        outlined-->
            <!--                        dense-->
            <!--                        v-model="detailInfo.entryTime"-->
            <!--                      ></vs-date-picker>-->
            <!--                    </v-col>-->
            <!--                    <v-col cols="12" md="2">-->
            <!--                      <v-text-field-->
            <!--                        label="工龄"-->
            <!--                        readonly-->
            <!--                        outlined-->
            <!--                        dense-->
            <!--                        v-model="detailInfo.workAge"-->
            <!--                        suffix="年"-->
            <!--                        required-->
            <!--                      ></v-text-field>-->
            <!--                    </v-col>-->
            <!--                    <v-col cols="12" md="2">-->
            <!--                      <v-text-field-->
            <!--                        label="档案存放地点"-->
            <!--                        outlined-->
            <!--                        dense-->
            <!--                        v-model="detailInfo.archivePlace"-->
            <!--                      ></v-text-field>-->
            <!--                    </v-col>-->
            <!--                  </v-row>-->
            <!--                </v-card-text>-->
            <!--              </v-expansion-panel-content>-->
            <!--            </v-expansion-panel>-->
            <v-expansion-panel>
              <v-expansion-panel-header>其他信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-attachment-only
                    title="含船员手签名的加入申请（加盖手印）"
                    :attachment="detailInfo.crewAttachment.handleApply"
                    accept="all"
                    @change="changeHandleApply"
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员身份证复印件"
                    :attachment="detailInfo.crewAttachment.idCardCopy"
                    accept="all"
                    @change="changeIdCardCopy"
                  ></v-attachment-only>
                  <v-attachment-only
                    title="无工作或无缴纳社保证明"
                    :attachment="detailInfo.crewAttachment.proveNoWork"
                    accept="all"
                    @change="changeProveNoWork"
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员简历表"
                    :attachment="detailInfo.crewAttachment.resume"
                    accept="all"
                    @change="changeResume"
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员工作考评表（或业务部门推荐）"
                    :attachment="detailInfo.crewAttachment.workScoreTable"
                    accept="all"
                    @change="changeWorkScoreTable"
                  ></v-attachment-only>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </v-card>
      <v-dialog
        attach="#mask"
        v-model="dialog"
        persistent
        width="500"
        hide-overlay
      >
        <v-card>
          <v-card-title>{{ isEdit ? '新增' : '修改' }}---家庭成员</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="4">
                <v-text-field
                  label="姓名"
                  dense
                  outlined
                  v-model="famliyPerson.name"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="电话"
                  dense
                  outlined
                  v-model="famliyPerson.phoneNo"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="与本人关系"
                  dense
                  outlined
                  v-model="famliyPerson.relation"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-card-actions>
              <v-btn
                @click="upData"
                outlined
                tile
                color="success"
                class="mx-1"
                block
                v-permission="['个人家庭成员:保存']"
              >
                保存
              </v-btn>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-form>
  </v-container>
</template>
<script>
import vCrewPic from './private/v-crewPic.vue'
import VAttachmentOnly from './private/v-attachmentOnly.vue'
import routerControl from '@/mixin/routerControl'
import cityData from '@/assets/province_city.json'
export default {
  components: { vCrewPic, VAttachmentOnly },
  mixins: [routerControl],
  name: 'crew-personal-information',
  created() {
    this.getProvinces()
    this.headers = [
      { text: '姓名', value: 'name' },
      { text: '电话号码', value: 'phoneNo' },
      { text: '与本人关系', value: 'relation' },
    ]
    this.bankList = [
      { text: '招商银行', value: '招商银行' },
      { text: '中国工商银行', value: '中国工商银行' },
      { text: '中国农业银行', value: '中国农业银行' },
      { text: '中国银行', value: '中国银行' },
      { text: '中国建设银行', value: '中国建设银行' },
      { text: '交通银行', value: '交通银行' },
      { text: '中信银行', value: '中信银行' },
      { text: '平安银行', value: '平安银行' },
      { text: '中国光大银行', value: '中国光大银行' },
      { text: '华夏银行', value: '华夏银行' },
      { text: '中国民生银行', value: '中国民生银行' },
      { text: '广发银行', value: '广发银行' },
      { text: '兴业银行', value: '兴业银行' },
      { text: '上海浦东发展银行', value: '上海浦东发展银行' },
      { text: '中国邮政储蓄银行', value: '中国邮政储蓄银行' },
    ]
    this.backRouteName = 'crew-personal-information-approve-list'
  },
  watch: {
    'detailInfo.country': {
      handler(newValue) {
        this.detailInfo.countryCode = this.country.find(
          (ele) => ele.country === newValue,
        )?.twoCode
      },
    },
    'detailInfo.chName': {
      async handler(newValue) {
        if (newValue) {
          const { errorRaw, data } = await this.getAsync(
            `/business/common/function/pinyin`,
            { chinese: newValue },
          )
          if (errorRaw) {
            return
          }
          this.detailInfo.enName = data[0]
          this.detailInfo.rmbName = newValue
        }
      },
      deep: true,
    },
    rmbAddressHead: {
      async handler(newValue) {
        if (newValue) {
          await this.getCityItems(newValue)
        }
      },
    },
  },
  data() {
    return {
      roleNames: ['船员调配主管', '外包公司账号'],
      Operators: [],
      provinces: [],
      bankList: [],
      cityItems: [],
      Operator: '',
      title: '船员个人信息',
      detailInfo: {
        country: '中国',
        countryCode: 'CN',
        crewAttachment: {},
      },
      selected: false,
      loading1: false,
      famliyPerson: {},
      panel: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      rules: {
        required: (v) => !!v || v === 0 || v === false || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        bankName: (v) => /银行$/.test(v) || '格式应为xx银行',
        idcard: (v) => /^\d{17}(\d|x|X)$/.test(v) || '请正确输入身份证号',
      },
      isEdit: false,
      dialog: false,
      coutryItems: [],
      country: [],
      approveFlag: false,
      rmbAddressHead: '',
      rmbAddressTail: '',
    }
  },

  methods: {
    //获取国家信息
    async getCountry() {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/function/country/list`,
      )
      if (errorRaw) {
        return
      }
      this.country = data
      this.coutryItems = data.map((val) => val.country)
    },
    //获取调配主管信息
    async getOperators() {
      const { errorRaw, data } = await this.postAsync(
        `/business/crew/baseInfo/user/role`,
        this.roleNames,
      )
      if (errorRaw) {
        return
      }
      this.Operators = data
    },
    //获取账户个人信心
    async getOwnInfo() {
      await this.getCountry()
      const user_id = this.$local.data.get('userInfo').id
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/recordByUserId/${user_id}`,
      )
      if (errorRaw) {
        return
      }
      this.approveFlag = data ? false : true
      this.detailInfo = {
        crewAttachment: {},
        crewUploadFileModifyDTO: {},
        familyMember: [],
        country: '中国',
        countryCode: 'CN',
        userId: this.$local.data.get('userInfo').id,
        ...data,
      }
      if (data.rmbAddress) {
        const arr = data.rmbAddress.split(' ')
        if (arr.length === 2) {
          this.rmbAddressHead = arr[0]
          this.rmbAddressTail = arr[1]
        }
      }
    },
    //船员个人信息保存
    async commit() {
      this.loading1 = true
      // console.log('Operator', this.Operator)
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        this.loading1 = false
        return
      }
      if (!(await this.$dialog.msgbox.confirm('当前信息是否已确认完毕？')))
        return
      const { errorRaw } = await this.postAsync(
        `/business/crew/baseInfo/recordApprove/commit`,
        {
          ...this.detailInfo,
          operatorId: this.Operator,
          rmbAddress: `${this.rmbAddressHead} ${this.rmbAddressTail}`,
        },
      )
      this.loading1 = false
      if (errorRaw) {
        // this.$dialog.message.error(`提交失败！`)
        return
      }
      this.$dialog.message.success(`提交成功！`)
      await this.closeAndTo(this.backRouteName, {})
    },
    // 新船员个人信息提交
    async commitInfo() {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('当前信息是否已确认完毕？')))
        return
      const { errorRaw } = await this.postAsync(
        `/business/crew/baseInfo/init`,
        this.detailInfo,
      )
      if (errorRaw) {
        this.$dialog.message.error(`审批提交失败！`)
        return
      }
      await this.getOwnInfo()
      this.$dialog.message.success(`审批提交成功！`)
    },
    //增加家庭成员的探窗开启函数
    addFamily() {
      this.isEdit = true
      this.dialog = true
    },
    //修改紧急联系人按钮
    change() {
      this.dialog = true
      this.isEdit = false
      this.famliyPerson = this.selected
    },
    //删除紧急联系人
    deleteItem() {
      let index = this.detailInfo.familyMember.indexOf(this.selected)
      this.detailInfo.familyMember.splice(index, 1)
    },
    //添加紧急联系人
    upData() {
      if (this.isEdit) {
        this.detailInfo.familyMember.push(this.famliyPerson)
        this.dialog = false
        this.famliyPerson = {}
      } else {
        this.dialog = false
      }
    },
    // 附件相关函数
    changeCardPic(attachmentId) {
      this.detailInfo.bankCardPic = attachmentId
    },
    changeHandleApply(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.handleApply = attachmentId
    },
    changeIdCardCopy(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.idCardCopy = attachmentId
    },
    changeProveNoWork(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.proveNoWork = attachmentId
    },
    changeResume(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.resume = attachmentId
    },
    changeWorkScoreTable(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.workScoreTable = attachmentId
    },
    //图片上传
    changePicture(val) {
      this.detailInfo.identificationPhoto = val
    },
    getProvinces() {
      this.provinces = cityData.map((ele) => {
        return {
          text: ele.name,
          value: ele.name,
        }
      })
      // console.log('provinces', this.provinces)
    },
    getCityItems(newValue) {
      let tempData = cityData.filter((ele) => ele.name === newValue)
      // console.log('tempData', tempData)
      if (tempData.length === 0) {
        return
      }
      this.cityItems = tempData[0].city.map((ele) => {
        return {
          text: ele.name,
          value: ele.name,
        }
      })
      // console.log('cityItems', this.cityItems)
    },
  },

  async mounted() {
    await this.getOwnInfo()
    await this.getOperators()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
  z-index: 100;
}
</style>
