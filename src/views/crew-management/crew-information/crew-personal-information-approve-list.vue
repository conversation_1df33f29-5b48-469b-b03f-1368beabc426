<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      :push-params="pushParams"
      :item-key="id"
    >
      <template #searchflieds>
        <v-col cols="12" md="1">
          <v-text-field
            label="中文姓名"
            outlined
            dense
            v-model="searchRemain.name"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="身份证号"
            outlined
            dense
            v-model="searchRemain.idCard"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="审批状态"
            required
            :items="statusInfo"
            v-model="searchRemain.status"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns></template>
      <template v-slot:[`item.aprStatus`]="{ item }">
        <v-chip v-if="item.aprStatus === 0" color="" small dark>待审批</v-chip>
        <v-chip v-else-if="item.aprStatus === 1" color="success" small dark>
          已通过
        </v-chip>
        <v-chip v-else-if="item.aprStatus === 2" color="#A80F25" small dark>
          未通过
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'crew-personal-information-approve-list',
  created() {
    this.tableName = '船员个人信息修改审批列表'
    this.reqUrl = '/business/crew/baseInfo/recordApprove/page'
    this.headers = [
      { text: '姓名', value: 'chName' },
      { text: '提交时间', value: 'createTime' },
      { text: '身份证号码', value: 'idCard' },
      { text: '电话号码', value: 'phone' },
      { text: '审批状态', value: 'aprStatus' },
      { text: '审批人', value: 'approver' },
      { text: '审批时间', value: 'approveTime' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'crew-personal-information-approve-detail',
    }
    this.id = 'id'
    this.searchRemain.roleName = this.$local.data.get('userInfo').roleName
  },
  watch: {},

  data() {
    return {
      selected: false,
      searchRemain: {
        Name: '',
      },
      dialog: false,
      statusInfo: [
        { text: '待审批', value: 0 },
        { text: '已审批', value: 1 },
        { text: '未通过', value: 2 },
      ],
    }
  },
  methods: {},

  async mounted() {},
}
</script>

<style></style>
