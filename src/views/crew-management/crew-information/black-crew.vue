<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :showExportButton="true"
      :search-remain="searchRemain"
      @dbclick="openCrewInfo"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="船员姓名"
            outlined
            dense
            clearable
            v-model="searchRemain.name"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="电话号码"
            outlined
            dense
            clearable
            v-model="searchRemain.phoneNumber"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="实际职务"
            clearable
            v-model="searchRemain.actualPosition"
          ></v-ship-station>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="Audit"
          v-permission="['船员黑名单管理:移出黑名单']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          移出黑名单
        </v-btn>
      </template>
    </v-table-searchable>
    <v-crew-card v-model="dialog" :initialData="initialData"></v-crew-card>
  </v-container>
</template>
<script>
import vCrewCard from '../private/v-crew-card.vue'
export default {
  components: { vCrewCard },
  name: 'black-crew',
  created() {
    this.tableName = '船员黑名单'
    this.reqUrl = '/business/crew/baseInfo/blackList'
    this.headers = [
      { text: '	中文姓名', value: 'chName' },
      { text: '	身份证号', value: 'idCard' },
      { text: '出生日期', value: 'birthDate' },
      { text: '手机号', value: 'phone' },
      { text: '籍贯', value: 'nativePlace' },
      { text: '实际职务', value: 'actualPosition' },
      { text: '证书职务', value: 'certificatePosition' },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
      initialData: {},
      dialog: false,
    }
  },

  methods: {
    async Audit() {
      if (!(await this.$dialog.msgbox.confirm('确定将该船员取出黑名单？')))
        return
      const { errorRaw } = await this.getAsync(
        `/business/crew/baseInfo/blackLst`,
        { crewId: this.selected.id, operation: false },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('成功加入黑名单')
      await this.$refs.table.loadTableData()
    },
    openCrewInfo(val) {
      this.initialData = { userId: val.userId }
      this.dialog = true
    },
  },

  mounted() {},
}
</script>

<style></style>
