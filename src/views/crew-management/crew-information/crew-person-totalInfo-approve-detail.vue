<template>
  <v-container fluid class="d-flex">
    <template>
      <v-container fluid>
        <v-form ref="form">
          <v-card>
            <v-card-title>船员个人信息(修改前)</v-card-title>
            <v-card-text>
              <v-expansion-panels
                multiple
                accordion
                v-model="panel_1"
                focusable
              >
                <v-expansion-panel>
                  <v-expansion-panel-header>基本信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2" v-if="detailInfoRead.empId">
                          <v-text-field
                            readonly
                            label="工号"
                            outlined
                            dense
                            v-model="detailInfoRead.empId"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="中文姓名"
                            outlined
                            dense
                            v-model="detailInfoRead.chName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="英文姓名"
                            outlined
                            dense
                            v-model="detailInfoRead.enName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="身份证号码"
                            outlined
                            dense="[rules.idcard]"
                            v-model="detailInfoRead.idCard"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="民族"
                            outlined
                            dense
                            v-model="detailInfoRead.nation"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            outlined
                            dense
                            label="出生日期"
                            v-model="detailInfoRead.birthDate"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            readonly
                            label="性别"
                            outlined
                            dense
                            :items="[
                              { text: '男', value: '1' },
                              { text: '女', value: '2' },
                            ]"
                            v-model="detailInfoRead.gender"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            readonly
                            label="国家"
                            outlined
                            dense
                            :items="coutryItems"
                            v-model="detailInfoRead.country"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="国籍"
                            outlined
                            dense
                            v-model="detailInfoRead.countryCode"
                          ></v-text-field>
                        </v-col>

                        <v-col cols="12" md="2">
                          <v-select
                            readonly
                            label="婚姻状况"
                            outlined
                            dense
                            :items="['未婚', '已婚', '离异']"
                            v-model="detailInfoRead.maritalStatus"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            readonly
                            label="政治面貌"
                            outlined
                            dense
                            :items="[
                              '共产党员',
                              '共青团员',
                              '群众',
                              '预备党员',
                              '民主党派成员',
                            ]"
                            v-model="detailInfoRead.politicsStatus"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="宗教信仰"
                            outlined
                            dense
                            v-model="detailInfoRead.religion"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="外语语种"
                            outlined
                            dense
                            v-model="detailInfoRead.foreignLanguages"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="外语水平"
                            outlined
                            dense
                            v-model="detailInfoRead.foreignLevel"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="6">
                          <v-text-field
                            readonly
                            label="籍贯"
                            dense
                            outlined
                            v-model="detailInfoRead.nativePlace"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                      <v-textarea
                        readonly
                        outlined
                        dense
                        label="有无病史(个人填写)"
                        v-model="detailInfoRead.medicalHistory"
                      ></v-textarea>
                      <v-textarea
                        readonly
                        outlined
                        dense
                        label="有无病史(公司填写)"
                        v-model="detailInfoRead.medicalHistoryCom"
                      ></v-textarea>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>体貌特征</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="身高"
                            outlined
                            dense
                            v-model="detailInfoRead.height"
                            suffix="cm"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="体重"
                            outlined
                            dense
                            v-model="detailInfoRead.weight"
                            suffix="kg"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            readonly
                            label="血型"
                            outlined
                            dense
                            :items="['A', 'AB', 'B', 'O']"
                            v-model="detailInfoRead.bloodType"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            readonly
                            label="工作服尺码"
                            outlined
                            dense
                            :items="[
                              160, 165, 170, 175, 180, 185, 190, 195, 200,
                            ]"
                            v-model="detailInfoRead.workClothesSize"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            readonly
                            label="工作鞋尺码"
                            outlined
                            dense
                            :items="[
                              35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46,
                            ]"
                            v-model="detailInfoRead.workShoeSize"
                          ></v-select>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>联系方式</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="手机号"
                            outlined
                            dense
                            v-model="detailInfoRead.phone"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="邮箱"
                            outlined
                            dense
                            v-model="detailInfoRead.email"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col md="6">
                          <v-textarea
                            readonly
                            label="邮寄地址"
                            outlined
                            dense
                            v-model="detailInfoRead.mailingAddress"
                          ></v-textarea>
                        </v-col>
                        <v-col md="6">
                          <v-textarea
                            readonly
                            label="家庭住址（请精确到门牌号）"
                            outlined
                            dense
                            v-model="detailInfoRead.homeAddress"
                          ></v-textarea>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>家庭成员</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card>
                      <v-table-list
                        ref="table"
                        v-model="selected"
                        :headers="headers"
                        :items="detailInfo.familyMember"
                        item-key="name"
                      ></v-table-list>
                    </v-card>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>
                    紧急联系人
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="紧急联系人"
                            outlined
                            dense
                            v-model="detailInfoRead.emergencyName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="电话"
                            outlined
                            dense
                            v-model="detailInfoRead.emergencyPhone"
                          ></v-text-field>
                        </v-col>
                        <v-col>
                          <v-text-field
                            readonly
                            label="地址"
                            outlined
                            dense
                            v-model="detailInfoRead.emergencyAddress"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>教育经历</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="最高学历"
                            outlined
                            dense
                            v-model="detailInfoRead.highestDegree"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3">
                          <v-text-field
                            readonly
                            label="毕业院校"
                            outlined
                            dense
                            v-model="detailInfoRead.graduateSchool"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3">
                          <v-text-field
                            readonly
                            dense
                            outlined
                            label="毕业时间"
                            v-model="detailInfoRead.graduationDate"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>职务信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="3">
                          <v-text-field
                            readonly
                            dense
                            outlined
                            label="实际职务"
                            v-model="detailInfoRead.actualPosition"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3">
                          <v-text-field
                            readonly
                            dense
                            outlined
                            label="证书职务"
                            v-model="detailInfoRead.certificatePosition"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>
                    银行卡信息
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="人民币开户行人姓名"
                            outlined
                            dense
                            v-model="detailInfoRead.rmbName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="人民币开户行"
                            outlined
                            dense
                            v-model="detailInfoRead.rmbBank"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="人民币开户银行分支机构"
                            outlined
                            dense
                            v-model="detailInfoRead.rmbBankBranch"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="人民币卡号"
                            outlined
                            dense
                            v-model="detailInfoRead.rmbCard"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="开户地省份"
                            outlined
                            dense
                            v-model="rmbAddressHeadRead"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="开户地城市(区)"
                            outlined
                            dense
                            v-model="rmbAddressTailRead"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="美元开户行"
                            dense
                            outlined
                            v-model="detailInfoRead.usdBank"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="美元开户银行分支机构中文名称"
                            outlined
                            dense
                            v-model="detailInfoRead.usdBankBranch"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="美元开户银行分支机构英文名称"
                            outlined
                            dense
                            v-model="detailInfoRead.usdBankBranchEn"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="美元卡号"
                            outlined
                            dense
                            v-model="detailInfoRead.usdCard"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="美元开户人姓名"
                            outlined
                            dense
                            v-model="detailInfoRead.usdName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="SWIFT CODE"
                            dense
                            outlined
                            v-model="detailInfoRead.swiftCode"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                      <v-attachment-read-only
                        title="银行卡照片附件美元银行卡图片附件"
                        :attachment="detailInfoRead.bankCardPicAttachment"
                        @change="changeCardPic"
                      ></v-attachment-read-only>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>照片附件</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-crew-pic-read
                      :ImagePicture="
                        detailInfoRead.identificationPhotoAttachment
                      "
                      @change="changePicture"
                    ></v-crew-pic-read>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>档案信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2">
                          <v-select
                            readonly
                            label="是否由公司保存档案"
                            outlined
                            dense
                            :items="[
                              { text: '是', value: true },
                              { text: '否', value: false },
                            ]"
                            v-model="detailInfoRead.archiveFlag"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            label="入职时间"
                            readonly
                            outlined
                            dense
                            v-model="detailInfoRead.entryTime"
                            required
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            label="工龄"
                            outlined
                            dense
                            v-model="detailInfoRead.workAge"
                            suffix="年"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="档案存放地点"
                            outlined
                            dense
                            v-model="detailInfoRead.archivePlace"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>其他信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-attachment-read-only
                        title="含船员手签名的加入申请（加盖手印）"
                        :attachment="detailInfoRead.crewAttachment.handleApply"
                        accept="all"
                      ></v-attachment-read-only>
                      <v-attachment-read-only
                        title="船员身份证复印件"
                        :attachment="detailInfoRead.crewAttachment.idCardCopy"
                        accept="all"
                        @change="changeIdCardCopy"
                      ></v-attachment-read-only>
                      <v-attachment-read-only
                        title="无工作或无缴纳社保证明"
                        :attachment="detailInfoRead.crewAttachment.proveNoWork"
                        accept="all"
                        @change="changeProveNoWork"
                      ></v-attachment-read-only>
                      <v-attachment-read-only
                        title="船员简历表"
                        :attachment="detailInfoRead.crewAttachment.resume"
                        accept="all"
                        @change="changeResume"
                      ></v-attachment-read-only>
                      <v-attachment-read-only
                        title="船员工作考评表（或业务部门推荐）"
                        :attachment="
                          detailInfoRead.crewAttachment.workScoreTable
                        "
                        accept="all"
                        @change="changeWorkScoreTable"
                      ></v-attachment-read-only>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>基本信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            label="企业微信号"
                            outlined
                            dense
                            readonly
                            v-model="detailInfoRead.wechatNumber"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>社保信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            label="社保信息"
                            outlined
                            dense
                            v-model="detailInfoRead.socialSecurity"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>船员属性</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            outlined
                            dense
                            label="船员管理公司"
                            :items="creFirst"
                            v-model="detailInfoRead.creProperty.creCompany"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            readonly
                            outlined
                            dense
                            label="船员性质"
                            :items="['自有船员', '外聘船员', '外包船员']"
                            v-model="detailInfoRead.creProperty.creFeature"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            readonly
                            outlined
                            dense
                            label="劳动合同签署公司"
                            v-model="detailInfoRead.creProperty.creType"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>测评结果</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-textarea
                        readonly
                        label="测评结果"
                        outlined
                        dense
                        v-model="detailInfoRead.testResult"
                      ></v-textarea>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-card-text>
          </v-card>
        </v-form>
      </v-container>
    </template>
    <template>
      <v-container fluid>
        <v-form ref="form">
          <v-card>
            <v-card-title>
              船员个人信息(修改后)
              <v-spacer></v-spacer>
              <v-btn
                v-if="show()"
                outlined
                tile
                :loading="loading1"
                class="mx-1"
                color="error"
                @click="drawBack"
                v-permission="['船员信息审批:审批']"
              >
                不通过
              </v-btn>
              <v-btn
                v-if="show()"
                :loading="loading2"
                outlined
                tile
                class="mx-1"
                color="green"
                @click="save"
                v-permission="['船员信息审批:审批']"
              >
                通过
              </v-btn>
            </v-card-title>
            <v-card-text>
              <v-expansion-panels
                multiple
                accordion
                v-model="panel_1"
                focusable
              >
                <v-expansion-panel>
                  <v-expansion-panel-header>基本信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2" v-if="detailInfo.empId">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.empId, detailInfo.empId)
                            "
                            readonly
                            label="工号"
                            outlined
                            dense
                            v-model="detailInfo.empId"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.chName, detailInfo.chName)
                            "
                            readonly
                            label="中文姓名"
                            outlined
                            dense
                            v-model="detailInfo.chName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.enName, detailInfo.enName)
                            "
                            readonly
                            label="英文姓名"
                            outlined
                            dense
                            v-model="detailInfo.enName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.idCard, detailInfo.idCard)
                            "
                            readonly
                            label="身份证号码"
                            outlined
                            dense
                            v-model="detailInfo.idCard"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.nation, detailInfo.nation)
                            "
                            readonly
                            label="民族"
                            outlined
                            dense
                            v-model="detailInfo.nation"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.birthDate,
                                detailInfo.birthDate,
                              )
                            "
                            readonly
                            outlined
                            dense
                            label="出生日期"
                            v-model="detailInfo.birthDate"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            :class="
                              differ(detailInfoRead.gender, detailInfo.gender)
                            "
                            readonly
                            label="性别"
                            outlined
                            dense
                            :items="[
                              { text: '男', value: '1' },
                              { text: '女', value: '2' },
                            ]"
                            v-model="detailInfo.gender"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            :class="
                              differ(detailInfoRead.country, detailInfo.country)
                            "
                            readonly
                            label="国家"
                            outlined
                            dense
                            :items="coutryItems"
                            v-model="detailInfo.country"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.countryCode,
                                detailInfo.countryCode,
                              )
                            "
                            readonly
                            label="国籍"
                            outlined
                            dense
                            v-model="detailInfo.countryCode"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            :class="
                              differ(
                                detailInfoRead.maritalStatus,
                                detailInfo.maritalStatus,
                              )
                            "
                            readonly
                            label="婚姻状况"
                            outlined
                            dense
                            :items="['未婚', '已婚', '离异']"
                            v-model="detailInfo.maritalStatus"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            :class="
                              differ(
                                detailInfoRead.politicsStatus,
                                detailInfo.politicsStatus,
                              )
                            "
                            readonly
                            label="政治面貌"
                            outlined
                            dense
                            :items="[
                              '共产党员',
                              '共青团员',
                              '群众',
                              '预备党员',
                              '民主党派成员',
                            ]"
                            v-model="detailInfo.politicsStatus"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.religion,
                                detailInfo.religion,
                              )
                            "
                            readonly
                            label="宗教信仰"
                            outlined
                            dense
                            v-model="detailInfo.religion"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.foreignLanguages,
                                detailInfo.foreignLanguages,
                              )
                            "
                            readonly
                            label="外语语种"
                            outlined
                            dense
                            v-model="detailInfo.foreignLanguages"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.foreignLevel,
                                detailInfo.foreignLevel,
                              )
                            "
                            readonly
                            label="外语水平"
                            outlined
                            dense
                            v-model="detailInfo.foreignLevel"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="6">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.nativePlace,
                                detailInfo.nativePlace,
                              )
                            "
                            readonly
                            label="籍贯"
                            dense
                            outlined
                            v-model="detailInfo.nativePlace"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                      <v-textarea
                        :class="
                          differ(
                            detailInfoRead.medicalHistory,
                            detailInfo.medicalHistory,
                          )
                        "
                        readonly
                        outlined
                        dense
                        label="有无病史(个人填写)"
                        v-model="detailInfo.medicalHistory"
                      ></v-textarea>
                      <v-textarea
                        :class="
                          differ(
                            detailInfoRead.medicalHistoryCom,
                            detailInfo.medicalHistoryCom,
                          )
                        "
                        readonly
                        outlined
                        dense
                        label="有无病史(公司填写)"
                        v-model="detailInfo.medicalHistoryCom"
                      ></v-textarea>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>体貌特征</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.height, detailInfo.height)
                            "
                            readonly
                            label="身高"
                            outlined
                            dense
                            v-model="detailInfo.height"
                            suffix="cm"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.weight, detailInfo.weight)
                            "
                            readonly
                            label="体重"
                            outlined
                            dense
                            v-model="detailInfo.weight"
                            suffix="kg"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            :class="
                              differ(
                                detailInfoRead.bloodType,
                                detailInfo.bloodType,
                              )
                            "
                            readonly
                            label="血型"
                            outlined
                            dense
                            :items="['A', 'AB', 'B', 'O']"
                            v-model="detailInfo.bloodType"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            :class="
                              differ(
                                detailInfoRead.workClothesSize,
                                detailInfo.workClothesSize,
                              )
                            "
                            readonly
                            label="工作服尺码"
                            outlined
                            dense
                            :items="[
                              160, 165, 170, 175, 180, 185, 190, 195, 200,
                            ]"
                            v-model="detailInfo.workClothesSize"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-select
                            :class="
                              differ(
                                detailInfoRead.workShoeSize,
                                detailInfo.workShoeSize,
                              )
                            "
                            readonly
                            label="工作鞋尺码"
                            outlined
                            dense
                            :items="[
                              35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46,
                            ]"
                            v-model="detailInfo.workShoeSize"
                          ></v-select>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>联系方式</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.phone, detailInfo.phone)
                            "
                            readonly
                            label="手机号"
                            outlined
                            dense
                            v-model="detailInfo.phone"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.email, detailInfo.email)
                            "
                            readonly
                            label="邮箱"
                            outlined
                            dense
                            v-model="detailInfo.email"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col md="6">
                          <v-textarea
                            :class="
                              differ(
                                detailInfoRead.mailingAddress,
                                detailInfo.mailingAddress,
                              )
                            "
                            readonly
                            label="邮寄地址"
                            outlined
                            dense
                            v-model="detailInfo.mailingAddress"
                          ></v-textarea>
                        </v-col>
                        <v-col md="6">
                          <v-textarea
                            :class="
                              differ(
                                detailInfoRead.homeAddress,
                                detailInfo.homeAddress,
                              )
                            "
                            readonly
                            label="家庭住址（请精确到门牌号）"
                            outlined
                            dense
                            v-model="detailInfo.homeAddress"
                          ></v-textarea>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>家庭成员</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card>
                      <v-table-list
                        ref="table"
                        v-model="selected"
                        :headers="headers"
                        :items="detailInfo.familyMember"
                        item-key="name"
                      ></v-table-list>
                    </v-card>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>
                    紧急联系人
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.emergencyName,
                                detailInfo.emergencyName,
                              )
                            "
                            readonly
                            label="紧急联系人"
                            outlined
                            dense
                            v-model="detailInfo.emergencyName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.emergencyPhone,
                                detailInfo.emergencyPhone,
                              )
                            "
                            readonly
                            label="电话"
                            outlined
                            dense
                            v-model="detailInfo.emergencyPhone"
                          ></v-text-field>
                        </v-col>
                        <v-col>
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.emergencyAddress,
                                detailInfo.emergencyAddress,
                              )
                            "
                            readonly
                            label="地址"
                            outlined
                            dense
                            v-model="detailInfo.emergencyAddress"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>教育经历</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.highestDegree,
                                detailInfo.highestDegree,
                              )
                            "
                            readonly
                            label="最高学历"
                            outlined
                            dense
                            v-model="detailInfo.highestDegree"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.graduateSchool,
                                detailInfo.graduateSchool,
                              )
                            "
                            readonly
                            label="毕业院校"
                            outlined
                            dense
                            v-model="detailInfo.graduateSchool"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.graduationDate,
                                detailInfo.graduationDate,
                              )
                            "
                            readonly
                            dense
                            outlined
                            label="毕业时间"
                            v-model="detailInfo.graduationDate"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>职务信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="3">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.actualPosition,
                                detailInfo.actualPosition,
                              )
                            "
                            readonly
                            dense
                            outlined
                            label="实际职务"
                            v-model="detailInfo.actualPosition"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.certificatePosition,
                                detailInfo.certificatePosition,
                              )
                            "
                            readonly
                            dense
                            outlined
                            label="证书职务"
                            v-model="detailInfo.certificatePosition"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>
                    银行卡信息
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.rmbName, detailInfo.rmbName)
                            "
                            readonly
                            label="人民币开户行人姓名"
                            outlined
                            dense
                            v-model="detailInfo.rmbName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.rmbBank, detailInfo.rmbBank)
                            "
                            readonly
                            label="人民币开户行"
                            outlined
                            dense
                            v-model="detailInfo.rmbBank"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.rmbBankBranch,
                                detailInfo.rmbBankBranch,
                              )
                            "
                            readonly
                            label="人民币开户银行分支机构"
                            outlined
                            dense
                            v-model="detailInfo.rmbBankBranch"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.rmbCard, detailInfo.rmbCard)
                            "
                            readonly
                            label="人民币卡号"
                            outlined
                            dense
                            v-model="detailInfo.rmbCard"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="differ(rmbAddressHeadRead, rmbAddressHead)"
                            readonly
                            label="开户地省份"
                            outlined
                            dense
                            v-model="rmbAddressHead"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="differ(rmbAddressTailRead, rmbAddressTail)"
                            readonly
                            label="开户地城市(区)"
                            outlined
                            dense
                            v-model="rmbAddressTail"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.usdBank, detailInfo.usdBank)
                            "
                            readonly
                            label="美元开户行"
                            dense
                            outlined
                            v-model="detailInfo.usdBank"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.usdBankBranch,
                                detailInfo.usdBankBranch,
                              )
                            "
                            readonly
                            label="美元开户银行分支机构中文名称"
                            outlined
                            dense
                            v-model="detailInfo.usdBankBranch"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.usdBankBranchEn,
                                detailInfo.usdBankBranchEn,
                              )
                            "
                            readonly
                            label="美元开户银行分支机构英文名称"
                            outlined
                            dense
                            v-model="detailInfo.usdBankBranchEn"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.usdCard, detailInfo.usdCard)
                            "
                            readonly
                            label="美元卡号"
                            outlined
                            dense
                            v-model="detailInfo.usdCard"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.usdName, detailInfo.usdName)
                            "
                            readonly
                            label="美元开户人姓名"
                            outlined
                            dense
                            v-model="detailInfo.usdName"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.swiftCode,
                                detailInfo.swiftCode,
                              )
                            "
                            readonly
                            label="SWIFT CODE"
                            dense
                            outlined
                            v-model="detailInfo.swiftCode"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                      <v-attachment-read-only
                        title="银行卡照片附件美元银行卡图片附件"
                        :attachment="detailInfo.bankCardPicAttachment"
                        @change="changeCardPic"
                      ></v-attachment-read-only>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>照片附件</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-crew-pic-read
                      :ImagePicture="detailInfo.identificationPhotoAttachment"
                      @change="changePicture"
                    ></v-crew-pic-read>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>档案信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="2">
                          <v-select
                            :class="
                              differ(
                                detailInfoRead.archiveFlag,
                                detailInfo.archiveFlag,
                              )
                            "
                            readonly
                            label="是否由公司保存档案"
                            outlined
                            dense
                            :items="[
                              { text: '是', value: true },
                              { text: '否', value: false },
                            ]"
                            v-model="detailInfo.archiveFlag"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            label="入职时间"
                            readonly
                            outlined
                            dense
                            :class="
                              differ(
                                detailInfoRead.entryTime,
                                detailInfo.entryTime,
                              )
                            "
                            v-model="detailInfo.entryTime"
                            required
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(detailInfoRead.workAge, detailInfo.workAge)
                            "
                            readonly
                            label="工龄"
                            outlined
                            dense
                            v-model="detailInfo.workAge"
                            suffix="年"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.archivePlace,
                                detailInfo.archivePlace,
                              )
                            "
                            readonly
                            label="档案存放地点"
                            outlined
                            dense
                            v-model="detailInfo.archivePlace"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>其他信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-attachment-read-only
                        title="含船员手签名的加入申请（加盖手印）"
                        :attachment="detailInfo.crewAttachment.handleApply"
                        accept="all"
                      ></v-attachment-read-only>
                      <v-attachment-read-only
                        title="船员身份证复印件"
                        :attachment="detailInfo.crewAttachment.idCardCopy"
                        accept="all"
                        @change="changeIdCardCopy"
                      ></v-attachment-read-only>
                      <v-attachment-read-only
                        title="无工作或无缴纳社保证明"
                        :attachment="detailInfo.crewAttachment.proveNoWork"
                        accept="all"
                        @change="changeProveNoWork"
                      ></v-attachment-read-only>
                      <v-attachment-read-only
                        title="船员简历表"
                        :attachment="detailInfo.crewAttachment.resume"
                        accept="all"
                        @change="changeResume"
                      ></v-attachment-read-only>
                      <v-attachment-read-only
                        title="船员工作考评表（或业务部门推荐）"
                        :attachment="detailInfo.crewAttachment.workScoreTable"
                        accept="all"
                        @change="changeWorkScoreTable"
                      ></v-attachment-read-only>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>基本信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.wechatNumber,
                                detailInfo.wechatNumber,
                              )
                            "
                            label="企业微信号"
                            outlined
                            dense
                            v-model="detailInfo.wechatNumber"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>社保信息</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.socialSecurity,
                                detailInfo.socialSecurity,
                              )
                            "
                            readonly
                            label="社保信息"
                            outlined
                            dense
                            v-model="detailInfo.socialSecurity"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>船员属性</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.creProperty.creCompany,
                                detailInfo.creProperty.creCompany,
                              )
                            "
                            readonly
                            outlined
                            dense
                            label="船员管理公司"
                            :items="creFirst"
                            v-model="detailInfo.creProperty.creCompany"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.creProperty.creFeature,
                                detailInfo.creProperty.creFeature,
                              )
                            "
                            outlined
                            dense
                            label="船员性质"
                            :items="['自有船员', '外聘船员', '外包船员']"
                            :readonly="isReadonly"
                            v-model="detailInfo.creProperty.creFeature"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            :class="
                              differ(
                                detailInfoRead.creProperty.creType,
                                detailInfo.creProperty.creType,
                              )
                            "
                            outlined
                            dense
                            label="劳动合同签署公司"
                            :items="creThird"
                            readonly
                            v-model="detailInfo.creProperty.creType"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <v-expansion-panel>
                  <v-expansion-panel-header>测评结果</v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-textarea
                        :class="
                          differ(
                            detailInfoRead.testResult,
                            detailInfo.testResult,
                          )
                        "
                        readonly
                        label="测评结果"
                        outlined
                        dense
                        v-model="detailInfo.testResult"
                      ></v-textarea>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-card-text>
          </v-card>
        </v-form>
      </v-container>
    </template>
  </v-container>
</template>
<script>
import VAttachmentReadOnly from './private/v-attachmentReadOnly.vue'
import vCrewPicRead from './private/v-crewPic-read.vue'
import routerControl from '@/mixin/routerControl'
export default {
  components: { vCrewPicRead, VAttachmentReadOnly },
  mixins: [routerControl],
  name: 'crew-person-totalInfo-approve-detail',
  inject: {
    form: { default: null },
  },
  created() {
    this.headers6 = [
      { text: '船员名称', value: 'creName' },
      { text: '船舶名称', value: 'shipName' },
      { text: '在船岗位', value: 'position' },
      { text: '岗位工资', value: 'actualSalary' },
      { text: '工资开始日期', value: 'actualSalaryBeginDate' },
    ]
    this.headers1 = [
      { text: '证书编号', value: 'code' },
      { text: '证书名称', value: 'certificateName' },
      { text: '证书职务', value: 'certificatePost' },
      { text: '证书类别', value: 'category' },
      { text: '海事局', value: 'msa' },
      { text: '船员', value: 'creName' },
      { text: '船舶种类描述', value: 'shipCategory' },
      { text: '国家地区', value: 'countryPart' },
      { text: '申办单位', value: 'applicant' },
      { text: '签发时间', value: 'signDate' },
      { text: '签发地点', value: 'signPlace' },
      { text: '到期时间', value: 'expireDate' },
      { text: '操作', value: 'operta' },
    ]
    this.backRouteName = 'crew-person-totalInfo-approve-list'
    this.subtitles = ['个人填写', '公司填写']
    this.headers = [
      { text: '姓名', value: 'name' },
      { text: '电话号码', value: 'phoneNo' },
      { text: '与本人关系', value: 'relation' },
    ]
    this.crePropertyHeaders = [
      { text: '劳动合同签署公司', value: 'creType' },
      { text: '劳动合同签署公司描述', value: 'creDescribe' },
    ]
    this.headers5 = [
      { text: '船名', value: 'shipName' },
      { text: '船舶类型', value: 'shipType' },
      { text: '船舶归属', value: 'selfShipFlag' },
      { text: '船员隶属公司', value: 'crewCompany' },
      { text: '上船时间', value: 'startDate' },
      { text: '上船地点', value: 'upBoardPlace' },
      { text: '下船时间', value: 'endDate' },
      { text: '下船地点', value: 'offBoardPlace' },
      { text: '下船原因', value: 'leaveReason' },
      { text: '在职天数', value: 'officeDay' },
      { text: '任职部门', value: 'officeDepartment' },
      { text: '任职岗位', value: 'officeJob' },
      { text: '载重吨（DWT）', value: 'dwt' },
      { text: '总吨（GT）', value: 'gtun' },
      { text: '主机类型', value: 'hostType' },
      { text: '主机功率（kw）', value: 'hostPower' },
      { text: '船东评价', value: 'opinion' },
    ]
    this.newCard = this.$route.params.id
  },
  data() {
    return {
      userId: '',
      title: '新增船员信息详情',
      dialog: false,
      loading1: false,
      loading2: false,
      isEdit: true,
      selected: false,
      CreproSelected: false,
      CreproDialog: false,
      creProperty: {},
      isReadonly: false, // 控制 二三级属性readonly 状态
      //修改前数据
      detailInfoRead: {
        country: '中国',
        countryCode: 'CN',
        familyMember: [],
        creProperty: { creFeature: '外聘船员' },
        crewAttachment: {},
        idCard: '',
        birthDate: '',
      },
      //修改后数据
      detailInfo: {
        country: '中国',
        countryCode: 'CN',
        familyMember: [],
        creProperty: { creFeature: '外聘船员' },
        crewAttachment: {},
        idCard: '',
        birthDate: '',
      },
      famliyPerson: {},
      panel_1: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      panel_2: [0, 1, 2, 3, 4],
      creFirst: [],
      creThird: [],
      creThirdAll: [],
      coutryItems: [],
      country: [],
      rmbAddressHeadRead: '',
      rmbAddressTailRead: '',
      rmbAddressHead: '',
      rmbAddressTail: '',
      workInfo: [],
      workInfoSelected: false,
      dialogTwo: false,
      initialData: {},
      initialUser: {},
      cardInfo: {
        baseInfo: { creProperty: {} },
        certificateInfo: {},
        cultivateInfo: {},
        deployInfo: {},
        jobChangeInfo: {},
        cardWork: {},
        deploySalaryInfo: {},
      },
    }
  },
  watch: {
    'detailInfo.idCard'(newVal) {
      if (newVal.length === 18) {
        // 从身份证号码中提取出生日期
        // 身份证的7-14位是出生年月日，格式为YYYYMMDD
        const year = newVal.substring(6, 10)
        const month = newVal.substring(10, 12)
        const day = newVal.substring(12, 14)
        const genderCode = newVal.charAt(16)
        this.detailInfo.gender = parseInt(genderCode) % 2 === 0 ? '2' : '1'
        this.detailInfo.socialSecurity = newVal
        // 设置出生日期字段的格式
        this.detailInfo.birthDate = `${year}-${month}-${day}`
      }
    },
    'detailInfo.chName': {
      handler(newValue) {
        this.getEnName(newValue)
        this.detailInfo.rmbName = this.detailInfo.chName
      },
    },
    'detailInfo.creProperty': {
      handler(newValue) {
        if (newValue.creCompany && newValue.creFeature) {
          this.getCreThird()
        }
        if (newValue.creType) {
          this.detailInfo.crePropertyId = this.creThirdAll.find(
            (val) => val.creType === newValue.creType,
          )?.id
        }
      },
      deep: true,
    },
    'detailInfo.country': {
      handler(newValue) {
        this.detailInfo.countryCode = this.country.find(
          (ele) => ele.country === newValue,
        )?.twoCode
      },
    },
  },

  methods: {
    //船员个人信息保存
    async save() {
      // console.log('detailInfo', this.detailInfo)
      if (!(await this.$dialog.msgbox.confirm('当前信息是否已确认完毕？')))
        return
      this.detailInfo.status = 1
      this.loading2 = true
      const { errorRaw } = await this.postAsync(
        `/business/crew/baseInfo/recordApprove/pass`,
        {
          ...this.detailInfo,
          crePropertyId: this.detailInfo.creProperty.id,
          rmbAddress: `${this.rmbAddressHead} ${this.rmbAddressTail}`,
        },
      )
      this.loading2 = false
      if (errorRaw) {
        this.$dialog.message.error(`保存失败！`)
        return
      }
      this.$dialog.message.success(`保存成功！`)
      await this.closeAndTo(this.backRouteName, {})
    },
    //船员个人信息审批不通过
    async drawBack() {
      if (!(await this.$dialog.msgbox.confirm('当前信息是否已确认完毕？')))
        return
      this.detailInfo.status = 2
      this.loading1 = true
      const { errorRaw } = await this.getAsync(
        `/business/crew/baseInfo/recordApprove/drawback/${this.$route.params.id}`,
      )
      this.loading1 = false
      if (errorRaw) {
        this.$dialog.message.error(`保存失败！`)
        return
      }
      this.$dialog.message.success(`保存成功！`)
      await this.closeAndTo(this.backRouteName, {})
    },
    async downloadPdf(item) {
      let arr = []
      arr.push(item.certificateId)
      const { data } = await this.postAsync(
        '/business/crew/certificate/exportCrewCertificate',
        arr,
      )
      // console.log(data)
      if (data) {
        data.forEach((item) => {
          this.dowloadPDFs(item)
        })
      }
    },
    async dowloadPDFs(item) {
      this.downPDF = `/api/system/file/download?fileName=${encodeURIComponent(
        item.fileName,
      )}&filePath=${item.filePath}`
      // console.log(this.downPDF)

      const link = this.$refs.downPDFHref
      link.href = this.downPDF
      link.download = this.extractFilename(this.downPDF)
      link.style.display = 'none'
      document.body.appendChild(link)

      // 模拟点击<a>标签以触发下载
      link.click()
    },
    extractFilename(url) {
      return url.substring(url.lastIndexOf('/') + 1)
    },
    getCategory(item) {
      const map = new Map([
        [0, '海员证'],
        [1, '培训合格证书'],
        [2, '船员服务簿'],
        [3, '适任证书'],
        [4, '海事局健康证明'],
        [5, '护照'],
        [6, '香港适任证书'],
        [7, '巴拿马适任证书'],
        [8, '疫苗接种或预防措施国际证书'],
        [9, '国际旅行健康检查证明书'],
      ])
      return map.get(item)
    },
    // 获取英文姓名
    async getEnName(newValue) {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/function/pinyin`,
        { chinese: newValue },
      )
      if (errorRaw) {
        return
      }
      this.detailInfo.enName = data[0]
    },
    // 附件相关函数
    changeCardPic(attachmentId) {
      this.detailInfo.bankCardPic = attachmentId
    },
    changeHandleApply(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.handleApply = attachmentId
    },
    changeIdCardCopy(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.idCardCopy = attachmentId
    },
    changeProveNoWork(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.proveNoWork = attachmentId
    },
    changeResume(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.resume = attachmentId
    },
    changeWorkScoreTable(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.workScoreTable = attachmentId
    },

    //图片上传
    changePicture(val) {
      this.detailInfo.identificationPhoto = val
    },
    //船员属性
    editElectronicchart() {
      this.CreproDialog = true
    },
    changeCrepro(val) {
      this.detailInfo.creProperty = val
      this.creProperty = [val]
      this.CreproSelected = false
    },
    delCrepro() {
      this.creProperty = []
      this.detailInfo.creProperty = {}
      this.CreproSelected = false
    },
    close() {
      this.CreproDialog = false
    },
    // 获取船员信息
    async getOriCrewInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/baseInfo/ApproveDetailAfter/${this.$route.params.id}`,
          { flag: 0 },
        )
        if (errorRaw) {
          return
        }
        this.userId = data.userId
        if (data.creProperty === null) {
          this.detailInfoRead = {
            crewUploadFileModifyDTO: {},
            crewAttachment: {},
            ...data,
            creProperty: {},
          }
        } else {
          this.detailInfoRead = {
            crewUploadFileModifyDTO: {},
            crewAttachment: {},
            ...data,
          }
        }
        this.creProperty = [data.creProperty]
        if (data.rmbAddress) {
          const arr = data.rmbAddress.split(' ')
          if (arr.length === 2) {
            this.rmbAddressHeadRead = arr[0]
            this.rmbAddressTailRead = arr[1]
          }
        }
        this.title = data.chName + '-----个人信息详情'
        // console.log('crewInfoRead', this.detailInfoRead)
      }
    },
    // 获取船员信息
    async getCrewInfo() {
      if (this.$route.params.id !== 'new') {
        const { errorRaw, data } = await this.getAsync(
          `/business/crew/baseInfo/ApproveDetailAfter/${this.$route.params.id}`,
          { flag: 1 },
        )
        if (errorRaw) {
          return
        }
        this.userId = data.userId
        if (data.creProperty === null) {
          this.detailInfo = {
            crewUploadFileModifyDTO: {},
            crewAttachment: {},
            ...data,
            creProperty: {},
          }
        } else {
          this.detailInfo = {
            crewUploadFileModifyDTO: {},
            crewAttachment: {},
            ...data,
          }
        }
        this.creProperty = [data.creProperty]
        if (data.rmbAddress) {
          const arr = data.rmbAddress.split(' ')
          if (arr.length === 2) {
            this.rmbAddressHead = arr[0]
            this.rmbAddressTail = arr[1]
          }
        }
        this.title = data.chName + '-----个人信息详情'
      }
      // console.log('crewInfo', this.detailInfo)
    },
    // 获取所有国家
    async getCountry() {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/function/country/list`,
      )
      if (errorRaw) {
        return
      }
      this.country = data
      this.coutryItems = data.map((val) => val.country)
    },
    //获取船员属性信息
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.creFirst = data
    },
    async getCreThird() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/list`,
        {
          creCompany: this.detailInfo.creProperty.creCompany,
          creFeature: this.detailInfo.creProperty.creFeature,
        },
      )
      if (errorRaw) {
        return
      }
      this.creThird = data.records.map((val) => val.creType)
      this.creThirdAll = data.records
      // if (
      //   this.detailInfo.creProperty.creCompany == '上海森海' ||
      //   this.detailInfo.creProperty.creCompany == '青岛英航' ||
      //   this.detailInfo.creProperty.creCompany == '青岛连航' ||
      //   this.detailInfo.creProperty.creCompany == '青岛成舟'
      // ) {
      //   this.detailInfo.creProperty.creType =
      //     this.detailInfo.creProperty.creCompany
      //   this.detailInfo.creProperty.creFeature = '外包船员'
      //   this.isReadonly = true
      // } else {
      //   this.isReadonly = false
      // }
    },
    // 获取船员履历信息
    async getWorkDetailInfo() {
      // console.log('creId', this.$route.params.id)
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/workHistory/list`,
        {
          creId: this.userId,
        },
      )
      if (errorRaw) {
        return
      }
      this.workInfo = data
      for (let i = 0; i < this.workInfo.length; i++) {
        this.workInfo[i].cid = i + 1
      }
    },
    async getCardDetailInfo() {
      // console.log('111', this.userId)
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/ifoCard/ByUserId?userId=${this.userId}`,
      )
      if (errorRaw) {
        return
      }
      this.cardInfo = data
      // console.log(this.cardInfo.certificateInfo.certificateOutputDTOList)
    },
    delAudit() {
      const len = this.workInfo.length
      const index = this.workInfo.findIndex(
        (ele) => ele.cid === this.workInfoSelected.cid,
      )
      this.workInfo.splice(index, 1)
      this.workInfoSelected = false
      for (let i = 0; i < this.workInfo.length; i++) {
        this.workInfo[i].cid = i + 1
      }
      if (len === this.workInfo.length) {
        this.$dialog.message.error('删除失败')
      } else {
        this.$dialog.message.success('删除成功')
      }
    },
    async upWorkInfo() {
      const { errorRaw } = await this.postAsync(
        `/business/crew/baseInfo/workHistory/batchSaveOrUpdate`,
        {
          modifyList: this.workInfo,
        },
      )
      if (errorRaw) {
        return errorRaw
      }
    },
    show() {
      // console.log('detailInfo', this.detailInfo)
      if (this.detailInfo.aprStatus === 0) {
        return true
      }
      return false
    },
    differ(before, after) {
      if (before === after) {
        return ''
      }
      return 'text-field-color'
    },
  },

  async mounted() {
    await this.getCountry()
    await this.getOriCrewInfo()
    await this.getCrewInfo()
    await this.getCreFirst()
    await this.getWorkDetailInfo()
    await this.getCardDetailInfo()
  },
}
</script>

<style scoped></style>
