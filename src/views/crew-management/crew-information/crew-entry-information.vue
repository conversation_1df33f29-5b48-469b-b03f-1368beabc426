<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :searchRemain="searchRemain"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-select
            label="审批状态"
            outlined
            dense
            required
            :items="[
              { text: '未开始', value: '1' },
              { text: '进行中', value: '2' },
              { text: '已完成', value: '3' },
              { text: '驳回', value: '4' },
            ]"
            v-model="searchRemain.status"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          left
          outlined
          tile
          class="mx-1"
          color="blue"
          @click="commitInfo"
          :disabled="!selected"
          v-permission="['船员入职信息审批:发起审批']"
        >
          <v-icon>mdi-arrow-up-bold</v-icon>
          发起审批
        </v-btn>
      </template>
      <template v-slot:[`item.crewLevel`]="{ item }">
        {{ item.crewLevel === 1 ? '高级船员' : '普通船员' }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip v-if="item.status === 1" color="" small dark>未开始</v-chip>
        <v-chip v-else-if="item.status === 2" color="info" small dark>
          进行中
        </v-chip>
        <v-chip v-else-if="item.status === 3" color="success" small dark>
          已完成
        </v-chip>
        <v-chip v-else color="error" small dark>驳回</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'crew-entry-information',
  created() {
    this.tableName = '入职船员审批列表'
    this.reqUrl = '/business/crew/infoCheck/page'
    this.pushParams = {
      name: 'crew-entry-detail',
    }
    this.headers = [
      { text: '船员姓名', value: 'creName' },
      { text: '身份证号', value: 'creIdNo' },
      { text: '信息审核人姓名', value: 'handlerName' },
      { text: '审批发起人姓名', value: 'submitUserName' },
      { text: '船员级别', value: 'crewLevel' },
      { text: '审批状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: false,
      searchRemain: {},
    }
  },

  methods: {
    async commitInfo() {
      if (!(await this.$dialog.msgbox.confirm('是否将选中记录发起审批？')))
        return
      if (this.selected.status === 2) {
        this.$dialog.message.error('该船员正在审批中，请勿重复提交！')
        return
      } else if (this.selected.status === 3) {
        this.$dialog.message.error('该船员已通过审批！')
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/crew/infoCheck/submit`,
        { checkId: this.selected.id },
      )
      if (errorRaw) {
        this.$dialog.message.error(`审批提交失败，请重试`)
        return
      }
      this.$dialog.message.success(`审核提交成功`)
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
