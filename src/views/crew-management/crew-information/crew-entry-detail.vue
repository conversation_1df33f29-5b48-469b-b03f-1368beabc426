<template>
  <v-container fluid>
    <v-detail-view
      :title="title"
      :tooltip="title"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['船员入职信息审批:编辑']"
    >
      <template
        v-slot:topcontent
        v-if="auditParams && auditParams.processInstanceId"
      >
        <v-card-text class="mt-2 pb-0">
          <v-audit ref="audit" :auditParams="auditParams"></v-audit>
        </v-card-text>
      </template>
      <template v-slot:个人填写信息>
        <v-expansion-panels multiple accordion v-model="panel" focusable>
          <v-expansion-panel>
            <v-expansion-panel-header>基本信息</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form" readonly>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="中文姓名"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.chName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="英文姓名"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.enName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <vs-date-picker
                        v-model="detailInfo.birthDate"
                        label="出生日期"
                        outlined
                        dense
                        :rules="[rules.required]"
                      ></vs-date-picker>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="民族"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.nation"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="性别"
                        outlined
                        dense
                        :items="[
                          { text: '男', value: '1' },
                          { text: '女', value: '2' },
                        ]"
                        :rules="[rules.required]"
                        v-model="detailInfo.gender"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="国家"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.country"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="国籍"
                        outlined
                        dense
                        :rules="[rules.required]"
                        readonly
                        v-model="detailInfo.countryCode"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="婚姻状况"
                        outlined
                        dense
                        :items="['未婚', '已婚', '离异']"
                        :rules="[rules.required]"
                        v-model="detailInfo.maritalStatus"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="政治面貌"
                        outlined
                        dense
                        :items="['党员', '团员', '群众']"
                        v-model="detailInfo.politicsStatus"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="宗教信仰"
                        outlined
                        dense
                        v-model="detailInfo.religion"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="外语语种"
                        outlined
                        dense
                        v-model="detailInfo.foreignLanguages"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="外语水平"
                        outlined
                        dense
                        v-model="detailInfo.foreignLevel"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="籍贯"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.nativePlace"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="身份证号码"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.idCard"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-textarea
                    outlined
                    dense
                    label="病历"
                    v-model="detailInfo.medicalHistory"
                  ></v-textarea>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>体貌特征</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form" readonly>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="身高(cm)"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.height"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="体重(kg)"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.weight"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="血型"
                        outlined
                        dense
                        :rules="[rules.required]"
                        :items="['A', 'AB', 'B', 'O']"
                        v-model="detailInfo.bloodType"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作服尺码"
                        outlined
                        dense
                        :rules="[rules.required]"
                        :items="[160, 165, 170, 175, 180, 185, 190, 195, 200]"
                        v-model="detailInfo.workClothesSize"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作鞋尺码"
                        outlined
                        dense
                        :rules="[rules.required]"
                        :items="[39, 40, 41, 42, 43, 44, 45, 46]"
                        v-model="detailInfo.workShoeSize"
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>联系方式</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form" readonly>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="手机号"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.phone"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="邮箱"
                        outlined
                        dense
                        v-model="detailInfo.email"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col md="6">
                      <v-textarea
                        label="邮寄地址"
                        outlined
                        :rules="[rules.required]"
                        dense
                        v-model="detailInfo.mailingAddress"
                      ></v-textarea>
                    </v-col>
                    <v-col md="6">
                      <v-textarea
                        label="家庭住址"
                        outlined
                        :rules="[rules.required]"
                        dense
                        v-model="detailInfo.homeAddress"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>家庭成员</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card>
                <v-card-title>船员家庭成员</v-card-title>
                <v-table-list
                  ref="table"
                  v-model="selected"
                  :headers="headers"
                  :items="detailInfo.familyMember"
                  item-key="name"
                ></v-table-list>
              </v-card>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>紧急联系人</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form" readonly>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="紧急联系人"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.emergencyName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="电话"
                        outlined
                        :rules="[rules.required]"
                        dense
                        v-model="detailInfo.emergencyPhone"
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <v-text-field
                        label="地址"
                        outlined
                        :rules="[rules.required]"
                        dense
                        v-model="detailInfo.emergencyAddress"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>教育经历</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form" readonly>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="最高学历"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.highestDegree"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="毕业院校"
                        outlined
                        dense
                        :rules="[rules.required]"
                        v-model="detailInfo.graduateSchool"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <vs-date-picker
                        dense
                        outlined
                        label="毕业时间"
                        :rules="[rules.required]"
                        v-model="detailInfo.graduationDate"
                      ></vs-date-picker>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>职务信息</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form" readonly>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        disabled
                        :rules="[rules.required]"
                        v-model="detailInfo.actualPosition"
                      ></v-ship-station>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        disabled
                        :rules="[rules.required]"
                        v-model="detailInfo.certificatePosition"
                      ></v-ship-station>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>银行卡信息</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form" readonly>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币开户行"
                        outlined
                        dense
                        v-model="detailInfo.rmbBank"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币开户银行分支机构"
                        outlined
                        dense
                        v-model="detailInfo.rmbBankBranch"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币卡号"
                        outlined
                        dense
                        v-model="detailInfo.rmbCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="人民币开户行人姓名"
                        outlined
                        dense
                        v-model="detailInfo.rmbName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="开户地省份"
                        outlined
                        dense
                        v-model="rmbAddressHead"
                        :rules="[rules.required]"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="开户地城市(区)"
                        outlined
                        dense
                        v-model="rmbAddressTail"
                        :rules="[rules.required]"
                        required
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元开户行"
                        dense
                        outlined
                        v-model="detailInfo.usdBank"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元开户银行分支机构中文名称"
                        outlined
                        dense
                        v-model="detailInfo.usdBankBranch"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元开户银行分支机构英文名称"
                        outlined
                        dense
                        v-model="detailInfo.usdBankBranchEn"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元卡号"
                        outlined
                        dense
                        v-model="detailInfo.usdCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="美元开户人姓名"
                        outlined
                        dense
                        v-model="detailInfo.usdName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="SWIFT CODE"
                        dense
                        outlined
                        v-model="detailInfo.swiftCode"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-attachment-only
                    disabled
                    title="银行卡照片附件美元银行卡图片附件"
                    :attachment="detailInfo.bankCardPicAttachment"
                  ></v-attachment-only>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>照片附件</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-crew-pic
                disabled
                :ImagePicture="detailInfo.identificationPhotoAttachment"
              ></v-crew-pic>
            </v-expansion-panel-content>
          </v-expansion-panel>
          <v-expansion-panel>
            <v-expansion-panel-header>其他信息</v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-card-text>
                <v-form ref="form" readonly>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-select
                        label="是否由公司保存档案"
                        outlined
                        dense
                        :items="[
                          { text: '是', value: true },
                          { text: '否', value: false },
                        ]"
                        v-model="detailInfo.archiveFlag"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="档案存放地点"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.archivePlace"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="工龄（岁）"
                        outlined
                        dense
                        v-model="detailInfo.workAge"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-attachment-only
                    title="含船员手签名的加入申请（加盖手印）"
                    disabled
                    :attachment="detailInfo.crewAttachment.handleApply"
                    accept="all"
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员身份证复印件"
                    disabled
                    :attachment="detailInfo.crewAttachment.idCardCopy"
                    accept="all"
                  ></v-attachment-only>
                  <v-attachment-only
                    title="无工作或无缴纳社保证明"
                    disabled
                    :attachment="detailInfo.crewAttachment.proveNoWork"
                    accept="all"
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员简历表"
                    disabled
                    :attachment="detailInfo.crewAttachment.resume"
                    accept="all"
                  ></v-attachment-only>
                  <v-attachment-only
                    title="船员工作考评表（或业务部门推荐）"
                    disabled
                    :attachment="detailInfo.crewAttachment.workScoreTable"
                    accept="all"
                  ></v-attachment-only>
                </v-form>
              </v-card-text>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </template>
      <template v-slot:公司填写信息>
        <v-container fluid>
          <v-expansion-panels multiple accordion v-model="panel_2" focusable>
            <v-expansion-panel>
              <v-expansion-panel-header>基本信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="企业微信号"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.wechatNumber"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>社保信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="社保信息"
                        outlined
                        dense
                        readonly
                        v-model="detailInfo.socialSecurity"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>船员属性</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-form ref="form" readonly>
                    <v-row>
                      <v-col cols="12" md="2">
                        <v-text-field
                          outlined
                          dense
                          label="调配公司"
                          v-model="detailInfo.creProperty.creCompany"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-select
                          outlined
                          dense
                          label="船员性质"
                          :items="['自有船员', '外聘船员']"
                          v-model="detailInfo.creProperty.creFeature"
                        ></v-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          outlined
                          dense
                          label="签署公司"
                          v-model="detailInfo.creProperty.creType"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>测评结果</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-textarea
                    label="测评结果"
                    outlined
                    dense
                    readonly
                    v-model="detailInfo.testResult"
                  ></v-textarea>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-container>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import vCrewPic from './private/v-crewPic.vue'
import VAttachmentOnly from './private/v-attachmentOnly.vue'
export default {
  components: { vCrewPic, VAttachmentOnly },
  name: 'crew-entry-detail',
  created() {
    this.backRouteName = 'crew-entry-information'
    this.subtitles = ['个人填写信息', '公司填写信息']
    this.headers = [
      { text: '姓名', value: 'name' },
      { text: '电话号码', value: 'phoneNo' },
      { text: '与本人关系', value: 'relation' },
    ]
  },
  data() {
    return {
      title: '审批信息详情',
      panel: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      panel_2: [0, 1, 2, 3],
      auditParams: false,
      detailInfo: {
        crewAttachment: {},
        creProperty: {},
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        idcard: (v) => /^\d{17}(\d|x|X)$/.test(v) || '请正确输入身份证号',
      },
      rmbAddressHead: '',
      rmbAddressTail: '',
      selected: false,
      loading: false,
      img: null,
      detail: {},
    }
  },

  methods: {
    async save(goBack) {
      if (!this.auditParams) {
        const { errorRaw } = await this.getAsync(
          `/business/crew/infoCheck/submit`,
          { checkId: this.$route.params.id },
        )
        if (errorRaw) {
          this.$dialog.message.error(`审批提交失败，请重试`)
          return
        }
        this.$dialog.message.success(`审核提交成功`)
      } else if (this.auditParams.isReject) {
        if (this.$refs.audit && !this.$refs.audit.validate()) {
          return
        }
        const error = await this.$refs.audit.submit()
        if (error) {
          this.$dialog.message.error(`审批提交失败，请重试`)
          return
        }
        this.$dialog.message.success(`审核提交成功`)
      } else {
        if (this.$refs.audit && !this.$refs.audit.validate()) {
          return
        }
        if (this.detail.status === 2) {
          const error = await this.$refs.audit.submit()
          if (error) {
            this.$dialog.message.error(`审批提交失败，请重试`)
            return
          }
          this.$dialog.message.success(`审核提交成功`)
        }
      }

      goBack()
    },
    async getDetailInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/infoCheck/detail/${this.$route.params.id}`,
      )
      if (errorRaw) {
        return
      }
      this.detail = data
      this.auditParams = data.auditParams
      this.detailInfo = data.infoBaseOutputDTO
      if (data.infoBaseOutputDTO.rmbAddress) {
        const arr = data.infoBaseOutputDTO.rmbAddress.split(' ')
        if (arr.length === 2) {
          this.rmbAddressHead = arr[0]
          this.rmbAddressTail = arr[1]
        }
      }
    },
  },

  async mounted() {
    await this.getDetailInfo()
  },
}
</script>

<style></style>
