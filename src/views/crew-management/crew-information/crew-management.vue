<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      @loadCompelte="handleTableLoaded"
      :fix-header="false"
      :searchRemain="searchRemain"
      :push-params="pushParams"
      :showExportButton="true"
    >
      <template #searchflieds>
        <v-col cols="12" md="1">
          <v-text-field
            label="中文姓名"
            outlined
            dense
            v-model="searchRemain.name"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="身份证号"
            outlined
            dense
            v-model="searchRemain.idCard"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-ship-station
            label="实际职务"
            clearable
            v-model="searchRemain.actualPosition"
          ></v-ship-station>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员管理公司"
            outlined
            dense
            required
            :items="newInfo"
            v-model="searchRemain.crePropertyId"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员性质"
            outlined
            dense
            required
            :items="secondInfo"
            v-model="searchRemain.creFeature"
            @change="getCreThird"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="劳动合同签署公司"
            required
            :items="creThird"
            v-model="searchRemain.creContract"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            outlined
            dense
            label="船员状态"
            required
            :items="statusInfo"
            v-model="searchRemain.status"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="是否暂存"
            outlined
            dense
            required
            clearable
            v-model="searchRemain.tempFlag"
            :items="approveFlagOptions"
            item-text="text"
            item-value="value"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          @click="resetPassword"
          v-permission="['船员信息管理:导出选中船员简历']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          重置船员密码
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          @click="openNewWindow"
          v-permission="['船员信息管理:导出选中船员简历']"
        >
          <v-icon>mdi-email-arrow-right-outline</v-icon>
          导出选中船员简历
        </v-btn>
        <v-import-more-btn
          :import-url="`/business/crew/baseInfo/excelsInfoImport`"
          @importSuccess="importSuccess"
          :buttonLabel="'导入船员信息'"
          v-permission="['船员信息管理:导入船员信息']"
        ></v-import-more-btn>
        <!-- <v-import-btn
          :import-url="`/business/crew/baseInfo/archive/import`"
          @importSuccess="importSuccess"
          v-permission="['船员信息管理:导入EXCEL']"
        ></v-import-btn> -->
        <v-btn
          outlined
          tile
          color="blue"
          class="mx-1"
          :href="`/api/business/crew/baseInfo/excel/template/download`"
          target="_blank"
          v-permission="['船员信息管理:船员档案模板下载']"
        >
          <v-icon left>mdi-arrow-down-bold</v-icon>
          船员信息导入模板下载
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="blue"
          class="mx-1"
          :href="`/api/business/crew/baseInfo/archive/template/download`"
          target="_blank"
          v-permission="['船员信息管理:船员档案模板下载']"
        >
          <v-icon left>mdi-arrow-down-bold</v-icon>
          船员档案模板下载
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/crew-management/crew-account-information/crew-information/new"
          v-permission="['船员信息管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          :loading="loading1"
          color="error"
          class="mx-1"
          @click="Audit"
          v-permission="['船员信息管理:加入黑名单']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          加入黑名单
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="dialogSpecial = true"
          v-permission="['船员信息管理:加入特殊管理']"
        >
          <v-icon left>mdi-alert-octagon</v-icon>
          特殊管理
        </v-btn>
      </template>
      <template v-slot:[`item.creStatus`]="{ item }">
        {{ getLabel(item.creStatus) }}
      </template>
      <template v-slot:[`item.crePropertyCompany`]="{ item }">
        {{ item.creProperty && item.creProperty.creCompany }}
      </template>
      <template v-slot:[`item.crePropertyType`]="{ item }">
        {{ item.creProperty && item.creProperty.creType }}
      </template>
      <template v-slot:[`item.crePropertyFeature`]="{ item }">
        {{ item.creProperty && item.creProperty.creFeature }}
      </template>
      <template v-slot:[`item.approveFlag`]="{ item }">
        <v-chip v-if="item.approveFlag" color="success" small dark>
          已提交
        </v-chip>
        <v-chip v-else color="error" small dark>未提交</v-chip>
      </template>
      <template v-slot:[`item.tempFlag`]="{ item }">
        <v-chip v-if="item.tempFlag" color="success" small dark>暂存</v-chip>
        <v-chip v-else color="grew" small dark>未暂存</v-chip>
      </template>
    </v-table-searchable>
    <v-dialog v-model="dialogSpecial" max-width="500">
      <v-card>
        <v-card-title class="headline">特殊管理操作</v-card-title>
        <v-card-text>
          <v-radio-group v-model="specialType" mandatory>
            <v-radio label="警告" value="1"></v-radio>
            <v-radio label="慎用" value="2"></v-radio>
          </v-radio-group>
          <v-textarea
            v-model="specialRemark"
            label="情况说明"
            outlined
            rows="3"
            :rules="[(v) => !!v || '必须填写情况说明']"
          ></v-textarea>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="dialogSpecial = false">取消</v-btn>
          <v-btn color="primary" @click="handleSpecialManagement">确认</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'crew-management',
  mixins: [dictHelper],
  created() {
    this.tableName = '船员信息管理'
    this.reqUrl = '/business/crew/baseInfo/page'
    this.headers = [
      { text: '中文姓名', value: 'chName' },
      { text: '身份证号', value: 'idCard' },
      { text: '出生日期', value: 'birthDate' },
      { text: '手机号', value: 'phone' },
      { text: '籍贯', value: 'nativePlace', hideDefault: true },
      { text: '毕业院校', value: 'graduateSchool', hideDefault: true },
      { text: '船员管理公司', value: 'crePropertyCompany' },
      { text: '船员性质', value: 'crePropertyFeature' },
      { text: '劳动合同签署公司', value: 'crePropertyType' },
      { text: '实际职务', value: 'actualPosition' },
      { text: '证书职务', value: 'certificatePosition' },
      { text: '企业微信号', value: 'wechatNumber', hideDefault: true },
      { text: '船员状态', value: 'creStatus' },
      { text: '船名', value: 'shipName' },
      { text: '是否暂存', value: 'tempFlag' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = {
      name: 'crew-information-detail',
    }
  },

  data() {
    return {
      selected: false,
      loading1: false,
      dialogSpecial: false,
      specialType: '',
      specialRemark: '',
      searchRemain: {
        creProperty: {},
      },
      dictMap: [],
      plan: {},
      newInfo: [],
      secondInfo: [],
      creThird: [],
      approveFlagOptions: [
        { text: '是', value: true },
        { text: '否', value: false },
      ],
      statusInfo: [
        { text: '新员工', value: 1 },
        { text: '公休', value: 2 },
        { text: '已安排船舶待上船', value: 3 },
        { text: '在船', value: 4 },
        { text: '上船前面试', value: 5 },
        { text: '培训', value: 6 },
        { text: '退休', value: 7 },
        { text: '失踪', value: 8 },
        { text: '去世', value: 9 },
        { text: '考试中', value: 10 },
      ],

      baseURL:
        'https://jk.sitc.com/webroot/decision/view/report?viewlet=Test%252FADMIN_CENTER%252FCrewResume.cpt&',
    }
  },
  // watch: {
  //   searchRemain: {
  //     handler(newValue) {
  //       if (newValue.crePropertyId && newValue.creFeature) {
  //         console.log(this)
  //         this.getCreThird()
  //       }
  //     },
  //     deep: true, // 这里设置深度监听
  //   },
  // },

  methods: {
    handleTableLoaded() {
      //此方法为通用导出方法中增加船员属性
      this.$refs.table.items.forEach((item) => {
        if (item.creProperty && item.creProperty.creType) {
          item.crePropertyType = item.creProperty.creType
          item.crePropertyFeature = item.creProperty.creFeature
          item.crePropertyCompany = item.creProperty.creCompany
        }
      })

      console.log('处理后的数据：', this.$refs.table.items)
    },

    async resetPassword() {
      const { errorRaw, data } = await this.postAsync(
        `/system/user/resetPassword`,
        { id: this.selected.userId, password: '123' },
      )
      if (errorRaw) {
        return
      }
      if (data) {
        this.$dialog.message.success('操作成功')
      }
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
      await this.$nextTick()
    },
    getLabel(value) {
      return this.dictMap.find((item) => item.dictValue == value)?.dictLabel
    },
    openNewWindow() {
      console.log('选中', this.selected)
      let url = this.baseURL + '__bypagesize__=false&'
      let idCard = this.selected.idCard
      url = url + 'id_card=' + idCard
      let creChName = this.selected.chName
      creChName = creChName + '_简历'
      url = url + '&__filename__=' + creChName
      window.open(url, '_blank')
    },
    async Audit() {
      if (!(await this.$dialog.msgbox.confirm('确定将该船员加入黑名单？')))
        return
      this.loading1 = true
      const { errorRaw } = await this.getAsync(
        `/business/crew/baseInfo/blackLst`,
        { crewId: this.selected.id, operation: true },
      )
      this.loading1 = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('成功加入黑名单')
      await this.$refs.table.loadTableData()
    },
    async handleSpecialManagement() {
      if (!this.specialType || !this.specialRemark) {
        this.$dialog.message.error('请完整填写特殊管理信息')
        return
      }

      const confirm = await this.$dialog.msgbox.confirm(
        '确认执行此特殊管理操作？',
      )
      if (!confirm) return

      const { errorRaw } = await this.getAsync(
        '/business/crew/baseInfo/specialManagement',
        {
          crewId: this.selected.id,
          operation: true,
          specialStatus: this.specialType,
          specialRemark: this.specialRemark,
        },
      )

      if (!errorRaw) {
        this.$dialog.message.success('操作成功')
        this.dialogSpecial = false
        await this.$refs.table.loadTableData()
        // 重置表单
        this.specialType = ''
        this.specialRemark = ''
      }
    },
    async getCreFirst() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/firstProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.newInfo = data
    },
    async getCreSecond() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/secondProperty/list`,
      )
      if (errorRaw) {
        return
      }
      this.secondInfo = data
    },
    async getCreThird() {
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/crewProperty/list`,
        {
          creCompany: this.searchRemain.crePropertyId,
          creFeature: this.searchRemain.creFeature,
        },
      )
      if (errorRaw) {
        return
      }
      this.creThird = []
      console.log(this.creThird)
      this.creThird = data.records.map((val) => {
        return { text: val.creType, value: val.id }
      })
      // this.creThirdAll = data.records
    },
  },

  async mounted() {
    this.dictMap = await this.getDictByType('crew_status')
    await this.getCreFirst()
    await this.getCreSecond()
    await this.getCreThird()
  },
}
</script>

<style></style>
