<template>
  <v-container fluid class="d-flex">
    <v-form ref="form">
      <v-card>
        <v-card-title>船员个人信息(修改前)</v-card-title>
        <v-card-text>
          <v-expansion-panels multiple accordion v-model="panel" focusable>
            <v-expansion-panel>
              <v-expansion-panel-header>基本信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="中文姓名"
                        readonly
                        outlined
                        dense
                        v-model="detailInfoRead.chName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="英文姓名"
                        outlined
                        dense
                        v-model="detailInfoRead.enName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <!--                      <vs-date-picker-->
                      <!--                        v-model="detailInfoRead.birthDate"-->
                      <!--                        label="出生日期"-->
                      <!--                        outlined-->
                      <!--                        dense-->
                      <!--                        -->
                      <!--                        -->
                      <!--                      ></vs-date-picker>-->
                      <v-text-field
                        readonly
                        label="出生日期"
                        outlined
                        dense
                        v-model="detailInfo.birthDate"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="民族"
                        outlined
                        dense
                        v-model="detailInfoRead.nation"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="性别"
                        readonly
                        outlined
                        dense
                        :items="[
                          { text: '男', value: '1' },
                          { text: '女', value: '2' },
                        ]"
                        v-model="detailInfoRead.gender"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="国家"
                        readonly
                        outlined
                        dense
                        :items="coutryItems"
                        v-model="detailInfoRead.country"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="国籍"
                        outlined
                        dense
                        readonly
                        v-model="detailInfoRead.countryCode"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="婚姻状况"
                        readonly
                        outlined
                        dense
                        :items="['未婚', '已婚', '离异']"
                        v-model="detailInfoRead.maritalStatus"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="政治面貌"
                        readonly
                        outlined
                        dense
                        :items="[
                          '共产党员',
                          '共青团员',
                          '群众',
                          '预备党员',
                          '民主党派成员',
                        ]"
                        v-model="detailInfoRead.politicsStatus"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="宗教信仰"
                        outlined
                        dense
                        v-model="detailInfoRead.religion"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="外语语种"
                        outlined
                        dense
                        v-model="detailInfoRead.foreignLanguages"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="外语水平"
                        outlined
                        dense
                        v-model="detailInfoRead.foreignLevel"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        readonly
                        label="籍贯"
                        outlined
                        dense
                        v-model="detailInfoRead.nativePlace"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        readonly
                        label="身份证号码"
                        outlined
                        dense
                        v-model="detailInfoRead.idCard"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-textarea
                    outlined
                    dense
                    readonly
                    label="有无病史"
                    v-model="detailInfoRead.medicalHistory"
                  ></v-textarea>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>体貌特征</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="身高"
                        outlined
                        dense
                        v-model="detailInfoRead.height"
                        suffix="cm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="体重"
                        outlined
                        dense
                        v-model="detailInfoRead.weight"
                        suffix="kg"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="血型"
                        readonly
                        outlined
                        dense
                        :items="['A', 'AB', 'B', 'O']"
                        v-model="detailInfoRead.bloodType"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作服尺码"
                        readonly
                        outlined
                        dense
                        :items="[160, 165, 170, 175, 180, 185, 190, 195, 200]"
                        v-model="detailInfoRead.workClothesSize"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        label="工作鞋尺码"
                        readonly
                        outlined
                        dense
                        :items="[39, 40, 41, 42, 43, 44, 45, 46]"
                        v-model="detailInfoRead.workShoeSize"
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>联系方式</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="手机号"
                        outlined
                        dense
                        v-model="detailInfoRead.phone"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="邮箱"
                        outlined
                        dense
                        v-model="detailInfoRead.email"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col md="6">
                      <v-textarea
                        label="邮寄地址"
                        readonly
                        outlined
                        dense
                        v-model="detailInfoRead.mailingAddress"
                      ></v-textarea>
                    </v-col>
                    <v-col md="6">
                      <v-textarea
                        label="家庭住址"
                        readonly
                        outlined
                        dense
                        v-model="detailInfoRead.homeAddress"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>家庭成员</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card>
                  <v-table-list
                    ref="table"
                    v-model="selected"
                    :headers="headers"
                    :items="detailInfoRead.familyMember"
                    item-key="name"
                  ></v-table-list>
                </v-card>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>紧急联系人</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="紧急联系人"
                        outlined
                        dense
                        v-model="detailInfoRead.emergencyName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="电话"
                        outlined
                        dense
                        v-model="detailInfoRead.emergencyPhone"
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <v-text-field
                        readonly
                        label="地址"
                        outlined
                        dense
                        v-model="detailInfoRead.emergencyAddress"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>教育经历</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="最高学历"
                        outlined
                        dense
                        v-model="detailInfoRead.highestDegree"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="毕业院校"
                        outlined
                        dense
                        v-model="detailInfoRead.graduateSchool"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <!--                      <vs-date-picker-->
                      <!--                        dense-->
                      <!--                        outlined-->
                      <!--                        label="毕业时间"-->
                      <!--                        -->
                      <!--                        v-model="detailInfoRead.graduationDate"-->
                      <!--                        -->
                      <!--                      ></vs-date-picker>-->
                      <v-text-field
                        readonly
                        label="毕业时间"
                        outlined
                        dense
                        v-model="detailInfo.graduationDate"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>职务信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <!--                      <v-ship-station-->
                      <!--                        aria-readonly="true"-->
                      <!--                        label="实际职务"-->
                      <!--                        -->
                      <!--                        v-model="detailInfoRead.actualPosition"-->
                      <!--                        -->
                      <!--                      ></v-ship-station>-->
                      <v-text-field
                        readonly
                        label="实际职务"
                        outlined
                        dense
                        v-model="detailInfo.actualPosition"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <!--                      <v-ship-station-->
                      <!--                        label="证书职务"-->
                      <!--                        -->
                      <!--                        v-model="detailInfoRead.certificatePosition"-->
                      <!--                        -->
                      <!--                      ></v-ship-station>-->
                      <v-text-field
                        readonly
                        label="证书职务"
                        outlined
                        dense
                        v-model="detailInfo.certificatePosition"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>
                银行卡信息（仅限招商银行）
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="人民币开户行"
                        outlined
                        dense
                        v-model="detailInfoRead.rmbBank"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="人民币开户银行分支机构"
                        outlined
                        dense
                        v-model="detailInfoRead.rmbBankBranch"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="人民币卡号"
                        outlined
                        dense
                        v-model="detailInfoRead.rmbCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="人民币开户行人姓名"
                        outlined
                        dense
                        v-model="detailInfoRead.rmbName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="开户地省份"
                        outlined
                        dense
                        v-model="rmbAddressHeadRead"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="开户地城市(区)"
                        outlined
                        dense
                        v-model="rmbAddressTailRead"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="美元开户行"
                        dense
                        outlined
                        v-model="detailInfoRead.usdBank"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="美元开户银行分支机构中文名称"
                        outlined
                        dense
                        v-model="detailInfoRead.usdBankBranch"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="美元开户银行分支机构英文名称"
                        outlined
                        dense
                        v-model="detailInfoRead.usdBankBranchEn"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="美元卡号"
                        outlined
                        dense
                        v-model="detailInfoRead.usdCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="美元开户人姓名"
                        outlined
                        dense
                        v-model="detailInfoRead.usdName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="SWIFT CODE"
                        dense
                        outlined
                        v-model="detailInfoRead.swiftCode"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-attachment-read-only
                    title="银行卡照片附件美元银行卡图片附件"
                    :attachment="detailInfoRead.bankCardPicAttachment"
                  ></v-attachment-read-only>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>照片附件</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-crew-pic-read
                  :ImagePicture="detailInfoRead.identificationPhotoAttachment"
                ></v-crew-pic-read>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>档案信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-select
                        readonly
                        label="是否由公司保存档案"
                        outlined
                        dense
                        :items="[
                          { text: '是', value: true },
                          { text: '否', value: false },
                        ]"
                        v-model="detailInfoRead.archiveFlag"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="入职时间"
                        readonly
                        outlined
                        dense
                        v-model="detailInfoRead.entryTime"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="工龄"
                        outlined
                        dense
                        v-model="detailInfoRead.workAge"
                        suffix="年"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        label="档案存放地点"
                        outlined
                        dense
                        v-model="detailInfoRead.archivePlace"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>其他信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-attachment-read-only
                    title="含船员手签名的加入申请（加盖手印）"
                    :attachment="detailInfoRead.crewAttachment.handleApply"
                    accept="all"
                  ></v-attachment-read-only>
                  <v-attachment-read-only
                    title="船员身份证复印件"
                    :attachment="detailInfoRead.crewAttachment.idCardCopy"
                    accept="all"
                  ></v-attachment-read-only>
                  <v-attachment-read-only
                    title="无工作或无缴纳社保证明"
                    :attachment="detailInfoRead.crewAttachment.proveNoWork"
                    accept="all"
                  ></v-attachment-read-only>
                  <v-attachment-read-only
                    title="船员简历表"
                    :attachment="detailInfoRead.crewAttachment.resume"
                    accept="all"
                  ></v-attachment-read-only>
                  <v-attachment-read-only
                    title="船员工作考评表（或业务部门推荐）"
                    :attachment="detailInfoRead.crewAttachment.workScoreTable"
                    accept="all"
                  ></v-attachment-read-only>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </v-card>
    </v-form>
    <v-form ref="form">
      <v-card>
        <v-card-title>
          船员个人信息(修改后)
          <v-spacer></v-spacer>
          <v-btn
            v-if="show()"
            :loading="loading1"
            outlined
            tile
            class="mx-1"
            color="error"
            @click="drawBack"
            v-permission="['船员个人信息修改审批:审批']"
          >
            不通过
          </v-btn>
          <v-btn
            v-if="show()"
            outlined
            :loading="loading2"
            tile
            class="mx-1"
            color="green"
            @click="save"
            v-permission="['船员个人信息修改审批:审批']"
          >
            通过
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-expansion-panels multiple accordion v-model="panel" focusable>
            <v-expansion-panel>
              <v-expansion-panel-header>基本信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.chName, detailInfo.chName)
                        "
                        readonly
                        label="中文姓名"
                        outlined
                        dense
                        v-model="detailInfo.chName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        :class="
                          differ(detailInfoRead.enName, detailInfo.enName)
                        "
                        label="英文姓名"
                        outlined
                        dense
                        v-model="detailInfo.enName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <!--                      <vs-date-picker-->
                      <!--                        v-model="detailInfo.birthDate"-->
                      <!--                        label="出生日期"-->
                      <!--                        outlined-->
                      <!--                        dense-->
                      <!--                        -->
                      <!--                        -->
                      <!--                      ></vs-date-picker>-->
                      <v-text-field
                        readonly
                        :class="
                          differ(detailInfoRead.birthDate, detailInfo.birthDate)
                        "
                        label="出生日期"
                        outlined
                        dense
                        v-model="detailInfo.birthDate"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.nation, detailInfo.nation)
                        "
                        readonly
                        label="民族"
                        outlined
                        dense
                        v-model="detailInfo.nation"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-select
                        :class="
                          differ(detailInfoRead.gender, detailInfo.gender)
                        "
                        label="性别"
                        readonly
                        outlined
                        dense
                        :items="[
                          { text: '男', value: '1' },
                          { text: '女', value: '2' },
                        ]"
                        v-model="detailInfo.gender"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.country, detailInfo.country)
                        "
                        readonly
                        label="国家"
                        outlined
                        dense
                        v-model="detailInfo.country"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.countryCode,
                            detailInfo.countryCode,
                          )
                        "
                        readonly
                        label="国籍"
                        outlined
                        dense
                        v-model="detailInfo.countryCode"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.maritalStatus,
                            detailInfo.maritalStatus,
                          )
                        "
                        readonly
                        label="婚姻状况"
                        outlined
                        dense
                        v-model="detailInfo.maritalStatus"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.politicsStatus,
                            detailInfo.politicsStatus,
                          )
                        "
                        readonly
                        label="政治面貌"
                        outlined
                        dense
                        v-model="detailInfo.politicsStatus"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.religion, detailInfo.religion)
                        "
                        readonly
                        label="宗教信仰"
                        outlined
                        dense
                        v-model="detailInfo.religion"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.foreignLanguages,
                            detailInfo.foreignLanguages,
                          )
                        "
                        readonly
                        label="外语语种"
                        outlined
                        dense
                        v-model="detailInfo.foreignLanguages"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.foreignLevel,
                            detailInfo.foreignLevel,
                          )
                        "
                        readonly
                        label="外语水平"
                        outlined
                        dense
                        v-model="detailInfo.foreignLevel"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.nativePlace,
                            detailInfo.nativePlace,
                          )
                        "
                        readonly
                        label="籍贯"
                        outlined
                        dense
                        v-model="detailInfo.nativePlace"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.idCard, detailInfo.idCard)
                        "
                        readonly
                        label="身份证号码"
                        outlined
                        dense
                        v-model="detailInfo.idCard"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-textarea
                    :class="
                      differ(
                        detailInfoRead.medicalHistory,
                        detailInfo.medicalHistory,
                      )
                    "
                    outlined
                    dense
                    readonly
                    label="有无病史"
                    v-model="detailInfo.medicalHistory"
                  ></v-textarea>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>体貌特征</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.height, detailInfo.height)
                        "
                        readonly
                        label="身高"
                        outlined
                        dense
                        v-model="detailInfo.height"
                        suffix="cm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.weight, detailInfo.weight)
                        "
                        readonly
                        label="体重"
                        outlined
                        dense
                        v-model="detailInfo.weight"
                        suffix="kg"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.bloodType, detailInfo.bloodType)
                        "
                        readonly
                        label="血型"
                        outlined
                        dense
                        v-model="detailInfo.bloodType"
                      ></v-text-field>
                      <!--                      <v-select-->
                      <!--                        label="血型"-->
                      <!--                        readonly-->
                      <!--                        outlined-->
                      <!--                        dense-->
                      <!--                        -->
                      <!--                        :items="['A', 'AB', 'B', 'O']"-->
                      <!--                        v-model="detailInfo.bloodType"-->
                      <!--                        -->
                      <!--                      ></v-select>-->
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.workClothesSize,
                            detailInfo.workClothesSize,
                          )
                        "
                        readonly
                        label="工作服尺码"
                        outlined
                        dense
                        v-model="detailInfo.workClothesSize"
                      ></v-text-field>
                      <!--                      <v-select-->
                      <!--                        label="工作服尺码"-->
                      <!--                        readonly-->
                      <!--                        outlined-->
                      <!--                        dense-->
                      <!--                        -->
                      <!--                        :items="[165, 170, 175, 180, 185, 190, 195, 200]"-->
                      <!--                        v-model="detailInfo.workClothesSize"-->
                      <!--                        -->
                      <!--                      ></v-select>-->
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.workShoeSize,
                            detailInfo.workShoeSize,
                          )
                        "
                        readonly
                        label="工作鞋尺码"
                        outlined
                        dense
                        v-model="detailInfo.workShoeSize"
                      ></v-text-field>
                      <!--                      <v-select-->
                      <!--                        label="工作鞋尺码"-->
                      <!--                        readonly-->
                      <!--                        outlined-->
                      <!--                        dense-->
                      <!--                        -->
                      <!--                        :items="[39, 40, 41, 42, 43, 44, 45, 46]"-->
                      <!--                        v-model="detailInfo.workShoeSize"-->
                      <!--                        -->
                      <!--                      ></v-select>-->
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>联系方式</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="differ(detailInfoRead.phone, detailInfo.phone)"
                        readonly
                        label="手机号"
                        outlined
                        dense
                        v-model="detailInfo.phone"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="differ(detailInfoRead.email, detailInfo.email)"
                        readonly
                        label="邮箱"
                        outlined
                        dense
                        v-model="detailInfo.email"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col md="6">
                      <v-textarea
                        :class="
                          differ(
                            detailInfoRead.mailingAddress,
                            detailInfo.mailingAddress,
                          )
                        "
                        label="邮寄地址"
                        readonly
                        outlined
                        dense
                        v-model="detailInfo.mailingAddress"
                      ></v-textarea>
                    </v-col>
                    <v-col md="6">
                      <v-textarea
                        :class="
                          differ(
                            detailInfoRead.homeAddress,
                            detailInfo.homeAddress,
                          )
                        "
                        label="家庭住址"
                        readonly
                        outlined
                        dense
                        v-model="detailInfo.homeAddress"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>家庭成员</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card>
                  <v-table-list
                    ref="table"
                    v-model="selected"
                    :headers="headers"
                    :items="detailInfoRead.familyMember"
                    item-key="name"
                  ></v-table-list>
                </v-card>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>紧急联系人</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.emergencyName,
                            detailInfo.emergencyName,
                          )
                        "
                        readonly
                        label="紧急联系人"
                        outlined
                        dense
                        v-model="detailInfo.emergencyName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.emergencyPhone,
                            detailInfo.emergencyPhone,
                          )
                        "
                        readonly
                        label="电话"
                        outlined
                        dense
                        v-model="detailInfo.emergencyPhone"
                      ></v-text-field>
                    </v-col>
                    <v-col>
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.emergencyAddress,
                            detailInfo.emergencyAddress,
                          )
                        "
                        readonly
                        label="地址"
                        outlined
                        dense
                        v-model="detailInfo.emergencyAddress"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>教育经历</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.highestDegree,
                            detailInfo.highestDegree,
                          )
                        "
                        readonly
                        label="最高学历"
                        outlined
                        dense
                        v-model="detailInfo.highestDegree"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.graduateSchool,
                            detailInfo.graduateSchool,
                          )
                        "
                        readonly
                        label="毕业院校"
                        outlined
                        dense
                        v-model="detailInfo.graduateSchool"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <!--                      <vs-date-picker-->
                      <!--                        dense-->
                      <!--                        outlined-->
                      <!--                        label="毕业时间"-->
                      <!--                        -->
                      <!--                        v-model="detailInfo.graduationDate"-->
                      <!--                        -->
                      <!--                      ></vs-date-picker>-->
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.graduationDate,
                            detailInfo.graduationDate,
                          )
                        "
                        readonly
                        label="毕业时间"
                        outlined
                        dense
                        v-model="detailInfo.graduationDate"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>职务信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.actualPosition,
                            detailInfo.actualPosition,
                          )
                        "
                        readonly
                        label="实际职务"
                        outlined
                        dense
                        v-model="detailInfo.actualPosition"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <!--                      <v-ship-station-->
                      <!--                        label="证书职务"-->
                      <!--                        -->
                      <!--                        v-model="detailInfo.certificatePosition"-->
                      <!--                        -->
                      <!--                      ></v-ship-station>-->
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.certificatePosition,
                            detailInfo.certificatePosition,
                          )
                        "
                        readonly
                        label="证书职务"
                        outlined
                        dense
                        v-model="detailInfo.certificatePosition"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>
                银行卡信息（仅限招商银行）
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.rmbBank, detailInfo.rmbBank)
                        "
                        readonly
                        label="人民币开户行"
                        outlined
                        dense
                        v-model="detailInfo.rmbBank"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.rmbBankBranch,
                            detailInfo.rmbBankBranch,
                          )
                        "
                        readonly
                        label="人民币开户银行分支机构"
                        outlined
                        dense
                        v-model="detailInfo.rmbBankBranch"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.rmbCard, detailInfo.rmbCard)
                        "
                        readonly
                        label="人民币卡号"
                        outlined
                        dense
                        v-model="detailInfo.rmbCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.rmbName, detailInfo.rmbName)
                        "
                        readonly
                        label="人民币开户行人姓名"
                        outlined
                        dense
                        v-model="detailInfo.rmbName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="differ(rmbAddressHeadRead, rmbAddressHead)"
                        readonly
                        label="开户地省份"
                        outlined
                        dense
                        v-model="rmbAddressHead"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="differ(rmbAddressTailRead, rmbAddressTail)"
                        readonly
                        label="开户地城市(区)"
                        outlined
                        dense
                        v-model="rmbAddressTail"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.usdBank, detailInfo.usdBank)
                        "
                        readonly
                        label="美元开户行"
                        dense
                        outlined
                        v-model="detailInfo.usdBank"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.usdBankBranch,
                            detailInfo.usdBankBranch,
                          )
                        "
                        readonly
                        label="美元开户银行分支机构中文名称"
                        outlined
                        dense
                        v-model="detailInfo.usdBankBranch"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.usdBankBranchEn,
                            detailInfo.usdBankBranchEn,
                          )
                        "
                        readonly
                        label="美元开户银行分支机构英文名称"
                        outlined
                        dense
                        v-model="detailInfo.usdBankBranchEn"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.usdCard, detailInfo.usdCard)
                        "
                        readonly
                        label="美元卡号"
                        outlined
                        dense
                        v-model="detailInfo.usdCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        readonly
                        :class="
                          differ(detailInfoRead.usdName, detailInfo.usdName)
                        "
                        label="美元开户人姓名"
                        outlined
                        dense
                        v-model="detailInfo.usdName"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.swiftCode, detailInfo.swiftCode)
                        "
                        readonly
                        label="SWIFT CODE"
                        dense
                        outlined
                        v-model="detailInfo.swiftCode"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-attachment-read-only
                    title="银行卡照片附件美元银行卡图片附件"
                    :attachment="detailInfo.bankCardPicAttachment"
                  ></v-attachment-read-only>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>照片附件</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-crew-pic-read
                  :ImagePicture="detailInfo.identificationPhotoAttachment"
                ></v-crew-pic-read>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>档案信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="2">
                      <v-select
                        :class="
                          differ(
                            detailInfoRead.archiveFlag,
                            detailInfo.archiveFlag,
                          )
                        "
                        readonly
                        label="是否由公司保存档案"
                        outlined
                        dense
                        :items="[
                          { text: '是', value: true },
                          { text: '否', value: false },
                        ]"
                        v-model="detailInfo.archiveFlag"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="入职时间"
                        readonly
                        outlined
                        dense
                        :class="
                          differ(detailInfoRead.entryTime, detailInfo.entryTime)
                        "
                        v-model="detailInfo.entryTime"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(detailInfoRead.workAge, detailInfo.workAge)
                        "
                        readonly
                        label="工龄"
                        outlined
                        dense
                        v-model="detailInfo.workAge"
                        suffix="年"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-text-field
                        :class="
                          differ(
                            detailInfoRead.archivePlace,
                            detailInfo.archivePlace,
                          )
                        "
                        readonly
                        label="档案存放地点"
                        outlined
                        dense
                        v-model="detailInfo.archivePlace"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header>其他信息</v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-attachment-read-only
                    title="含船员手签名的加入申请（加盖手印）"
                    :attachment="detailInfo.crewAttachment.handleApply"
                    accept="all"
                  ></v-attachment-read-only>
                  <v-attachment-read-only
                    title="船员身份证复印件"
                    :attachment="detailInfo.crewAttachment.idCardCopy"
                    accept="all"
                  ></v-attachment-read-only>
                  <v-attachment-read-only
                    title="无工作或无缴纳社保证明"
                    :attachment="detailInfo.crewAttachment.proveNoWork"
                    accept="all"
                  ></v-attachment-read-only>
                  <v-attachment-read-only
                    title="船员简历表"
                    :attachment="detailInfo.crewAttachment.resume"
                    accept="all"
                  ></v-attachment-read-only>
                  <v-attachment-read-only
                    title="船员工作考评表（或业务部门推荐）"
                    :attachment="detailInfo.crewAttachment.workScoreTable"
                    accept="all"
                  ></v-attachment-read-only>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </v-card>
    </v-form>
  </v-container>
</template>
<script>
import VAttachmentReadOnly from './private/v-attachmentReadOnly.vue'
import vCrewPicRead from './private/v-crewPic-read.vue'
import routerControl from '@/mixin/routerControl'
export default {
  components: { VAttachmentReadOnly, vCrewPicRead },
  mixins: [routerControl],
  name: 'crew-personal-information-approve-detail',
  created() {
    this.backRouteName = 'crew-personal-information-approve-list'
    this.headers = [
      { text: '姓名', value: 'name' },
      { text: '电话号码', value: 'phoneNo' },
      { text: '与本人关系', value: 'relation' },
    ]
  },
  watch: {
    'detailInfo.country': {
      handler(newValue) {
        this.detailInfo.countryCode = this.country.find(
          (ele) => ele.country === newValue,
        )?.twoCode
      },
    },
    'detailInfo.chName': {
      async handler(newValue) {
        if (newValue) {
          const { errorRaw, data } = await this.getAsync(
            `/business/common/function/pinyin`,
            { chinese: newValue },
          )
          if (errorRaw) {
            return
          }
          this.detailInfo.enName = data[0]
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      title: '船员个人信息',
      backRouteName: '',
      detailInfo: {
        country: '中国',
        countryCode: 'CN',
        crewAttachment: {},
      },
      detailInfoRead: {
        country: '中国',
        countryCode: 'CN',
        crewAttachment: {},
      },
      selected: false,
      loading1: false,
      loading2: false,
      famliyPerson: {},
      panel: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      isEdit: false,
      dialog: false,
      coutryItems: [],
      country: [],
      approveFlag: false,
      rmbAddressHeadRead: '',
      rmbAddressTailRead: '',
      rmbAddressHead: '',
      rmbAddressTail: '',
    }
  },

  methods: {
    //获取国家信息
    async getCountry() {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/function/country/list`,
      )
      if (errorRaw) {
        return
      }
      this.country = data
      this.coutryItems = data.map((val) => val.country)
    },
    //获取修改前账户个人信息
    async getOriOwnInfo() {
      console.log('route', this.$route.params)
      await this.getCountry()
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/ApproveDetailAfter/${this.$route.params.id}`,
        { flag: 0 },
      )
      if (errorRaw) {
        return
      }
      this.approveFlag = data ? false : true
      this.detailInfoRead = {
        crewAttachment: {},
        crewUploadFileModifyDTO: {},
        familyMember: [],
        country: '中国',
        countryCode: 'CN',
        ...data,
      }
      if (data.rmbAddress) {
        const arr = data.rmbAddress.split(' ')
        if (arr.length === 2) {
          this.rmbAddressHeadRead = arr[0]
          this.rmbAddressTailRead = arr[1]
        }
      }
    },
    //获取修改后账户个人信息
    async getOwnInfo() {
      console.log('route', this.$route.params)
      await this.getCountry()
      const { errorRaw, data } = await this.getAsync(
        `/business/crew/baseInfo/ApproveDetailAfter/${this.$route.params.id}`,
        { flag: 1 },
      )
      if (errorRaw) {
        return
      }
      this.approveFlag = data ? false : true
      this.detailInfo = {
        crewAttachment: {},
        crewUploadFileModifyDTO: {},
        familyMember: [],
        country: '中国',
        countryCode: 'CN',
        userId: this.$local.data.get('userInfo').id,
        ...data,
      }
      if (data.rmbAddress) {
        const arr = data.rmbAddress.split(' ')
        if (arr.length === 2) {
          this.rmbAddressHead = arr[0]
          this.rmbAddressTail = arr[1]
        }
      }
      console.log('operatorId', this.detailInfo.operatorId)
      console.log('detailInfo', this.detailInfo)
    },
    //船员个人信息保存
    async save() {
      this.loading2 = true
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      console.log('detailInfo', this.detailInfo)
      if (!(await this.$dialog.msgbox.confirm('当前信息是否已确认完毕？')))
        return
      const { errorRaw } = await this.postAsync(
        `/business/crew/baseInfo/recordApprove/pass`,
        {
          ...this.detailInfo,
          rmbAddress: `${this.rmbAddressHead} ${this.rmbAddressTail}`,
        },
      )
      this.loading2 = false
      if (errorRaw) {
        this.$dialog.message.error(`保存失败！`)
        return
      }
      this.$dialog.message.success(`保存成功！`)
      await this.closeAndTo(this.backRouteName, {})
    },
    //船员个人信息审批不通过
    async drawBack() {
      this.loading1 = true
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('当前信息是否已确认完毕？')))
        return
      const { errorRaw } = await this.getAsync(
        `/business/crew/baseInfo/recordApprove/drawback/${this.$route.params.id}`,
      )
      this.loading1 = false
      if (errorRaw) {
        this.$dialog.message.error(`保存失败！`)
        return
      }
      this.$dialog.message.success(`保存成功！`)
      await this.closeAndTo(this.backRouteName, {})
    },
    // 新船员个人信息提交
    async commitInfo() {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('当前信息是否已确认完毕？')))
        return
      const { errorRaw } = await this.postAsync(
        `/business/crew/baseInfo/init`,
        this.detailInfo,
      )
      if (errorRaw) {
        this.$dialog.message.error(`审批提交失败！`)
        return
      }
      await this.getOwnInfo()
      await this.getOriOwnInfo()
      this.$dialog.message.success(`审批提交成功！`)
    },
    //增加家庭成员的探窗开启函数
    addFamily() {
      this.isEdit = true
      this.dialog = true
    },
    //修改紧急联系人按钮
    change() {
      this.dialog = true
      this.isEdit = false
      this.famliyPerson = this.selected
    },
    //删除紧急联系人
    deleteItem() {
      let index = this.detailInfo.familyMember.indexOf(this.selected)
      this.detailInfo.familyMember.splice(index, 1)
    },
    //添加紧急联系人
    upData() {
      if (this.isEdit) {
        this.detailInfo.familyMember.push(this.famliyPerson)
        this.dialog = false
        this.famliyPerson = {}
      } else {
        this.dialog = false
      }
    },
    // 附件相关函数
    changeCardPic(attachmentId) {
      this.detailInfo.bankCardPic = attachmentId
    },
    changeHandleApply(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.handleApply = attachmentId
    },
    changeIdCardCopy(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.idCardCopy = attachmentId
    },
    changeProveNoWork(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.proveNoWork = attachmentId
    },
    changeResume(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.resume = attachmentId
    },
    changeWorkScoreTable(attachmentId) {
      this.detailInfo.crewUploadFileModifyDTO.workScoreTable = attachmentId
    },
    // //图片上传
    // changePicture(val) {
    //   this.detailInfo.identificationPhoto = val
    // },
    show() {
      if (this.detailInfo.aprStatus === 0) {
        return true
      }
      return false
    },
    differ(before, after) {
      if (before === after) {
        return ''
      }
      return 'text-field-color'
    },
  },

  async beforeMount() {
    await this.getOwnInfo()
    await this.getOriOwnInfo()
  },
}
</script>

<style scoped></style>
