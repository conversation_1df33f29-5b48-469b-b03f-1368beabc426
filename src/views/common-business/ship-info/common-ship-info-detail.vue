<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['船舶信息:编辑']"
      :title="`船舶信息-${isEdit ? formData.shipBase.chShipName : '新增'}`"
      backRouteName="common-ship-info-list"
      :subtitles="subTitles"
      :tooltip="isEdit ? formData.shipBase.chShipName : '新增'"
      @save="save"
    >
      <template v-slot:基础信息>
        <v-form ref="form">
          <v-card-text>
            <v-row>
              <v-col
                v-for="t in basicTextFields"
                :key="t.value"
                cols="12"
                md="3"
                class="py-0"
              >
                <v-text-field
                  v-if="t.value === 'shipCode'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required, rules.required2]"
                  dense
                  :readonly="isEdit"
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else-if="t.value === 'chShipName'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required, rules.required2]"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else-if="t.value === 'enShipName'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required, rules.required2]"
                  dense
                  outlined
                ></v-text-field>
                <v-select
                  :items="船型选项"
                  v-else-if="t.value === 'shipType'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required, rules.required2]"
                  dense
                  outlined
                ></v-select>
                <v-dict-select
                  dict-type="port_registry"
                  v-else-if="t.value === 'flagPort'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-dict-select>
                <v-dict-select
                  dict-type="flag_state"
                  v-else-if="t.value === 'flagState'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  :rules="[rules.required]"
                  outlined
                ></v-dict-select>
                <v-dict-select
                  dict-type="classification_society"
                  v-else-if="t.value === 'classification'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  outlined
                  :rules="[rules.required, rules.required2]"
                ></v-dict-select>
                <v-select
                  :items="是否二手船选项"
                  v-else-if="t.value === 'secondHand'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  :rules="[rules.required]"
                  outlined
                ></v-select>
                <v-select
                  :items="是否支付伙食费"
                  v-else-if="t.value === 'boardWages'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  :rules="[rules.required]"
                  outlined
                ></v-select>
                <v-select
                  :items="是否自引船舶选项"
                  v-else-if="t.value === 'selfPilot'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  :rules="[rules.required]"
                  outlined
                ></v-select>
                <v-select
                  :items="船舶状态选项"
                  v-else-if="t.value === 'shipStatus'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  :rules="[rules.required, rules.required2]"
                  outlined
                ></v-select>
                <v-dict-select
                  dict-type="series_ship"
                  v-else-if="t.value === 'seriesShip'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                ></v-dict-select>
                <v-dict-select
                  :dict-type="t.dict"
                  v-else-if="!!t.dict"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                ></v-dict-select>
                <v-text-field
                  v-else
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col
                v-for="t in basicIntgerTextFields"
                :key="t.value"
                cols="12"
                md="3"
                class="py-0"
              >
                <v-select
                  :items="付款公司选项"
                  v-if="t.value === 'paymentCompany'"
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
                <v-text-field
                  v-else
                  :label="t.label"
                  v-model="formData.shipBase[t.value]"
                  dense
                  type="number"
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-dict-select
                  dict-type="ship_management_company"
                  label="管理公司"
                  v-model="formData.shipBase.manageCompany"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-dict-select
                  dict-type="ship_crew_conpany"
                  label="船员公司"
                  v-model="formData.shipBase.crewCompany"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-text-field
                  label="船东公司"
                  v-model="formData.shipBase.ownerCompany"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template #大宗集采供应商信息按钮>
        <v-btn
          :disabled="!formData.shipBase.shipCode"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createSup"
          v-permission="['大宗集采供应商信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedSup"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="updateSup"
          v-permission="['大宗集采供应商信息:修改']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedSup"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delSup"
          v-permission="['大宗集采供应商信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click="saveSup"
          v-permission="['大宗集采供应商信息:保存']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          保存
        </v-btn>
      </template>
      <template v-slot:大宗集采供应商信息>
        <v-card-text>
          <v-table-list
            :headers="pendingHeaders"
            :items="supplyPriceModifyList"
            v-model="selectedSup"
            :ship-code="formData.shipBase.shipCode"
            item-key="vid"
          ></v-table-list>
          <!-- <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="pendingHeaders"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
    ></v-table-searchable> -->
        </v-card-text>
      </template>
      <template v-slot:通导信息>
        <v-form ref="form2">
          <v-card-text>
            <v-row>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  label="*船舶呼号"
                  v-model="formData.shipImo.callSign"
                  dense
                  outlined
                  :rules="[rules.required2]"
                ></v-text-field>
              </v-col>
              <v-col
                class="py-0"
                v-for="t in ImoTextFields"
                :key="t.value"
                cols="12"
                md="3"
              >
                <v-text-field
                  :label="t.label"
                  v-model="formData.shipImo[t.value]"
                  dense
                  outlined
                  :rules="
                    formData.shipImo[t.value]
                      ? [t.type === 'email' ? rules.email : rules.required]
                      : []
                  "
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:船舶参数>
        <v-form ref="form1">
          <v-card-text>
            <v-row>
              <v-col
                v-for="t in shipParamsTextFields"
                :key="t.value"
                cols="12"
                md="3"
                class="py-0"
              >
                <vs-date-picker
                  v-if="t.value === 'keelLaidDate'"
                  :label="t.label"
                  v-model="formData.shipParams[t.value]"
                  dense
                  :rules="[rules.required, rules.required2]"
                  outlined
                ></vs-date-picker>
                <vs-date-picker
                  v-else-if="t.type == 'date'"
                  v-model="formData.shipParams[t.value]"
                  :label="t.label"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></vs-date-picker>
                <v-text-field
                  v-else-if="t.value === 'shipAge'"
                  :label="t.label"
                  v-model="formData.shipParams[t.value]"
                  disabled
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  :label="t.label"
                  v-model="formData.shipParams[t.value]"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>

              <v-col
                v-for="t in shipParamsIntgerTextFields"
                :key="t.value"
                cols="12"
                md="3"
                class="py-0"
              >
                <v-text-field
                  :label="t.label"
                  v-model="formData.shipParams[t.value]"
                  dense
                  type="number"
                  outlined
                  :rules="[!!t.canEmpty ? true : rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="救生艇种类"
                  v-model="formData.shipParams['lifeboat']"
                  outlined
                  dense
                  :items="['重力式', '尾抛式']"
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
            </v-row>
            <v-row class="text-h6">
              <b>设备核心参数:</b>
              {{ equipments.join('、') }}
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template #坞修信息>
        <v-container fluid>
          <v-row>
            <v-col class="pt-0" cols="12">只读信息不可编辑</v-col>
            <v-col
              class="py-0"
              v-for="h in 坞修信息字段"
              :key="h.value"
              cols="12"
              md="3"
            >
              <vs-date-picker
                v-if="h.type == 'date'"
                v-model="dockRepairEvaluationOutputDTO[h.value]"
                :label="h.text"
                outlined
                dense
                readonly
              ></vs-date-picker>
              <v-select
                v-else-if="h.type == 'yn'"
                v-model="dockRepairEvaluationOutputDTO[h.value]"
                :label="h.text"
                outlined
                dense
                readonly
                :items="yn"
              ></v-select>
              <v-dict-select
                v-else-if="h.type == 'dict'"
                dict-type="dock_type"
                v-model="dockRepairEvaluationOutputDTO[h.value]"
                :label="h.text"
                readonly
              ></v-dict-select>
              <v-text-field
                v-else-if="h.type == 'readonly'"
                v-model="dockRepairEvaluationOutputDTO[h.value]"
                :label="h.text"
                dense
                readonly
                outlined
                disabled
              ></v-text-field>
              <v-text-field
                v-else
                v-model="dockRepairEvaluationOutputDTO[h.value]"
                :label="h.text"
                dense
                readonly
                outlined
              ></v-text-field>
            </v-col>
            <v-col class="py-0" cols="12">
              <v-textarea
                v-model="dockRepairEvaluationOutputDTO.remark"
                label="备注"
                outlined
                dense
                readonly
              ></v-textarea>
            </v-col>
          </v-row>
        </v-container>
      </template>
      <template v-slot:船舱信息>
        只读信息不可编辑
        <v-table-list
          :headers="carbinHeaders"
          :items="shipCarbin"
          :search-dicts="carbinSearchDicts"
        ></v-table-list>
      </template>
      <template v-slot:工资标准>
        只读信息不可编辑
        <v-table-list
          :headers="salaryHeaders"
          :items="shipSalary"
        ></v-table-list>
      </template>
      <template v-slot:管理信息>
        <v-card-text>
          <v-table-list
            :headers="船舶主管"
            :items="shipDirector"
            v-model="selected"
          ></v-table-list>
        </v-card-text>
      </template>
      <!-- <template v-if="isEdit" v-slot:管理信息按钮>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="dialog = true"
        >
          <v-icon left>mdi-pencil</v-icon>
          编辑
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template> -->
    </v-detail-view>
    <manager-select
      @success="loadShipDirector"
      :users="shipDirector"
      v-model="dialog"
    ></manager-select>
    <pending-sup
      v-model="dialogSup"
      @success="successSup"
      :initial-data="initialDataSup"
      :shipCode="formData.shipBase.shipCode"
    ></pending-sup>
  </v-container>
</template>
<script>
import ManagerSelect from './private/manager-select.vue'
import PendingSup from './private/pendingSup.vue'
export default {
  components: { ManagerSelect, PendingSup },
  name: 'common-ship-info-detail',
  created() {
    this.subTitles = [
      '基础信息',
      '大宗集采供应商信息',
      '通导信息',
      '船舶参数',
      '坞修信息',
      '船舱信息',
      '管理信息',
      '工资标准',
    ]
    this.pendingHeaders = [
      { text: '供应商', value: 'supplierName' },
      { text: '币种', value: 'currencyCode' },
      { text: '费用科目', value: 'subjectName' },
      { text: '开始时间', value: 'startTime' },
      { text: '结束时间', value: 'endTime' },
      { text: '备注', value: 'remark' },
    ]
    this.basicTextFields = [
      { label: '*中文船名', value: 'chShipName' },
      { label: '*英文船名', value: 'enShipName' },
      { label: '*船舶编号', value: 'shipCode' },
      { label: '*船级社', value: 'classification' },
      { label: '*船籍港', value: 'flagPort' },
      { label: '*船旗国', value: 'flagState' },
      { label: '*保险公司', value: 'insuranceCompany' },
      { label: '*SAP利润中心代码', value: 'sapCenterCode' },
      { label: '*SAP CODE', value: 'sapCode' },
      { label: '*RO（体系）', value: 'roIsm' },
      { label: '*系列船舶', value: 'seriesShip' },
      { label: '*船型', value: 'shipType' },
      { label: '*船舶状态', value: 'shipStatus' },
      { label: '*二手', value: 'secondHand' },
      { label: '*是否支付伙食费', value: 'boardWages' },
      { label: '*自引', value: 'selfPilot' },
      { label: '*油漆品牌', value: 'paintSupport', dict: 'paint_brand' },
      {
        label: '*化学品品牌',
        value: 'chemicalSupport',
        dict: 'chemical_brand',
      },
      { label: '*滑油品牌', value: 'greaseSupport', dict: 'grease_brand' },
      { label: '航线', value: 'route', dict: 'sea_way_type' },
    ]
    this.basicIntgerTextFields = [
      { label: '*最高配员', value: 'maximumSafeManning' },
      { label: '*最低配员', value: 'minimumSafeManning' },
      { label: '*标准配员', value: 'standardSafeManning' },
      { label: '*付款公司', value: 'paymentCompany' },
    ]
    this.ImoTextFields = [
      // { label: '*船舶呼号', value: 'callSign' },
      { label: '*C站号码', value: 'cNo' },
      { label: '*FBB电话', value: 'fbbNo' },
      { label: '*FO电话', value: 'foNo' },
      { label: '*国外手机号码', value: 'foreignTelephoneNo' },
      { label: '*IMO编号', value: 'imoNumber' },
      { label: '*IRRIDIUM电话', value: 'irridiumNo' },
      { label: '*LRIT号码', value: 'lritNo' },
      { label: '*1749邮箱', value: 'mail1749', type: 'email' },
      { label: '*RYDEX邮箱', value: 'mailRydex', type: 'email' },
      { label: '*MMSI', value: 'mmsi' },
      { label: '*船舶登记号', value: 'officialNumber' },
      { label: '*其他卫通电话', value: 'otherSatNo' },
      { label: '*SSAS号码', value: 'ssasNo' },
      { label: '*VSAT电话', value: 'vsatNo' },
    ]
    this.shipParamsTextFields = [
      { label: '*铺龙骨时间', value: 'keelLaidDate', type: 'date' },
      { label: '*下水日期', value: 'launchedDate', type: 'date' },
      { label: '*交船日期', value: 'deliveredDate', type: 'date' },
      { label: '*航区装载限制', value: 'areaLoadingRestrictions' },
      { label: '*造船厂', value: 'builderCompany' },
      { label: '*船壳编号', value: 'hullNo' },
      { label: '*船龄', value: 'shipAge' },
    ]
    //     areaLoadingRestrictions	航区装载限制	string
    // breadth	型宽 m	integer
    // builderCompany	造船厂	string
    // deadWeight	满载吨位 t	integer
    // deliveredDate	交船日期	string
    // depth	型深 m	integer
    // designSpeed	最高航速 kn	integer
    // dwct	净载重吨位 t: Dead Weight Cargo Tonnage	integer
    // economicSpeed	营运航速 kn	integer
    // freeBoard	干舷 m	integer
    // fuelOilConsumption	燃油日消耗 t	integer
    // gdwt	总载重吨位 t: Gross Dead Weight Tonnage	integer
    // grossTonnage	总吨 t	integer
    // gzeroM		integer
    // hullNo	船壳编号	string
    // id	物理主键	string
    // keelLaidDate	建造日期	string
    // launchedDate	下水日期	string
    // lbp	两柱间长 m	integer
    // lightDraftFore	空船吃水-艏 m	integer
    // lightDraftMidship	空船吃水-舯 m	integer
    // lightDraftStern	空船吃水-艉 m	integer
    // lightShipWeight	空船吨位 t	integer
    // loa	总长 m	integer
    // loadedDraftFore	满载吃水-艏 m	integer
    // loadedDraftMidship	满载吃水-舯 m	integer
    // loadedDraftStern	满载吃水-艉 m	integer
    // netTonnage	净吨 t	integer
    // reeferContainerSocket	冷箱插座数量	integer
    // shipAge	船龄	string
    // shipCode	船舶编号	string
    // teu	TEU数量	integer
    // tpc	厘米吃水吨数	integer
    this.shipParamsIntgerTextFields = [
      { label: '*总长 m', value: 'loa' },
      { label: '*两柱间长 m', value: 'lbp' },
      { label: '*型宽', value: 'breadth' },
      { label: '*型深', value: 'depth' },
      { label: '*净空高度', value: 'airDraft' },
      { label: '*总载重量t', value: 'gdwt' },
      { label: '*满载排水量t', value: 'deadWeight' },
      { label: '*净载重吨位', value: 'dwct' },
      { label: '*空船重量 t', value: 'lightShipWeight' },
      { label: '*总吨', value: 'grossTonnage' },
      { label: '*净吨', value: 'netTonnage' },
      { label: '*压载水总量M³', value: 'totalBallast' },
      { label: '空船吃水-艏 m', value: 'lightDraftFore', canEmpty: true },
      { label: '空船吃水-舯 m', value: 'lightDraftMidship', canEmpty: true },
      { label: '空船吃水-艉 m', value: 'lightDraftStern', canEmpty: true },
      { label: '满载吃水-艏 m', value: 'loadedDraftFore', canEmpty: true },
      { label: '*满载吃水-舯 m', value: 'loadedDraftMidship' },
      { label: '满载吃水-艉 m', value: 'loadedDraftStern', canEmpty: true },
      { label: '*干舷 m', value: 'freeBoard' },
      { label: '*最高航速', value: 'designSpeed' },
      { label: '*最高航速燃油日消耗t', value: 'maxFuelOilConsumption' },
      { label: '*营运航速', value: 'economicSpeed' },
      { label: '*营运航速燃油日消耗t', value: 'fuelOilConsumption' },
      { label: '*冷箱插座数量', value: 'reeferContainerSocket' },
      { label: '*TEU数量', value: 'teu' },
      { label: '*TPC', value: 'tpc' },
      { label: '*临界稳性', value: 'gZeroM' },
      { label: '*副机最大负荷NO1/2/3/4', value: 'auxiliaryMaxLoad' },
    ]
    this.船型选项 = ['集装箱', '散货船']
    this.系列船舶选项 = ['集装箱', '散货船']
    this.船旗国选项 = ['香港', '巴拿马', '中国']
    this.船级社选项 = ['HK', 'DNV', 'CCS', 'LR', 'BV', 'ABS']
    this.付款公司选项 = ['3000', '3800', '8903', '3402']
    this.船舶状态选项 = [
      { text: '运营', value: 0 },
      { text: '坞修', value: 1 },
      { text: '停航', value: 2 },
      { text: '退役', value: 3 },
      { text: '卖船', value: 4 },
    ]
    this.是否二手船选项 = [
      { text: '否', value: 0 },
      { text: '是', value: 1 },
    ]
    this.是否支付伙食费 = [
      { text: '否', value: 0 },
      { text: '是', value: 1 },
    ]
    this.是否自引船舶选项 = [
      { text: '否', value: 0 },
      { text: '是', value: 1 },
    ]
    this.船舶主管 = [
      { text: '主管姓名', value: 'managerName' },
      { text: '主管部门/岗位', value: 'currentDept' },
    ]
    this.坞修信息字段 = [
      { text: '上次坞修时间', value: 'lastDockTime', type: 'date' },
      { text: '上次尾轴抽检时间', value: 'lastTailShaftTime', type: 'date' },
      { text: '预计修理时间', value: 'nextRepairTime', type: 'date' },
      { text: '预计修理类别', value: 'nextRepairType', type: 'dict' },
      { text: 'IOPP证书到期时间', value: 'ioppOverdueTime', type: 'date' },
      {
        text: '压载水处理装最迟安装时间',
        value: 'waterTreatmentTime',
        type: 'date',
      },
      { text: 'cctv', value: 'cctv', type: 'yn' },
      { text: '渔网收集器', value: 'fishingNetCollector', type: 'yn' },
      // { text: '备注', value: 'remark' },
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
    this.carbinSearchDicts = [
      {
        dicType: 'ship_cabin_type',
        label: '船舱类型',
        key: 'type',
      },
    ]
    this.carbinHeaders = [
      { text: '船舱类型', value: 'type' },
      { text: '船舱名称', value: 'name' },
      { text: '船舱容积', value: 'volume' },
      { text: '船舱位置', value: 'address' },
      { text: '管理岗位', value: 'post' },
      { text: '备注', value: 'remark' },
    ]
    this.salaryHeaders = [
      { text: '岗位', value: 'post' },
      { text: '工资标准', value: 'standard' },
      { text: '浮动范围', value: 'floatRange' },
    ]
  },
  data() {
    return {
      formData: {
        shipBase: {
          chShipName: '',
        },
        shipImo: {},
        shipManage: {},
        shipParams: {},
      },
      selected: false,
      dialog: false,
      isUpdate: false,
      shipDirector: [],
      initialData: {},
      rules: {
        // required: (v) => !!v || v === 0 || '必填项不能为空',
        required2: (v) => !!v || v === 0 || '必填项不能为空',
        yyyymmdd: (v) =>
          /^\d{4}-\d{2}-\d{2}$/.test(v) || '请输入yyyy-mm-dd格式',
        email: (v) =>
          /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(v) ||
          '请输入正确的邮箱格式',
      },
      deptInfo: [],
      dockRepairEvaluationOutputDTO: {},
      equipments: [],
      ports: [],
      shipCarbin: [],
      shipSalary: [],
      supplyPriceModifyList: [],
      selectedSup: false,
      initialDataSup: {},
      dialogSup: false,
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    船管公司() {
      return this.deptInfo.filter((i) => i.deptType === '0')
    },
    船员公司() {
      return this.deptInfo.filter((i) => i.deptType === '1')
    },
  },

  methods: {
    async save(goBack) {
      if (
        !this.$refs.form.validate() ||
        !this.$refs.form1.validate() ||
        !this.$refs.form2.validate()
      ) {
        return
      }
      const 基础信息链接 = this.isEdit
        ? '/business/common/ship/base/update'
        : '/business/common/ship/base/save'
      const 船舶参数链接 = this.isEdit
        ? '/business/common/ship/params/update'
        : '/business/common/ship/params/save'
      const 通导信息链接 = this.isEdit
        ? '/business/common/ship/imo/update'
        : '/business/common/ship/imo/save'
      const 管理信息链接 = this.isEdit
        ? '/business/common/ship/management/update'
        : '/business/common/ship/management/save'
      let shipDataReqs = []
      shipDataReqs.push(this.postAsync(基础信息链接, this.formData.shipBase))
      shipDataReqs.push(
        this.postAsync(通导信息链接, {
          shipCode: this.formData.shipBase.shipCode,
          ...this.formData.shipImo,
        }),
      )
      shipDataReqs.push(
        this.postAsync(船舶参数链接, {
          shipCode: this.formData.shipBase.shipCode,
          ...this.formData.shipParams,
        }),
      )
      shipDataReqs.push(
        this.postAsync(管理信息链接, {
          shipCode: this.formData.shipBase.shipCode,
          ...this.formData.shipManage,
        }),
      )
      const results = await Promise.all(shipDataReqs)
      if (results.every((item) => !item.errorRaw)) {
        goBack()
      }
    },
    async loadShipIfo() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync('/business/common/ship/detailInfo', {
        code: this.$route.params.id,
      })
      this.formData.shipBase = data.shipBase || {}
      this.formData.shipImo = data.shipImo || {}
      this.formData.shipManage = data.shipManage || {}
      this.formData.shipParams = data.shipParams || {}
      this.loadDockRepairEvaluationOutputDTO()
      this.loadEquipmentInfo()
    },
    async loadShipDirector() {
      if (this.$route.params.id !== `new`) {
        const { errorRaw, data } = await this.getAsync(
          `/business/common/ship/managementOwner/owners`,
          {
            shipCode: this.$route.params.id,
          },
        )
        if (errorRaw) {
          return
        }
        console.log(data)
        // this.shipDirector = data
        this.shipDirector = data.filter(
          (i) =>
            i.currentDept != null &&
            (i.currentDept.includes('船员') ||
              i.currentDept.includes('机务') ||
              i.currentDept.includes('ISM') ||
              i.currentDept.includes('通导') ||
              i.currentDept.includes('海务')),
        )
      }
    },
    async loadDeptInfo() {
      const { data } = await this.getAsync('/system/dept/getDeptTreeList')
      // 遍历部门树结构，找到deptType为0的部门
      const deptInfo = []
      const findDept = (data) => {
        data.forEach((item) => {
          if (item.deptType === '0') {
            deptInfo.push({
              value: item.id,
              text: item.name,
              deptType: item.deptType,
            })
          }
          if (item.children) {
            findDept(item.children)
          }
        })
      }
      findDept(data)
      this.deptInfo = deptInfo
      // this.deptInfo = data[0].children.map((i) => ({
      //   value: i.id,
      //   text: i.name,
      //   deptType: i.deptType,
      // }))
    },
    async loadDockRepairEvaluationOutputDTO() {
      if (!this.isEdit) return
      const url = '/dockRepairApply/getEvaluationDetailForPlanByShipCode'
      const { data } = await this.getAsync(url, {
        shipCode: this.formData.shipBase.shipCode,
      })
      this.dockRepairEvaluationOutputDTO = data
    },
    async loadPortList() {
      const { data } = await this.getAsync('/business/shipAffairs/Port/list', {
        size: 1000,
        current: 1,
      })
      this.ports = data.records.map((i) => i.portEn)
    },
    async loadEquipmentInfo() {
      if (!this.isEdit) return
      const url =
        '/business/shipAffairs/equipmentInformation/getMainParamsByShipCode'
      const { data } = await this.getAsync(url, {
        shipCode: this.formData.shipBase.shipCode,
      })
      this.equipments = data
    },
    addShipdirectoer() {
      this.dialog = true
    },
    // async editElectronicchart() {
    //   this.isUpdate = true
    //   this.dialog = true
    //   this.initialData = {
    //     ...this.selected,
    //     shipCode: this.formData.shipBase.shipCode,
    //   }
    // },
    async success() {
      this.$dialog.message.success('添加成功')
      await this.loadShipDirector()
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/common/ship/managementOwner/delete`,
        {
          managerId: this.selected.managerId,
          shipCode: this.formData.shipBase.shipCode,
        },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selected = false
      await this.loadShipDirector()
    },
    async loadShipCarbin() {
      if (!this.isEdit) return
      const { data } = await this.getAsync('/business/shipAffairs/Cabin/list', {
        shipCode: this.$route.params.id,
        size: 1000,
        current: 1,
      })
      this.shipCarbin = data.records
    },
    async loadShipSalary() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/crew/salaryStandard/standardByShip`,
        {
          shipCode: this.$route.params.id,
        },
      )
      this.shipSalary = data?.details || []
    },
    createSup() {
      // this.selectedSup.isNew = true
      console.log(this.supplyPriceModifyList)
      this.initialDataSup = { isNew: true }
      this.dialogSup = true
    },
    updateSup() {
      this.selectedSup.isNew = false
      this.initialDataSup = this.selectedSup
      this.dialogSup = true
    },
    delSup() {
      this.supplyPriceModifyList = this.supplyPriceModifyList.filter(
        (s) => !(s.vid === this.selectedSup.vid),
      )
      this.selectedSup = false
    },
    successSup(newSup) {
      console.log(newSup)
      console.log(this.supplyPriceModifyList)
      if (
        this.supplyPriceModifyList.some(
          (s) =>
            s.supplierId === newSup.supplierId &&
            s.subjectId === newSup.subjectId &&
            newSup.operationType !== 2,
        )
      ) {
        this.$dialog.message.error('供应商重复')
        return
      }
      if (newSup.id !== undefined) {
        const index = this.supplyPriceModifyList.findIndex(
          (item) => item.id === newSup.id,
        )

        this.$set(this.supplyPriceModifyList, index, newSup)
      } else {
        this.supplyPriceModifyList.push(newSup)
      }
      // this.supplyPriceModifyList.push(newSup)
    },
    async saveSup() {
      const url = '/business/shipAffairs/purchasePrice/bulkSupplierSaveOrUpdate'
      console.log(this.supplyPriceModifyList)
      console.log(this.formData.supplyPriceModifyList)
      const delList = this.formData.supplyPriceModifyList
        .filter((i) => !this.supplyPriceModifyList.includes(i))
        .map((i) => {
          return { id: i.id, operationType: 3 }
        })
      const bulkSupplierModifyDTOS = [...this.supplyPriceModifyList, ...delList]
      bulkSupplierModifyDTOS.forEach((item) => {
        item.shipCode = this.formData.shipBase.shipCode
      })
      if (bulkSupplierModifyDTOS.length === 0) {
        this.$dialog.message.warning('请添加大宗供应商')
        return false
      }
      const { errorRaw } = await this.postAsync(url, bulkSupplierModifyDTOS)
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
        return
      }
      this.$dialog.message.success('保存成功')
      await this.loadSup()
    },
    async loadSup() {
      // if (this.$route.params.id !== `new`) {
      //   const url = '/business/shipAffairs/purchasePrice/bulkSupplierByShipCode'
      //   const { data } = await this.getAsync(url, {
      //     shipCode: this.formData.shipBase.shipCode,
      //   })
      //   this.supplyPriceModifyList = data
      // }
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        `/business/shipAffairs/purchasePrice/bulkSupplierByShipCode`,
        {
          shipCode: this.$route.params.id,
        },
      )
      // this.formData.supplyPriceModifyList = data
      // this.supplyPriceModifyList = data
      this.supplyPriceModifyList = data.map((s) => {
        return { ...s, vid: s.id, operationType: 0 }
      })
      this.formData.supplyPriceModifyList = this.supplyPriceModifyList
    },
  },
  mounted() {
    this.loadShipIfo()
    this.loadShipDirector()
    this.loadDeptInfo()
    this.loadPortList()
    this.loadShipCarbin()
    this.loadShipSalary()
    this.loadSup()
  },
}
</script>

<style></style>
