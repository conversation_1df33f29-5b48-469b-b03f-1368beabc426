<template>
  <div>
    <v-text-field
      v-model="selectedText"
      :label="label"
      :disabled="disabled"
      :rules="rules"
      :clearable="clearable"
      @click="dialog = true"
      readonly
      outlined
      dense
      append-icon="mdi-chevron-down"
      @click:clear="clearSelection"
    ></v-text-field>

    <v-dialog v-model="dialog" max-width="800px">
      <v-card>
        <v-card-title>
          请选择
          <v-spacer></v-spacer>
          <v-text-field
            v-model="searchObj.deptName"
            label="岗位名称"
            dense
            outlined
            clearable
            hide-details
            class="mt-0 pt-0"
            style="max-width: 300px"
          ></v-text-field>
        </v-card-title>

        <v-card-text>
          <v-data-table
            :headers="headers"
            :items="items"
            :loading="loading"
            :search="searchObj.deptName"
            item-key="deptId"
            :single-select="true"
            :show-select="true"
            v-model="selected"
            disable-pagination
            hide-default-footer
            class="elevation-1"
            @item-selected="onItemSelected"
          ></v-data-table>

          <div class="d-flex justify-center mt-2">
            <v-pagination
              v-model="page"
              :length="pageCount"
              :total-visible="7"
            ></v-pagination>
          </div>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="dialog = false">取消</v-btn>
          <v-btn
            color="primary"
            text
            @click="confirmSelection"
            :disabled="!selected.length"
          >
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: 'custom-dept-select',
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    label: {
      type: String,
      default: '管理人岗位/部门',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    rules: {
      type: Array,
      default: () => [],
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      items: [],
      selected: [],
      selectedText: '',
      headers: [{ text: '岗位名称', value: 'deptName', sortable: false }],
      searchObj: {
        deptName: '',
      },
      page: 1,
      itemsPerPage: 10,
      allItems: [],
    }
  },
  computed: {
    paginatedItems() {
      const start = (this.page - 1) * this.itemsPerPage
      const end = start + this.itemsPerPage
      return this.allItems.slice(start, end)
    },
    pageCount() {
      return Math.ceil(this.allItems.length / this.itemsPerPage)
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (!val) {
          this.selected = []
          this.selectedText = ''
        }
      },
    },
    'searchObj.deptName': {
      handler() {
        this.loadData()
      },
    },
    dialog(val) {
      if (val) {
        this.loadData()
      }
    },
    page() {
      this.updateItems()
    },
    allItems() {
      this.updateItems()
    },
  },
  methods: {
    updateItems() {
      const start = (this.page - 1) * this.itemsPerPage
      const end = start + this.itemsPerPage
      this.items = this.allItems.slice(start, end)
    },
    async loadData() {
      this.loading = true
      try {
        const { data } = await this.getAsync(
          '/business/common/ship/managementOwner/positions',
          {
            deptName: this.searchObj.deptName,
          },
        )
        console.log('加载数据响应:', data)
        if (data) {
          this.allItems = data
          this.page = 1
        }
      } catch (error) {
        console.error('加载数据错误:', error)
      } finally {
        this.loading = false
      }
    },
    onItemSelected({ item, value }) {
      // 确保只有一个选中项
      if (value) {
        this.selected = [item]
      } else if (this.selected.includes(item)) {
        this.selected = []
      }
    },
    confirmSelection() {
      if (this.selected.length) {
        const selectedItem = this.selected[0]
        this.selectedText = selectedItem.deptName
        this.$emit('input', selectedItem.deptId)
        this.$emit('change', selectedItem)
        this.dialog = false
      }
    },
    clearSelection() {
      this.selected = []
      this.selectedText = ''
      this.$emit('input', '')
      this.$emit('change', null)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
