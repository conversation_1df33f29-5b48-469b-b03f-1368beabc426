<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        管理人员
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable-pay
          ref="table"
          table-name=""
          v-model="selected"
          outlined
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          :fuzzy-label="fuzzyLabel"
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.nickName"
                label="用户名"
                outlined
                dense
                clearable
              ></v-text-field>
            </v-col>
          </template>
          <template #btns></template>
        </v-table-searchable-pay>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import vTableSearchablePay from '@/components/v-table-searchablePay.vue'
export default {
  components: { vTableSearchablePay },
  name: 'manager-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/system/user/page'
    this.headers = [
      { text: '用户名', value: 'nickName' },
      { text: '部门/岗位', value: 'deptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    users: Array,
    shipCode: String,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      searchObj: { userType: 1 },
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    users: {
      handler(val) {
        this.selected = val.map((i) => {
          return { ...i, id: i.managerId }
        })
      },
      deep: true,
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async confirm() {
      const { errorRaw } = await this.postAsync(
        '/business/common/ship/managementOwner/updateBatch',
        {
          dataList: this.selected.map((i) => {
            return { managerId: i.id, shipCode: this.shipCode }
          }),
        },
      )
      if (errorRaw) return
      this.$dialog.message.success('操作成功')
      this.$emit('change', false)
      this.$emit('success')
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
