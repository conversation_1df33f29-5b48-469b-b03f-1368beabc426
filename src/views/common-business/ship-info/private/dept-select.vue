<template>
  <div>
    <v-text-field
      v-model="selectedText"
      :label="label"
      :disabled="disabled"
      :rules="rules"
      :clearable="clearable"
      @click="dialog = true"
      readonly
      outlined
      dense
      append-icon="mdi-chevron-down"
      @click:clear="clearSelection"
    ></v-text-field>

    <v-dialog v-model="dialog" max-width="800px">
      <v-card>
        <v-card-title>
          请选择
          <v-spacer></v-spacer>
          <v-text-field
            v-model="searchObj.deptName"
            label="岗位名称"
            dense
            outlined
            clearable
            hide-details
            class="mt-0 pt-0"
            style="max-width: 300px"
          ></v-text-field>
        </v-card-title>

        <v-card-text>
          <v-data-table
            :headers="headers"
            :items="items"
            :loading="loading"
            :search="searchObj.deptName"
            item-key="deptId"
            dense
            :items-per-page="10"
            :footer-props="{
              'items-per-page-options': [10, 20, 50],
              'items-per-page-text': '每页显示',
            }"
            @click:row="handleRowClick"
          >
            <template #item="{ item, isSelected }">
              <tr
                :class="{
                  'selected-row': isSelected || selected.includes(item),
                }"
                @click="handleRowClick(item)"
              >
                <td>{{ item.deptName }}</td>
              </tr>
            </template>
          </v-data-table>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="dialog = false">取消</v-btn>
          <v-btn
            color="primary"
            text
            @click="confirmSelection"
            :disabled="!selected.length"
          >
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: 'dept-select',
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    label: {
      type: String,
      default: '管理人岗位/部门',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    rules: {
      type: Array,
      default: () => [],
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      items: [],
      selected: [],
      selectedText: '',
      headers: [{ text: '岗位名称', value: 'deptName', sortable: false }],
      searchObj: {
        deptName: '',
      },
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (!val) {
          this.selected = []
          this.selectedText = ''
        }
      },
    },
    'searchObj.deptName': {
      handler() {
        this.loadData()
      },
    },
    dialog(val) {
      if (val) {
        this.loadData()
      }
    },
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const { data } = await this.getAsync(
          '/business/common/ship/managementOwner/positions',
          {
            deptName: this.searchObj.deptName,
          },
        )
        console.log('加载数据响应:', data)
        if (data) {
          this.items = data
        }
      } catch (error) {
        console.error('加载数据错误:', error)
      } finally {
        this.loading = false
      }
    },
    handleRowClick(item) {
      this.selected = [item]
    },
    confirmSelection() {
      if (this.selected.length) {
        const selectedItem = this.selected[0]
        this.selectedText = selectedItem.deptName
        this.$emit('input', selectedItem.deptId)
        this.$emit('change', selectedItem)
        this.dialog = false
      }
    },
    clearSelection() {
      this.selected = []
      this.selectedText = ''
      this.$emit('input', '')
      this.$emit('change', null)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
.selected-row {
  background-color: var(--v-primary-lighten4) !important;
}
</style>
