<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ type === 2 ? '按管理人编辑' : '按船舶编辑' }}
        <v-spacer></v-spacer>
        <v-btn
          :disabled="!shipCode"
          v-show="type != 2"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="dialog = true"
          v-permission="['船舶分管:按船舶编辑']"
        >
          <v-icon left>mdi-pencil</v-icon>
          编辑
        </v-btn>
        <v-btn
          :disabled="!selectedDirector"
          v-show="type != 2"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit2"
          v-permission="['船舶分管:按船舶删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text v-if="type === 1">
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row class="py-0">
              <v-col class="py-0" cols="12" md="2">
                <v-ship-select
                  v-model="shipCode"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-ship-select>
              </v-col>
            </v-row>
            <v-row class="py-0">
              <!-- <v-spacer></v-spacer>
              <v-col class="py-0" cols="2">
                <v-btn
                  :disabled="!shipCode"
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="dialog = true"
                  v-permission="['船舶分管:按船舶编辑']"
                >
                  <v-icon left>mdi-pencil</v-icon>
                  编辑
                </v-btn>
                <v-btn
                  :disabled="!selectedDirector"
                  outlined
                  tile
                  color="error"
                  class="mx-1"
                  @click="delAudit2"
                  v-permission="['船舶分管:按船舶删除']"
                >
                  <v-icon left>mdi-delete-empty</v-icon>
                  删除
                </v-btn>
              </v-col> -->
              <v-col cols="12" class="py-0">
                <v-card-text>
                  <v-table-list
                    :headers="船舶主管"
                    :items="shipDirector"
                    v-model="selectedDirector"
                  ></v-table-list>
                </v-card-text>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-text v-else-if="type === 2">
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col class="py-0" cols="12" md="2">
                <v-handler
                  :use-current="false"
                  v-model="managerId"
                  label="管理人"
                ></v-handler>
              </v-col>
              <v-col cols="12">
                <v-ship-select multiple v-model="shipCodes"></v-ship-select>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['船舶分管:按管理人编辑']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      <manager-select
        @success="loadShipDirector"
        :users="shipDirector"
        v-model="dialog"
        :shipCode="shipCode"
      ></manager-select>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-handler
            :use-current="false"
            v-model="searchObj.managerId"
            label="管理人"
            clearable
          ></v-handler>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="管理人岗位/部门"
            outlined
            dense
            clearable
            @input="onInput1"
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="createByDir"
          v-permission="['船舶分管:按主管新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          按主管新增
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="createByShip"
          v-permission="['船舶分管:按船舶新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          按船舶新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delAudit"
          v-permission="['船舶分管:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import ManagerSelect from './private/manager-select.vue'
export default {
  components: { ManagerSelect },
  name: 'common-ship-director',
  created() {
    this.tableName = '船舶分管'
    this.reqUrl = '/business/common/ship/managementOwner/page'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    //     currentDept	所属部门名称	string
    // id	主键	string
    // managerId	管理人员user_id	string
    // managerName	管理人员姓名	string
    // managerType	管理人员类型（见数据字典 ship_manager_type）：1 机务主管，2 海务主管，3 通导主管，4 船员调配主管	integer
    // parentDept	上级部门名称	string
    // shipCode	船舶编号	string
    // shipName	船舶名	string
    this.headers = [
      { text: '船舶', value: 'shipName' },
      { text: '管理人员姓名', value: 'managerName' },
      { text: '管理人岗位/部门', value: 'currentDept' },
      { text: '生效时间', value: 'createTime' },
    ]
    this.船舶主管 = [
      { text: '主管姓名', value: 'managerName' },
      { text: '主管部门/岗位', value: 'currentDept' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      selected: false,
      selectedDirector: false,
      searchObj: { managerDept: '' },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      formData: {},
      shipCode: '',
      isEdit: false,
      loading: false,
      formShow: false,
      type: 0,
      shipDirector: [],
      dialog: false,
      shipCodes: [],
      managerId: '',
    }
  },

  watch: {
    shipCode(val) {
      if (val) {
        this.loadShipDirector(val)
      } else {
        this.shipDirector = []
      }
    },
    managerId(val) {
      if (val) {
        this.loadShips(val)
      }
    },
  },

  methods: {
    onInput1(val) {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.getValue1(val)
      }, 500)
    },
    getValue1(val) {
      this.searchObj.managerDept = val
    },
    createByDir() {
      this.type = 2
      this.formShow = true
      this.$refs.table.disabled = true
    },
    createByShip() {
      this.type = 1
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async delItem() {
      // const { errorRaw } = await this.deleteAsync(
      //   '/business/common/ship/managementOwner',
      //   { ids: this.selected },
      // )
      // if (errorRaw) {
      //   return
      // }
      // this.$refs.table.refresh()
    },

    async loadShipDirector(shipCode = this.shipCode) {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/ship/managementOwner/owners`,
        { shipCode },
      )
      if (errorRaw) {
        return
      }
      this.shipDirector = data
    },
    async loadShips(managerId) {
      const { errorRaw, data } = await this.getAsync(
        `/business/common/ship/managementOwner/ships`,
        { managerId },
      )
      if (errorRaw) {
        return
      }
      this.shipCodes = data.map((i) => i.shipCode)
    },
    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl =
        '/business/common/ship/managementOwner/updateBatchByManager'
      const { errorRaw } = await this.postAsync(reqUrl, {
        userId: this.managerId,
        shipCodes: this.shipCodes,
      })
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {}
      this.shipCode = ''
      this.managerId = ''
      this.shipCodes = []
      this.formShow = false
      this.$refs.table.disabled = false
      this.type = 0
      this.isEdit = false
      this.shipDirector = []
      this.selectedDirector = false
      this.$refs.table.loadTableData()
    },
    async delAudit() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/common/ship/managementOwner/delete`,
        {
          managerId: this.selected.managerId,
          shipCode: this.selected.shipCode,
        },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async delAudit2() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/common/ship/managementOwner/delete`,
        {
          managerId: this.selectedDirector.managerId,
          shipCode: this.shipCode,
        },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功')
      this.selectedDirector = false
      this.loadShipDirector()
    },
  },

  mounted() {},
}
</script>

<style></style>
