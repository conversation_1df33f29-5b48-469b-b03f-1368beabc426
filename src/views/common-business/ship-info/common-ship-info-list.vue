<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      item-key="shipCode"
      use-ship
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-dict-select
            dict-type="flag_state"
            label="船旗国"
            v-model="searchObj.flagState"
            dense
            outlined
            clearable
          ></v-dict-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-dict-select
            dict-type="classification_society"
            label="船级社"
            v-model="searchObj.classification"
            dense
            outlined
            clearable
          ></v-dict-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="管理公司"
            outlined
            clearable
            dense
            :items="itemsCompanies"
            :item-text="'text'"
            :item-value="'text'"
            v-model="searchObj.manageCompany"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="船员公司"
            outlined
            clearable
            dense
            :items="itemsCompanies"
            v-model="searchObj.crewCompany"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="openEditDialog"
          v-permission="['船舶信息:期初入库']"
        >
          <v-icon left>mdi-pencil</v-icon>
          期初入库权限调整
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          href="/api/business/ownerAffairs/shipStatus/excelOfShipMessage"
          v-permission="['船舶信息:船东信息统计']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          船东信息统计
        </v-btn>
        <v-btn
          :to="{ name: 'common-ship-info-detail', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['船舶信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delShip"
          v-permission="['船舶信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipStatus`]="{ item }">
        {{ ['运营', '坞修', '停航', '退役', '卖船'][item.shipStatus] }}
      </template>
      <template v-slot:[`item.initInput`]="{ item }">
        <v-chip
          small
          :color="item.initInput ? 'success' : 'error'"
          :text-color="item.initInput ? 'white' : 'white'"
        >
          {{ item.initInput ? '允许' : '禁止' }}
        </v-chip>
      </template>
    </v-table-searchable>

    <v-dialog v-model="editDialog" max-width="500px">
      <v-card>
        <v-card-title>期初入库权限调整</v-card-title>
        <v-card-text>
          <v-switch
            v-model="selected.initInput"
            label="是否允许期初入库"
          ></v-switch>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" text @click="editDialog = false">取消</v-btn>
          <v-btn color="primary" text @click="saveChanges">确认</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'common-ship-info-list',
  mixins: [dictHelper],
  created() {
    this.tableName = '船舶信息'
    this.reqUrl = '/business/common/ship/detailInfo/page'
    this.headers = [
      { text: '中文船名', value: 'chShipName' },
      { text: '英文船名', value: 'enShipName' },
      { text: '船舶编号', value: 'shipCode' },
      { text: '船级社', value: 'classification' },
      { text: '船旗国', value: 'flagState' },
      { text: '建造日期', value: 'keelLaidDate' },
      { text: '*SAP CODE', value: 'sapCode' },
      { text: '管理公司', value: 'manageCompany' },
      { text: '船员公司', value: 'crewCompany' },
      { text: '船龄', value: 'shipAge' },
      { text: '船舶状态', value: 'shipStatus' },
      { text: '船型', value: 'shipType' },
      {
        text: '期初入库权限',
        value: 'initInput',
        align: 'center',
        width: '120',
      },
    ]
    this.船旗国选项 = ['香港', '巴拿马', '中国']
    this.船级社选项 = ['HK', 'DNV', 'CCS', 'LR', 'BV', 'ABS']
    this.pushParams = { name: 'common-ship-info-detail' }
  },

  data() {
    return {
      selected: false,
      editDialog: false,
      itemsCompanies: [],
      searchObj: {
        flagState: '',
        classification: '',
      },
    }
  },

  methods: {
    async delShip() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/common/ship/detailInfo/delete/${this.selected.shipCode}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
    async getCrewCompany() {
      const dataInfo = await this.getDictByType('ship_crew_conpany')
      this.itemsCompanies = dataInfo.map((ele) => {
        return { text: ele.dictLabel, value: ele.dictValue }
      })
      console.log('itemsCompanies', this.itemsCompanies)
    },
    openEditDialog() {
      this.editDialog = true
    },
    async saveChanges() {
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/updateInitStock',
        {
          id: this.selected.id,
          code: this.selected.initInput,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('期初入库权限调整成功')
        this.editDialog = false
        await this.$refs.table.loadTableData()
      }
    },
  },

  async mounted() {
    await this.getCrewCompany()
  },
}
</script>

<style></style>
