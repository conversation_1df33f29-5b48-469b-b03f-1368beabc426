<template>
  <v-container fluid>
    <v-table-searchable
      class="mb-5"
      outlined
      ref="table2"
      table-name="已分配滑油"
      :headers="headers2"
      :req-url="reqUrl2"
      :fix-header="false"
      :search-remain="searchObj2"
      item-key="greaseId"
      :search-dicts="[searchDicts[0]]"
      fuzzy-label="英文名/滑油号/厂家"
      v-model="selected2"
    >
      <template #searchflieds>
        <v-col md="3" sm="6" cols="12">
          <v-ship-select :clearable="false" v-model="shipCode"></v-ship-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected2"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['滑油分配:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipInfo`]="{ item }">
        {{ item.shipInfo.chShipName }}
      </template>
    </v-table-searchable>
    <v-table-searchable
      v-if="!isShip"
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :single-select="false"
      :search-remain="searchObj"
      fuzzy-label="英文名/滑油号/厂家"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          :loading="loading"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="saveDis"
          v-permission="['滑油分配:保存当前分配']"
        >
          <v-icon left>mdi-content-save</v-icon>
          保存当前分配
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'slide-oil-distribution',
  created() {
    this.tableName = '滑油分配'
    this.reqUrl = '/business/shipAffairs/GreaseInfo/list'
    this.reqUrl2 = '/business/shipAffairs/greaseDistribution/pageByParams'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.searchDicts = [
      {
        dicType: 'ship_grease_info_type',
        label: '滑油类型',
        key: 'type',
      },
      {
        dicType: 'grease_brand',
        label: '滑油品牌',
        key: 'factory',
      },
    ]
    this.headers = [
      { text: '英文名称', value: 'nameEn' },
      { text: '滑油类型', value: 'type' },
      { text: '滑油号', value: 'code' },
      { text: '厂家', value: 'factory' },
      { text: '供应商', value: 'supplierName' },
      { text: '规格', value: 'specs' },
      { text: '单位', value: 'unit' },
    ]
    this.headers2 = [
      { text: '英文名称', value: 'nameEn' },
      { text: '滑油类型', value: 'type' },
      { text: '滑油号', value: 'code' },
      { text: '厂家', value: 'factory' },
      { text: '供应商', value: 'supplierName' },
      { text: '规格', value: 'specs' },
      { text: '单位', value: 'unit' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    // TODO:默认选择一个船舶
    return {
      selected: [],
      originSelected: [],
      shipCode: 'STTK',
      searchObj: { shipCode: 'STTK' },
      searchObj2: { shipCode: 'STTK' },
      loading: false,
      selected2: false,
    }
  },

  methods: {
    async getShipDis(shipCode) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/greaseDistribution/pageByParams',
        { shipCode, current: 1, size: 1000 },
      )
      this.selected = data.records.map((i) => ({ id: i.greaseId }))
      this.searchObj2.shipCode = shipCode
      this.originSelected = data.records.map((i) => ({
        vid: i.id,
        id: i.greaseId,
      }))
    },

    async saveDis() {
      this.loading = true
      const delList = this.originSelected
        .filter((i) => !this.selected.some((j) => j.id === i.id))
        .map((i) => i.vid)
      const addList = this.selected
        .filter((i) => !this.originSelected.some((j) => j.id === i.id))
        .map((i) => i.id)
      if (delList.length)
        await this.postAsync(
          '/business/shipAffairs/greaseDistribution/delete',
          delList,
        )
      await this.postAsync(
        '/business/shipAffairs/greaseDistribution/saveOrUpdate',
        [
          {
            shipCode: this.shipCode,
            greaseIds: addList,
          },
        ],
      )
      this.$dialog.message.success('保存成功')
      await this.$refs.table2.loadTableData()
      this.getShipDis(this.shipCode)
      this.loading = false
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const delList = []
      delList.push(this.selected2.id)
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/greaseDistribution/delete',
        delList,
      )
      if (!errorRaw) {
        this.selected2 = false
        this.$refs.table.loadTableData()
        this.$refs.table2.loadTableData()
      }
    },
  },
  watch: {
    shipCode(val) {
      this.getShipDis(val)
    },
  },
  mounted() {
    this.getShipDis(this.shipCode)
  },
}
</script>

<style></style>
