<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}滑油基础信息
        <v-spacer></v-spacer>
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          v-permission="['滑油基础信息:编辑']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '修改' : '新增' }}
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col class="py-1" cols="12" md="3">
                <v-text-field
                  :disabled="isEdit"
                  v-model="form.code"
                  label="滑油号"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col class="py-1" cols="12" md="3">
                <v-text-field
                  :disabled="isEdit"
                  v-model="form.nameEn"
                  label="英文名称"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col class="py-1" cols="12" md="3">
                <v-text-field
                  :disabled="isEdit"
                  v-model="form.nameCn"
                  label="中文名称"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col class="py-1" cols="12" md="3">
                <v-text-field
                  v-model="form.sapCode"
                  label="SAP编码"
                  disabled
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col class="py-1" cols="12" md="3">
                <v-dict-select
                  :disabled="isEdit"
                  v-model="form.type"
                  label="滑油类型"
                  dictType="ship_grease_info_type"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-dict-select>
              </v-col>
              <v-col class="py-1" cols="12" md="3">
                <v-dict-select
                  v-model="form.unit"
                  label="滑油单位"
                  dictType="grease_unit"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-dict-select>
              </v-col>

              <v-col class="py-1" cols="12" md="3">
                <v-text-field
                  v-model="form.specs"
                  label="规格"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>

              <!-- <v-col class="py-1" cols="12" md="3">
                <v-dict-select
                  dict-type="grease_brand"
                  label="厂家"
                  v-model="form.factory"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-dict-select>
              </v-col> -->

              <v-col class="py-1" cols="12" md="3">
                <v-select
                  dense
                  v-model="form.isSap"
                  label="是否在sap存货管理"
                  outlined
                  :items="yn"
                  :rules="[rules.required]"
                  disabled
                ></v-select>
              </v-col>

              <v-col cols="12" md="3" class="py-1">
                <v-select
                  label="厂家"
                  :items="factoryType"
                  v-model="form.factory"
                  outlined
                  dense
                  :rules="[rules.required]"
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-1">
                <v-select
                  label="供应商"
                  v-model="form.supplierCode"
                  :items="sapCodeValue"
                  outlined
                  dense
                  :rules="[rules.required]"
                  required
                  :disabled="!form.factory"
                ></v-select>
              </v-col>

              <v-col class="py-1">
                <v-text-field
                  v-model="form.remark"
                  label="备注"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      :dense="true"
      @dbclick="editSlideOil"
      fuzzy-label="英文名/滑油号/厂家"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            label="状态"
            v-model="searchObj.stopUse"
            dense
            outlined
            :items="[
              { text: '停用', value: true },
              { text: '启用', value: false },
            ]"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-import-btn
          import-url="/business/shipAffairs/GreaseInfo/insertByExcel"
          @importSuccess="importSuccess"
          v-permission="['滑油基础信息:导入EXCEL']"
        ></v-import-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          href="/api/business/common/bizTemplate/download?bizCode=shipGreaseInfo"
        >
          <v-icon left>mdi-download</v-icon>
          下载导入模板
        </v-btn> -->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateStopUse"
          v-permission="['滑油基础信息:变更状态']"
        >
          <v-icon left>mdi-pencil</v-icon>
          变更状态
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="formShow = true"
          v-permission="['滑油基础信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delSlideoil"
          v-permission="['滑油基础信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.isSap`]="{ item }">
        {{ item.isSap ? '是' : '否' }}
      </template>
      <template v-slot:[`item.stopUse`]="{ item }">
        <span style="color: red" v-if="item.stopUse == 1">停用</span>
        <span v-if="item.stopUse == 0">启用</span>
      </template>
    </v-table-searchable>
    <v-dialog v-model="updateUseDialog" max-width="600">
      <template v-slot:default="updateUseDialog">
        <v-card style="height: 320px">
          <v-card-title>
            修改状态
            <v-spacer></v-spacer>
            <v-btn
              :loading="loading"
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveStopUse"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              保存
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="updateUseDialog.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-text-field
                label="滑油号"
                v-model="updateUseData.code"
                dense
                readonly
                required
                outlined
              ></v-text-field>
            </v-row>
            <v-row>
              <v-text-field
                label="中文名称"
                v-model="updateUseData.nameCn"
                dense
                readonly
                required
                outlined
              ></v-text-field>
            </v-row>
            <v-row>
              <v-select
                label="状态"
                v-model="updateUseData.stopUse"
                dense
                outlined
                :items="[
                  { text: '停用', value: true },
                  { text: '启用', value: false },
                ]"
              ></v-select>
            </v-row>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import dictHelper from '@/mixin/dictHelper'
export default {
  name: 'slide-oil-list',
  mixins: [dictHelper],
  created() {
    this.tableName = '滑油基础信息'
    this.reqUrl = '/business/shipAffairs/GreaseInfo/list'
    this.searchDicts = [
      {
        dicType: 'ship_grease_info_type',
        label: '滑油类型',
        key: 'type',
      },
      {
        dicType: 'grease_brand',
        label: '滑油品牌',
        key: 'factory',
      },
      // {
      //   dicType: 'grease_unit',
      //   label: '滑油单位',
      //   key: 'factory',
      // },
    ]
    this.headers = [
      { text: '滑油号', value: 'code' },
      { text: '中文名称', value: 'nameCn', hideDefault: true },
      { text: '英文名称', value: 'nameEn' },
      { text: 'SAP编码', value: 'sapCode' },
      { text: '滑油类型', value: 'type' },
      { text: '规格', value: 'specs' },
      { text: '厂家', value: 'factory' },
      { text: '供应商', value: 'supplierName' },
      { text: '单位', value: 'unit' },
      { text: '是否在sap存货管理', value: 'isSap' },
      { text: '状态', value: 'stopUse' },
      { text: '备注', value: 'remark' },
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
  },

  data() {
    return {
      selected: false,
      form: {
        type: '',
        nameEn: '',
        nameCn: '',
        code: '',
        specs: '',
        factory: '',
        isSap: undefined,
        remark: '',
      },
      valid: false,
      rules: {
        required: (v) => v !== '' || v === 0 || '必填项不能为空',
      },
      isEdit: false,
      loading: false,
      formShow: false,
      // 防止科目Id发生变化
      otherId: '',
      factoryType: [],
      factoryList: [],
      sapCodeValue: [],
      updateUseDialog: false,
      updateUseData: {},
      searchObj: {},
    }
  },

  watch: {
    formShow(val) {
      if (!val) {
        this.$refs.form.reset()
        this.isEdit = false
      } else {
        this.$refs.table.disabled = true
      }
    },
    'form.type'(val) {
      if (val === this.otherId) {
        this.form.isSap = false
      } else if (val) {
        this.form.isSap = true
      } else {
        this.form.isSap = undefined
      }
    },
    'form.factory': {
      handler(val) {
        this.sapCodeValue = this.factoryList.map((ele) => {
          if (ele.cssClass === val) {
            // { text: '是', value: 1 },
            //         { text: '否', value: 0 },
            // return ele.dictValue
            return { text: ele.dictLabel, value: ele.dictValue }
          }
        })
      },
    },
  },

  methods: {
    async delSlideoil() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/GreaseInfo/deleteById/${this.selected.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editSlideOil() {
      this.form = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      // console.log(this.form)
      if (!this.$refs.form.validate()) return
      if (
        this.form.supplierCode == null ||
        this.form.supplierCode == undefined
      ) {
        this.$dialog.message.error(`请选择供应商`)
        return
      }
      this.sapCodeValue.forEach((tag) => {
        if (tag != undefined) {
          if (tag.value === this.form.supplierCode) {
            this.form.supplierName = tag.text
          }
        }
      })

      const reqUrl = this.isEdit
        ? '/business/shipAffairs/GreaseInfo/update'
        : '/business/shipAffairs/GreaseInfo/insert'
      const { errorRaw } = await this.postAsync(reqUrl, { ...this.form }, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.form = {
        type: '',
        nameEn: '',
        nameCn: '',
        code: '',
        specs: '',
        factory: '',
        isSap: undefined,
        remark: '',
        supplierCode: undefined,
        supplierName: undefined,
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
      await this.$nextTick()
    },
    closeForm() {
      this.$refs.form.reset()
      this.form = {
        type: '',
        nameEn: '',
        nameCn: '',
        code: '',
        specs: '',
        factory: '',
        isSap: undefined,
        remark: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },

    async loadTypes() {
      const greaseTypes = await this.getDictByType('ship_grease_info_type')
      this.otherId = greaseTypes.find(
        (i) => i.dictLabel === '其他小品种油',
      ).dictValue
    },
    async getClassByType() {
      const { data, errorRaw } = await this.getAsync(
        '/system/dict-data/getClassByDictType',
        { dictType: 'grease_brand_new' },
      )
      if (errorRaw) {
        this.$dialog.message.error(`字典获取失败，部分功能受损`)
        return null
      }
      if (data.length === 0) {
        this.$dialog.message.error(`数据字典为空，部分功能受损`)
      }
      this.factoryType = data
    },
    updateStopUse() {
      this.updateUseData = {
        ...this.selected,
      }
      console.log(this.updateUseData)
      this.updateUseDialog = true
    },
    async saveStopUse() {
      const url = '/business/shipAffairs/GreaseInfo/updateStopUse'
      const { errorRaw } = await this.postAsync(url, {
        ...this.updateUseData,
      })
      this.loading = false
      if (!errorRaw) {
        this.updateUseDialog = false
        await this.$refs.table.loadTableData()
        await this.$nextTick()
      }
    },
  },

  async mounted() {
    this.getClassByType()
    this.factoryList = await this.getDictByType(`grease_brand_new`)
    this.loadTypes()
  },
}
</script>

<style></style>
