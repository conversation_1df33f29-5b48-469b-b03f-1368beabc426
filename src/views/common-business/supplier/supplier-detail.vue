<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['供应商列表:编辑']"
      :title="`供应商详情-${detail.name}`"
      :tooltip="detail.name"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template v-slot:custombtns>
        <v-btn
          tile
          color="error"
          small
          class="mx-1"
          @click="saveFinanceList"
          v-permission="['供应商列表:修改对账负责人']"
        >
          <v-icon left>mdi-check</v-icon>
          修改对账负责人
        </v-btn>
        <v-btn
          tile
          color="error"
          small
          class="mx-1"
          @click="saveCgList"
          v-permission="['供应商列表:修改采购负责人']"
        >
          <v-icon left>mdi-check</v-icon>
          修改采购负责人
        </v-btn>
      </template>
      <template #供应商基础信息>
        <v-container fluid>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  v-model="detail.sapCode"
                  label="SAP代码"
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  v-model="detail.name"
                  label="供应商名称"
                  dense
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  v-model="detail.nameEn"
                  label="英文名称"
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-combobox
                  v-model="detail.contact"
                  label="联系人"
                  multiple
                  outlined
                  small-chips
                  dense
                  hint="输入联系人后回车即保存"
                ></v-combobox>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-combobox
                  v-model="detail.telePhone"
                  label="手机号"
                  multiple
                  outlined
                  small-chips
                  dense
                  hint="输入手机号后回车即保存"
                ></v-combobox>
              </v-col> -->
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  outlined
                  v-model="detail.supplierMail"
                  label="邮箱"
                  dense
                  :rules="[rules.email]"
                ></v-text-field> -->
                <v-combobox
                  v-model="detail.supplierMail"
                  label="邮箱"
                  multiple
                  outlined
                  small-chips
                  dense
                  hint="输入邮箱后回车即保存"
                ></v-combobox>
              </v-col>
              <v-col cols="12" md="2">
                <v-dict-select
                  v-model="detail.businessType"
                  label="业务分类"
                  multiple
                  required
                  dict-type="cost_subject_type"
                  dense
                ></v-dict-select>
                <!-- <v-select
                  label="业务类型"
                  v-model="detail.businessType"
                  multiple
                  dense
                  outlined
                  :items="[
                    { text: '船员', value: '船员' },
                    { text: '备件', value: '备件' },
                    { text: '物料', value: '物料' },
                    { text: '滑油', value: '滑油' },
                    { text: '化学品', value: '化学品' },
                    { text: '缆绳', value: '缆绳' },
                    { text: '绑扎件', value: '绑扎件' },
                    { text: '油漆', value: '油漆' },
                    { text: '锚、锚链', value: '锚、锚链' },
                    { text: '消防救生检验', value: '消防救生检验' },
                    { text: '海图', value: '海图' },
                    { text: '通导', value: '通导' },
                    { text: '坞修', value: '坞修' },
                    { text: '航修', value: '航修' },
                    { text: '固定资产', value: '固定资产' },
                    { text: '年度协议', value: '年度协议' },
                    { text: '大宗采购', value: '大宗采购' },
                    { text: '大宗采购', value: '大宗采购' },
                  ]"
                ></v-select> -->
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="年度协议"
                  v-model="detail.annual"
                  dense
                  outlined
                  :items="[
                    { text: '是', value: true },
                    { text: '否', value: false },
                  ]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  label="坞修白名单"
                  v-model="detail.isDockWhite"
                  dense
                  outlined
                  :items="[
                    { text: '是', value: true },
                    { text: '否', value: false },
                  ]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="1">
                <v-switch
                  class="mt-1"
                  :label="detail.flagBlacklist ? '正常' : '禁用'"
                  v-model="detail.flagBlacklist"
                  dense
                ></v-switch>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-btn
                  outlined
                  tile
                  color="primary"
                  @click="getPwd"
                  :loading="loading"
                  v-permission="['供应商基础信息:查看密码']"
                >
                  <v-icon left>mdi-eye</v-icon>
                  查看密码
                </v-btn>
              </v-col> -->
              <!-- <v-col cols="12" md="2">
                质量平均:{{ detail.supplierAvgScore1 }}
              </v-col>
              <v-col cols="12" md="2">
                服务平均:{{ detail.supplierAvgScore2 }}
              </v-col> -->
              <v-col cols="12" md="2">评级平均分:{{ detail.avgScore }}</v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template #管理负责人>
        <v-row>
          <v-col cols="12" md="3">
            <v-combobox
              v-model="detail.checkMails"
              label="供应商对账邮箱"
              multiple
              outlined
              small-chips
              dense
              hint="输入邮箱后回车即保存"
            ></v-combobox>
          </v-col>
          <v-col cols="12" md="3">
            <v-combobox
              v-model="detail.invoiceMails"
              label="供应商发票邮箱"
              multiple
              outlined
              small-chips
              dense
              hint="输入邮箱后回车即保存"
            ></v-combobox>
          </v-col>
          <v-col cols="12" md="3">
            <v-btn
              tile
              color="error"
              class="mx-1"
              @click="saveMails"
              v-permission="['供应商详情:修改对账发票邮箱']"
            >
              <v-icon left>mdi-check</v-icon>
              修改对对账/发票邮箱
            </v-btn>
          </v-col>
        </v-row>
        <v-col cols="12" md="2">
          <v-autocomplete
            label="对账负责人"
            v-model="managerIds"
            dense
            outlined
            item-text="nickName"
            item-value="id"
            multiple
            :items="financeList"
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" md="2">
          <v-autocomplete
            label="采购负责人"
            v-model="managerIdsCg"
            dense
            outlined
            item-text="nickName"
            item-value="id"
            multiple
            :items="cgList"
          ></v-autocomplete>
        </v-col>
        <v-table-searchable
          outlined
          ref="table"
          table-name="历史记录"
          :headers="managerHeader"
          :req-url="reqUrl3"
          :fix-header="false"
          :dense="true"
          :search-remain="searchObj3"
        >
          <template v-slot:[`item.status`]="{ item }">
            <v-chip small color="warning" v-if="item.status == 0">失效</v-chip>
            <v-chip small color="success" v-if="item.status == 1">有效</v-chip>
          </template>
        </v-table-searchable>
      </template>
      <template #付款公司按钮>
        <!-- <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          :disabled="!payCom"
          @click="addSupplierBank"
          v-permission="['付款公司:新增银行信息']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增银行信息
        </v-btn>
        <v-btn
          outlined
          small
          tile
          color="success"
          :disabled="!bank"
          class="mx-1"
          @click="editSupplierBank"
          v-permission="['付款公司:编辑银行信息']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          编辑银行信息
        </v-btn>
        <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click="addPayCom"
          v-permission="['付款公司:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!payCom"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editSupplierP"
          v-permission="['付款公司:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!payCom"
          small
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delSupplierP"
          v-permission="['付款公司:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template #付款公司>
        <v-divider></v-divider>
        <v-data-table
          :headers="payComHeaders"
          :items="supplierPurchaserList"
          v-model="payComs"
          single-select
          disable-pagination
          hide-default-footer
          show-select
          dense
          class="use-divider"
          show-expand
          :expanded.sync="expanded"
        >
          <template v-slot:[`item.status`]="{ item }">
            {{ ['有效', '暂停整顿', '冻结', '黑名单'][item.status] }}
          </template>
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length">
              <v-list class="my-2" dense>
                <v-list-item-group v-model="bank" color="primary">
                  <v-list-item-subtitle
                    class="d-flex justify-space-between"
                    v-for="b in item.supplierBankListOutputDTOS"
                    :key="b.id"
                    :value="b"
                  >
                    <div>银行名称:{{ b.bank }}</div>
                    <div>银行账户:{{ b.account }}</div>
                    <div>币别:{{ b.ccyCode }}</div>
                  </v-list-item-subtitle>
                </v-list-item-group>
              </v-list>
            </td>
          </template>
        </v-data-table>
      </template>
      <template #供应商评论按钮>
        <v-btn
          outlined
          small
          tile
          color="success"
          class="mx-1"
          @click="addSupplierComment"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!comment"
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editSupplierComment"
          v-permission="['供应商评论:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!comment"
          small
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delSupplierComment"
          v-permission="['供应商评论:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #供应商准入退出评级记录>
        <v-table-searchable
          outlined
          ref="table"
          :table-name="tableName"
          :headers="headers2"
          :req-url="reqUrl2"
          :fix-header="false"
          :dense="true"
          :search-remain="searchObj2"
        >
          <template v-slot:[`item.applicationNo`]="{ item }">
            <router-link
              v-if="item.applicationNo"
              :to="{
                name: 'supplier-in-detail',
                params: { id: item.id },
              }"
            >
              {{ item.applicationNo }}
            </router-link>
            <div v-else>--</div>
          </template>
          <template v-slot:[`item.status`]="{ item }">
            <v-chip small :color="statusColors[item.status]" :dark="true">
              {{ statuses[item.status] }}
            </v-chip>
          </template>
          <template v-slot:[`item.applyType`]="{ item }">
            <v-chip small v-if="item.applyType == 1">准入</v-chip>
            <v-chip small color="warning" v-if="item.applyType == 2">
              退出
            </v-chip>
            <v-chip small color="error" v-if="item.applyType == 3">评级</v-chip>
          </template>
        </v-table-searchable>
      </template>
      <template #供应商评论>
        <v-table-searchable
          outlined
          ref="table"
          :table-name="tableName"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :dense="true"
          :search-remain="searchObj"
          v-model="comment"
        >
          <template v-slot:[`item.orderNo`]="{ item }">
            <router-link
              v-if="item.orderNo"
              :to="{
                name: types[item.orderType],
                params: { id: item.orderId },
              }"
            >
              {{ item.orderNo }}
            </router-link>
            <div v-else>--</div>
          </template>
        </v-table-searchable>
      </template>
    </v-detail-view>
    <bank-dialog
      :initialData="form1"
      v-model="formShow1"
      @success="loadBank"
    ></bank-dialog>
    <pay-com-dialog
      :initialData="form2"
      v-model="formShow2"
      @success="loadBank"
    ></pay-com-dialog>
    <comment-dialog
      v-model="dialog"
      :initialData="initData"
      @success="success"
    ></comment-dialog>
  </v-container>
</template>
<script>
import bankDialog from './private/bank-dialog.vue'
import PayComDialog from './private/pay-com-dialog.vue'
import commentDialog from '@/views/maritime-maintence/components/comment-dialog.vue'
import routerControl from '@/mixin/routerControl'
// account	账号	string
// flagBlacklist	是否黑名单	boolean
// id	物理主键	string
// name	供应商名称	string
// nameEn	英文名称	string
// sapCode	sap代码	string

// orderId	订单id	string
// orderNo	订单单号	string
// orderType	订单类型	string
// remark	评价	string
// remarkTime	评论时间	string
// score1	评分字段1:产品质量	number
// score2	评分字段2:工作质量	number
// score3	评分字段3:准时送货	number
// score4	评分字段4:售后服务	number
// score5	评分字段5:信用度	number
// scoreAvg	平均分	number
// supplierId	供应商id	string
// supplierName	供应商名称	string
// userNickName	评论人名称	string
export default {
  components: { bankDialog, PayComDialog, commentDialog },
  mixins: [routerControl],
  name: 'supplier-detail',
  created() {
    this.backRouteName = 'supplier-main-list'
    this.statuses = ['暂无审批', '草稿', '审批中', '已审批', '已驳回']
    this.statusColors = ['info', '', 'warning', 'success', 'error']
    this.subtitles = [
      '供应商基础信息',
      '管理负责人',
      '付款公司',
      '供应商准入退出评级记录',
      '供应商评论',
    ]
    this.banHeaders = [
      { text: '开户账号', value: 'account' },
      { text: '开户地址', value: 'address' },
      { text: '开户行', value: 'bank' },
      { text: '开户银行代码', value: 'bankCode' },
      { text: '开户城市', value: 'city' },
      { text: '开户国家', value: 'country' },
      { text: '币种', value: 'ccyName' },
      { text: '开户名', value: 'name' },
      { text: '开户地区/省', value: 'region' },
      { text: 'swift代码', value: 'swiftCode' },
    ]
    this.payComHeaders = [
      { text: '', value: 'data-table-expand' },
      { text: '付款公司', value: 'paymentCompany' },
      { text: '有效期-开始', value: 'beginDate' },
      { text: '赊销期（天）', value: 'creditDate' },
      { text: '有效期-结束', value: 'endDate' },
      { text: '联系人姓名', value: 'managerName' },
      { text: '联系人电话', value: 'managerPhone' },
      { text: '评级打分', value: 'score' },
      // { text: '状态', value: 'status' },
      { text: '供应商类型', value: 'supplierType' },
    ]
    this.dict = [
      {
        dicType: 'currency',
        label: '币种',
        key: 'currencyType',
      },
    ]
    this.tableName = ''
    this.reqUrl = '/business/shipAffairs/SupplierAssessment/pageBySupplierId'
    this.reqUrl2 = '/business/shipAffairs/supplierInOut/list'
    this.reqUrl3 = '/business/shipAffairs/Supplier/supplierManagerPage'
    this.headers = [
      { text: '订单号', value: 'orderNo' },
      { text: '订单类型', value: 'orderType' },
      { text: '评价', value: 'remark' },
      { text: '质量评分', value: 'score1' },
      { text: '服务评分', value: 'score2' },
      { text: '评论人', value: 'userNickName' },
      { text: '评论时间', value: 'remarkTime' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.headers2 = [
      { text: '申请单号', value: 'applicationNo' },
      { text: '申请类型', value: 'applyType' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.managerHeader = [
      { text: '姓名', value: 'managerName' },
      { text: '创建时间', value: 'createTime' },
      { text: '操作人', value: 'handlerName' },
    ]
    this.searchObj = {
      supplierId: this.$route.params.id,
    }
    this.searchObj2 = {
      supplierId: this.$route.params.id,
      status: 3,
    }
    this.searchObj3 = {
      supplierId: this.$route.params.id,
    }
    this.types = {
      备件订单: 'spare-order-detail',
      滑油订单: 'soil-order-detail',
      物料订单: 'materials-order-detail',
      航修修理单: 'voyage-repair-detail',
      坞修修理单: 'dock-repair-detail',
    }
  },
  computed: {
    supId() {
      return this.$route.params.id
    },
    payCom() {
      return this.payComs.length > 0 ? this.payComs[0] : false
    },
    // bank() {
    //   return this.banks.length > 0 ? this.banks[this.index] : false
    // },
  },
  data() {
    return {
      detail: { name: '' },
      supplierBankList: [],
      supplierPurchaserList: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        email: (v) => !v || /.+@.+\..+/.test(v) || '请输入有效的邮箱',
      },
      bank: false,
      payComs: [],
      pwd: '',
      loading: false,
      form1: {},
      formShow1: false,
      form2: {},
      formShow2: false,
      expanded: [],
      index: null,
      dialog: false,
      initData: {},
      comment: false,
      financeList: [],
      managerIds: [],
      managerIdsCg: [],
      cgList: [],
    }
  },

  methods: {
    async save(goBack) {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/Supplier/update'
      const { errorRaw } = await this.postAsync(reqUrl, this.detail)
      if (!errorRaw) goBack()
    },

    async loadDeatil() {
      await Promise.all([this.loadBasic(), this.loadBank()])
    },

    async loadBasic() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getById/${this.supId}`,
      )
      this.detail = data
      this.managerIds = this.detail.managerId
      this.managerIdsCg = this.detail.managercgId
    },
    async loadBank() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getDetail2/${this.supId}`,
      )
      this.supplierBankList = data.supplierBankListOutputDTOS
      this.supplierPurchaserList = data.supplierPurchaserListOutputDTOS
      this.expanded = this.supplierPurchaserList.map((item) => item.id)
    },

    async getPwd() {
      this.loading = true
      if (!this.pwd) {
        const { data } = await this.getAsync(
          `/business/shipAffairs/Supplier/getPasswordById/${this.supId}`,
        )
        this.pwd = data
      }
      this.loading = false
      await this.$dialog.msgbox.alert(`您的密码是 ${this.pwd}`)
    },

    async delSupplierBank() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/SupplierBank/deleteById/${this.bank.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.loadBank()
      this.bank = false
    },
    async delSupplierP() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/SupplierPurchaser/deleteById/${this.payCom.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      this.payCom = false
    },
    editSupplierBank() {
      this.form1 = { ...this.bank }
      this.formShow1 = true
    },
    addSupplierBank() {
      this.form1 = {
        supplierId: this.$route.params.id,
        purchaserId: this.payCom.id,
      }
      this.formShow1 = true
    },
    editSupplierP() {
      this.form2 = { ...this.payCom }
      this.formShow2 = true
    },
    addPayCom() {
      this.form2 = { supplierId: this.$route.params.id }
      this.formShow2 = true
    },
    addSupplierComment() {
      this.initData = {
        supplierId: this.detail.id,
        supplierName: this.detail.name + '/' + this.detail.nameEn,
      }
      this.dialog = true
    },
    editSupplierComment() {
      this.initData = {
        ...this.comment,
        supplierId: this.detail.id,
        supplierName: this.detail.name + '/' + this.detail.nameEn,
      }
      this.dialog = true
    },
    async delSupplierComment() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/SupplierAssessment/assessmentDelete`,
        {
          id: this.comment.id,
        },
      )
      if (errorRaw) return
      this.$dialog.message.success(`删除成功`)
      this.comment = false
      await this.$refs.table.loadTableData()
    },
    async success() {
      this.comment = false
      await this.$refs.table.loadTableData()
    },
    async getFinanceList() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getFinanceList`,
      )
      console.log(data)
      this.financeList = data
    },
    async saveFinanceList() {
      console.log(this.managerIds)
      this.detail.managerIds = this.managerIds
      const reqUrl = '/business/shipAffairs/Supplier/updateManager'
      const { errorRaw } = await this.postAsync(reqUrl, this.detail)
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
    async saveMails() {
      // this.detail.invoiceMails = this.managerIds
      const reqUrl = '/business/shipAffairs/Supplier/updateSupplierInvoiceMails'
      const { errorRaw } = await this.postAsync(reqUrl, this.detail)
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
    async getCgList() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getCgList`,
      )
      console.log(data)
      this.cgList = data
    },
    async saveCgList() {
      console.log(this.managerIdsCg)
      this.detail.managercgIds = this.managerIdsCg
      const reqUrl = '/business/shipAffairs/Supplier/updateManagerCg'
      const { errorRaw } = await this.postAsync(reqUrl, this.detail)
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
  },

  mounted() {
    this.getFinanceList()
    this.getCgList()
    this.loadDeatil()
  },
}
</script>

<style></style>
