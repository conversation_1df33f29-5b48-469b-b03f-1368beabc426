<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}供应商付款公司
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-subtitle class="pb-0 mb-1">手动录入</v-card-subtitle>
      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.beginDate"
                  label="有效期-开始"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.creditDate"
                  label="赊销期（天）"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.endDate"
                  label="有效期-结束"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.managerEmail"
                  label="邮箱"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.managerName"
                  label="联系人姓名"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.managerPhone"
                  label="联系人电话"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.paymentCompany"
                  label="付款公司"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.score"
                  label="评级打分"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.status"
                  label="状态"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.supplierId"
                  label="供应商id	"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.supplierType"
                  label="供应商类型"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['供应商付款银行:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="formShow = true"
          v-permission="['供应商付款银行:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editSupplierP"
          v-permission="['供应商付款银行:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delSupplierP"
          v-permission="['供应商付款银行:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'supplier-payment-company',
  created() {
    this.tableName = '供应商付款公司'
    this.reqUrl = '/business/shipAffairs/SupplierPurchaser/list'
    this.headers = [
      { text: '有效期-开始', value: 'beginDate' },
      { text: '赊销期（天）', value: 'creditDate' },
      { text: '有效期-结束', value: 'endDate' },
      { text: '邮箱', value: 'managerEmail' },
      { text: '联系人姓名', value: 'managerName' },
      { text: '联系人电话', value: 'managerPhone' },
      { text: '付款公司', value: 'paymentCompany' },
      { text: '评级打分', value: 'score' },
      { text: '状态', value: 'status' },
      { text: '供应商id', value: 'supplierId' },
      { text: '供应商类型', value: 'supplierType' },
    ]
  },

  data() {
    return {
      selected: [],
      form: {
        beginDate: '',
        creditDate: 0,
        endDate: '',
        managerEmail: '',
        managerName: '',
        managerPhone: '',
        paymentCompany: '',
        score: 0,
        status: 0,
        supplierId: '',
        supplierType: '',
      },
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      isEdit: false,
      loading: false,
      formShow: false,
    }
  },

  methods: {
    async delSupplierP() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/SupplierPurchaser/deleteById/${this.selected.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editSupplierP() {
      this.form = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/SupplierPurchaser/update'
        : '/business/shipAffairs/SupplierPurchaser/insert'
      const { errorRaw } = await this.postAsync(reqUrl, this.form, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.form = {
        beginDate: '',
        creditDate: 0,
        endDate: '',
        managerEmail: '',
        managerName: '',
        managerPhone: '',
        paymentCompany: '',
        score: 0,
        status: 0,
        supplierId: '',
        supplierType: '',
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    /*async importSuccess() {
      await this.$refs.table.loadTableData()
      await this.$nextTick()
    },*/
    closeForm() {
      this.$refs.form.reset()
      this.form = {
        beginDate: '',
        creditDate: 0,
        endDate: '',
        managerEmail: '',
        managerName: '',
        managerPhone: '',
        paymentCompany: '',
        score: 0,
        status: 0,
        supplierId: '',
        supplierType: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
