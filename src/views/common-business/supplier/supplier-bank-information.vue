<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}供应商银行信息
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-subtitle class="pb-0 mb-1">手动录入</v-card-subtitle>
      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.account"
                  label="开户账号"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.address"
                  label="开户地址"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.bank"
                  label="开户行"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.bankCode"
                  label="开户银行代码"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.city"
                  label="开户城市"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.country"
                  label="开户国家"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.currencyType"
                  label="币种"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.name"
                  label="开户名"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.region"
                  label="开户地区/省"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.supplierId"
                  label="供应商id"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.swiftCode"
                  label="swift代码"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['供应商银行信息:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="addSupplierBank"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editSupplierBank"
          v-permission="['供应商银行信息:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delSupplierBank"
          v-permission="['供应商银行信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'supplier-bank-information',
  created() {
    this.tableName = '供应商银行信息'
    this.reqUrl = '/business/shipAffairs/SupplierBank/list'
    this.headers = [
      { text: '开户账号', value: 'account' },
      { text: '开户地址', value: 'address' },
      { text: '开户行', value: 'bank' },
      { text: '开户银行代码', value: 'bankCode' },
      { text: '开户城市', value: 'city' },
      { text: '开户国家', value: 'country' },
      { text: '币种', value: 'currencyType' },
      { text: '开户名', value: 'name' },
      { text: '开户地区/省', value: 'region' },
      { text: '供应商id', value: 'supplierId' },
      { text: 'swift代码', value: 'swiftCode' },
    ]
  },

  data() {
    return {
      selected: [],
      form: {
        account: '',
        address: '',
        bank: '',
        bankCode: '',
        city: '',
        country: '',
        currencyType: '',
        name: '',
        region: '',
        supplierId: '',
        swiftCode: '',
      },
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      isEdit: false,
      loading: false,
      formShow: false,
    }
  },

  methods: {
    async delSupplierBank() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/SupplierBank/deleteById/${this.selected.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editSupplierBank() {
      this.form = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/SupplierBank/update'
        : '/business/shipAffairs/SupplierBank/insert'
      const { errorRaw } = await this.postAsync(reqUrl, this.form, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.form = {
        account: '',
        address: '',
        bank: '',
        bankCode: '',
        city: '',
        country: '',
        currencyType: '',
        name: '',
        region: '',
        supplierId: '',
        swiftCode: '',
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    /*async importSuccess() {
      await this.$refs.table.loadTableData()
      await this.$nextTick()
    },*/
    closeForm() {
      this.$refs.form.reset()
      this.form = {
        account: '',
        address: '',
        bank: '',
        bankCode: '',
        city: '',
        country: '',
        currencyType: '',
        name: '',
        region: '',
        supplierId: '',
        swiftCode: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
