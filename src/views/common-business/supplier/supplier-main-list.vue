<template>
  <v-container fluid>
    <!-- <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}供应商主表信息
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-subtitle class="pb-0 mb-1">手动录入</v-card-subtitle>
      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.sapCode"
                  label="SAP代码"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.name"
                  label="供应商名称"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.nameEn"
                  label="英文名称"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.account"
                  label="账号"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.supplierMail"
                  label="邮箱"
                  :rules="[rules.required, rules.email]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.password"
                  label="密码"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['供应商列表:保存']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card> -->
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      :push-params="pushParams"
      :search-remain="searchObj"
      :showExportButton="true"
      :specialHeaders="specialHeaders"
    >
      <template #btns>
        <!-- <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="formShow = true"
          v-permission="['供应商列表:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="!selected"
          @click="resetPassword"
          v-permission="['供应商准入:账户密码重置']"
        >
          <span class="mdi mdi-account-reactivate"></span>
          账户密码重置
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{
            name: 'supplier-in-detail',
            params: { id: 'new', applyType: 1 },
          }"
          v-permission="['供应商准入:新增准入']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增准入
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="!selected"
          :to="{
            name: 'supplier-in-detail',
            params: { id: 'new', applyType: 2, supplierId: selected.id },
          }"
          v-permission="['供应商准入:新增退出']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增退出
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{
            name: 'supplier-in-detail',
            params: { id: 'new', applyType: 4, supplierId: selected.id },
          }"
          v-permission="['供应商准入:新增慎用']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增慎用
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{
            name: 'supplier-in-detail',
            params: { id: 'new', applyType: 3, supplierId: selected.id },
          }"
          v-permission="['供应商准入:新增评级']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增评级
        </v-btn>
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delSupplier"
          v-permission="['供应商列表:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            label="供应商名称"
            outlined
            dense
            v-model="searchObj.name"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            label="英文名称"
            outlined
            dense
            v-model="searchObj.nameEn"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            label="SAP代码"
            outlined
            dense
            v-model="searchObj.sapCode"
          ></v-text-field>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="3">
          <v-text-field
            label="邮箱"
            outlined
            dense
            v-model="searchObj.supplierMails"
          ></v-text-field>
        </v-col> -->
        <v-col cols="12" sm="6" md="3">
          <v-select
            v-model="searchObj.businessStatus"
            :items="[
              { text: '已通过SAP评审', value: '已通过SAP评审' },
              { text: '未通过SAP评审', value: '未通过SAP评审' },
              { text: '已退出', value: '已退出' },
            ]"
            label="状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <!-- 
        3开头的就是员工
        2开头的就是外部
        0开头的就是内部公司
 -->
        <v-col cols="12" sm="6" md="3">
          <v-select
            v-model="searchObj.type"
            :items="[
              { text: '内部公司', value: '0' },
              { text: '外部', value: '2' },
              { text: '员工', value: '3' },
            ]"
            label="内部、外部、个人"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-dict-select
            v-model="searchObj.businessType2"
            label="业务分类"
            required
            dict-type="cost_subject_type"
            dense
          ></v-dict-select>
          <!-- <v-select
            v-model="searchObj.businessType2"
            :items="[
              { text: '船员', value: '船员' },
              { text: '备件', value: '备件' },
              { text: '物料', value: '物料' },
              { text: '滑油', value: '滑油' },
              { text: '化学品', value: '化学品' },
              { text: '缆绳', value: '缆绳' },
              { text: '绑扎件', value: '绑扎件' },
              { text: '油漆', value: '油漆' },
              { text: '锚、锚链', value: '锚、锚链' },
              { text: '消防救生检验', value: '消防救生检验' },
              { text: '海图', value: '海图' },
              { text: '通导', value: '通导' },
              { text: '坞修', value: '坞修' },
              { text: '航修', value: '航修' },
              { text: '固定资产', value: '固定资产' },
              { text: '年度协议', value: '年度协议' },
              { text: '大宗采购', value: '大宗采购' },
              { text: '大宗采购', value: '大宗采购' },
              { text: '查看全部（包含未分配业务类型的供应商）', value: '' },
            ]"
            label="业务类型"
            outlined
            clearable
            dense
          ></v-select> -->
        </v-col>
        <v-col cols="12" md="2">
          <v-autocomplete
            label="对账负责人"
            v-model="searchObj.manager"
            dense
            outlined
            item-text="nickName"
            item-value="id"
            :items="financeList"
            clearable
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" md="2">
          <v-autocomplete
            label="采购负责人"
            v-model="searchObj.managerCg"
            dense
            outlined
            item-text="nickName"
            item-value="id"
            :items="cgList"
            clearable
          ></v-autocomplete>
        </v-col>
      </template>
      <template v-slot:[`item.account`]="{ item }">
        <v-chip small v-if="item.sapCode != null">{{ item.sapCode }}</v-chip>
        <v-chip small v-if="item.sapCode == null">{{ item.account }}</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import { resetSourceDefaulter } from 'echarts/lib/data/helper/sourceHelper'

export default {
  name: 'supplier-main-list',
  created() {
    this.tableName = '供应商主表信息'
    this.reqUrl = '/business/shipAffairs/Supplier/list'
    this.headers = [
      { text: 'SAP代码', value: 'sapCode' },
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '登陆账号', value: 'account' },
      // { text: '账号', value: 'sapCode' },
      { text: '联系人', value: 'contact' },
      // { text: '手机号', value: 'telePhone' },
      { text: '邮箱', value: 'supplierMail' },
      { text: '状态', value: 'businessStatus' },
      { text: '对账负责人', value: 'managerName' },
      { text: '采购负责人', value: 'managercgName' },
    ]
    this.specialHeaders = [
      // {
      //   text: 'status',
      //   value: [
      //     { text: 30, value: '付款审批审批中' },
      //     { text: 31, value: '付款审批退回' },
      //     { text: 32, value: '付款审批已完成' },
      //     { text: 33, value: '已付款' },
      //     { text: 34, value: '付款审批未提交' },
      //   ],
      // },
    ]
    this.pushParams = { name: 'supplier-detail' }
  },

  data() {
    return {
      selected: false,
      form: {
        nameEn: '',
        name: '',
        sapCode: '',
        account: '',
        password: '',
      },
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        email: (v) => /.+@.+\..+/.test(v) || '请输入有效的邮箱',
      },
      isEdit: false,
      loading: false,
      formShow: false,
      searchObj: {},
      financeList: [],
      cgList: [],
    }
  },

  methods: {
    resetSourceDefaulter,
    async resetPassword() {
      if (!(await this.$dialog.msgbox.confirm('确定重置账号密码？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/Supplier/resetById/${this.selected.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`密码重置失败，请重试`)
        return
      }
      this.$dialog.message.success(`密码重置成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
    async delSupplier() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/Supplier/deleteById/${this.selected.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/Supplier/update'
        : '/business/shipAffairs/Supplier/insert'
      const { errorRaw, data } = await this.postAsync(reqUrl, this.form, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.form = {
        nameEn: '',
        name: '',
        sapCode: '',
        account: '',
        password: '',
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      this.$router.push({ name: 'supplier-detail', params: { id: data } })
    },
    closeForm() {
      this.$refs.form.reset()
      this.form = {
        nameEn: '',
        name: '',
        sapCode: '',
        account: '',
        password: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
    async getFinanceList() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getFinanceList`,
      )
      console.log(data)
      this.financeList = data
    },
    async getCgList() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getCgList`,
      )
      console.log(data)
      this.cgList = data
    },
  },

  mounted() {
    this.getFinanceList()
    this.getCgList()
  },
}
</script>

<style></style>
