<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}船舶仓库信息
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text v-if="isExcel">
        <div class="pb-0 mb-1">excel导入</div>
        <v-row>
          <v-col cols="12" md="2">
            <v-ship-select
              label="船舶"
              v-model="excel.shipCode"
              required
              :rules="[rules.required]"
              use-id
            ></v-ship-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-btn
              outlined
              tile
              color="success"
              class="mx-1"
              href="/api/business/common/bizTemplate/download?bizCode=shipDepository"
              v-permission="['船舶仓库:下载模板']"
            >
              <v-icon left>mdi-download</v-icon>
              下载模板
            </v-btn>
          </v-col>
          <v-col colss="12" md="2">
            <v-import-btn
              v-show="excel.shipCode"
              import-url="/business/shipAffairs/Depository/insertByExcel"
              :other-params="excel"
              @importSuccess="onImportSuccess"
            ></v-import-btn>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-text v-else>
        <div class="pb-0 mb-1">手动录入</div>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="form.shipCode"
                  label="船舶"
                  :rules="[rules.required]"
                  required
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-select
                  v-model="form.department"
                  label="部门"
                  :rules="[rules.required]"
                  required
                  dense
                  outlined
                  :items="['甲板部', '轮机部']"
                ></v-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-dict-select
                  v-model="form.type"
                  label="仓库类型"
                  dictType="ship_depository_type"
                  :rules="[rules.required]"
                  dense
                  required
                ></v-dict-select>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.name"
                  label="仓库名称"
                  :rules="[rules.required]"
                  dense
                  outlined
                  required
                ></v-text-field>
              </v-col>

              <!-- <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.code"
                  label="仓库编码"
                  :rules="[rules.required]"
                  dense
                  outlined
                  required
                ></v-text-field>
              </v-col> -->

              <!-- <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.address"
                  label="仓库位置"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col> -->

              <v-col cols="12" md="2">
                <v-ship-station
                  v-model="form.manager"
                  label="仓库管理岗位"
                  dense
                  :rules="[rules.required]"
                ></v-ship-station>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  dense
                  outlined
                  v-model="form.remark"
                  label="备注"
                ></v-textarea>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['船舶仓库:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      use-ship
      @dbclick="editShiphouse"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="formShow = true"
          v-permission="['船舶仓库:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editShiphouse"
          v-permission="['船舶仓库:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delShiphouse"
          v-permission="['船舶仓库:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'ship-house-list',
  created() {
    this.tableName = '船舶仓库信息'
    this.reqUrl = '/business/shipAffairs/Depository/list'
    this.searchDicts = [
      {
        dicType: 'ship_depository_type',
        label: '仓库类型',
        key: 'type',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '部门', value: 'department' },
      { text: '仓库类型', value: 'type' },
      { text: '仓库名称', value: 'name' },
      // { text: '仓库编码', value: 'code' },
      // { text: '仓库位置', value: 'address' },
      { text: '仓库管理岗位', value: 'manager' },
      // { text: '业务模块', value: 'businessModule' },
      { text: '备注', value: 'remark' },
    ]
  },

  data() {
    return {
      selected: [],
      form: {
        ship: '',
        department: '',
        type: '0',
        name: '',
        code: '',
        address: '',
        manager: '',
        businessModule: '',
        remark: '',
      },
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      isEdit: false,
      loading: false,
      formShow: false,
      isExcel: false,
      excel: {},
    }
  },

  methods: {
    async delShiphouse() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/Depository/deleteById/${this.selected.id}`,
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editShiphouse() {
      this.form = {
        ...this.selected,
        shipCode: this.selected.shipInfo.shipCode,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/Depository/update'
        : '/business/shipAffairs/Depository/insert'
      const { errorRaw } = await this.postAsync(reqUrl, this.form)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.form = {}
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    closeForm() {
      !this.isExcel && this.$refs.form.reset()
      this.form = {}
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
    async onImportSuccess() {
      await this.$refs.table.loadTableData()
      this.$dialog.message.success('导入成功，请注意检查更新')
      this.closeForm()
    },
  },

  mounted() {},
}
</script>

<style></style>
