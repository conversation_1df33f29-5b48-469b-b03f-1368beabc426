<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}船舱信息
        <v-spacer></v-spacer>
        <!-- <v-switch
          class="mx-1"
          v-model="isExcel"
          :label="isExcel ? 'Excel' : '手动录入'"
        ></v-switch> -->
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text v-if="isExcel">
        <div class="pb-0 mb-1">excel导入</div>
        <v-row>
          <v-col cols="12" md="2">
            <v-ship-select
              label="船舶"
              v-model="excel.shipCode"
              required
              :rules="[rules.required]"
            ></v-ship-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-btn
              outlined
              tile
              color="success"
              class="mx-1"
              href="/api/business/common/bizTemplate/download?bizCode=shipCabin"
              v-permission="['船舱信息:下载模板']"
            >
              <v-icon left>mdi-download</v-icon>
              下载模板
            </v-btn>
          </v-col>
          <v-col cols="12" md="2">
            <v-import-btn
              v-show="excel.shipCode"
              import-url="/business/shipAffairs/Cabin/insertByExcel"
              :other-params="excel"
              @importSuccess="onImportSuccess"
            ></v-import-btn>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-text v-else>
        <div class="pb-0 mb-1">手动录入</div>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-ship-select
                  v-model="form.shipCode"
                  label="船舶代码"
                  :rules="[rules.required]"
                  required
                ></v-ship-select>
              </v-col>

              <v-col cols="12" md="2">
                <v-dict-select
                  v-model="form.type"
                  label="船舱类型"
                  dictType="ship_cabin_type"
                  :rules="[rules.required]"
                  dense
                  required
                ></v-dict-select>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.post"
                  label="管理岗位"
                  dense
                  readonly
                  outlined
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.name"
                  label="船舱名称"
                  :rules="[rules.required]"
                  dense
                  required
                  outlined
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.volume"
                  label="船舱(m³)"
                  type="number"
                  :rules="[rules.required]"
                  dense
                  required
                  outlined
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.address"
                  dense
                  label="船舱位置"
                  outlined
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-textarea
                  dense
                  v-model="form.remark"
                  label="备注"
                  outlined
                ></v-textarea>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['船舱信息:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      @dbclick="editCarbin"
      use-ship
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="formShow = true"
          v-permission="['船舱信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editCarbin"
          v-permission="['船舱信息:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delCarbin"
          v-permission="['船舱信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipCode`]="{ item }">
        {{ (item.shipInfo && item.shipInfo.chShipName) || '' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'carbin-list',
  created() {
    this.tableName = '船舱信息'
    this.reqUrl = '/business/shipAffairs/Cabin/list'
    this.searchDicts = [
      {
        dicType: 'ship_cabin_type',
        label: '船舱类型',
        key: 'type',
      },
    ]
    this.headers = [
      { text: '船舶名称', value: 'shipCode' },
      { text: '船舱类型', value: 'type' },
      { text: '船舱名称', value: 'name' },
      { text: '船舱容积', value: 'volume' },
      { text: '船舱位置', value: 'address' },
      { text: '管理岗位', value: 'post' },
      { text: '备注', value: 'remark' },
    ]
  },

  data() {
    return {
      selected: [],
      form: {
        shipCode: '',
        type: '0',
        name: '',
        volume: '',
        address: '',
        remark: '',
        post: '轮机长',
      },
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      isEdit: false,
      loading: false,
      formShow: false,
      isExcel: false,
      excel: {},
    }
  },

  methods: {
    async delCarbin() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/Cabin/deleteById/${this.selected.id}`,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },

    async editCarbin() {
      this.form = {
        ...this.selected,
        shipCode: this.selected.shipInfo.shipCode,
        post: '轮机长',
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/Cabin/update'
        : '/business/shipAffairs/Cabin/insert'
      const { errorRaw } = await this.postAsync(reqUrl, this.form)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.form = {
        post: '轮机长',
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    closeForm() {
      !this.isExcel && this.$refs.form.reset()
      this.form = {
        post: '轮机长',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
    async onImportSuccess() {
      await this.$refs.table.loadTableData()
      this.$dialog.message.success('导入成功，请注意检查更新')
      this.closeForm()
    },
  },

  mounted() {},
}
</script>

<style></style>
