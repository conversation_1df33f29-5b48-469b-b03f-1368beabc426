<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}港口信息
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-subtitle class="pb-0 mb-1">手动录入</v-card-subtitle>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.portCn"
                  label="中文名称"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.portEn"
                  label="英文名称"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.portCode"
                  label="港口代码"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.codeThree"
                  label="3位码"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-text-field
                  v-model="form.codeTwo"
                  label="国家代码"
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="2">
                <v-dict-select
                  v-model="form.type"
                  label="港口分类"
                  dictType="port_type"
                  :rules="[rules.required]"
                  required
                ></v-dict-select>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['港口信息:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      @dbclick="editPort"
      ref="table"
      :search-dicts="searchDicts"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :dense="true"
      fuzzy-label="模糊搜索"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="formShow = true"
          v-permission="['港口信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editPort"
          v-permission="['港口信息:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delPort"
          v-permission="['港口信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'port-list',
  created() {
    this.tableName = '港口信息'
    this.reqUrl = '/business/shipAffairs/Port/list'
    this.searchDicts = [
      {
        dicType: 'port_type',
        label: '港口类型',
        key: 'type',
      },
    ]
    this.headers = [
      { text: '中文名称', value: 'portCn' },
      { text: '英文名称', value: 'portEn' },
      { text: '港口代码', value: 'portCode' },
      { text: '3位码', value: 'codeThree' },
      { text: '国家代码', value: 'codeTwo' },
      { text: '分类', value: 'type' },
    ]
  },

  data() {
    return {
      selected: false,
      form: {
        portCn: '',
        portEn: '',
        portCode: '',
        codeThree: '',
        codeTwo: '',
        type: '',
      },
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      isEdit: false,
      loading: false,
      formShow: false,
    }
  },

  methods: {
    async delPort() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/Port/deleteById/${this.selected.id}`,
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editPort() {
      this.form = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/Port/update'
        : '/business/shipAffairs/Port/insert'
      const { errorRaw } = await this.postAsync(reqUrl, this.form, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.form = {
        portCn: '',
        portEn: '',
        portCode: '',
        codeThree: '',
        codeTwo: '',
        type: '',
      }
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    closeForm() {
      this.$refs.form.reset()
      this.form = {
        portCn: '',
        portEn: '',
        portCode: '',
        codeThree: '',
        codeTwo: '',
        type: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
