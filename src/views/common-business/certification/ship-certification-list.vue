<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col
                class="py-0"
                v-for="h in tableFeilds"
                :key="h.value"
                cols="12"
                md="3"
              >
                <v-ship-select
                  v-if="h.value == 'shipInfo'"
                  v-model="formData.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
                <vs-date-picker
                  v-else-if="h.type == 'date'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  outlined
                  dense
                ></vs-date-picker>
                <v-dict-select
                  v-else-if="h.value == 'cetificateType'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                  dict-type="ship_certificate_type"
                ></v-dict-select>
                <v-text-field
                  v-else-if="h.type == 'number'"
                  type="number"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-select
                  v-model="formData.toCheckStatus"
                  label="待检状态"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :items="['仅签章', '换发']"
                ></v-select>
              </v-col>
              <v-col cols="6"></v-col>
              <v-col cols="12">
                <v-card-text>
                  <v-attach-list
                    :attachments="formData.attachmentRecords"
                    @change="(ids) => (formData.attachmentIds = ids)"
                  ></v-attach-list>
                </v-card-text>
              </v-col>

              <v-col cols="6">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save(true)"
                  v-permission="['船舶证书:编辑']"
                  block
                >
                  <v-icon left>mdi-refresh</v-icon>
                  {{ isEdit ? '勘误' : '新增' }}
                </v-btn>
              </v-col>
              <v-col v-if="isEdit" cols="6">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save(false)"
                  v-permission="['船舶证书:勘误']"
                  block
                >
                  <v-icon left>mdi-pencil</v-icon>
                  换签
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :search-dicts="searchDicts"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-ship-select v-model="shipCode"></v-ship-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.shipManageLock"
            label="全部船舶"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="!shipCode"
          :href="excelUrl"
          v-permission="['船舶证书:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['船舶证书:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['船舶证书:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.history`]="{ item }">
        <v-btn
          @click.stop="openHistoryDialog(item.id)"
          dark
          x-small
          color="primary"
          elevation="0"
          v-permission="['船舶证书:查看历史']"
        >
          查看历史
        </v-btn>
      </template>
      <template v-slot:[`item.invaildDate`]="{ item }">
        <v-chip
          small
          :color="
            today < new Date(item.invaildDate)
              ? new Date(
                  new Date().setDate(
                    today.getDate() + (item.alarmDayNum * 1 || 0),
                  ),
                ) < new Date(item.invaildDate)
                ? 'success'
                : 'warning'
              : 'error'
          "
        >
          {{ item.invaildDate }}
        </v-chip>
      </template>
    </v-table-searchable>
    <v-dialog
      v-model="historyDialog"
      max-width="1300"
      hide-overlay
      attach="#mask"
    >
      <v-card>
        <v-card-title class="text-h5">历史证书</v-card-title>
        <v-card-text>
          <v-table-list
            :headers="headers"
            :items="historys"
            hide-default-footer
          >
            <template v-slot:[`item.shipInfo`]="{ item }">
              {{ item.shipInfo.chShipName }}
            </template>
          </v-table-list>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
// alarmDayNum	报警天数	string
// catificateNo	证书号码	string
// cetificateName	证书名称	string
// cetificateType	证书类型	string
// handAddress	发放地点	string
// id	物理主键	string
// invaildDate	到期日期	string
// issueDate	签发日期	string
// manageDept	主管机关	string
// parentId	父级证书id	string
// printSort	打印排序	integer
// remark	备注	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// status	记录状态	string
// toCheckDate	检查（待检）日期	string
// toCheckStatus	待检状态	string
// vaildDate	有效期（年）	integer
// vaildateType	有效期种类	string
// yearCheckDate	年检日期		false
const today = new Date(Date.now())
export default {
  name: 'ship-certification-list',
  created() {
    this.tableName = '船舶证书'
    this.reqUrl = '/business/common/ship/certificationOfShip/certificationPage'
    this.searchDicts = [
      {
        dicType: 'ship_certificate_type',
        label: '证书类型',
        key: 'cetificateType',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '证书名称', value: 'cetificateName' },
      { text: '证书类型', value: 'cetificateType' },
      { text: '证书号码', value: 'catificateNo' },
      { text: '签发日期', value: 'issueDate', type: 'date' },
      { text: '有效期（年）', value: 'vaildDate', type: 'number' },
      {
        text: '年检日期',
        value: 'yearCheckDate',
        hideDefault: true,
        type: 'date',
      },
      { text: '报警天数', value: 'alarmDayNum', hideDefault: true },
      { text: '到期日期', value: 'invaildDate', type: 'date' },
      { text: '发放地点', value: 'handAddress' },
      { text: '主管机关', value: 'manageDept' },
      {
        text: '打印排序',
        value: 'printSort',
        hideDefault: true,
        type: 'number',
      },
      { text: '备注', value: 'remark' },
      { text: '检查（待检）日期', value: 'toCheckDate', type: 'date' },
      //   { text: '待检状态', value: 'toCheckStatus' },
      { text: '有效期种类', value: 'vaildateType' },
      { text: '申办费用', value: 'applyFee', type: 'number' },
      { text: '历史证书', value: 'history', noEdit: true },
      { text: '证书附件', value: 'attachmentRecords', noEdit: true },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.today = today
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      shipCode: '',
      searchObj: { shipCode: '' },
      historys: [],
      historyDialog: false,
    }
  },

  computed: {
    excelUrl() {
      return `/api/business/common/ship/certificationOfShip/certificationgExport?shipCode=${this.shipCode}`
    },
    tableFeilds() {
      return this.headers.filter((item) => !item.noEdit)
    },
  },
  watch: {
    shipCode(val) {
      this.searchObj.shipCode = val
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!(await this.$dialog.msgbox.confirm('删除记录后无法恢复,是否删除')))
        return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/certificationOfShip/deleteCertification',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected,
        parentId: this.selected.id,
        shipCode: this.selected.shipInfo.shipCode,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save(withId = false) {
      if (!this.$refs.form.validate()) return
      const reqUrl =
        '/business/common/ship/certificationOfShip/saveOrUpdateCertification'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        {
          ...this.formData,
          id: withId ? this.formData.id : '',
          parentId: withId ? this.formData.parentId : this.formData.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.resetValidation()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },

    async openHistoryDialog(id) {
      const { data } = await this.getAsync(
        '/business/common/ship/certificationOfShip/listById',
        { id },
      )
      this.historys = data
      this.historyDialog = true
    },
  },

  mounted() {},
}
</script>

<style></style>
