<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
    >
      <template #searchflieds></template>
      <template #btns>
        <!-- <v-btn
          :loading="checking"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="check"
          v-permission="['货币汇率维护:同步SAP数据']"
        >
          <v-icon left>mdi-refresh</v-icon>
          同步SAP数据
        </v-btn> -->
      </template>
      <template v-slot:[`item.isEnabled`]="{ item }">
        {{ item.isEnabled ? '是' : '否' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// ccyCode	币种代号	string
// ccyName	币种名称	string
// id	物理主键	string
// isEnabled	是否启用	boolean
// isMain	是否主币种	integer
// rateToMain	本货币对主货币的汇率	number
// rateToThis	主货币对本货币汇率	number
// remark	备注	string
// syncTime	同步时间	string
export default {
  name: 'currency-list',
  created() {
    this.tableName = '货币汇率维护'
    this.reqUrl = '/business/common/ship/currencyExchangeRate/page'
    this.headers = [
      { text: '币种代号', value: 'ccyCode' },
      { text: '币种名称', value: 'ccyName' },
      { text: '是否主币种', value: 'isMain' },
      { text: '对主货币汇率', value: 'rateToMain' },
      { text: '主货币汇率', value: 'rateToThis' },
      { text: '同步时间', value: 'syncTime' },
      { text: '是否启用', value: 'isEnabled' },
      { text: '备注', value: 'remark' },
    ]
  },

  data() {
    return {
      selected: false,
      checking: false,
    }
  },

  methods: {
    async check() {},
  },

  mounted() {},
}
</script>

<style></style>
