<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      table-name="物料基础信息"
      v-model="selected"
      fuzzy-label="物料分类"
      :headers="headers"
      req-url="/business/shipAffairs/MaterialInfo/list"
      :fix-header="false"
      :push-params="pushParams"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/maritime-maintence/materials/new"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editMaterials"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delMaterials"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'material-management-list',
  created() {
    this.searchDicts = [{}]
    this.headers = [
      { text: '物料分类', value: 'type' },
      { text: '物料名称', value: 'name_cn' },
      { text: '物料英文名称', value: 'name_en' },
      { text: '规格', value: 'specs' },
      { text: '型号', value: 'model' },
      { text: '单位', value: 'unit' },
      { text: '物料描述', value: 'description' },
      { text: '物料编码', value: 'code' },
      { text: '品牌', value: 'brand' },
      { text: '备注', value: 'remark' },
    ]
    this.pushParams = {
      name: 'material-detail',
    }
  },
  data: () => ({
    selected: undefined,
  }),

  methods: {
    editMaterials() {
      this.$router.push(`/maritime-maintence/materials/${this.selected.id}`)
    },
    async delMaterials() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        `/business/shipAffairs/MaterialInfo/deleteById/${this.selected.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = undefined
      await this.$nextTick()
    },
  },

  mounted() {},
}
</script>

<style></style>
