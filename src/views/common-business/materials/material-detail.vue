<template>
  <v-container>
    <v-card>
      <v-card-title>物料-{{ isEdit ? '编辑' : '新建' }}</v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="4">
                <v-text-field
                  v-model="formData.type"
                  label="物料分类"
                  required
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  v-model="formData.name_cn"
                  label="物料名称"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  v-model="formData.name_en"
                  label="物料英文名称"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="规格"
                  v-model="formData.specs"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="型号"
                  v-model="formData.model"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="单位"
                  v-model="formData.unit"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="物料描述"
                  v-model="formData.description"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="物料编码"
                  v-model="formData.code"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="品牌"
                  v-model="formData.brand"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  label="备注"
                  v-model="formData.remark"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="'0'"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script>
export default {
  name: 'circular-detail',
  data() {
    return {
      dateMenu: false,
      attachmentRecords: [],
      formData: {
        type: '',
        name_cn: '',
        name_en: '',
        specs: '',
        model: '',
        unit: '',
        description: '',
        code: '',
        brand: '',
        remark: '',
        attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },

    async getMaterials() {
      if (!this.isEdit) {
        this.$store.commit('updateViewTitle', {
          title: '物料信息-新增',
          nowFullPath: this.$route.path,
        })
        return
      }
      const { data } = await this.getAsync(
        `/business/shipAffairs/MaterialInfo/getById/${this.$route.params.id}`,
      )
      this.attachmentRecords = data.attachmentRecords
      this.formData = data

      this.$store.commit('updateViewTagsTooltip', {
        tooltip: data?.code,
        nowFullPath: this.$route.path,
      })
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit
        ? '/business/shipAffairs/MaterialInfo/update'
        : '/business/shipAffairs/MaterialInfo/insert'
      const { errorRaw } = await this.postAsync(url, this.formData)
      if (!errorRaw) {
        this.$dialog.message.success(this.isEdit ? '保存成功' : '创建成功')
        this.$store.commit('removeViewTags', this.$route)
        this.$store.commit('removeKeepLive', this.$route.name)
        this.$router.push({
          path: 'maritime-maintence/materials/list',
          query: {
            reload: true,
          },
        })
      }
    },
  },
  created() {
    this.getMaterials()
  },
}
</script>

<style></style>
