<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          v-if="$local.data.get('userInfo').isShipSyS"
          outlined
          dense
          class="mx-1"
          color="primary"
          @click="syncFile(0)"
        >
          <v-icon left>mdi-sync</v-icon>
          拉取文件
        </v-btn>
        <v-btn
          v-if="$local.data.get('userInfo').isShipSyS"
          outlined
          dense
          class="mx-1"
          color="primary"
          @click="syncFile(1)"
        >
          <v-icon left>mdi-sync</v-icon>
          推送文件
        </v-btn>
        <v-btn
          outlined
          dense
          class="mx-1"
          :disabled="!selected"
          color="success"
          :href="`/api/system/file/previewForExcel?fileId=${selected.id}`"
          v-permission="['文件信息管理:excel转pdf']"
        >
          <v-icon>mdi-microsoft-excel</v-icon>
          excel转pdf
        </v-btn>
        <v-btn
          outlined
          dense
          class="mx-1"
          :disabled="!selected"
          color="blue"
          :href="`/api/system/file/preview?fileId=${selected.id}`"
          v-permission="['文件信息管理:word转pdf']"
        >
          <v-icon>mdi-microsoft-word</v-icon>
          word转pdf
        </v-btn>
        <v-btn
          outlined
          tile
          class="mx-1"
          color="#00BFA5"
          :disabled="!selected"
          :href="`/api/system/file/download?fileName=${encodeURIComponent(
            selected.name,
          )}&filePath=${selected.path}`"
          v-permission="['文件信息管理:文件下载']"
        >
          <v-icon>mdi-download</v-icon>
          文件下载
        </v-btn>
        <file-upload @update="update"></file-upload>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['文件信息管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import fileUpload from './file-upload.vue'
export default {
  components: { fileUpload },
  name: 'base-file-management',
  created() {
    this.tableName = '文件管理'
    this.reqUrl = '/system/file/page'
    this.headers = [
      { text: '文件名称', value: 'name' },
      { text: '文件类型', value: 'contentType' },
      { text: '物理路径', value: 'path' },
      { text: 'url地址', value: 'url' },
      { text: '创建时间', value: 'createTime' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '文件时间',
      interval: true,
    }
  },

  data() {
    return {
      selected: false,
    }
  },

  methods: {
    async update() {
      await this.$refs.table.loadTableData()
    },
    async ExcelToPdf() {
      const { errorRaw } = await this.getAsync(`/system/file/previewForExcel`, {
        fileId: this.selected.id,
      })
      if (errorRaw) {
        return
      }
    },
    async del() {
      const { errorRaw } = await this.getAsync(`/system/file/delete`, {
        id: this.selected.id,
      })
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('删除成功！')
      await this.$refs.table.loadTableData()
    },
    async syncFile(type) {
      this.$dialog.message.info('正在同步文件，请稍后！')
      const { errorRaw } = await this.getAsync(`/system/file/syncFile`, {
        type,
      })
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('同步成功！')
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
