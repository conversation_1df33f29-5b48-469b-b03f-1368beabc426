<template>
  <div>
    <v-btn
      :loading="isSelecting"
      outlined
      tile
      class="mx-1"
      @click="onButtonClick"
    >
      <v-icon left>mdi-plus-circle</v-icon>
      上传
    </v-btn>
    <input ref="uploader" class="d-none" type="file" @change="onFileChanged" />
  </div>
</template>
<script>
export default {
  name: 'file-upload',
  data() {
    return {
      isSelecting: false,
    }
  },

  methods: {
    onButtonClick() {
      window.addEventListener('focus', () => {}, { once: true })
      this.$refs.uploader.click()
    },
    async onFileChanged(e) {
      this.isSelecting = true
      let formData = new FormData()
      formData.append('file', e.target.files[0])
      const { errorRaw } = await this.postAsync('/system/file/upload', formData)
      if (errorRaw) {
        this.isSelecting = false
        return
      }
      this.$dialog.message.success('上传成功')
      this.isSelecting = false
      this.$emit('update', true)
    },
  },

  mounted() {},
}
</script>

<style></style>
