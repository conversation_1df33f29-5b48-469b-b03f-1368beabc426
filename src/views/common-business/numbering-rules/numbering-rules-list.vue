<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }} {{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-subtitle class="pb-0 mb-1">参数设置</v-card-subtitle>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2" v-for="(h, i) in formHeaders" :key="i">
                <v-text-field
                  v-if="
                    ['currentNumber', 'serialNumberLength', 'startNo'].includes(
                      h.value,
                    )
                  "
                  v-model="numRule[h.value]"
                  type="number"
                  :label="h.text"
                  :rules="[rules.int]"
                  required
                ></v-text-field>
                <v-text-field
                  v-else-if="h.value == 'remark'"
                  v-model="numRule[h.value]"
                  :label="h.text"
                ></v-text-field>
                <v-select
                  v-else-if="h.value == 'isResetSerial'"
                  :items="[
                    { text: '不重置', value: 0 },
                    { text: '按年重置', value: 1 },
                    { text: '按年月重置', value: 2 },
                    { text: '按年月日重置', value: 3 },
                  ]"
                  v-model="numRule[h.value]"
                  :label="h.text"
                ></v-select>
                <v-switch
                  v-else-if="
                    [
                      'isEnabled',
                      'isDistinguishedShip',
                      'isSerialEnabled',
                    ].includes(h.value)
                  "
                  v-model="numRule[h.value]"
                  :label="h.text"
                ></v-switch>
                <v-text-field
                  v-else
                  v-model="numRule[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['编号规则列表:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-subtitle class="pb-0 mb-1">生成测试</v-card-subtitle>
      <v-card-text>
        <v-btn @click="generateCode">生成规则</v-btn>
        {{ exampleCode }}
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editNumRule"
      v-permission="['编号规则列表:修改']"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <v-text-field
            v-model="searchObj.ruleName"
            label="规则名称"
            outlined
            dense
            append-icon="mdi-magnify"
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="initRules"
          v-permission="['编号规则列表:全船初始化']"
        >
          <v-icon left>mdi-play</v-icon>
          全船初始化
        </v-btn>
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editNumRule"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn> -->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delNumRule"
          v-permission="['编号规则列表:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.isDistinguishedShip`]="{ item }">
        {{ item.isDistinguishedShip ? '是' : '否' }}
      </template>
      <template v-slot:[`item.isEnabled`]="{ item }">
        {{ item.isEnabled ? '是' : '否' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// createTime	创建时间	string
// currentNumber	当前流水号长度	integer
// handler	操作人/经办人	string
// isDistinguishedShip	是否分船	boolean
// isEnabled	是否启用	boolean
// isResetSerial	流水号是否重置	integer
// isSerialEnabled	是否启用流水号	boolean
// matchingString	匹配串	string
// remark	备注	string
// ruleCode	规则编号	string
// ruleName	规则名称	string
// serialNumberLength	流水号长度	integer
// serialNumberingRelus	流水号规则	string
// startNo	起始编号	integer
export default {
  name: 'numbering-rules-list',
  created() {
    this.tableName = '编码规则'
    this.reqUrl = '/business/common/ship/numberingRules/page'
    this.headers = [
      { text: '规则名称', value: 'ruleName' },
      { text: '规则编号', value: 'ruleCode' },
      { text: '流水号规则', value: 'serialNumberingRelus' },
      { text: '匹配串', value: 'matchingString' },
      { text: '分船', value: 'isDistinguishedShip' },
      { text: '启用', value: 'isEnabled' },
      { text: '流水号长度', value: 'serialNumberLength' },
      { text: '创建时间', value: 'createTime' },
      { text: '备注', value: 'remark' },
    ]
    this.formHeaders = [
      { text: '规则名称', value: 'ruleName' },
      { text: '规则编号', value: 'ruleCode' },
      { text: '当前流水号长度', value: 'currentNumber' },
      { text: '流水号规则', value: 'serialNumberingRelus' },
      { text: '起始编号', value: 'startNo' },
      { text: '匹配串', value: 'matchingString' },
      { text: '分船', value: 'isDistinguishedShip' },
      { text: '启用', value: 'isEnabled' },
      { text: '流水号是否重置', value: 'isResetSerial' },
      { text: '流水号长度', value: 'serialNumberLength' },
      { text: '备注', value: 'remark' },
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: {},
      numRule: {},
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        int: (v) => /^[0-9]*$/.test(v) || '请输入整数',
      },
      isEdit: false,
      loading: false,
      formShow: false,
      exampleCode: '',
    }
  },

  methods: {
    async delNumRule() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/common/ship/numberingRules/deleteBatch',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editNumRule() {
      this.numRule = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/common/ship/numberingRules/update'
        : '/business/common/ship/numberingRules/save'
      const { errorRaw } = await this.postAsync(reqUrl, this.numRule, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.numRule = {}
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
      await this.$nextTick()
    },
    closeForm() {
      this.$refs.form.reset()
      this.numRule = {}
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },

    async generateCode() {
      const { data } = await this.getAsync(
        '/business/common/ship/numberingRules/generateCodeByruleCode',
        { 编码规则code: this.selected.ruleCode },
      )
      this.exampleCode = data
    },

    async initRules() {
      const { data } = await this.getAsync(
        '/business/common/ship/numberingRules/initRulesForAllShip',
      )
      if (!data) {
        this.$dialog.message.error(`初始化失败，请重试`)
        return
      }
      this.$dialog.message.success(`初始化成功`)
    },
  },

  mounted() {},
}
</script>

<style></style>
