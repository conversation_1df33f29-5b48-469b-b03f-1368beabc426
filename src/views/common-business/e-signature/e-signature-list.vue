<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }} {{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="3" v-if="userType == 0">
                <v-dialog-select
                  v-if="!isEdit"
                  label="用户"
                  item-text="username"
                  item-value="id"
                  v-model="formData.userId"
                  :headers="userHeaders"
                  :rules="[rules.required]"
                  req-url="/system/user/page"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field label="用户名"></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select>
                <v-text-field
                  v-else
                  dense
                  outlined
                  v-model="formData.userName"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-file-input
                  outlined
                  truncate-length="15"
                  dense
                  label="上传电子签名(大小不超过1M,建议签名格式:png/jpg/jpeg,建议签名尺寸:150*90)"
                  accept=".png,.jpg,.jpeg"
                  v-model="formData.file"
                  :rules="[rules.required]"
                ></v-file-input>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  v-permission="['电子签名列表:编辑']"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="edit"
    >
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="formShow = true"
          v-permission="['电子签名列表:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>

        <v-btn
          outlined
          tile
          color="error"
          class="mx-1"
          @click="ban"
          :disabled="!selected"
          v-permission="['电子签名列表:禁用签名']"
        >
          <v-icon left>mdi-cancel</v-icon>
          禁用签名
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="enable"
          :disabled="!selected"
          v-permission="['电子签名列表:恢复签名']"
        >
          <v-icon left>mdi-backup-restore</v-icon>
          恢复签名
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delSign"
          v-permission="['电子签名列表:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.isForbid`]="{ item }">
        <v-chip small v-if="item.isForbid === 0" color="success">正常</v-chip>
        <v-chip small v-else color="error" dark>禁用</v-chip>
      </template>
      <template v-slot:[`item.action`]="{ item }">
        <v-icon @click.stop="viewSign(item.id)">mdi-eye-outline</v-icon>
      </template>
    </v-table-searchable>
    <v-dialog max-width="450" v-model="dialog" hide-overlay>
      <v-card outlined height="300">
        <v-card-title>签名预览</v-card-title>
        <v-img
          aspect-ratio="1"
          class="grey lighten-2"
          contain
          :src="img || '/not/exact/path.png'"
          max-height="200"
        >
          <template v-slot:placeholder>
            <v-row class="fill-height ma-0" align="center" justify="center">
              <v-progress-circular
                indeterminate
                color="grey lighten-5"
              ></v-progress-circular>
            </v-row>
          </template>
        </v-img>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
export default {
  name: 'e-signature-list',
  created() {
    this.tableName = '电子签名'
    this.reqUrl = '/business/common/eSignature/page'
    this.headers = [
      { text: '用户名称', value: 'userName' },
      { text: '签名状态', value: 'isForbid' },
      { text: '上传时间', value: 'updateTime' },
      { text: '查看', value: 'action' },
    ]
    this.userHeaders = [
      { text: '用户名', value: 'username' },
      { text: '部门名称', value: 'deptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
  },

  data() {
    return {
      selected: false,
      dialog: false,
      img: null,
      formShow: false,
      isEdit: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      userType: this.$local.data.get('userInfo').userType,
    }
  },

  methods: {
    arrayBufferToBase64(buffer) {
      let binary = ''
      let bytes = new Uint8Array(buffer)
      let len = bytes.byteLength
      for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
      }
      return `data:image/png;base64,${window.btoa(binary)}`
    },
    async viewSign(id) {
      this.dialog = true
      this.img = null
      const result = await this.getArrayBuffer(
        `/business/common/eSignature/download/${id}`,
      )
      this.img = this.arrayBufferToBase64(result.data)
    },
    async ban() {
      const { errorRaw } = await this.getAsync(
        `/business/common/eSignature/disable/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('禁用成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
    async edit() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },
    async enable() {
      const { errorRaw } = await this.getAsync(
        `/business/common/eSignature/enable/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('恢复成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
    async delSign() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/common/eSignature/delete/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
    closeForm() {
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
      this.formData = {}
      this.$refs.form.resetValidation()
    },
    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? `/business/common/eSignature/update/${this.formData.id}`
        : '/business/common/eSignature/admin/upload'
      let uploadData = new FormData()
      uploadData.append('file', this.formData.file)
      if (!this.isEdit)
        uploadData.append(
          'userName',
          this.userType == 0
            ? this.formData.userName
            : this.$local.data.get('userInfo').nickName,
        )
      uploadData.append(
        'userId',
        this.userType == 0
          ? this.formData.userId
          : this.$local.data.get('userInfo').id,
      )
      const { errorRaw } = await this.postAsync(reqUrl, uploadData)
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`保存成功`)
      this.closeForm()
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
