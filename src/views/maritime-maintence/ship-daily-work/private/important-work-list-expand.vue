<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">重要工作跟踪明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-form ref="tform">
      <v-divider></v-divider>
      <v-data-table
        hide-default-footer
        disable-pagination
        dense
        :headers="headers"
        class="use-divider"
        :items="list"
      ></v-data-table>
      <v-divider></v-divider>
    </v-form>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
export default {
  name: 'important-work-list-expand',
  created() {
    this.tableName = '重要工作跟踪明细'
    this.reqUrl = '/business/shipAffairs/supplyCommon/page'
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '发生日期', value: 'happenTime' },
      { text: '跟踪事项', value: 'item' },
      { text: '进度', value: 'schedule' },
      { text: '组长批注', value: 'remark' },
      // { text: '完结', value: 'over' },
      { text: '完结日期', value: 'finishTime' },
    ]
  },
  props: {
    happenTime: String,
    item: String,
    shipCode: String,
  },
  data() {
    return {
      list: [],
      searchObj: {
        happenTime: this.happenTime,
        item: this.item,
        shipCode: this.shipCode,
      },
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/dailyWork/followingSumListByShipCode',
        {
          happenTime: this.happenTime,
          item: this.item,
          shipCode: this.shipCode,
        },
      )
      this.list = data
      console.log(data)
      console.log(this.list)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
