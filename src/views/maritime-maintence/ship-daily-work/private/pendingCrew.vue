<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-select
                  label="管理类型"
                  outlined
                  dense
                  :items="[
                    { text: '面试', value: '面试' },
                    { text: '现场培训', value: '现场培训' },
                    { text: '远程培训', value: '远程培训' },
                  ]"
                  :rules="[rules.required]"
                  v-model="formData.type"
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="formData.shipCode"
                  :rules="[rules.required]"
                  required
                  dense
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-station
                  label="职务"
                  :rules="[rules.required]"
                  outlined
                  dense
                  v-model="formData.post"
                ></v-ship-station>
              </v-col>
              <v-col cols="12" md="3">
                <v-dialog-select
                  label="选择船员"
                  dense
                  outlined
                  table-name="选择船员"
                  :headers="creHeaders"
                  :reqUrl="`/business/crew/baseInfo/salaryApply/page`"
                  itemText="name"
                  itemValue="userId"
                  :search-remain="searchRemain"
                  v-model="formData.crewId"
                  :initSelected="initSelected"
                  :rules="[rules.required]"
                  @select="setCrewName($event)"
                  :disabled="formData.crewName"
                >
                  <template #searchflieds>
                    <v-col cols="12" md="2">
                      <v-text-field
                        label="船员姓名"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.name"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-text-field
                        label="身份证号"
                        outlined
                        dense
                        clearable
                        v-model="searchRemain.idCard"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="2">
                      <v-ship-station
                        label="船员职务"
                        clearable
                        v-model="searchRemain.position"
                      ></v-ship-station>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  single-line
                  dense
                  outlined
                  label="船员姓名"
                  placeholder="搜索不到船员时候使用"
                  v-model="formData.crewName"
                  :disabled="formData.crewId"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="isEdit"
                  outlined
                  dense
                  v-model="formData.happenTime"
                  :rules="[rules.required]"
                  required
                  label="日期"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  outlined
                  label="结果/内容"
                  v-model="formData.result"
                  :rules="[rules.required]"
                  required
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import currencyHelper from '@/mixin/currencyHelper'
import vShipSelect from '@/components/v-ship-select.vue'
export default {
  components: { vShipSelect },
  mixins: [currencyHelper],
  name: 'pendingCrew',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.creHeaders = [
      { text: '船员姓名', value: 'name' },
      { text: '身份证号', value: 'idCard' },
      { text: '岗位名称', value: 'position' },
      { text: '银行卡号', value: 'rmbCard' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    shipCode: {
      type: String,
      default: '',
    },
    initSelected: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchObj2: {},
      dialog: false,
      formData: { crewName: '', crewCard: '' },
      supplier: {},
      currency: [],
      rules: {
        required: (v) => !!v || v == 0 || '必填项不能为空',
      },
      initSupply: {},
      currencyId: '',
      searchRemain: {},
    }
  },
  watch: {
    open(val) {
      this.formData = { ...this.initialData }
      this.dialog = val
      this.$refs?.form?.resetValidation()
    },
  },
  computed: {
    isEdit() {
      return !!this.initialData?.vid
    },
  },
  methods: {
    setCrewName(event) {
      // console.log(******************************)
      // console.log(event)
      // console.log(event.crewName)
      // console.log(event.crewCard)
      this.formData.crewId = event.creId
      this.formData.crewName = event.name
      this.formData.crewCard = event.idCard
      // console.log(this.formData)
    },
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.$emit('change', false)
      if (!this.isEdit)
        this.$emit('success', {
          vid: Math.floor(Math.random() * 1000 + 1),
          // supplyId:this.$route.params.id,
          ...this.formData,
          crewPost: this.formData.post,
          crewName: this.formData.crewName,
          operationType: this.isEdit ? 2 : 1,
        })
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
