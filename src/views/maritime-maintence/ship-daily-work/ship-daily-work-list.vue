<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :single-select="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :loading="loading"
          :disabled="!canDown"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="downloadExcel"
          v-permission="['机务日常工作报告:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'ship-daily-work-detail', params: { id: 'new' } }"
          v-permission="['机务日常工作报告:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!canDel"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['机务日常工作报告:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!canSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="submitApply"
          v-permission="['机务日常工作报告:审批通过']"
        >
          <v-icon left>mdi-send</v-icon>
          审批通过
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'ship-daily-work-list',
  created() {
    this.tableName = '机务日常工作报告'
    this.reqUrl = '/business/shipAffairs/dailyWork/page'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '报表日期', value: 'applyDate' },
      { text: '机务主管', value: 'managerName' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '报表日期',
      value: 'applyDate',
    }
    this.pushParams = { name: 'ship-daily-work-detail' }
  },

  data() {
    return {
      selected: [],
      searchObj: {
        isMe: true,
      },
      loading: false,
    }
  },
  computed: {
    canDel() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            (item.status == 1 || item.status == 4) &&
            item.managerId == this.$local.data.get('userInfo').id,
        )
      )
    },
    canSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) => item?.auditParams?.taskId && item.status == 2,
        )
      )
    },
    canDown() {
      return (
        this.selected.length > 0 &&
        this.selected.every((item) => item.status == 3)
      )
    },
  },
  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除吗？'))) return
      // const { errorRaw } = await this.getAsync(
      //   '/business/shipAffairs/dailyWork/dailyWorkDelete',
      //   { workId: this.selected.id },
      //   false,
      // )
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/dailyWork/deleteBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async submitApply() {
      if (!(await this.$dialog.msgbox.confirm('确定审批通过所选记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/batchCompleteTaskAndCommentAndSetVar',
        this.selected.map((item) => ({
          adopt: true,
          comment: '',
          params: {},
          taskId: item.auditParams.taskId,
        })),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`提交成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
    async downloadExcel() {
      const idss = this.selected.map((item) => item.id)
      idss.forEach((key) => {
        this.loading = true
        const { errorRaw } = this.getBlobDownload(
          '/business/shipAffairs/dailyWork/createDailyWorkExcels',
          { workId: key },
        )
        this.loading = false
        if (errorRaw) {
          this.$dialog.message.error(errorRaw)
        }
      })
    },
  },

  mounted() {},
}
</script>

<style></style>
