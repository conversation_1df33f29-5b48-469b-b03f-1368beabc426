<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        重要工作跟踪汇总
        <v-spacer></v-spacer>
        <v-btn outlined tile color="info" class="mx-1" @click="downloadExcel">
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-row>
          <v-col cols="12" sm="6" md="2">
            <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
          </v-col>
          <v-col cols="12" sm="6" md="4">
            <v-menu
              v-model="datesMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  ref="dates"
                  :value="dateRangeText"
                  label="发生日期"
                  append-icon="mdi-calendar"
                  outlined
                  dense
                  readonly
                  clearable
                  @click:clear="dates = []"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <vc-date-picker
                v-model="dates"
                mode="date"
                is-range
              ></vc-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-text-field
              label="跟踪事项"
              v-model="searchObj.item"
              outlined
              dense
              clearable
            ></v-text-field>
          </v-col>
          <v-col cols="12" sm="6" md="4">
            <v-menu
              v-model="datesMenu2"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  ref="dates2"
                  :value="dateRangeText2"
                  label="完结日期"
                  append-icon="mdi-calendar"
                  outlined
                  dense
                  readonly
                  clearable
                  @click:clear="dates2 = []"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <vc-date-picker
                v-model="dates2"
                mode="date"
                is-range
              ></vc-date-picker>
            </v-menu>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-data-table
        :headers="headers"
        :items="dataFollowing"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
        :show-expand="true"
      >
        <template v-slot:expanded-item="{ headers, item }">
          <td :colspan="headers.length">
            <importantWorkListExpandVue
              :item="item.item"
              :shipCode="item.shipCode"
              :happenTime="item.happenTime"
            ></importantWorkListExpandVue>
          </td>
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>
<script>
import importantWorkListExpandVue from './private/important-work-list-expand.vue'
export default {
  components: { importantWorkListExpandVue },
  name: 'important-work-list',
  created() {
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '发生日期', value: 'happenTime' },
      { text: '跟踪事项', value: 'item' },
      { text: '进度', value: 'schedule' },
      { text: '组长批注', value: 'remark' },
      // { text: '完结', value: 'over' },
      { text: '完结日期', value: 'finishTime' },
    ]
  },

  computed: {
    dateRangeText() {
      return this.dates?.start && this.dates?.end
        ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
        : ''
    },
    dateRangeText2() {
      return this.dates2?.start && this.dates2?.end
        ? `${this.dates2.start.toLocaleDateString()} 至 ${this.dates2?.end.toLocaleDateString()}`
        : ''
    },
  },

  data() {
    return {
      selected: false,
      searchObj: {
        shipCode: '',
        item: '',
      },
      dataFollowing: [],
      dates: [],
      datesMenu: false,
      dates2: [],
      datesMenu2: false,
      loading: false,
    }
  },

  watch: {
    dates: {
      handler: function () {
        this.getFollowing()
      },
    },
    dates2: {
      handler: function () {
        this.getFollowing()
      },
    },
    // searchObj: {
    //   handler: function () {
    //     this.getFollowing()
    //   },
    // },
    'searchObj.shipCode'(val) {
      console.log(val)
      // if (val) {
      this.getFollowing()
      // }
    },
    'searchObj.item'(val) {
      console.log(val)
      // if (val) {
      this.getFollowing()
      // }
    },
  },

  methods: {
    async getFollowing() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/dailyWork/followingSumList',
        {
          happenTimeFrom: this.dates?.start?.toISOString()?.split('T')[0],
          happenTimeTo: this.dates?.end?.toISOString()?.split('T')[0],
          shipCode: this.searchObj.shipCode,
          item: this.searchObj.item,
          finishTimeFrom: this.dates2?.start?.toISOString()?.split('T')[0],
          finishTimeTo: this.dates2?.end?.toISOString()?.split('T')[0],
        },
      )
      this.dataFollowing = data
    },
    async downloadExcel() {
      this.loading = true
      let params = {
        happenTimeFrom: this.dates?.start?.toISOString()?.split('T')[0],
        happenTimeTo: this.dates?.end?.toISOString()?.split('T')[0],
        shipCode: this.searchObj.shipCode,
        item: this.searchObj.item,
        finishTimeFrom: this.dates2?.start?.toISOString()?.split('T')[0],
        finishTimeTo: this.dates2?.end?.toISOString()?.split('T')[0],
      }
      await this.getBlobDownload(
        '/business/shipAffairs/dailyWork/excelExport',
        params,
        // 时间戳后四位
        `重要工作跟踪汇总-${new Date().getTime().toString().slice(-4)}.xlsx`,
      )
      this.loading = false
    },
  },

  mounted() {
    // 获取当前时间
    const now = new Date()
    this.dates.end = now // 你可以根据需要格式化日期

    // 获取半年前的时间
    const halfYear = 6 * 30 * 24 * 60 * 60 * 1000 // 6个月转换为毫秒
    const halfYearAgoDate = new Date(now - halfYear)
    this.dates.start = halfYearAgoDate // 你可以根据需要格式化日期
    // this.dateRangeText =
    //   this.dates?.start && this.dates?.end
    //     ? `${this.dates.start.toLocaleDateString()} 至 ${this.dates?.end.toLocaleDateString()}`
    //     : ''
    this.getFollowing()
  },
}
</script>

<style></style>
