<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['机务日常工作报告:编辑']"
      :title="`机务日常工作报告-${isEdit ? detail.managerName : '新增'}`"
      :tooltip="isEdit ? detail.managerName : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
      @save="save"
      @submit="submit"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit-only-yes
              ref="audit"
              :auditParams="detail.auditParams"
            ></v-audit-only-yes>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="机务主管"
                  v-model="detail.managerName"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  disabled
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="isEdit"
                  outlined
                  dense
                  v-model="detail.applyDate"
                  :rules="[rules.required]"
                  label="报表日期"
                ></vs-date-picker>
                <!-- :disabled="applyDateDisable" -->
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template v-if="!isEdit2" #安全工作检查表按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="allZC"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          一键正常
        </v-btn>
        <v-btn
          :disabled="!selectedChecks"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCheck"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
          <!-- v-permission="['安全工作检查表:删除']" -->
        </v-btn>
      </template>
      <template #安全工作检查表>
        <v-form ref="form2">
          <v-card-text>
            <v-table-list
              :headers="!isEdit2 ? workCheckHeaders : workCheckHeaders2"
              :items="workCheckModifyList"
              :show-select="!isEdit"
              v-model="selectedChecks"
              item-key="shipCode"
            >
              <template v-if="!isEdit2" v-slot:[`item.shipCode`]="{ item }">
                <v-ship-select
                  v-model="item.shipCode"
                  readonly
                  disabled
                ></v-ship-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.mainEngine`]="{ item }">
                <v-select
                  label="主机"
                  outlined
                  dense
                  :items="[
                    { text: '正常', value: '正常' },
                    { text: '不正常', value: '不正常' },
                  ]"
                  v-model="item.mainEngine"
                  :rules="[rules.required]"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.lastName`]="{ item }">
                <v-select
                  label="副机"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '正常', value: '正常' },
                    { text: '不正常', value: '不正常' },
                  ]"
                  v-model="item.lastName"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.ervoMotor`]="{ item }">
                <v-select
                  label="舵机"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '正常', value: '正常' },
                    { text: '不正常', value: '不正常' },
                  ]"
                  v-model="item.ervoMotor"
                  required
                ></v-select>
              </template>
              <template
                v-if="!isEdit2"
                v-slot:[`item.anchorMachine`]="{ item }"
              >
                <v-select
                  label="锚机"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '正常', value: '正常' },
                    { text: '不正常', value: '不正常' },
                  ]"
                  v-model="item.anchorMachine"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.boiler`]="{ item }">
                <v-select
                  label="锅炉"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '正常', value: '正常' },
                    { text: '不正常', value: '不正常' },
                  ]"
                  v-model="item.boiler"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.abnormal`]="{ item }">
                <v-select
                  label="异常报警"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '有', value: '有' },
                    { text: '无', value: '无' },
                  ]"
                  v-model="item.abnormal"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.locking`]="{ item }">
                <v-select
                  label="报警锁闭"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '有', value: '有' },
                    { text: '无', value: '无' },
                  ]"
                  v-model="item.locking"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.porting`]="{ item }">
                <v-select
                  label="进出港靠离泊"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '-', value: '-' },
                    { text: '√', value: '√' },
                  ]"
                  v-model="item.porting"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.berthing`]="{ item }">
                <!-- <v-select
                  label="机舱值班监控"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '-', value: '-' },
                    { text: '√', value: '√' },
                  ]"
                  v-model="item.berthing"
                  required
                ></v-select> -->
                <v-text-field
                  outlined
                  dense
                  v-model="item.berthing"
                  label="机舱值班监控"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </template>
              <template
                v-if="!isEdit2"
                v-slot:[`item.narrowWaterway`]="{ item }"
              >
                <v-select
                  label="狭水道"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '-', value: '-' },
                    { text: '√', value: '√' },
                  ]"
                  v-model="item.narrowWaterway"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.badWeather`]="{ item }">
                <v-select
                  label="恶劣天气"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '-', value: '-' },
                    { text: '√', value: '√' },
                  ]"
                  v-model="item.badWeather"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.fuel`]="{ item }">
                <v-select
                  label="燃油"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '正常', value: '正常' },
                    { text: '不正常', value: '不正常' },
                  ]"
                  v-model="item.fuel"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.oil`]="{ item }">
                <v-select
                  label="气缸油"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '正常', value: '正常' },
                    { text: '不正常', value: '不正常' },
                  ]"
                  v-model="item.oil"
                  required
                ></v-select>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.remark`]="{ item }">
                <!-- <v-text-field
                label="备注"
                dense
                outlined
                v-model="item.remark"
              ></v-text-field> -->
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      @click="editRemark(item)"
                      v-bind="attrs"
                      v-on="on"
                      v-model="item.remark"
                      label="备注"
                      single-line
                      dense
                      outlined
                    ></v-text-field>
                  </template>
                  <span>{{ item.remark }}</span>
                </v-tooltip>
              </template>
              <template v-if="!isEdit2" v-slot:[`item.type`]="{ item }">
                <v-select
                  label="了解方式"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :items="[
                    { text: '平台', value: '平台' },
                    { text: '微信', value: '微信' },
                    { text: '邮件', value: '邮件' },
                    { text: '电话', value: '电话' },
                  ]"
                  v-model="item.type"
                  required
                ></v-select>
              </template>
            </v-table-list>
          </v-card-text>
        </v-form>
      </template>
      <template v-if="!isEdit2" #船员工作按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCrew"
          v-permission="['船员工作:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedCrew"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCrew"
          v-permission="['船员工作:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #船员工作>
        <v-card-text>
          <!-- 草稿 -->
          <v-table-list
            v-if="!isEdit2"
            :headers="!isEdit2 ? workCrewHeaders : workCrewHeaders2"
            :items="workCrewModifyList"
            v-model="selectedCrew"
            item-key="vid"
            :show-select="!isEdit"
          >
            <template v-if="!isEdit2" v-slot:[`item.shipCode`]="{ item }">
              <v-ship-select
                v-model="item.shipCode"
                readonly
                disabled
              ></v-ship-select>
            </template>
            <template v-slot:[`item.type`]="{ item }">
              <v-select
                label="工作类型"
                outlined
                dense
                :items="[
                  { text: '面试', value: '面试' },
                  { text: '现场培训', value: '现场培训' },
                  { text: '远程培训', value: '远程培训' },
                ]"
                v-model="item.type"
                required
              ></v-select>
            </template>
            <template v-slot:[`item.crewPost`]="{ item }">
              <v-ship-station
                label="职务"
                outlined
                dense
                v-model="item.crewPost"
              ></v-ship-station>
            </template>
            <!-- <template v-slot:[`item.crewName`]="{ item }">
              <v-text-field
                label="姓名"
                outlined
                dense
                clearable
                v-model="item.crewName"
              ></v-text-field>
            </template> -->
            <template v-slot:[`item.happenTime`]="{ item }">
              <vs-date-picker
                outlined
                dense
                v-model="item.happenTime"
                label="日期"
              ></vs-date-picker>
            </template>
            <template v-slot:[`item.result`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    @click="editResult(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.result"
                    label="结果/内容"
                    single-line
                    dense
                    outlined
                  ></v-text-field>
                </template>
                <span>{{ item.result }}</span>
              </v-tooltip>
            </template>
          </v-table-list>
          <!-- 审批中，已完成 -->
          <v-table-list
            v-if="isEdit2"
            :headers="!isEdit2 ? workCrewHeaders : workCrewHeaders2"
            :items="workCrewModifyList"
            v-model="selectedCrew"
            item-key="vid"
            :show-select="!isEdit"
          >
            <template v-if="!isEdit2" v-slot:[`item.shipCode`]="{ item }">
              <v-ship-select
                v-model="item.shipCode"
                readonly
                disabled
              ></v-ship-select>
            </template>
            <template v-slot:[`item.result`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on }">
                  <span v-on="on">{{ item.result.substring(0, 25) }}...</span>
                </template>
                <span>{{ item.result }}</span>
              </v-tooltip>
            </template>
          </v-table-list>
        </v-card-text>
      </template>
      <template v-if="!isEdit2" #重要工作跟踪按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createFollowing"
          v-permission="['重要工作跟踪:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedFollowing"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delFollowing"
          v-permission="['重要工作跟踪:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #重要工作跟踪>
        <v-card-text>
          <!-- 草稿 -->
          <v-table-list
            v-if="!isEdit2"
            :headers="!isEdit2 ? workFollowingHeaders : workFollowingHeaders2"
            :items="workFollowingModifyList"
            v-model="selectedFollowing"
            :show-select="!isEdit"
            item-key="vid"
          >
            <template v-if="!isEdit2" v-slot:[`item.shipCode`]="{ item }">
              <v-ship-select
                v-model="item.shipCode"
                readonly
                disabled
              ></v-ship-select>
            </template>
            <!-- <template v-if="!isEdit2" v-slot:[`item.item`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-if="!isEdit2"
                    @click="editItem(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.item"
                    label="跟踪事项"
                    single-line
                    dense
                    outlined
                  ></v-text-field>
                </template>
                <span>{{ item.item }}</span>
              </v-tooltip>
            </template> -->
            <!-- <template v-if="isEdit2" v-slot:[`item.item`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on }">
                  <span v-on="on">{{ item.item.substring(0, 25) }}...</span>
                </template>
                <span>{{ item.item }}</span>
              </v-tooltip>
            </template> -->

            <!-- <template v-slot:[`item.schedule`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on }">
                  <span v-on="on">{{ item.schedule.substring(0, 25) }}...</span>
                </template>
                <span>{{ item.schedule }}</span>
              </v-tooltip>
            </template> -->
            <template v-if="!isEdit2" v-slot:[`item.schedule`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-if="!isEdit2"
                    @click="editSchedule(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.schedule"
                    label="进度"
                    single-line
                    dense
                    outlined
                  ></v-text-field>
                </template>
                <span>{{ item.schedule }}</span>
              </v-tooltip>
            </template>
            <template v-slot:[`item.finishTime`]="{ item }">
              <vs-date-picker
                :readonly="isEdit2"
                outlined
                dense
                v-model="item.finishTime"
                label="完结日期"
              ></vs-date-picker>
            </template>
          </v-table-list>
          <!-- 审批中，已完成 -->
          <v-table-list
            v-if="isEdit2"
            :headers="!isEdit2 ? workFollowingHeaders : workFollowingHeaders2"
            :items="workFollowingModifyList"
            v-model="selectedFollowing"
            :show-select="!isEdit"
            item-key="vid"
          >
            <template v-if="!isEdit2" v-slot:[`item.shipCode`]="{ item }">
              <v-ship-select v-model="item.shipCode" readonly></v-ship-select>
            </template>
            <template v-if="isEdit2" v-slot:[`item.item`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on }">
                  <span v-on="on">{{ item.item.substring(0, 25) }}...</span>
                </template>
                <span>{{ item.item }}</span>
              </v-tooltip>
            </template>

            <template v-slot:[`item.schedule`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on }">
                  <span v-on="on">{{ item.schedule.substring(0, 25) }}...</span>
                </template>
                <span>{{ item.schedule }}</span>
              </v-tooltip>
            </template>
            <template
              v-if="
                detail.businessStatus == '管船组长' ||
                detail.businessStatus == '山东航运负责人'
              "
              v-slot:[`item.remark`]="{ item }"
            >
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-if="
                      detail.businessStatus == '管船组长' ||
                      detail.businessStatus == '山东航运负责人'
                    "
                    @click="editRemarkP(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.remark"
                    label="批注"
                    single-line
                    dense
                    outlined
                  ></v-text-field>
                </template>
                <span>{{ item.remark }}</span>
              </v-tooltip>
            </template>
          </v-table-list>
        </v-card-text>
      </template>
      <v-card-text>
        <v-attach-list
          :attachments="detail.attachmentRecords"
          @change="changeAttachment"
        ></v-attach-list>
      </v-card-text>
    </v-detail-view>
    <pending-crew
      v-model="dialog"
      @success="success"
      :initial-data="initialData"
    ></pending-crew>
    <pending-following
      v-model="dialogFollowing"
      @success="successFollowing"
      :initial-data="initialDataFollowing"
    ></pending-following>
    <v-dialog v-model="dialog1" max-width="600">
      <template v-slot:default="dialog1">
        <v-card style="height: 320px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <v-dialog v-model="dialog2" max-width="600">
      <template v-slot:default="dialog2">
        <v-card style="height: 320px">
          <v-card-title>
            编辑跟踪事项
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveItem"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog2.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form13">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="跟踪事项"
                    v-model="editItemDetails.item"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <v-dialog v-model="dialog3" max-width="600">
      <template v-slot:default="dialog3">
        <v-card style="height: 320px">
          <v-card-title>
            编辑进度
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveSchedule"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog3.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form14">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="进度"
                    v-model="editScheduleDetails.schedule"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <v-dialog v-model="dialog4" max-width="600">
      <template v-slot:default="dialog4">
        <v-card style="height: 320px">
          <v-card-title>
            编辑结果/内容
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveResult"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog4.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form14">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="结果/内容"
                    v-model="editResultDetails.result"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <v-dialog v-model="dialog1P" max-width="600">
      <template v-slot:default="dialog1P">
        <v-card style="height: 320px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemarkP"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1P.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkPDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import pendingCrew from './private/pendingCrew.vue'
import pendingFollowing from './private/pendingFollowing.vue'
export default {
  components: { pendingCrew, pendingFollowing },
  name: 'ship-daily-work-detail',
  created() {
    this.backRouteName = 'ship-daily-work-list'
    this.subtitles = ['基本信息', '安全工作检查表', '船员工作', '重要工作跟踪']
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    isEdit2() {
      return (
        this.$route.params.id !== 'new' &&
        this.detail.businessStatus !== '未提交' &&
        this.detail.status !== '4'
      )
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
  },
  data() {
    return {
      detail: {
        attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || v === false || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
      },
      workCheckHeaders: [
        { text: '船名', value: 'shipCode', width: 50 },
        { text: '主机', value: 'mainEngine', width: 50 },
        { text: '副机', value: 'lastName', width: 50 },
        { text: '舵机', value: 'ervoMotor', width: 50 },
        { text: '锚机', value: 'anchorMachine', width: 50 },
        { text: '锅炉', value: 'boiler', width: 50 },
        { text: '异常报警', value: 'abnormal', width: 50 },
        { text: '报警锁闭', value: 'locking', width: 50 },
        // { text: '进出港', value: 'porting', width: 50 },
        // { text: '靠离泊', value: 'berthing', width: 50 },
        { text: '进出港靠离泊', value: 'porting', width: 50 },
        { text: '机舱值班监控', value: 'berthing', width: 50 },
        { text: '狭水道', value: 'narrowWaterway', width: 50 },
        { text: '恶劣天气', value: 'badWeather', width: 50 },
        { text: '燃油', value: 'fuel', width: 50 },
        { text: '气缸油', value: 'oil', width: 50 },
        { text: '备注', value: 'remark' },
        { text: '了解方式', value: 'type', width: 50 },
      ],
      workCrewHeaders: [
        { text: '船名', value: 'shipCode' },
        { text: '工作类型', value: 'type' },
        { text: '职务', value: 'crewPost' },
        { text: '姓名', value: 'crewName' },
        { text: '日期', value: 'happenTime' },
        { text: '结果/内容', value: 'result' },
      ],
      workFollowingHeaders: [
        { text: '船名', value: 'shipCode' },
        { text: '发生日期', value: 'happenTime' },
        { text: '跟踪事项', value: 'item' },
        { text: '进度', value: 'schedule' },
        { text: '完结日期', value: 'finishTime' },
      ],
      workCheckHeaders2: [
        { text: '船名', value: 'shipName' },
        { text: '主机', value: 'mainEngine' },
        { text: '副机', value: 'lastName' },
        { text: '舵机', value: 'ervoMotor' },
        { text: '锚机', value: 'anchorMachine' },
        { text: '锅炉', value: 'boiler' },
        { text: '异常报警', value: 'abnormal' },
        { text: '报警锁闭', value: 'locking' },
        { text: '进出港靠离泊', value: 'porting' },
        { text: '机舱值班监控', value: 'berthing' },
        { text: '狭水道', value: 'narrowWaterway' },
        { text: '恶劣天气', value: 'badWeather' },
        { text: '燃油', value: 'fuel' },
        { text: '气缸油', value: 'oil' },
        { text: '备注', value: 'remark' },
        { text: '了解方式', value: 'type' },
      ],
      workCrewHeaders2: [
        { text: '船名', value: 'shipName' },
        { text: '工作类型', value: 'type' },
        { text: '职务', value: 'crewPost' },
        { text: '姓名', value: 'crewName' },
        { text: '日期', value: 'happenTime' },
        { text: '结果/内容', value: 'result' },
      ],
      workFollowingHeaders2: [
        { text: '船名', value: 'shipName' },
        { text: '发生日期', value: 'happenTime' },
        { text: '跟踪事项', value: 'item' },
        { text: '进度', value: 'schedule' },
        { text: '完成日期', value: 'finishTime' },
        { text: '组长批注', value: 'remark' },
      ],
      workCheckModifyList: [],
      workCrewModifyList: [],
      selectedCrew: false,
      dialog: false,
      initialData: {},
      workFollowingModifyList: [],
      selectedFollowing: false,
      dialogFollowing: false,
      initialDataFollowing: {},
      dialog1: false,
      editRemarkDetails: {},
      applyDateDisable: true,
      editItemDetails: {},
      dialog2: false,
      editScheduleDetails: {},
      dialog3: false,
      editResultDetails: {},
      dialog4: false,
      selectedChecks: false,
      editRemarkPDetails: {},
      dialog1P: false,
    }
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    editRemark(item) {
      // console.log(item)
      this.editRemarkDetails = item
      this.dialog1 = true
    },
    saveRemark() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1 = false
    },
    editRemarkP(item) {
      // console.log(item)
      this.editRemarkPDetails = item
      this.dialog1P = true
    },
    saveRemarkP() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1P = false
    },
    editItem(item) {
      // console.log(item)
      this.editItemDetails = item
      this.dialog2 = true
    },
    saveItem() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog2 = false
    },
    editSchedule(item) {
      // console.log(item)
      this.editScheduleDetails = item
      this.dialog3 = true
    },
    saveSchedule() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog3 = false
    },
    editResult(item) {
      this.editResultDetails = item
      this.dialog4 = true
    },
    saveResult() {
      this.dialog4 = false
    },
    createCrew() {
      this.initialData = {}
      this.dialog = true
    },
    delCrew() {
      this.workCrewModifyList = this.workCrewModifyList.filter(
        (s) => !(s.vid === this.selectedCrew.vid),
      )
      this.selectedCrew = false
    },
    delCheck() {
      this.workCheckModifyList = this.workCheckModifyList.filter(
        (s) => !(s.shipCode === this.selectedChecks.shipCode),
      )
      this.selectedChecks = false
    },
    success(newCrew) {
      // console.log(newSup)
      this.workCrewModifyList.push(newCrew)
    },
    createFollowing() {
      this.initialDataFollowing = {}
      this.dialogFollowing = true
    },
    delFollowing() {
      this.workFollowingModifyList = this.workFollowingModifyList.filter(
        (s) => !(s.vid === this.selectedFollowing.vid),
      )
      this.selectedFollowing = false
    },
    successFollowing(newFollowing) {
      // console.log(newSup)
      this.workFollowingModifyList.push(newFollowing)
    },
    getWorkCheckModifyList() {
      const ids = this.workCheckModifyList.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.dailyWorkCheckOutputDTOS
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.workCheckModifyList.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },
    getWorkCrewModifyList() {
      const ids = this.workCrewModifyList.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.dailyWorkCrewOutputDTOS
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.workCrewModifyList.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },
    getWorkFollowingModifyList() {
      const ids = this.workFollowingModifyList.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.dailyWorkFollowingOutputDTOS
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.workFollowingModifyList.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate() || !this.$refs.form2.validate()) {
        return
      }
      const detailList1 = this.getWorkCheckModifyList()
      const detailList2 = this.getWorkCrewModifyList()
      const detailList3 = this.getWorkFollowingModifyList()
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/dailyWork/dailyWorkSaveOrUpdate',
        {
          ...this.detail,
          workCheckModifyList: [...detailList1],
          workCrewModifyList: [...detailList2],
          workFollowingModifyList: [...detailList3],
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!this.$refs.form.validate() || !this.$refs.form2.validate()) {
        return
      }
      const data = await this.save(goBack, true)
      if (!data) return
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/dailyWork/dailyWorkSubmit',
          { workId: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    allZC() {
      // this.workCheckModifyList = this.workCheckModifyList.forEach((item) => {
      //   item.mainEngine = '正常'
      //   item.lastName = '正常'
      //   item.ervoMotor = '正常'
      //   item.anchorMachine = '正常'
      //   item.boiler = '正常'
      //   item.fuel = '正常'
      //   item.oil = '正常'

      //   // item.mainEngine = '正常'
      //   // item.mainEngine = '正常'
      //   // item.mainEngine = '正常'
      //   // item.mainEngine = '正常'
      //   // item.mainEngine = '正常'
      // })
      this.workCheckModifyList = this.workCheckModifyList.map((s) => {
        return {
          ...s,
          mainEngine: '正常',
          lastName: '正常',
          ervoMotor: '正常',
          anchorMachine: '正常',
          boiler: '正常',
          fuel: '正常',
          oil: '正常',
        }
      })
    },
    async loadShipManager() {
      this.detail.managerName = this.$local.data.get('userInfo').nickName
      const shipCodes = this.$local.data.get('userInfo').shipCodes.split(',')
      // const shipCodes = 'STHM,STHN,STHK'.split(',')
      for (const key of shipCodes) {
        const { data } = await this.getAsync(
          '/business/common/ship/getShipBaseByCode',
          {
            code: key,
          },
        )
        if (data.shipStatus == 0) {
          const item = {
            shipCode: key,
            porting: '-',
            berthing: '-',
            narrowWaterway: '-',
            badWeather: '-',
            locking: '无',
            abnormal: '无',
          }
          this.workCheckModifyList.push(item)
        }
      }
      // shipCodes.forEach((key) => {
      //   // 判断船舶是否为运营状态
      //   const { data } = this.getAsync(
      //     '/business/common/ship/getShipBaseByCode',
      //     { code: key },
      //   )
      //   if (data.shipStatus == 0) {
      //     const item = {
      //       shipCode: key,
      //       porting: '-',
      //       berthing: '-',
      //       narrowWaterway: '-',
      //       badWeather: '-',
      //       locking: '无',
      //       abnormal: '无',
      //     }
      //     this.workCheckModifyList.push(item)
      //   }
      // })
      // 获取上一单未完成的重要工作跟踪,自动计算下一天报表日期；首单不影响，后续自动出
      const { data } = await this.getAsync(
        '/business/shipAffairs/dailyWork/dailyWorkDetailByManagerId',
        { managerId: this.$local.data.get('userInfo').id },
      )
      if (data) {
        const chushihua = data.chushihua
        if (chushihua == 'yes') {
          this.applyDateDisable = false
        } else {
          this.detail.applyDate = data.applyDate
          this.workFollowingModifyList = data.dailyWorkFollowingOutputDTOS
        }
      }

      // this.workCheckModifyList = []
    },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/dailyWork/dailyWorkDetailById',
        { workId: this.$route.params.id },
      )
      this.detail = { ...data }
      // this.workCheckModifyList = [...data.dailyWorkCheckOutputDTOS]
      // this.workCrewModifyList = [...data.dailyWorkCrewOutputDTOS]
      // this.workFollowingModifyList = [...data.dailyWorkFollowingOutputDTOS]
      this.workCheckModifyList = data.dailyWorkCheckOutputDTOS.map((s) => {
        return { ...s, operationType: 0 }
      })
      this.workCrewModifyList = data.dailyWorkCrewOutputDTOS.map((s) => {
        return { ...s, operationType: 0 }
      })
      this.workFollowingModifyList = data.dailyWorkFollowingOutputDTOS.map(
        (s) => {
          return { ...s, operationType: 0 }
        },
      )
    },
  },

  mounted() {
    if (!this.isEdit) {
      this.loadShipManager()
    } else {
      this.loadDetail()
    }
  },
}
</script>

<style></style>
