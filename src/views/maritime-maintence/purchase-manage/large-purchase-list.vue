<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :push-params="pushParams"
      :single-select="false"
      use-ship
      fuzzy-label="供应项目/预算说明/订单号"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchRemain.dataSource"
            label="数据来源"
            outlined
            dense
            clearable
            :items="dataSources"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchRemainStatus"
            :items="statusMap"
            label="审批状态"
            outlined
            clearable
            dense
          ></v-select>
          <!-- <v-select
            v-model="searchRemain.status"
            :items="statusMap"
            label="审批状态"
            outlined
            clearable
            dense
          ></v-select> -->
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <!-- <v-select
            v-model="searchRemain.businessStatus"
            :items="businessStatusMap"
            label="业务状态"
            outlined
            clearable
            dense
          ></v-select> -->
          <v-autocomplete
            label="业务状态"
            v-model="searchRemain.businessStatus"
            dense
            outlined
            multiple
            :items="businessStatusMap"
          ></v-autocomplete>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchRemain.businessStatus"
            :items="businessStatusMap"
            label="费用类型"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchRemain.businessStatus"
            :items="businessStatusMap"
            label="费用科目"
            outlined
            clearable
            dense
          ></v-select>
        </v-col> -->
        <v-col cols="12" sm="6" md="2">
          <v-dict-select
            clearable
            v-model="searchRemain.subjectType"
            label="费用类型"
            dense
            outlined
            dict-type="cost_subject_type"
          ></v-dict-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-autocomplete
            clearable
            item-text="subjectName"
            item-value="id"
            v-model="searchRemain.costSubjectId"
            label="费用科目"
            dense
            outlined
            :items="costSubjects"
            :disabled="!searchRemain.subjectType"
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-supply-select-list
            v-model="searchRemain.supplierId"
            label="供应商"
            outlined
            clearable
            dense
          ></v-supply-select-list>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <!-- <v-handler
            clearable
            label="实际申请人"
            v-model="searchRemain.applicantId"
            :use-current="isApplicantId"
            :initUser="isApplicantId"
          ></v-handler> -->
          <v-handler
            clearable
            label="实际申请人"
            v-model="searchRemain.applicantId"
            :use-current="false"
            :initUser="isApplicantId"
          ></v-handler>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <!-- <v-handler
            clearable
            label="实际申请人"
            v-model="searchRemain.applicantId"
            :use-current="isApplicantId"
            :initUser="isApplicantId"
          ></v-handler> -->
          <v-handler
            clearable
            label="录单人"
            v-model="searchRemain.applyUser"
            :use-current="false"
          ></v-handler>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-menu
            v-model="datesMenu2"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                ref="dates2"
                :value="dateRangeText2"
                :label="'申请日期'"
                append-icon="mdi-calendar"
                outlined
                dense
                readonly
                clearable
                @click:clear="dates2 = []"
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <vc-date-picker
              v-model="dates2"
              mode="date"
              is-range
            ></vc-date-picker>
          </v-menu>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-menu
            v-model="datesMenu1"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                ref="dates1"
                :value="dateRangeText"
                :label="'发生日期'"
                append-icon="mdi-calendar"
                outlined
                dense
                readonly
                clearable
                @click:clear="dates1 = []"
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <vc-date-picker
              v-model="dates1"
              mode="date"
              is-range
            ></vc-date-picker>
          </v-menu>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-menu
            v-model="datesMenu3"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                ref="dates3"
                :value="dateRangeText3"
                :label="'001过账日期'"
                append-icon="mdi-calendar"
                outlined
                dense
                readonly
                clearable
                @click:clear="dates3 = []"
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <vc-date-picker
              v-model="dates3"
              mode="date"
              is-range
            ></vc-date-picker>
          </v-menu>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-autocomplete
            label="对账负责人"
            v-model="searchRemain.managerId"
            dense
            outlined
            item-text="nickName"
            item-value="id"
            :items="financeList"
            clearable
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <!-- <v-switch
            class="mt-1"
            dense
            v-model="searchRemain.isMe"
            label="待我审批"
            color="success"
          ></v-switch> -->
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2" v-if="searchRemain.status == 2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchRemain.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col> -->
        <!-- <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchRemain.orderNo"
            label="订单号"
            outlined
            dense
            clearable
          ></v-text-field>
        </v-col> -->
      </template>
      <template #btns>
        <!-- <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="sendOA"
          v-permission="['供应采购管理:发送OA']"
        >
          <v-icon left>mdi-send</v-icon>
          发送OA
        </v-btn> -->
        <v-btn
          :loading="loading"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="downloadExcel"
          v-permission="['预算管理审批:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="sendOA"
          :disabled="!canSubmitOA"
          v-permission="['预算管理审批:发送OA']"
        >
          <v-icon left>mdi-send</v-icon>
          发送OA
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="openBatch"
          v-permission="['预算管理审批:Excel导入']"
        >
          <v-icon left>mdi-file-import-outline</v-icon>
          Excel导入
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :to="{ name: 'batch-cost-detail', params: { id: 'new' } }"
          v-permission="['预算管理审批:批量新增预算']"
        >
          <v-icon left>mdi-file-import-outline</v-icon>
          批量新增预算
        </v-btn>
        <!-- <v-btn
          :disabled="!canSubmit3"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="genSapBudget"
          v-permission="['预算管理审批:生成预算报文']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成预算报文
        </v-btn> -->
        <!-- <v-btn
          :disabled="!canSubmit4"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="genSapWriteOff"
          v-permission="['预算管理审批:生成冲销报文']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成冲销报文
        </v-btn> -->
        <v-btn
          :to="{ name: 'large-purchase-detail', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['预算管理审批:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!canCopy"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="copyApply"
          v-permission="['预算管理审批:复制']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          复制
        </v-btn>
        <v-btn
          :disabled="!canDel"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delApply"
          v-permission="['预算管理审批:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!canSubmit5"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="abandon"
          v-permission="['预算管理审批:废弃']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          废弃
        </v-btn>
        <v-btn
          :disabled="!canSubmit55"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="abandonConfirm"
          v-permission="['预算管理审批:同意废弃']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          同意废弃
        </v-btn>
        <v-btn
          :disabled="!canSubmit55"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="abandonCancle"
          v-permission="['预算管理审批:拒绝废弃']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          拒绝废弃
        </v-btn>
        <v-btn
          :disabled="!canSubmit2"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="batchStart"
          v-permission="['预算管理审批:批量提交']"
        >
          <v-icon left>mdi-send</v-icon>
          批量提交
        </v-btn>
        <v-btn
          :disabled="!canSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="submitApply"
          v-permission="['预算管理审批:审批通过']"
        >
          <v-icon left>mdi-send</v-icon>
          审批通过
        </v-btn>
      </template>
      <template v-slot:[`item.applyRemark`]="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <span v-on="on">{{ item.applyRemark.substring(0, 9) }}...</span>
          </template>
          <span>{{ item.applyRemark }}</span>
        </v-tooltip>
      </template>

      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses2[item.status] }}
        </v-chip>
      </template>
      <template v-slot:[`item.needSendOa`]="{ item }">
        <v-chip v-if="item.needSendOa == 0">否</v-chip>
        <v-chip v-if="item.needSendOa == 1">是</v-chip>
      </template>
      <template v-slot:[`item.isGenCost`]="{ item }">
        <v-chip v-if="item.isGenCost == 0">否</v-chip>
        <v-chip v-if="item.isGenCost == 1">是</v-chip>
      </template>
      <template v-slot:[`item.businessStatus`]="{ item }">
        <v-chip v-if="item.businessStatus != null">
          {{ getTextByStatus(item.businessStatus) }}
        </v-chip>
      </template>
      <template v-slot:[`item.overSecond`]="{ item }">
        <v-chip v-if="item.overSecond == 0">否</v-chip>
        <v-chip v-if="item.overSecond == 1">是</v-chip>
      </template>

      <template v-slot:[`item.supplierName`]="{ item }">
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <span v-on="on">{{ item.supplierName.substring(0, 25) }}...</span>
          </template>
          <span>{{ item.supplierName }}</span>
        </v-tooltip>
      </template>
      <template v-slot:[`item.money`]="{ item }">
        {{ item.money.toLocaleString() }}
      </template>
      <template v-slot:[`item.finalMoney`]="{ item }">
        {{ item.finalMoney.toLocaleString() }}
      </template>
      <template v-slot:[`item.toDetail`]="{ item }">
        <v-btn
          v-if="item.batchApplyId"
          :to="{
            name: 'batch-cost-detail',
            params: { id: item.batchApplyId },
          }"
          color="primary"
          text
          small
        >
          {{ item.batchNo }}批量预算
        </v-btn>
        <v-btn
          v-if="item.accidentId"
          :to="{
            name: 'accident-detail',
            params: { id: item.accidentId },
          }"
          color="primary"
          text
          small
        >
          事故报告
        </v-btn>
        <v-btn
          v-if="item.costProjectId"
          :to="{
            name: 'cost-project-detail',
            params: { id: item.costProjectId },
          }"
          color="primary"
          text
          small
        >
          费用项目
        </v-btn>
        <v-btn
          v-if="item.businessId"
          :to="{
            name: routerMap[item.dataSource],
            params: { id: item.businessId },
          }"
          color="primary"
          text
          small
        >
          订单详情
        </v-btn>
      </template>
    </v-table-searchable>
    <budgetBatchDialog v-model="dialog" @success="success"></budgetBatchDialog>
    <!-- <budgetBatchSendoaDialog
      v-model="dialog2"
      :items="selected"
      @success="success"
    ></budgetBatchSendoaDialog> -->
    <budgetBatchSendoaDialog
      v-model="dialog2"
      :items="canSubmitOA ? selected : []"
      @success="success"
    ></budgetBatchSendoaDialog>
  </v-container>
</template>
<script>
import budgetBatchDialog from './private/budget-batch-dialog.vue'
import budgetBatchSendoaDialog from './private/budget-batch-sendoa-dialog.vue'
// applicantId	申请人	string
// applyDate	申请日期	string
// applyRemark	申请说明	string
// attachmentRecords	附件列表	array	CommonAttachment
// auditParams	流程参数	AuditParams	AuditParams
// currencyId	币种id(货币)	integer
// id	物理主键	string
// money	金额	number
// shipCode	船舶编码	string
// status	审批状态 1：未开始(草稿)；2：进行中(已提交)；3已完成；4：驳回	string
// supplierName	供应商名称	string
// supplyItem	供应项目	string
// supplyType	供应类型
export default {
  components: { budgetBatchDialog, budgetBatchSendoaDialog },
  name: 'large-purchase-list',
  created() {
    this.tableName = '预算管理审批'
    this.reqUrl = '/business/shipAffairs/supplyCommon/page'
    this.headers = [
      { text: '船舶名称', value: 'shipInfo' },
      { text: '数据来源', value: 'dataSource' },
      // { text: '是否有前置立项', value: 'needSendOa', hideDefault: true },
      //{ text: 'OA项目编号', value: 'itemNo', hideDefault: true },
      { text: '供应商', value: 'supplierName' },
      { text: '供应项目', value: 'supplyItem' },
      { text: '订单号', value: 'orderNo' },
      { text: '订单号(引用)', value: 'orderNoY' },
      { text: '科目', value: 'costSubjectName' },
      { text: '预算金额', value: 'money' },
      { text: '折算美金', value: 'moneyUsd' },
      { text: '实际付款金额', value: 'finalMoney' },
      { text: '币种', value: 'currencyName' },
      { text: '发生日期', value: 'proposedDate' },
      { text: '实际申请人', value: 'applicantName' },
      { text: '录单人', value: 'applyUserName' },
      { text: '申请日期', value: 'applyDate', hideDefault: true },
      { text: '预算说明', value: 'applyRemark' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '是否生成费用', value: 'isGenCost' },
      { text: '数据包号', value: 'uuId1' },
      { text: '生成预算报文时间', value: 'budgetTime', hideDefault: true },
      { text: '生成冲销报文时间', value: 'writeOffTime', hideDefault: true },
      { text: '是否超二级科目预算', value: 'overSecond' },
      { text: '附件', value: 'attachmentRecords' },
      { text: '链接', value: 'toDetail', hideDefault: true },
      { text: '凭证日期', value: 'costOrderDate', hideDefault: true },
      { text: '001SAP实际过账日期', value: 'buDate' },
    ]
    this.pushParams = { name: 'large-purchase-detail' }
    this.searchDate = {
      label: '申请日期',
      value: 'applyDate',
      interval: true,
    }
    this.searchDate2 = {
      label: '发生日期',
      value: 'proposedDate',
      interval: true,
    }
    this.statuses = ['', '草稿', '审批中', '通过']
    this.color = ['', '草稿', '审批中', '通过']
    this.dataSources = [
      { text: '单次预算', value: '单次预算' },
      { text: '批量预算', value: '批量预算' },
      { text: '备件订单', value: '备件订单' },
      { text: '物料订单', value: '物料订单' },
      { text: '滑油订单', value: '滑油订单' },
      { text: '航修修理单', value: '航修修理单' },
      { text: '坞修修理单', value: '坞修修理单' },
      { text: '滑油消耗', value: '滑油消耗' },
      { text: '自修奖申请', value: '自修奖申请' },
    ]
    this.routerMap = {
      // 0: '',
      备件订单: 'spare-order-detail',
      物料订单: 'materials-order-detail',
      滑油订单: 'soil-order-detail',
      航修修理单: 'voyage-repair-detail',
      坞修修理单: 'dock-repair-detail',
      滑油消耗单: 'soil-out-detail',
      自修奖申请: 'self-repair-bonus-detail',
      // 6: 'spare-order-detail',
      // 7: 'hire-purchase-detail',
      // 8: '',
      // 9: 'self-repair-bonus-detail',
      // 10: '',
      // 11: '',
      // 12: 'materials-order-detail',
      // 13: 'batch-cost-detail',
    }
    // this.routerMap = {
    //   0: '',
    //   1: 'spare-order-detail',
    //   2: 'materials-order-detail',
    //   3: 'soil-order-detail',
    //   4: 'voyage-repair-detail',
    //   5: 'dock-repair-detail',
    //   6: 'spare-order-detail',
    //   7: 'hire-purchase-detail',
    //   8: '',
    //   9: 'self-repair-bonus-detail',
    //   10: '',
    //   11: '',
    //   12: 'materials-order-detail',
    //   13: 'batch-cost-detail',
    // }
    this.bussinessModules = [
      // { text: '手动录入', value: 0 },
      { text: '备件订单', value: '备件订单' },
      { text: '物料订单', value: '物料订单' },
      { text: '滑油订单', value: '滑油订单' },
      { text: '航修修理单', value: '航修修理单' },
      { text: '坞修修理单', value: '坞修修理单' },
      { text: '滑油消耗单', value: '滑油消耗单' },
      // { text: '坞修备件订单', value: 6 },
      // { text: '分期付款', value: 7 },
      // { text: '备用金', value: 8 },
      // { text: '自修奖', value: 9 },
      // { text: '船东账', value: 10 },
      // { text: '新造船', value: 11 },
      // { text: '坞修物料订单', value: 12 },
      // { text: '批量费用', value: 13 },
    ]
    this.statuses2 = ['暂无审批', '草稿', '审批中', '已审批', '已驳回', '废弃']
    this.statusColors = ['info', '', 'warning', 'success', 'error', 'error']
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '待我审批', value: '99' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
      { text: '废弃', value: '5' },
    ]
    this.businessStatusMap = [
      { text: '待机务/通导主管确认', value: '待机务/通导主管确认' },
      { text: '待采购主管确认', value: '待采购主管确认' },
      { text: '待供应商发货', value: '待供应商发货' },
      { text: '供应商修改订单待确认', value: '供应商修改订单待确认' },
      { text: '供应商已发货', value: '供应商已发货' },
      { text: '供应商已安排', value: '供应商已安排' },
      { text: '供应商确认完工', value: '供应商确认完工' },
      { text: '机务/通导确认完工', value: '机务/通导确认完工' },
      { text: '机务/通导退回完工', value: '机务/通导退回完工' },
      { text: '船端入库未提交', value: '船端入库未提交' },
      { text: '船端入库审批中', value: '船端入库审批中' },
      { text: '船端已入库', value: '船端已入库' },
      { text: '船端确认完工', value: '船端确认完工' },
      { text: '已上传发票', value: '已上传发票' },
      { text: '采购申请未提交', value: '未提交' },
      { text: '采购申请待实际申请人确认', value: '待实际申请人确认' },
      { text: '采购申请审批通过', value: '审批通过' },
      { text: '已废弃', value: '废弃' },
      { text: '确认废弃', value: '待单船财务确认废弃' },
      { text: '费用项目未提交', value: '未做凭证' },
      { text: '发票未提交', value: '已做凭证' },
      { text: '发票待实际申请人确认', value: '发票待实际申请人确认' },
      { text: '发票审批中', value: '凭证审批中' },
      { text: '发票审批退回', value: '凭证审批失败' },
      { text: '发票已审批', value: '凭证审批通过' },
      { text: '发票财务未确认-业务', value: '映射错误' },
      { text: '发票财务未确认-业务', value: '报文错误' },
      { text: '发票财务确认中', value: '已发送SAP' },
      { text: '发票财务已确认', value: 'SAP执行成功' },
      { text: '发票财务未确认-SAP', value: 'SAP执行失败' },
      { text: '付款审批未提交', value: '付款审批未提交' },
      { text: '付款审批审批中', value: '付款审批中' },
      { text: '付款审批退回', value: '付款审批未通过' },
      { text: '付款审批已完成', value: '付款审批通过' },
    ]
    // { text: '已付款', value: '已付款' },
    // { text: '待发送OA立项', value: '待发送OA立项' },
    // { text: '已发送OA立项', value: '已发送OA立项' },
    // { text: 'OA立项审批通过', value: 'OA立项审批通过' },
    // { text: 'OA立项审批失败', value: 'OA立项审批失败' },
    // { text: '预算已发送', value: '预算已发送' },
    // { text: '凭证SAP执行成功', value: '凭证SAP执行成功' },
    // { text: '预算已冲销', value: '预算已冲销' },
  },

  data() {
    return {
      selected: [],
      searchRemain: {
        supplyType: 1,
        isMe: true,
        status: '2',
        subjectType: '',
        costSubjectId: '',
        businessStatus: '',
        applicantId: '',
      },
      loading: false,
      dialog: false,
      dialog2: false,
      datesMenu1: false,
      dates1: [],
      datesMenu2: false,
      dates2: [],
      datesMenu3: false,
      dates3: [],
      costSubjects: [],
      suppliers: [],
      searchRemainStatus: '99',
      isApplicantId: false,
      isGenCost: '',
      dataSource: '',
      financeList: [],
    }
  },
  watch: {
    dialog2(val) {
      if (!val) {
        this.$refs.table.loadTableData()
        this.success()
        console.log(1)
      }
    },
    dates1: {
      handler() {
        if (this.dates1?.start && this.dates1?.end) {
          this.datesMenu1 = false
          this.searchRemain.fromTime = this.dates1?.start
            .toISOString()
            .split('T')[0]
          this.searchRemain.toTime = this.dates1?.end
            .toISOString()
            .split('T')[0]

          console.log(
            this.searchRemain.fromTime,
            this.searchRemain.toTime,
            this.dates1,
          )
          this.$refs.table.loadTableData()
        } else {
          this.searchRemain.fromTime = ''
          this.searchRemain.toTime = ''
          this.$refs.table.loadTableData()
        }
      },
    },
    dates2: {
      handler() {
        if (this.dates2?.start && this.dates2?.end) {
          this.datesMenu2 = false
          this.searchRemain.fromTime2 = this.dates2?.start
            .toISOString()
            .split('T')[0]
          this.searchRemain.toTime2 = this.dates2?.end
            .toISOString()
            .split('T')[0]

          console.log(
            this.searchRemain.fromTime2,
            this.searchRemain.toTime2,
            this.dates2,
          )
          this.$refs.table.loadTableData()
        } else {
          this.searchRemain.fromTime2 = ''
          this.searchRemain.toTime2 = ''
          this.$refs.table.loadTableData()
        }
      },
    },
    dates3: {
      handler() {
        if (this.dates3?.start && this.dates3?.end) {
          this.datesMenu3 = false
          this.searchRemain.fromTime3 = this.dates3?.start
            .toISOString()
            .split('T')[0]
          this.searchRemain.toTime3 = this.dates3?.end
            .toISOString()
            .split('T')[0]

          console.log(
            this.searchRemain.fromTime3,
            this.searchRemain.toTime3,
            this.dates3,
          )
          this.$refs.table.loadTableData()
        } else {
          this.searchRemain.fromTime3 = ''
          this.searchRemain.toTime3 = ''
          this.$refs.table.loadTableData()
        }
      },
    },
    'searchRemain.subjectType': {
      handler: function (val) {
        this.getCostSubjects(val)
      },
    },
    searchRemainStatus(val) {
      if (val) {
        if (val == 99) {
          this.searchRemain.status = 2
          this.searchRemain.isMe = true
        } else {
          this.searchRemain.status = val
          this.searchRemain.isMe = false
        }
      } else {
        this.searchRemain.status = ''
      }
    },
  },
  computed: {
    canDel() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            item.dataSource == '单次预算' &&
            (item.status == 1 || item.status == 4) &&
            item.applyUser == this.$local.data.get('userInfo').id,
        )
      )
    },
    canCopy() {
      return (
        this.selected.length > 0 &&
        this.selected.every((item) => item.dataSource == '单次预算')
      )
    },
    canSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) => item?.auditParams?.taskId && item.status == 2,
        )
      )
    },
    canSubmit2() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            ((item?.status == '1' &&
              item?.businessStatus != '待实际申请人确认') ||
              item?.status == '4') &&
            item.dataSource == '单次预算' &&
            item.applyUser == this.$local.data.get('userInfo').id,
        )
      )
    },
    canSubmit3() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            item?.status == '3' &&
            (item?.businessStatus == '审批通过' ||
              item?.businessStatus == 'OA立项审批通过') &&
            (item.dataSource == '单次预算' ||
              // item.dataSource == '航修修理单' ||
              item.dataSource == '批量预算'),
        )
      )
    },
    canSubmit4() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            item?.status == '3' &&
            item?.budgetTime &&
            item?.businessStatus == '凭证SAP执行成功',
        )
      )
    },
    canSubmit5() {
      // return (
      //   this.selected.length > 0 &&
      //   this.selected.every(
      //     (item) =>
      //       item?.status == '3' &&
      //       item?.budgetTime &&
      //       (item?.businessStatus == '凭证SAP执行成功' ||
      //         item?.businessStatus == '预算已发送'),
      //   )
      // )
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            (item?.status == '3' || item?.status == '4') &&
            item.applyUser == this.$local.data.get('userInfo').id &&
            item.dataSource !== '滑油消耗',
        )
      )
      // return (
      //   this.selected.length > 0 &&
      //   this.selected.every(
      //     (item) =>
      //       item?.status == '3' &&
      //       item.applyUser == this.$local.data.get('userInfo').id &&
      //       (item?.businessStatus == '待发送OA立项' ||
      //         item?.businessStatus == 'OA立项审批失败'),
      //   )
      // )
    },
    canSubmit55() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            item?.status == '3' && item?.businessStatus == '待单船财务确认废弃',
        )
      )
    },
    canSubmitOA() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            item?.status == '3' &&
            item?.needSendOa == 1 &&
            (item?.businessStatus == '待发送OA立项' ||
              item?.businessStatus == 'OA立项审批失败'),
        )
      )
    },
    dateRangeText() {
      return this.dates1?.start && this.dates1?.end
        ? `${this.dates1.start.toLocaleDateString()} 至 ${this.dates1?.end.toLocaleDateString()}`
        : ''
    },
    dateRangeText2() {
      return this.dates2?.start && this.dates2?.end
        ? `${this.dates2.start.toLocaleDateString()} 至 ${this.dates2?.end.toLocaleDateString()}`
        : ''
    },
    dateRangeText3() {
      return this.dates3?.start && this.dates3?.end
        ? `${this.dates3.start.toLocaleDateString()} 至 ${this.dates3?.end.toLocaleDateString()}`
        : ''
    },
  },

  methods: {
    async delApply() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/supplyCommon/deleteBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async copyApply() {
      if (!(await this.$dialog.msgbox.confirm('确定复制此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/supplyCommon/copyBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`复制成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async submitApply() {
      if (!(await this.$dialog.msgbox.confirm('确定审批通过所选记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/batchCompleteTaskAndCommentAndSetVar',
        this.selected.map((item) => ({
          adopt: true,
          comment: '',
          params: {},
          taskId: item.auditParams.taskId,
        })),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`提交成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
    async batchStart() {
      if (!(await this.$dialog.msgbox.confirm('确定提交所选记录？'))) return
      this.loading = true
      this.selected.forEach((item) => {
        item.status = 1
        item.businessStatus = '待实际申请人确认'
        const reqUrls =
          '/business/shipAffairs//supplyCommon/saveOrUpdateSupplyCommon'
        const { errorRaw } = this.postAsync(reqUrls, {
          ...item,
          shipCode: item.shipInfo.shipCode,
          supplierId: item.supplierId,
          supplyType: 1,
        })
        if (errorRaw) {
          return
        }
      })
      this.$dialog.message.success(`提交成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
    async genSapBudget() {
      const ids = this.selected.map((item) => item.id)
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/supplyCommon/createSapList',
        ids,
      )
      if (!errorRaw) this.$dialog.message.success('生成成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async genSapWriteOff() {
      const ids = this.selected.map((item) => item.id)
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/supplyCommon/createWriteOffSapList',
        ids,
      )
      if (!errorRaw) this.$dialog.message.success('生成成功')
      this.selected = []
      await this.$refs.table.loadTableData()
    },
    async abandon() {
      if (!(await this.$dialog.msgbox.confirm('确定废弃此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/supplyCommon/abandonBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`废弃成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async abandonConfirm() {
      if (!(await this.$dialog.msgbox.confirm('确定废弃此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/supplyCommon/abandonConfirmBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`废弃成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async abandonCancle() {
      if (!(await this.$dialog.msgbox.confirm('确定取消废弃此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/supplyCommon/abandonCancleBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`取消成功`)
      this.selected = []
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },
    async success() {
      await this.$refs.table.loadTableData()
    },
    openBatch() {
      this.dialog = true
    },
    sendOA() {
      this.dialog2 = true
    },
    async getCostSubjects(subjectType) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/costSubject/page',
        {
          subjectType,
          size: 1000,
          current: 1,
        },
      )
      // { text: '草稿', value: '1' },
      this.costSubjects = data.records
    },
    async downloadExcel() {
      this.loading = true
      let params = { ...this.$refs.table.searchRemain }
      console.log(this.$refs.table.searchRemain)
      console.log(params)
      params = {
        ...params,
        fuzzyParam: this.$refs.table.fuzzyParam,
        // shipCode: this.$refs.table.ship,
        // fromTime: this.dates1?.start?.toISOString()?.split('T')?.[0],
        // toTime: this.dates1?.end?.toISOString()?.split('T')?.[0],
        // fromTime2: this.dates2?.start?.toISOString()?.split('T')?.[0],
        // toTime2: this.dates2?.end?.toISOString()?.split('T')?.[0],
        // fromTime3: this.dates3?.start?.toISOString()?.split('T')?.[0],
        // toTime3: this.dates3?.end?.toISOString()?.split('T')?.[0],
      }
      console.log(params)
      await this.getBlobDownload(
        '/business/shipAffairs//supplyCommon/excelExport',
        params,
        // 时间戳后四位
        `单次预算审批-${new Date().getTime().toString().slice(-4)}.xlsx`,
      )
      this.loading = false
    },
    getTextByStatus(businessStatus) {
      const statusItem = this.businessStatusMap.find(
        (item) => item.value === businessStatus,
      )
      return statusItem ? statusItem.text : businessStatus
    },
    async getFinanceList() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/Supplier/getFinanceList`,
      )
      console.log(data)
      this.financeList = data
    },
  },

  mounted() {
    this.getFinanceList()
    if (this.$route.query.status != undefined) {
      // this.searchRemain.status = this.$route.query.status
      this.searchRemainStatus = this.$route.query.status
      this.searchRemain.businessStatus = this.$route.query.businessStatus
      this.searchRemain.applicantId = this.$route.query.applicantId
      this.searchRemain.isGenCost = this.$route.query.isGenCost
      this.searchRemain.dataSource = this.$route.query.dataSource
      if (
        this.searchRemain.businessStatus == '待单船财务确认废弃' ||
        this.searchRemain.businessStatus == '对账负责人'
      ) {
        this.searchRemain.managerId = this.$local.data.get('userInfo').id
      }
      // this.isApplicantId = true
      this.isApplicantId = this.searchRemain.applicantId
        ? {
            id: this.searchRemain.applicantId,
            nickName: this.$local.data.get('userInfo').nickName,
          }
        : false
      // if (
      //   this.searchRemain.applicantId &&
      //   this.searchRemain.applicantId.trim()
      // ) {
      //   this.isApplicantId = true
      // }
      // 在3秒后执行一次任务
      // setTimeout(() => {
      //   this.searchRemain.businessStatus = ''
      //   this.searchRemain.applicantId = ''
      //   console.log(1)
      // }, 1000)
    }
  },
}
</script>

<style></style>
