<template>
  <v-dialog
    attach="#mask"
    @input="(val) => $store.commit('setMaskLayer', val)"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        批量导入预算
        <v-spacer></v-spacer>
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="dowExcel"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          导出模板
        </v-btn>
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="importExcel"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          导入并保存草稿
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-file-input
                  outlined
                  dense
                  accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
                  label="导入EXCEL"
                  v-model="file"
                ></v-file-input>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-select
                  :items="付款公司选项"
                  label="付款公司"
                  v-model="formData.paymentCompany"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-supply-select2
                  :disabled="!formData.paymentCompany"
                  v-model="formData.supplyId"
                  :payment-company="formData.paymentCompany"
                  @select="
                    (item) => {
                      currency = item.currency
                    }
                  "
                  :init-selected="initSupply"
                  :rules="[rules.required]"
                ></v-supply-select2>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="formData.subjectId"
                  :init-selected="formData.initSubject"
                  :search-dicts="searchDicts"
                  :search-remain="searchObj"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  required
                  dense
                  fuzzy-label="模糊搜索"
                  :rules="[rules.required]"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="备注"
                  v-model="formData.remark"
                  dense
                  :rules="[rules.required]"
                  required
                ></v-textarea>
              </v-col> -->
            </v-row>
          </v-container>
          <!-- <v-attach-list
            :attachments="formData.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list> -->
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'budget-batch-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.付款公司选项 = ['3000', '3800', '8903', '3402']
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      draw: {},
      searchObj: { efFlag: 1, nickName: '', deptId: '1', paymentCompany: '' },
      file: null,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
    },
    // file(val) {
    //   if (val) {
    //     this.importExcel()
    //   }
    // },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
    },
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async dowExcel() {
      // const items = this.components.map((i) => i.materialId)
      const { errorRaw } = await this.blobDownload(
        '/business/shipAffairs//supplyCommon/dowExcel',
        '单次预算导入模板.xlsx',
      )
      if (errorRaw) this.$dialog.message.error(errorRaw)
    },
    async importExcel() {
      if (this.file == null) {
        this.$dialog.message.error('请上传附件！')
        return
      }
      this.$emit('change', false)
      let formData = new FormData()
      formData.append('file', this.file)
      const { data } = await this.postAsync(
        '/business/shipAffairs//supplyCommon/importExcel',
        formData,
      )
      if (data) {
        this.$dialog.message.success('保存成功')
        this.$emit('change', false)
      }
    },
  },

  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
