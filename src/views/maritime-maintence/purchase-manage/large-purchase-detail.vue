<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['预算管理审批:编辑']"
      :title="`预算管理审批-${statuses[detail.status] || '新增'}`"
      :tooltip="isEdit ? detail.supplyItem : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        ((detail.dataSource == '单次预算' ||
          detail.dataSource == '备件订单' ||
          detail.dataSource == '滑油订单' ||
          detail.dataSource == '滑油消耗' ||
          detail.dataSource == '物料订单' ||
          detail.dataSource == '自修奖申请' ||
          detail.dataSource == '航修修理单' ||
          detail.dataSource == '坞修修理单') &&
          ((detail.status == 1 &&
            detail.businessStatus != '待实际申请人确认') ||
            (detail.status == 2 &&
              (!detail.auditParams || detail.auditParams.taskId)) ||
            detail.status == 4 ||
            detail.status == undefined)) ||
        !isEdit
      "
      @save="save"
      @submit="submit"
      :can-save="
        (detail.dataSource == '单次预算' &&
          ((detail.status == 1 &&
            detail.businessStatus != '待实际申请人确认') ||
            detail.status == 4 ||
            detail.status == undefined)) ||
        !isEdit
      "
    >
      <!-- :can-submit="!detail.auditParams || detail.auditParams.taskId" -->
      <template v-slot:custombtns>
        <v-btn
          v-if="detail.businessStatus == '待实际申请人确认' && isApplyPerson"
          tile
          color="error"
          small
          class="mx-1"
          @click="startProcess"
          :loading="quoteLoading"
          v-permission="['预算管理审批:实际申请人确认']"
        >
          <v-icon left>mdi-check</v-icon>
          实际申请人确认
        </v-btn>
        <v-btn
          v-if="detail.businessStatus == '待实际申请人确认' && isApplyPerson"
          tile
          color="error"
          small
          class="mx-1"
          @click="returnProcess"
          :loading="quoteLoading"
          v-permission="['预算管理审批:退回']"
        >
          <v-icon left>mdi-check</v-icon>
          退回
        </v-btn>
      </template>
      <template
        v-if="auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit
              ref="audit"
              :auditParams="auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:基本信息>
        <v-form :readonly="detail.status == '3'" ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  :disabled="isEdit"
                  @change="checkOrderNo"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.applyDate"
                  label="申请日期"
                  use-today
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  readonly
                ></vs-date-picker>
              </v-col>
              <v-col md="3" cols="12">
                <v-handler-ship-code
                  :disabled="isEdit"
                  label="实际申请人"
                  :use-current="!isEdit"
                  v-model="detail.applicantId"
                  :rules="[rules.required]"
                  :init-user="initHandler"
                  :shipCode="detail.shipCode"
                  @selectUser="(user) => (detail.applicantName = user.nickName)"
                ></v-handler-ship-code>
              </v-col>
              <v-col cols="12" md="3" v-if="false">
                <v-select
                  v-model="detail.needApprove"
                  :items="[
                    { text: '是', value: true },
                    { text: '否', value: false },
                  ]"
                  label="是否需要审批"
                  dense
                  required
                  outlined
                  :disabled="isEdit"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :disabled="isEdit"
                  v-model="detail.applyType"
                  :items="[
                    { text: '常规', value: 1 },
                    { text: '紧急', value: 2 },
                    { text: '坞修', value: 3 },
                    { text: '固定资产', value: 4 },
                    { text: '事故', value: 5 },
                  ]"
                  label="申请类型"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-select>
              </v-col>
              <v-col md="3" cols="12" v-if="detail.applyType == 5">
                <v-dialog-select
                  req-url="/business/seaAffairs/AccidentRecord/record/list"
                  label="事故单号"
                  v-model="detail.accidentId"
                  :rules="detail.applyType == 5 ? [rules.required] : []"
                  :init-selected="detail.initAccident"
                  item-text="id"
                  item-value="id"
                  :headers="AccidentHeaders"
                  :search-remain="searchObj2"
                  @select="
                    (item) => {
                      detail.accidentNo = item.id
                    }
                  "
                  :required="detail.applyType != 5"
                  :readonly="isEdit"
                  dense
                  :disabled="!detail.shipCode || detail.applyType != 5"
                ></v-dialog-select>
              </v-col>
              <!-- <v-col md="3" cols="12" v-if="detail.accidentId">
                <v-btn
                  v-if="detail.accidentId"
                  :to="{
                    name: 'accident-detail',
                    params: { id: detail.accidentId },
                  }"
                  text
                >
                  事故报告详情
                </v-btn>
              </v-col> -->
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/pageNew"
                  label="费用科目"
                  v-model="detail.costSubjectId"
                  :rules="[rules.required]"
                  :init-selected="detail.initSubject"
                  :search-dicts="searchDicts"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  @select="
                    (item) => {
                      detail.budgetSapCode = item.budgetSapCode
                      detail.subjectName = item.subjectName
                      detail.newProcessFlag = item.newProcessFlag
                      detail.orderNoType = item.orderNoType
                      genOrderNo()
                    }
                  "
                  :readonly="isEdit"
                  :search-remain="searchObjSubject"
                  required
                  dense
                  fuzzy-label="模糊查询"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="订单号"
                  dense
                  v-model="detail.orderNo"
                  outlined
                  :rules="[rules.required]"
                  @blur="checkOrderNo"
                  readonly
                  :disabled="isEdit"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.supplyItem"
                  label="供应项目"
                  dense
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.proposedDate"
                  label="发生日期"
                  dense
                  :rules="[rules.required]"
                  :readonly="!canEditFields && isEdit"
                  required
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <port-select-dialog2
                  v-if="canEditFields || !isEdit"
                  :init-selected="{ portCn: detail.deliveryPlace }"
                  @select="
                    (p) => {
                      detail.deliveryPlace = p.portCn
                    }
                  "
                ></port-select-dialog2>
                <v-text-field
                  label="港口"
                  v-else
                  dense
                  outlined
                  v-model="detail.deliveryPlace"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  readonly
                  placeholder="确定中标供应商后自动赋值"
                  label="中标供应商"
                  dense
                  v-model="supplierName"
                  outlined
                  :rules="[rules.required]"
                  @change="checkOrderNo"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  readonly
                  placeholder="确定中标供应商后自动赋值"
                  v-model="detail.currencyId"
                  :items="currencys"
                  item-text="ccyCode"
                  item-value="currencyType"
                  label="币种"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-if="isJPY"
                  placeholder="确定中标供应商后自动赋值"
                  @change="() => (detail.money = Math.round(detail.money))"
                  v-model="detail.money"
                  label="预算金额"
                  type="number"
                  dense
                  :rules="[rules.required]"
                  readonly
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  placeholder="确定中标供应商后自动赋值"
                  v-model="detail.money"
                  label="预算金额"
                  type="number"
                  dense
                  :rules="[rules.required]"
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.moneyUsd"
                  label="折算美金"
                  dense
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.finalMoney"
                  placeholder="凭证审批后系统自动回写"
                  label="实际付款金额"
                  dense
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-select
                  v-model="detail.supplyCycle"
                  :items="[
                    { text: '年度', value: 0 },
                    { text: '季度', value: 1 },
                    { text: '临时', value: 2 },
                  ]"
                  label="供货周期"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-select>
              </v-col> -->
              <v-col cols="12">
                <v-btn
                  v-if="detail.accidentId"
                  :to="{
                    name: 'accident-detail-new',
                    params: { id: detail.accidentId },
                  }"
                  color="primary"
                  text
                >
                  <sapn style="color: red">点击查看事故报告详情</sapn>
                </v-btn>
                <v-btn
                  v-if="detail.costProjectId"
                  :to="{
                    name: 'cost-project-detail',
                    params: { id: detail.costProjectId },
                  }"
                  color="primary"
                  text
                >
                  <sapn style="color: red">点击查看费用项目详情</sapn>
                </v-btn>
                <v-btn
                  v-if="
                    detail.businessId &&
                    (detail.status == 2 || detail.status == 3)
                  "
                  :to="{
                    name: routerMap[detail.dataSource2],
                    params: { id: detail.businessId },
                  }"
                  color="primary"
                  text
                >
                  <sapn style="color: red">点击查看订单详情</sapn>
                  <sapn style="color: red" v-if="detail.orderNoY">
                    :{{ detail.orderNoY }}
                  </sapn>
                </v-btn>
              </v-col>
              <!-- v-if="detail.money < 0" -->
              <!-- 备件0、物料1、滑油2、通导固定资产3、航修4、无99 -->
              <v-col
                md="3"
                cols="12"
                v-if="
                  detail.money < 0 &&
                  (detail.orderNoType == 'spareApplyCode' ||
                    detail.orderNoType == 'materialApplyCode' ||
                    detail.orderNoType == 'greaseApplyCode') &&
                  !(detail.status == 2 || detail.status == 3)
                "
              >
                <v-dialog-select
                  req-url="/business/shipAffairs/purchaseManage/purchaseOrderPage"
                  label="采购订单"
                  v-model="detail.businessId"
                  :rules="[rules.required]"
                  item-text="orderNo"
                  item-value="id"
                  :headers="orderHeaders"
                  :readonly="isEdit"
                  :search-remain="searchObj"
                  required
                  dense
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="订单号"
                        outlined
                        dense
                        v-model="searchObj.fuzzyParam"
                      ></v-text-field>
                    </v-col>
                  </template>
                  <template v-slot:[`item.isDockRepair`]="{ item }">
                    {{ item.isDockRepair ? '是' : '否' }}
                  </template>
                  <template v-slot:[`item.orderType`]="{ item }">
                    {{
                      item.orderType == '01'
                        ? '备件'
                        : item.orderType == '02'
                        ? '物料'
                        : '滑油'
                    }}
                  </template>
                  <template v-slot:[`item.businessStatus`]="{ item }">
                    {{ businessStatusMap[item.businessStatus] }}
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col
                md="3"
                cols="12"
                v-if="
                  detail.money < 0 &&
                  (detail.orderNoType == 'UWORDERCODE' ||
                    detail.orderNoType == 'voyageRepairOrder') &&
                  !(detail.status == 2 || detail.status == 3)
                "
              >
                <v-dialog-select
                  req-url="/business/shipAffairs/voyageRepair/orderPage"
                  label="航修订单"
                  v-model="detail.businessId"
                  :rules="[rules.required]"
                  item-text="orderNo"
                  item-value="id"
                  :headers="voyageHeaders"
                  :readonly="isEdit"
                  :search-remain="searchObjVoyage"
                  required
                  dense
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="修理单号"
                        outlined
                        dense
                        v-model="searchObjVoyage.fuzzyParam"
                      ></v-text-field>
                    </v-col>
                  </template>
                  <template v-slot:[`item.businessStatus`]="{ item }">
                    {{ businessStatusMap[item.businessStatus] }}
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="detail.applyRemark"
                  :rules="[rules.required]"
                  label="预算说明"
                  outlined
                  dense
                  :readonly="!canEditFields && isEdit"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  title="未中标附件"
                  :attachments="attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  title="中标附件(审批通过后，自动推送至中标供应商，请谨慎上传)"
                  :attachments="attachmentRecords2"
                  @change="changeAttachment2"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <!-- <template v-if="!supplierName" #待选供应商按钮> -->
      <template v-if="!isEdit" #待选供应商按钮>
        <!-- <template
        v-if="!detail.status || detail.status == '1' || detail.status == '4'"
        #待选供应商按钮
      > -->
        <v-btn
          :disabled="!selectedSup"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click="confirm"
          v-permission="['预算管理审批:确定中标']"
        >
          <v-icon left>mdi-check</v-icon>
          确定中标
        </v-btn>
        <v-btn
          :disabled="!detail.shipCode || !detail.proposedDate"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createSup"
          v-permission="['待选供应商:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selectedSup"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delSup"
          v-permission="['待选供应商:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #待选供应商>
        <v-card-text>
          <v-table-list
            :headers="pendingHeaders"
            :items="supplyPriceModifyList"
            v-model="selectedSup"
            :ship-code="detail.shipCode"
            item-key="vid"
          ></v-table-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <pending-sup
      v-model="dialog"
      @success="success"
      :initial-data="initialData"
      :shipCode="detail.shipCode"
      :happenDate="detail.proposedDate"
    ></pending-sup>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import PortSelectDialog2 from '../components/port-select-dialog2.vue'
import pendingSup from './private/pendingSup.vue'
import routerControl from '@/mixin/routerControl'
export default {
  components: { pendingSup, PortSelectDialog2 },
  mixins: [currencyHelper, routerControl],
  name: 'large-purchase-detail',
  created() {
    this.backRouteName = 'large-purchase-list'
    this.subtitles = ['基本信息', '待选供应商']
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.pendingHeaders = [
      { text: '供应商', value: 'supplierName' },
      { text: '币种', value: 'currency' },
      { text: '预算金额', value: 'money' },
      { text: '折算美金', value: 'usd' },
      { text: '备注', value: 'remark' },
    ]
    this.statuses = ['', '未提交', '审批中', '通过', '驳回']
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.AccidentHeaders = [
      { text: '船舶', value: 'shipInfo' },
      { text: '事故发生时间（地方时间）', value: 'localTime' },
      { text: '船长', value: 'captain' },
      { text: '轮机长', value: 'chiefEngineer' },
      { text: '死亡人数', value: 'deathDoll' },
      { text: '受伤人数', value: 'injuryDoll' },
      { text: '发生污染', value: 'occurPollution' },
      { text: '沉船或全损', value: 'shipwreckOrTotalLoss' },
      { text: '事故性质', value: 'natureAccident' },
      { text: '事故等级', value: 'levelAccident' },
      { text: '结案', value: 'isClosed' },
      { text: '审批状态', value: 'status' },
    ]
    this.orderHeaders = [
      { text: '船舶', value: 'shipInfo' },
      { text: '订单号', value: 'orderNo' },
      { text: '订单类型', value: 'orderType' },
      { text: '状态', value: 'businessStatus' },
      { text: '申请单号', value: 'applyNo' },
      { text: '供应商', value: 'supplierName' },
      { text: '创建日期', value: 'createTime' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '交货港口', value: 'orderPortName' },
      { text: '入库完成日期', value: 'completeTime' },
      { text: '是否坞修', value: 'isDockRepair' },
    ]
    this.voyageHeaders = [
      { text: '修理单号', value: 'orderNo' },
      { text: '供应商', value: 'supplierName' },
      { text: '修理费用', value: 'reallyRepairExpense' },
      { text: '其他费用', value: 'reallyOtherMoney' },
      // { text: '申请部门', value: 'dept' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.routerMap = {
      // 0: '',
      备件订单: 'spare-order-detail',
      物料订单: 'materials-order-detail',
      滑油订单: 'soil-order-detail',
      航修修理单: 'voyage-repair-detail',
      备件订单0: 'spare-order-detail',
      物料订单1: 'materials-order-detail',
      滑油订单2: 'soil-order-detail',
      备件订单3: 'spare-order-detail',
      单次预算1: 'materials-order-detail',
      单次预算2: 'soil-order-detail',
      单次预算3: 'spare-order-detail',
      航修修理单99: 'voyage-repair-detail',
      坞修修理单: 'dock-repair-detail',
      滑油消耗: 'soil-out-detail',
      自修奖申请: 'self-repair-bonus-detail',
      // 6: 'spare-order-detail',
      // 7: 'hire-purchase-detail',
      // 8: '',
      // 9: 'self-repair-bonus-detail',
      // 10: '',
      // 11: '',
      // 12: 'materials-order-detail',
      // 13: 'batch-cost-detail',
    }
    // this.routerMap = {
    //   0: '',
    //   1: 'spare-order-detail',
    //   2: 'materials-order-detail',
    //   3: 'soil-order-detail',
    //   4: 'voyage-repair-detail',
    //   5: 'dock-repair-detail',
    //   6: 'spare-order-detail',
    //   7: 'hire-purchase-detail',
    //   8: '',
    //   9: 'self-repair-bonus-detail',
    //   10: '',
    //   11: '',
    //   12: 'materials-order-detail',
    //   13: 'batch-cost-detail',
    // }
    this.bussinessModules = [
      // { text: '手动录入', value: 0 },
      { text: '备件订单', value: '备件订单' },
      { text: '物料订单', value: '物料订单' },
      { text: '滑油订单', value: '滑油订单' },
      { text: '航修修理单', value: '航修修理单' },
      { text: '坞修修理单', value: '坞修修理单' },
      { text: '滑油消耗单', value: '滑油消耗单' },
      { text: '自修奖申请', value: '自修奖申请' },
      // { text: '坞修备件订单', value: 6 },
      // { text: '分期付款', value: 7 },
      // { text: '备用金', value: 8 },
      // { text: '自修奖', value: 9 },
      // { text: '船东账', value: 10 },
      // { text: '新造船', value: 11 },
      // { text: '坞修物料订单', value: 12 },
      // { text: '批量费用', value: 13 },
    ]
    this.businessStatusMap = {
      0: '费用项目未提交', //未做凭证
      1: '发票未提交', //已做凭证
      10: '发票未提交', //未提交
      11: '发票审批中', //凭证审批中
      12: '发票审批退回', //审批失败
      13: '发票已审批', //审批通过
      21: '发票财务未确认-业务', //映射错误
      22: '发票财务未确认-业务', //报文错误
      24: '发票财务确认中', //已发送SAP
      26: '发票财务已确认', //SAP执行成功
      27: '发票财务未确认-SAP', //SAP执行失败
      30: '付款审批审批中', //付款审批中
      31: '付款审批退回', //付款审批未通过
      32: '付款审批已完成', //付款审批通过
      33: '已付款', //已付款
      34: '付款审批未提交', //付款审批未提交
      99: '邮件采购订单',
      1000: '待机务/通导主管确认',
      1001: '待供应商发货', //待供应商确认发货
      10011: '机务/通导主管已确认，待OA审批通过',
      10099: '机务/通导主管已确认,超二级科目预算审批中',
      100999: '机务/通导主管已确认,超二级科目预算审批失败',
      1002: '供应商已发货', //已发货
      1003: '船端入库未提交', //已到货
      1008: '船端入库审批中', //入库中
      1004: '船端已入库', //入库完成
      10044: '已上传发票', //入库完成
      1005: '付款中',
      1006: '已付款',
      1007: '作废',
      1009: '重新定标',
    }
  },
  data() {
    return {
      dialog: false,
      selectedSup: false,
      supplierName: '',
      supplierId: '',
      currencys: [],
      attachmentRecords: [],
      attachmentRecords2: [],
      auditParams: {},
      supplyPriceModifyList: [],
      detail: {
        attachmentIds: [],
        supplyPriceModifyList: [],
        money: 0,
        // applicantId: this.$local.data.get('userInfo').nickName,
        applicantId: this.$local.data.get('userInfo').id,
        shipCode: '',
        needApprove: true,
        status: 1,
        orderNo: '',
        needSendOa: 0,
      },
      initialData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      initHandler: false,
      userId: this.$local.data.get('userInfo').id,
      flag: false,
      quoteLoading: false,
      searchObj2: { shipCode: '' },
      searchObjSubject: { isWhite: false },
      searchObj: {
        orderType: '',
      },
      searchObjVoyage: {
        orderType: '',
      },
    }
  },
  watch: {
    'detail.shipCode'(val) {
      // 生成订单号
      if (!this.isEdit && val) {
        this.genOrderNo()
      }
      if (val) {
        this.searchObj2.shipCode = val
        this.searchObj.shipCode = val
        this.searchObjVoyage.shipCode = val
      }
      if (!this.isEdit && val) {
        this.checkOrderNo()
      }
    },
    'detail.needSendOa'(val) {
      if (val) {
        if (this.$route.params.id == 'new') {
          this.detail.costSubjectId = ''
        }
        this.searchObjSubject.isWhite = val
      } else {
        if (this.$route.params.id == 'new') {
          this.detail.costSubjectId = ''
        }
        this.searchObjSubject.isWhite = val
      }
    },
  },
  computed: {
    canEditFields() {
      if (!this.detail.businessStatus) {
        return false
      }
      return (
        this.detail.dataSource === '单次预算' &&
        this.detail.businessStatus.includes('采购主管')
      )
    },
    isEdit() {
      return (
        this.$route.params.id !== 'new' &&
        this.detail.businessStatus !== '未提交'
      )
    },
    isJPY() {
      const JPYID = this.currencyInfo.find((i) => i.ccyCode === 'JPY')?.id
      return this.detail.currencyId === JPYID
    },
    isApplyPerson() {
      return this.userId == this.detail.applicantId
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    changeAttachment2(attachmentIds) {
      this.detail.attachmentIds2 = attachmentIds
    },
    async save(goBack, notMove = false) {
      // console.log(this.detail.applicantId)
      // console.log(this.$local.data.get('userInfo').nickName)
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.detail.id == 'new' || this.detail.id == undefined) {
        this.detail.dataSource = '单次预算'
        this.detail.needApprove = true

        if (this.detail.applicantId == this.$local.data.get('userInfo').id) {
          this.detail.applicantName = this.$local.data.get('userInfo').nickName
        }
      }
      console.log(this.detail.status)
      console.log(this.detail.attachmentIds2)
      if (
        this.detail.status != 2 &&
        this.detail.status != 3 &&
        this.detail.attachmentIds2 == null &&
        this.detail.newProcessFlag
      ) {
        this.$dialog.message.error('请上传中标附件！')
        return
      }
      if (
        this.detail.status != 2 &&
        this.detail.status != 3 &&
        (!Array.isArray(this.detail.attachmentIds2) ||
          this.detail.attachmentIds2.length === 0) &&
        this.detail.newProcessFlag
      ) {
        this.$dialog.message.error('请上传中标附件！')
        return
      }
      const url = '/business/shipAffairs/supplyCommon/saveOrUpdateSupplyCommon'
      const delList = this.detail.supplyPriceModifyList
        .filter((i) => !this.supplyPriceModifyList.includes(i))
        .map((i) => {
          return { id: i.id, operationType: 3 }
        })
      const { errorRaw, data } = await this.postAsync(url, {
        ...this.detail,
        supplierId: this.supplierId,
        supplyType: 1,
        supplyPriceModifyList: [...this.supplyPriceModifyList, ...delList],
        supplyCycle: 0,
      })
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      // 未提交和审批失败状态重新提交到待实际申请人确认状态
      // this.loadDetail()
      if (
        this.detail.status == 1 ||
        this.detail.status == 4 ||
        this.detail.status == undefined
      ) {
        if (this.detail.applicantId != this.userId) {
          this.detail.id = data
          this.detail.status = 1
          this.detail.businessStatus = '待实际申请人确认'
          const reqUrl =
            '/business/shipAffairs/supplyCommon/saveOrUpdateSupplyCommon'
          const { errorRaw } = await this.postAsync(reqUrl, {
            ...this.detail,
            supplierId: this.supplierId,
            supplyType: 1,
            supplyCycle: 0,
          })
          if (errorRaw) return false
          this.flag = true
          goBack()
        } else {
          this.detail.id = data
          this.flag = true
          this.startProcess()
          this.closeAndTo(this.backRouteName)
        }
      }
      if (!this.flag) {
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/supplyCommon/submitAudit',
            { id: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) {
            const { data } = await this.getAsync(
              '/business/shipAffairs/supplyCommon/getDetailById',
              { Id: this.$route.params.id },
            )
            if (data.status == 3) {
              const ids = [this.$route.params.id]
              const { errorRaw } = await this.postAsync(
                '/business/shipAffairs/supplyCommon/createSapNewList',
                ids,
              )
              if (!errorRaw) goBack()
            } else {
              goBack()
            }
          }
        }
      }
    },
    async returnProcess() {
      this.quoteLoading = true
      this.detail.status = 1
      this.detail.businessStatus = '未提交'
      const reqUrl =
        '/business/shipAffairs/supplyCommon/saveOrUpdateSupplyCommon'
      const { errorRaw } = await this.postAsync(reqUrl, {
        ...this.detail,
        supplierId: this.supplierId,
        supplyType: 1,
        supplyCycle: 0,
      })
      if (errorRaw) return false
      this.quoteLoading = false
      if (!errorRaw) this.closeAndTo(this.backRouteName)
    },
    async startProcess() {
      this.quoteLoading = true
      if (this.detail.dataSource == '滑油消耗') {
        // 校验是否超年度预算    总金额  付款频次
        // const flag = await this.checkBudgetYear()
        // console.log('11111111111111:' + flag)
        // if (flag) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/supplyCommon/submitAuditGrease',
          { id: this.detail.id },
        )
        this.quoteLoading = false
        if (!errorRaw) this.closeAndTo(this.backRouteName)
        // }
      } else {
        // 校验是否超年度预算    总金额  付款频次
        const flag = await this.checkBudgetYear()
        console.log('11111111111111:' + flag)
        if (flag) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/supplyCommon/submitAudit',
            { id: this.detail.id },
          )
          this.quoteLoading = false
          if (!errorRaw) this.closeAndTo(this.backRouteName)
        }
      }
      this.quoteLoading = false
    },
    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/supplyCommon/getDetailById',
        { Id: this.$route.params.id },
      )
      data.costSubjectId = data.costSubjectId.replace(/[\u4e00-\u9fa5]/g, '')
      this.detail = data
      this.supplierId = data.supplierId
      this.detail.initSubject = {
        id: data.costSubjectId,
        subjectName: data.costSubjectName,
      }
      this.detail.initAccident = {
        id: data.accidentId,
        no: data.accidentNo,
      }
      this.supplierName = data.supplierName
      this.detail.newProcessFlag = data.newProcessFlag
      this.attachmentRecords = data.attachmentRecords
      this.attachmentRecords2 = data.attachmentRecords2
      this.supplyPriceModifyList = data.supplyPriceModifyList.map((s) => {
        return { ...s, vid: s.id, operationType: 0 }
      })
      this.detail.supplyPriceModifyList = this.supplyPriceModifyList
      this.auditParams = data.auditParams
      this.initHandler = {
        id: data.applicantId,
        nickName: data.applicantName,
      }
    },

    async loadCurrency() {
      const { data } = await this.getAsync(
        '/business/common/ship/currencyExchangeRate/page',
        { current: 1, size: 50 },
      )
      this.currencys = data.records.map((c) => {
        return { ccyCode: c.ccyCode, currencyType: c.id }
      })
    },

    createSup() {
      this.initialData = {}
      this.dialog = true
    },
    updateSup() {
      this.initialData = this.selectedSup
      this.dialog = true
    },
    delSup() {
      this.supplyPriceModifyList = this.supplyPriceModifyList.filter(
        (s) => !(s.vid === this.selectedSup.vid),
      )
    },
    async confirm() {
      if (!(await this.$dialog.msgbox.confirm('确定中标?'))) return
      this.supplierId = this.selectedSup.supplierId
      this.supplierName = this.selectedSup.supplierName
      this.detail.currencyId =
        this.selectedSup?.currencyId || this.detail.currencyId
      this.detail.money = this.selectedSup?.money || this.detail.money
      this.detail.moneyUsd = this.selectedSup?.usd
      this.checkOrderNo()
    },
    success(newSup) {
      if (
        this.supplyPriceModifyList.some(
          (s) => s.supplierId === newSup.supplierId,
        )
      ) {
        this.$dialog.message.error('供应商重复')
        return
      }
      this.supplyPriceModifyList.push(newSup)
    },
    // 订单号重复禁止保存 重复后数据清空订单号，禁止保存
    // 1. 订单号查重：同订单号，同船，同供应商
    async checkOrderNo() {
      if (this.detail.id == 'new' || this.detail.id == undefined) {
        this.detail.dataSource = '单次预算'
      }
      if (
        this.detail.shipCode == undefined ||
        this.detail.shipCode == '' ||
        (this.detail.shipCode == undefined) == null
      )
        return
      if (
        this.supplierId == undefined ||
        this.supplierId == '' ||
        (this.supplierId == undefined) == null
      )
        return
      if (
        this.detail.orderNo == undefined ||
        this.detail.orderNo == '' ||
        (this.detail.orderNo == undefined) == null
      )
        return
      if (this.detail.dataSource != '单次预算') return
      const { data } = await this.getAsync(
        '/business/shipAffairs/supplyCommon/page2',
        {
          shipCode: this.detail.shipCode,
          supplierId: this.supplierId,
          orderNo: this.detail.orderNo,
        },
      )
      if (data && data.records.length > 0) {
        let hasId1 = data.records.some((item) => item.id === this.detail.id)
        // 包含当前记录 hasId1 = true 去除当前记录
        console.log(hasId1)
        if (hasId1) {
          data.records = data.records.filter(
            (item) => item.id !== this.detail.id,
          )
        }
        if (data.records.length > 0) {
          this.$dialog.message.error('该供应商订单号重复，请重新输入！')
          this.detail.orderNo = ''
        }
      }
    },
    loadAccidentInfo(accident) {
      console.log(accident)
    },
    async checkBudgetYear() {
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/budgetYear/budgetYearCheckByTypeNew',
        {
          date: this.detail.proposedDate,
          shipCode: this.detail.shipCode,
          subjectId: this.detail.costSubjectId
            .replace(/[\u4e00-\u9fa5]/g, '')
            .replace(/-/g, ''),
          money: this.detail.money,
          type: '1', //预算
          budgetId: this.detail.id,
        },
      )
      if (errorRaw) {
        return false
      }
      if (data) {
        //不超预算 可以提交
        return true
      }
    },
    async genOrderNo() {
      console.log(this.detail)
      if (
        this.detail.shipCode == undefined ||
        this.detail.shipCode == '' ||
        this.detail.shipCode == null
      ) {
        this.detail.orderNo = ''
        return
      }
      if (
        this.detail.costSubjectId == undefined ||
        this.detail.costSubjectId == '' ||
        this.detail.costSubjectId == null
      ) {
        this.detail.orderNo = ''
        return
      }
      if (
        this.detail.id != undefined &&
        this.detail.id != '' &&
        this.detail.id != null
      ) {
        return
      }
      const { data, errorRaw } = await this.getAsync(
        '/business/shipAffairs/supplyCommon/genOrderNo',
        {
          shipCode: this.detail.shipCode,
          costSubjectId: this.detail.costSubjectId
            .replace(/[\u4e00-\u9fa5]/g, '')
            .replace(/-/g, ''),
        },
      )
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
      }
      console.log(data)
      if (data) {
        this.detail.orderNo = data
      }
    },
  },

  mounted() {
    this.loadDetail()
    this.loadCurrency()
  },
}
</script>

<style></style>
