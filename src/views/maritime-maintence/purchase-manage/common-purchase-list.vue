<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :push-params="pushParams"
      :single-select="false"
      use-ship
      use-status
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          :to="{ name: 'common-purchase-detail', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['常规供应审批:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!canDel"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delApply"
          v-permission="['常规供应审批:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!canSubmit"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="submitApply"
          v-permission="['常规供应审批:审批通过']"
        >
          <v-icon left>mdi-send</v-icon>
          审批通过
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applicantId	申请人	string
// applyDate	申请日期	string
// applyRemark	申请说明	string
// attachmentRecords	附件列表	array	CommonAttachment
// auditParams	流程参数	AuditParams	AuditParams
// currencyId	币种id(货币)	integer
// id	物理主键	string
// money	金额	number
// shipCode	船舶编码	string
// status	审批状态 1：未开始(草稿)；2：进行中(已提交)；3已完成；4：驳回	string
// supplierName	供应商名称	string
// supplyItem	供应项目	string
// supplyType	供应类型
export default {
  name: 'common-purchase-list',
  created() {
    this.tableName = '常规供应审批'
    this.reqUrl = '/business/shipAffairs/supplyCommon/page'
    this.headers = [
      { text: '船舶名称', value: 'shipInfo' },
      { text: '供应商', value: 'supplierName' },
      { text: '货币', value: 'currencyName' },
      { text: '金额', value: 'money' },
      { text: '供应项目', value: 'supplyItem' },
      { text: '科目', value: 'costSubjectName' },
      { text: '日期', value: 'applyDate' },
      { text: '申请人', value: 'applicantId' },
      { text: '申请说明', value: 'applyRemark' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.pushParams = { name: 'common-purchase-detail' }
    this.searchDate = {
      label: '申请日期',
      value: 'applyDate',
    }
    this.statuses = [
      '',
      '草稿（未提交）',
      '审批中',
      '通过',
      '驳回（可再次提交）',
    ]
    this.color = ['', '草稿', '审批中', '通过']
  },

  data() {
    return {
      selected: [],
      searchRemain: { supplyType: 0 },
      loading: false,
    }
  },

  computed: {
    canDel() {
      return (
        this.selected.length > 0 &&
        this.selected.every((item) => item.status == 1 || item.status == 4)
      )
    },
    canSubmit() {
      return (
        this.selected.length > 0 &&
        this.selected.every((item) => item?.auditParams?.taskId)
      )
    },
  },

  methods: {
    async delApply() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/supplyCommon/deleteBatch',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = []
      await this.$nextTick()
    },

    async submitApply() {
      if (!(await this.$dialog.msgbox.confirm('确定审批通过所选记录？'))) return
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/flow/task/batchCompleteTaskAndCommentAndSetVar',
        this.selected.map((item) => ({
          adopt: true,
          comment: '',
          params: {},
          taskId: item.auditParams.taskId,
        })),
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`提交成功`)
      await this.$refs.table.loadTableData()
      this.loading = false
      this.selected = []
    },
  },

  mounted() {},
}
</script>

<style></style>
