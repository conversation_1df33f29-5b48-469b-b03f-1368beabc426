<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['常规供应审批:编辑']"
      :title="`常规供应审核-${statuses[detail.status] || '新增'}`"
      :tooltip="isEdit ? detail.supplyItem : '新增'"
      :backRouteName="backRouteName"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template
        v-if="auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit ref="audit" :auditParams="auditParams"></v-audit>
          </v-card-text>
        </v-form>
      </template>
      <template v-slot:基本信息>
        <v-form
          :readonly="detail.status == '2' || detail.status == '3'"
          ref="form"
        >
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.supplyItem"
                  label="供应项目"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-supply-select
                  v-if="!isEdit"
                  :disabled="!detail.shipCode"
                  v-model="detail.supplierId"
                  :rules="[rules.required]"
                  :ship-code="detail.shipCode"
                  @select="
                    (item) => {
                      currencys = item.currency
                    }
                  "
                  :init-selected="initSupply"
                ></v-supply-select>
                <v-text-field
                  label="供应商"
                  v-else
                  dense
                  outlined
                  v-model="detail.supplierName"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="detail.costSubjectId"
                  :rules="[rules.required]"
                  :init-selected="detail.initSubject"
                  :search-dicts="searchDicts"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  :readonly="isEdit"
                  required
                  fuzzy-label="模糊查询"
                  dense
                ></v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.proposedDate"
                  label="拟供货日期"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.applyDate"
                  label="申请日期"
                  use-today
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  readonly
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.applicantId"
                  label="申请人"
                  dense
                  outlined
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <port-select-dialog
                  v-if="!isEdit"
                  @select="
                    (item) => {
                      detail.deliveryPlace = item.portCn
                    }
                  "
                  v-model="detail.delivery"
                  label="供货地点"
                ></port-select-dialog>
                <v-text-field
                  v-else
                  v-model="detail.deliveryPlace"
                  label="供货地点"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.currencyId"
                  :items="currencys"
                  item-text="ccyCode"
                  item-value="currencyType"
                  label="币种"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  :readonly="isEdit"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-if="isJPY"
                  @change="() => (detail.money = Math.round(detail.money))"
                  v-model="detail.money"
                  label="采购金额"
                  type="number"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="detail.money"
                  label="采购金额"
                  type="number"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.supplyCycle"
                  :items="[
                    { text: '年度', value: 0 },
                    { text: '季度', value: 1 },
                    { text: '临时', value: 2 },
                  ]"
                  label="供货周期"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="机务主管——供应理由说明"
                  v-model="detail.applyRemark"
                  :rules="[rules.required]"
                  required
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import portSelectDialog from '../components/port-select-dialog.vue'
export default {
  components: { portSelectDialog },
  mixins: [currencyHelper],
  name: 'common-purchase-detail',
  created() {
    this.backRouteName = 'common-purchase-list'
    this.subtitles = ['基本信息']
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.statuses = [
      '',
      '草稿（未提交）',
      '审批中',
      '通过',
      '驳回（可再次提交）',
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
  },
  data() {
    return {
      searchObj2: {},
      currencys: [],
      attachmentRecords: [],
      auditParams: {},
      supplyPriceModifyList: [],
      detail: {
        attachmentIds: [],
        applicantId: this.$local.data.get('userInfo').nickName,
      },
      rules: {
        required: (v) => v === false || !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      initSupply: {},
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    isJPY() {
      const JPYID = this.currencyInfo.find((i) => i.ccyCode === 'JPY')?.id
      return this.detail.currencyId === JPYID
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/shipAffairs/supplyCommon/saveOrUpdateSupplyCommon'
      const { errorRaw, data } = await this.postAsync(url, {
        applicantId: this.$local.data.get('userInfo').nickName,
        ...this.detail,
        supplyType: 0,
      })
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/supplyCommon/submitAudit',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/supplyCommon/getDetailById',
        { Id: this.$route.params.id },
      )
      this.detail = data
      this.detail.initSubject = {
        id: data.costSubjectId,
        subjectName: data.costSubjectName,
      }
      this.attachmentRecords = data.attachmentRecords
      this.auditParams = data.auditParams
      this.initSupply = {
        id: data.supplierId,
        name: data.supplierName,
      }
    },

    async loadCurrency() {
      const { data } = await this.getAsync(
        '/business/common/ship/currencyExchangeRate/page',
        { current: 1, size: 50 },
      )
      this.currencys = data.records.map((c) => {
        return { ccyCode: c.ccyCode, currencyType: c.id }
      })
    },
  },

  mounted() {
    this.loadDetail()
    this.loadCurrency()
  },
}
</script>

<style></style>
