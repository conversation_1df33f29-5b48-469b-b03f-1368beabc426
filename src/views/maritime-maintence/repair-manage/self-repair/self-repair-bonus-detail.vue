<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['自修奖申请:编辑']"
      :title="`自修奖申请-${isEdit ? detail.applyCode : '新增'}`"
      :tooltip="isEdit ? detail.applyCode : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        !detail.auditParams || detail.auditParams.taskId || detail.status == '4'
      "
      :can-save="this.detail.status != 2 && this.detail.status != 3"
      @save="save"
      @submit="submit"
    >
      <template
        v-if="auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:基本信息>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :disabled="isEdit"
                  v-model="detail.shipCode"
                  dense
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  label="申请部门"
                  v-model="detail.department"
                  outlined
                  dense
                  :disabled="isEdit"
                  :items="['甲板部', '轮机部']"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.applicantId"
                  label="申请人"
                  dense
                  outlined
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  readonly
                  v-model="detail.captainName"
                  label="船长"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="申请时间"
                  v-model="detail.applyTime"
                  outlined
                  dense
                  use-today
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.userId"
                  :init-user="initUser"
                  outlined
                  dense
                  :disabled="isEdit"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-autocomplete
                  label="年份"
                  v-model="detail.year"
                  outlined
                  dense
                  :items="yearList"
                  :rules="[rules.required]"
                  :disabled="isEdit"
                  clearable
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :items="quList"
                  v-model="detail.quarter"
                  label="季度/临时"
                  :disabled="isEdit"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="detail.remark"
                  dense
                  outlined
                  label="备注"
                  :disabled="
                    !!detail.status && detail.status != 1 && detail.status != 4
                  "
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="4">
                申请金额(人民币):{{ computedApplyBonus }}
              </v-col>
              <v-col cols="12" md="4">
                标准金额(人民币):{{ computedStandardBonus }}
              </v-col>
              <v-col cols="12" md="4">
                批准金额(人民币):{{ computedApprovedBonus }}
              </v-col>
              <v-col class="caption" cols="12" v-if="detail.status == 2">
                注:请单击批准金额输入金额
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </template>
      <template
        v-if="!isEdit || detail.status == 1 || detail.status == 4"
        v-slot:申请条目明细按钮
      >
        <!-- <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          :disabled="!detail.shipCode || !detail.department"
          @click.stop="addItem"
          v-permission="['申请条目明细:手动添加']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          手动添加
        </v-btn> -->
        <v-btn
          :disabled="!detail.shipCode"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="dialog = true"
          v-permission="['申请条目明细:选择完工项']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          :disabled="!detail.shipCode || !detail.department"
          @click.stop="dialog = true"
          v-permission="['申请条目明细:选择完工项']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择自修项目
        </v-btn> -->
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['申请条目明细:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:申请条目明细>
        <v-form ref="form2">
          <v-table-list
            :headers="headers"
            :items="repairList"
            v-model="selected"
            item-key="id"
          >
            <template v-slot:[`item.name`]="{ item }">
              {{ item.name || '手动添加' }}
            </template>
            <template v-slot:[`item.description`]="{ item }">
              {{ item.description || item.applyReasons }}
            </template>
            <template
              v-if="!detail.status || detail.status == 1 || detail.status == 4"
              v-slot:[`item.applyBonus`]="{ item }"
            >
              <v-text-field
                v-model="item.applyBonus"
                label="金额"
                type="number"
                single-line
                dense
                :rules="[rules.required, rules.aboveZero]"
              ></v-text-field>
            </template>
            <template
              v-if="!detail.status || detail.status == 1 || detail.status == 4"
              v-slot:[`item.remark`]="{ item }"
            >
              <!-- <v-text-field
                v-model="item.remark"
                label="备注"
                single-line
                dense
              ></v-text-field> -->

              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    @click="editRemark(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.remark"
                    label="备注"
                    single-line
                    dense
                  ></v-text-field>
                </template>
                <span>{{ item.remark }}</span>
              </v-tooltip>
            </template>
            <template
              v-if="detail.status == 2"
              v-slot:[`item.approvedBonus`]="{ item }"
            >
              <v-text-field
                v-model="item.approvedBonus"
                label="金额"
                type="number"
                single-line
                counter
                dense
                :rules="[rules.required, rules.notMinus]"
              ></v-text-field>
            </template>
          </v-table-list>
        </v-form>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="(ids) => (detail.attachmentIds = ids)"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-slot:奖金分配情况>
        <v-col cols="12" md="4">
          已分配金额:{{ allBonus }},待分配金额{{ approvedBonus - allBonus }}
        </v-col>
        <v-table-list
          :headers="assignHeaders"
          :items="assignList"
          v-model="selectedAssign"
        ></v-table-list>
      </template>
      <template v-slot:奖金分配情况按钮>
        <v-btn
          v-if="detail.businessStatus === '审批通过'"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="addAssign"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增分配
        </v-btn>
        <v-btn
          v-if="detail.businessStatus === '审批通过'"
          outlined
          tile
          small
          :disabled="!selectedAssign"
          color="warning"
          class="mx-1"
          @click.stop="editAssign"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          修改
        </v-btn>

        <v-btn
          v-if="detail.businessStatus === '审批通过'"
          :disabled="!selectedAssign"
          outlined
          tile
          small
          color="error"
          class="mx-1"
          @click.stop="delAssign"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          v-if="
            detail.businessStatus === '已完成奖金分配' ||
            detail.businessStatus === '岸端已确认'
          "
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          :href="`/api/business/shipAffairs/repairBonus/excelRepairBonus?id=${detail.id}`"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出分配明细
        </v-btn>
        <v-btn
          v-if="detail.businessStatus === '已完成奖金分配'"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click="confirmBonus"
        >
          <v-icon left>mdi-check</v-icon>
          确认分配
        </v-btn>
      </template>
    </v-detail-view>
    <!-- <bonus-select-dialog
      v-model="dialog"
      :ship-code="detail.shipCode"
      :initialList="repairList"
      @update="updateList"
    ></bonus-select-dialog> -->
    <!-- <repair-dialog-select
      v-model="dialog"
      :ship-code="detail.shipCode"
      :initialList="repairList"
      @update="updateList"
    ></repair-dialog-select> -->
    <repairDialogSelectNew
      v-model="dialog"
      @update="pushRepairNew"
      :shipCode="detail.shipCode"
    ></repairDialogSelectNew>
    <bonus-new-item
      v-model="dialog2"
      :initialData="initialData"
      @success="pushItem"
    ></bonus-new-item>
    <assign-user
      :initialData="initAssign"
      v-model="dialog3"
      @success="loadAssign"
    ></assign-user>
    <v-dialog v-model="dialog1" max-width="600">
      <template v-slot:default="dialog1">
        <v-card style="height: 300px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import AssignUser from './private/assign-user.vue'
import BonusNewItem from './private/bonus-new-item.vue'
// import repairDialogSelect from './private/repair-dialog-select.vue'
import repairDialogSelectNew from './private/repair-dialog-select-new.vue'

// import bonusSelectDialog from './private/bonus-select-dialog.vue'
// applyBonus	自修奖申请金额	number
// applyReasons	若不是从已完工自修项目中选择，需填写此项奖金申请原因，与“自修项目ID”列必选其中一列填写	string
// approvedBonus	自修奖被批准的金额	number
// id		string
// repairBonusId	自修奖金申请单id	string
// repairCompleteId	自修完工表包含的项目id	string
// standardBonus	自修奖标准金额
export default {
  // components: { bonusSelectDialog, BonusNewItem, AssignUser },
  components: { BonusNewItem, AssignUser, repairDialogSelectNew },
  name: 'self-repair-bonus-detail',
  created() {
    this.backRouteName = 'self-repair-bonus'
    this.statuses = ['未提交', '审批中', '审批通过', '已驳回', '已完工']
    this.assignHeaders = [
      { text: '船员', value: 'nickName' },
      { text: '分配金额', value: 'distributedBonus' },
    ]
    this.rangeArray(2000, 2070)
    this.quList = [
      { text: '临时', value: '0' },
      { text: '第一季度', value: '1' },
      { text: '第二季度', value: '2' },
      { text: '第三季度', value: '3' },
      { text: '第四季度', value: '4' },
    ]
  },
  data() {
    return {
      subtitles: ['基本信息', '申请条目明细'],
      detail: {
        list: [],
        captainName: '',
        applicantId: this.$local.data.get('userInfo').nickName,
      },
      auditParams: {},
      repairList: [],
      completeList: [],
      initialData: {},
      dialog: false,
      selected: false,
      dialog2: false,
      headers: [
        // { text: '自修项目名', value: 'name' },
        // { text: '项目描述', value: 'description' },
        // { text: '标准奖金', value: 'standardBonus' },
        { text: '自修项目名', value: 'name' },
        { text: '项目描述', value: 'description' },
        { text: '标准奖金', value: 'standardBonus' },
        { text: '申请金额', value: 'applyBonus', width: 140 },
        { text: '备注', value: 'remark' },
      ],
      assignList: [],
      allBonus: 0,
      approvedBonus: 0,
      dialog3: false,
      selectedAssign: false,
      initAssign: {},
      initUser: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        notMinus: (v) => v >= 0 || '不得为负',
        aboveZero: (v) => v > 0 || '必须大于0',
      },
      dialog1: false,
      editRemarkDetails: {},
    }
  },
  watch: {
    'detail.shipCode'(val) {
      if (val != null) {
        this.getCaptainName(val)
        if (this.detail.department != null) {
          this.getApplicantId(this.detail.department)
        } else {
          this.detail.applicantId = ''
        }
      } else {
        this.detail.captainName = ''
        this.detail.applicantId = ''
      }
    },
    'detail.department'(val) {
      if (!this.detail.shipCode) {
        return
      }
      if (val != null) {
        this.getApplicantId(val)
      } else {
        this.detail.applicantId = ''
      }
    },
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    addList() {
      return this.repairList
        .filter(
          (i) =>
            !this.detail.list.find(
              (o) => o.id === i.id || o.applyBonus != i.applyBonus,
            ),
        )
        .map((i) => {
          return {
            repairBonusId: this.$route.params.id,
            applyReasons: i.applyReasons,
            applyBonus: i.applyBonus,
            standardBonus: i.standardBonus,
            [parseInt(i.id) < 1000 ? false : 'repairCompleteId']:
              i.repairCompleteId,
          }
        })
    },
    delList() {
      return this.detail.list
        .filter((i) => !this.repairList.find((o) => o.id === i.id))
        .map((i) => i.id)
    },
    computedApplyBonus() {
      return this.repairList.reduce(
        (c, R) => c + parseFloat(R.applyBonus == undefined ? 0 : R.applyBonus),
        0,
      )
    },
    computedStandardBonus() {
      return this.repairList.reduce(
        (c, R) =>
          c + parseFloat(R.standardBonus == undefined ? 0 : R.standardBonus),
        0,
      )
    },
    computedApprovedBonus() {
      return this.repairList.reduce(
        (c, R) =>
          c + parseFloat(R.approvedBonus == undefined ? 0 : R.approvedBonus),
        0,
      )
    },
  },

  methods: {
    editRemark(item) {
      // console.log(item)
      this.editRemarkDetails = item
      this.dialog1 = true
    },
    saveRemark() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1 = false
    },
    rangeArray(start, end) {
      const yearNow = new Date()
      this.detail.year = yearNow.getFullYear() + ''
      let length = end - start + 1
      let step = start - 1
      this.yearList = Array.from({ length: length }, () => {
        step++
        return step + ''
      })
    },
    async getCaptainName(val) {
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/repairApply/getCaptainName`,
        { shipCode: val, post: '船长' },
      )

      if (errorRaw) {
        this.detail.captainName = ''
        return
      }
      if (data) {
        this.detail.captainName = data
      } else {
        this.detail.captainName = ''
      }
    },
    async getApplicantId(val) {
      if (val == '甲板部') {
        val = '大副'
      }
      if (val == '轮机部') {
        val = '轮机长'
      }
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/repairApply/getCaptainName`,
        { shipCode: this.detail.shipCode, post: val },
      )

      if (errorRaw) {
        this.detail.applicantId = ''
        return
      }
      if (data) {
        this.detail.applicantId = data
      } else {
        this.detail.applicantId = ''
      }
    },
    async save(goBack, notMove = false) {
      // 无id 未提交 已驳回
      if (!this.$refs.form.validate()) return false
      if (!this.$refs.form2.validate()) return false
      if (!this.isEdit) {
        // this.repairList.forEach((item) => {
        //   item['repairCompleteId'] = item.id
        // })
        this.detail.list = this.repairList.map((i) => {
          return { ...i, id: undefined }
        })
        this.detail.userId = this.$local.data.get('userInfo').id
        const { errorRaw, data } = await this.postAsync(
          '/business/shipAffairs/repairBonus/saveRepairBonus',
          this.detail,
        )
        if (errorRaw) return false
        if (notMove) return data
      } else if (this.detail.status == 4 || this.detail.status == 1) {
        const re1 = await this.postAsync(
          '/business/shipAffairs/repairBonus/modifyBatchRepairBonusItem',
          this.repairList
            .filter(
              (i) =>
                !this.detail.list.some(
                  (o) => o.id === i.id && o.applyBonus == i.applyBonus,
                ),
            )
            .map((i) => {
              return {
                repairBonusId: this.$route.params.id,
                applyReasons: i.applyReasons,
                applyBonus: i.applyBonus,
                standardBonus: i.standardBonus,
                repairCompleteId: i.repairCompleteId,
                remark: i.remark,
                [parseInt(i.id) < 1000 ? 'qwd' : 'id']: i.id,
              }
            }),
        )
        const re2 = await this.postAsync(
          '/business/shipAffairs/repairBonus/deleteRepairBonusItem',
          this.delList,
        )
        const re3 = await this.postAsync(
          '/business/shipAffairs/repairBonus/modifyRepairBonus',
          this.detail,
        )
        if (re1.errorRaw || re2.errorRaw || re3.errorRaw) return false
        if (notMove) return this.detail.id
      }
      // 审批中，，提交审批流情况
      if (this.detail.status == 2) {
        if (this.repairList.some((i) => i.approvedBonus == 0)) {
          if (
            !(await this.$dialog.msgbox.confirm(
              '存在批准金额为0的申请条目,是否继续提交?',
            ))
          )
            return
        }
        const re3 = await this.postAsync(
          '/business/shipAffairs/repairBonus/modifyRepairBonus',
          this.detail,
        )
        if (re3.errorRaw) return false
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/repairBonus/addApproveBonus',
          this.repairList.map((i) => {
            return { id: i.id, approvedBonus: i.approvedBonus }
          }),
        )
        if (errorRaw) return false
        if (notMove) return this.detail.id
      }
      // 审批结束
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (this.repairList.length === 0) {
        this.$dialog.message.error('请添加申请明细')
        return
      }
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/repairBonus/process/start',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        // 驳回重新提交
        if (this.detail.status == '4') {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/repairBonus/process/start',
            { id: data },
          )
          if (!errorRaw) goBack()
        }
        //      最后一个人审批时候判断是否超年度预算,修改为机务主管审批时校验
        if (
          this.detail.businessStatus == '机务主管' &&
          this.$refs.audit.adopt
        ) {
          const flag = await this.checkBudgetYear()
          console.log('11111111111111:' + flag)
          if (flag) {
            // console.log('11111111111111:' + flag)
            const error = await this.$refs.audit.submit()
            if (!error) goBack()
          }
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },
    async checkBudgetYear() {
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs//repairBonus/genSupplyCheck',
        {
          // date: this.detail.happenDate,
          // shipCode: this.detail.shipCode,
          // subjectId: this.detail.initSubject.id,
          // money: this.detail.money,
          // type: '2', //生成费用项目
          budgetId: this.detail.id,
        },
      )
      if (errorRaw) {
        return false
      }
      if (data) {
        //不超预算 可以提交
        return true
      }
      // this.$dialog.message.warning('当前')
    },
    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/shipAffairs/repairBonus/getDetailId/${this.$route.params.id}`,
      )
      this.detail = data
      this.initUser = { id: data.userId, nickName: data.userName }
      this.repairList = [
        ...data.list.map((i) => {
          return Object.assign({}, i)
        }),
      ]
      this.auditParams = data.auditParams
      if (this.detail.status != 1 && this.detail.status != 4) {
        // this.headers.push({ text: '批准金额', value: 'approvedBonus' })
        this.headers.splice(4, 0, { text: '批准金额', value: 'approvedBonus' })
      }
      if (
        this.detail.businessStatus === '审批通过' ||
        this.detail.businessStatus === '已完成奖金分配' ||
        this.detail.businessStatus === '岸端已确认'
      ) {
        await this.loadAssign()
      }
    },
    async loadAssign() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/repairBonus/getDetailDistribution',
        { id: this.detail.id },
      )
      this.assignList = data.list
      this.approvedBonus = data.approvedBonus
      this.allBonus = data.allBonus
      this.detail.status = data.status
      this.selectedAssign = false
    },
    async addItem() {
      this.initialData = {}
      this.dialog2 = true
    },
    async delItem() {
      this.repairList = this.repairList.filter((i) => i.id !== this.selected.id)
    },
    updateList(list) {
      this.repairList = list
    },
    async success() {
      await this.loadCompleteDetail()
    },
    pushItem(item) {
      if (!this.repairList.find((i) => i.id === item.id)) {
        this.repairList = [...this.repairList, item]
      }
    },
    async confirmBonus() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/repairBonus/checkRepairBonus',
        { id: this.detail.id },
      )
      if (!data) return
      await this.loadDetail()
    },
    addAssign() {
      this.initAssign = {}
      this.dialog3 = true
    },
    editAssign() {
      this.initAssign = { ...this.selectedAssign }
      this.dialog3 = true
    },
    async delAssign() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/repairBonus/deleteDistribution',
        [this.selectedAssign.id],
      )
      if (!errorRaw) {
        await this.loadAssign()
      }
    },
    pushRepairNew(newItem) {
      if (!newItem || !newItem.id) {
        return
      }
      // console.log(newItem)
      // let newI = newItem
      // newItem.id = newItem.id + '' + this.repairList.length
      // console.log(newItem)
      // newItem.id = newItem.id + '' + this.repairList.length
      // const timestamp = Date.now() // 获取当前时间戳
      // const randomSuffix = Math.floor(Math.random() * 1000) // 生成一个0到999之间的随机数
      // newItem.id = `${timestamp}-${randomSuffix}` // 生成新的id
      // this.repairList.push(newItem)
      let aaa = { ...newItem }
      aaa.repairCompleteId = aaa.id
      aaa.id = aaa.id + this.repairList.length // 生成新的id ，防止重复项目id选中
      this.$set(this.repairList, this.repairList.length, aaa)
      console.log(this.repairList)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
