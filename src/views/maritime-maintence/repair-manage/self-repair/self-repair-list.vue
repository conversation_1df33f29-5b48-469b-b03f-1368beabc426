<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.department"
            label="部门"
            outlined
            dense
            clearable
            :items="['轮机部', '甲板部']"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.stateFlag"
            label="状态"
            outlined
            dense
            clearable
            :items="stateFlagList"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'self-repair-detail', params: { id: 'new' } }"
          v-permission="['自修管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['自修管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applyTime	申请维修时间	string
// department	部门（甲板部和轮机部）	string
// deptId	船员部门id	string
// id	物理主键	string
// remarks	自修情况备注说明	string
// shipCode	船舶编码	string
// stateFlag	自修项目状态，0: 未提交、1: 审批中、2: 审批通过、3: 已驳回 4:已完工,可用值:NO_AUDIT,HAVE_IN_HAND,SUCCESS,REJECT,COMPLETE	string
// userId	船员id	string
export default {
  name: 'self-repair-list',
  created() {
    this.tableName = '自修申请列表'
    this.reqUrl = '/business/shipAffairs/repairApply/page'
    this.headers = [
      { text: '自修单编号', value: 'applyCode' },
      { text: '船舶', value: 'shipName' },
      { text: '部门', value: 'department' },
      { text: '申请时间', value: 'applyTime' },
      { text: '备注', value: 'remarks' },
      { text: '状态', value: 'stateFlag' },
    ]
    this.searchDate = {
      label: '申请时间',
      value: 'applyTime',
    }
    this.pushParams = { name: 'self-repair-detail' }
    this.stateFlagList = [
      { text: '未提交', value: 'NO_AUDIT' },
      { text: '审批中', value: 'HAVE_IN_HAND' },
      { text: '审批通过', value: 'SUCCESS' },
      { text: '已驳回', value: 'REJECT' },
      { text: '已完工', value: 'COMPLETE' },
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: { stateFlag: 'HAVE_IN_HAND' },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/repairApply/deleteRepairApply/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
