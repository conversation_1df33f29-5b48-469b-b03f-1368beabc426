<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :single-select="false"
      :search-remain="searchObj"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.department"
            label="部门"
            outlined
            dense
            clearable
            :items="['轮机部', '甲板部']"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.year"
            label="年份"
            outlined
            dense
            clearable
            :items="yearList"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.quarter"
            label="季度"
            outlined
            dense
            clearable
            :items="['临时', '第一季度', '第二季度', '第三季度', '第四季度']"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            label="状态"
            outlined
            dense
            clearable
            :items="businessStatus"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.group"
            label="小组"
            outlined
            dense
            clearable
            :items="group"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.company"
            label="船员公司"
            outlined
            dense
            clearable
            :items="company"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-menu
            v-model="datesMenu2"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                ref="dates2"
                :value="dateRangeText2"
                :label="'申请日期'"
                append-icon="mdi-calendar"
                outlined
                dense
                readonly
                clearable
                @click:clear="dates2 = []"
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <vc-date-picker
              v-model="dates2"
              mode="date"
              is-range
            ></vc-date-picker>
          </v-menu>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!canPDF"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="dowloadPDF"
          v-permission="['自修奖分配:批量导出分配明细']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          导出分配明细
        </v-btn>
        <a v-if="true" :href="downPDF" ref="downPDFHref"></a>
        <v-btn
          :loading="loading"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="downloadExcel"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
      </template>
      <!-- <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'self-repair-bonus-detail', params: { id: 'new' } }"
          v-permission="['自修奖申请:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['自修奖申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template> -->
      <template v-slot:[`item.quarter`]="{ item }">
        <v-chip v-if="item.quarter == 0">临时</v-chip>
        <v-chip v-if="item.quarter == 1">第一季度</v-chip>
        <v-chip v-if="item.quarter == 2">第二季度</v-chip>
        <v-chip v-if="item.quarter == 3">第三季度</v-chip>
        <v-chip v-if="item.quarter == 4">第四季度</v-chip>
      </template>
      <template v-slot:[`item.approvedBonus`]="{ item }">
        {{ Number(item.approvedBonus).toFixed(2) }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applyCode	申请单号;根据编码规则生成	string
// applyTime	自修奖申请时间	string
// department	部门（甲板部和轮机部）	string
// id	物理主键	string
// shipCode	船code	string
// shipName	船舶名	string
// status	自修奖申请状态： 0: 未提交，1: 审批中，2: 审批通过， 3: 已驳回 4: 已完成奖金分配,可用值:NO_AUDIT,HAVE_IN_HAND,SUCCESS,REJECT,COMPLETE	string
// userDeptId	船员部门id	string
// userId	船员id	string
export default {
  name: 'self-bonus-distribute-list',
  created() {
    this.tableName = '自修奖分配'
    this.reqUrl = '/business/shipAffairs/repairBonus/pageList'
    this.headers = [
      { text: '船舶', value: 'shipName' },
      { text: '申请单号', value: 'applyCode' },
      { text: '自修奖申请时间', value: 'applyTime' },
      { text: '部门', value: 'department' },
      // { text: '船舶', value: 'shipName' },
      { text: '年份', value: 'year' },
      { text: '季度', value: 'quarter' },
      { text: '金额', value: 'approvedBonus' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.pushParams = { name: 'self-bonus-distribute-detail' }
    this.businessStatus = [
      { text: '审批通过', value: 'SUCCESS' },
      { text: '岸端已确认', value: 'CONFIRM' },
      { text: '岸端已退回', value: 'REJECT' },
      { text: '已完成奖金分配', value: 'COMPLETE' },
    ]
    this.group = [
      { text: '管船一组', value: '1595613067957829633' },
      { text: '管船二组', value: '1595613104641212418' },
      { text: '管船三组', value: '1595613131748999170' },
      { text: '管船四组', value: '1664201587410251777' },
      { text: '山东船管', value: '1595599002623606786' },
    ]
    this.company = [
      { text: '山东省海丰船舶管理有限公司', value: '3607' },
      { text: '上海海丰海事服务有限公司', value: '3604' },
      { text: '厦门新海丰船舶服务有限公司', value: '8938' },
      { text: '中介公司A', value: '中介公司A' },
      { text: '上海森海', value: '2000077975' },
      { text: '青岛英航', value: '2000063264' },
      { text: '青岛连航', value: '9997' },
      { text: '青岛成舟', value: '9996' },
    ]
  },

  data() {
    return {
      selected: [],
      searchObj: {
        status: 3,
        department: '',
        quarter: '',
        businessStatus: '',
        group: '',
        company: '',
        shipCode: '',
        fromTime: '',
        toTime: '',
        year: '',
      },
      dates2: [],
      downPDF: '',
      yearList: this.generateYearList(),
    }
  },
  watch: {
    dates2: {
      handler() {
        if (this.dates2?.start && this.dates2?.end) {
          this.datesMenu2 = false
          this.searchObj.fromTime = this.dates2?.start
            .toISOString()
            .split('T')[0]
          this.searchObj.toTime = this.dates2?.end.toISOString().split('T')[0]
          this.$refs.table.loadTableData()
        } else {
          this.searchObj.fromTime = null
          this.searchObj.toTime = null
          this.$refs.table.loadTableData()
        }
      },
    },
  },
  computed: {
    canPDF() {
      return (
        this.selected.length > 0 &&
        this.selected.every(
          (item) =>
            item.businessStatus == '已完成奖金分配' ||
            item.businessStatus == '岸端已确认',
        )
      )
    },
    dateRangeText2() {
      return this.dates2?.start && this.dates2?.end
        ? `${this.dates2.start.toLocaleDateString()} 至 ${this.dates2?.end.toLocaleDateString()}`
        : ''
    },
  },
  methods: {
    generateYearList() {
      const currentYear = new Date().getFullYear()
      const startYear = currentYear - 3
      const endYear = currentYear + 1
      return Array.from(
        { length: endYear - startYear + 1 },
        (_, i) => startYear + i,
      )
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/repairBonus/deleteRepairBonus/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
    async dowloadPDF() {
      // const ids = this.selected.map((item) => item.id)
      // const { data } = await this.postAsync(
      //   '/business/shipAffairs/costOrder/createCostOrderPDFList',
      //   ids,
      // )
      // if (data) {
      // console.log(data)
      this.selected.forEach((item) => {
        this.dowloadPDFs(item)
      })
      // }
    },
    async dowloadPDFs(item) {
      this.downPDF = `/api/business/shipAffairs/repairBonus/excelRepairBonus?id=${item.id}`
      console.log(this.$refs.downPDFHref)

      const link = this.$refs.downPDFHref
      link.href = this.downPDF
      link.download = this.extractFilename(this.downPDF)
      link.style.display = 'none'
      document.body.appendChild(link)

      // 模拟点击<a>标签以触发下载
      link.click()
    },
    extractFilename(url) {
      return url.substring(url.lastIndexOf('/') + 1)
    },
    async downloadExcel() {
      this.loading = true
      let params = { ...this.$refs.table.searchRemain }
      params = {
        ...params,
        shipCode: this.$refs.table.ship,
        department: this.searchObj.department,
        quarter: this.searchObj.quarter,
        businessStatus: this.searchObj.businessStatus,
        group: this.searchObj.group,
        company: this.searchObj.company,
        fromTime: this.dates2?.start?.toISOString()?.split('T')?.[0],
        toTime: this.dates2?.end?.toISOString()?.split('T')?.[0],
      }
      // 打印检查参数是否收集正确
      console.log('导出参数:', params)
      await this.getBlobDownload(
        '/business/shipAffairs//repairBonus/excelExport',
        params,
        // 时间戳后四位
        `自修奖分配-${new Date().getTime().toString().slice(-4)}.xlsx`,
      )
      this.loading = false
    },
  },

  mounted() {
    if (this.$route.query.businessStatus != undefined) {
      this.searchObj.businessStatus = this.$route.query.businessStatus
    }
  },
}
</script>

<style></style>
