<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :search-remain="searchObj"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            :items="statusMap"
            label="审批状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.department"
            label="部门"
            outlined
            dense
            clearable
            :items="['轮机部', '甲板部']"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.year"
            label="年份"
            outlined
            dense
            clearable
            :items="yearList"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.quarter"
            label="季度"
            outlined
            dense
            clearable
            :items="['临时', '第一季度', '第二季度', '第三季度', '第四季度']"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.status"
            label="状态"
            outlined
            dense
            clearable
            :items="status"
          ></v-select>
        </v-col> -->
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'self-repair-bonus-detail', params: { id: 'new' } }"
          v-permission="['自修奖申请:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="
            !selected ||
            selected.businessStatus == '审批通过' ||
            selected.status == '2'
          "
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['自修奖申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="!selected || selected.businessStatus !== '审批通过'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="disuse"
          v-permission="['自修奖申请:废弃']"
        >
          <v-icon left>mdi-cancel</v-icon>
          废弃
        </v-btn>
      </template>
      <template v-slot:[`item.quarter`]="{ item }">
        <v-chip v-if="item.quarter == 0">临时</v-chip>
        <v-chip v-if="item.quarter == 1">第一季度</v-chip>
        <v-chip v-if="item.quarter == 2">第二季度</v-chip>
        <v-chip v-if="item.quarter == 3">第三季度</v-chip>
        <v-chip v-if="item.quarter == 4">第四季度</v-chip>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses2[item.status] }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applyCode	申请单号;根据编码规则生成	string
// applyTime	自修奖申请时间	string
// department	部门（甲板部和轮机部）	string
// id	物理主键	string
// shipCode	船code	string
// shipName	船舶名	string
// status	自修奖申请状态： 0: 未提交，1: 审批中，2: 审批通过， 3: 已驳回 4: 已完成奖金分配,可用值:NO_AUDIT,HAVE_IN_HAND,SUCCESS,REJECT,COMPLETE	string
// userDeptId	船员部门id	string
// userId	船员id	string
export default {
  name: 'self-repair-bonus',
  created() {
    this.tableName = '自修奖申请'
    this.reqUrl = '/business/shipAffairs/repairBonus/page'
    this.headers = [
      { text: '船舶', value: 'shipName' },
      { text: '申请单号', value: 'applyCode' },
      { text: '自修奖申请时间', value: 'applyTime' },
      { text: '部门', value: 'department' },
      // { text: '船舶', value: 'shipName' },
      { text: '年份', value: 'year' },
      { text: '季度', value: 'quarter' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.searchDate = {
      label: '申请时间',
      interval: true,
    }
    this.pushParams = { name: 'self-repair-bonus-detail' }
    this.status = [
      { text: '未提交', value: 'NO_AUDIT' },
      { text: '机务主管审核', value: 'MAINTENANCE_AUDIT' },
      { text: '机务组长审核', value: 'MAINTENANCE_AUDIT2' },
      { text: '船东审核', value: 'OWNER_AUDIT' },
      { text: '审批通过', value: 'SUCCESS' },
      { text: '已驳回', value: 'REJECT' },
      { text: '已完成奖金分配', value: 'COMPLETE' },
    ]
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
      { text: '废弃', value: '5' },
    ]
    this.statuses2 = ['暂无审批', '草稿', '审批中', '已审批', '已驳回', '废弃']
    this.statusColors = ['info', '', 'warning', 'success', 'error', 'error']
  },

  data() {
    return {
      selected: false,
      searchObj: { isMe: true, status: '2' },
      yearList: this.generateYearList(),
    }
  },

  methods: {
    generateYearList() {
      const currentYear = new Date().getFullYear()
      const startYear = currentYear - 3
      const endYear = currentYear + 1
      return Array.from(
        { length: endYear - startYear + 1 },
        (_, i) => startYear + i,
      )
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/repairBonus/deleteRepairBonus/${this.selected.id}`,
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
    async disuse() {
      if (!(await this.$dialog.msgbox.confirm('确定废弃此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/repairBonus/disuseRepairBonus/${this.selected.id}`,
      )
      if (errorRaw) {
        this.$dialog.message.error(`废弃失败，请重试`)
        return
      }
      this.$dialog.message.success(`废弃成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
