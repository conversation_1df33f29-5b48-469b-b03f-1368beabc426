<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['自修管理:编辑']"
      :title="`自修申请-${detail.applyCode || '新增'}-${
        detail.stateFlag || '未提交'
      }`"
      :tooltip="isEdit ? detail.applyCode : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
    >
      <template
        v-if="auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:基本信息>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :disabled="isEdit"
                  v-model="detail.shipCode"
                  dense
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  label="申请部门"
                  v-model="detail.department"
                  outlined
                  dense
                  :disabled="isEdit"
                  :items="['甲板部', '轮机部']"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="申请时间"
                  v-model="detail.applyTime"
                  outlined
                  dense
                  use-today
                  readonly
                  :disabled="isEdit"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  :disabled="isEdit"
                  outlined
                  label="备注"
                  dense
                  v-model="detail.remarks"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </template>
      <template
        v-if="
          !isEdit ||
          detail.stateFlag === '未提交' ||
          detail.stateFlag === '已驳回'
        "
        v-slot:修理项目详情按钮
      >
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          :disabled="!detail.shipCode"
          @click.stop="dialog = true"
          v-permission="['修理项目详情:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['修理项目详情:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-else-if="detail.stateFlag === '审批通过'" v-slot:完工详情按钮>
        <v-btn
          outlined
          :disabled="!repair"
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="finish"
          v-permission="['修理项目详情:完工']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          完工
        </v-btn>
      </template>
      <template v-slot:修理项目详情>
        <v-table-list
          :show-select="
            detail.stateFlag === '审批通过' ||
            !isEdit ||
            detail.stateFlag === '未提交' ||
            detail.stateFlag === '已驳回'
          "
          :headers="headers"
          :items="repairList"
          v-model="selected"
        >
          <template v-slot:[`item.property`]="{ item }">
            {{ item.property == 0 ? '公用' : '专属' }}
          </template>
        </v-table-list>
      </template>
      <template v-slot:完工详情>
        <v-table-list
          :headers="completeHeaders"
          :items="completeList"
          v-model="repair"
        >
          <template v-slot:[`item.property`]="{ item }">
            {{ item.property === '0' ? '公用' : '专属' }}
          </template>
          <template v-slot:[`item.componentOutId`]="{ item }">
            <router-link
              v-if="item.componentId"
              :to="{
                name: 'spare-out-detail',
                params: { id: item.componentId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
          <template v-slot:[`item.greaseOutId`]="{ item }">
            <router-link
              v-if="item.greaseId"
              :to="{
                name: 'soil-out-detail',
                params: { id: item.greaseId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
          <template v-slot:[`item.materialOutId`]="{ item }">
            <router-link
              v-if="item.materialId"
              :to="{
                name: 'materials-out-detail',
                params: { id: item.materialId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <repair-dialog-select
      v-model="dialog"
      :ship-code="detail.shipCode"
      :initialList="repairList"
      @update="updateList"
    ></repair-dialog-select>
    <complete-dialog
      v-model="dialog2"
      :initialData="initialData"
      @success="success"
    ></complete-dialog>
  </v-container>
</template>
<script>
import CompleteDialog from './private/complete-dialog.vue'
import repairDialogSelect from './private/repair-dialog-select.vue'
export default {
  components: { repairDialogSelect, CompleteDialog },
  name: 'self-repair-detail',
  created() {
    this.backRouteName = 'self-repair-list'
    this.statuses = ['未提交', '审批中', '审批通过', '已驳回', '已完工']
    this.headers = [
      { text: '自修项目名', value: 'name' },
      { text: '自修项目分类', value: 'description' },
      { text: '标准奖金', value: 'defaultBonus' },
      { text: '船舶专属', value: 'property' },
    ]
    this.completeHeaders = [
      { text: '自修项目名', value: 'name' },
      { text: '完工内容', value: 'completeContent' },
      { text: '完工日期', value: 'completeDate' },
      { text: '完工状态', value: 'status' },
      // { text: '备件', value: 'componentOutId' },
      // { text: '滑油', value: 'greaseOutId' },
      // { text: '物料', value: 'materialOutId' },
      { text: '附件', value: 'attachmentRecords' },
    ]
  },
  data() {
    return {
      subtitles: ['基本信息', '修理项目详情'],
      detail: {
        list: [],
      },
      auditParams: {},
      repairList: [],
      completeList: [],
      initialData: {},
      dialog: false,
      dialog2: false,
      selected: false,
      repair: false,
      searchObj: {},
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    addIds() {
      return this.repairList
        .filter((i) => !this.detail.list.includes(i))
        .map((i) => i.id)
    },
    delIds() {
      return this.detail.list
        .filter((i) => !this.repairList.includes(i))
        .map((i) => i.id)
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      // 无id 未提交 已驳回
      if (!this.isEdit) {
        this.detail.ids = this.repairList.map((i) => i.id)
        this.detail.userId = this.$local.data.get('userInfo').nickName
        const { errorRaw, data } = await this.postAsync(
          '/business/shipAffairs/repairApply/saveRepairApply',
          this.detail,
        )
        if (errorRaw) return false
        if (notMove) return data
      } else if (
        this.detail.stateFlag === '已驳回' ||
        this.detail.stateFlag === '未提交'
      ) {
        const re1 =
          this.addIds.length !== 0 &&
          (await this.getAsync(
            '/business/shipAffairs/repairApply/addRepairItem',
            {
              repairApplyId: this.detail.id,
              idList: this.addIds.join(','),
            },
          ))
        const re2 =
          this.delIds.length !== 0 &&
          (await this.getAsync(
            '/business/shipAffairs/repairApply/deleteRepairComplete',
            {
              repairApplyId: this.detail.id,
              idList: this.delIds.join(','),
            },
          ))
        const re3 = await this.postAsync(
          '/business/shipAffairs/repairApply/modifyRepairApply',
          this.detail,
        )
        if (re1?.errorRaw || re2?.errorRaw || re3.errorRaw) return false
        if (notMove) return this.detail.id
      } else {
        if (notMove) return this.detail.id
      }
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (this.repairList.length === 0) {
        this.$dialog.message.error('请添加维修明细')
        return
      }
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/repairApply/process/start',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/shipAffairs/repairApply/getDetailId/${this.$route.params.id}`,
      )
      this.detail = data
      this.repairList = data.list
      this.auditParams = data.auditParams
      if (
        this.detail.stateFlag === '审批通过' ||
        this.detail.stateFlag === '已完工'
      ) {
        if (!this.subtitles.includes('完工详情'))
          this.subtitles.push('完工详情')
        await this.loadCompleteDetail()
      }
    },
    async loadCompleteDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/repairApply/PageComplete',
        { current: 1, size: 100, repairApplyId: this.detail.id },
      )
      const { records } = data
      this.completeList = records.map((i) => {
        return { ...i, ...i.repairItemOutputDTO }
      })
    },
    async addItem() {},
    async delItem() {
      this.repairList = this.repairList.filter((i) => i.id !== this.selected.id)
    },
    updateList(list) {
      this.repairList = list
    },
    finish() {
      this.initialData = {
        ...this.repair,
        repairApplyId: this.detail.id,
        repairItemId: this.repair.id,
      }
      this.dialog2 = true
    },
    async success() {
      await this.loadDetail()
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
