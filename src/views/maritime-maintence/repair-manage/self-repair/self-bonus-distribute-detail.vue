<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['自修奖分配:编辑']"
      :title="`自修奖申请-${isEdit ? detail.applyCode : '新增'}`"
      :tooltip="isEdit ? detail.applyCode : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
      @save="save"
      @submit="submit"
    >
      <template
        v-if="auditParams && auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template v-slot:基本信息>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :disabled="isEdit"
                  v-model="detail.shipCode"
                  dense
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  label="申请部门"
                  v-model="detail.department"
                  outlined
                  dense
                  :disabled="isEdit"
                  :items="['甲板部', '轮机部']"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.applicantId"
                  label="申请人"
                  dense
                  outlined
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  readonly
                  v-model="detail.captainName"
                  label="船长"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="申请时间"
                  v-model="detail.applyTime"
                  outlined
                  dense
                  :disabled="isEdit"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.userId"
                  :init-user="initUser"
                  outlined
                  dense
                  :disabled="isEdit"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-autocomplete
                  label="年份"
                  v-model="detail.year"
                  outlined
                  dense
                  :items="yearList"
                  :rules="[rules.required]"
                  :disabled="isEdit"
                  clearable
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :items="quList"
                  v-model="detail.quarter"
                  label="季度/临时"
                  :disabled="isEdit"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="detail.remark"
                  dense
                  outlined
                  label="备注"
                  :disabled="
                    !!detail.status && detail.status != 1 && detail.status != 4
                  "
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="4">
                申请金额(人民币):{{ computedApplyBonus }}
              </v-col>
              <v-col cols="12" md="4">
                标准金额(人民币):{{ computedStandardBonus }}
              </v-col>
              <v-col cols="12" md="4">
                批准金额(人民币):{{ computedApprovedBonus }}
              </v-col>
              <v-col class="caption" cols="12" v-if="detail.status == 2">
                注:请单击批准金额输入金额
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </template>
      <template
        v-if="!isEdit || detail.status == 1 || detail.status == 4"
        v-slot:申请条目明细按钮
      >
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          :disabled="!detail.shipCode || !detail.department"
          @click.stop="addItem"
          v-permission="['申请条目明细:手动添加']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          手动添加
        </v-btn>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          :disabled="!detail.shipCode || !detail.department"
          @click.stop="dialog = true"
          v-permission="['申请条目明细:选择完工项']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择完工项
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['申请条目明细:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:申请条目明细>
        <v-table-list
          item-key="id"
          :headers="headers"
          :items="repairList"
          v-model="selected"
        >
          <template v-slot:[`item.name`]="{ item }">
            {{ item.name || '手动添加' }}
          </template>
          <template v-slot:[`item.description`]="{ item }">
            {{ item.description || item.applyReasons }}
          </template>
          <template
            v-if="!detail.status || detail.status == 1 || detail.status == 4"
            v-slot:[`item.applyBonus`]="{ item }"
          >
            <v-text-field
              v-model="item.applyBonus"
              label="金额"
              type="number"
              single-line
              dense
            ></v-text-field>
          </template>
          <template
            v-if="!detail.status || detail.status == 1 || detail.status == 4"
            v-slot:[`item.remark`]="{ item }"
          >
            <v-text-field
              v-model="item.remark"
              label="备注"
              single-line
              dense
            ></v-text-field>
          </template>
          <template
            v-if="detail.status == 2"
            v-slot:[`item.approvedBonus`]="{ item }"
          >
            <v-text-field
              v-model="item.approvedBonus"
              label="金额"
              type="number"
              single-line
              counter
              dense
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template v-slot:奖金分配情况>
        <v-col cols="12" md="4">
          已分配金额:{{ allBonus }},待分配余额{{ approvedBonus - allBonus }}
        </v-col>
        <v-table-list
          :headers="assignHeaders"
          :items="assignList"
          v-model="selectedAssign"
        >
          <template v-slot:[`item.status`]="{ item }">
            {{ formatStatus(item.status) }}
          </template>
          <template
            v-if="
              detail.businessStatus === '审批通过' ||
              detail.businessStatus === '岸端已退回'
            "
            v-slot:[`item.distributedBonus`]="{ item }"
          >
            <vue-numeric
              v-model="item.distributedBonus"
              :precision="2"
              style="border: 1px solid black"
              :max="approvedBonus - allBonus"
              :min="0"
              @blur="checkMaxValue($event, item.creIdNo)"
              dense
            ></vue-numeric>
          </template>
        </v-table-list>
      </template>
      <template v-slot:奖金分配情况按钮>
        <v-btn
          v-if="
            detail.businessStatus === '审批通过' ||
            detail.businessStatus === '岸端已退回'
          "
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="addAssign"
          v-permission="['自修奖分配:新增分配']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择船员
        </v-btn>
        <!-- <v-btn
          v-if="detail.status === '审批通过'"
          outlined
          tile
          small
          :disabled="!selectedAssign"
          color="warning"
          class="mx-1"
          @click.stop="editAssign"
          v-permission="['自修奖分配:修改']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          修改
        </v-btn> -->
        <v-btn
          v-if="
            detail.businessStatus === '审批通过' ||
            detail.businessStatus === '岸端已退回'
          "
          :disabled="!selectedAssign"
          outlined
          tile
          small
          color="error"
          class="mx-1"
          @click.stop="delAssign"
          v-permission="['自修奖分配:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          v-if="
            detail.businessStatus === '已完成奖金分配' ||
            detail.businessStatus === '岸端已确认'
          "
          outlined
          tile
          small
          color="primary"
          class="mx-1"
          :href="`/api/business/shipAffairs/repairBonus/excelRepairBonus?id=${detail.id}`"
          v-permission="['自修奖分配:导出分配明细']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出分配明细
        </v-btn>
        <v-btn
          v-if="detail.businessStatus === '已完成奖金分配'"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click="confirmBonus(false)"
          v-permission="['自修奖分配:确认分配']"
        >
          <v-icon left>mdi-check</v-icon>
          确认分配
        </v-btn>
        <v-btn
          v-if="detail.businessStatus === '已完成奖金分配'"
          outlined
          tile
          small
          color="error"
          class="mx-1"
          @click="returnBonus()"
          v-permission="['自修奖分配:退回']"
        >
          <v-icon left>mdi-check</v-icon>
          退回
        </v-btn>
        <v-btn
          v-else-if="
            approvedBonus === allBonus &&
            (detail.businessStatus === '审批通过' ||
              detail.businessStatus === '岸端已退回')
          "
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click="confirmBonus(true)"
          v-permission="['自修奖分配:完成分配']"
        >
          <v-icon left>mdi-check</v-icon>
          完成分配
        </v-btn>
      </template>
    </v-detail-view>
    <bonus-select-dialog
      v-model="dialog"
      :ship-code="detail.shipCode"
      :initialList="repairList"
      @update="updateList"
    ></bonus-select-dialog>
    <bonus-new-item
      v-model="dialog2"
      :initialData="initialData"
      @success="pushItem"
    ></bonus-new-item>
    <!-- <assign-user
      :initialData="initAssign"
      v-model="dialog3"
      @success="loadAssign"
    ></assign-user> -->
    <bonus-crew-select
      v-model="dialog3"
      :assign-list.sync="assignList"
      :ship-code="detail.shipCode"
      :detail="detail"
    ></bonus-crew-select>
  </v-container>
</template>
<script>
// import AssignUser from './private/assign-user.vue'
import BonusCrewSelect from './private/bonus-crew-select.vue'
import BonusNewItem from './private/bonus-new-item.vue'
import bonusSelectDialog from './private/bonus-select-dialog.vue'
import VueNumeric from 'vue-numeric'
import routerControl from '@/mixin/routerControl'
// applyBonus	自修奖申请金额	number
// applyReasons	若不是从已完工自修项目中选择，需填写此项奖金申请原因，与“自修项目ID”列必选其中一列填写	string
// approvedBonus	自修奖被批准的金额	number
// id		string
// repairBonusId	自修奖金申请单id	string
// repairCompleteId	自修完工表包含的项目id	string
// standardBonus	自修奖标准金额
export default {
  components: { bonusSelectDialog, BonusNewItem, BonusCrewSelect, VueNumeric },
  name: 'self-repair-bonus-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'self-bonus-distribute-list'
    this.statuses = ['未提交', '审批中', '审批通过', '已驳回', '已完工']
    this.assignHeaders = [
      // { text: '船员', value: 'nickName' },
      // { text: '身份证', value: 'idCard' },
      // { text: '分配金额', value: 'distributedBonus' },
      { text: '姓名', value: 'creName', sortable: false },
      { text: '身份证', value: 'creIdNo', sortable: false },
      { text: '自修奖年份', value: 'year', sortable: false },
      // { text: '在船职务', value: 'post', sortable: false },
      { text: '分配金额', value: 'distributedBonus', sortable: false },
      {
        text: '是否被船员杂费模块获取申报OA',
        value: 'status',
        sortable: false,
      },
    ]
    this.rangeArray(2000, 2070)
    this.quList = [
      { text: '临时', value: '0' },
      { text: '第一季度', value: '1' },
      { text: '第二季度', value: '2' },
      { text: '第三季度', value: '3' },
      { text: '第四季度', value: '4' },
    ]
  },
  data() {
    return {
      subtitles: ['基本信息', '奖金分配情况'],
      detail: {
        list: [],
        captainName: '',
        applicantId: this.$local.data.get('userInfo').nickName,
      },
      auditParams: {},
      repairList: [],
      completeList: [],
      initialData: {},
      dialog: false,
      selected: false,
      dialog2: false,
      headers: [
        { text: '自修项目名', value: 'name' },
        { text: '项目描述', value: 'description' },
        { text: '标准奖金', value: 'standardBonus' },
        { text: '申请金额', value: 'applyBonus', width: 140 },
        { text: '备注', value: 'remark' },
      ],
      assignList: [],
      allBonus: 0,
      approvedBonus: 0,
      dialog3: false,
      selectedAssign: false,
      initAssign: {},
      initUser: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        notMinus: (v) => v >= 0 || '不得为负',
        aboveZero: (v) => v > 0 || '必须大于0',
      },
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    addList() {
      return this.repairList
        .filter(
          (i) =>
            !this.detail.list.find(
              (o) => o.id === i.id || o.applyBonus != i.applyBonus,
            ),
        )
        .map((i) => {
          return {
            repairBonusId: this.$route.params.id,
            applyReasons: i.applyReasons,
            applyBonus: i.applyBonus,
            standardBonus: i.standardBonus,
            [parseInt(i.id) < 1000 ? false : 'repairCompleteId']: i.id,
          }
        })
    },
    delList() {
      return this.detail.list
        .filter((i) => !this.repairList.find((o) => o.id === i.id))
        .map((i) => i.id)
    },
    computedApplyBonus() {
      return this.repairList.reduce((c, R) => c + parseFloat(R.applyBonus), 0)
    },
    computedStandardBonus() {
      return this.repairList.reduce(
        (c, R) => c + parseFloat(R.standardBonus),
        0,
      )
    },
    computedApprovedBonus() {
      return this.repairList.reduce(
        (c, R) => c + parseFloat(R.approvedBonus),
        0,
      )
    },
  },
  methods: {
    formatStatus(status) {
      if (Number(status) === 1) {
        return '已申报'
      } else {
        return '未申报'
      }
    },
    rangeArray(start, end) {
      const yearNow = new Date()
      this.detail.year = yearNow.getFullYear() + ''
      let length = end - start + 1
      let step = start - 1
      this.yearList = Array.from({ length: length }, () => {
        step++
        return step + ''
      })
    },
    async save(goBack, notMove = false) {
      // 无id 未提交 已驳回
      if (!this.isEdit) {
        this.detail.list = this.repairList.map((i) => {
          return { ...i, id: undefined }
        })
        this.detail.userId = this.$local.data.get('userInfo').id
        const { errorRaw, data } = await this.postAsync(
          '/business/shipAffairs/repairBonus/saveRepairBonus',
          this.detail,
        )
        if (errorRaw) return false
        if (notMove) return data
      } else if (this.detail.status == 4 || this.detail.status == 1) {
        const re1 = await this.postAsync(
          '/business/shipAffairs/repairBonus/modifyBatchRepairBonusItem',
          this.repairList
            .filter(
              (i) =>
                !this.detail.list.some(
                  (o) => o.id === i.id && o.applyBonus == i.applyBonus,
                ),
            )
            .map((i) => {
              return {
                repairBonusId: this.$route.params.id,
                applyReasons: i.applyReasons,
                applyBonus: i.applyBonus,
                standardBonus: i.standardBonus,
                repairCompleteId: i.repairCompleteId,
                [parseInt(i.id) < 1000 ? 'qwd' : 'id']: i.id,
              }
            }),
        )
        const re2 = await this.postAsync(
          '/business/shipAffairs/repairBonus/deleteRepairBonusItem',
          this.delList,
        )
        const re3 = await this.postAsync(
          '/business/shipAffairs/repairBonus/modifyRepairBonus',
          this.detail,
        )
        if (re1.errorRaw || re2.errorRaw || re3.errorRaw) return false
        if (notMove) return this.detail.id
      }
      // 审批中，，提交审批流情况
      if (this.detail.status == 2) {
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/repairBonus/addApproveBonus',
          this.repairList.map((i) => {
            return { id: i.id, approvedBonus: i.approvedBonus }
          }),
        )
        if (errorRaw) return false
        if (notMove) return this.detail.id
      }
      // 审批结束
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        if (this.repairList.find((i) => i.approvedBonus == 0)) {
          if (
            !(await this.$dialog.msgbox.confirm(
              '当前有项目审批金额为0,您确定吗',
            ))
          )
            return
        }
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/repairBonus/process/start',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        `/business/shipAffairs/repairBonus/getDetailId/${this.$route.params.id}`,
      )
      this.detail = data
      this.initUser = { id: data.userId, nickName: data.userName }
      this.repairList = [
        ...data.list.map((i) => {
          return Object.assign({}, i)
        }),
      ]
      this.auditParams = data.auditParams
      if (this.detail.status != 1 && this.detail.status != 4) {
        // this.headers.push({ text: '批准金额', value: 'approvedBonus' })
        this.headers.splice(4, 0, {
          text: '批准金额',
          value: 'approvedBonus',
        })
      }
      if (
        this.detail.businessStatus === '审批通过' ||
        this.detail.businessStatus === '岸端已退回' ||
        this.detail.businessStatus === '已完成奖金分配' ||
        this.detail.businessStatus === '岸端已确认'
      ) {
        await this.loadAssign()
      }
      if (this.detail.businessStatus === '岸端已退回') {
        this.assignList = []
      }
    },
    async loadAssign() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/repairBonus/getDetailDistribution',
        { id: this.detail.id },
      )
      this.assignList = data.list
      this.assignList.forEach((item) => {
        item['creId'] = item.userId
      })
      // console.log(this.assignList)
      this.approvedBonus = data.approvedBonus
      this.allBonus = data.allBonus
      this.detail.status = data.status
      this.selectedAssign = false
      this.loadAssignList()
    },
    loadAssignList() {
      this.assignList = this.assignList.map((item) => ({
        ...item,
        year: this.detail.year || item.year,
      }))
    },
    async addItem() {
      this.initialData = {}
      this.dialog2 = true
    },
    async delItem() {
      this.repairList = this.repairList.filter((i) => i.id !== this.selected.id)
    },
    updateList(list) {
      this.repairList = list
    },
    async success() {
      await this.loadCompleteDetail()
    },
    pushItem(item) {
      if (!this.repairList.find((i) => i.id === item.id)) {
        this.repairList = [...this.repairList, item]
      }
    },
    async confirmBonus(ship = false) {
      if (!(await this.$dialog.msgbox.confirm('确定当前分配吗？'))) return
      if (ship) {
        this.assignList.forEach((item) => {
          item['userId'] = item.creId
          item['repairBonusId'] = this.detail.id
          item['id'] = ''
          item['crePropertyId'] = item.creProperty.id
        })
        const url = '/business/shipAffairs/repairBonus/distributionBonusNew'
        const { errorRaw } = await this.postAsync(url, this.assignList)
        if (errorRaw) {
          return
        }
        const { data } = await this.getAsync(
          '/business/shipAffairs/repairBonus/shipCheckRepairBonus',
          { id: this.detail.id },
        )
        if (!data) return
        this.closeAndTo(this.backRouteName)
        // await this.loadDetail()
      } else {
        const { data } = await this.getAsync(
          '/business/shipAffairs/repairBonus/checkRepairBonus',
          { id: this.detail.id },
        )
        if (!data) return
        this.closeAndTo(this.backRouteName)
        // await this.loadDetail()
      }
    },
    addAssign() {
      this.initAssign = {}
      this.dialog3 = true
    },
    editAssign() {
      this.initAssign = { ...this.selectedAssign }
      this.dialog3 = true
    },
    async delAssign() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.assignList = this.assignList.filter(
        (i) => i.creIdNo !== this.selectedAssign.creIdNo,
      )
      this.allBonus = 0
      this.assignList.forEach((item) => {
        this.allBonus = Number(item.distributedBonus) + Number(this.allBonus)
      })
      this.select = false
    },
    checkMaxValue(event, creIdNo) {
      let inputValue = parseFloat(event.target.value) || 0
      let otherBonus = this.assignList
        .filter((item) => item.creIdNo !== creIdNo)
        .reduce((sum, item) => sum + parseFloat(item.distributedBonus || 0), 0)
      let maxVal = this.approvedBonus - otherBonus
      if (inputValue > maxVal) {
        this.$dialog.message.error(
          '输入的金额超过了待分配金额，当前金额已变更为最大可分配余额',
        )
        inputValue = maxVal
      }
      this.assignList.forEach((item) => {
        if (item.creIdNo === creIdNo) {
          item.distributedBonus = inputValue
        }
      })

      // 更新所有已分配金额
      this.allBonus = this.assignList.reduce(
        (sum, item) => sum + parseFloat(item.distributedBonus || 0),
        0,
      )
    },
    async returnBonus() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/repairBonus/returnRepairBonus',
        { id: this.detail.id },
      )
      if (!data) return
      this.closeAndTo(this.backRouteName)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
