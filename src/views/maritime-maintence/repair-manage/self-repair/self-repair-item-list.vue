<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }} {{ tableName }}
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          v-permission="['自修项目清单:修改']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '修改' : '新增' }}
        </v-btn>
        <v-btn outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="4">
                <v-text-field
                  v-model="item.name"
                  dense
                  outlined
                  label="自修项目名"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-dict-select
                  v-model="item.description"
                  dense
                  outlined
                  label="自修项目分类"
                  required
                  dict-type="self_repair_item_type"
                  :rules="[rules.required]"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="item.defaultBonus"
                  dense
                  outlined
                  type="number"
                  label="标准奖金"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="item.remark"
                  dense
                  outlined
                  label="备注"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-ship-select v-model="item.property"></v-ship-select>
              </v-col>
              <v-col class="font-weight-thin" cols="12" md="2">
                注:不填写船舶名称即视为公用
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      v-if="!isShip"
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
      :search-remain="searchObj"
      use-ship
      fuzzy-label="模糊查询"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          @click="createItem"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['自修项目清单:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['自修项目清单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.property`]="{ item }">
        {{ item.property == 0 ? '公用' : '专属' }}
      </template>
    </v-table-searchable>
    <v-table-searchable
      v-if="isShip"
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
      :search-remain="searchObj"
      fuzzy-label="模糊查询"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          @click="createItem"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['自修项目清单:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['自修项目清单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.property`]="{ item }">
        {{ item.property == 0 ? '公用' : '专属' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// defaultBonus	自修项目的标准奖金	number
// description	自修项目描述	string
// id	物理主键	string
// name	自修项目名	string
// property	此项目为所有船共用项目还是某个船舶专属，如果为共用项目，不填，否则填专属船舶的ID。	string
export default {
  name: 'self-repair-item-list',
  created() {
    this.tableName = '自修项目清单管理'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.reqUrl = '/business/shipAffairs/repairItem/page'
    this.headers = [
      { text: '自修项目名', value: 'name' },
      { text: '自修项目分类分类', value: 'description' },
      { text: '标准奖金', value: 'defaultBonus' },
      { text: '船舶专属', value: 'property' },
      { text: '备注', value: 'remark' },
    ]
    this.searchDicts = [
      {
        dicType: 'self_repair_item_type',
        label: '自修项目分类',
        key: 'description',
      },
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: { shipCode: '' },
      item: {},
      valid: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      isEdit: false,
      loading: false,
      formShow: false,
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/repairItem/deleteRepairItem',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },

    async editItem() {
      this.item = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    createItem() {
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = false
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/repairItem/modifyRepairItem'
      const { errorRaw } = await this.postAsync(reqUrl, this.item, false)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.$refs.form.reset()
      this.item = {}
      this.isEdit = false
      this.selected = false
      this.formShow = false
      this.$refs.table.disabled = false
      await this.$nextTick()
    },
    closeForm() {
      this.$refs.form.reset()
      this.item = {}
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
