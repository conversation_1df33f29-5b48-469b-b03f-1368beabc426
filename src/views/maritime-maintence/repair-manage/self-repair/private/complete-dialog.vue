<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        完工情况
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  label="完工日期"
                  v-model="formData.completeDate"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="formData.completeContent"
                  outlined
                  label="完工内容"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-attach-list
                  :attachments="formData.attachmentRecords"
                  @change="changeAttachment"
                  :ship-code="formData.shipCode"
                ></v-attach-list>
              </v-col>
              <!-- <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('spare')"
                  :disabled="!!spareOutId"
                >
                  备件消耗
                </v-btn>
              </v-col>
              <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('material')"
                  :disabled="!!materialOutId"
                >
                  物料消耗
                </v-btn>
              </v-col>
              <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('soil')"
                  :disabled="!!soilOutId"
                >
                  滑油消耗
                </v-btn>
              </v-col> -->
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  完工
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'complete-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.formData.attachmentRecords = []
      this.formData.attachmentIds = []
    },
  },
  computed: {
    spareOutId() {
      return (
        this.formData.componentId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.repairItemId &&
            b.itemType === 'spare-out-detail' &&
            b.businessType === 'self-repair-detail',
        )?.outId
      )
    },
    materialOutId() {
      return (
        this.formData.materialOutId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.repairItemId &&
            b.itemType === 'materials-out-detail',
        )?.outId
      )
    },
    soilOutId() {
      return (
        this.formData.soilOutId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.repairItemId &&
            b.itemType === 'soil-out-detail',
        )?.outId
      )
    },
  },
  methods: {
    closeForm() {
      this.$store.commit('removeOutParamByItemId', this.formData.repairItemId)
      this.$emit('change', false)
    },
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/shipAffairs/repairApply/completeRepair'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        // TODO:根据业务类型获取出库单id
        componentId: this.spareOutId,
        greaseId: this.soilOutId,
        materialId: this.materialOutId,
      })
      if (!errorRaw) {
        this.$store.commit('removeOutParamByItemId', this.formData.repairItemId)
        this.$emit('change', false)
        this.$emit('success')
      }
    },

    setOutParam(type) {
      const types = {
        spare: 'spare-out-detail',
        material: 'materials-out-detail',
        soil: 'soil-out-detail',
      }
      const businessItemId = this.formData.repairItemId
      const businessId = this.$route.params.id
      this.$store.commit('emitOut', {
        businessType: 'self-repair-detail',
        businessItemId,
        businessId,
        itemType: types[type],
      })
      this.$router.push({
        name: types[type],
        params: { id: 'new' },
      })
    },
  },
  beforeDestroy() {
    this.$store.commit('removeOutParamByItemId', this.formData.repairItemId)
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
