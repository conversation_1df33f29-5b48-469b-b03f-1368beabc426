<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        当前在船船员
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          item-key="creId"
          :single-select="false"
          outlined
        >
          <template #searchflieds></template>
          <template #btns></template>
        </v-table-searchable>
      </v-card-text>
      <v-card-text>
        <v-table-searchable2
          ref="table2"
          table-name="历史在船船员"
          :search-date="searchDate"
          v-model="selected2"
          :headers="headersHistory"
          :req-url="reqUrlHistory"
          :search-remain="searchObjHistory"
          :fix-header="false"
          item-key="creId"
          :single-select="false"
          outlined
        >
          <template #searchflieds></template>
          <template #btns></template>
        </v-table-searchable2>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
const today = new Date(Date.now())
const untilDate = new Date(new Date().setDate(today.getDate() - 365))
  .toISOString()
  .substr(0, 10)
export default {
  name: 'bonus-crew-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/crew/osmOnShipCrew/page'
    this.reqUrlHistory = '/business/crew/osmOnShipCrew/historyOnShip/page'
    this.headers = [
      { text: '姓名', value: 'creName', sortable: false },
      { text: '身份证', value: 'creIdNo', sortable: false },
      { text: '在船职务', value: 'post', sortable: false },
    ]
    this.headersHistory = [
      { text: '姓名', value: 'creName', sortable: false },
      { text: '身份证', value: 'creIdNo', sortable: false },
      { text: '在船职务', value: 'post', sortable: false },
    ]
    this.searchDate = {
      label: '在船时间',
      interval: true,
    }
    this.untilDate = untilDate
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: {
      type: String,
      default: '',
    },
    voucher: Array,
    batchCost: Array,
    assignList: Array,
    detail: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchObj: {
        shipCode: '',
      },
      selected: [],
      toTime: untilDate,
      deptInfo: [],
      searchObjHistory: {
        shipCode: '',
      },
      selected2: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
      this.searchObjHistory.shipCode = val
    },
    assignList(val) {
      this.selected = val
      this.selected2 = val
    },
    // voucher(val) {
    //   this.selected = val
    // },
    // batchCost(val) {
    //   this.selected2 = val
    // },
    // toTime(val) {
    //   this.searchObj.toTime = val
    //   this.searchObjBatchCost.toTime = val
    // },
    // 'searchObj.company'() {
    //   this.searchObj.depts = []
    //   this.searchObjBatchCost.company = this.searchObj.company
    // },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const voucher = [...this.selected]
      const batchCost = [...this.selected2]
      // console.log(voucher)
      // console.log(batchCost)
      var assignList = []
      assignList = assignList.concat(voucher).concat(batchCost)
      assignList.forEach((item) => {
        item['distributedBonus'] = 0
        item['userId'] = item.creId
        item['status'] = false
        item['year'] = this.detail.year
      })
      // console.log(assignList)
      this.$emit('update:assignList', assignList)
      this.$emit('change', false)
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
  mounted() {
    // console.log(this.searchObj)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
