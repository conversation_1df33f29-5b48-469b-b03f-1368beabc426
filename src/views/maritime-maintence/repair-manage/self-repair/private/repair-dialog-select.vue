<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        自修项目选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          :table-name="''"
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :single-select="false"
          :search-remain="searchObj"
          fuzzy-label="模糊查询"
          :search-dicts="searchDicts"
        >
          <template #searchflieds></template>
          <template #btns></template>
          <template v-slot:[`item.property`]="{ item }">
            {{ item.property == 0 ? '公用' : '专属' }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
// defaultBonus	自修项目的标准奖金	number
// description	自修项目描述	string
// id	物理主键	string
// name	自修项目名	string
// property	此项目为所有船共用项目还是某个船舶专属，如果为共用项目，不填，否则填专属船舶的ID。
export default {
  name: 'repair-dialog-select',
  created() {
    this.reqUrl = '/business/shipAffairs/repairApply/getCanAddItem'
    this.headers = [
      { text: '自修项目名', value: 'name' },
      { text: '自修项目分类', value: 'description' },
      { text: '标准奖金', value: 'defaultBonus' },
      { text: '船舶专属', value: 'property' },
    ]
    this.selected = []
    this.searchDicts = [
      {
        dicType: 'self_repair_item_type',
        label: '自修项目分类',
        key: 'description',
      },
    ]
  },

  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialList: {
      type: Array,
      default: () => [],
    },
    shipCode: String,
  },
  data() {
    return {
      dialog: false,
      selected: [],
      searchObj: { shipCode: '' },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.selected = this.initialList
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
  },
  methods: {
    closeForm() {
      this.selected = []
      this.$emit('change', false)
    },
    confirm() {
      this.selected.forEach((item) => {
        item['standardBonus'] = item.defaultBonus
      })
      this.$emit('update', this.selected)
      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
