<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        自修奖申请选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          outlined
          ref="table"
          :table-name="''"
          v-model="selected"
          :headers="headers"
          :search-dicts="searchDicts"
          :search-date="searchDate"
          :req-url="reqUrl"
          :fix-header="false"
          :single-select="false"
          :search-remain="searchObj"
        >
          <template #searchflieds></template>
          <template #btns></template>
          <template v-slot:[`item.name`]="{ item }">
            {{ item.repairItemOutputDTO.name }}
          </template>
          <template v-slot:[`item.defaultBonus`]="{ item }">
            {{ item.repairItemOutputDTO.defaultBonus }}
          </template>
          <template v-slot:[`item.description`]="{ item }">
            {{ item.repairItemOutputDTO.description }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'bonus-select-dialog',
  created() {
    this.reqUrl = '/business/shipAffairs/repairBonus/getCanAddItem'
    this.headers = [
      { text: '自修项目名', value: 'name' },
      { text: '自修项目分类', value: 'description' },
      { text: '标准奖金', value: 'defaultBonus' },
      { text: '完工内容', value: 'completeContent' },
      { text: '完工时间', value: 'completeDate' },
    ]
    this.searchDicts = [
      {
        dicType: 'self_repair_item_type',
        label: '自修项目分类',
        key: 'description',
      },
    ]
    this.selected = []
    this.searchDate = {
      label: '完工时间',
      interval: true,
    }
  },

  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialList: {
      type: Array,
      default: () => [],
    },
    shipCode: String,
    department: String,
  },
  data() {
    return {
      dialog: false,
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$nextTick(() => (this.selected = this.initialList))
    },
  },
  computed: {
    searchObj() {
      return { shipCode: this.shipCode, department: this.department }
    },
  },
  methods: {
    closeForm() {
      this.selected = []
      this.$emit('change', false)
    },
    confirm() {
      this.$emit(
        'update',
        this.selected.map((i) => {
          if (parseInt(i.id) < 1000) return i
          return {
            ...i?.repairItemOutputDTO,
            standardBonus: i?.repairItemOutputDTO?.defaultBonus,
            applyBonus: 0,
            repairCompleteId: i.id,
            ...i,
          }
        }),
      )
      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
