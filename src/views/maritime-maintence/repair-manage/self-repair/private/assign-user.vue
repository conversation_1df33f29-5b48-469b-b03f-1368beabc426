<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        分配情况
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" small @click="save">
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '保存' : '创建' }}
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <!-- <v-dialog-select
                  label="船员"
                  item-text="nickName"
                  item-value="id"
                  v-model="formData.userId"
                  :headers="userHeaders"
                  :rules="[rules.required]"
                  req-url="/system/user/page"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field label="用户名"></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select> -->
                <v-handler
                  :initUser="initUser"
                  label="船员"
                  v-model="formData.userId"
                  user-type="3"
                  :rules="[rules.required]"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.distributedBonus"
                  label="分配金额"
                  dense
                  outlined
                  type="number"
                  :rules="[rules.required, rules.aboveZero]"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'assign-user',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.userHeaders = [
      { text: '用户名', value: 'nickName' },
      { text: '部门名称', value: 'deptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {
        distributedBonus: 0,
        userId: '',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        aboveZero: (v) => v > 0 || '必须大于0',
      },
      initUser: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = { ...this.initialData }
      this.initUser = this.initialData.id
        ? {
            id: this.initialData.userId,
            nickName: this.initialData.nickName,
          }
        : false
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/shipAffairs/repairBonus/distributionBonus'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        repairBonusId: this.$route.params.id,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
