<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
    >
      <template #searchflieds></template>
      <template #btns></template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'table-list',
  created() {
    this.tableName = '港口信息'
    this.reqUrl = '/dockRepairApply/getPageOfApply'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = []
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      selected: false,
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
