<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="3" v-if="formStatus === '已安排'">
                <!-- <port-select-dialog
                  v-model="detail.portId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                ></port-select-dialog> -->
                <v-text-field
                  v-model="detail.portId"
                  dense
                  outlined
                  label="修理厂家"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="formStatus === '已安排'">
                <vs-date-picker
                  :rules="[rules.required]"
                  v-model="detail.repairDate"
                  dense
                  outlined
                  label="安排日期"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3" v-if="formStatus === '已确认'">
                <v-text-field
                  type="number"
                  v-model="detail.estimatedExpense"
                  dense
                  outlined
                  :label="`预估金额(${selected.ccyCode})`"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12" v-if="formStatus === '已确认'">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="detail.costSubjectId"
                  :rules="[rules.required]"
                  :search-remain="{ repairFlag: 1 }"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  required
                  dense
                  fuzzy-label="模糊查询"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="changeStatus(formStatus)"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  确认
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="isShip ? headersShip : headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      use-status
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="修理单号"
            outlined
            dense
            clearable
            v-model="searchObj.orderNo"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-btn
          outlined
          :disabled="!selected || selected.businessStatus !== '待确认'"
          tile
          color="primary"
          class="mx-1"
          @click="editItem('已确认')"
          v-permission="['坞修修理单:主管确认']"
        >
          <v-icon left>mdi-check</v-icon>
          主管确认
        </v-btn>
        <v-btn
          outlined
          :disabled="
            selected.businessStatus !== '已确认' || selected.status !== '3'
          "
          tile
          color="primary"
          class="mx-1"
          @click="editItem('已安排')"
          v-permission="['坞修修理单:安排修理']"
        >
          <v-icon left>mdi-package-variant-closed-check</v-icon>
          安排修理
        </v-btn> -->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="changeStatus('废弃')"
          v-permission="['坞修修理单:废弃']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          废弃
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="downloadExcel"
          v-permission="['坞修修理单:导出EXCEL']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          导出EXCEL
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applicantId	申请人	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// businessStatus	业务状态	string
// enquiryId	询价单id	string
// id	物理主键	string
// orderNo		string
// otherExpense	其他费用	number
// post	申请部门	string
// quoteId	报价单id	string
// remark	备注	string
// repairExpense	修理费用	number
// shipInfo	船舶编码	ShipInfoDO
export default {
  name: 'dock-repair-list',
  created() {
    this.tableName = '坞修修理单'
    this.reqUrl = '/dockRepairApply/getPageOfOrder'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '修理单号', value: 'orderNo' },
      { text: '修理费用', value: 'totalPrice' },
      { text: '币种', value: 'ccyCode' },
      { text: '折算美元', value: 'toUsd' },
      { text: '供应商', value: 'supplierName' },
      { text: '坞修类型', value: 'dockRepairType' },
      { text: '安排修理日期', value: 'repairDate' },
      { text: '决算金额', value: 'accountMoney', hideDefault: true },
      // { text: '申请部门', value: 'dept' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
      { text: '备注', value: 'remark' },
      // { text: '申请岗位', value: 'applicantPost' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '修理单号', value: 'orderNo' },
      { text: '供应商', value: 'supplierName' },
      { text: '坞修类型', value: 'dockRepairType' },
      { text: '安排修理日期', value: 'repairDate' },
      // { text: '申请部门', value: 'dept' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
      { text: '备注', value: 'remark' },
      // { text: '申请岗位', value: 'applicantPost' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'dock-repair-detail' }
  },

  data() {
    return {
      selected: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      portId: '',
      initPort: {},
      formShow: false,
      repairDate: '',
      estimatedExpense: 0,
      detail: {},
      searchObj: { orderType: 0, isMe: true },
      formStatus: '',
      loading: false,
    }
  },
  computed: {
    canExcel() {
      return (
        this.selected &&
        this.selected.businessStatus != '待确认' &&
        this.selected.businessStatus != '已确认' &&
        this.selected.businessStatus != '已安排' &&
        this.selected.businessStatus != '废弃' &&
        this.selected.businessStatus != '全部完工'
      )
    },
  },
  methods: {
    async editItem(formStatus) {
      this.formStatus = formStatus
      this.portId = this.selected.enquiryPortId
      this.initPort = {
        portCn: this.selected.enquiryPortName,
        id: this.selected.enquiryPortId,
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },

    closeForm() {
      this.$refs.form.reset()
      this.initPort = {}
      this.formShow = false
      this.$refs.table.disabled = false
    },

    async changeStatus(formStatus) {
      if (!(this.$refs?.form?.validate() ?? true)) return
      if (formStatus == '废弃') {
        if (
          this.selected.businessStatus != '已安排' &&
          this.selected.businessStatus != '已确认' &&
          this.selected.businessStatus != '待确认'
        ) {
          this.$dialog.message.error('当前状态不可废弃！')
          return
        }
        if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      }
      const { errorRaw } = await this.postAsync(
        '/dockRepairApply/updateOrder',
        {
          id: this.selected.id,
          orderType: this.selected.orderType,
          businessStatus: formStatus || this.formStatus,
          ...this.detail,
        },
      )
      if (errorRaw) return
      if (this.formStatus === '已确认') {
        const { errorRaw } = await this.getAsync(
          '/dockRepairApply/submitOrderById',
          {
            id: this.selected.id,
          },
        )
        if (errorRaw) return
      }
      this.$dialog.message.success('状态变更成功')
      this.selected = false
      this.formStatus = ''
      this.closeForm()
      await this.$refs.table.loadTableData()
    },
    async downloadExcel() {
      this.loading = true
      let params = {
        ...this.selected,
      }
      await this.getBlobDownload(
        '/dockRepairApply/excelExportDock',
        params,
        // 时间戳后四位
        `坞修修理单-${this.selected.orderNo}.xlsx`,
      )
      this.loading = false
    },
  },

  mounted() {},
}
</script>

<style></style>
