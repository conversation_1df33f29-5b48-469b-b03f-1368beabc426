<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        供应商安排修理
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          安排修理
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col class="py-0" md="12" cols="12">
                <v-textarea
                  outlined
                  dense
                  v-model="formData.remarks"
                  label="备注"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'dock-confirm-sup-dialog',
  created() {},
  activated() {
    this.dialog = this.open
  },
  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
    status: {
      type: Number,
    },
  },
  data() {
    return {
      dialog: false,
      open: false,
      formData: {},
      resolveFn: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.open = false
      this.resolveFn(false)
    },

    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.open = false
      this.resolveFn(this.formData)
    },

    async confirm() {
      this.open = true
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
