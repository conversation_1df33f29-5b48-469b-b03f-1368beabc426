<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        修理单确认
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          确认
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.orderPortId"
                  dense
                  outlined
                  label="修理船厂"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="安排日期"
                  v-model="formData.repairDate"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  type="number"
                  v-model="formData.estimatedExpense"
                  dense
                  outlined
                  label="预估金额"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <!-- <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="formData.costSubjectId"
                  :rules="[rules.required]"
                  :search-remain="{ repairFlag: 1 }"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  required
                  dense
                  fuzzy-label="模糊查询"
                ></v-dialog-select>
              </v-col> -->
              <v-col class="py-0" md="12" cols="12">
                <v-textarea
                  outlined
                  dense
                  v-model="formData.remarks"
                  label="备注"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'reduce-in-dialog',
  created() {
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
    status: {
      type: Number,
    },
  },
  data() {
    return {
      dialog: false,
      open: false,
      formData: {},
      resolveFn: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.open = false
      this.resolveFn(false)
    },

    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.open = false
      this.resolveFn(this.formData)
    },

    async confirm() {
      this.open = true
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
