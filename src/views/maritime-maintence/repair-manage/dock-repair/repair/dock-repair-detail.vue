<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修修理单-${detail.orderNo}`"
      :tooltip="detail.orderNo"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="detail.auditParams && detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['坞修修理单:编辑']"
      ref="detail"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template #custombtns>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == '待确认'"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('已确认')"
          v-permission="['坞修修理单:主管确认(机务)']"
        >
          机务主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == '待确认'"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('已确认')"
          v-permission="['坞修修理单:主管确认(通导)']"
        >
          通导主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == '已确认'"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('已安排')"
          v-permission="['坞修修理单:安排修理']"
        >
          安排修理
        </v-btn>
      </template>
      <template v-if="detail.auditParams && !isSupper" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #订单信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-for="(h, i) in isShip ? tableFeildsShip : tableFeilds"
                :key="i"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfoDO'"
                  v-model="detail.shipCode"
                  readonly
                ></v-ship-select>
                <v-switch
                  v-else-if="h.value === 'priceIncTax'"
                  class="mt-1"
                  :label="detail.priceIncTax ? '含税' : '不含税'"
                  v-model="detail[h.value]"
                  dense
                ></v-switch>
                <v-select
                  label="补充项目"
                  v-else-if="h.value === 'isAdd'"
                  outlined
                  dense
                  :items="[
                    { text: '是', value: true },
                    { text: '否', value: false },
                  ]"
                  v-model="detail.isAdd"
                  readonly
                ></v-select>
                <v-select
                  label="业务类型"
                  v-else-if="h.value === 'managerType'"
                  outlined
                  dense
                  :items="[
                    { text: '机务', value: '1' },
                    { text: '通导', value: '2' },
                  ]"
                  v-model="detail.managerType"
                  readonly
                ></v-select>
                <v-text-field
                  v-else
                  outlined
                  dense
                  v-model="detail[h.value]"
                  :label="h.text"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12">
                <v-textarea
                  outlined
                  dense
                  v-model="detail.remark"
                  label="备注"
                  :readonly="true"
                ></v-textarea>
              </v-col>
              <v-col v-if="!isSupper" class="py-0" cols="12">
                <v-textarea
                  outlined
                  dense
                  v-model="detail.orderEvaluation"
                  label="订单评价"
                  :readonly="false"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template #修理明细>
        <v-table-list
          :show-select="false"
          :headers="isShip ? headersShip : headers"
          :items="detail.detailList"
        >
          <template v-slot:[`item.isCompleted`]="{ item }">
            {{
              item.isCompleted == 1
                ? '是'
                : item.isCompleted == 2
                ? '取消'
                : '否'
            }}
          </template>
          <!-- <template v-slot:[`item.afterRepairPrice`]="{ item }">
            {{ item.repairDiscount * item.repairPrice }}
          </template> -->
        </v-table-list>
      </template>
      <template #供应商信息按钮>
        <!-- TODO:何时可以评价 -->
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['坞修修理单:评价供应商']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          评价供应商
        </v-btn>
      </template>
      <template v-if="!isSupper" #供应商信息>
        <v-container fluid>
          <b>供应商名称:</b>
          {{ detail.supplierName }}
          <v-card outlined class="mt-1 pt-2 px-2" v-if="comment">
            <v-row>
              <v-col cols="12" md="6">
                质量评分
                <v-rating
                  v-model="comment.score1"
                  background-color="purple lighten-3"
                  color="purple"
                  length="10"
                  readonly
                ></v-rating>
              </v-col>
              <v-col cols="12" md="6">
                服务评分
                <v-rating
                  v-model="comment.score2"
                  background-color="green lighten-3"
                  color="green"
                  length="10"
                  readonly
                ></v-rating>
              </v-col>
            </v-row>
            <v-card-text class="text-body-1">{{ comment.remark }}</v-card-text>
            <v-card-text>
              <v-attach-list
                title="评价附件"
                disabled
                :attachments="comment.attachmentRecords"
              ></v-attach-list>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <b>评论最后更改:</b>
              {{ comment.remarkTime }}，
              <b>评论人:</b>
              {{ comment.userNickName }}
            </v-card-actions>
          </v-card>
        </v-container>
      </template>
    </v-detail-view>
    <comment-dialog
      v-model="dialog"
      :initialData="initData"
      @success="loadCom"
    ></comment-dialog>
    <dock-confirm-dialog ref="dialog"></dock-confirm-dialog>
    <dock-confirm-sup-dialog ref="dialog2"></dock-confirm-sup-dialog>
  </v-container>
</template>
<script>
import commentDialog from '@/views/maritime-maintence/components/comment-dialog.vue'
import DockConfirmDialog from './private/dock-confirm-dialog.vue'
import DockConfirmSupDialog from './private/dock-confirm-sup-dialog.vue'
// accountMoney	决算金额	number
// attachmentRecords	附件ids	array	CommonAttachment
// auditParams	流程实例id	AuditParams	AuditParams
// businessStatus	业务状态	string
// ccyCode	币种	string
// completeDate	完工日期	string(date-time)
// costSubjectOutputDTO	费用科目信息	CostSubjectOutputDTO_1	CostSubjectOutputDTO_1
// currencyId	币种id	string
// detailList	明细列表	array	DockRepairOrderDetailOutputDTO
// dockRepairType	坞修类型：厂修/外协	string
// estimatedExpense	预估金额	number
// id	物理主键	string
// invoiceType	发票类型	string
// orderEvaluation	订单评价	string
// orderNo	修理单单号	string
// orderPortId	修理单港口id	string
// orderPortName	修理单港口名称	string
// orderType	修理单类型：原始修理单/增量工程	string
// parentId	（增量工程的原修理单id）父级id	string
// priceIncTax	发票是否含税	boolean
// quoteId	报价单id	string
// quoteNo	报价单号	string
// rateToMain	折算汇率	number
// remark	备注	string
// repairDate	修理日期	string(date-time)
// shipInfo	船舶编码	ShipInfoDO	ShipInfoDO
// status	流程状态	string
// supplierId	供应商id	string
// supplierName	供应商名称	string
// supplierScoreId	供应商评分id	string
// tax	税费	number
// taxRate	发票税率	number
// toUsd	折算美元	number

// itemName	项目名称	string
// itemNo	项目编号	string
// orderId	修理单/增量工程id	string
// remark	备注	string
// repairDiscount	修理折扣	number
// repairPrice	修理价格

export default {
  name: 'dock-repair-detail',
  components: { commentDialog, DockConfirmDialog, DockConfirmSupDialog },
  created() {
    this.backRouteName = 'dock-repair-list'
    this.subtitles = ['订单信息', '修理明细', '供应商信息']
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目编号', value: 'itemNo' },
      { text: '是否完工', value: 'isCompleted' },
      // { text: '修理折扣', value: 'repairDiscount' },
      // { text: '修理原价', value: 'repairPrice' },
      // { text: '折扣后价格', value: 'afterRepairPrice' },
      { text: '成交单价', value: 'discountPrice' },
      // { text: '付款单价', value: 'payPrice' },
      { text: '结算金额', value: 'payPrice' },
      // { text: '备注', value: 'remark' },
    ]
    this.tableFeilds = [
      { text: '船舶', value: 'shipInfoDO' },
      { text: '坞修类型', value: 'dockRepairType' },
      { text: '安排修理日期', value: 'repairDate' },
      { text: '费用科目', value: 'subjectName' },
      { text: '币种', value: 'ccyCode' },
      { text: '明细总价', value: 'totalFinalPrice' },
      { text: '汇率', value: 'rateToMain' },
      { text: '是否含税', value: 'priceIncTax' },
      { text: '发票税率', value: 'taxRate' },
      { text: '税费', value: 'tax' },
      { text: '折算美元', value: 'toUsd' },
      { text: '预估金额', value: 'estimatedExpense' },
      { text: '决算金额', value: 'accountMoney' },
      // { text: '修理单港口', value: 'orderPortName' },
      // { text: '发票类型', value: 'invoiceType' },
      { text: '修理船厂', value: 'orderPortId' },
      // { text: '备注', value: 'remark' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '业务类型', value: 'managerType' },
      { text: '是否补充', value: 'isAdd' },
    ]

    this.headersShip = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目编号', value: 'itemNo' },
      { text: '是否完工', value: 'isCompleted' },
      // { text: '备注', value: 'remark' },
    ]
    this.tableFeildsShip = [
      { text: '船舶', value: 'shipInfoDO' },
      { text: '坞修类型', value: 'dockRepairType' },
      { text: '安排修理日期', value: 'repairDate' },
      { text: '费用科目', value: 'subjectName' },
      // { text: '修理单港口', value: 'orderPortName' },
      { text: '发票类型', value: 'invoiceType' },
      { text: '修理厂家', value: 'orderPortId' },
      // { text: '备注', value: 'remark' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '是否补充', value: 'isAdd' },
    ]
    if (this.$local.data.get('userInfo').userType == 4) {
      this.tableFeilds = this.tableFeilds.filter(
        (item) => item.text !== '预估金额' && item.text !== '决算金额',
      )
    }
  },
  data() {
    return {
      detail: {},
      dialog: false,
      initData: {},
      comment: {},
      loading: false,
    }
  },

  computed: {
    isSupper() {
      return this.$local.data.get('userInfo').userType == 4
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack, notMove = false) {
      const { errorRaw } = await this.postAsync(
        '/dockRepairApply/updateOrder',
        this.detail,
      )
      if (errorRaw) {
        return false
      }
      if (notMove) {
        return true
      }
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const res = await this.save(goBack, true)
      if (!res) {
        return false
      }
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/dockRepairApply/submitOrderById',
          { id: this.detail.id },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadDetail() {
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfOrderById',
        {
          id: this.$route.params.id,
        },
      )
      this.detail = data
      this.detail.detailList.forEach((item) => {
        item['discountPrice'] = (
          item.repairDiscount * item.repairPrice
        ).toFixed(2)
      })
      this.detail.shipCode = this.detail.shipInfo.shipCode
      this.detail.subjectName = this.detail.costSubjectOutputDTO?.subjectName
    },
    async loadCom() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/SupplierAssessment/getByOrderId',
        {
          id: this.$route.params.id,
        },
      )
      this.comment = data
    },
    createCom() {
      this.dialog = true
      this.initData = {
        ...this.comment,
        supplierId: this.detail.supplierId,
        supplierName: this.detail.supplierName,
        orderId: this.detail.id,
        orderNo: this.detail.orderNo,
      }
    },

    async changeStatus(businessStatus) {
      let formData = { businessStatus }
      if (businessStatus == '已确认') {
        const param = await this.$refs.dialog.confirm()
        if (!param) return
        formData = { ...formData, ...param }
        formData.remark = '主管确认：' + param.remarks + '\n'
        formData.businessStatus = ''
      }
      if (businessStatus == '已安排') {
        const param = await this.$refs.dialog2.confirm()
        if (!param) return
        formData = { ...formData, ...param }
        formData.remark =
          this.detail.remark + '供应商安排修理：' + param.remarks + '\n'
      }
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/dockRepairApply/updateOrder',
        {
          id: this.detail.id,
          orderType: this.detail.orderType,
          ...formData,
        },
      )
      this.loading = false
      if (errorRaw) return
      if (businessStatus === '已确认') {
        this.loading = true
        const { errorRaw } = await this.getAsync(
          '/dockRepairApply/submitOrderById',
          {
            id: this.detail.id,
          },
        )
        this.loading = false
        if (errorRaw) return
      }
      this.$dialog.message.success('状态变更成功')
      this.$refs.detail.closeAndTo(this.backRouteName)
    },
  },

  mounted() {
    this.loadDetail()
    this.loadCom()
  },
}
</script>

<style></style>
