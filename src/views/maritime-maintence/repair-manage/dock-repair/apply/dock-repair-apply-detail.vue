<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修申请-${isEdit ? detail.applyNo : '新增'}`"
      :tooltip="isEdit ? detail.applyNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['坞修申请:编辑']"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <v-card-text>
            <v-form ref="form">
              <v-row>
                <v-col cols="12" md="3" class="py-0">
                  <v-ship-select
                    :readonly="isEdit"
                    v-model="detail.shipCode"
                    dense
                    :rules="[rules.required]"
                  ></v-ship-select>
                </v-col>
                <v-col cols="12" md="3" class="py-0">
                  <v-handler
                    label="申请人"
                    :readonly="isEdit"
                    v-model="detail.applicantId"
                    :init-user="initUser"
                    :use-current="!isEdit"
                    :rules="[rules.required]"
                  ></v-handler>
                </v-col>
                <v-col cols="12" md="3" class="py-0">
                  <v-ship-dept
                    label="申请部门"
                    v-model="detail.dept"
                    outlined
                    dense
                    :readonly="isEdit"
                    :items="['甲板部', '轮机部']"
                    :rules="[rules.required]"
                  ></v-ship-dept>
                </v-col>
                <v-col cols="12" md="3" class="py-0">
                  <vs-date-picker
                    label="申请时间"
                    v-model="detail.applyDate"
                    :readonly="isEdit"
                    outlined
                    use-today
                    dense
                    :rules="[rules.required]"
                  ></vs-date-picker>
                </v-col>
                <v-col cols="12" md="3" class="py-0">
                  <v-dict-select
                    dict-type="dock_type"
                    v-model="detail.estimatedRepairCategory"
                    label="修理类型"
                    :rules="[rules.required]"
                    :readonly="isEdit"
                  ></v-dict-select>
                </v-col>
                <v-col cols="12" md="3" class="py-0">
                  <v-select
                    label="业务类型"
                    outlined
                    dense
                    :items="[
                      { text: '机务', value: '1' },
                      { text: '通导', value: '2' },
                    ]"
                    v-model="detail.managerType"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="3" class="py-0">
                  <v-select
                    label="补充项目"
                    outlined
                    dense
                    :items="[
                      { text: '是', value: true },
                      { text: '否', value: false },
                    ]"
                    v-model="detail.isAdd"
                    readonly
                  ></v-select>
                </v-col>
                <!-- <v-col cols="12" md="3">
                  <port-select-dialog
                    v-model="detail.portId"
                    :rules="[rules.required]"
                    :initSelected="initPort"
                    :readonly="isEdit"
                  ></port-select-dialog>
                </v-col> -->
                <v-col cols="12" class="py-0">
                  <v-textarea
                    :readonly="detail.status == 3"
                    outlined
                    label="申请目的"
                    dense
                    v-model="detail.applyPurpose"
                    :rules="[rules.required]"
                  ></v-textarea>
                </v-col>
                <!-- <v-col cols="12">
                  <v-textarea
                    :readonly="detail.status == 3"
                    outlined
                    label="备注"
                    dense
                    v-model="detail.remark"
                  ></v-textarea>
                </v-col> -->
              </v-row>
            </v-form>
          </v-card-text>
        </v-container>
      </template>
      <template v-if="canEdit" #坞修明细按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="dialog = true"
          v-permission="['坞修申请:选择项目']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择项目
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['坞修申请:坞修明细删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #坞修明细>
        <v-table-list
          :headers="headers"
          :items="detail.detailList"
          item-key="itemTypeId"
          v-model="selected"
        >
          <!-- <template
            v-if="detail.status !== '3'"
            v-slot:[`item.remark`]="{ item }"
          >
            <v-text-field
              single-line
              dense
              v-model="item.remark"
            ></v-text-field>
          </template> -->

          <template
            v-if="detail.status !== '3'"
            v-slot:[`item.remark`]="{ item }"
          >
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  single-line
                  dense
                  @click="editRemark(item)"
                  v-bind="attrs"
                  v-on="on"
                  v-model="item.remark"
                ></v-text-field>
              </template>
              <span>{{ item.remark }}</span>
            </v-tooltip>
          </template>
        </v-table-list>
        <v-col>
          操作记录
          <v-textarea
            outlined
            dense
            readonly
            v-model="detail.operationRecord"
          ></v-textarea>
        </v-col>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <dock-item-select
      v-model="dialog"
      :items.sync="detail.detailList"
      @updatelog="(log) => operationRecord.push(...log)"
    ></dock-item-select>
    <v-dialog v-model="dialog1" max-width="600">
      <template v-slot:default="dialog1">
        <v-card style="height: 300px">
          <v-card-title>
            编辑项目说明
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-0" style="color: black">
                  项目编号：{{ editRemarkDetails.itemNo }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  项目名称：{{ editRemarkDetails.itemName }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  项目分类：{{ editRemarkDetails.itemTypeName }}
                </v-col>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="项目说明"
                    v-model="editRemarkDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
// import PortSelectDialog from '@/views/maritime-maintence/components/port-select-dialog.vue'
import DockItemSelect from './private/dock-item-select.vue'
export default {
  name: 'dock-repair-apply-detail',
  components: {
    // PortSelectDialog,
    DockItemSelect,
  },
  created() {
    this.backRouteName = 'dock-repair-apply-list'
    this.subtitles = ['基本信息', '坞修明细']
    this.headers = [
      { text: '项目编号', value: 'itemNo' },
      { text: '项目名称', value: 'itemName' },
      // { text: '项目分类', value: 'itemType' },
      { text: '项目分类', value: 'itemTypeName' },
      { text: '项目说明', value: 'remark', width: 300 },
    ]
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return (
        ['1', '2', '4'].includes(this.detail.status) ||
        this.detail.status == null
      )
    },
  },

  watch: {
    operationRecord: {
      handler(val) {
        this.detail.operationRecord = val.join('\n')
      },
      deep: true,
    },
    'detail.shipCode': {
      handler(val) {
        console.log(val)
        this.getIsAdd()
      },
      deep: true,
    },
  },

  data() {
    return {
      detail: {
        attachmentIds: [],
        attachmentRecords: [],
        detailList: [],
        isAdd: '',
      },
      initUser: false,
      dialog: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      initPort: {},
      delIds: [],
      selected: false,
      operationRecord: [],
      dialog1: false,
      editRemarkDetails: {},
    }
  },

  methods: {
    editRemark(item) {
      // console.log(item)
      this.editRemarkDetails = item
      this.dialog1 = true
    },
    saveRemark() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1 = false
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return false
      }
      if (this.detail.detailList.length === 0) {
        this.$dialog.message.error('请添加坞修明细')
        return false
      }
      const detailList = this.detail.detailList.map((i) => ({
        ...i,
        operationType: i.id ? '2' : '1',
        userId: i.id ? i.userId : this.$local.data.get('userInfo').id,
      }))
      const { errorRaw, data } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateApply',
        { ...this.detail, detailList: [...detailList, ...this.delIds] },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/dockRepairApply/submitApplyById',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfApplyById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
      this.initUser = {
        id: data.applicantId,
        nickName: data.applicantName,
      }
      // this.initPort = {
      //   id: data.portId,
      //   portCn: data.portName,
      // }
      this.operationRecord = data.operationRecord?.split('\n') ?? []
    },

    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const date = new Date()

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      const second = String(date.getSeconds()).padStart(2, '0')

      const datetime = `${year}-${month}-${day} ${hour}:${minute}:${second}`

      if (this.selected.id)
        this.delIds.push({
          id: this.selected.id,
          operationType: '3',
        })
      this.detail.detailList = this.detail.detailList.filter(
        (i) => this.selected.itemTypeId !== i.itemTypeId,
      )
      this.operationRecord.push(
        `项目名称：${this.selected.itemName}，项目编号：${
          this.selected.itemNo
        }，删除人：${
          this.$local.data.get('userInfo').nickName
        }，删除时间：${datetime}`,
      )
      this.select = false
    },
    async loadItemTypeNamw(val) {
      if (val) {
        val.forEach((item) => {
          // console.log(1111111111)
          // console.log(item)
          // console.log(2222222222222222)
          const { data } = this.getAsync(
            '/dockRepairItemType/getItemTypeById',
            {
              id: item.itemType,
            },
          )
          // console.log(data)
          if (data) {
            // console.log(data)
            // console.log(777)
            item.itemType = data.typeName
          }
        })
      }
    },
    async getIsAdd() {
      // 查询后端  是否存在坞修预算申请，有则为补充项目
      if (
        this.detail.status === 1 ||
        this.detail.status === 4 ||
        this.detail.status === undefined
      ) {
        if (this.detail.shipCode) {
          const { data } = await this.getAsync('/dockRepairApply/getIsAdd', {
            shipCode: this.detail.shipCode,
          })
          // console.log(data.id)
          if (data.id?.length > 0) {
            this.detail.isAdd = true
          } else {
            this.detail.isAdd = false
          }
        }
      }
    },
  },

  mounted() {
    this.loadDetail()
    this.getIsAdd()
  },
}
</script>

<style></style>
