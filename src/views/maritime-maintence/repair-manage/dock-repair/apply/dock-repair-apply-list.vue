<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      :search-dicts="searchDicts"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!canEnd"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="isItemEnd"
          v-permission="['坞修申请:完结']"
        >
          <v-icon left>mdi-close-circle</v-icon>
          完结
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'dock-repair-apply-detail', params: { id: 'new' } }"
          v-permission="['坞修申请:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['坞修申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <!-- <template v-slot:[`item.isEnd`]="{ item }">
        <v-chip small color="warning" v-if="item.isEnd == false">未完结</v-chip>
        <v-chip small v-if="item.isEnd == true">已完结</v-chip>
      </template> -->
      <template v-slot:[`item.managerType`]="{ item }">
        <v-chip small v-if="item.managerType == 1">机务</v-chip>
        <v-chip small v-if="item.managerType == 2">通导</v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applicantId	申请人id	string
// applicantName	申请人姓名	string
// applicantPost	申请人岗位	string
// applyDate	申请日期	string
// applyNo	申请单号	string
// applyPurpose	申请目的	string
// businessStatus	业务状态	string
// dept	申请部门	string
// id	物理主键	string
// portId	申请港口id	string
// portName	港口名称	string
// remark	备注	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// status	流程状态	string
export default {
  name: 'dock-repair-apply-list',
  created() {
    this.tableName = '坞修申请'
    this.reqUrl = '/dockRepairApply/getPageOfApply'
    this.searchDicts = [
      {
        dicType: 'dock_type',
        label: '修理类别',
        key: 'estimatedRepairCategory',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '申请单号', value: 'applyNo' },
      { text: '申请日期', value: 'applyDate' },
      { text: '申请人', value: 'applicantName' },
      { text: '申请人岗位', value: 'applicantPost' },
      { text: '申请部门', value: 'dept' },
      { text: '修理类别', value: 'estimatedRepairCategory' },
      { text: '业务类型', value: 'managerType' },
      // { text: '港口名称', value: 'portName' },
      { text: '申请目的', value: 'applyPurpose' },
      { text: '业务状态', value: 'businessStatus' },
      // { text: '是否完结', value: 'isEnd' },
      { text: '审批状态', value: 'status' },
      // { text: '备注', value: 'remark' },
    ]
    this.searchDate = {
      label: '申请日期',
      interval: true,
    }
    this.pushParams = { name: 'dock-repair-apply-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: true,
      },
    }
  },
  computed: {
    canEnd() {
      return this.selected.status == 3 && this.selected.isEnd == false
    },
  },
  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync('/dockRepairApply/deleteApply', {
        id: this.selected.id,
      })
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        this.$refs.table.loadTableData()
        this.selected = false
      }
    },
    async isItemEnd() {
      if (!(await this.$dialog.msgbox.confirm('确定完结此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/dockRepairApplyIsEnd',
        { applyId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`完结失败，请重试`)
        return
      }
      this.$dialog.message.success(`完结成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
