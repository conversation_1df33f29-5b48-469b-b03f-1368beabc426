<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        坞修项目选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          fuzzy-label="模糊搜索"
          outlined
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="3">
              <treeselect
                v-model="searchObj.itemType"
                :options="typeTreeList"
                placeholder="请选择坞修分类"
                style="z-index: 100"
              />
            </v-col>
          </template>
          <template #btns></template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="dialog = false">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: { Treeselect },
  name: 'spare-part-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/dockRepairItem/getPageOfDockRepairItem'
    this.headers = [
      { text: '项目编号', value: 'itemNo' },
      { text: '项目名称', value: 'itemName' },
      { text: '项目分类', value: 'itemTypeName' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊查询'
    this.searchDicts = [
      {
        dicType: 'dock_repiar_item_type',
        label: '项目分类',
        key: 'itemType',
      },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    items: Array,
  },
  data() {
    return {
      typeTreeList: [],
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {},
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    searchRemain(val) {
      this.searchObj = val
    },
    items(val) {
      this.selected = val.map((i) => {
        return { ...i, vid: i.id, id: i.itemTypeId }
      })
    },
  },
  computed: {},
  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        '/dockRepairItemType/getTypeTreeList',
      )
      this.typeTreeList.push(data)
    },
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const date = new Date()

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      const second = String(date.getSeconds()).padStart(2, '0')

      const datetime = `${year}-${month}-${day} ${hour}:${minute}:${second}`

      const items = this.selected.map((i) => {
        console.log(3333333333333)
        console.log(i)
        console.log(44444444444444)
        const comp = {
          ...i,
          itemTypeId: i.id,
          id: i.vid,
          userNickName: this.$local.data.get('userInfo').nickName,
        }
        return comp
      })
      const newItems = items
        .filter(
          (a) =>
            this.items.findIndex((b) => b.itemTypeId === a.itemTypeId) === -1,
        )
        .map(
          (i) =>
            `项目名称：${i.itemName}，项目编号：${i.itemNo}，添加人：${
              this.$local.data.get('userInfo').nickName
            }，添加时间：${datetime}`,
        )

      const delItems = this.items
        .filter(
          (a) => items.findIndex((b) => b.itemTypeId === a.itemTypeId) === -1,
        )
        .map(
          (i) =>
            `项目名称：${i.itemName}，项目编号：${i.itemNo}，删除人：${
              this.$local.data.get('userInfo').nickName
            }，删除时间：${datetime}`,
        )
      this.$emit('update:items', items)
      this.$emit('change', false)
      this.$emit('updatelog', [...newItems, ...delItems])
    },
  },
  mounted() {
    this.loadDetail()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
