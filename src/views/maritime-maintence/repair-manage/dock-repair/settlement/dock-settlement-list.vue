<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="结算单单号"
            outlined
            dense
            clearable
            v-model="searchObj.settlementNo"
          ></v-text-field>
        </v-col>
        <!-- <v-col cols="12" md="2">
          <v-text-field
            label="修理单单号"
            outlined
            dense
            clearable
            v-model="searchObj.orderNo"
          ></v-text-field>
        </v-col> -->
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'dock-settlement-new' }"
          v-permission="['坞修结算单:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="
            selected.status != 3 ||
            !['OA审核不通过', '业务审批通过'].includes(selected.businessStatus)
          "
          outlined
          tile
          color="primary"
          class="mx-1"
          @click.stop="sendOA"
          v-permission="['坞修结算单:发送OA']"
        >
          <v-icon left>mdi-send</v-icon>
          发送OA
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// businessStatus	业务状态	string
// estimatedExpense	预估金额	number
// handler	处理人	string
// handlerName	处理人姓名	string
// id	物理主键	string
// orderId	修理单id	string
// orderNo	修理单单号	string
// remark	备注	string
// settlementAmount	结算金额	number
// settlementNo	结算单单号	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO

export default {
  name: 'dock-settlement-list',
  created() {
    this.tableName = '坞修结算单'
    this.reqUrl = '/dockRepairApply/getPageOfSettlement'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '结算单单号', value: 'settlementNo' },
      { text: '修理单单号', value: 'orderNo' },
      { text: '预估金额', value: 'estimatedExpense' },
      { text: '结算金额', value: 'settlementAmount' },
      { text: '币别', value: 'ccyCode' },
      { text: '处理人', value: 'handlerName' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'dock-settlement-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: true,
      },
    }
  },

  methods: {
    async sendOA() {
      const { errorRaw } = await this.getAsync('/dockRepairApply/sendOAById', {
        id: this.selected.id,
      })
      if (!errorRaw) {
        this.$dialog.message.success('发送成功')
        this.selected = false
        await this.$refs.table.loadTableData()
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
