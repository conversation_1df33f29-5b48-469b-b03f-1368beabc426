<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    label="坞修修理单"
    :headers="headers"
    item-text="orderNo"
    :req-url="reqUrl"
    :search-remain="searchObj"
    @select="update"
    max-width="1300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
    :rules="[rules.required]"
  >
    <template v-slot:[`item.isDockRepair`]="{ item }">
      {{ item.isDockRepair ? '是' : '否' }}
    </template>
  </v-dialog-select>
</template>
<script>
export default {
  name: 'repair-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/dockRepairApply/getPageOfOrder'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '修理单号', value: 'orderNo' },
      { text: '修理费用', value: 'totalPrice' },
      { text: '币种', value: 'ccyCode' },
      { text: '折算美元', value: 'toUsd' },
      { text: '供应商', value: 'supplierName' },
      { text: '坞修类型', value: 'dockRepairType' },
    ]
  },
  props: {
    shipCode: String,
    ccyCode: String,
    value: [String, Object],
    disabled: [String, Boolean],
    readonly: [String, Boolean],
    numbers: Array,
    initSelected: Object,
    // read
  },
  data() {
    return {
      searchObj: {
        shipCode: '',
        status: 3,
        businessStatus: '全部完工',
        // dockRepairType: '厂修',   取消筛选，增加原始修理单超额申请，可选外协
        isSettlemented: false,
      },
      val: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  watch: {
    value(val) {
      this.val = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
  },

  methods: {
    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },
    update(val) {
      // console.log(val)
      this.$emit('update', val.id)
      this.$emit('update:ccyCode', val.ccyCode)
    },
    updateSearchObj() {
      //   // this.$nextTick(() => {
      //   // })
      //   if (this.searchObj.shipCode !== this.shipCode) {
      //     console.log('asd')
      //     this.searchObj.shipCode = ''
      //     this.$nextTick(() => {
      //       this.searchObj.shipCode = this.shipCode
      //     })
      //   }
    },
  },

  mounted() {
    this.searchObj.shipCode = this.shipCode
  },
}
</script>

<style></style>
