<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修结算单-${isEdit ? detail.settlementNo : '新增'}`"
      :tooltip="isEdit ? detail.settlementNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['坞修结算单:新增']"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #结算单信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  readonly
                  v-model="detail.shipCode"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3" v-for="(h, i) in tableFeilds" :key="i">
                <v-text-field
                  outlined
                  dense
                  v-model="detail[h.value]"
                  :label="h.text"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="结算总额"
                  dense
                  outlined
                  readonly
                  v-model="settlementAmount"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="备注"
                  dense
                  outlined
                  v-model="detail.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #结算明细>
        <v-form ref="form2">
          <v-table-list
            :show-select="false"
            :headers="headers"
            :items="detail.detailList"
          >
            <template v-slot:[`item.isCompleted`]="{ item }">
              {{
                item.isCompleted == 1
                  ? '是'
                  : item.isCompleted == 2
                  ? '取消'
                  : '否'
              }}
            </template>
            <template v-slot:[`item.price`]="{ item }">
              {{ item.repairPrice * item.repairDiscount }}
            </template>
            <template
              v-if="canEdit"
              v-slot:[`item.settlementAmount`]="{ item }"
            >
              <v-text-field
                v-if="item.isCompleted == 1"
                label="结算金额"
                single-line
                type="number"
                dense
                v-model="item.settlementAmount"
                @change="
                  (i) => {
                    item.deductionDiff =
                      item.settlementAmount -
                      item.repairPrice * item.repairDiscount
                  }
                "
              ></v-text-field>
              <v-text-field
                v-if="item.isCompleted == 2"
                label="结算金额"
                single-line
                type="number"
                dense
                v-model="item.settlementAmount"
                @change="
                  (i) => {
                    item.deductionDiff =
                      item.settlementAmount -
                      item.repairPrice * item.repairDiscount
                  }
                "
                readonly
              ></v-text-field>
            </template>
            <template v-if="canEdit" v-slot:[`item.remark`]="{ item }">
              <v-text-field
                label="项目说明"
                single-line
                dense
                v-model="item.remark"
                :rules="[rules.required]"
              ></v-text-field>
            </template>
            <!-- <template v-if="canEdit" v-slot:[`item.deductionDiff`]="{ item }">
              <v-text-field
                label="折减差"
                single-line
                type="number"
                dense
                v-model="item.deductionDiff"
                :readonly="canEdit"
              ></v-text-field>
            </template> -->
          </v-table-list>
        </v-form>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
// estimatedExpense	预估金额	number
// handler	处理人	string
// handlerName	处理人姓名	string
// id	物理主键	string
// orderId	修理单id	string
// orderNo	修理单单号	string
// remark	备注	string
// settlementAmount	结算金额	number
// settlementNo	结算单单号	string

// itemName	项目名称	string
// itemNo	项目编码	string
// remark	备注	string
// repairDiscount	修理折扣	number
// repairPrice	修理价格	number
// settlementAmount	结算金额	number
export default {
  name: 'dock-settlement-detail',
  created() {
    this.backRouteName = 'dock-settlement-list'
    this.subtitles = ['结算单信息', '结算明细']
    this.tableFeilds = [
      { text: '修理单单号', value: 'orderNo' },
      { text: '预估金额', value: 'estimatedExpense' },
      { text: '币种', value: 'ccyCode' },
      { text: '处理人', value: 'handlerName' },
    ]
    this.headers = [
      { text: '项目编码', value: 'itemNo' },
      { text: '项目名称', value: 'itemName' },
      { text: '是否完工', value: 'isCompleted' },
      { text: '初始报价', value: 'repairPrice' },
      { text: '结算金额', value: 'settlementAmount', width: 200 },
      { text: '折减差', value: 'deductionDiff', width: 200 },
      { text: '备注', value: 'remark', width: 300 },
    ]
  },
  data() {
    return {
      detail: {
        attachmentRecords: [],
        attachmentIds: [],
        detailList: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    settlementAmount() {
      return this.detail.detailList.reduce((a, b) => {
        return a + b.settlementAmount * 1
      }, 0)
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack, notMove) {
      if (!this.$refs.form.validate() || !this.$refs.form2.validate()) return
      const detailList = this.detail.detailList.map((item) => {
        return {
          ...item,
          operationType: 2,
        }
      })
      const { errorRaw } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateSettlement',
        { ...this.detail, detailList, settlementAmount: this.settlementAmount },
      )
      if (errorRaw) {
        return false
      }
      if (notMove) {
        return true
      }
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const res = await this.save(goBack, true)
      if (!res) {
        return false
      }
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/dockRepairApply/submitSettlementById',
          { id: this.detail.id },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadDetail() {
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfSettlementById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
