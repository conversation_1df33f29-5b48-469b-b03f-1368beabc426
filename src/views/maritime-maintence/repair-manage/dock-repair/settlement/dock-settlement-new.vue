<template>
  <v-container fluid>
    <v-detail-view
      title="坞修结算单-新增"
      tooltip="新增"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['坞修结算单:新增']"
    >
      <template #结算单信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <repair-selelct
                  :ship-code="detail.shipCode"
                  v-model="detail.orderId"
                  :initSelected="initOrder"
                  :rules="[rules.required]"
                ></repair-selelct>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  label="处理人"
                  v-model="detail.handler"
                  use-current
                  :rules="[rules.required]"
                ></v-handler>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import repairSelelct from './private/repair-selelct.vue'
export default {
  name: 'dock-settlement-new',
  components: {
    repairSelelct,
  },
  mixins: [routerControl],
  created() {
    this.backRouteName = 'dock-settlement-list'
    this.subtitles = ['结算单信息']
  },
  data() {
    return {
      detail: { orderId: '' },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },

  watch: {
    'detail.orderId'(val) {
      if (val) {
        this.save()
      }
    },
  },

  methods: {
    async save() {
      const { errorRaw, data } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateSettlement',
        this.detail,
      )
      if (errorRaw) return
      this.closeAndTo('dock-settlement-detail', { id: data })
    },
  },

  mounted() {},
}
</script>

<style></style>
