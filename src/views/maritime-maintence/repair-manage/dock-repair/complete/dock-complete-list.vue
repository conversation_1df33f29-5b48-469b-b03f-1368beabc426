<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      use-status
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="完工单号"
            outlined
            dense
            clearable
            v-model="searchObj.completeNo"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="修理单单号"
            outlined
            dense
            clearable
            v-model="searchObj.orderNo"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'dock-complete-new' }"
          v-permission="['坞修完工单:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status != 1 && selected.status != 4"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['坞修完工单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// businessStatus	业务状态	string
// completeDate	完工日期	string
// completeNo	完工单单号	string
// dept	填写部门	string
// handlerId	完工人id	string
// handlerName	完工人名称	string
// id	物理主键	string
// orderId	修理单id	string
// orderNo	修理单单号	string
// orderType	修理单类型：0-原始修理单/1-增量工程	string
// portId	修理单港口id	string
// portName	修理单港口名称	string
// remark	备注	string
// repairDate	修理日期	string
// shipInfo	船舶基础信息	ShipInfoDO
// status	流程状态	string
export default {
  name: 'dock-complete-list',
  created() {
    this.tableName = '坞修完工单'
    this.reqUrl = '/dockRepairApply/getPageOfCompletion'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '完工单号', value: 'completeNo' },
      { text: '修理单单号', value: 'orderNo' },
      { text: '完工日期', value: 'completeDate' },
      { text: '申请部门', value: 'dept' },
      { text: '完工人', value: 'handlerName' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'dock-complete-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: true,
      },
    }
  },

  methods: {
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync('/dockRepairApply/deleteApply', {
        id: this.selected.id,
      })
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        this.$refs.table.loadTableData()
        this.selected = false
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
