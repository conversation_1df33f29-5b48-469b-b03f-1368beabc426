<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修完工-新增`"
      tooltip="新增"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['坞修完工单:新增']"
    >
      <template #基本信息>
        <v-container fluid>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  v-model="detail.completeDate"
                  use-today
                  label="完工日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  dense
                  outlined
                  v-model="detail.dept"
                  label="申请部门"
                  :items="['甲板部', '轮机部']"
                  :rules="[rules.required]"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.handlerId"
                  use-current
                  :rules="[rules.required]"
                  label="完工人"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <repair-selelct
                  :ship-code="detail.shipCode"
                  v-model="detail.orderId"
                  :initSelected="initOrder"
                  :rules="[true]"
                ></repair-selelct>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="备注"
                  dense
                  v-model="detail.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
// applicantId	申请人id	string
// applicantNickName	申请人姓名	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// attachmentRecords	附件列表	array	CommonAttachment
// auditParams	流程参数	AuditParams	AuditParams
// businessStatus	业务状态	string
// completeDate	完工日期	string(date-time)
// completionNo	完工单单号	string
// componentOutId	备件出库单id	string
// dept	申请部门	string
// enquiryId	询价单id	string
// greaseOutId	滑油出库单id	string
// id	物理主键	string
// materialsOutId	物料出库单id	string
// orderId	修理单id	string
// otherExpense	其他费用	number
// quoteId	报价单id	string
// remark	备注	string
// repair	修理费用	number
import repairSelelct from './private/repair-selelct.vue'
export default {
  name: 'dock-complete-new',
  components: {
    repairSelelct,
  },
  mixins: [routerControl],
  created() {
    this.backRouteName = 'dock-complete-list'
    this.subtitles = ['基本信息']
    this.reqUrl = '/business/shipAffairs/dockRepair/orderPage'
    this.repairHeaders = [
      { text: '船舶', value: 'shipInfo' },
      { text: '修理单号', value: 'orderNo' },
      { text: '修理费用', value: 'totalPrice' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '出库日期', value: 'inoutDate' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '物品类型', value: 'inoutNature' },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
  },
  data() {
    return {
      detail: {
        attachmentIds: [],
        orderId: '',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchObj: {
        shipCode: '',
        businessStatus: '已安排',
        status: 3,
      },
      initOrder: {},
      outs: [],
      initHandler: {},
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
  },
  watch: {
    'detail.shipCode'(val) {
      if (val) {
        this.searchObj.shipCode = val
      }
    },
    'detail.orderId'(val) {
      if (val) {
        this.save(val)
      }
    },
  },

  methods: {
    async save(orderId) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (!orderId) {
        this.$dialog.message.error('请选择修理单')
        return
      }
      const { data, errorRaw } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateCompletion',
        {
          ...this.detail,
          orderId,
        },
      )
      if (errorRaw) return false
      this.closeAndTo('dock-complete-detail', { id: data })
    },
  },
}
</script>

<style></style>
