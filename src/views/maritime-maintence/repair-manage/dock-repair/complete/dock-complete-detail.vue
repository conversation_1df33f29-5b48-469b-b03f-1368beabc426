<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修完工-${isEdit ? detail.completeNo : '新增'}`"
      :tooltip="isEdit ? detail.completeNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['坞修完工单:编辑']"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template v-if="detail.auditParams && !isSupper" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <repair-selelct
                  :ship-code="detail.shipCode"
                  v-model="detail.orderId"
                  :readonly="isEdit"
                  :initSelected="initOrder"
                ></repair-selelct>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  :readonly="!canEdit"
                  v-model="detail.completeDate"
                  use-today
                  label="完工日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  :readonly="isEdit"
                  dense
                  outlined
                  v-model="detail.dept"
                  label="申请部门"
                  :items="['甲板部', '轮机部']"
                  :rules="[rules.required]"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.handlerId"
                  :use-current="!isEdit"
                  :rules="[rules.required]"
                  label="完工人"
                  :init-user="initHandler"
                ></v-handler>
              </v-col>

              <v-col cols="12" md="3">
                <v-select
                  label="业务类型"
                  outlined
                  dense
                  :items="[
                    { text: '机务', value: '1' },
                    { text: '通导', value: '2' },
                  ]"
                  v-model="detail.managerType"
                  readonly
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  :readonly="!canEdit"
                  outlined
                  label="备注"
                  dense
                  v-model="detail.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <!-- <template #消耗单按钮>
        <v-btn
          color="primary"
          @click="setOutParam('spare')"
          class="mx-1"
          small
          outlined
          tile
          :disabled="!!spareOutId"
        >
          备件消耗
        </v-btn>
        <v-btn
          color="primary"
          @click="setOutParam('material')"
          class="mx-1"
          small
          outlined
          tile
          :disabled="!!materialOutId"
        >
          物料消耗
        </v-btn>
        <v-btn
          color="primary"
          @click="setOutParam('soil')"
          class="mx-1"
          small
          outlined
          tile
          :disabled="!!soilOutId"
        >
          滑油消耗
        </v-btn>
      </template> -->
      <template v-if="canEdit" #完工明细按钮>
        <v-btn
          outlined
          :disabled="!selected"
          tile
          small
          color="primary"
          class="mx-1"
          @click.stop="finish"
          v-permission="['坞修完工单:完工']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          完工
        </v-btn>
        <v-btn
          outlined
          :disabled="!selected"
          tile
          small
          color="error"
          class="mx-1"
          @click.stop="cancleItem"
          v-permission="['坞修完工单:取消不做']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          取消不做
        </v-btn>
      </template>
      <template #完工明细>
        <v-table-list
          v-model="selected"
          :headers="headers"
          :items="detail.detailList"
        >
          <template v-slot:[`item.isCompleted`]="{ item }">
            {{
              item.isCompleted == 1
                ? '是'
                : item.isCompleted == 2
                ? '取消'
                : '否'
            }}
          </template>
          <template v-slot:[`item.componentOutId`]="{ item }">
            <router-link
              v-if="item.componentOutId"
              :to="{
                name: 'spare-out-detail',
                params: { id: item.componentOutId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
          <template v-slot:[`item.greaseOutId`]="{ item }">
            <router-link
              v-if="item.greaseOutId"
              :to="{
                name: 'soil-out-detail',
                params: { id: item.greaseOutId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
          <template v-slot:[`item.materialOutId`]="{ item }">
            <router-link
              v-if="item.materialOutId"
              :to="{
                name: 'materials-out-detail',
                params: { id: item.materialOutId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
        </v-table-list>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="(ids) => (detail.attachmentIds = ids)"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
        <v-card-text>
          <v-attach-list
            v-if="canUpload"
            title="验收单附件"
            :attachments="detail.attachmentRecords2"
            @change="(ids) => (detail.attachmentIds2 = ids)"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <complete-dialog
      v-model="dialog"
      :initialData="initialData"
      @success="success"
    ></complete-dialog>
  </v-container>
</template>
<script>
import CompleteDialog from './private/complete-dialog.vue'
// applicantId	申请人id	string
// applicantNickName	申请人姓名	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// attachmentRecords	附件列表	array	CommonAttachment
// auditParams	流程参数	AuditParams	AuditParams
// businessStatus	业务状态	string
// completeDate	完工日期	string(date-time)
// completionNo	完工单单号	string
// componentOutId	备件出库单id	string
// dept	申请部门	string
// enquiryId	询价单id	string
// greaseOutId	滑油出库单id	string
// id	物理主键	string
// materialsOutId	物料出库单id	string
// orderId	修理单id	string
// otherExpense	其他费用	number
// quoteId	报价单id	string
// remark	备注	string
// repair	修理费用	number

// completionId	完工单id	string
// componentOutId	备件出库单id	string
// greaseOutId	滑油出库单id	string
// id	物理主键	string
// isCompleted	是否完工	boolean
// itemDetailId	报价明细id	string
// itemId	项目id	string
// itemName	项目名称	string
// itemNo	项目编号	string
// materialOutId	物料出库单id	string
// remark	备注	string
// repairDetail	修理详情	string
// repairDiscount	修理折扣	number
// repairPrice	修理价格	number
import repairSelelct from './private/repair-selelct.vue'
export default {
  name: 'dock-complete-detail',
  components: {
    repairSelelct,
    CompleteDialog,
  },
  created() {
    this.backRouteName = 'dock-complete-list'
    this.subtitles = ['基本信息', '完工明细']
    this.reqUrl = '/business/shipAffairs/dockRepair/orderPage'
    this.repairHeaders = [
      { text: '船舶', value: 'shipInfo' },
      { text: '修理单号', value: 'orderNo' },
      { text: '修理费用', value: 'repairExpense' },
      { text: '其他费用', value: 'otherExpense' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.headers = [
      { text: '项目编号', value: 'itemNo' },
      { text: '项目名称', value: 'itemName' },
      { text: '是否完工', value: 'isCompleted' },
      { text: '完工内容', value: 'repairDetail' },
      { text: '备件', value: 'componentOutId' },
      { text: '滑油', value: 'greaseOutId' },
      { text: '物料', value: 'materialOutId' },
    ]
  },
  data() {
    return {
      detail: {
        attachmentIds: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchObj: {
        shipCode: '',
        businessStatus: '已安排',
      },
      initOrder: {},
      outs: [],
      initHandler: {},
      dialog: false,
      initialData: {},
      selected: false,
      canUpload: false,
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
    isSupper() {
      return this.$local.data.get('userInfo').userType == 4
    },
    // canUpload() {
    //   return (
    //     this.detail.attachmentRecords2.length > 0 ||
    //     this.datail.businessStatus.includes('安技')
    //   )
    // },
  },
  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { data, errorRaw } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateCompletion',
        {
          ...this.detail,
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      // 管船中心安技主管审批需提交附件

      if (
        this.detail.attachmentIds2.includes('') &&
        this.detail.attachmentIds2.length === 1
      ) {
        this.detail.attachmentIds2 = []
      }
      // const result =
      //   this.detail.attachmentIds2.includes('') &&
      //   this.detail.attachmentIds2.length === 1
      if (
        this.detail.businessStatus.includes('安技') &&
        this.detail.attachmentIds2.length === 0
      ) {
        this.$dialog.message.error('请上传验收单附件！')
        return
      }
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/dockRepairApply/submitCompletionById',
            { id: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },

    async loadDetail(onlyList = false) {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfCompletionById',
        { id: this.$route.params.id },
      )
      if (onlyList) {
        this.detail.detailList = data.detailList
        return
      }
      this.detail = {
        ...data,
        shipCode: data.shipInfo.shipCode,
      }
      this.canUpload =
        this.detail.attachmentRecords2.length > 0 ||
        data.businessStatus.includes('安技')
      // this.datail.businessStatus.indexOf('安技') != -1
      this.initOrder = { id: data.orderId, orderNo: data.orderNo }
      this.initHandler = { id: data.handlerId, nickName: data.handlerName }
    },

    finish() {
      this.initialData = {
        ...this.selected,
      }
      this.dialog = true
    },
    async cancleItem() {
      if (this.selected.isCompleted != 0) {
        this.$dialog.message.error('当前状态不可取消！')
        return
      }
      if (!(await this.$dialog.msgbox.confirm('确定取消此记录？'))) return
      // const { errorRaw } = await this.getAsync('/dockRepairApply/deleteApply', {
      //   id: this.selected.id,
      // })
      // if (!errorRaw) {
      //   this.$dialog.message.success('删除成功')
      //   this.$refs.table.loadTableData()
      //   this.selected = false
      // }
      const url = '/dockRepairApply/updateCompletionDetail'
      const { errorRaw } = await this.postAsync(url, {
        ...this.selected,
        isCompleted: 2,
      })
      if (!errorRaw) {
        await this.loadDetail(true)
      }
    },
    async success() {
      await this.loadDetail(true)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
