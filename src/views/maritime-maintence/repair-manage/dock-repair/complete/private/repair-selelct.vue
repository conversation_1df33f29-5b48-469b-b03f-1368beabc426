<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    label="坞修修理单"
    :headers="headers"
    item-text="orderNo"
    :req-url="reqUrl"
    :search-remain="searchObj"
    @update="update"
    max-width="1300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
  >
    <template v-slot:[`item.isDockRepair`]="{ item }">
      {{ item.isDockRepair ? '是' : '否' }}
    </template>
  </v-dialog-select>
</template>
<script>
export default {
  name: 'apply-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/dockRepairApply/getPageOfOrder'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '修理单号', value: 'orderNo' },
      { text: '修理费用', value: 'totalPrice' },
      { text: '币种', value: 'ccyCode' },
      { text: '折算美元', value: 'toUsd' },
      { text: '供应商', value: 'supplierName' },
      { text: '坞修类型', value: 'dockRepairType' },
    ]
  },
  props: {
    shipCode: String,
    value: [String, Object],
    disabled: [String, Boolean],
    readonly: [String, Boolean],
    numbers: Array,
    initSelected: Object,
    // read
  },
  data() {
    return {
      searchObj: {
        shipCode: '',
        status: 3,
        businessStatus: '已安排',
        orderType: 0,
      },
      val: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  watch: {
    value(val) {
      this.val = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
  },

  methods: {
    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },
    update() {
      this.$emit('update', this.val.id)
    },
    updateSearchObj() {
      //   // this.$nextTick(() => {
      //   // })
      //   if (this.searchObj.shipCode !== this.shipCode) {
      //     console.log('asd')
      //     this.searchObj.shipCode = ''
      //     this.$nextTick(() => {
      //       this.searchObj.shipCode = this.shipCode
      //     })
      //   }
    },
  },

  mounted() {
    this.searchObj.shipCode = this.shipCode
  },
}
</script>

<style></style>
