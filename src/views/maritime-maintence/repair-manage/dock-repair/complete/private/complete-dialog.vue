<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        完工情况
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          完工
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  label="完工日期"
                  v-model="formData.completeDate"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  :label="formData.isCompleted ? '完工' : '未完工'"
                  v-model="formData.isCompleted"
                  dense
                ></v-switch>
              </v-col> -->
              <v-col cols="12">
                <v-textarea
                  v-model="formData.repairDetail"
                  outlined
                  label="完工内容"
                ></v-textarea>
              </v-col>
              <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('spare')"
                  :disabled="!!spareOutId"
                >
                  备件消耗
                </v-btn>
              </v-col>
              <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('material')"
                  :disabled="!!materialOutId"
                >
                  物料消耗
                </v-btn>
              </v-col>
              <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('soil')"
                  :disabled="!!soilOutId"
                >
                  滑油消耗
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'complete-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    spareOutId() {
      return (
        this.formData.componentOutId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.id &&
            b.itemType === 'spare-out-detail' &&
            b.businessType === 'dock-complete-detail',
        )?.outId
      )
    },
    materialOutId() {
      return (
        this.formData.materialOutId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.id &&
            b.itemType === 'materials-out-detail' &&
            b.businessType === 'dock-complete-detail',
        )?.outId
      )
    },
    soilOutId() {
      return (
        this.formData.greaseOutId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.id &&
            b.itemType === 'soil-out-detail' &&
            b.businessType === 'dock-complete-detail',
        )?.outId
      )
    },
  },
  methods: {
    closeForm() {
      this.$store.commit('removeOutParamByItemId', this.formData.id)
      this.$emit('change', false)
    },
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/dockRepairApply/updateCompletionDetail'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        // TODO:根据业务类型获取出库单id
        componentOutId: this.spareOutId,
        materialOutId: this.materialOutId,
        greaseOutId: this.soilOutId,
        // isCompleted: true,
        isCompleted: 1,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },

    setOutParam(type) {
      const types = {
        spare: 'spare-out-detail',
        material: 'materials-out-detail',
        soil: 'soil-out-detail',
      }
      const businessItemId = this.formData.id
      const businessId = this.$route.params.id
      this.$store.commit('emitOut', {
        businessType: 'dock-complete-detail',
        businessItemId,
        businessId,
        itemType: types[type],
      })
      this.$router.push({
        name: types[type],
        params: { id: 'new' },
      })
    },
  },
  beforeDestroy() {
    this.$store.commit('removeOutParamByItemId', this.formData.id)
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
