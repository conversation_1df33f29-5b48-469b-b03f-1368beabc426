<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修询价-${detail.enquiryNo || '新增'}`"
      :tooltip="detail.enquiryNo || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="detail.auditParams && detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['坞修询价:编辑']"
      :can-save="false"
    >
      <template v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          v-if="!isEdit"
          width="90"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          v-permission="['坞修询价:发起询价']"
        >
          发起询价
        </v-btn>
        <v-btn
          v-if="
            detail.businessStatus == '报价完成' &&
            (detail.status == '1' || detail.status == '4')
          "
          tile
          color="primary"
          small
          class="mx-1"
          @click="updateIsWinsJw"
          :loading="quoteLoading"
          v-permission="['坞修询价:定标']"
        >
          <v-icon left>mdi-check</v-icon>
          定标
        </v-btn>
        <v-btn
          v-if="detail.pdfAttachment"
          :href="`/api/system/file/download?fileName=${encodeURIComponent(
            detail.pdfAttachment.name,
          )}&filePath=${detail.pdfAttachment.filePath}`"
          width="90"
          tile
          color="success"
          small
          class="mx-1"
        >
          <v-icon>mdi-download</v-icon>
          PDF
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3" class="py-0">
                <apply-selelct
                  :readonly="isEdit"
                  v-model="detail.applyId"
                  :rules="[rules.required]"
                  :initSelected="initApply"
                  :shipCode="detail.shipCode"
                  :roleName="roleName"
                ></apply-selelct>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-ship-select
                  :readonly="isEdit"
                  :disabled="detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <port-select-dialog
                  :readonly="isEdit"
                  :disabled="!detail.applyId"
                  v-model="detail.enquiryPortId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                  label="预计下线港口"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <vs-date-picker
                  :readonly="isEdit"
                  dense
                  outlined
                  label="修理日期"
                  v-model="detail.receivedDate"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col v-if="isEdit" cols="12" md="3" class="py-0">
                <vs-date-picker
                  dense
                  outlined
                  label="报价起始时间"
                  v-model="detail.startTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col v-if="isEdit" cols="12" md="3" class="py-0">
                <vs-date-picker
                  dense
                  outlined
                  label="报价截至时间"
                  v-model="detail.endTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-handler
                  label="询价人"
                  use-current
                  dense
                  :init-user="initUser"
                  outlined
                  :readonly="isEdit"
                  v-model="detail.handlerId"
                  :rules="[rules.required]"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="坞修类型"
                  dense
                  outlined
                  :readonly="isEdit"
                  v-model="detail.dockRepairType"
                  :rules="[rules.required]"
                  :items="['厂修', '外协']"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="业务类型"
                  outlined
                  dense
                  :items="[
                    { text: '机务', value: '1' },
                    { text: '通导', value: '2' },
                  ]"
                  v-model="detail.managerType"
                  readonly
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="补充项目"
                  outlined
                  dense
                  :items="[
                    { text: '是', value: true },
                    { text: '否', value: false },
                  ]"
                  v-model="detail.isAdd"
                  readonly
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-text-field
                  label="中标项目折算美金合计（开标后自动计算）"
                  dense
                  outlined
                  :readonly="true"
                  v-model="detail.total"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12" class="py-0">
                <v-textarea
                  label="项目说明"
                  :readonly="
                    !(
                      idIsNew ||
                      detail.businessStatus == '未提交' ||
                      detail.businessStatus == '超期' ||
                      detail.businessStatus == '报价完成' ||
                      detail.status == 4
                    )
                  "
                  dense
                  outlined
                  v-model="detail.remark"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
          <v-row v-if="!isEdit">
            <v-col cols="12">
              <enquiry-form
                ref="enquiry"
                @close="formShow = false"
                :shipCode="detail.shipCode"
                :happenDate="detail.receivedDate"
                dock-repair
                :businessType="'坞修'"
              ></enquiry-form>
            </v-col>
          </v-row>
        </v-container>
      </template>
      <template #询价详情按钮 v-if="canAddItem">
        <v-btn
          :disabled="!applyInfo.detailList"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click="enquiryDialog = true"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择询价项目
        </v-btn>
      </template>
      <template #询价详情>
        <v-table-list
          :show-select="false"
          v-model="selectedEn"
          :items="enquiryList"
          :headers="enquiryHeaders"
        ></v-table-list>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-if="canBid" #报价详情按钮>
        <!-- <v-btn
          :disabled="!selectedQuote"
          tile
          color="primary"
          small
          class="mx-1"
          @click="quoteBid"
          :loading="quoteLoading"
          v-permission="['坞修询价:定标']"
        >
          <v-icon left>mdi-check</v-icon>
          定标
        </v-btn> -->
      </template>
      <template #报价详情 v-if="isEdit">
        <v-table-list
          v-if="
            !(
              detail.businessStatus == '询价中' ||
              detail.businessStatus == '重新报价' ||
              detail.businessStatus == '超期无报价'
            )
          "
          :show-select="canBid"
          v-model="selectedQuote"
          :headers="quoteHeaders"
          :showSelect="false"
          :items="quoteList"
        >
          <template v-slot:[`item.isWin`]="{ item }">
            {{ item.isWin ? '是' : '否' }}
          </template>
          <template v-slot:[`item.isDockWhite`]="{ item }">
            {{ item.isDockWhite ? '是' : '否' }}
          </template>
        </v-table-list>
        <compare-price-table
          v-if="
            !(
              detail.businessStatus == '询价中' ||
              detail.businessStatus == '重新报价' ||
              detail.businessStatus == '超期无报价'
            )
          "
          :quoteIds="quoteIds"
          :businessStatus="detail.businessStatus"
          @refresh="refresh"
          ref="compareTable"
        ></compare-price-table>
      </template>
    </v-detail-view>
    <apply-item-selelct
      v-model="enquiryDialog"
      :items="applyInfo.detailList"
      :sitems.sync="enquiryList"
    ></apply-item-selelct>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import PortSelectDialog from '@/views/maritime-maintence/components/port-select-dialog.vue'
import ApplyItemSelelct from './private/apply-item-selelct.vue'
import applySelelct from './private/apply-selelct.vue'
import ComparePriceTable from './private/compare-price-table.vue'
import EnquiryForm from '@/views/maritime-maintence/components/enquiry/enquiry-form.vue'

export default {
  components: {
    applySelelct,
    PortSelectDialog,
    ApplyItemSelelct,
    ComparePriceTable,
    EnquiryForm,
  },
  name: 'dock-enquiry-detail',
  created() {
    this.backRouteName = 'dock-enquiry-list'
    this.subtitles = ['基本信息', '询价详情', '报价详情']
    this.roleName = this.$local.data.get('userInfo').roleName
    this.enquiryHeaders = [
      // TODO:备件号
      { text: '项目编号', value: 'itemNo' },
      { text: '项目名称', value: 'itemName' },
      { text: '备注', value: 'remark' },
    ]
    this.quoteHeaders = [
      { text: '报价单号', value: 'quoteNo' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '白名单', value: 'isDockWhite' },
      { text: '报价开始时间', value: 'startTime' },
      { text: '截止时间', value: 'endTime' },
      { text: '备货天数', value: 'stocksUpDays' },
      //{ text: '是否中标', value: 'isWin' },
      { text: '币种', value: 'ccyCode' },
      { text: '总价', value: 'totalPrice' },
      { text: '折算美元', value: 'toUsd' },
      { text: '状态', value: 'businessStatus' },
    ]
    //     ccyCode		string
    // deliveryDate	交付日期	string
    // discount	折扣	number
    // orderId	采购订单id	string
    // price	单价	number
    // purchaseNum	订单购买数量	number
    // supplierName	供应商	string
    this.hisHeader = [
      { text: '币种', value: 'ccyCode' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '单价', value: 'price' },
      { text: '折扣', value: 'discount' },
      { text: '订单购买数量', value: 'purchaseNum' },
      { text: '供应商', value: 'supplierName' },
    ]
  },
  mixins: [routerControl],
  data() {
    return {
      detail: {
        applyId: '',
        handlerId: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        isAdd: '',
      },
      initApply: {},
      initPort: {},
      initUser: false,
      enquiryList: [],
      applyInfo: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      selectedEn: false,
      enquiryDialog: false,
      engine: {},
      quoteList: [],
      quoteIds: [],
      history: [],
      selectedQuote: false,
      hisDialog: false,
      hisId: '',
      searchObj: { id: '' },
      quoteLoading: false,
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canAddItem() {
      return (
        !this.isEdit ||
        ['未提交', '审批已驳回'].includes(this.detail.businessStatus) ||
        this.detail.status === '4'
      )
    },
    canBid() {
      return (
        // ['超期', '报价完成'].includes(this.detail.businessStatus) &&
        // this.detail.status === '1'
        (this.detail.status == 1 && this.detail.businessStatus == '超期') ||
        (this.detail.status == 1 && this.detail.businessStatus == '报价完成') ||
        this.detail.status == 4
      )
    },
    idIsNew() {
      return this.$route.params.id == 'new'
    },
  },
  watch: {
    'detail.shipCode'(_, oldVal) {
      if (!oldVal) return
      this.clearApply()
    },
    'detail.applyId': {
      handler(val, oldVal) {
        this.loadApplyInfo(val)
        if (!oldVal) return
        this.clearComponents()
      },
      immediate: true,
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },

    async getHis(item) {
      this.searchObj.id = item.itemId
      // console.log(this.searchObj.id)
      await this.$nextTick()
      this.hisDialog = true
      await this.$nextTick()
      this.searchObj.id = item.itemId
    },

    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.enquiryList.length === 0) {
        this.$dialog.message.error('询价明细不能为空')
        return
      }
      const detailList = this.getCompWithOperation()
      if (!this.isEdit) {
        const dockRepairEnquirySubmitInputDTO = await this.$refs.enquiry.save()
        if (!dockRepairEnquirySubmitInputDTO) return
        const { errorRaw } = await this.postAsync(
          '/dockRepairApply/submitEnquiry',
          {
            dockRepairEnquiryModifyDTO: { ...this.detail, detailList },
            dockRepairEnquirySubmitInputDTO,
          },
        )
        if (!errorRaw) {
          this.closeAndTo(this.backRouteName)
        }
      } else {
        const { errorRaw } = await this.postAsync(
          '/dockRepairApply/saveOrUpdateEnquiry',
          { ...this.detail, detailList },
        )
        if (notMove) return errorRaw
        if (!errorRaw) goBack()
      }
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const errorRaw = await this.save(goBack, true)
      if (errorRaw) return
      const error = await this.$refs.audit.submit()
      if (!error) goBack()
    },

    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfEnquiryById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
      this.enquiryList = data.detailList.map((i) => {
        return {
          ...i,
        }
      })
      this.initPort = {
        portCn: data.enquiryPortName,
        id: data.enquiryPortId,
      }
      this.initUser = {
        id: data.handlerId,
        nickName: data.handlerName,
      }
      if (this.businessStatus !== '未提交') {
        await this.loadQuote()
      }
    },

    async loadApplyInfo(applyId) {
      if (!applyId) {
        this.applyInfo = {}
        return
      }
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfApplyById',
        { id: applyId },
      )
      this.applyInfo = data
      //   this.applyList = data.detailList
      if (!this.isEdit) {
        this.initPort = {
          portCn: data.portName,
          id: data.portId,
        }
        this.detail.enquiryPortId = data.portId
      }
      this.detail.isAdd = data.isAdd
      this.detail.managerType = data.managerType
      this.initApply = {
        applyNo: data.applyNo,
        id: data.id,
      }
      this.detail.shipCode = data.shipInfo.shipCode
    },

    getCompWithOperation() {
      const ids = this.enquiryList.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.detailList
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.enquiryList.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },

    clearApply() {
      this.$dialog.message.info('由于船舶变更,自动清空所选申请单')
      this.detail.applyId = ''
    },
    clearComponents() {
      this.$dialog.message.info('由于申请单变更,自动清空所选备件')
      this.applyInfo = {}
      this.enquiryList = []
      this.selectedEn = false
    },

    async loadQuote() {
      const { data } = await this.getAsync('/dockRepairApply/getPageOfQuote', {
        enquiryId: this.$route.params.id,
      })
      this.quoteList = data.records
      this.quoteIds = data.records.map((i) => i.id)
    },

    async quoteBid() {
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (
        !(await this.$dialog.msgbox.confirm(
          `确定 ${this.selectedQuote.supplierName} 中标`,
        ))
      )
        return

      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/openBidForEnquiry',
        { enquiryId: this.$route.params.id, quoteId: this.selectedQuote.id },
      )
      this.quoteLoading = false
      if (!errorRaw) await this.loadDetail()
    },
    async updateIsWinsJw() {
      const quoteWins = this.$refs.compareTable.getQuoteWins()
      if (quoteWins.length == 0) {
        this.$dialog.message.error('请勾选中标项目！')
        return
      }
      this.quoteLoading = true
      const quotesss = this.$refs.compareTable.getQuote()
      const { errorRaw } = await this.postAsync(
        '/dockRepairApply/dockRepairEnquiryBidUpdateIsWins',
        quotesss,
      )
      this.quoteLoading = false
      if (errorRaw) return
      this.quoteJw()
    },
    async quoteJw() {
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/openBidForEnquiry2',
        // { enquiryId: this.$route.params.id, quoteId: this.selectedQuote.id },
        {
          id: this.detail.id,
          remark: this.detail.remark,
        },
      )
      this.quoteLoading = false
      if (!errorRaw) await this.loadDetail()
      // if (!errorRaw) {
      //   this.closeAndTo(this.backRouteName)
      // }
    },
    refresh(value) {
      if (value) {
        this.loadDetail()
      }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
