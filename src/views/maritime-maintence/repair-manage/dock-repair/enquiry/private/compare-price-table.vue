<template>
  <v-card>
    <v-card-title>比价详情</v-card-title>
    <v-divider></v-divider>
    <v-simple-table class="use-divider">
      <template v-slot:default>
        <thead>
          <tr>
            <th colspan="3" class="text-left">供应商名称</th>
            <th
              v-for="sup of quotes"
              :key="sup.id"
              class="text-left"
              colspan="5"
            >
              {{ sup.supplierName }}
              <v-btn
                v-if="sup.businessStatus == '已填报'"
                width="90"
                tile
                color="error"
                small
                class="mx-1"
                @click="reBid(sup)"
                v-permission="['坞修询价:重新报价']"
              >
                重新报价
              </v-btn>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colspan="3">币种</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id">
              {{ sup.ccyCode }}
            </td>
          </tr>
          <tr>
            <td colspan="3">总价格</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id + '1'">
              {{ sup.total }}
            </td>
          </tr>
          <tr>
            <td colspan="3">总价格(美元)</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id + '2'">
              {{ sup.changeTotal }}
            </td>
          </tr>
          <tr>
            <th class="text-left">项目编号</th>
            <th class="text-left">项目名称</th>
            <th class="text-left">备注</th>
            <template v-for="sup of quotes">
              <th :key="'项目价格' + sup.id" class="text-left">项目价格</th>
              <th :key="'成交单价' + sup.id" class="text-left">成交单价</th>
              <th :key="'折算美元' + sup.id" class="text-left">折算美元</th>
              <th :key="'备注' + sup.id" class="text-left">备注</th>
              <th
                :key="'是否中标' + sup.id"
                class="text-left"
                style="white-space: nowrap"
              >
                是否中标
              </th>
            </template>
          </tr>
          <tr v-for="(sup, i) of quotes[0].detailList" :key="i">
            <template>
              <td :key="i + '项目编号'">
                {{ quotes[0].detailList[i].itemNo }}
              </td>
              <td :key="i + '项目名称'">
                {{ quotes[0].detailList[i].itemName }}
              </td>
              <td :key="i + '备注'">
                {{ quotes[0].detailList[i].remark }}
              </td>
              <template v-for="sup of quotes">
                <td :key="'项目价格' + sup.id">
                  {{ sup.detailList[i].repairPrice }}
                </td>
                <td :key="'成交单价' + sup.id">
                  <!-- <v-text-field
                    v-model="sup.detailList[i].finalPrice"
                    label="成交单价"
                    type="number"
                    dense
                    single-line
                  ></v-text-field> -->
                  <v-text-field
                    v-if="sup.ccyCode == 'JPY'"
                    @change="
                      () =>
                        (sup.detailList[i].finalPrice = Math.round(
                          sup.detailList[i].finalPrice,
                        ))
                    "
                    v-model="sup.detailList[i].finalPrice"
                    label="成交单价"
                    type="number"
                    :readonly="canEdit"
                    single-line
                    dense
                  ></v-text-field>
                  <v-text-field
                    v-else
                    @change="
                      () =>
                        (sup.detailList[i].finalPrice = (
                          Math.round(sup.detailList[i].finalPrice * 100) / 100
                        ).toFixed(2))
                    "
                    v-model="sup.detailList[i].finalPrice"
                    label="成交单价"
                    type="number"
                    :readonly="canEdit"
                    single-line
                    dense
                  ></v-text-field>
                </td>
                <td :key="'折算价格' + sup.id">
                  <!-- {{
                    Math.round(sup.detailList[i].repairPrice * sup.rate * 100) /
                    (100).toFixed(2)
                  }} -->
                  {{
                    Math.round(sup.detailList[i].finalPrice * sup.rate * 100) /
                    (100).toFixed(2)
                  }}
                </td>
                <td :key="'备注' + sup.id">
                  {{ sup.detailList[i].remark }}
                </td>
                <td :key="'是否中标' + sup.id">
                  <v-switch
                    v-model="sup.detailList[i].isWins"
                    @change="
                      trackingStateChanged(
                        $event,
                        sup.detailList[i].itemName,
                        sup.detailList[i].itemNo,
                        sup.detailList[i].id,
                        i,
                      )
                    "
                    :readonly="canEdit"
                    dense
                    color="success"
                  ></v-switch>
                </td>
              </template>
            </template>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
  </v-card>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'compare-price-table',
  mixins: [currencyHelper],
  props: {
    quoteIds: [],
    businessStatus: {
      type: String,
    },
  },
  data() {
    return {
      quotes: [{ detailList: {} }],
      canEdit: true,
    }
  },

  watch: {
    quoteIds(val) {
      if (val.length > 0) this.loadPrice()
    },
    businessStatus(val) {
      if (val == '报价完成' || val == '待商务主管定标' || val == '超期') {
        this.canEdit = false
      }
    },
  },

  methods: {
    async loadPrice() {
      let reqs = []
      for (const id of this.quoteIds) {
        reqs.push(
          this.getAsync('/dockRepairApply/getDetailOfQuoteById', {
            id,
          }),
        )
      }
      const results = await Promise.all(reqs)
      await this.loadCurrencyInfo()
      this.quotes = results.map((i) => ({
        ...i.data,
        rate: i.data.rateToMain,
        total: i.data.detailList.reduce(
          (x, { repairPrice, repairDiscount }) =>
            x + repairPrice * repairDiscount,
          0,
        ),
        changeTotal: i.data.toUsd,
      }))
    },
    getQuoteWins() {
      const quoteWins = []
      this.quotes.forEach((item) => {
        item.detailList.forEach((detail) => {
          if (detail.isWins) {
            quoteWins.push(detail)
          }
        })
      })
      return quoteWins
    },
    getQuote() {
      const quotess = []
      this.quotes.forEach((item) => {
        item.detailList.forEach((detail) => {
          // if (detail.isWins) {
          quotess.push(detail)
          // }
        })
      })
      return quotess
    },
    async reBid(sup) {
      // console.log(sup)
      if (!(await this.$dialog.msgbox.confirm('确定重新报价吗？'))) return
      const { errorRaw } = await this.postAsync('/dockRepairApply/reBid', {
        ...sup,
      })
      if (errorRaw) {
        return
      }
      this.$emit('refresh', true)
    },
    trackingStateChanged(event, itemName, itemNo, id, indexs) {
      // console.log(itemName)
      // console.log(itemNo)
      // console.log(id)
      // console.log(!event)
      // let elements = this.$el.querySelectorAll('.' + productId)
      // let tds = this.$refs.productId
      // let tds = this.$('td[key=' + productId + ']')
      // let tds = document.querySelector('.' + productId)
      // console.log(tds)
      // console.log(event) //event is undefined
      // console.log(productId) //productId is available e.g pr345665
      if (event) {
        this.quotes.forEach((item) => {
          item.detailList.forEach((detail, index) => {
            if (
              detail.id !== id &&
              detail.itemName == itemName &&
              detail.itemNo == itemNo &&
              index == indexs
            ) {
              // detail['isWins'] = !event
              detail.isWins = !event
              this.$set(item.detailList, index, detail)
            }
          })
        })
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
