<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    label="坞修申请单"
    :headers="headers"
    item-text="applyNo"
    :req-url="reqUrl"
    :search-remain="searchObj"
    @update="update"
    max-width="1300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
    :rules="[rules.required]"
  >
    <template v-slot:[`item.isDockRepair`]="{ item }">
      {{ item.isDockRepair ? '是' : '否' }}
    </template>
    <template v-slot:[`item.managerType`]="{ item }">
      <v-chip small v-if="item.managerType == 1">机务</v-chip>
      <v-chip small v-if="item.managerType == 2">通导</v-chip>
    </template>
    <template #searchflieds>
      <v-col cols="3">
        <v-select
          label="业务状态"
          dense
          outlined
          clearable
          v-model="searchObj.businessStatus"
          :items="businessStatuses"
        ></v-select>
      </v-col>
    </template>
  </v-dialog-select>
</template>
<script>
export default {
  name: 'apply-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/dockRepairApply/getPageOfApply'
    this.headers = [
      { text: '申请单号', value: 'applyNo' },
      { text: '申请日期', value: 'applyDate' },
      { text: '申请人', value: 'applicantName' },
      { text: '申请人岗位', value: 'applicantPost' },
      { text: '申请部门', value: 'dept' },
      { text: '修理类别', value: 'estimatedRepairCategory' },
      { text: '业务类型', value: 'managerType' },
      // { text: '港口名称', value: 'portName' },
      { text: '申请目的', value: 'applyPurpose' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.businessStatuses = ['部分询价', '全部询价', '待询价']
  },
  props: {
    shipCode: String,
    roleName: String,
    value: [String, Object],
    disabled: [String, Boolean],
    readonly: [String, Boolean],
    numbers: Array,
    initSelected: Object,
    // read
  },
  data() {
    return {
      searchObj: {
        shipCode: '',
        status: 3,
        businessStatus: '待询价',
        isEnd: false,
        managerType: '',
      },
      val: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  watch: {
    value(val) {
      this.val = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
    roleName(val) {
      if (val == '机务主管') {
        this.searchObj.managerType = '1'
      } else if (val == '通导主管') {
        this.searchObj.managerType = '2'
      } else if (val == '管理员') {
        this.searchObj.managerType = ''
      }
      // this.searchObj.managerType = val
    },
  },

  methods: {
    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },
    update() {
      this.$emit('update', this.val.id)
    },
    updateSearchObj() {
      //   // this.$nextTick(() => {
      //   // })
      //   if (this.searchObj.shipCode !== this.shipCode) {
      //     console.log('asd')
      //     this.searchObj.shipCode = ''
      //     this.$nextTick(() => {
      //       this.searchObj.shipCode = this.shipCode
      //     })
      //   }
    },
  },

  mounted() {
    this.searchObj.shipCode = this.shipCode
    if (this.roleName == '机务主管') {
      this.searchObj.managerType = '1'
    } else if (this.roleName == '通导主管') {
      this.searchObj.managerType = '2'
    } else if (this.roleName == '管理员') {
      this.searchObj.managerType = ''
    }
  },
}
</script>

<style></style>
