<template>
  <v-container fluid>
    <enquiry-form
      v-if="selected && formShow"
      @close="formShow = false"
      :req-url="'/dockRepairApply/submitEnquiry'"
      :enquiry="selected || { shipInfo: {} }"
      @success="loadTableData"
    ></enquiry-form>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      use-status
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-dept
            v-model="searchObj.dept"
            label="申请部门"
            outlined
            dense
            :items="['甲板部', '轮机部']"
          ></v-ship-dept>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'dock-enquiry-detail', params: { id: 'new' } }"
          v-permission="['坞修询价:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="
            selected.businessStatus != '审批通过,发送OA失败' &&
            selected.businessStatus != '审批通过,待发送OA立项'
          "
          outlined
          tile
          color="primary"
          class="mx-1"
          @click.stop="sendOA"
          v-permission="['坞修询价:发送OA']"
        >
          <v-icon left>mdi-send</v-icon>
          发送OA
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :disabled="selected.businessStatus !== '未提交'"
          @click="formShow = true"
          v-permission="['坞修询价:发起询价']"
        >
          <v-icon left>mdi-comment-question-outline</v-icon>
          发起询价
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :disabled="
            selected.businessStatus !== '询价中' &&
            selected.businessStatus !== '重新报价' &&
            selected.businessStatus !== '超期' &&
            selected.businessStatus !== '超期无报价'
          "
          @click="dialogDelay = true"
          v-permission="['坞修询价:延期']"
        >
          <v-icon left>mdi-timer-plus-outline</v-icon>
          延期
        </v-btn>
        <v-btn
          :disabled="selected.businessStatus !== '未提交'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delEnquiry"
          v-permission="['坞修询价:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="
            selected.businessStatus !== '未提交' &&
            selected.businessStatus !== '超期' &&
            selected.businessStatus !== '报价完成' &&
            selected.businessStatus !== '超期无报价' &&
            selected.businessStatus !== '询价中' &&
            selected.businessStatus !== '用户开标'
          "
          outlined
          tile
          color="error"
          class="mx-1"
          @click="disuse"
          v-permission="['坞修询价:废弃']"
        >
          <v-icon left>mdi-cancel</v-icon>
          废弃
        </v-btn>
      </template>
    </v-table-searchable>
    <delay-dialog
      v-model="dialogDelay"
      :enquiryId="selected.id"
      @success="loadTableData"
      type="dock"
    ></delay-dialog>
  </v-container>
</template>
<script>
import enquiryForm from '@/views/maritime-maintence/components/enquiry/enquiry-form.vue'
import delayDialog from '@/views/maritime-maintence/components/enquiry/delay-dialog.vue'
// applicantId	申请人id	string
// applicantName	申请人姓名	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// applyNo	申请单号	string
// applyPortId	申请港口id	string
// applyPortName	申请港口名称	string
// businessStatus	业务状态	string
// dept	申请部门	string
// dockRepairType	坞修类型：厂修/外协	string
// endTime	报价截止时间	string
// enquiryDate	询价日期	string
// enquiryNo	询价单号	string
// enquiryPortId	询价港口id	string
// enquiryPortName	询价港口名称	string
// handlerId	询价人id	string
// handlerName	询价人名称	string
// id	物理主键	string
// receivedDate	交货日期/修理日期	string
// remark	备注	string
// shipInfo	船舶编码	ShipInfoDO	ShipInfoDO
// startTime	报价开始时间	string
// status	流程状态	string
export default {
  components: { enquiryForm, delayDialog },
  name: 'dock-enquiry-list',

  created() {
    this.tableName = '坞修询价'
    this.reqUrl = '/dockRepairApply/getPageOfEnquiry'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '询价单号', value: 'enquiryNo' },
      { text: '申请部门', value: 'dept' },
      { text: '申请人', value: 'applicantName' },
      { text: '申请人岗位', value: 'applicantPost' },
      { text: '询价人', value: 'handlerName' },
      { text: '坞修类型', value: 'dockRepairType' },
      { text: '报价开始时间', value: 'startTime', hideDefault: true },
      { text: '截至报价时间', value: 'endTime' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.pushParams = { name: 'dock-enquiry-detail' }
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      selected: false,
      searchObj: { isMe: true },
      formShow: false,
      dialogDelay: false,
    }
  },

  methods: {
    async delEnquiry() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/deleteEnquiry',
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async loadTableData() {
      await this.$refs.table.loadTableData()
    },
    async disuse() {
      if (!(await this.$dialog.msgbox.confirm('确定废弃此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/disusePurchaseEnquiry',
        { enquiryId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`废弃失败，请重试`)
        return
      }
      this.$dialog.message.success(`废弃成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async sendOA() {
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/dockRepairEnquirySendOAById',
        {
          id: this.selected.id,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('发送成功')
        this.selected = false
        await this.$refs.table.loadTableData()
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
