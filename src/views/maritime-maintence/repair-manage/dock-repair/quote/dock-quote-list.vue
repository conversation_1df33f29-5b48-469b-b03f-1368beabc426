<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            label="业务状态"
            outlined
            dense
            :items="buses"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.quoteNo"
            label="报价单号"
            outlined
            dense
            clearable
          ></v-text-field>
        </v-col>
      </template>
      <template #btns></template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applyId	申请单id	string
// businessStatus	业务状态	string
// ccyCode	币种代码	string
// currencyId	币种id	string
// enquiryId	询价单id	string
// filledBy	填报人id	string
// id	物理主键	string
// otherExpense	其他费用	number
// quoteNo	报价单号	string
// remark	备注	string
// repairExpense	修理费用	number
// shipInfo	船舶编码	ShipInfoDO	ShipInfoDO
// status	流程状态	string
// supplierId	供应商id	string
// supplierName	供应商名称	string
export default {
  name: 'dock-quote-list',
  created() {
    this.tableName = '坞修报价'
    this.reqUrl = '/dockRepairApply/getPageOfQuote2'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '报价单号', value: 'quoteNo' },
      { text: '修理费用', value: 'totalPrice' },
      // { text: '成交单价', value: 'finalPrice' },
      { text: '报价开始时间', value: 'startTime' },
      { text: '报价截止时间', value: 'endTime' },
      { text: '币种', value: 'ccyCode' },
      { text: '供应商', value: 'supplierName' },
      { text: '填报人', value: 'filledBy' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'dock-quote-detail' }
    this.buses = ['未填报', '填报中', '已填报', '通过', '不通过', '废弃']
    // TODO:英文状态返回
    this.busesEn = [
      'not filled',
      'filling',
      'filled',
      'approved',
      'rejected',
      'abandoned',
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: {
        businessStatus: '',
      },
    }
  },

  methods: {},

  mounted() {},
}
</script>

<style></style>
