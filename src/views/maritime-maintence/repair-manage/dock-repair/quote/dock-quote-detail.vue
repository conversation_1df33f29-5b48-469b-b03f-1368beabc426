<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修报价-${detail.quoteNo}`"
      :tooltip="detail.quoteNo"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="canSubmit"
      :can-save="canSubmit"
      @save="save"
      @submit="submit"
      v-permission="['坞修报价:编辑']"
    >
      <template v-slot:基本信息-BasicInfo>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form" :readonly="!canSubmit">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="true"
                  v-model="detail.shipInfo.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.stocksUpDays"
                  :rules="[rules.required]"
                  label="备货天数/stockUpDays"
                  type="number"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  use-current
                  v-model="detail.filledBy"
                  :rules="[rules.required]"
                  label="填报人/filledBy"
                  dense
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.invoiceType"
                  :rules="[rules.required]"
                  label="发票类型/invoiceType"
                  dense
                  :items="发票类型"
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  disabled
                  dense
                  v-model="detail.ccyCode"
                  label="币种/ccyCode"
                  outlined
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  v-model="detail.termOfVail"
                  :rules="[rules.required]"
                  label="有效期"
                  outlined
                ></vs-date-picker>
              </v-col>

              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.taxRate"
                  :rules="[rules.required]"
                  label="发票税率/taxRate"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="tax"
                  :rules="[rules.required]"
                  label="税费/tax"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  :label="
                    detail.priceIncTax
                      ? '含税/taxIncluded'
                      : '不含税/excludingTax'
                  "
                  v-model="detail.priceIncTax"
                  dense
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="明细总价"
                  :rules="[rules.required]"
                  label="明细总价/detailTotalPrice"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="折扣后明细总价"
                  :rules="[rules.required]"
                  label="折扣后明细总价/totalDetailPriceAfterDiscount"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="折扣后总价"
                  :rules="[rules.required]"
                  label="折扣后总价/totalPriceAfterDiscount"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
            <v-card-text>
              <v-attach-list
                disabled
                :attachments="detail.attachmentRecords"
                @change="(ids) => (detail.attachmentIds = ids)"
              ></v-attach-list>
            </v-card-text>
          </v-form>
        </v-container>
      </template>
      <template #报价明细-QuoteDetail>
        <v-form ref="form2">
          <v-data-table
            :headers="canSubmit ? headers : headers2"
            :items="detail.detailList"
            hide-default-footer
            disable-pagination
            dense
            class="use-divider"
          >
            <template v-if="canSubmit" v-slot:[`item.quotNum`]="{ item }">
              <!-- <v-edit-dialog :return-value.sync="item.quotNumm">
              <div class="text-decoration-underline blue--text">
                {{ item.quotNum }}
              </div>
              <template v-slot:input>
                <v-text-field
                  v-model="item.quotNum"
                  label="报价数量"
                  type="number"
                  single-line
                ></v-text-field>
              </template>
            </v-edit-dialog> -->
              <v-text-field
                class="shrink"
                v-model="item.quotNum"
                @change="() => (item.quotNum = Math.round(item.quotNum))"
                label="报价数量/num"
                type="number"
                dense
                single-line
                :rules="[rules.required]"
              ></v-text-field>
            </template>
            <template v-if="canSubmit" v-slot:[`item.repairPrice`]="{ item }">
              <v-text-field
                v-if="isJPY"
                @change="
                  () => (item.repairPrice = Math.round(item.repairPrice))
                "
                v-model="item.repairPrice"
                label="修理费/price"
                type="number"
                single-line
                dense
              ></v-text-field>
              <v-text-field
                v-else
                @change="
                  () =>
                    (item.repairPrice = (
                      Math.round(item.repairPrice * 100) / 100
                    ).toFixed(2))
                "
                v-model="item.repairPrice"
                label="修理费/price"
                type="number"
                single-line
                dense
                :rules="[rules.required]"
              ></v-text-field>
            </template>
            <template
              v-if="canSubmit"
              v-slot:[`item.repairDiscount`]="{ item }"
            >
              <v-numeric
                v-if="!item.haveMainPrice"
                v-model="item.repairDiscount"
                precision="2"
                label="折扣率/discount"
                type="number"
                single-line
                dense
                :rules="[rules.required]"
                :max="1"
                :min="0"
              ></v-numeric>
              <div v-else>1</div>
            </template>
            <template v-if="canSubmit" v-slot:[`item.remark`]="{ item }">
              <!-- <v-text-field
                v-model="item.remark"
                single-line
                dense
              ></v-text-field> -->
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    @click="editRemark(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.remark"
                    single-line
                    dense
                  ></v-text-field>
                </template>
                <span>{{ item.remark }}</span>
              </v-tooltip>
            </template>
            <template v-slot:[`item.total`]="{ item }">
              {{
                isJPY
                  ? Math.round(item.repairPrice * item.repairDiscount)
                  : Math.round(item.repairPrice * item.repairDiscount * 100) /
                    100
              }}
            </template>
            <template v-if="!canSubmit" v-slot:[`item.isWins`]="{ item }">
              <v-chip small color="success" v-if="item.isWins">是</v-chip>
              <v-chip small color="warning" v-if="!item.isWins">否</v-chip>
            </template>
          </v-data-table>
        </v-form>
      </template>
    </v-detail-view>
    <v-dialog v-model="dialog1" max-width="600">
      <template v-slot:default="dialog1">
        <v-card style="height: 320px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'dock-quote-detail',
  mixins: [currencyHelper],
  created() {
    this.backRouteName = 'dock-quote-list'
    this.subtitles = ['基本信息-BasicInfo', '报价明细-QuoteDetail']
    this.发票类型 = [
      { text: '增值税专用发票', value: '0' },
      { text: '普通发票', value: '1' },
      { text: '形式发票', value: '2' },
    ]
    this.headers = [
      { text: '项目名称/itemName', value: 'itemName' },
      { text: '项目编号/itemNo', value: 'itemNo' },
      { text: '项目说明/itemEx', value: 'itemExplain' },
      { text: '单价/price', value: 'repairPrice', width: 150 },
      {
        text: '折扣率/discount',
        value: 'repairDiscount',
        width: 150,
        sortable: false,
      },
      { text: '总价/total', value: 'total' },
      { text: '供应商备注/supRemark', value: 'remark' },
    ]
    this.headers2 = [
      { text: '项目名称/itemName', value: 'itemName' },
      { text: '项目编号/itemNo', value: 'itemNo' },
      { text: '项目说明/itemEx', value: 'itemExplain' },
      { text: '单价/price', value: 'repairPrice', width: 150 },
      {
        text: '折扣率/discount',
        value: 'repairDiscount',
        width: 150,
        sortable: false,
      },
      { text: '总价/total', value: 'total' },
      { text: '供应商备注/supRemark', value: 'remark' },
      { text: '是否中标/successful bid', value: 'isWins' },
    ]
  },
  data() {
    return {
      detail: {
        businessStatus: '',
        currencyId: '',
        quoteNo: '',
        detailList: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      dialog1: false,
      editRemarkDetails: {},
    }
  },

  computed: {
    明细总价() {
      return this.detail.detailList.reduce(
        (x, { repairPrice }) => x + repairPrice * 1,
        0,
      )
    },
    折扣后明细总价() {
      return this.detail.detailList.reduce(
        (x, { repairPrice, repairDiscount }) =>
          x + Math.round(repairPrice * repairDiscount * 100) / 100,
        0,
      )
    },
    折扣后总价() {
      return this.折扣后明细总价 + (this.detail.otherExpense || 0) * 1
    },
    tax() {
      return this.折扣后总价 * (this.detail.taxRate || 0)
    },
    canSubmit() {
      return ['填报中', '未填报'].includes(this.detail.businessStatus)
    },
    isJPY() {
      return this.detail.ccyCode === 'JPY'
    },
    USD() {
      return (
        this.总价 *
        this.currencyInfo.find((item) => item.id === this.detail.currencyId)
          ?.rateToMain
      )
    },
  },

  methods: {
    editRemark(item) {
      // console.log(item)
      this.editRemarkDetails = item
      this.dialog1 = true
    },
    saveRemark() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1 = false
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return
      const detailList = this.detail.detailList.map((item) => {
        return {
          ...item,
          operationType: 2,
        }
      })
      const { errorRaw } = await this.postAsync(
        '/dockRepairApply/updateQuote',
        { ...this.detail, detailList },
      )
      if (errorRaw) return false
      if (notMove) return true
      goBack()
    },
    async submit(goBack) {
      if (!this.$refs.form2.validate()) return
      const res = await this.save(goBack, true)
      if (!res) return
      const { errorRaw } = await this.getAsync('/dockRepairApply/submitQuote', {
        id: this.$route.params.id,
      })
      if (errorRaw) return false
      goBack()
    },

    async loadDetail() {
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfQuoteById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.filledBy = this.$local.data.get('userInfo').nickName
      this.$refs.form.resetValidation()
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
