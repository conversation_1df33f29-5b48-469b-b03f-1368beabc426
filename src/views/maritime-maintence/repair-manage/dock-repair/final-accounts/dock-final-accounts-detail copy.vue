<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修预决算-${
        isEdit ? detail.accountNo + ' ' + detail.businessStatus : '新增'
      }`"
      :tooltip="isEdit ? detail.accountNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      :can-save="canSave"
      v-permission="['坞修预决算:编辑']"
    >
      <template #custombtns>
        <v-btn
          v-if="isOverBudget"
          outlined
          tile
          color="error"
          class="mx-1"
          v-permission="['坞修预决算:预算超额发OA']"
        >
          <v-icon left>mdi-send</v-icon>
          预算超额5%发OA
        </v-btn>
      </template>
      <template #基础信息>
        <v-container fluid>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :rules="[rules.required]"
                  v-model="detail.shipCode"
                  :readonly="isEdit"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dict-select
                  dict-type="dock_type"
                  v-model="detail.dockRepairType"
                  label="修理类型"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="上次修理日期"
                  dense
                  outlined
                  v-model="detail.dockRepairLastDate"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  label="修理日期"
                  dense
                  outlined
                  v-model="detail.repairTime"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="坞期（天数）"
                  dense
                  outlined
                  v-model="detail.dockTime"
                  type="number"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="预计停航(天数)"
                  dense
                  outlined
                  v-model="detail.ceaseNavigationDate"
                  type="number"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="预计坞修(天数)"
                  dense
                  outlined
                  v-model="detail.preRepairDate"
                  type="number"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  label="申请人"
                  disabled
                  outlined
                  v-model="detail.handler"
                  use-current
                  :init-user="initUser"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="oa账户"
                  dense
                  outlined
                  v-model="detail.oaAccount"
                  disabled
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="预计维修费用"
                  dense
                  outlined
                  v-model="detail.preRepairMoney"
                  readonly
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-dialog-select
                  v-if="!isEdit"
                  label="供应商"
                  item-text="name"
                  item-value="name"
                  v-model="detail.supplierIds"
                  :headers="supHeaders"
                  :rules="[rules.required]"
                  req-url="/business/shipAffairs/Supplier/list"
                  :search-remain="supSearchObj"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="供应商名称"
                        v-model="supSearchObj.name"
                      ></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select>
                <v-text-field
                  label="供应商"
                  v-else
                  dense
                  outlined
                  v-model="detail.supplierName"
                  readonly
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  :label="detail.isClaimDemage ? '索赔' : '不索赔'"
                  v-model="detail.isClaimDemage"
                  dense
                  :readonly="isEdit"
                ></v-switch>
              </v-col>
              <v-col v-if="detail.isClaimDemage" cols="12" md="3">
                <v-text-field
                  label="索赔金额"
                  dense
                  outlined
                  v-model="detail.claimDemage"
                  :readonly="isEdit"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="修理原因"
                  dense
                  outlined
                  v-model="detail.repairReason"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
          <v-card-text>
            <v-attach-list
              :attachments="detail.attachmentRecords"
              @change="(ids) => (detail.attachmentIds = ids)"
            ></v-attach-list>
          </v-card-text>
        </v-container>
      </template>
      <template v-if="!isEdit" #坞修费用按钮>
        <!-- <v-btn
          :disabled="!detail.shipCode"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="dialogOrder = true"
          v-permission="['坞修预决算:订单']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          {{ canAppend ? '追加' : '选择' }}订单预算
        </v-btn> -->
        <v-btn
          :disabled="!detail.shipCode"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="dialogRepair = true"
          v-permission="['坞修预决算:修理单']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          <!-- {{ canAppend ? '追加' : '选择' }}修理单预算 -->
          选择预算(坞修修理单/坞修采购订单)
        </v-btn>
        <!-- <v-btn
          :disabled="!selected || (canAppend ? !selected.addFlag : false)"
          small
          outlined
          tile
          color="error"
          class="mx-1"
          v-permission="['坞修预决算:编辑']"
          @click="deleteOrder"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn> -->
      </template>
      <template #坞修费用>
        <v-table-list
          v-model="selected"
          item-key="orderCode"
          :headers="projectHeaders"
          :items="items"
        >
          <template
            v-if="canAppend"
            v-slot:[`item.accountDiffExplain`]="{ item }"
          >
            <v-text-field
              label="说明"
              dense
              single-line
              v-model="item.accountDiffExplain"
            ></v-text-field>
          </template>
          <template v-slot:[`item.priceIncTax`]="{ item }">
            {{ item.priceIncTax ? '是' : '否' }}
          </template>
          <template v-slot:[`item.addFlag`]="{ item }">
            {{ item.addFlag ? '是' : '否' }}
          </template>
          <template v-slot:[`item.costProjectStatus`]="{ item }">
            {{ stateMap[item.costProjectStatus] }}
          </template>
        </v-table-list>
      </template>
      <template v-if="!isEdit" #非坞修费用按钮>
        <v-btn
          :disabled="!detail.shipCode"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="dialogOrderFixed = true"
          v-permission="['坞修预决算:非坞修订单']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择固定资产预算
        </v-btn>
      </template>
      <template #非坞修费用>
        <v-table-list
          v-model="selected"
          item-key="orderCode"
          :headers="projectHeaders"
          :items="itemsFixed"
        >
          <template
            v-if="canAppend"
            v-slot:[`item.accountDiffExplain`]="{ item }"
          >
            <v-text-field
              label="说明"
              dense
              single-line
              v-model="item.accountDiffExplain"
            ></v-text-field>
          </template>
          <template v-slot:[`item.priceIncTax`]="{ item }">
            {{ item.priceIncTax ? '是' : '否' }}
          </template>
          <template v-slot:[`item.addFlag`]="{ item }">
            {{ item.addFlag ? '是' : '否' }}
          </template>
          <template v-slot:[`item.costProjectStatus`]="{ item }">
            {{ stateMap[item.costProjectStatus] }}
          </template>
        </v-table-list>
      </template>
      <template v-if="canAppend" #补充项目按钮>
        <v-btn
          :disabled="!detail.shipCode"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['坞修预决算:补充项目']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择补充项目
        </v-btn>
      </template>
      <template v-if="canAppend" #补充项目>
        <v-table-list
          v-model="selected"
          item-key="orderCode"
          :headers="projectHeaders"
          :items="itemsAdd"
        >
          <template
            v-if="canAppend"
            v-slot:[`item.accountDiffExplain`]="{ item }"
          >
            <v-text-field
              label="说明"
              dense
              single-line
              v-model="item.accountDiffExplain"
            ></v-text-field>
          </template>
          <template v-slot:[`item.priceIncTax`]="{ item }">
            {{ item.priceIncTax ? '是' : '否' }}
          </template>
          <template v-slot:[`item.addFlag`]="{ item }">
            {{ item.addFlag ? '是' : '否' }}
          </template>
          <template v-slot:[`item.costProjectStatus`]="{ item }">
            {{ stateMap[item.costProjectStatus] }}
          </template>
        </v-table-list>
      </template>
      <template v-if="!isEdit || canAppend" #费用科目按钮>
        <!-- <v-btn
          small
          outlined
          tile
          color="warining"
          class="mx-1"
          @click="save(goBackExe, false, true)"
          v-permission="['坞修预决算:生成']"
        >
          <v-icon left>mdi-file-eye-outline</v-icon>
          {{ canAppend ? '追加' : '生成' }}并查看
        </v-btn> -->
      </template>
      <!-- <template v-if="isEdit" #费用科目> -->
      <template #费用科目>
        <v-table-list
          :show-select="false"
          :headers="headers"
          :items="detail.accountDetailList"
        >
          <template v-slot:[`item.isOverBudget`]="{ item }">
            {{ item.isOverBudget ? '是' : '否' }}
          </template>
        </v-table-list>
      </template>
      <template v-if="isDecide" #坞修评价>
        <v-container fluid>
          <v-form ref="cform">
            <v-row>
              <v-col
                class="pt-0"
                v-for="h in 评价内容"
                :key="h.value"
                cols="12"
                :md="h.value === 'remark' ? 12 : 3"
              >
                <vs-date-picker
                  v-if="h.type == 'date'"
                  v-model="detail.dockRepairEvaluationOutputDTO[h.value]"
                  :label="h.text"
                  outlined
                  dense
                ></vs-date-picker>
                <v-select
                  v-else-if="h.type == 'yn'"
                  v-model="detail.dockRepairEvaluationOutputDTO[h.value]"
                  :label="h.text"
                  outlined
                  dense
                  :items="yn"
                ></v-select>
                <v-dict-select
                  v-else-if="h.type == 'dict'"
                  dict-type="dock_type"
                  v-model="detail.dockRepairEvaluationOutputDTO[h.value]"
                  :label="h.text"
                ></v-dict-select>
                <v-textarea
                  v-else-if="h.value == 'remark'"
                  outlined
                  dense
                  v-model="detail.dockRepairEvaluationOutputDTO[h.value]"
                  :label="h.text"
                ></v-textarea>
                <v-text-field
                  v-else
                  v-model="detail.dockRepairEvaluationOutputDTO[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-card-text>
                <v-attach-list
                  title="评价附件"
                  :attachments="
                    detail.dockRepairEvaluationOutputDTO.attachmentRecords
                  "
                  @change="
                    (ids) =>
                      (detail.dockRepairEvaluationOutputDTO.attachmentIds = ids)
                  "
                ></v-attach-list>
              </v-card-text>
            </v-row>
          </v-form>
        </v-container>
      </template>
    </v-detail-view>
    <order-select
      v-model="dialogOrder"
      :ship-code="detail.shipCode"
      :is-fixed="false"
      :orders.sync="orders"
      :isAppend="canAppend"
    ></order-select>
    <order-fixed-select
      v-model="dialogOrderFixed"
      :ship-code="detail.shipCode"
      :is-fixed="true"
      :orders.sync="ordersFixed"
      :isAppend="canAppend"
    ></order-fixed-select>
    <!-- <repair-select
      v-model="dialogRepair"
      :ship-code="detail.shipCode"
      :repairs.sync="repairs"
      :isAppend="canAppend"
    ></repair-select> -->
    <budget-select
      v-model="dialogRepair"
      :ship-code="detail.shipCode"
      :repairs.sync="repairs"
      :isAppend="canAppend"
    ></budget-select>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import orderSelect from './private/order-select.vue'
import orderFixedSelect from './private/order-fixed-select.vue'
// import RepairSelect from './private/repair-select.vue'
import BudgetSelect from './private/budget-select.vue'
import { stateMap } from '@/views/finance-affairs/private/constant'
export default {
  components: { orderSelect, orderFixedSelect, BudgetSelect },
  name: 'dock-final-accounts-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'dock-final-accounts-list'
    this.subtitles = [
      '基础信息',
      '坞修费用',
      '非坞修费用',
      '补充项目',
      '费用科目',
      '坞修评价',
    ]
    this.tableFeilds = [
      { text: '是否索赔', value: 'isClaimDemage' },
      { text: '预计索赔金额', value: 'claimDemage' },
      { text: '修理类型', value: 'dockRepairType' },
      { text: '上次日期', value: 'dockRepairLastDate' },
      { text: '坞期（天数）', value: 'dockTime' },
      { text: '预计停航(天数)', value: 'ceaseNavigationDate' },
      { text: '申请人', value: 'handlerName' },
      { text: '申请人oa账户', value: 'oaAccount' },
      { text: '预计坞修(天数)', value: 'preRepairDate' },
      { text: '预计维修费用', value: 'preRepairMoney' },
      { text: '修理原因', value: 'repairReason' },
      { text: '本次修理日期', value: 'repairTime' },
      { text: '供应商名称', value: 'supplierName' },
      // { text: '备注', value: 'remark' },
    ]
    // accountDiff	预决算差额	number
    // accountDiffExplain	预决算差额说明	string
    // costSubjectName	费用科目	string
    // curBudget	本次预算/本次维修预算	number
    // curSettlementAmount	本次决算	number
    // yearBudget	年度预算	string
    this.headers = [
      { text: '费用科目', value: 'costSubjectName' },
      { text: '本次预算', value: 'curBudget' },
      { text: '年度预算', value: 'yearBudget' },
      { text: '本次预算是否超额', value: 'isOverBudget' },
      { text: '本次决算', value: 'curSettlementAmount' },
      { text: '预决算差额', value: 'accountDiff' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]

    this.projectHeaders2 = [
      { text: '修理单/订单号', value: 'orderCode' },
      { text: '费用科目', value: 'costSubjectName' },
      { text: '供应商', value: 'supplierName' },
      { text: '本次维修预算（USD/RMB/JPY）', value: 'curBudget' },
      { text: '货币', value: 'ccyCode' },
      { text: '本次预算折合（USD）', value: 'toUsd' },
      // { text: '结算金额', value: 'settlementAmount' },
      // { text: '是否追加', value: 'addFlag' },
      // { text: '追加比例', value: 'addProportion' },
      // { text: '费用项目状态', value: 'costProjectStatus' },
      { text: '费用状态', value: 'costProjectStatus' },
      { text: '决算金额（USD）', value: 'curSettlementAmount' },
      { text: '预决算差额（USD）', value: 'accountDiff' },
      { text: '是否含税', value: 'priceIncTax' },
      { text: '预决算差额说明（USD）', value: 'accountDiffExplain' },
    ]
    this.projectHeaders = [
      { text: '数据来源', value: 'dataSource' },
      // { text: '是否有前置立项', value: 'needSendOa', hideDefault: true },
      // { text: 'OA项目编号', value: 'itemNo', hideDefault: true },
      { text: '修理单/订单号', value: 'orderNo' },
      { text: '供应商', value: 'supplierName' },
      // { text: '供应项目', value: 'supplyItem' },
      { text: '费用科目', value: 'costSubjectName' },
      { text: '货币', value: 'currencyName' },
      { text: '本次维修预算（USD/RMB/JPY）', value: 'money' },
      { text: '本次预算折合（USD）', value: 'moneyUsd' },
      { text: '预算状态', value: 'businessStatus' },
      { text: '决算金额（USD）', value: 'finalMoney' },
      { text: '预决算差额（USD）', value: 'accountDiff' },
      { text: '是否含税', value: 'priceIncTax' },
      { text: '预决算差额说明（USD）', value: 'accountDiffExplain' },
      // { text: '发生日期', value: 'proposedDate' },
      // { text: '实际申请人', value: 'applicantName' },
      // { text: '录单人', value: 'applyUserName' },
      // { text: '预算说明', value: 'applyRemark' },
      // { text: '审批状态', value: 'status' },
      // { text: '是否生成费用', value: 'isGenCost' },
      // { text: '生成预算报文时间', value: 'budgetTime' },
      // { text: '生成冲销报文时间', value: 'writeOffTime' },
      // { text: '附件', value: 'attachmentRecords' },
      // { text: '链接', value: 'toDetail', hideDefault: true },
    ]
    //     attachmentRecords	附件ids	array	CommonAttachment
    // cctv	CCTV	boolean
    // fishingNetCollector	渔网收集器	boolean
    // handler	处理人	string
    // id	物理主键	string
    // ioppExpirationTime	IOPP证书到期时间	string
    // nextRepairType	下次修理类别	string
    // preRepairDate	下次修理时间	string
    // remark	备注	string
    // repairDate	坞修时间	string
    // repairType	本次修理类别	string
    // shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
    // tailInspectionTime	尾轴抽检时间	string
    // waterTreatmentInstallTime	压载水处理装最迟安装时间	string
    this.评价内容 = [
      { text: '坞修时间', value: 'repairDate', type: 'date' },
      { text: '本次修理类别', value: 'repairType', type: 'dict' },
      { text: '下次修理时间', value: 'preRepairDate', type: 'date' },
      { text: '下次修理类别', value: 'nextRepairType', type: 'dict' },
      { text: 'CCTV', value: 'cctv', type: 'yn' },
      { text: '渔网收集器', value: 'fishingNetCollector', type: 'yn' },
      { text: 'IOPP证书到期时间', value: 'ioppExpirationTime', type: 'date' },
      { text: '尾轴抽检时间', value: 'tailInspectionTime', type: 'date' },
      {
        text: '压载水处理装最迟安装时间',
        value: 'waterTreatmentInstallTime',
        type: 'date',
      },
      { text: '备注', value: 'remark' },
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
    this.stateMap = stateMap
  },
  data() {
    return {
      detail: {
        attachmentRecords: [],
        attachmentIds: [],
        oaAccount: this.$local.data.get('userInfo').oaAccount,
        detailList: [],
        detailList1: [],
        detailList2: [],
        accountDetailList: [],
        dockRepairEvaluationOutputDTO: {
          attachmentRecords: [],
          attachmentIds: [],
        },
        businessStatus: '',
      },
      supSearchObj: { name: '' },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      dialogOrder: false,
      dialogOrderFixed: false,
      dialogRepair: false,
      orders: [],
      ordersFixed: [],
      repairs: [],
      repairsAdd: [],
      orderSubjects: [],
      repairSubjects: [],
      delList: [],
      selected: false,
      initUser: false,
      canSave: true,
      isOverBudget: false,
    }
  },

  watch: {
    orders(val) {
      if (this.canAppend) {
        this.loadPreview(val)
        return
      }
      this.loadAllOrders(val)
    },
    ordersFixed(val) {
      if (this.canAppend) {
        this.loadPreview(val)
        return
      }
      this.loadAllOrdersFixed(val)
    },
    repairs(val) {
      if (this.canAppend) {
        this.loadPreview(val)
        return
      }
      this.loadAllRepairs(val)
    },
    repairsAdd(val) {
      if (this.canAppend) {
        this.loadPreview(val)
        return
      }
      this.loadAllRepairs(val)
    },
    items: {
      handler(val) {
        if (val.length > 0)
          this.detail.preRepairMoney = val.reduce((a, b) => a + b.moneyUsd, 0)
      },
      deep: true,
    },
    itemsAdd: {
      handler(val) {
        if (val.length > 0)
          this.detail.preRepairMoney = val.reduce((a, b) => a + b.moneyUsd, 0)
      },
      deep: true,
    },
    itemsFixed: {
      handler(val) {
        if (val.length > 0)
          this.detail.preRepairMoney = val.reduce((a, b) => a + b.moneyUsd, 0)
      },
      deep: true,
    },
    'detail.shipCode'(val) {
      console.log(val)
    },
    'detail.accountDetailList'(val) {
      if (
        val &&
        (this.detail.businessStatus == undefined ||
          this.detail.businessStatus == '预算未提交')
      ) {
        console.log(this.detail.businessStatus)
        if (val.length > 0)
          val.forEach((element) => {
            if (element.isOverBudget) {
              this.isOverBudget = true
              return
            }
          })
      }
    },
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    items() {
      return [...this.orders, ...this.repairs, ...this.detail.detailList]
    },
    itemsAdd() {
      return [...this.repairsAdd, ...this.detail.detailList2]
    },
    itemsFixed() {
      return [...this.ordersFixed, ...this.detail.detailList1]
    },
    canAppend() {
      return ['预算通过', '决算未提交', '决算不通过'].includes(
        this.detail.businessStatus,
      )
    },
    isDecide() {
      // return ['决算通过', '决算未提交', '决算不通过'].includes(
      //   this.detail.businessStatus,
      // )
      return ['决算通过'].includes(this.detail.businessStatus)
    },
  },
  methods: {
    async save(goBack, notMove = false, refresh = false) {
      if (!this.$refs.form.validate()) return false
      const detailList = this.items.map((i) => ({
        orderNo: i.businessId,
        operationType: i.operationType || 2,
        orderCate: i.orderCate || i.orderType === '坞修单' ? 1 : 0,
        shipCode: this.detail.shipCode,
        addFlag: i.addFlag,
        accountDiffExplain: i.accountDiffExplain,
        type: 0,
      }))
      const detailList1 = this.itemsFixed.map((i) => ({
        // orderNo: i.orderNo || i.id,
        orderNo: i.businessId,
        operationType: i.operationType || 2,
        orderCate: i.orderCate || i.orderType === '坞修单' ? 1 : 0,
        shipCode: this.detail.shipCode,
        addFlag: i.addFlag,
        accountDiffExplain: i.accountDiffExplain,
        type: 1,
      }))
      detailList.push(...detailList1)
      const detailList2 = this.itemsAdd.map((i) => ({
        orderNo: i.businessId,
        operationType: i.operationType || 2,
        orderCate: i.orderCate || i.orderType === '坞修单' ? 1 : 0,
        shipCode: this.detail.shipCode,
        addFlag: i.addFlag,
        accountDiffExplain: i.accountDiffExplain,
        type: 2,
      }))
      detailList.push(...detailList2)
      const { errorRaw, data } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateAccountsBudget',
        {
          ...this.detail,
          detailList,
          dockRepairEvaluationModifyDTO: {
            ...this.detail.dockRepairEvaluationOutputDTO,
            operationType: this.isEdit ? 2 : 1,
          },
        },
      )
      if (errorRaw) return false
      if (!notMove) {
        if (refresh) {
          this.$parent.$parent.$parent.refresh(null, this.$options.name)
          // console.log(this.$parent.$parent.$parent.$options.name)
          return
        }
        goBack()
        return
      }
      this.closeAndTo('dock-final-accounts-detail', { id: data })
    },

    async loadOrderProjects(orderId) {
      const { data } = await this.getAsync(
        '/dockRepairApply/getProjectInfoOrDockPurchaseOrder',
        { orderId },
      )
      const order = this.orders.find((i) => i.id === orderId)
      this.$set(order, 'costSubjectName', data[0].costSubjectName)
      // this.$set(order, 'moneyUsd', data[0].toUsd)
      // this.$set(order, 'money', data[0].totalPrice)
      this.dockRepairAccountPreview()
    },
    async loadOrderFixedProjects(orderId) {
      const { data } = await this.getAsync(
        '/dockRepairApply/getProjectInfoOrDockPurchaseOrder',
        { orderId },
      )
      const order = this.ordersFixed.find((i) => i.businessId === orderId)
      this.$set(order, 'costSubjectName', data[0].costSubjectName)
      // this.$set(order, 'moneyUsd', data[0].toUsd)
      // this.$set(order, 'money', data[0].totalPrice)
      this.dockRepairAccountPreview()
    },
    async loadRepairProjects(orderId) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/businessCostController/getOrderSubjectOfDockRepair2',
        { orderId },
      )
      const repair = this.repairs.find((i) => i.businessId === orderId)
      this.$set(repair, 'costSubjectName', data[0].subjectName)
      // this.$set(repair, 'moneyUsd', data[0].usdMoney)
      // this.$set(repair, 'money', data[0].money)
      this.dockRepairAccountPreview()
    },
    async dockRepairAccountPreview() {
      const detailList = this.items.map((i) => ({
        // orderNo: i.orderNo || i.businessId,
        orderNo: i.businessId,
        operationType: i.operationType || 2,
        orderCate: i.orderCate || i.orderType === '坞修单' ? 1 : 0,
        shipCode: this.detail.shipCode,
        addFlag: i.addFlag,
        accountDiffExplain: i.accountDiffExplain,
        curBudget: i.money,
      }))
      const detailList1 = this.itemsFixed.map((i) => ({
        // orderNo: i.orderNo || i.businessId,
        orderNo: i.businessId,
        operationType: i.operationType || 2,
        orderCate: i.orderCate || i.orderType === '坞修单' ? 1 : 0,
        shipCode: this.detail.shipCode,
        addFlag: i.addFlag,
        accountDiffExplain: i.accountDiffExplain,
        curBudget: i.money,
      }))
      detailList.push(...detailList1)
      const { errorRaw, data } = await this.postAsync(
        '/dockRepairApply/dockRepairAccountPreview',
        {
          ...this.detail,
          detailList,
          dockRepairEvaluationModifyDTO: {
            ...this.detail.dockRepairEvaluationOutputDTO,
            operationType: this.isEdit ? 2 : 1,
          },
        },
      )
      if (errorRaw) {
        this.canSave = false
        return false
      }
      if (data) {
        this.detail.accountDetailList = data
      }
    },
    // async checkDockRepairAccountOverBudget() {
    //   const detailList = this.items.map((i) => ({
    //     orderNo: i.orderNo || i.id,
    //     operationType: i.operationType || 2,
    //     orderCate: i.orderCate || i.orderType === '坞修单' ? 1 : 0,
    //     shipCode: this.detail.shipCode,
    //     addFlag: i.addFlag,
    //     accountDiffExplain: i.accountDiffExplain,
    //   }))
    //   const { errorRaw, data } = await this.postAsync(
    //     '/dockRepairApply/checkDockRepairAccountOverBudget',
    //     {
    //       ...this.detail,
    //       detailList,
    //       dockRepairEvaluationModifyDTO: {
    //         ...this.detail.dockRepairEvaluationOutputDTO,
    //         operationType: this.isEdit ? 2 : 1,
    //       },
    //     },
    //   )
    //   if (errorRaw) {
    //     return false
    //   }
    //   if (data) {
    //     this.isOverBudget = data
    //     console.log(data)
    //   }
    // },
    async loadAllOrders(val) {
      const orderPromises = val.map((i) => this.loadOrderProjects(i.id))
      await Promise.all(orderPromises)
    },
    async loadAllOrdersFixed(val) {
      const orderPromises = val.map((i) => this.loadOrderFixedProjects(i.id))
      await Promise.all(orderPromises)
    },
    async loadAllRepairs(val) {
      // const repairPromises = val.map((i) => this.loadRepairProjects(i.id))
      const repairPromises = val.map((i) =>
        this.loadRepairProjects(i.businessId),
      )
      await Promise.all(repairPromises)
    },

    async deleteOrder() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (this.selected.id) {
        this.delList.push({
          id: this.selected.id,
          operationType: 3,
        })
        this.detail.detailList = this.detail.detailList.filter(
          (i) => i.id !== this.selected.id,
        )
      } else if (
        this.selected.orderType === '坞修单' ||
        this.selected.orderCate == 1
      ) {
        this.repairs = this.repairs.filter(
          (i) => i.orderCode !== this.selected.orderCode,
        )
      } else {
        this.orders = this.orders.filter(
          (i) => i.orderCode !== this.selected.orderCode,
        )
      }
      this.selected = false
    },

    // 追加预算时使用
    async loadPreview(val) {
      val = val.filter((i) => i.operationType == 1)
      if (val.length === 0) return
      const detailList = val.map((i) => ({
        orderNo: i.orderNo || i.id,
        operationType: i.operationType || 2,
        orderCate: i.orderCate || i.orderType === '坞修单' ? 1 : 0,
        shipCode: this.detail.shipCode,
        addFlag: i.addFlag,
        accountDiffExplain: i.accountDiffExplain,
      }))
      const { errorRaw, data } = await this.postAsync(
        '/dockRepairApply/availAdditionOfDetailForAccounts',
        {
          detailList,
          shipCode: this.detail.shipCode,
        },
      )
      if (errorRaw) return false
      for (const item in data.detailList) {
        const i = data.detailList[item]
        const order = val.find((j) => j.id === i.orderNo)
        Object.assign(order, i)
      }
      // 修改费用科目明细
      // for (const item in data.accountDetailList) {
      //   const i = data.accountDetailList[item]
      //   const order = this.detail.accountDetailList.find(
      //     (j) => j.costSubjectName === i.costSubjectName,
      //   )
      //   if (!order) {
      //     this.detail.accountDetailList.push(i)
      //     continue
      //   }
      //   order.curBudget += i.curBudget
      //   order.curSettlementAmount += i.curSettlementAmount
      //   order.accountDiff += i.accountDiff
      // }
    },

    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfAccountById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.initUser = { id: data.handler, nickName: data.handlerName }
      this.detail.dockRepairEvaluationOutputDTO =
        data.dockRepairEvaluationOutputDTO || {}
      this.detail.shipCode = data.shipInfo.shipCode
    },
    goBackExe() {
      this.closeAndTo(this.backRouteName)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
