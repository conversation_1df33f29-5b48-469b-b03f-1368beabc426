<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        订单选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="orderHeaders"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          outlined
        >
          <template #searchflieds></template>
          <template #btns></template>
          <template v-slot:[`item.orderType`]="{ item }">
            {{ stocksType[item.orderType] }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'order-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/shipAffairs/purchaseManage/purchaseOrderPage'
    this.orderHeaders = [
      { text: '订单号', value: 'orderNo' },
      { text: '申请单号', value: 'applyNo' },
      { text: '订单类型', value: 'orderType' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '交货港口', value: 'orderPortName' },
      { text: '入库完成日期', value: 'completeTime' },
    ]
    this.stocksType = {
      '01': '备件订单',
      '02': '物料订单',
      '03': '滑油订单',
    }
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: {
      type: String,
      default: '',
    },
    orders: Array,
    isAppend: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchObj: {
        shipCode: '',
        isDockRepair: true,
        isAccounted: false,
        businessStatus: '',
        isFixed: false,
      },
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
    isAppend(val) {
      if (val) this.searchObj.businessStatus = '已付款'
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const orders = this.selected.map((i) => ({
        ...i,
        orderType: this.stocksType[i.orderType],
        orderCode: i.orderNo,
        addFlag: this.isAppend,
        operationType: 1,
        orderNo: '',
        type: 0,
      }))
      this.$emit('update:orders', orders)
      this.$emit('change', false)
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
