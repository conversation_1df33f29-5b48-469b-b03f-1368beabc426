<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        修理单选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="orderHeaders"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          outlined
        >
          <template #searchflieds></template>
          <template #btns></template>
          <template v-slot:[`item.orderType`]="{ item }">
            {{ stocksType[item.orderType] }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'repair-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/dockRepairApply/getPageOfOrder'
    this.orderHeaders = [
      { text: '修理单号', value: 'orderNo' },
      { text: '预估金额', value: 'estimatedExpense' },
      { text: '修理总价', value: 'totalPrice' },
      { text: '修理总价折算美元', value: 'toUsd' },
      { text: '币种', value: 'ccyCode' },
      { text: '供应商', value: 'supplierName' },
      { text: '坞修类型', value: 'dockRepairType' },
    ]
    this.stocksType = {
      '01': '备件',
      '02': '物料',
      '03': '滑油',
    }
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: {
      type: String,
      default: '',
    },
    repairs: Array,
    isAppend: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchObj: {
        shipCode: '',
        orderType: 0,
        isAccounted: false,
        status: 3,
        businessStatus: '',
      },
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
    isAppend(val) {
      if (val) this.searchObj.businessStatus = '已付款'
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const repairs = this.selected.map((i) => ({
        ...i,
        orderType: '坞修单',
        orderCode: i.orderNo,
        addFlag: this.isAppend,
        operationType: 1,
        orderNo: '',
      }))
      this.$emit('update:repairs', repairs)
      this.$emit('change', false)
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
