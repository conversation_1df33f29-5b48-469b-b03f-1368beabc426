<template>
  <v-dialog
    attach="#mask"
    @input="(val) => $store.commit('setMaskLayer', val)"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        超年初预算发送OA
        <v-spacer></v-spacer>
        <v-btn small outlined tile color="success" class="mx-1" @click="save">
          <v-icon left>mdi-send</v-icon>
          发送OA
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  label="项目名称"
                  v-model="formData.itemName"
                  outlined
                  dense
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  label="折算美金合计"
                  v-model="formData.all"
                  outlined
                  dense
                  disabled
                  readonly
                ></v-text-field>
              </v-col> -->
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="备注"
                  v-model="formData.remark"
                  dense
                  :rules="[rules.required]"
                  required
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
          <v-attach-list
            :attachments="formData.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'budget-over-sendoa-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    accountId: String,
  },
  created() {
    this.付款公司选项 = ['3000', '3800', '8903', '3402']
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
  },
  data() {
    return {
      dialog: false,
      formData: { attachmentIds: [] },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      draw: {},
      searchObj: { efFlag: 1, nickName: '', deptId: '1', paymentCompany: '' },
      file: null,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
    },
    accountId(val) {
      if (val) {
        this.formData.accountId = this.accountId
      }
    },
    // file(val) {
    //   if (val) {
    //     this.importExcel()
    //   }
    // },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      if (!this.formData.attachmentIds.length) {
        this.$dialog.message.warning('请上传附件！')
        return
      }
      console.log(this.formData)
      // 发送OA代码
      const reqUrls = '/dockRepairApply/overSendOA'
      const { errorRaw } = this.postAsync(reqUrls, {
        ...this.formData,
      })
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('发送OA成功！')
      this.$emit('change', false)
    },
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    // changeAttachment2(attachmentUrls) {
    //   // console.log(attachmentUrls)
    //   // this.formData.attachmentUrls = [...this.arr1, ...attachmentUrls]
    //   this.formData.attachmentUrls = attachmentUrls
    //   console.log(this.formData.attachmentUrls)
    // },
  },

  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
