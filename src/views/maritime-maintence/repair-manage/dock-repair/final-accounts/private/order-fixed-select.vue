<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        预算单选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="orderHeaders"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          outlined
        >
          <template #searchflieds></template>
          <template #btns></template>
          <!-- <template v-slot:[`item.orderType`]="{ item }">
            {{ stocksType[item.orderType] }}
          </template> -->
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'order-fixed-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/shipAffairs/supplyCommon/page3'
    this.orderHeaders = [
      { text: '数据来源', value: 'dataSource' },
      { text: '供应商', value: 'supplierName' },
      { text: '供应项目', value: 'supplyItem' },
      { text: '订单号', value: 'orderNo' },
      { text: '科目', value: 'costSubjectName' },
      { text: '预算金额', value: 'money' },
      { text: '折算美金', value: 'moneyUsd' },
      { text: '实际付款金额', value: 'finalMoney' },
      { text: '币种', value: 'currencyName' },
      { text: '发生日期', value: 'proposedDate' },
      { text: '实际申请人', value: 'applicantName' },
      { text: '录单人', value: 'applyUserName' },
      { text: '预算说明', value: 'applyRemark' },
      { text: '审批状态', value: 'status' },
    ]
    this.stocksType = {
      '01': '备件',
      '02': '物料',
      '03': '滑油',
    }
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: {
      type: String,
      default: '',
    },
    ordersFixed: Array,
    isAppend: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      searchObj: {
        shipCode: '',
        orderType: 0,
        isFixed: true,
        isAccounted: false,
        status: 3,
        businessStatus: '',
      },
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
    isAppend(val) {
      if (val) this.searchObj.businessStatus = '已付款'
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const ordersFixed = this.selected.map((i) => ({
        ...i,
        orderType: i.dataSource == '坞修修理单' ? '坞修单' : i.dataSource,
        orderCode: i.orderNo,
        addFlag: this.isAppend,
        operationType: 1,
        // orderNo: '',
        curBudget: i.money,
        toUsd: i.moneyUsd,
        ccyCode: i.currencyName,
        dataSource: i.dataSource,
        budgetId: i.id,
        id: i.businessId,
        costSubjectId: i.costSubjectId,
        currencyId: i.currencyId,
        type: 1,
      }))
      this.$emit('update:ordersFixed', ordersFixed)
      this.$emit('change', false)
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
