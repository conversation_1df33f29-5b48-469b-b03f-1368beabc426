<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-dicts="searchDicts"
      :search-remain="searchObj"
      use-ship
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="预决算单号"
            outlined
            dense
            clearable
            v-model="searchObj.accountNo"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            outlined
            label="状态"
            dense
            :items="buses"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'dock-final-accounts-detail', params: { id: 'new' } }"
          v-permission="['坞修预决算:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.businessStatus != '预算未提交'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['坞修预决算:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="
            selected.businessStatus != '预算超额,待发送OA立项' &&
            selected.businessStatus != '预算超额OA审批失败'
          "
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="overSendOA"
          v-permission="['坞修预决算:预算超额发OA']"
        >
          <v-icon left>mdi-send</v-icon>
          预算超额发OA
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="
            selected.businessStatus !== '预算未提交' &&
            selected.businessStatus !== '预算不通过' &&
            selected.businessStatus !== '预算超额OA审批通过'
          "
          @click="budgetSend"
          v-permission="['坞修预决算:预算发OA']"
        >
          <v-icon left>mdi-send</v-icon>
          预算发OA
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="selected.businessStatus !== '预算通过'"
          @click="checkStatus"
          v-permission="['坞修预决算:检查费用']"
        >
          <v-icon left>mdi-progress-question</v-icon>
          检查费用
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="
            selected.businessStatus !== '预算通过' &&
            selected.businessStatus !== '决算未提交' &&
            selected.businessStatus !== '决算不通过'
          "
          @click="settlementSend"
          v-permission="['坞修预决算:决算发OA']"
        >
          <v-icon left>mdi-send</v-icon>
          决算发OA
        </v-btn>
      </template>
      <template v-slot:[`item.isClaimDemage`]="{ item }">
        {{ item.isClaimDemage ? '是' : '否' }}
      </template>
      <template v-slot:[`item.isSendOa`]="{ item }">
        {{ item.isSendOa ? '是' : '否' }}
      </template>
    </v-table-searchable>
    <budgetOverSendoaDialog
      v-model="dialog2"
      :accountId="accountId"
      @success="success"
    ></budgetOverSendoaDialog>
  </v-container>
</template>
<script>
// accountNo	预决算单号	string
// applyId	坞修申请单id	string
// businessStatus	业务状态	string
// ceaseNavigationDate	预计停航时间	number
// claimDemage	索赔（预计索赔金额）	string
// detailList	明细列表	array	DockRepairAccountsDetailOutputDTO
// dockRepairEvaluationOutputDTO	评价表	DockRepairEvaluationOutputDTO	DockRepairEvaluationOutputDTO
// dockRepairLastDate	本类修理上次日期	string(date-time)
// dockRepairType	修理类型	string
// dockTime	坞期（天数）	integer(int32)
// handler	申请人	string
// handlerName	申请人姓名	string
// id	物理主键	string
// isClaimDemage	是否向保险公司索赔	boolean
// isSendOa	是否发oa	boolean
// oaAccount	申请人oa账户	string
// preRepairDate	预计停航（天数	integer(int32)
// preRepairMoney	预计维修费用	number
// remark	备注	string
// repairReason	修理原因	string
// repairTime	修理日期	string(date-time)
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// supplierIds	供应商（手选）修理厂商	string
// supplierName	供应商名称	string
import budgetOverSendoaDialog from './private/budget-over-sendoa-dialog.vue'

export default {
  components: { budgetOverSendoaDialog },
  name: 'dock-final-accounts-list',
  created() {
    this.tableName = '坞修预决算'
    this.reqUrl = '/dockRepairApply/getPageOfAccounts'
    this.searchDicts = [
      {
        dicType: 'dock_type',
        label: '修理类型',
        key: 'dockRepairType',
      },
    ]
    this.headers = [
      { text: '船舶信息', value: 'shipInfo' },
      { text: '预决算单号', value: 'accountNo' },
      { text: '预计索赔金额', value: 'claimDemage' },
      { text: '修理类型', value: 'dockRepairType' },
      { text: '上次日期', value: 'dockRepairLastDate' },
      { text: '坞期（天数）', value: 'dockTime' },
      { text: '预计停航(天数)', value: 'ceaseNavigationDate' },
      { text: '申请人', value: 'handlerName' },
      { text: '是否索赔', value: 'isClaimDemage' },
      { text: '是否已发oa', value: 'isSendOa' },
      { text: '申请人oa账户', value: 'oaAccount' },
      { text: '预计坞修(天数)', value: 'preRepairDate' },
      { text: '预计维修费用', value: 'preRepairMoney' },
      { text: '修理原因', value: 'repairReason' },
      { text: '修理日期', value: 'repairTime' },
      // { text: '供应商名称', value: 'supplierName' },
      { text: '业务状态', value: 'businessStatus' },
      // { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'dock-final-accounts-detail' }
    this.buses = [
      '预算未提交',
      '预算审批中',
      '预算通过',
      '预算不通过',
      '决算未提交',
      '决算审批中',
      '决算通过',
      '决算不通过',
    ]
  },

  data() {
    return {
      selected: false,
      searchObj: {},
      dialog2: false,
      accountId: '',
    }
  },
  watch: {
    dialog2(val) {
      if (!val) {
        this.$refs.table.loadTableData()
      } else {
        this.accountId = this.selected.id
      }
    },
  },

  methods: {
    // async budgetSend() {
    //   const { errorRaw, data } = await this.getAsync(
    //     '/dockRepairApply/sendBudgetsToOAById',
    //     { id: this.selected.id },
    //   )
    //   if (errorRaw) return
    //   if (data) {
    //     //未超年度预算
    //     this.$dialog.message.success('发送成功')
    //     this.selected = false
    //     this.$refs.table.loadTableData()
    //   } else {
    //     // 超年度预算 弹框发送OA立项
    //     this.accountId = this.selected.id
    //     this.dialog2 = true
    //   }
    // },
    overSendOA() {
      this.dialog2 = true
    },
    async budgetSend() {
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/sendBudgetsToOAById',
        { id: this.selected.id },
      )
      if (errorRaw) return
      this.$dialog.message.success('发送成功')
      this.selected = false
      this.$refs.table.loadTableData()
    },

    async settlementSend() {
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/sendFinalToOAById',
        { id: this.selected.id },
      )
      if (errorRaw) return
      this.$dialog.message.success('发送成功')
      this.selected = false
      this.$refs.table.loadTableData()
    },

    async delItem() {
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/deleteAccounts',
        { id: this.selected.id },
      )
      if (errorRaw) return
      this.$dialog.message.success('删除成功')
      this.selected = false
      this.$refs.table.loadTableData()
    },

    async checkStatus() {
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/checkBudgetStatus',
        { id: this.selected.id },
      )
      if (errorRaw) return
      this.$dialog.message.success('校验成功')
      this.selected = false
      this.$refs.table.loadTableData()
    },
    async success() {
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
