<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="
            selected.businessStatus != '审批通过,发送OA失败' &&
            selected.businessStatus != '审批通过,待发送OA立项'
          "
          outlined
          tile
          color="primary"
          class="mx-1"
          @click.stop="sendOA"
          v-permission="['备件询价:发送OA']"
        >
          <v-icon left>mdi-send</v-icon>
          发送OA
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'dock-plan-detail', params: { id: 'new' } }"
          v-permission="['年度坞修计划:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['年度坞修计划:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// businessStatus	业务状态	string
// dockPlanNo	单号	string
// id	物理主键	string
// manageCompany	管理公司	string
// processInstanceId	流程实例id	string
// remark	备注	string
// status	状态	string
// year	年度	integer
export default {
  name: 'dock-plan-list',
  created() {
    this.tableName = '年度坞修计划'
    this.reqUrl = '/dockRepairApply/getPageOfDockPlan'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '单号', value: 'dockPlanNo' },
      { text: '年度', value: 'year' },
      // { text: '管理公司', value: 'manageCompanyName' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'dock-plan-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: true,
      },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/deleteDockPlan',
        {
          id: this.selected.id,
        },
      )
      if (errorRaw) return
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    async sendOA() {
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/dockYearPlanUpdateByOA',
        {
          id: this.selected.id,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('发送成功')
        this.selected = false
        await this.$refs.table.loadTableData()
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
