<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        船舶详情
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '保存' : '创建' }}
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col
                class="pt-0"
                v-for="h in headers"
                :key="h.value"
                cols="12"
                md="3"
              >
                <v-ship-select
                  v-if="h.value === 'shipCode'"
                  v-model="formData.shipCode"
                ></v-ship-select>
                <vs-date-picker
                  v-else-if="h.type == 'date'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  outlined
                  dense
                ></vs-date-picker>
                <v-select
                  v-else-if="h.type == 'yn'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  outlined
                  dense
                  :items="yn"
                ></v-select>
                <v-dict-select
                  v-else-if="h.type == 'dict'"
                  dict-type="dock_type"
                  v-model="formData[h.value]"
                  :label="h.text"
                ></v-dict-select>
                <v-text-field
                  v-else-if="h.type == 'readonly'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                  disabled
                ></v-text-field>
                <v-text-field
                  v-else-if="h.type == 'company'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                  disabled
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="formData.remark"
                  label="备注"
                  outlined
                  dense
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'ship-dock-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    //     breadth	总宽(船舶信息冗余)	string
    // cctv	cctv	boolean
    // fishingNetCollector	渔网收集器	boolean
    // flagState	船旗(船舶信息冗余)	string
    // id	物理主键	string
    // ioppOverdueTime	IOPP证书到期时间	string
    // lastDockTime	上次坞修时间	string(date-time)
    // lastTailShaftTime	上次尾轴抽检时间	string(date-time)
    // launchedDate	下水时间(船舶信息冗余)	string
    // loa	总长(船舶信息冗余)	string
    // nextRepairTime	预计修理时间	string(date-time)
    // nextRepairType	下次修理类别	string
    // remark	备注	string
    // shipClassification	船级社(船舶信息冗余)	string
    // shipCode	船舶编码	string
    // shipName	船名(船舶信息冗余)	string
    // shipType	船型(船舶信息冗余)	string
    // waterTreatmentTime	压载水处理装最迟安装时间	string(date-time)
    this.headers = [
      { text: '船舶', value: 'shipCode' },
      { text: '上次坞修时间', value: 'lastDockTime', type: 'date' },
      { text: '上次尾轴抽检时间', value: 'lastTailShaftTime', type: 'date' },
      { text: '预计修理时间', value: 'nextRepairTime', type: 'date' },
      { text: '预计修理类别', value: 'nextRepairType', type: 'dict' },
      { text: 'IOPP证书到期时间', value: 'ioppOverdueTime', type: 'date' },
      {
        text: '压载水处理装最迟安装时间',
        value: 'waterTreatmentTime',
        type: 'date',
      },
      { text: 'cctv', value: 'cctv', type: 'yn' },
      { text: '渔网收集器', value: 'fishingNetCollector', type: 'yn' },
      { text: '管理公司', value: 'company', type: 'company' },
      // { text: '备注', value: 'remark' },
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
    'formData.shipCode'(val) {
      if (!this.isEdit && val) this.loadDockInfo(val)
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      // const url = '/dockRepairApply/saveOrUpdateDockPlanDetail'
      // const { errorRaw } = await this.postAsync(url, {
      //   ...this.formData,
      // })
      // if (!errorRaw) {
      //   this.$emit('change', false)
      //   this.$emit('success')
      // }
      this.$emit('update:addData', this.formData)
      this.$emit('change', false)
      this.$emit('success')
    },

    async loadDockInfo(shipCode) {
      const url = '/dockRepairApply/getEvaluationDetailForPlanByShipCode'
      const { data } = await this.getAsync(url, {
        shipCode,
      })
      Object.assign(this.formData, data)
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
