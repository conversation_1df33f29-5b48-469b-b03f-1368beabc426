<template>
  <v-container fluid>
    <v-detail-view
      :title="`年度坞修计划-${isEdit ? detail.dockPlanNo : '新增'}`"
      :tooltip="isEdit ? detail.dockPlanNo : '新增'"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      v-permission="['年度坞修计划:编辑']"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template v-slot:custombtns>
        <v-btn
          v-if="detail.pdfAttachment"
          :href="`/api/system/file/download?fileName=${encodeURIComponent(
            detail.pdfAttachment.name,
          )}&filePath=${detail.pdfAttachment.filePath}`"
          width="90"
          tile
          color="success"
          small
          class="mx-1"
        >
          <v-icon>mdi-download</v-icon>
          PDF
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <!-- <v-text-field
                  label="年度"
                  dense
                  outlined
                  v-model="detail.year"
                  :rules="[rules.required, rules.yyyy]"
                  :readonly="isEdit"
                ></v-text-field> -->
                <v-autocomplete
                  label="年份"
                  outlined
                  dense
                  v-model="detail.year"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                  :items="yearList"
                  clearable
                ></v-autocomplete>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-select
                  :items="船管公司"
                  label="管理公司"
                  v-model="detail.manageCompany"
                  :rules="[rules.required]"
                  dense
                  outlined
                  :readonly="isEdit"
                ></v-select>
              </v-col> -->
              <v-col cols="12">
                <v-textarea
                  label="备注"
                  dense
                  outlined
                  v-model="detail.remark"
                  :readonly="!canEdit"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <!-- <template v-if="!isEdit" #维护船舶按钮> -->
      <!-- 保存并查看 -->
      <!-- <v-btn
          v-permission="['年度坞修计划:编辑']"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="saveAndCheck"
        >
          <v-icon left>mdi-eye</v-icon>
          保存并查看
        </v-btn> -->
      <!-- </template> -->
      <!-- <template v-else-if="canSubmit" #维护船舶按钮> -->
      <template
        v-if="detail.status == 1 || detail.status == 4 || !isEdit"
        #维护船舶按钮
      >
        <!-- 保存并查看 -->
        <v-btn
          v-permission="['年度坞修计划:编辑']"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="addItem"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          v-permission="['年度坞修计划:编辑']"
          outlined
          tile
          color="warning"
          class="mx-1"
          :disabled="!selected"
          @click="editItem"
        >
          <v-icon left>mdi-pen</v-icon>
          编辑
        </v-btn>
        <v-btn
          outlined
          :loading="loading"
          color="error"
          class="mx-1"
          :disabled="!selected"
          @click="delInfo"
          v-permission="['年度坞修计划:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #维护船舶>
        <v-table-list
          v-model="selected"
          :headers="headers"
          item-key="shipName"
          :items="detail.detailList"
        >
          <template v-slot:[`item.cctv`]="{ item }">
            {{ item.cctv ? '是' : '否' }}
          </template>
          <template v-slot:[`item.fishingNetCollector`]="{ item }">
            {{ item.fishingNetCollector ? '是' : '否' }}
          </template>
        </v-table-list>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="(ids) => (detail.attachmentIds = ids)"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <ship-dock-dialog
      :initialData="initData"
      v-model="dialog"
      :addData.sync="addData"
      @success="success"
    ></ship-dock-dialog>
  </v-container>
</template>
<script>
// breadth	总宽(船舶信息冗余)	string
// cctv	cctv	boolean
// fishingNetCollector	渔网收集器	boolean
// flagState	船旗(船舶信息冗余)	string
// id	物理主键	string
// ioppOverdueTime	IOPP证书到期时间	string
// lastDockTime	上次坞修时间	string
// lastTailShaftTime	上次尾轴抽检时间	string
// launchedDate	下水时间(船舶信息冗余)	string
// loa	总长(船舶信息冗余)	string
// nextRepairTime	预计修理时间	string
// nextRepairType	下次修理类别	string
// remark	备注	string
// shipClassification	船级社(船舶信息冗余)	string
// shipCode	船舶编码	string
// shipName	船名(船舶信息冗余)	string
// shipType	船型(船舶信息冗余)	string
// waterTreatmentTime	压载水处理装最迟安装时间	string
import routerControl from '@/mixin/routerControl'
import shipDockDialog from './private/ship-dock-dialog.vue'
export default {
  components: { shipDockDialog },
  name: 'dock-plan-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'dock-plan-list'
    this.subtitles = ['基本信息', '维护船舶']
    this.headers = [
      { text: '船名', value: 'shipName' },
      { text: '船型', value: 'shipType' },
      { text: '船级社', value: 'shipClassification' },
      { text: '船旗', value: 'flagState' },
      { text: '总长', value: 'loa' },
      { text: '总宽', value: 'breadth' },
      { text: '下水时间', value: 'launchedDate' },
      { text: '上次坞修时间', value: 'lastDockTime' },
      { text: '上次尾轴抽检时间', value: 'lastTailShaftTime' },
      { text: '预计修理时间', value: 'nextRepairTime' },
      { text: '预计修理类别', value: 'nextRepairType' },
      { text: 'IOPP证书到期时间', value: 'ioppOverdueTime' },
      { text: '压载水处理装最迟安装时间', value: 'waterTreatmentTime' },
      { text: '管理公司', value: 'company' },
      { text: 'cctv', value: 'cctv' },
      { text: '渔网收集器', value: 'fishingNetCollector' },
      { text: '备注', value: 'remark' },
    ]
    this.rangeArray(2020, new Date().getFullYear())
  },
  data() {
    return {
      detail: {
        year: '',
        manageCompany: '',
        remark: '',
        attachmentRecords: [],
        attachmentIds: [],
        detailList: [],
      },
      deptInfo: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        yyyy: (v) => /^\d{4}$/.test(v) || '请输入4位数字',
      },
      dialog: false,
      initData: {},
      selected: false,
      addData: {},
      yearList: Array.from({ length: 50 }, (_, i) => i + 1),
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canSubmit() {
      return this.detail.status == '1'
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
    船管公司() {
      return this.deptInfo.filter((i) => i.deptType === '0')
    },
  },

  watch: {
    // detail: {
    //   handler(val) {
    //     if (val.manageCompany && val.year.length == 4 && !this.isEdit) {
    //       this.saveAndCheck()
    //     }
    //   },
    //   deep: true,
    // },
    addData: {
      handler(val) {
        let flag = true
        this.detail.detailList.forEach((element) => {
          if (element.shipCode == val.shipCode) {
            this.$dialog.message.error('请勿重复添加！')
            flag = false
            return
          }
        })
        if (flag) this.detail.detailList.push(val)
        console.log(this.detail.detailList)
      },
      deep: true,
    },
  },

  methods: {
    rangeArray(start, end) {
      let length = end - start + 1
      let step = start - 1
      this.yearList = Array.from({ length: length }, () => {
        step++
        return step
      })
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { data } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateDockPlan',
        this.detail,
      )
      if (!data) return
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (this.detail.detailList.length === 0) {
        this.$dialog.message.error('请添加维护船舶')
        return
      }
      const data = await this.save(goBack, true)
      if (!data) return
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/dockRepairApply/submitDockPlanById',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async saveAndCheck() {
      if (!this.$refs.form.validate()) {
        return
      }
      const { data } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateDockPlan',
        this.detail,
      )
      if (!data) return
      this.closeAndTo('dock-plan-detail', { id: data })
    },
    // async loadDetail(onlyList = false) {
    //   if (!this.isEdit) return
    //   const { data } = await this.getAsync(
    //     '/dockRepairApply/getDetailOfDockPlanById',
    //     {
    //       id: this.$route.params.id,
    //     },
    //   )
    //   if (!data) return
    //   if (onlyList) {
    //     this.detail.detailList = data.detailList
    //     return
    //   }
    //   this.detail = data
    // },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfDockPlanById',
        {
          id: this.$route.params.id,
        },
      )
      if (!data) return
      this.detail.detailList = data.detailList
      this.detail = data
    },
    success() {
      // this.loadDetail(true)
    },
    editItem() {
      this.initData = { ...this.selected }
      this.dialog = true
    },
    addItem() {
      // this.initData = { mainId: this.detail.id }
      this.initData = {}
      this.dialog = true
    },

    async loadDeptInfo() {
      const { data } = await this.getAsync('/system/dept/getDeptTreeList')
      // 遍历部门树结构，找到deptType为0的部门
      const deptInfo = []
      const findDept = (data) => {
        data.forEach((item) => {
          if (item.deptType === '0') {
            deptInfo.push({
              value: item.id,
              text: item.name,
              deptType: item.deptType,
            })
          }
          if (item.children) {
            findDept(item.children)
          }
        })
      }
      findDept(data)
      this.deptInfo = deptInfo
    },
    async delInfo() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.loading = true
      if (this.selected.id) {
        const { errorRaw } = await this.getAsync(
          `/dockRepairApply/deleteDockPlanDetail`,
          {
            id: this.selected.id,
          },
        )
        if (errorRaw) {
          this.loading = false
          return
        }
        this.$dialog.message.success('删除成功')
        this.loading = false
        await this.loadDetail()
      } else {
        this.detail.detailList = this.detail.detailList.filter(
          (ele) => ele !== this.selected,
        )
      }
      this.loading = false
    },
  },

  mounted() {
    this.loadDeptInfo()
    this.loadDetail()
  },
}
</script>

<style></style>
