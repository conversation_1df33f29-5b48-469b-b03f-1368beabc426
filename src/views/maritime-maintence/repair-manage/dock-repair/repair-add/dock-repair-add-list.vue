<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="3">
                <port-select-dialog
                  v-model="detail.portId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  use-today
                  v-model="detail.repairDate"
                  dense
                  outlined
                  label="安排日期"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  type="number"
                  v-model="detail.estimatedExpense"
                  dense
                  outlined
                  label="预估金额"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="detail.costSubjectId"
                  :rules="[rules.required]"
                  :search-dicts="searchDicts"
                  :search-remain="{ repairFlag: 1 }"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  required
                  dense
                ></v-dialog-select>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="changeStatus('已安排')"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  确认
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      use-status
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'dock-repair-add-detail', params: { id: 'new' } }"
          v-permission="['坞修增量工程:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['坞修增量工程:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import portSelectDialog from '@/views/maritime-maintence/components/port-select-dialog.vue'
// applicantId	申请人	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// businessStatus	业务状态	string
// enquiryId	询价单id	string
// id	物理主键	string
// orderNo		string
// otherExpense	其他费用	number
// post	申请部门	string
// quoteId	报价单id	string
// remark	备注	string
// repairExpense	修理费用	number
// shipInfo	船舶编码	ShipInfoDO
export default {
  components: { portSelectDialog },
  name: 'dock-repair-add-list',
  created() {
    this.tableName = '坞修增量工程'
    this.reqUrl = '/dockRepairApply/getPageOfOrder'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '增量单号', value: 'orderNo' },
      { text: '原修理单号', value: 'parentOrderNo' },
      { text: '修理费用', value: 'totalPrice' },
      { text: '币种', value: 'ccyCode' },
      { text: '折算美元', value: 'toUsd' },
      { text: '供应商', value: 'supplierName' },
      { text: '坞修类型', value: 'dockRepairType' },
      { text: '决算金额', value: 'accountMoney', hideDefault: true },
      // { text: '申请部门', value: 'dept' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '流程状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'dock-repair-add-detail' }
  },

  data() {
    return {
      selected: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      portId: '',
      initPort: {},
      formShow: false,
      repairDate: '',
      estimatedExpense: 0,
      detail: {},
      searchObj: { orderType: 1 },
    }
  },

  methods: {
    async editItem() {
      this.portId = this.selected.enquiryPortId
      this.initPort = {
        portCn: this.selected.enquiryPortName,
        id: this.selected.enquiryPortId,
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },

    closeForm() {
      this.$refs.form.reset()
      this.initPort = {}
      this.formShow = false
      this.$refs.table.disabled = false
    },

    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/dockRepairApply/deleteOrder',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },

    async changeStatus(businessStatus) {
      if (businessStatus == '已安排') {
        if (!this.$refs.form.validate()) return
      }
      const detail = businessStatus == '已安排' ? { ...this.detail } : {}
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/dockRepair/saveOrUpdateOrder',
        {
          id: this.selected.id,
          businessStatus,
          ...detail,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('状态变更成功')
        this.selected = false
        this.closeForm()
        await this.$refs.table.loadTableData()
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
