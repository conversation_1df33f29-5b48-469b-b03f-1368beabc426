<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        坞修项目选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          fuzzy-label="模糊搜索"
        >
          <template #searchflieds></template>
          <template #btns></template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="dialog = false">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'spare-part-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/dockRepairItem/getPageOfDockRepairItem'
    this.headers = [
      { text: '项目编号', value: 'itemNo' },
      { text: '项目名称', value: 'itemName' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    items: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {},
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    searchRemain(val) {
      this.searchObj = val
    },
    items(val) {
      this.selected = val.map((i) => {
        return { ...i, vid: i.id, id: i.itemId }
      })
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const items = this.selected.map((i) => {
        const comp = {
          ...i,
          itemId: i.id,
          id: i.vid,
          repairDiscount: 1,
        }
        return comp
      })
      this.$emit('update:items', items)
      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
