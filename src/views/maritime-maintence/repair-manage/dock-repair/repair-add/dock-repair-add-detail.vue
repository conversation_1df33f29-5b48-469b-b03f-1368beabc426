<template>
  <v-container fluid>
    <v-detail-view
      :title="`坞修增量工程-${isEdit ? detail.orderNo : '新增'}`"
      :tooltip="isEdit ? detail.orderNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['坞修增量工程:编辑']"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="form">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #订单信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  :readonly="isEdit"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <repair-selelct
                  :ship-code="detail.shipCode"
                  v-model="detail.orderId"
                  :readonly="isEdit"
                  :initSelected="initOrder"
                  :ccyCode.sync="detail.ccyCode"
                ></repair-selelct>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.ccyCode"
                  label="币种"
                  disabled
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #修理明细按钮>
        <v-btn
          :disabled="!canEdit"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="dialog = true"
          v-permission="['坞修增量工程:选择项目']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择项目
        </v-btn>
      </template>
      <template #修理明细>
        <v-form ref="form2">
          <v-table-list
            item-key="itemId"
            :headers="headers"
            :items="detail.detailList"
          >
            <template v-if="canEdit" v-slot:[`item.repairPrice`]="{ item }">
              <v-text-field
                v-if="!item.haveMainPrice && isJPY"
                @change="
                  () => (item.repairPrice = Math.round(item.repairPrice))
                "
                v-model="item.repairPrice"
                label="修理费/price"
                type="number"
                single-line
                dense
                :rules="[rules.required]"
              ></v-text-field>
              <v-text-field
                v-else-if="!item.haveMainPrice"
                v-model="item.repairPrice"
                label="修理费/price"
                type="number"
                single-line
                dense
                :rules="[rules.required]"
              ></v-text-field>
              <div v-else>{{ item.repairPrice }}</div>
            </template>
            <template v-slot:[`item.afterRepairPrice`]="{ item }">
              {{ item.repairDiscount * item.repairPrice }}
            </template>
          </v-table-list>
        </v-form>
      </template>
    </v-detail-view>
    <dock-item-select
      v-model="dialog"
      :items.sync="detail.detailList"
    ></dock-item-select>
  </v-container>
</template>
<script>
import repairSelelct from './private/repair-selelct.vue'
// accountMoney	决算金额	number
// attachmentRecords	附件ids	array	CommonAttachment
// auditParams	流程实例id	AuditParams	AuditParams
// businessStatus	业务状态	string
// ccyCode	币种	string
// completeDate	完工日期	string(date-time)
// costSubjectOutputDTO	费用科目信息	CostSubjectOutputDTO_1	CostSubjectOutputDTO_1
// currencyId	币种id	string
// detailList	明细列表	array	DockRepairOrderDetailOutputDTO
// dockRepairType	坞修类型：厂修/外协	string
// estimatedExpense	预估金额	number
// id	物理主键	string
// invoiceType	发票类型	string
// orderEvaluation	订单评价	string
// orderNo	修理单单号	string
// orderPortId	修理单港口id	string
// orderPortName	修理单港口名称	string
// orderType	修理单类型：原始修理单/增量工程	string
// parentId	（增量工程的原修理单id）父级id	string
// priceIncTax	发票是否含税	boolean
// quoteId	报价单id	string
// quoteNo	报价单号	string
// rateToMain	折算汇率	number
// remark	备注	string
// repairDate	修理日期	string(date-time)
// shipInfo	船舶编码	ShipInfoDO	ShipInfoDO
// status	流程状态	string
// supplierId	供应商id	string
// supplierName	供应商名称	string
// supplierScoreId	供应商评分id	string
// tax	税费	number
// taxRate	发票税率	number
// toUsd	折算美元	number

// itemName	项目名称	string
// itemNo	项目编号	string
// orderId	修理单/增量工程id	string
// remark	备注	string
// repairDiscount	修理折扣	number
// repairPrice	修理价格

import DockItemSelect from './private/dock-item-select.vue'
export default {
  components: { repairSelelct, DockItemSelect },
  name: 'dock-repair-add-detail',
  created() {
    this.backRouteName = 'dock-repair-add-list'
    this.subtitles = ['订单信息', '修理明细']
    this.headers = [
      { text: '项目名称', value: 'itemName' },
      { text: '项目编号', value: 'itemNo' },
      { text: '修理折扣', value: 'repairDiscount', width: 150 },
      { text: '修理原价', value: 'repairPrice', width: 150 },
      { text: '折扣后价格', value: 'afterRepairPrice' },
      { text: '备注', value: 'remark' },
    ]

    this.tableFeilds = [
      { text: '船舶', value: 'shipInfoDO' },
      { text: '坞修类型', value: 'dockRepairType' },
      { text: '费用科目', value: 'subjectName' },
      { text: '币种', value: 'ccyCode' },
      { text: '汇率', value: 'rateToMain' },
      { text: '是否含税', value: 'priceIncTax' },
      { text: '发票税率', value: 'taxRate' },
      { text: '税费', value: 'tax' },
      { text: '折算美元', value: 'toUsd' },
      { text: '预估金额', value: 'estimatedExpense' },
      { text: '决算金额', value: 'accountMoney' },
      { text: '修理单港口', value: 'orderPortName' },
      { text: '发票类型', value: 'invoiceType' },
      { text: '备注', value: 'remark' },
      { text: '供应商名称', value: 'supplierName' },
    ]
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
    isJPY() {
      return this.detail.ccyCode === 'JPY'
    },
    // usdMoney() {
    //   return (
    //     this.detail.usdMoney ??
    //     (this.currencyInfo?.find((i) => i.id === this.detail.ccyId)
    //       ?.rateToMain || 0) * (this.detail.money || 0)
    //   )
    // },
  },
  data() {
    return {
      detail: { detailList: [] },
      dialog: false,
      initOrder: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },

  methods: {
    async save(goBack, notMove = false) {
      if (this.detail.detailList.length === 0) {
        this.$dialog.message.error('请添加修理明细')
        return false
      }
      if (!this.$refs.form2.validate()) return false
      const detailList = this.detail.detailList.map((i) => ({
        ...i,
        operationType: this.isEdit ? '2' : '1',
      }))
      const { data, errorRaw } = await this.postAsync(
        '/dockRepairApply/saveOrUpdateAdditionWorkOfOrder',
        { ...this.detail, detailList, orderType: 1 },
      )
      if (errorRaw) {
        return false
      }
      if (notMove) {
        return data
      }
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const res = await this.save(goBack, true)
      if (!res) {
        return false
      }
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/dockRepairApply/submitOrderById',
          { id: res },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/dockRepairApply/getDetailOfOrderById',
        {
          id: this.$route.params.id,
        },
      )
      this.detail = data
      this.detail.shipCode = this.detail.shipInfo.shipCode
      this.initOrder = { id: data.parentId, orderNo: data.parentOrderNo }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
