<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          small
          v-permission="['坞修项目清单:编辑']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '修改' : '新增' }}
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col md="3" sm="6" cols="12">
                <treeselect
                  v-model="formData.itemType"
                  :options="typeTreeList"
                  outlined
                  dense
                  placeholder="项目分类"
                />
              </v-col>

              <v-col md="3" sm="6" cols="12">
                <v-text-field
                  readonly
                  v-model="formData.itemNo"
                  label="项目编号"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col md="3" sm="6" cols="12">
                <v-text-field
                  v-model="formData.itemName"
                  label="项目名称"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>

              <v-col class="pt-0" cols="12">
                <v-textarea
                  v-model="formData.remark"
                  label="备注"
                  outlined
                  dense
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      @dbclick="editItem"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :search-dicts="searchDicts"
      :search-remain="searchObj"
      :fix-header="false"
      fuzzy-label="模糊搜索"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="3">
          <treeselect
            v-model="searchObj.itemType"
            :options="typeTreeList"
            placeholder="请选择坞修分类"
          />
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['坞修项目清单:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['坞修项目清单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// itemName	项目名称	string
// itemNo	项目编号	string
// remark	备注	string
export default {
  components: { Treeselect },
  name: 'dock-repair-item',
  created() {
    this.tableName = '坞修项目清单'
    this.reqUrl = '/dockRepairItem/getPageOfDockRepairItem'
    this.searchDicts = [
      // {
      //   dicType: 'dock_repiar_item_type',
      //   label: '项目分类',
      //   key: 'itemType',
      // },
    ]
    this.headers = [
      { text: '项目编号', value: 'itemNo' },
      { text: '项目名称', value: 'itemName' },
      { text: '项目分类', value: 'itemTypeName' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      typeTreeList: [],
      searchObj: {},
      selected: false,
      formData: { itemNo: '' },
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/dockRepairItem/deleteDockRepairItem',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        itemNo: '',
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/dockRepairItem/saveOrUpdateDockRepairItem'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
        itemNo: '',
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        '/dockRepairItemType/getTypeTreeList',
      )
      this.typeTreeList.push(data)
    },
    async loadItemNo(val) {
      if (val) {
        const { errorRaw, data } = await this.getAsync(
          '/dockRepairItemType/getTypeCodeById',
          {
            id: val,
          },
        )
        if (errorRaw) {
          this.formData.itemNo = ''
          return
        }
        if (data) {
          this.formData.itemNo = data
        }
      }
    },
  },
  watch: {
    'formData.itemType'(val) {
      this.loadItemNo(val)
    },
  },
  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
