<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        坞修项目分类
        <v-spacer></v-spacer>
      </v-card-title>
      <v-card-text>
        <v-treeview
          ref="aaaaa"
          v-if="openPanel"
          :active.sync="active"
          item-key="id"
          return-object
          activatable
          open-all
          :items="typeTreeList"
        >
          <template v-slot:append="{ item }">
            <v-btn
              ref="add"
              color="primary"
              class="ma-2"
              dark
              @click="addFolder(item)"
              v-permission="['坞修项目分类:新增']"
            >
              新增
            </v-btn>
            <v-btn
              v-if="item.id !== '0'"
              color="primary"
              class="ma-2"
              dark
              @click="editFolder(item)"
              v-permission="['坞修项目分类:修改']"
            >
              修改
            </v-btn>
            <v-btn
              v-if="item.id !== '0'"
              color="primary"
              class="ma-2"
              dark
              @click="deleteFolder(item)"
              v-permission="['坞修项目分类:删除']"
            >
              删除
            </v-btn>
          </template>
        </v-treeview>
      </v-card-text>
    </v-card>

    <v-dialog v-model="dialog" max-width="600">
      <template v-slot:default="dialog">
        <v-card style="height: 300px">
          <v-card-title>
            {{ isEdit ? '修改' : '新增' }}坞修项目分类
            <v-spacer></v-spacer>

            <v-btn
              v-if="!isEdit"
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="save"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              新增
            </v-btn>
            <v-btn
              v-if="isEdit"
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="update"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              修改
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form">
              <v-row>
                <v-col cols="12" md="2" class="py-5 my-0 px-0 mx-0">
                  &nbsp;&nbsp;&nbsp;上级菜单：
                </v-col>
                <v-col cols="12" md="9" class="py-4 my-0 px-0 mx-0">
                  <treeselect
                    v-if="openPanel"
                    v-model="itemType.parentId"
                    :options="typeTreeList"
                    placeholder="请选择上级菜单"
                    style="z-index: 100"
                  />
                  <pre class="result">{{ value }}</pre>
                </v-col>

                <v-col cols="12" md="4" class="py-4 my-0">
                  <v-text-field
                    label="项目分类名称"
                    v-model="itemType.typeName"
                    :rules="[rules.required]"
                    required
                    dense
                    outlined
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="4" class="py-4 my-0">
                  <v-text-field
                    label="编码规则"
                    v-model="itemType.code"
                    :rules="[rules.required]"
                    required
                    dense
                    outlined
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="4" class="py-4 my-0">
                  <v-text-field
                    label="排序号（整数）"
                    v-model="itemType.seq"
                    :rules="[rules.required]"
                    required
                    dense
                    outlined
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  components: { Treeselect },
  name: 'dock-repair-itemType',
  created() {
    this.backRouteName = 'report-emit-list'
    this.subtitles = []
  },
  data() {
    return {
      openPanel: true,
      overlay: false,
      zIndex: 10,
      dialog: false,
      typeTreeList: [],
      open: true,
      options: [],
      selected: {
        id: 0,
        items: [],
      },
      itemType: {
        parentId: '',
        name: '',
        code: '',
        seq: '',
      },
      active: [],
      isEdit: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  methods: {
    async save() {
      console.log(this.itemType)
      if (!this.$refs.form.validate()) return
      const { errorRaw, data } = await this.postAsync(
        '/dockRepairItemType/saveOrUpdateDockRepairItemType',
        { ...this.itemType },
      )
      if (errorRaw) {
        return
      }
      this.dialog = false
      if (data) {
        // this.itemType.id = data
        // this.itemType.delFlag = 0
        // this.itemType.label = this.itemType.typeName
        // this.itemType.name = this.itemType.typeName
        // this.itemType.children = []
        // const handleData = (id, datas, obj) => {
        //   datas.forEach((item) => {
        //     if (item.id === id) {
        //       item.children.push(obj)
        //     } else {
        //       if (item.children) {
        //         handleData(id, item.children, obj)
        //       }
        //     }
        //   })
        //   return datas
        // }
        // var additemType = this.itemType
        // handleData(additemType.parentId, this.typeTreeList, additemType)
        // setTimeout(() => {
        //   //查询树的数据
        //   this.loadDetail() //<el-tree>组件使用v-if重新加载
        //   this.openPanel = false
        //   this.$nextTick(() => {
        //     this.openPanel = true
        //   })
        // }, 100)
        // console.log(this.typeTreeList)
        // console.log(this.options)
        // this.typeTreeList = aaa
      }
      this.$dialog.message.success(`保存成功`)
      this.$router.go(0)
      this.$refs.form.reset()
      this.form = {}
    },
    async update() {
      console.log(this.itemType)
      if (!this.$refs.form.validate()) return
      const { errorRaw, data } = await this.postAsync(
        '/dockRepairItemType/saveOrUpdateDockRepairItemType',
        { ...this.itemType },
      )
      if (errorRaw) {
        return
      }
      this.dialog = false
      if (data) {
        // this.itemType.id = data
        // this.itemType.delFlag = 0
        // this.itemType.label = this.itemType.typeName
        // this.itemType.name = this.itemType.typeName
        // this.itemType.children = []
        // const handleData = (id, datas, obj) => {
        //   if (!datas || !datas.length) {
        //     return
        //   }
        //   for (let i = 0; i < datas.length; i++) {
        //     if (datas[i].id == id) {
        //       datas[i] = obj
        //       break
        //     }
        //     handleData(id, datas[i].children, obj)
        //   }
        // }
        // handleData(data, this.typeTreeList, this.itemType)
      }

      this.$dialog.message.success(`修改成功`)
      this.$router.go(0)
      this.$refs.form.reset()
      this.form = {}
    },
    addItem() {
      this.formShow = true
      // this.$refs.table.disabled = false
    },
    addFolder(item) {
      this.dialog = true
      // var idsss = item.id
      this.itemType.parentId = item.id
      // this.itemType.typeName = ''
      // this.itemType.code = ''
      // this.itemType.seq = ''
      this.itemType.id = ''
      // console.log(this.itemType)
      // console.log(this.itemType)
      // console.log(this.$refs)
    },
    editFolder(item) {
      this.isEdit = true
      this.dialog = true
      this.itemType = item
      console.log(this.itemType)
    },
    async deleteFolder(item) {
      // alert(item.id)
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/dockRepairItemType/deleteDockRepairItemType',
        { id: item.id },
      )
      if (!errorRaw) {
        // this.$router.go(0)
        // await this.loadDetail()

        this.active[0].delFlag = '1' //flag as deleted
        this.active.splice(0, 1) //remove from active array
        this.removeDeleted(this, this.typeTreeList)
      }
    },
    removeDeleted(me, currentArray) {
      const delItems = []
      currentArray.forEach((element) => {
        if (element.delFlag == '1') {
          delItems.push(element)
        }
        if (
          element.children != undefined &&
          element.children != null &&
          element.children.length > 0
        ) {
          me.removeDeleted(me, element.children)
        }
      })
      delItems.forEach((item) => {
        currentArray.splice(currentArray.indexOf(item), 1)
      })
    },

    closeForm() {
      this.$refs.form.reset()
      this.form = {}
      this.isEdit = false
    },
    async success() {},
    async loadDetail() {
      const { data } = await this.getAsync(
        '/dockRepairItemType/getTypeTreeList',
      )
      // console.log(data)
      // this.typeTreeList = []
      this.typeTreeList.push(data)
      this.options.push(data)

      // console.log(this.typeTreeList)

      // const info = await this.getAsync('/dockRepairItemType/getTypeTreeList')
      // if (info.data) {
      //   console.log(info)
      //   console.log(info.data)
      // }
    },
  },
  watch: {
    active: {
      deep: true,
      handler() {
        this.selected.id = this.active[0].id
        // alert(this.selected.id)
      },
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
