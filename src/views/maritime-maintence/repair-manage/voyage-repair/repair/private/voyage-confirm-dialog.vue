<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title v-if="this.initialData.voyageRepairOrderStatus == '已确认'">
        修理单确认（主管）
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          确认
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-title v-if="this.initialData.voyageRepairOrderStatus == '已安排'">
        修理单确认（供应商）
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          确认
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-title
        v-if="this.initialData.voyageRepairOrderStatus == '完工待审核'"
      >
        最终工程、价格确认
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          确认
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-title
        v-if="this.initialData.voyageRepairOrderStatus == '完工确认'"
      >
        完工确认
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          确认
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-title
        v-if="this.initialData.voyageRepairOrderStatus == '完工退回'"
      >
        完工退回
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          确认
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <!-- <v-col cols="12" md="3">
                <port-select-dialog
                  v-model="formData.portId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                  label="修理港口"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  use-today
                  v-model="formData.repairDate"
                  dense
                  outlined
                  label="安排日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col> -->
              <v-col cols="12" md="3">
                <vs-date-picker
                  use-today
                  v-model="formData.repairDate"
                  dense
                  outlined
                  label="修理日期"
                  v-if="
                    this.initialData.voyageRepairOrderStatus ===
                      '完工待审核11' ||
                    this.initialData.voyageRepairOrderStatus == '已确认'
                  "
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col
                class="py-0"
                md="12"
                cols="12"
                v-if="this.initialData.voyageRepairOrderStatus == '已确认'"
              >
                <!-- {{ this.initialData.jwReamrk }} -->
                <v-textarea
                  outlined
                  dense
                  placeholder="填写修理信息:港口、修理日期..."
                  v-model="formData.remark"
                  label="公司备注"
                ></v-textarea>
              </v-col>
              <v-col
                class="py-0"
                md="12"
                cols="12"
                v-if="this.initialData.voyageRepairOrderStatus == '已安排'"
              >
                <v-textarea
                  outlined
                  dense
                  placeholder="填写修理信息:港口、修理日期..."
                  v-model="formData.remark"
                  label="备注"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <v-col
                class="py-0"
                md="12"
                cols="12"
                v-if="this.initialData.voyageRepairOrderStatus == '完工待审核'"
              >
                <v-textarea
                  outlined
                  dense
                  placeholder="填写完工说明信息"
                  v-model="formData.remark"
                  label="完工说明"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <v-col
                class="py-0"
                md="12"
                cols="12"
                v-if="this.initialData.voyageRepairOrderStatus == '完工确认'"
              >
                <v-textarea
                  outlined
                  dense
                  placeholder="填写完工确认信息"
                  v-model="formData.remark"
                  label="完工确认信息"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <v-col
                class="py-0"
                md="12"
                cols="12"
                v-if="this.initialData.voyageRepairOrderStatus == '完工退回'"
              >
                <v-textarea
                  outlined
                  dense
                  placeholder="填写完工退回信息"
                  v-model="formData.remark"
                  label="完工退回信息"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
// import portSelectDialog from '@/views/maritime-maintence/components/port-select-dialog.vue'

export default {
  name: 'reduce-in-dialog',
  // components: { portSelectDialog },
  components: {},
  created() {
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
    status: {
      type: Number,
    },
  },
  data() {
    return {
      dialog: false,
      open: false,
      formData: {},
      resolveFn: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.open = false
      this.resolveFn(false)
    },

    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.open = false
      this.resolveFn(this.formData)
    },

    async confirm() {
      this.open = true
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
