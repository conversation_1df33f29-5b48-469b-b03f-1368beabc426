<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-text>
        <v-form ref="form">
          <v-container fluid class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <port-select-dialog
                  v-model="portId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                  label="修理港口"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  use-today
                  v-model="repairDate"
                  dense
                  outlined
                  label="安排日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="changeStatus('已安排')"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  确认
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="isShip ? headersShip : headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      use-ship
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            label="业务状态"
            outlined
            dense
            :items="businessStatusMap"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="订单号"
            outlined
            dense
            clearable
            v-model="searchObj.orderNo"
          ></v-text-field>
        </v-col>
        <!--        <v-col cols="12" sm="6" md="2">-->
        <!--          <v-switch-->
        <!--            class="mt-1"-->
        <!--            dense-->
        <!--            v-model="searchObj.isMe"-->
        <!--            label="待我审批"-->
        <!--            color="success"-->
        <!--          ></v-switch>-->
        <!--        </v-col>-->
      </template>
      <template #btns>
        <!-- <v-btn
          outlined
          :disabled="!selected || selected.businessStatus !== '待确认'"
          tile
          color="primary"
          class="mx-1"
          @click="changeStatus('已确认')"
          v-permission="['航修修理单:主管确认']"
        >
          <v-icon left>mdi-check</v-icon>
          主管确认
        </v-btn>
        <v-btn
          outlined
          :disabled="
            selected.businessStatus !== '已确认' || selected.status !== '3'
          "
          tile
          color="primary"
          class="mx-1"
          @click="editItem"
          v-permission="['航修修理单:安排修理']"
        >
          <v-icon left>mdi-package-variant-closed-check</v-icon>
          安排修理
        </v-btn> -->
        <v-btn
          :disabled="!canAbort"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="changeStatus('重新定标')"
          v-permission="['航修修理单:重新定标']"
        >
          <v-icon left>mdi-restart</v-icon>
          重新定标
        </v-btn>
        <v-btn
          :disabled="!canAbort"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="changeStatus('废弃')"
          v-permission="['航修修理单:废弃']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          废弃
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="downloadExcel"
          v-permission="['航修修理单:导出EXCEL']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          导出EXCEL
        </v-btn>
      </template>
      <template v-slot:[`item.businessStatus`]="{ item }">
        <v-chip v-if="item.businessStatus != null">
          {{ getTextByStatus(item.businessStatus) }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import portSelectDialog from '@/views/maritime-maintence/components/port-select-dialog.vue'
// applicantId	申请人	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// businessStatus	业务状态	string
// enquiryId	询价单id	string
// id	物理主键	string
// orderNo		string
// otherExpense	其他费用	number
// post	申请部门	string
// quoteId	报价单id	string
// remark	备注	string
// repairExpense	修理费用	number
// shipInfo	船舶编码	ShipInfoDO
export default {
  components: { portSelectDialog },
  name: 'voyage-repair-list',
  created() {
    this.init()
    this.buses = [
      '废弃',
      '待确认',
      '已确认',
      '已安排',
      '完工确认',
      '完工退回',
      '全部完工',
      '完工待审核',
      'SAP执行成功',
      '付款审批通过',
      '已确认，待OA审批通过',
    ]
    this.businessStatusMap = [
      // { text: '待机务/通导主管确认', value: '待确认' },
      // { text: '机务/通导主管已确认', value: '已确认' },
      { text: '供应商已安排(待确认完工)', value: '已安排' },
      { text: '供应商确认完工', value: '完工待审核' },
      { text: '机务/通导确认完工', value: '完工确认' },
      { text: '机务/通导退回完工', value: '完工退回' },
      { text: '已废弃', value: '废弃' },
      { text: '船端确认完工', value: '全部完工' },
      { text: '费用项目未提交', value: '未做凭证' },
      { text: '费用项目未提交', value: '付款中' },
      { text: '发票未提交', value: '已做凭证' },
      { text: '发票待实际申请人确认', value: '发票待实际申请人确认' },
      { text: '发票审批中', value: '审批中' },
      { text: '发票审批退回', value: '审批失败' },
      { text: '发票已审批', value: '审批通过' },
      { text: '发票财务未确认-业务', value: '映射错误' },
      { text: '发票财务未确认-业务', value: '报文错误' },
      { text: '发票财务确认中', value: '已发送SAP' },
      { text: '发票财务已确认', value: 'SAP执行成功' },
      { text: '发票财务未确认-SAP', value: 'SAP执行失败' },
      { text: '付款审批未提交', value: '付款审批未提交' },
      { text: '付款审批审批中', value: '付款审批中' },
      { text: '付款审批退回', value: '付款审批未通过' },
      { text: '付款审批已完成', value: '付款审批通过' },
    ]
  },

  data() {
    return {
      selected: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      portId: '',
      initPort: {},
      formShow: false,
      repairDate: '',
      loading: false,
      searchObj: {
        isMe: false,
        businessStatus: '',
        repairType: '',
      },
      repairType: '',
    }
  },

  computed: {
    canAbort() {
      return (
        this.selected &&
        [
          '待确认',
          '已确认',
          '已安排',
          '已确认,超二级科目预算审批失败',
        ].includes(this.selected.businessStatus)
      )
    },
    canExcel() {
      return (
        this.selected &&
        this.selected.businessStatus != '待确认' &&
        this.selected.businessStatus != '已确认' &&
        this.selected.businessStatus != '已安排' &&
        this.selected.businessStatus != '废弃' &&
        this.selected.businessStatus != '全部完工'
      )
    },
  },

  methods: {
    init() {
      this.tableName = '航修订单'
      this.isShip = this.$local.data.get('userInfo').isShipSyS
      this.reqUrl = '/business/shipAffairs/voyageRepair/orderPage'
      this.headers = [
        { text: '船舶', value: 'shipInfo' },
        { text: '订单号', value: 'orderNo' },
        { text: '币种', value: 'ccyCode' },
        { text: '原始报价修理费用', value: 'repairExpense' },
        { text: '原始报价其他费用', value: 'otherExpense' },
        { text: '实际修理费用', value: 'reallyRepairExpense' },
        { text: '实际其他费用', value: 'reallyOtherMoney' },
        // { text: '申请部门', value: 'dept' },
        { text: '修理日期', value: 'repairDate' },
        { text: '业务状态', value: 'businessStatus' },
        // { text: '流程状态', value: 'status' },
        { text: '备注', value: 'remark' },
        { text: '申请岗位', value: 'applicantPost' },
        { text: '修理类型', value: 'repairType' },
      ]
      this.headersShip = [
        { text: '船舶', value: 'shipInfo' },
        { text: '订单号', value: 'orderNo' },
        // { text: '申请部门', value: 'dept' },
        { text: '修理日期', value: 'repairDate' },
        { text: '业务状态', value: 'businessStatus' },
        // { text: '流程状态', value: 'status' },
        { text: '备注', value: 'remark' },
        { text: '申请岗位', value: 'applicantPost' },
        { text: '修理类型', value: 'repairType' },
      ]
      this.fuzzyLabel = ''
      this.searchDate = {
        label: '',
        value: '',
      }
      this.pushParams = { name: 'voyage-repair-detail' }
    },
    async editItem() {
      this.portId = this.selected.enquiryPortId
      this.initPort = {
        portCn: this.selected.enquiryPortName,
        id: this.selected.enquiryPortId,
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },

    closeForm() {
      this.$refs.form.reset()
      this.initPort = {}
      this.formShow = false
      this.$refs.table.disabled = false
      // this.init()
    },

    async changeStatus(businessStatus) {
      if (businessStatus == '已安排') {
        if (!this.$refs.form.validate()) return
      }
      if (businessStatus == '废弃') {
        if (!(await this.$dialog.msgbox.confirm('确定废弃此记录？'))) return
      }
      if (businessStatus == '重新定标') {
        if (!(await this.$dialog.msgbox.confirm('确定重新定标此记录？'))) return
      }
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/saveOrUpdateOrder',
        {
          id: this.selected.id,
          repairType: this.selected.repairType == '机务项目' ? 0 : 1,
          businessStatus,
          [businessStatus == '已安排' ? 'portId' : 'sdf']: this.portId,
          [businessStatus == '已安排' ? 'repairDate' : 'sdf']: this.repairDate,
        },
      )
      if (businessStatus === '已确认') {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/voyageRepair/submitOrderById',
          {
            id: this.selected.id,
          },
        )
        if (errorRaw) return
        if (!errorRaw) {
          this.$dialog.message.success('状态变更成功')
          this.selected = false
          this.closeForm()
          await this.loadTableData()
        }
      }
      if (!errorRaw) {
        this.$dialog.message.success('状态变更成功')
        this.selected = false
        this.closeForm()
        await this.loadTableData()
      }
    },
    async downloadExcel() {
      this.loading = true
      let params = {
        ...this.selected,
      }
      await this.getBlobDownload(
        '/business/shipAffairs/voyageRepair/excelExportVoyage',
        params,
        // 时间戳后四位
        `航修修理单-${this.selected.orderNo}.xlsx`,
      )
      this.loading = false
    },
    getTextByStatus(businessStatus) {
      const statusItem = this.businessStatusMap.find(
        (item) => item.value === businessStatus,
      )
      return statusItem ? statusItem.text : businessStatus
    },
  },

  mounted() {
    if (this.$route.query.businessStatus != undefined) {
      this.searchObj.businessStatus = this.$route.query.businessStatus
      this.searchObj.repairType = this.$route.query.repairType
    }
  },
}
</script>

<style></style>
