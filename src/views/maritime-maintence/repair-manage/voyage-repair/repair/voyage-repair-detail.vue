<template>
  <v-container fluid>
    <v-detail-view
      :title="`航修订单详情-${detail.orderNo}`"
      :tooltip="detail.orderNo"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="detail.auditParams && detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['航修修理单:编辑']"
      ref="detail"
      :can-save="false"
    >
      <template #custombtns>
        <v-btn
          :loading="loading"
          v-if="
            detail.businessStatus == '待确认' && detail.repairType == '机务项目'
          "
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('已确认')"
          v-permission="['航修修理单:主管确认']"
        >
          主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="
            detail.businessStatus == '待确认' && detail.repairType == '通导项目'
          "
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('已确认')"
          v-permission="['航修修理单:主管确认(通导)']"
        >
          通导信息主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == '已确认'"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('已安排')"
          v-permission="['航修修理单:安排修理']"
        >
          安排修理
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="
            detail.businessStatus === '已安排' ||
            detail.businessStatus == '完工退回'
          "
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('完工待审核')"
          v-permission="['航修修理单:完工待审核']"
        >
          修理完成
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="false"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('完工确认')"
          v-permission="['航修修理单:完工确认(机务)']"
        >
          完工确认(机务)
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="false"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('完工确认')"
          v-permission="['航修修理单:完工确认(通导)']"
        >
          完工确认(通导)
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="false"
          width="120"
          tile
          color="error"
          small
          class="mx-1"
          @click="changeStatus('完工退回')"
          v-permission="['航修修理单:完工退回']"
        >
          完工退回
        </v-btn>
      </template>
      <template v-if="detail.auditParams && !isSupper" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="form">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-row>
          <v-col cols="12">
            <v-alert type="info" color="green" text dense class="mb-0">
              发票和结算单附件，由供应商上传；完工单附件由船员上传。
            </v-alert>
          </v-col>
        </v-row>
        <v-card-text
          type="info"
          color="green"
          text
          dense
          class="mb-0"
        ></v-card-text>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="3" class="py-0">
              <v-text-field
                :disabled="
                  detail.businessStatus != '已安排' &&
                  detail.businessStatus != '完工退回'
                "
                outlined
                dense
                v-model="detail.invoiceCode"
                label="发票编号"
                :error="isInvoiceCodeInvalid"
                :error-messages="isInvoiceCodeInvalid ? '请填写发票编号' : ''"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3" class="py-0">
              <v-text-field
                :disabled="
                  detail.businessStatus != '已安排' &&
                  detail.businessStatus != '完工退回'
                "
                outlined
                dense
                type="date"
                v-model="detail.invoiceDate"
                label="发票日期"
                clearable
                :error="isInvoiceDateInvalid"
                :error-messages="isInvoiceDateInvalid ? '请填写发票日期' : ''"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3" class="py-0">
              <v-text-field
                :disabled="
                  detail.businessStatus != '已安排' &&
                  detail.businessStatus != '完工退回'
                "
                type="number"
                outlined
                dense
                v-model="detail.invoiceMoney"
                label="发票金额"
                :error="isInvoiceMoneyInvalid"
                :error-messages="
                  isInvoiceMoneyInvalid ? '请填写发票金额费用' : ''
                "
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3" class="py-0">
              <v-btn
                :loading="loading"
                v-if="
                  detail.businessStatus == '已安排' ||
                  detail.businessStatus == '完工退回'
                "
                width="120"
                tile
                color="warning"
                class="mx-1"
                @click="savAttachs()"
                v-permission="['航修修理单:保存发票']"
              >
                提交发票
              </v-btn>
            </v-col>
          </v-row>
          <v-attach-list
            title="发票/结算单附件"
            :attachments="detail.attachmentRecords4"
            @change="(ids) => (detail.invoiceAttachmentRecords = ids)"
            :readonly="
              detail.businessStatus != '已安排' &&
              detail.businessStatus != '完工退回'
            "
          ></v-attach-list>
          <!--          <v-attach-list-->
          <!--            title="结算单附件"-->
          <!--            :attachments="detail.attachmentRecords5"-->
          <!--            @change="(ids) => (detail.invoiceAttachmentRecords1 = ids)"-->
          <!--            :readonly="-->
          <!--              detail.businessStatus != '已安排' &&-->
          <!--              detail.businessStatus != '完工退回'-->
          <!--            "-->
          <!--          ></v-attach-list>-->
        </v-card-text>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <template>
                <v-col cols="12">
                  <v-card class="thick-border" outlined>
                    <v-card-text>
                      <div>
                        <b>航修项目/repair project:</b>
                        {{ detail.applyPurpose.split('#:#')[0] }}
                      </div>
                      <div>
                        <b>故障诊断/trouble shooting:</b>
                        {{ detail.applyPurpose.split('#:#')[1] }}
                      </div>
                      <div>
                        <b>所需岸基支持/land support:</b>
                        {{ detail.applyPurpose.split('#:#')[2] }}
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
              </template>
              <v-col cols="12" md="3">
                <v-ship-select
                  readonly
                  v-model="detail.shipCode"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="申请人岗位"
                  dense
                  outlined
                  v-model="detail.applicantPost"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="申请部门"
                  dense
                  outlined
                  v-model="detail.dept"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="港口"
                  dense
                  outlined
                  v-model="detail.enquiryPortName"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="修理日期"
                  dense
                  outlined
                  v-model="detail.repairDate"
                  :readonly="
                    detail.businessStatus !== '已安排' &&
                    detail.businessStatus !== '完工退回'
                  "
                  type="date"
                  :error="isRepairDateInvalid"
                  :error-messages="isRepairDateInvalid ? '请填写修理日期' : ''"
                  clearable
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  label="修理类型"
                  dense
                  outlined
                  v-model="detail.repairType"
                  :value="detail.repairType"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  label="币别"
                  dense
                  outlined
                  v-model="detail.ccyCode"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3"></v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  label="原始报价修理费用"
                  dense
                  outlined
                  v-model="detail.repairExpense"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  label="原始报价其他费用"
                  dense
                  outlined
                  v-model="detail.otherExpense"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  label="原始报价总费用(折算美金)"
                  dense
                  outlined
                  v-model="detail.toUsd"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3"></v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="!isJPY"
                  @change="
                    () =>
                      (detail.reallyRepairExpense = (
                        Math.round(detail.reallyRepairExpense * 100) / 100
                      ).toFixed(2))
                  "
                  :label="'实际修理费用(' + detail.ccyCode + ')'"
                  dense
                  outlined
                  type="number"
                  :placeholder="detail.reallyRepairExpense1"
                  v-model="detail.reallyRepairExpense"
                  :readonly="
                    detail.businessStatus !== '已安排' &&
                    detail.businessStatus !== '完工退回'
                  "
                  :error="isReallyRepairExpenseInvalid"
                  :error-messages="
                    isReallyRepairExpenseInvalid ? '请填写实际修理费用' : ''
                  "
                ></v-text-field>
                <v-text-field
                  v-if="isJPY"
                  :label="'实际修理费用(' + detail.ccyCode + ')'"
                  dense
                  outlined
                  type="number"
                  :placeholder="detail.reallyRepairExpense1"
                  v-model="detail.reallyRepairExpense"
                  @change="
                    () =>
                      (detail.reallyRepairExpense = Math.round(
                        detail.reallyRepairExpense,
                      ))
                  "
                  :readonly="
                    detail.businessStatus !== '已安排' &&
                    detail.businessStatus !== '完工退回'
                  "
                  :error="isReallyRepairExpenseInvalid"
                  :error-messages="
                    isReallyRepairExpenseInvalid ? '请填写实际修理费用' : ''
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  label="实际修理费用(折算美金)"
                  dense
                  outlined
                  type="number"
                  v-model="reallyRepairExpense"
                  :readonly="true"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="!isJPY"
                  @change="
                    () =>
                      (detail.reallyOtherMoney = (
                        Math.round(detail.reallyOtherMoney * 100) / 100
                      ).toFixed(2))
                  "
                  :label="'实际其他费用(' + detail.ccyCode + ')'"
                  dense
                  outlined
                  type="number"
                  v-model="detail.reallyOtherMoney"
                  :placeholder="detail.reallyOtherMoney1"
                  :readonly="
                    detail.businessStatus !== '已安排' &&
                    detail.businessStatus !== '完工退回'
                  "
                  :error="isReallyOtherMoneyInvalid"
                  :error-messages="
                    isReallyOtherMoneyInvalid ? '请填写实际其他费用' : ''
                  "
                ></v-text-field>
                <v-text-field
                  v-if="isJPY"
                  @change="
                    () =>
                      (detail.reallyOtherMoney = Math.round(
                        detail.reallyOtherMoney,
                      ))
                  "
                  :label="'实际其他费用(' + detail.ccyCode + ')'"
                  dense
                  outlined
                  type="number"
                  v-model="detail.reallyOtherMoney"
                  :placeholder="detail.reallyOtherMoney1"
                  :readonly="
                    detail.businessStatus !== '已安排' &&
                    detail.businessStatus !== '完工退回'
                  "
                  :error="isReallyOtherMoneyInvalid"
                  :error-messages="
                    isReallyOtherMoneyInvalid ? '请填写实际其他费用' : ''
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  label="实际其他费用(折算美金)"
                  dense
                  outlined
                  type="number"
                  v-model="reallyOtherMoney"
                  :readonly="true"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="
                    detail.businessStatus !== '已安排' &&
                    detail.businessStatus !== '完工退回'
                  "
                  label="实际总金额（美金）"
                  dense
                  outlined
                  type="number"
                  v-model="detail.reallyFinalMoney"
                  readonly="true"
                ></v-text-field>
                <v-text-field
                  v-if="
                    detail.businessStatus == '已安排' ||
                    detail.businessStatus == '完工退回'
                  "
                  label="实际总金额（美金）"
                  dense
                  outlined
                  type="number"
                  v-model="reallyFinalMoney"
                  readonly="true"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  label="付款金额"
                  dense
                  outlined
                  v-model="detail.finalMoney"
                  readonly
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>

      <!-- 实际修理费&加工费 -->
      <template #实际修理费&加工费 v-if="!isShip">
        <v-card-text>
          <div class="d-flex justify-end mb-2">
            <v-chip color="primary" label>
              实际修理费总价：{{ actualRepairTotalPrice }}
            </v-chip>
          </div>
          <v-table-list
            :headers="repairHeaders"
            :items="repairList"
            v-model="selectedRepair"
            :ship-code="detail.shipInfo.shipCode"
            item-key="vid"
          ></v-table-list>
          <v-btn
            v-if="canEdit"
            outlined
            tile
            small
            color="success"
            class="mx-1 mt-2"
            @click.stop="createRepairItem"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            v-if="canEdit && selectedRepair"
            outlined
            small
            tile
            color="error"
            class="mx-1 mt-2"
            @click="delRepairItem"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-card-text>
      </template>

      <!-- 实际其他费用 -->
      <template #实际其他费用 v-if="!isShip">
        <v-card-text>
          <div class="d-flex justify-end mb-2">
            <v-chip color="primary" label>
              实际其他费用总价：{{ actualOtherTotalPrice }}
            </v-chip>
          </div>
          <v-table-list
            :headers="otherHeaders"
            :items="otherList"
            v-model="selectedOther"
            :ship-code="detail.shipInfo.shipCode"
            item-key="vid"
          ></v-table-list>
          <v-btn
            v-if="canEdit"
            outlined
            tile
            small
            color="success"
            class="mx-1 mt-2"
            @click.stop="createOtherItem"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            v-if="canEdit && selectedOther"
            outlined
            small
            tile
            color="error"
            class="mx-1 mt-2"
            @click="delOtherItem"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-card-text>
      </template>

      <!-- 实际备件费用 -->
      <template #实际备件费用 v-if="!isShip">
        <v-card-text>
          <div class="d-flex justify-end mb-2">
            <v-chip color="primary" label>
              实际备件费总价：{{ actualSpareTotalPrice }}
            </v-chip>
          </div>
          <v-table-list
            :headers="spareHeaders"
            :items="spareList"
            v-model="selectedSpare"
            :ship-code="detail.shipInfo.shipCode"
            item-key="vid"
          ></v-table-list>
          <v-btn
            v-if="canEdit"
            outlined
            tile
            small
            color="success"
            class="mx-1 mt-2"
            @click.stop="createSpareItem"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            v-if="canEdit && selectedSpare"
            outlined
            small
            tile
            color="error"
            class="mx-1 mt-2"
            @click="delSpareItem"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-card-text>
      </template>

      <!-- 实际物料费用 -->
      <template #实际物料费用 v-if="!isShip">
        <v-card-text>
          <div class="d-flex justify-end mb-2">
            <v-chip color="primary" label>
              实际物料费总价：{{ actualMaterialTotalPrice }}
            </v-chip>
          </div>
          <v-table-list
            :headers="materialHeaders"
            :items="materialList"
            v-model="selectedMaterial"
            :ship-code="detail.shipInfo.shipCode"
            item-key="vid"
          ></v-table-list>
          <v-btn
            v-if="canEdit"
            outlined
            tile
            small
            color="success"
            class="mx-1 mt-2"
            @click.stop="createMaterialItem"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            v-if="canEdit && selectedMaterial"
            outlined
            small
            tile
            color="error"
            class="mx-1 mt-2"
            @click="delMaterialItem"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-card-text>
      </template>

      <template #供应商信息>
        <v-container fluid>
          <v-row>
            <v-col cols="12" md="3" v-if="!isSupper && !isShip">
              <v-text-field
                label="额外预估费用"
                dense
                outlined
                v-model="detail.otherCost"
                readonly
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="3" v-if="!isSupper && !isShip">
              <v-text-field
                label="额外预估费用(美金)"
                dense
                outlined
                v-model="otherCost"
                readonly
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-textarea
                label="备注"
                dense
                outlined
                v-model="detail.remark"
                placeholder="填写修理信息:港口、修理日期..."
                readonly
              ></v-textarea>
            </v-col>
            <v-col cols="12" md="12" v-if="false">
              <v-attach-list
                :attachments="detail.attachmentRecords1"
                title="完工附件列表(供应商)"
                @change="(ids) => (detail.attachmentIds1 = ids)"
                :readonly="
                  detail.businessStatus !== '已安排' &&
                  detail.businessStatus !== '完工退回'
                "
              ></v-attach-list>
            </v-col>
            <v-col cols="12" md="12">
              <v-attach-list
                :attachments="detail.attachmentRecords3"
                title="完工单附件列表(船端)"
                :readonly="true"
              ></v-attach-list>
            </v-col>
            <v-col cols="12" md="12">
              <v-attach-list
                :attachments="detail.attachmentRecords2"
                title="申请单附件列表"
                :readonly="true"
              ></v-attach-list>
            </v-col>
          </v-row>
        </v-container>
        <!--        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>-->
      </template>
      <template #供应商信息按钮>
        <!-- TODO:何时可以评价 -->
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['航修修理单:评价供应商']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          评价供应商
        </v-btn>
      </template>
      <template>
        <v-container fluid>
          <b>供应商名称:</b>
          {{ detail.supplierName }}
          <v-card outlined class="mt-1 pt-2 px-2" v-if="comment">
            <v-row>
              <v-col cols="12" md="6">
                质量评分
                <v-rating
                  v-model="comment.score1"
                  background-color="purple lighten-3"
                  color="purple"
                  length="10"
                  readonly
                ></v-rating>
              </v-col>
              <v-col cols="12" md="6">
                服务评分
                <v-rating
                  v-model="comment.score2"
                  background-color="green lighten-3"
                  color="green"
                  length="10"
                  readonly
                ></v-rating>
              </v-col>
            </v-row>
            <v-card-text class="text-body-1">{{ comment.remark }}</v-card-text>
            <v-card-text>
              <v-attach-list
                title="评价附件"
                disabled
                :attachments="comment.attachmentRecords"
              ></v-attach-list>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <b>评论最后更改:</b>
              {{ comment.remarkTime }}，
              <b>评论人:</b>
              {{ comment.userNickName }}
            </v-card-actions>
          </v-card>
        </v-container>
      </template>
      <template #消耗单>
        <v-table-list :headers="isShip ? headersShip : headers" :items="outs">
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo.chShipName }}
          </template>
          <template v-slot:[`item.inoutMode`]="{ item }">
            {{
              [
                '订单入库',
                '调减入库',
                '期初入库',
                '维护保养消耗',
                '自修消耗',
                '航修消耗',
                '坞修消耗',
                '自由出库',
              ][item.inoutMode]
            }}
          </template>
          <template v-slot:[`item.inoutCode`]="{ item }">
            <router-link
              v-if="item.inoutNature == 0"
              :to="{ name: 'spare-out-detail', params: { id: item.id } }"
            >
              {{ item.inoutCode }}
            </router-link>
            <router-link
              v-else-if="item.inoutNature == 1"
              :to="{ name: 'spare-out-list', params: { id: item.id } }"
            >
              {{ item.inoutCode }}
            </router-link>
            <router-link
              v-else-if="item.inoutNature == 2"
              :to="{ name: 'spare-out-list', params: { id: item.id } }"
            >
              {{ item.inoutCode }}
            </router-link>
          </template>
          <template v-slot:[`item.inoutNature`]="{ item }">
            {{ ['备件', '物料', '滑油'][item.inoutNature] }}
          </template>
          <template v-slot:[`item.status`]="{ item }">
            {{ ['', '未提交', '审批中', '审批通过', '审批驳回'][item.status] }}
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <comment-dialog
      v-model="dialog"
      :initialData="initData"
      @success="loadCom"
    ></comment-dialog>
    <voyage-confirm-dialog
      :initialData="orderConfirmData"
      ref="dialog"
    ></voyage-confirm-dialog>
    <v-dialog v-model="completeDialog" max-width="500">
      <v-card>
        <v-card-title class="headline">完工确认</v-card-title>
        <v-card-text>
          <v-radio-group v-model="attachmentChoice" mandatory>
            <v-radio
              label="去挂完工单附件，在《航修完工单》页面中生成完工单到监督主管审批"
              value="upload"
            ></v-radio>
            <v-radio
              label="不挂附件，直接提交,在《航修完工单》页面中生成此订单的完工单草稿"
              value="skip"
            ></v-radio>
          </v-radio-group>

          <v-expand-transition>
            <div v-if="attachmentChoice === 'upload'" class="mt-4">
              <v-attach-list
                :attachments="detail.attachmentRecords1"
                title="完工附件列表"
                @change="(ids) => (detail.attachmentIds1 = ids)"
              ></v-attach-list>
            </div>
          </v-expand-transition>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey darken-1" text @click="completeDialog = false">
            取消
          </v-btn>
          <v-btn color="primary" @click="confirmComplete">确认</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import commentDialog from '@/views/maritime-maintence/components/comment-dialog.vue'
import routerControl from '@/mixin/routerControl'
import VoyageConfirmDialog from './private/voyage-confirm-dialog.vue'
import VAttachList from '@/components/v-attach-list.vue'
export default {
  name: 'voyage-repair-detail',
  // eslint-disable-next-line vue/no-unused-components
  components: { VAttachList, commentDialog, VoyageConfirmDialog },
  created() {
    this.backRouteName = 'voyage-repair-list'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '出库日期', value: 'inoutDate' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '物品类型', value: 'inoutNature' },
      { text: '物品金额', value: 'amountInvolved' },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '出库日期', value: 'inoutDate' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '物品类型', value: 'inoutNature' },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
  },
  mixins: [routerControl],
  computed: {
    isReallyRepairExpenseInvalid() {
      return (
        (this.detail.businessStatus == '已安排' ||
          this.detail.businessStatus == '完工退回') &&
        (this.detail.reallyRepairExpense == null ||
          this.detail.reallyRepairExpense === 0)
      )
    },
    isReallyOtherMoneyInvalid() {
      return (
        (this.detail.businessStatus == '已安排' ||
          this.detail.businessStatus == '完工退回') &&
        (this.detail.reallyOtherMoney == null ||
          this.detail.reallyOtherMoney === 0)
      )
    },
    isInvoiceCodeInvalid() {
      return (
        (this.detail.businessStatus == '已安排' ||
          this.detail.businessStatus == '完工退回') &&
        this.detail.invoiceCode == null
      )
    },
    isInvoiceMoneyInvalid() {
      return (
        ((this.detail.businessStatus == '已安排' ||
          this.detail.businessStatus == '完工退回') &&
          this.detail.invoiceMoney == null) ||
        this.detail.invoiceMoney === 0
      )
    },
    isInvoiceDateInvalid() {
      return (
        (this.detail.businessStatus == '已安排' ||
          this.detail.businessStatus == '完工退回') &&
        this.detail.invoiceDate == null
      )
    },
    isRepairDateInvalid() {
      return (
        (this.detail.businessStatus == '已安排' ||
          this.detail.businessStatus == '完工退回') &&
        this.detail.repairDate == null
      )
    },
    isJPY() {
      return this.detail.ccyCode === 'JPY' || this.detail.ccyCode === '日元'
    },
    isSupper() {
      return this.$local.data.get('userInfo').userType == 4
    },
    otherCost() {
      return ((this.detail.otherCost || 0) * this.detail.rateToMain).toFixed(2)
    },
    reallyRepairExpense() {
      return (
        (this.detail.reallyRepairExpense || 0) * this.detail.rateToMain
      ).toFixed(2)
    },
    reallyOtherMoney() {
      return (
        (this.detail.reallyOtherMoney || 0) * this.detail.rateToMain
      ).toFixed(2)
    },
    reallyFinalMoney() {
      return (
        (this.detail.reallyRepairExpense || 0) * this.detail.rateToMain +
        (this.detail.reallyOtherMoney || 0) * this.detail.rateToMain
      ).toFixed(2)
    },
    // 是否可以编辑实际费用详情
    canEdit() {
      return (
        this.detail.businessStatus === '已安排' ||
        this.detail.businessStatus === '完工退回'
      )
    },
    // 实际修理费总价
    actualRepairTotalPrice() {
      return this.repairList
        .reduce((sum, item) => {
          return sum + (Number(item.finalTotal) || 0)
        }, 0)
        .toFixed(2)
    },
    // 实际其他费用总价
    actualOtherTotalPrice() {
      return this.otherList
        .reduce((sum, item) => {
          return sum + (Number(item.finalTotal) || 0)
        }, 0)
        .toFixed(2)
    },
    // 实际备件费总价
    actualSpareTotalPrice() {
      return this.spareList
        .reduce((sum, item) => {
          return sum + (Number(item.finalTotal) || 0)
        }, 0)
        .toFixed(2)
    },
    // 实际物料费总价
    actualMaterialTotalPrice() {
      return this.materialList
        .reduce((sum, item) => {
          return sum + (Number(item.finalTotal) || 0)
        }, 0)
        .toFixed(2)
    },
  },
  data() {
    return {
      detail: {
        attachmentRecords: [],
        attachmentIds: [],
        applyPurpose: '',
      },
      dialog: false,
      initData: {},
      comment: {},
      spareOutId: '',
      materialOutId: '',
      soilOutId: '',
      outs: [],
      subtitles: [
        '基本信息',
        '实际修理费&加工费',
        '实际其他费用',
        '实际备件费用',
        '实际物料费用',
        '供应商信息',
        '消耗单',
      ],
      orderConfirmData: {},
      loading: false,
      completeDialog: false,
      attachmentChoice: 'upload',
      completionAttachments: [],
      // 实际费用详细列表
      repairList: [],
      otherList: [],
      spareList: [],
      materialList: [],
      // 选中的项目
      selectedRepair: null,
      selectedOther: null,
      selectedSpare: null,
      selectedMaterial: null,
      // 表格头部定义
      repairHeaders: [
        { text: '修理项目', value: 'itemName' },
        { text: '实际单价', value: 'realPrice' },
        { text: '实际数量', value: 'finalNum' },
        { text: '实际总价', value: 'finalTotal' },
        { text: '预估单价', value: 'price' },
        { text: '预估数量', value: 'quotNum' },
        { text: '预估总价', value: 'total' },
        { text: '备注', value: 'remark' },
      ],
      otherHeaders: [
        { text: '费用名称', value: 'itemName' },
        { text: '实际单价', value: 'realPrice' },
        { text: '实际数量', value: 'finalNum' },
        { text: '实际总价', value: 'finalTotal' },
        { text: '预估单价', value: 'price' },
        { text: '预估数量', value: 'quotNum' },
        { text: '预估总价', value: 'total' },
        { text: '备注', value: 'remark' },
      ],
      spareHeaders: [
        { text: '备件名称', value: 'componentName' },
        { text: '备件号', value: 'componentNo' },
        { text: '图纸号', value: 'drawingSerialNumber' },
        { text: '序列号', value: 'serialNumber' },
        { text: '实际数量', value: 'finalNum' },
        { text: '实际单价', value: 'realPrice' },
        { text: '实际总价', value: 'finalTotal' },
        { text: '预估数量', value: 'quotNum' },
        { text: '预估单价', value: 'price' },
        { text: '预估总价', value: 'total' },
        { text: '备注', value: 'remark' },
      ],
      materialHeaders: [
        { text: '物料名称', value: 'itemName' },
        { text: '物料编码', value: 'code' },
        { text: '实际数量', value: 'finalNum' },
        { text: '实际单价', value: 'realPrice' },
        { text: '实际总价', value: 'finalTotal' },
        { text: '预估数量', value: 'quotNum' },
        { text: '预估单价', value: 'price' },
        { text: '预估总价', value: 'total' },
        { text: '备注', value: 'remark' },
      ],
    }
  },

  watch: {
    spareOutId(val) {
      if (val) this.loadOuts(val)
    },
    materialOutId(val) {
      if (val) this.loadOuts(val)
    },
    soilOutId(val) {
      if (val) this.loadOuts(val)
    },
  },

  methods: {
    async savAttachs() {
      if (!this.detail.invoiceCode) {
        this.$dialog.message.error('请填写发票号')
        return false
      }
      if (!this.detail.invoiceDate) {
        this.$dialog.message.error('请填写发票日期')
        return false
      }
      if (this.detail.invoiceMoney <= 0) {
        this.$dialog.message.error('请填写发票金额')
        return false
      }
      if (
        !this.detail.invoiceAttachmentRecords ||
        this.detail.invoiceAttachmentRecords.length === 0
      ) {
        this.$dialog.message.error('请上传发票/结算单附件')
        return false
      }
      // if (
      //   !this.detail.invoiceAttachmentRecords ||
      //   this.detail.invoiceAttachmentRecords1.length === 0
      // ) {
      //   this.$dialog.message.error('请上传结算单附件')
      //   return false
      // }
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/saveVoyageOrderAttachmentIds',
        {
          id: this.detail.id,
          invoiceCode: this.detail.invoiceCode,
          invoiceDate: this.detail.invoiceDate,
          invoiceMoney: this.detail.invoiceMoney,
          invoiceAttachmentRecords: this.detail.invoiceAttachmentRecords,
          //invoiceAttachmentRecords1: this.detail.invoiceAttachmentRecords1,
        },
      )
      if (errorRaw) return
      this.$dialog.message.success('上传成功')
      this.loadDetail()
      //this.$refs.detail.closeAndTo(this.backRouteName)
    },

    async save(goBack, notMove = false) {
      // 合并详细列表数据
      const detailList = [
        ...this.repairList,
        ...this.otherList,
        ...this.spareList,
        ...this.materialList,
      ]

      const saveData = {
        ...this.detail,
        detailList,
      }

      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/saveOrUpdateOrder',
        saveData,
      )
      if (errorRaw) {
        return false
      }
      if (notMove) {
        return true
      }
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const res = await this.save(goBack, true)
      if (!res) {
        return false
      }
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/voyageRepair/submitOrderById',
          { id: this.detail.id },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/getOrderDetailById',
        {
          id: this.$route.params.id,
        },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
      this.orderConfirmData = {
        initPort: {
          portCn: this.detail.enquiryPortName,
          id: this.detail.enquiryPortId,
        },
        portId: this.detail.enquiryPortId,
      }
      if (
        this.detail.businessStatus === '已安排' ||
        this.detail.businessStatus === '完工退回'
      ) {
        if (
          this.detail.reallyRepairExpense1 == null ||
          this.detail.reallyRepairExpense1 === 0
        ) {
          this.detail.reallyRepairExpense1 = this.detail.repairExpense
        }
        if (
          this.detail.reallyOtherMoney1 == null ||
          this.detail.reallyOtherMoney1 === 0
        ) {
          this.detail.reallyOtherMoney1 = this.detail.otherExpense
        }
        if (this.detail.reallyRepairExpense === 0.0) {
          this.detail.reallyRepairExpense = null
        }
        if (this.detail.reallyOtherMoney === 0.0) {
          this.detail.reallyOtherMoney = null
        }
      }

      // 处理实际费用详细列表
      this.processDetailList()
    },
    async loadCom() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/SupplierAssessment/getByOrderId',
        {
          id: this.$route.params.id,
        },
      )
      this.comment = data
    },
    createCom() {
      this.dialog = true
      this.initData = {
        ...this.comment,
        supplierId: this.detail.supplierId,
        supplierName: this.detail.supplierName,
        orderId: this.detail.id,
        orderNo: this.detail.orderNo,
      }
    },

    async loadComplete() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/submitCompletionByOrderId',
        {
          id: this.$route.params.id,
        },
      )
      if (data.id) {
        // 消耗单标签页已在初始化时添加
        this.spareOutId = data.componentOutId
        this.soilOutId = data.greaseOutId
        this.materialOutId = data.materialsOutId
      }
    },

    async loadOuts(id) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseStockInOutPage',
        { id },
      )
      this.outs.push(data.records[0])
    },

    async changeStatus(businessStatus) {
      if (businessStatus === '完工确认') {
        this.completeDialog = true
        return
      }
      let formData = { businessStatus }
      if (businessStatus == '已确认') {
        this.orderConfirmData.voyageRepairOrderStatus = '已确认'
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        info.remark = '主管确认：' + info.remark + '\n'
        formData = { ...formData, ...info }
      }
      if (businessStatus == '已安排') {
        this.orderConfirmData.voyageRepairOrderStatus = '已安排'
        this.orderConfirmData.jwReamrk = this.detail.remark
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        info.remark =
          this.detail.remark + '供应商安排修理：' + info.remark + '\n'
        formData = { ...formData, ...info }
      }
      if (businessStatus == '完工待审核') {
        if (!this.detail.invoiceCode) {
          this.$dialog.message.error('请提交发票号')
          return false
        }
        if (!this.detail.invoiceDate) {
          this.$dialog.message.error('请提交发票日期')
          return false
        }
        if (this.detail.invoiceMoney <= 0) {
          this.$dialog.message.error('请提交发票金额')
          return false
        }
        if (
          !this.detail.invoiceAttachmentRecords ||
          this.detail.invoiceAttachmentRecords.length === 0
        ) {
          this.$dialog.message.error('请提交发票/结算单附件')
          return false
        }
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/voyageRepair/saveVoyageOrderAttachmentIds',
          {
            id: this.detail.id,
            invoiceCode: this.detail.invoiceCode,
            invoiceDate: this.detail.invoiceDate,
            invoiceMoney: this.detail.invoiceMoney,
            invoiceAttachmentRecords: this.detail.invoiceAttachmentRecords,
          },
        )
        if (errorRaw) return
        if (this.detail.repairDate == null) {
          this.$dialog.message.error('请选择维修日期')
          return
        }
        if (
          this.detail.reallyRepairExpense == null ||
          this.detail.reallyRepairExpense === 0
        ) {
          this.$dialog.message.error('请填写实际修理费用')
          return
        }
        if (
          this.detail.reallyOtherMoney == null ||
          this.detail.reallyOtherMoney === 0
        ) {
          this.$dialog.message.error('请填写实际其他费用')
          return
        }
        this.orderConfirmData.voyageRepairOrderStatus = '完工待审核'
        this.orderConfirmData.jwReamrk = this.detail.remark
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        info.remark = this.detail.remark + '供应商已完工：' + info.remark + '\n'
        formData = { ...formData, ...info }
      }
      if (businessStatus == '完工退回') {
        this.orderConfirmData.voyageRepairOrderStatus = '完工退回'
        this.orderConfirmData.jwReamrk = this.detail.remark
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        info.remark = this.detail.remark + '完工退回：' + info.remark + '\n'
        formData = { ...formData, ...info }
      }
      this.loading = true
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/saveOrUpdateOrder',
        {
          id: this.detail.id,
          repairType: this.detail.repairType,
          currencyId: this.detail.currencyId,
          reallyRepairExpense: this.detail.reallyRepairExpense,
          reallyOtherMoney: this.detail.reallyOtherMoney,
          attachmentIds1: this.detail.attachmentIds1,
          repairDate: this.detail.repairDate,
          ...formData,
        },
      )
      this.loading = false
      if (errorRaw) return
      this.$dialog.message.success('状态变更成功')
      this.$refs.detail.closeAndTo(this.backRouteName)
    },

    async confirmComplete() {
      if (
        this.attachmentChoice === 'upload' &&
        (!this.detail.attachmentIds1 || this.detail.attachmentIds1.length === 0)
      ) {
        this.$dialog.message.error('请上传完工单附件')
        return
      }
      this.completeDialog = false
      this.orderConfirmData.voyageRepairOrderStatus = '完工确认'
      this.orderConfirmData.jwReamrk = this.detail.remark
      const info = await this.$refs.dialog.confirm()
      if (!info) return
      info.remark = this.detail.remark + '完工确认：' + info.remark + '\n'
      let formData = { ...formData, ...info }
      this.loading = true

      try {
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/voyageRepair/saveOrUpdateOrder',
          {
            id: this.detail.id,
            businessStatus: '完工确认',
            repairType: this.detail.repairType,
            currencyId: this.detail.currencyId,
            reallyRepairExpense: this.detail.reallyRepairExpense,
            reallyOtherMoney: this.detail.reallyOtherMoney,
            attachmentIds1: this.detail.attachmentIds1,
            ...formData,
          },
        )
        this.loading = false

        if (!errorRaw) {
          this.$dialog.message.success('完工确认成功')
          this.completeDialog = false
          this.closeAndTo(this.backRouteName)
        }
      } catch (error) {
        console.error('完工确认失败:', error)
        this.$dialog.message.error('完工确认失败')
      }
    },

    // 处理详细列表数据
    processDetailList() {
      if (!this.detail.detailList) return

      // 根据 model 字段分类数据
      this.repairList = this.detail.detailList.filter(
        (item) => item.model === 0,
      )
      this.otherList = this.detail.detailList.filter((item) => item.model === 1)
      this.spareList = this.detail.detailList.filter((item) => item.model === 2)
      this.materialList = this.detail.detailList.filter(
        (item) => item.model === 3,
      )

      // 为每个项目设置默认值
      this.repairList.forEach((item) => this.setDefaultValues(item))
      this.otherList.forEach((item) => this.setDefaultValues(item))
      this.spareList.forEach((item) => this.setDefaultValues(item))
      this.materialList.forEach((item) => this.setDefaultValues(item))
    },

    // 设置默认值
    setDefaultValues(item) {
      if (item.finalNum === undefined || item.finalNum === null) {
        item.finalNum = item.quotNum || 0
      }
      if (item.realPrice === undefined || item.realPrice === null) {
        item.realPrice = item.price || 0
      }
      if (item.finalTotal === undefined || item.finalTotal === null) {
        item.finalTotal = (item.finalNum || 0) * (item.realPrice || 0)
      }
      // 为每个项目添加唯一标识符
      if (!item.vid) {
        item.vid = item.id || Date.now() + Math.random()
      }
    },

    // 计算项目总价
    calculateItemTotal(item) {
      const finalNum = Number(item.finalNum) || 0
      const realPrice = Number(item.realPrice) || 0
      item.finalTotal = finalNum * realPrice
    },

    // 格式化货币显示
    formatCurrency(value) {
      return (value || 0).toFixed(2)
    },

    // 创建修理项目
    createRepairItem() {
      const newItem = {
        itemName: '',
        realPrice: 0,
        finalNum: 0,
        finalTotal: 0,
        price: 0,
        quotNum: 0,
        total: 0,
        remark: '',
        model: 0,
        orderId: this.detail.id,
        vid: Date.now() + Math.random(),
      }
      this.repairList.push(newItem)
    },

    // 删除修理项目
    delRepairItem() {
      if (this.selectedRepair) {
        const index = this.repairList.findIndex(
          (item) => item.vid === this.selectedRepair.vid,
        )
        if (index > -1) {
          this.repairList.splice(index, 1)
        }
        this.selectedRepair = null
      }
    },

    // 创建其他费用项目
    createOtherItem() {
      const newItem = {
        itemName: '',
        realPrice: 0,
        finalNum: 0,
        finalTotal: 0,
        price: 0,
        quotNum: 0,
        total: 0,
        remark: '',
        model: 1,
        orderId: this.detail.id,
        vid: Date.now() + Math.random(),
      }
      this.otherList.push(newItem)
    },

    // 删除其他费用项目
    delOtherItem() {
      if (this.selectedOther) {
        const index = this.otherList.findIndex(
          (item) => item.vid === this.selectedOther.vid,
        )
        if (index > -1) {
          this.otherList.splice(index, 1)
        }
        this.selectedOther = null
      }
    },

    // 创建备件项目
    createSpareItem() {
      const newItem = {
        componentName: '',
        componentNo: '',
        drawingSerialNumber: '',
        serialNumber: '',
        realPrice: 0,
        finalNum: 0,
        finalTotal: 0,
        price: 0,
        quotNum: 0,
        total: 0,
        remark: '',
        model: 2,
        orderId: this.detail.id,
        vid: Date.now() + Math.random(),
      }
      this.spareList.push(newItem)
    },

    // 删除备件项目
    delSpareItem() {
      if (this.selectedSpare) {
        const index = this.spareList.findIndex(
          (item) => item.vid === this.selectedSpare.vid,
        )
        if (index > -1) {
          this.spareList.splice(index, 1)
        }
        this.selectedSpare = null
      }
    },

    // 创建物料项目
    createMaterialItem() {
      const newItem = {
        itemName: '',
        code: '',
        realPrice: 0,
        finalNum: 0,
        finalTotal: 0,
        price: 0,
        quotNum: 0,
        total: 0,
        remark: '',
        model: 3,
        orderId: this.detail.id,
        vid: Date.now() + Math.random(),
      }
      this.materialList.push(newItem)
    },

    // 删除物料项目
    delMaterialItem() {
      if (this.selectedMaterial) {
        const index = this.materialList.findIndex(
          (item) => item.vid === this.selectedMaterial.vid,
        )
        if (index > -1) {
          this.materialList.splice(index, 1)
        }
        this.selectedMaterial = null
      }
    },

    // 备件名称验证规则
    spareNameRules(item) {
      return [
        () => {
          if (Number(item.finalNum) > 0 && !item.componentName) {
            return '数量不为0时，备件名称为必填项'
          }
          return true
        },
      ]
    },

    // 备件编号验证规则
    spareNoRules(item) {
      return [
        () => {
          if (Number(item.finalNum) > 0 && !item.componentNo) {
            return '数量不为0时，备件编号为必填项'
          }
          return true
        },
      ]
    },

    // 图纸号验证规则
    drawingNoRules(item) {
      return [
        () => {
          if (Number(item.finalNum) > 0 && !item.drawingSerialNumber) {
            return '数量不为0时，图纸号为必填项'
          }
          return true
        },
      ]
    },

    // 备件单价验证规则
    sparePriceRules(item) {
      return [
        () => {
          if (
            Number(item.finalNum) > 0 &&
            (!item.realPrice || Number(item.realPrice) <= 0)
          ) {
            return '数量不为0时，单价为必填项'
          }
          return true
        },
      ]
    },

    // 物料名称验证规则
    materialNameRules(item) {
      return [
        () => {
          if (Number(item.finalNum) > 0 && !item.itemName) {
            return '数量不为0时，物料名称为必填项'
          }
          return true
        },
      ]
    },

    // 物料编码验证规则
    materialCodeRules(item) {
      return [
        () => {
          if (Number(item.finalNum) > 0 && !item.code) {
            return '数量不为0时，物料编码为必填项'
          }
          return true
        },
      ]
    },

    // 物料单价验证规则
    materialPriceRules(item) {
      return [
        () => {
          if (
            Number(item.finalNum) > 0 &&
            (!item.realPrice || Number(item.realPrice) <= 0)
          ) {
            return '数量不为0时，单价为必填项'
          }
          return true
        },
      ]
    },
  },

  mounted() {
    this.loadDetail()
    this.loadCom()
    this.loadComplete()
  },
}
</script>

<style>
.thick-border {
  border-width: 2px !important;
}
</style>
