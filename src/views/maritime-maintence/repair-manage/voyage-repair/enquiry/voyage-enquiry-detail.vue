<template>
  <v-container fluid>
    <v-detail-view
      :title="`
      航修询价-${isEdit ? detail.enquiryNo : '新增'}`"
      :tooltip="isEdit ? detail.enquiryNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="detail.auditParams && detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['航修询价:编辑']"
      :can-save="false"
    >
      <template v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          v-if="!isEdit"
          width="90"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          v-permission="['航修询价:发起询价']"
        >
          发起询价
        </v-btn>
        <v-btn
          v-if="
            (detail.businessStatus == '超期' ||
              detail.businessStatus == '报价完成' ||
              detail.status == 4 ||
              detail.businessStatus == '重新定标') &&
            detail.repairType == 0
          "
          tile
          color="primary"
          small
          class="mx-1"
          @click="quoteBid"
          :loading="quoteLoading"
          v-permission="['航修询价:定标（机务）']"
        >
          <v-icon left>mdi-check</v-icon>
          定标
        </v-btn>
        <v-btn
          v-if="
            (detail.businessStatus == '超期' ||
              detail.businessStatus == '报价完成' ||
              detail.status == 4 ||
              detail.businessStatus == '重新定标') &&
            detail.repairType == 1
          "
          tile
          color="primary"
          small
          class="mx-1"
          @click="quoteBid"
          :loading="quoteLoading"
          v-permission="['航修询价:定标（通导）']"
        >
          <v-icon left>mdi-check</v-icon>
          定标
        </v-btn>
        <v-btn
          v-if="
            (detail.businessStatus == '报价完成' ||
              detail.businessStatus == '超期' ||
              detail.businessStatus == '重新定标') &&
            detail.repairType == 0
          "
          tile
          color="error"
          small
          class="mx-1"
          @click="returnJw"
          :loading="quoteLoading"
          v-permission="['航修询价:退回重新询价(机务)']"
        >
          <v-icon left>mdi-check</v-icon>
          退回重新询价
        </v-btn>
        <v-btn
          v-if="
            (detail.businessStatus == '报价完成' ||
              detail.businessStatus == '超期' ||
              detail.businessStatus == '重新定标') &&
            detail.repairType == 1
          "
          tile
          color="error"
          small
          class="mx-1"
          @click="returnJw"
          :loading="quoteLoading"
          v-permission="['航修询价:退回重新询价(通导)']"
        >
          <v-icon left>mdi-check</v-icon>
          退回重新询价
        </v-btn>
        <v-btn
          v-if="
            (detail.businessStatus == '超期无报价' && detail.repairType == 0) ||
            (detail.businessStatus == '机务主管退回' && detail.repairType == 0)
          "
          tile
          color="primary"
          small
          class="mx-1"
          @click="resave"
          :loading="quoteLoading"
          v-permission="['航修询价:重新询价(机务)']"
        >
          <v-icon left>mdi-check</v-icon>
          重新发起询价(机务)
        </v-btn>
        <v-btn
          v-if="
            (detail.businessStatus == '超期无报价' && detail.repairType == 1) ||
            (detail.businessStatus == '通导信息主管退回' &&
              detail.repairType == 1)
          "
          tile
          color="primary"
          small
          class="mx-1"
          @click="resave"
          :loading="quoteLoading"
          v-permission="['航修询价:重新询价(通导)']"
        >
          <v-icon left>mdi-check</v-icon>
          重新发起询价(通导)
        </v-btn>
        <v-btn
          v-if="detail.pdfAttachment"
          :href="`/api/system/file/download?fileName=${encodeURIComponent(
            detail.pdfAttachment.name,
          )}&filePath=${detail.pdfAttachment.filePath}`"
          width="90"
          tile
          color="success"
          small
          class="mx-1"
        >
          <v-icon>mdi-download</v-icon>
          PDF
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-dialog-select
                  max-width="1300"
                  label="申请单"
                  :req-url="reqUrl"
                  item-text="applyNo"
                  item-value="id"
                  :headers="headers"
                  :search-remain="searchObj"
                  @select="selectApply"
                  v-model="detail.applyId"
                  :rules="[rules.required]"
                  :init-selected="initApply"
                  :readonly="isEdit"
                >
                  <template #searchflieds>
                    <v-col cols="3">
                      <v-select
                        label="业务状态"
                        dense
                        outlined
                        clearable
                        v-model="searchObj.businessStatus"
                        :items="businessStatuses"
                      ></v-select>
                    </v-col>
                  </template>
                  <template v-slot:[`item.applyPurpose`]="{ item }">
                    {{ item.applyPurpose.split('#:#')[0] }}
                  </template>
                </v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  :disabled="detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <port-select-dialog
                  :readonly="isEdit"
                  v-model="detail.portId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                  label="修理港口"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  outlined
                  label="询价日期"
                  v-model="detail.enquiryDate"
                  :rules="[rules.required]"
                  disabled
                  use-today
                ></vs-date-picker>
              </v-col>
              <v-col
                v-if="
                  isEdit &&
                  detail.businessStatus != '超期无报价' &&
                  detail.businessStatus != '通导信息主管退回' &&
                  detail.businessStatus != '机务主管退回'
                "
                cols="12"
                md="3"
              >
                <vs-date-picker
                  dense
                  outlined
                  label="报价起始时间"
                  v-model="detail.startTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col
                v-if="
                  isEdit &&
                  detail.businessStatus != '超期无报价' &&
                  detail.businessStatus != '通导信息主管退回' &&
                  detail.businessStatus != '机务主管退回'
                "
                cols="12"
                md="3"
              >
                <vs-date-picker
                  dense
                  outlined
                  label="报价截至时间"
                  v-model="detail.endTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col
                v-if="
                  (isEdit && detail.businessStatus == '超期无报价') ||
                  (isEdit && detail.businessStatus == '机务主管退回') ||
                  (isEdit && detail.businessStatus == '通导信息主管退回')
                "
                cols="12"
                md="3"
              >
                <vs-date-picker
                  dense
                  outlined
                  label="原询价单报价起始时间"
                  v-model="detail.startTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col
                v-if="
                  (isEdit && detail.businessStatus == '超期无报价') ||
                  (isEdit && detail.businessStatus == '机务主管退回') ||
                  (isEdit && detail.businessStatus == '通导信息主管退回')
                "
                cols="12"
                md="3"
              >
                <vs-date-picker
                  dense
                  outlined
                  label="原询价单报价截至时间"
                  v-model="detail.endTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  outlined
                  label="修理类型"
                  dense
                  v-model="detail.repairType"
                  :readonly="true"
                  :items="repairTypeOptions"
                  item-text="text"
                  item-value="value"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.handlerId"
                  use-current
                  :rules="[rules.required]"
                  label="询价人"
                  :init-user="initHandler"
                  :readonly="isEdit"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3" v-if="isEdit">
                <v-text-field
                  v-if="!isJPY"
                  @change="
                    () =>
                      (detail.otherCost = (
                        Math.round(detail.otherCost * 100) / 100
                      ).toFixed(2))
                  "
                  label="额外预估费用(中标报价单同样币种)"
                  dense
                  outlined
                  :readonly="
                    !(
                      (detail.businessStatus == '报价完成' ||
                        detail.businessStatus == '超期' ||
                        detail.businessStatus == '重新定标' ||
                        detail.status == '4') &&
                      isSelected
                    )
                  "
                  type="number"
                  v-model="detail.otherCost"
                ></v-text-field>
                <v-text-field
                  v-if="isJPY"
                  @change="
                    () => (detail.otherCost = Math.round(detail.otherCost))
                  "
                  label="额外预估费用(中标报价单同样币种)"
                  dense
                  outlined
                  :readonly="
                    !(
                      (detail.businessStatus == '报价完成' ||
                        detail.businessStatus == '超期' ||
                        detail.businessStatus == '重新定标' ||
                        detail.status == '4') &&
                      isSelected
                    )
                  "
                  type="number"
                  v-model="detail.otherCost"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="isEdit">
                <v-text-field
                  label="额外预估费用(美金)"
                  dense
                  outlined
                  :readonly="true"
                  type="number"
                  v-model="otherUsd"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="canEdit && isEdit">
                <v-text-field
                  label="中标项目折算美金合计"
                  dense
                  outlined
                  :readonly="true"
                  v-model="detail.total"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!canEdit && isEdit">
                <v-text-field
                  label="中标项目折算美金合计"
                  dense
                  outlined
                  :readonly="true"
                  v-model="total"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="canEdit && isEdit">
                <v-text-field
                  label="总价折算美金合计"
                  dense
                  outlined
                  :readonly="true"
                  v-model="detail.totalAdd"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!canEdit && isEdit">
                <v-text-field
                  label="总价折算美金合计"
                  dense
                  outlined
                  :readonly="true"
                  v-model="totalAdd"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="isEdit">
                <vs-date-picker
                  dense
                  outlined
                  label="预计维修日期"
                  v-model="detail.arriveDate"
                  :readonly="
                    detail.businessStatus != '报价完成' &&
                    detail.businessStatus != '重新定标'
                  "
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-card outlined>
                  <v-card-title>船端申请说明：</v-card-title>
                  <v-col cols="12">
                    <b>航修项目/repair project:</b>
                    {{ detail.applyPurpose.split('#:#')[0] }}
                  </v-col>
                  <v-col cols="12">
                    <b>故障诊断/trouble shooting:</b>
                    {{ detail.applyPurpose.split('#:#')[1] }}
                  </v-col>
                  <v-col cols="12">
                    <b>所需岸基支持/land support:</b>
                    {{ detail.applyPurpose.split('#:#')[2] }}
                  </v-col>
                  <v-col cols="12" v-if="canSelect1">
                    <b>设备主体/Equipment body:</b>
                    {{ detail.equipmentName }}
                    <b>设备型号/Equipment Model:</b>
                    {{ detail.equimentModel }}
                    <b>序列号/Serial Number:</b>
                    {{ detail.equipmentNumber }}
                    <b>生产厂家/Manufacturer:</b>
                    {{ detail.manufacture }}
                  </v-col>
                </v-card>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="项目和费用说明"
                  dense
                  outlined
                  v-model="detail.remark"
                  :readonly="
                    !(
                      detail.businessStatus == '超期' ||
                      detail.businessStatus == '待询价' ||
                      detail.businessStatus == '重新定标' ||
                      idIsNew ||
                      detail.status == '4'
                    )
                  "
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
          <v-row
            v-if="
              !isEdit ||
              detail.businessStatus === '超期无报价' ||
              detail.businessStatus === '机务主管退回' ||
              detail.businessStatus === '通导信息主管退回'
            "
          >
            <v-col cols="12">
              <enquiry-form
                ref="enquiry"
                @close="formShow = false"
                :shipCode="detail.shipCode"
                :happenDate="detail.enquiryDate"
                :businessType="'航修'"
              ></enquiry-form>
            </v-col>
          </v-row>
        </v-container>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            title="申请单附件列表"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-if="canBid" #报价详情按钮></template>
      <template #报价详情 v-if="isEdit">
        <v-table-list
          v-if="
            !(
              detail.businessStatus == '询价中' ||
              detail.businessStatus == '重新报价' ||
              detail.businessStatus == '超期无报价'
            )
          "
          v-model="selectedQuote"
          :headers="quoteHeaders"
          :items="quoteList"
        >
          <template v-slot:[`item.toUsd`]="{ item }">
            {{

                ((Number(item.otherExpense) + Number(item.finalPrice)) *
                  getRate(item.ccyCode)).toFixed(2),

            }}
          </template>

          <template v-slot:[`item.finalPrice`]="{ item }">
            <v-text-field
              v-if="item.ccyCode == 'JPY'"
              v-model="item.finalPrice"
              label="修理费成交价"
              type="number"
              :readonly="canEdit"
              :disabled="
                !(
                  detail.businessStatus === '报价完成' ||
                  detail.businessStatus == '超期' ||
                  detail.businessStatus == '重新定标' ||
                  detail.status === '4'
                )
              "
              single-line
              dense
            ></v-text-field>
            <v-text-field
              v-else
              v-model="item.finalPrice"
              label="修理费成交价"
              type="number"
              :readonly="canEdit"
              :disabled="
                !(
                  detail.businessStatus === '报价完成' ||
                  detail.businessStatus == '超期' ||
                  detail.businessStatus == '重新定标' ||
                  detail.status === '4'
                )
              "
              single-line
              dense
            ></v-text-field>
          </template>
          <template v-slot:[`item.businessStatus`]="{ item }">
            {{ item.businessStatus }}
            <v-btn
              v-if="detail.businessStatus == '报价完成'"
              width="90"
              tile
              color="error"
              small
              class="mx-1"
              @click="reBid(item)"
              v-permission="['航修询价:重新报价']"
            >
              重新报价
            </v-btn>
          </template>
          <template v-slot:[`item.isWins`]="{ item }">
            <v-switch
              v-model="item.isWins"
              dense
              color="success"
              :readonly="canEdit && detail.businessStatus != '重新定标'"
              @change="trackingStateChanged($event, item.id)"
            ></v-switch>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <v-dialog
      v-model="returnDialog"
      max-width="600"
      hide-overlay
      attach="#mask"
    >
      <v-card>
        <v-card-title class="text-h5">退回原因</v-card-title>
        <v-card-text>
          <v-form ref="returnForm">
            <v-textarea
              v-model="returnReason"
              label="请输入退回原因"
              outlined
              :rules="[rules.required]"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" text @click="returnDialog = false">取消</v-btn>
          <v-btn
            color="primary"
            text
            @click="confirmReturn"
            :loading="quoteLoading"
          >
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="quoteBidDialog"
      max-width="600"
      hide-overlay
      attach="#mask"
    >
      <v-card>
        <v-card-title class="text-h5">航修定标</v-card-title>
        <v-card-text>
          <v-form ref="returnForm">
            <v-textarea
              v-model="costRemark"
              label="请输入费用说明"
              outlined
              :rules="[rules.required]"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" text @click="quoteBidDialog = false">取消</v-btn>
          <v-btn
            color="primary"
            text
            @click="confirmquoteBid"
            :loading="quoteLoading"
          >
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="delayDialog" max-width="500" hide-overlay attach="#mask">
      <v-card>
        <v-card-title>重新报价</v-card-title>
        <v-card-text>
          <v-form ref="delayForm">
            <end-time-picker
              v-model="newEndTime"
              label="新的报价截止时间"
              :rules="[rules.required]"
            ></end-time-picker>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" text @click="delayDialog = false">取消</v-btn>
          <v-btn color="primary" text @click="confirmReBid()">确定</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import PortSelectDialog from '@/views/maritime-maintence/components/port-select-dialog.vue'
import currencyHelper from '@/mixin/currencyHelper'
import EnquiryForm from '@/views/maritime-maintence/components/enquiry/enquiry-form.vue'
import routerControl from '@/mixin/routerControl'
import VAttachList from '@/components/v-attach-list.vue'
import EndTimePicker from '@/views/maritime-maintence/components/enquiry/end-time-picker.vue'
export default {
  components: {
    EndTimePicker,
    VAttachList,
    PortSelectDialog,
    EnquiryForm,
  },
  name: 'voyage-enquiry-detail',
  mixins: [currencyHelper, routerControl],
  created() {
    this.backRouteName = 'voyage-enquiry-list'
    this.subtitles = ['基本信息', '报价详情']
    this.reqUrl = '/business/shipAffairs/voyageRepair/applyPage'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '申请单号', value: 'applyNo' },
      { text: '申请日期', value: 'applyDate' },
      { text: '申请人', value: 'applicantNickName' },
      { text: '申请部门', value: 'dept' },
      { text: '修理项目', value: 'applyPurpose' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.quoteHeaders = [
      { text: '报价单号', value: 'quoteNo' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '报价开始时间', value: 'startTime' },
      { text: '截止时间', value: 'endTime' },
      { text: '备货天数', value: 'stockUpDays' },
      { text: '修理费用', value: 'repairExpense' },
      { text: '修理费成交价', value: 'finalPrice' },
      { text: '其他费用', value: 'otherExpense' },
      { text: '折算美元合计', value: 'toUsd' },
      { text: '币种', value: 'ccyCode' },
      { text: '报价单状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
      { text: '是否中标', value: 'isWins' },
    ]
    this.businessStatuses = ['已询价', '审批通过']
  },
  computed: {
    canSelect1() {
      if (!this.detail.equipmentName) {
        return false
      }
      return true
    },
    otherUsd() {
      const quoteWins = this.quoteList.filter((detail) => detail.isWins)
      if (quoteWins.length === 0) {
        return this.detail.otherCost || 0
      }
      const selectedQuote = quoteWins[0]
      const rate = this.getRate(selectedQuote.ccyCode)
      return (Number(this.detail.otherCost || 0) * rate).toFixed(2)
    },
    total() {
      // 获取中标的报价单
      const quoteWins = this.quoteList.filter((detail) => detail.isWins)
      if (quoteWins.length === 0) {
        return this.detail.otherCost || 0
      }
      // 获取第一个中标报价单
      const selectedQuote = quoteWins[0]
      // 修理费成交价
      const finalPrice = Number(selectedQuote.finalPrice) || 0
      // 其他费用
      const otherExpense = Number(selectedQuote.otherExpense) || 0
      // 汇率换算
      const rate = this.getRate(selectedQuote.ccyCode)
      // 计算总价
      return ((finalPrice + otherExpense) * rate).toFixed(2) // 保留两位小数
    },
    totalAdd() {
      const quoteWins = this.quoteList.filter((detail) => detail.isWins)
      if (quoteWins.length === 0) {
        return this.detail.otherCost || 0
      }
      const selectedQuote = quoteWins[0]
      const finalPrice = Number(selectedQuote.finalPrice) || 0
      const otherExpense = Number(selectedQuote.otherExpense) || 0
      const rate = this.getRate(selectedQuote.ccyCode)
      return (
        (Number(this.detail.otherCost || 0) + (finalPrice + otherExpense)) *
        rate
      ).toFixed(2)
    },
    isJPY() {
      const quoteWins = []
      this.quoteList.forEach((detail) => {
        if (detail.isWins) {
          quoteWins.push(detail)
        }
      })
      if (quoteWins.length == 0) return false
      return quoteWins[0].ccyCode === 'JPY' || quoteWins[0].ccyCode === '日元'
    },
    isSelected() {
      const quoteWins = []
      this.quoteList.forEach((detail) => {
        if (detail.isWins) {
          quoteWins.push(detail)
        }
      })
      return !(quoteWins.length === 0)
    },
    canBid() {
      return (
        // ['超期', '报价完成'].includes(this.detail.businessStatus) &&
        // this.detail.status === '1'
        this.detail.businessStatus == '超期' ||
        this.detail.businessStatus == '报价完成' ||
        this.detail.status == 4
      )
    },
    isEdit() {
      return !(
        this.$route.params.id === 'new' ||
        this.detail.businessStatus == '待询价'
      )
    },
    idIsNew() {
      return this.$route.params.id == 'new'
    },
  },
  data() {
    return {
      detail: {
        attachmentIds: [],
        attachmentRecords: [],
        applyPurpose: '',
        repairType: null,
        needSendOa: 0,
        remark: '',
      },
      repairTypeOptions: [
        { text: '机务项目', value: null },
        { text: '机务项目', value: 0 },
        { text: '通导项目', value: 1 },
      ],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      searchObj: {
        shipCode: '',
        status: 3,
        businessStatus: '审批通过',
        isEnd: false,
      },
      initPort: {},
      initHandler: false,
      quoteList: [],
      selectedQuote: false,
      initUser: false,
      initApply: {},
      applyReason: '',
      quoteLoading: false,
      canEdit: true,
      returnDialog: false,
      quoteBidDialog: false,
      returnReason: '',
      delayDialog: false,
      newEndTime: '',
      currentSup: null,
    }
  },

  watch: {
    'detail.shipCode'(val) {
      if (val) {
        this.searchObj.shipCode = val
      }
    },
    'detail.id'(val) {
      if (val) {
        this.loadQuoteList(val)
      }
    },
    'detail.businessStatus'(val) {
      if (
        val === '报价完成' ||
        val == '超期' ||
        val == '重新定标' ||
        val == '填写申请单'
      ) {
        this.canEdit = false
      }
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },

    async loadDetail() {
      if (!this.isEdit && this.detail.endtime) return
      if (this.$route.params.id === 'new') return
      const { data } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/getEnquiryDetailById',
        {
          id: this.$route.params.id,
        },
      )
      this.detail = {
        ...data,
        repairType:
          data.repairType !== undefined ? Number(data.repairType) : null,
      }
      this.detail.shipCode = data.shipInfo.shipCode
      this.initPort = {
        id: data.portId,
        portCn: data.portName,
      }
      this.initHandler = {
        id: data.handlerId,
        nickName: data.handlerName,
      }
      this.initApply = {
        id: data.applyId,
        applyNo: data.applyNo,
      }
      if (this.detail.businessStatus == '待询价') {
        this.detail.remark = '项目说明：' + `${this.detail.remark || ''}`
      } else {
        this.detail.remark = this.detail.remark || ''
      }
    },

    async loadQuoteList(id) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/quotePage',
        {
          enquiryId: id,
          //isReBid: false,
        },
      )
      console.log(data.records)
      console.log(this.detail.status)
      if (this.detail.status == '1') {
        const filteredList = data.records.filter(
          (item) =>
            String(item.businessStatus) === '已填报' ||
            String(item.businessStatus) === '中标' ||
            String(item.businessStatus) === '不中标',
        )
        this.quoteList = filteredList
        console.log(filteredList)
      } else {
        this.quoteList = data.records
      }
    },

    async save(goBack, notMove = false) {
      if (this.detail.remark == null) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (!this.isEdit) {
        const voyageRepairEnquirySubmitInputDTO =
          await this.$refs.enquiry.save()
        if (!voyageRepairEnquirySubmitInputDTO) return
        const { data } = await this.postAsync(
          '/business/shipAffairs/voyageRepair/enquirySubmit',
          {
            voyageRepairEnquiryModifyDTO: this.detail,
            voyageRepairEnquirySubmitInputDTO,
          },
        )
        if (!data) return false
      } else {
        const { data } = await this.postAsync(
          '/business/shipAffairs/voyageRepair/saveOrUpdateEnquiry',
          this.detail,
        )
        if (!data) return false
        if (notMove) return true
      }
      this.closeAndTo(this.backRouteName)
    },
    async resave(goBack, notMove = false) {
      const voyageRepairEnquirySubmitInputDTO = await this.$refs.enquiry.save()
      if (!voyageRepairEnquirySubmitInputDTO) return
      const { data } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/resaveOrUpdateEnquiry',
        {
          voyageRepairEnquiryModifyDTO: this.detail,
          voyageRepairEnquirySubmitInputDTO,
        },
      )
      if (!data) return false
      if (notMove) return true
      this.closeAndTo(this.backRouteName)
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const res = await this.save(goBack, true)
      if (!res) return
      const error = await this.$refs.audit.submit()
      if (!error) goBack()
    },

    async quoteBid() {
      const quoteWins = []
      this.quoteList.forEach((detail) => {
        if (detail.isWins) {
          quoteWins.push(detail)
        }
      })
      if (quoteWins.length == 0) {
        this.$dialog.message.error('请勾选中标供应商')
        return
      }
      if (this.detail.remark == null) {
        this.$dialog.message.error('请填写项目说明')
        return
      }
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明')
        return
      }
      if (
        this.detail.arriveDate == null &&
        this.detail.businessStatus == '报价完成'
      ) {
        this.$dialog.message.error('请选择预计维修日期')
        return
      }
      if (this.detail.otherCost === 0 || this.detail.otherCost == null) {
        this.$dialog.message.error('额外预估费用不能为0')
        return
      }
      this.quoteBidDialog = true
    },
    async confirmquoteBid() {
      this.quoteBidDialog = false
      const quoteWins = []
      this.quoteList.forEach((detail) => {
        if (detail.isWins) {
          quoteWins.push(detail)
        }
      })
      this.detail.remark = this.detail.remark + `\n费用说明：${this.costRemark}`
      this.quoteLoading = true
      //    增加保存修理费成交价
      const { data } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/voyageEnquiryUpdateFinalPrice',
        this.quoteList,
      )
      if (data) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/voyageRepair/openBid',
          // { enquiryId: this.$route.params.id, quoteId: this.selectedQuote.id },
          {
            enquiryId: this.$route.params.id,
            quoteId: quoteWins[0].id,
            remark: this.detail.remark,
            otherCost: this.detail.otherCost,
            attachmentIds: this.detail.attachmentIds,
            arriveDate: this.detail.arriveDate,
          },
        )
        this.quoteLoading = false
        if (!errorRaw) {
          this.closeAndTo(this.backRouteName)
        }
      }
    },

    // 商务开标
    async quoteBid1() {
      const quoteWins = []
      this.quoteList.forEach((detail) => {
        if (detail.isWins) {
          quoteWins.push(detail)
        }
      })
      if (quoteWins.length == 0) {
        this.$dialog.message.error('请勾选中标供应商')
        return
      }
      if (this.detail.remark == null) {
        this.$dialog.message.error('请填写项目说明')
        return
      }
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明')
        return
      }
      if (this.detail.otherCost === 0 || this.detail.otherCost == null) {
        /*const confirmResult = await this.$dialog.msgbox.confirm(
          '额外预估费用为0,是否继续提交？',
        )
        if (!confirmResult) return*/
        this.$dialog.message.error('额外预估费用不能为0，提交失败')
        return
      }
      if (
        this.detail.arriveDate == null &&
        this.detail.businessStatus == '报价完成'
      ) {
        this.$dialog.message.error('请选择预计修理日期')
        return
      }
      this.quoteLoading = true
      //    增加保存修理费成交价
      const { data } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/voyageEnquiryUpdateFinalPrice',
        this.quoteList,
      )
      if (data) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/voyageRepair/openBid',
          // { enquiryId: this.$route.params.id, quoteId: this.selectedQuote.id },
          {
            enquiryId: this.$route.params.id,
            quoteId: quoteWins[0].id,
            remark: this.detail.remark,
            otherCost: this.detail.otherCost,
            attachmentIds: this.detail.attachmentIds,
            arriveDate: this.detail.arriveDate,
          },
        )
        this.quoteLoading = false
        if (!errorRaw) {
          // await this.loadDetail()
          // await this.loadQuoteList(this.$route.params.id)
          this.closeAndTo(this.backRouteName)
        }
      }
    },

    selectApply(val) {
      this.initPort = {
        id: val.portId,
        portCn: val.portName,
      }
      this.detail.portId = val.portId
      this.applyReason = val.applyPurpose
      this.detail.needSendOa = val.needSendOa
      this.detail.shipCode = val.shipInfo.shipCode
      this.detail.repairType = val.repairType
    },

    getUsd(val, ccyCode) {
      return (
        this.currencyInfo?.find((item) => item.ccyCode === ccyCode)
          ?.rateToMain || 1 * val
      )
    },
    getRate(ccyCode) {
      return (
        this.currencyInfo?.find((item) => item.ccyCode === ccyCode)
          ?.rateToMain || 1
      )
    },
    // 重新报价
    async reBid(item) {
      this.currentSup = item
      this.delayDialog = true
    },
    async confirmReBid() {
      this.delayDialog = false
      try {
        // 先调用延期接口
        const { errorRaw: delayError } = await this.getAsync(
          '/business/shipAffairs/voyageRepair/delayPurchaseEnquiry',
          {
            enquiryId: this.currentSup.enquiryId,
            delayDate: this.newEndTime,
          },
        )

        if (delayError) {
          this.$dialog.message.error('延期失败')
          return
        }

        // 再调用重新报价接口
        const { errorRaw: reBidError } = await this.postAsync(
          '/business/shipAffairs/voyageRepair/reBid',
          {
            ...this.currentSup,
          },
        )

        if (!reBidError) {
          this.$dialog.message.success('已发送邮箱提醒供应商重新报价！')
          this.newEndTime = ''
          this.currentSup = null
          this.closeAndTo(this.backRouteName)
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$dialog.message.error('操作失败')
      }
    },
    async returnJw() {
      this.returnDialog = true
    },
    async confirmReturn() {
      this.detail.remark =
        this.detail.remark + `\n退回原因：${this.returnReason}`
      this.returnDialog = false
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/voyageEnquiryReturnSw',
        {
          id: this.detail.id,
          remark: this.detail.remark,
        },
      )
      this.quoteLoading = false
      if (!errorRaw) {
        this.$dialog.message.success('退回成功！')
        this.closeAndTo(this.backRouteName)
      }
    },
    trackingStateChanged(event, id) {
      if (event) {
        this.detail.otherCost = 0
        this.quoteList.forEach((detail) => {
          if (detail.id !== id) {
            detail.isWins = !event
          }
        })
      }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
