<template>
  <v-container fluid>
    <enquiry-form
      v-if="selected && formShow"
      @close="formShow = false"
      :req-url="'/business/shipAffairs/voyageRepair/enquirySubmit'"
      :enquiry="selected || { shipInfo: {} }"
      @success="loadTableData"
    ></enquiry-form>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="status2"
            :items="statusMap"
            label="审批状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            :items="businessStatusMap"
            label="业务状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <!--        <v-col cols="12" md="2">
          <v-select
            v-model="searchObj.dept"
            label="申请部门"
            outlined
            dense
            :items="['甲板部', '轮机部']"
          ></v-select>
        </v-col>-->
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          v-if="false"
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'voyage-enquiry-detail', params: { id: 'new' } }"
          v-permission="['航修询价:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!-- <v-btn
          :disabled="
            selected.businessStatus != '审批通过,发送OA失败' &&
            selected.businessStatus != '审批通过,待发送OA立项'
          "
          outlined
          tile
          color="primary"
          class="mx-1"
          @click.stop="sendOA"
          v-permission="['坞修询价:发送OA']"
        >
          <v-icon left>mdi-send</v-icon>
          发送OA
        </v-btn> -->
        <!-- <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :disabled="selected.businessStatus !== '未提交'"
          @click="formShow = true"
          v-permission="['航修询价:发起询价']"
        >
          <v-icon left>mdi-comment-question-outline</v-icon>
          发起询价
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :disabled="
            selected.businessStatus !== '询价中' &&
            selected.businessStatus !== '重新报价' &&
            selected.businessStatus !== '超期' &&
            selected.businessStatus !== '超期无报价'
          "
          @click="dialogDelay = true"
          v-permission="['航修询价:延期']"
        >
          <v-icon left>mdi-timer-plus-outline</v-icon>
          延期
        </v-btn>
        <v-btn
          :disabled="selected.businessStatus !== '未提交'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delEnquiry"
          v-permission="['航修询价:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="
            selected.businessStatus !== '未提交' &&
            selected.businessStatus !== '超期' &&
            selected.businessStatus !== '报价完成' &&
            selected.businessStatus !== '超期无报价' &&
            selected.businessStatus !== '询价中' &&
            selected.businessStatus !== '用户开标'
          "
          outlined
          tile
          color="error"
          class="mx-1"
          @click="disuse"
          v-permission="['航修询价:废弃']"
        >
          <v-icon left>mdi-cancel</v-icon>
          废弃
        </v-btn>
      </template>
      <template v-slot:[`item.isDockRepair`]="{ item }">
        {{ item.isDockRepair ? '是' : '否' }}
      </template>
      <template v-slot:[`item.applyPurpose`]="{ item }">
        {{ item.applyPurpose.split('#:#')[0] }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses2[item.status] }}
        </v-chip>
      </template>
    </v-table-searchable>
    <delay-dialog
      v-model="dialogDelay"
      :enquiryId="selected.id"
      type="voyage"
      @success="loadTableData"
    ></delay-dialog>
  </v-container>
</template>
<script>
import enquiryForm from '@/views/maritime-maintence/components/enquiry/enquiry-form.vue'
import delayDialog from '@/views/maritime-maintence/components/enquiry/delay-dialog.vue'
// applicantId	申请人id	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// applyPurpose	申请目的	string
// businessStatus	业务状态	string
// dept	申请部门	string
// enquiryNo	询价单号	string
// id	物理主键	string
// remark	备注	string
// shipInfo	船舶编码	ShipInfoDO	ShipInfoDO
// status	流程状态	string
export default {
  components: { enquiryForm, delayDialog },
  name: 'voyage-enquiry-list',
  created() {
    this.tableName = '航修询价'
    this.reqUrl = '/business/shipAffairs/voyageRepair/enquiryPage'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '询价单号', value: 'enquiryNo' },
      { text: '询价次数', value: 'sortNum' },
      { text: '申请部门', value: 'dept' },
      { text: '申请人', value: 'applicantNickName' },
      { text: '申请人岗位', value: 'applicantPost' },
      { text: '修理项目', value: 'applyPurpose' },
      { text: '询价日期', value: 'enquiryDate' },
      { text: '报价截止日期', value: 'endTime' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊搜索'
    this.pushParams = { name: 'voyage-enquiry-detail' }
    this.searchDate = {
      label: '',
      value: '',
    }
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
      { text: '废弃', value: '5' },
    ]
    this.businessStatusMap = [
      { text: '超期', value: '超期' },
      { text: '待询价', value: '待询价' },
      { text: '未提交', value: '未提交' },
      { text: '询价中', value: '询价中' },
      { text: '报价完成', value: '报价完成' },
      { text: '审批通过', value: '审批通过' },
      { text: '超期无报价', value: '超期无报价' },
      { text: '超期,报价完成,重新定标', value: '超期,报价完成,重新定标' },
      {
        text: '超期无报价,机务主管退回,通导信息主管退回',
        value: '超期无报价,机务主管退回,通导信息主管退回',
      },
    ]
    this.statuses2 = ['暂无审批', '草稿', '审批中', '已审批', '已驳回', '废弃']
    this.statusColors = ['info', '', 'warning', 'success', 'error', 'error']
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: false,
        status: '',
        businessStatus: '',
        repairType: '',
      },
      formShow: false,
      dialogDelay: false,
      status2: '',
      repairType: '1',
    }
  },
  watch: {
    status2(val) {
      this.searchObj.status = val
    },
  },
  methods: {
    async delEnquiry() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/deleteEnquiry',
        { id: this.selected.id },
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async loadTableData() {
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async disuse() {
      if (!(await this.$dialog.msgbox.confirm('确定废弃此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/disusePurchaseEnquiry',
        { enquiryId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`废弃失败，请重试`)
        return
      }
      this.$dialog.message.success(`废弃成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async sendOA() {
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/voyageEnquirySendOAById',
        {
          id: this.selected.id,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('发送成功')
        this.selected = false
        await this.$refs.table.loadTableData()
      }
    },
  },

  mounted() {
    if (this.$route.query.businessStatus != undefined) {
      // console.log(1)
      this.searchObj.businessStatus = this.$route.query.businessStatus
      this.status2 = this.$route.query.status
      this.searchObj.repairType = this.$route.query.repairType
      // 在3秒后执行一次任务
      // setTimeout(() => {
      //   this.searchObj.businessStatus = ''
      //   // console.log(1)
      // }, 1000)
    }
    if (this.$route.query.status != undefined) {
      //this.searchObj.status = this.$route.query.status
      this.status2 = this.$route.query.status
      this.searchObj.isMe = this.$route.query.isMe
    }
  },
}
</script>

<style></style>
