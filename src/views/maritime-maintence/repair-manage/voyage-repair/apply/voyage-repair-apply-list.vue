<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :fuzzy-label="fuzzyLabel"
      :search-remain="searchObj"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="status2"
            :items="statusMap"
            label="审批状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!canCopy"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="copyApplies"
          v-permission="['航修申请:复制']"
        >
          <v-icon left>mdi-content-copy</v-icon>
          复制
        </v-btn>
        <v-btn
          :disabled="!canEnd"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="isItemEnd"
          v-permission="['航修申请:完结']"
        >
          <v-icon left>mdi-close-circle</v-icon>
          完结
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'voyage-repair-apply-detail', params: { id: 'new' } }"
          v-permission="['航修申请:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['航修申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.applyPurpose`]="{ item }">
        {{ item.applyPurpose.split('#:#')[0] }}
      </template>
      <!-- <template v-slot:[`item.isEnd`]="{ item }">
        <v-chip small color="warning" v-if="item.isEnd == false">未完结</v-chip>
        <v-chip small v-if="item.isEnd == true">已完结</v-chip>
      </template> -->
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses2[item.status] }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applicantId	申请人id	string
// applicantPost	申请人岗位	string
// applyDate	申请日期	string
// applyNo	申请单号	string
// applyPurpose	申请目的	string
// businessStatus	业务状态	string
// dept	申请部门	string
// id	物理主键	string
// remark	备注	string
// shipInfo	船舶编码	ShipInfoDO	ShipInfoDO
// status	流程状态	string
export default {
  name: 'voyage-repair-apply-list',
  created() {
    this.tableName = '航修申请'
    this.reqUrl = '/business/shipAffairs/voyageRepair/applyPage'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '申请单号', value: 'applyNo' },
      { text: '申请日期', value: 'applyDate' },
      { text: '申请人', value: 'applicantNickName' },
      { text: '申请部门', value: 'dept' },
      { text: '修理项目', value: 'applyPurpose' },
      // { text: '是否完结', value: 'isEnd' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
      // { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊查询'
    this.searchDate = {
      label: '申请日期',
      value: 'applyDate',
    }
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
      { text: '废弃', value: '5' },
    ]
    this.statuses2 = ['暂无审批', '草稿', '审批中', '已审批', '已驳回', '废弃']
    this.statusColors = ['info', '', 'warning', 'success', 'error', 'error']
    this.pushParams = { name: 'voyage-repair-apply-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: false,
      },
      status2: '',
    }
  },
  watch: {
    status2(val) {
      this.searchObj.status = val
      this.$refs.table.loadTableData()
    },
    isMe(val) {
      this.searchObj.isMe = val
      this.$refs.table.loadTableData()
    },
  },
  computed: {
    canEnd() {
      return this.selected.status == 3 && this.selected.isEnd == false
    },
    canCopy() {
      return this.selected
    },
  },
  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/deleteApply',
        { id: this.selected.id },
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        this.$refs.table.loadTableData()
        this.selected = false
      }
    },
    async isItemEnd() {
      if (!(await this.$dialog.msgbox.confirm('确定完结此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/voyageRepairApplyIsEnd',
        { applyId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`完结失败，请重试`)
        return
      }
      this.$dialog.message.success(`完结成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async copyApplies() {
      if (!(await this.$dialog.msgbox.confirm('确定复制所选申请单？'))) return

      try {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/voyageRepair/voyageRepairApplyCopy',
          { applyId: this.selected.id },
        )
        if (errorRaw) {
          this.$dialog.message.error(
            `复制申请单 ${this.selected.applicationNo} 失败`,
          )
          return
        }
        this.$dialog.message.success('复制申请单成功')
        await this.$refs.table.loadTableData()
        this.selected = []
      } catch (error) {
        console.error('复制失败:', error)
        this.$dialog.message.error('复制失败，请重试')
      }
    },
  },

  mounted() {
    if (this.$route.query.businessStatus != undefined) {
      this.searchObj.businessStatus = this.$route.query.businessStatus
      this.status2 = this.$route.query.status
      this.searchObj.repairType = this.$route.query.repairType
    }
    if (this.$route.query.status != undefined) {
      this.status2 = this.$route.query.status
      this.searchObj.isMe = this.$route.query.isMe
    }
  },
}
</script>

<style></style>
