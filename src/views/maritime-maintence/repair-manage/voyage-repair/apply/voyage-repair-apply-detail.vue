<template>
  <v-container fluid>
    <v-detail-view
      :title="`航修申请-${isEdit ? detail.applyNo : '新增'}`"
      :tooltip="isEdit ? detail.applyNo : '新增'"
      :backRouteName="backRouteName"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['航修申请:编辑']"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template v-if="['2', '3'].includes(detail.status)" v-slot:custombtns>
        <v-btn
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: detail.systemReportId },
          }"
          color="info"
          small
          class="mx-1"
          v-permission="['航修申请:查看部门报表']"
        >
          查看部门报表
        </v-btn>
      </template>
      <v-card-text class="mt-2 pb-0" v-if="detail.auditParams">
        <v-form ref="aform">
          <v-audit
            ref="audit"
            :auditParams="detail.auditParams"
            :shipCode="detail.shipCode"
          ></v-audit>
        </v-form>
      </v-card-text>
      <v-container fluid>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col class="py-0" cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-handler
                  label="申请人"
                  :readonly="isEdit"
                  v-model="detail.applicantId"
                  :init-user="initUser"
                  use-current
                  :rules="[rules.required]"
                ></v-handler>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-ship-dept
                  label="申请部门"
                  v-model="detail.dept"
                  outlined
                  dense
                  :readonly="isEdit"
                  :items="['甲板部', '轮机部']"
                  :rules="[rules.required]"
                ></v-ship-dept>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <vs-date-picker
                  label="申请时间"
                  v-model="detail.applyDate"
                  readonly
                  outlined
                  dense
                  :rules="[rules.required]"
                  use-today
                ></vs-date-picker>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <port-select-dialog2
                  v-model="detail.portId"
                  :initSelected="initPort"
                  :readonly="
                    detail.businessStatus !== '通导信息主管' &&
                    detail.businessStatus !== '机务主管' &&
                    !canEdit
                  "
                  label="修理港口"
                ></port-select-dialog2>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <vs-date-picker
                  :readonly="
                    detail.businessStatus !== '通导信息主管' &&
                    detail.businessStatus !== '机务主管' &&
                    !canEdit
                  "
                  outlined
                  dense
                  v-model="detail.arriveDate"
                  :rules="[rules.required]"
                  label="到港日期"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  outlined
                  label="修理类型"
                  dense
                  v-model="repairType"
                  :rules="[rules.required]"
                  :readonly="!canEdit"
                  :items="['机务项目', '通导项目']"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  outlined
                  label="航修项目"
                  dense
                  v-model="detail.applyPurpose"
                  :rules="[rules.required]"
                  :readonly="!canEdit"
                  :items="currentProjects"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0" v-if="canSelect">
                <v-select
                  outlined
                  label="设备主体"
                  dense
                  v-model="detail.equipmentId"
                  :rules="[rules.required]"
                  :readonly="!canEdit"
                  :items="firstEquipments"
                  clearable
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0" v-if="canSelect">
                <v-text-field
                  outlined
                  dense
                  v-model="computedEquimentModel"
                  label="设备型号"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" class="py-0" v-if="canSelect">
                <v-text-field
                  outlined
                  dense
                  v-model="computedEquipmentNumber"
                  label="序列号"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" class="py-0" v-if="canSelect">
                <v-text-field
                  outlined
                  dense
                  v-model="computedManufacture"
                  label="生产厂家"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" class="py-0" v-if="canSelect1">
                <v-text-field
                  outlined
                  dense
                  v-model="detail.equipmentName"
                  label="设备主体"
                ></v-text-field>
              </v-col>
              <template v-if="detail.equipmentName">
                <v-col cols="12" md="3" class="py-0">
                  <v-text-field
                    outlined
                    dense
                    v-model="detail.equimentModel"
                    label="设备型号"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3" class="py-0">
                  <v-text-field
                    outlined
                    dense
                    v-model="detail.equipmentNumber"
                    label="序列号"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="3" class="py-0">
                  <v-text-field
                    outlined
                    dense
                    v-model="detail.manufacture"
                    label="生产厂家"
                    readonly
                  ></v-text-field>
                </v-col>
              </template>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  label="故障诊断"
                  dense
                  v-model="detail.troubleshooting"
                  :readonly="!canEdit"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  label="所需岸基支持"
                  dense
                  v-model="detail.support"
                  :readonly="!canEdit"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <!-- <v-col cols="12" class="py-0">
                <v-textarea
                  outlined
                  label="备注"
                  dense
                  v-model="detail.remark"
                  :readonly="!canEdit"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col> -->
            </v-row>
          </v-form>
        </v-card-text>
      </v-container>
      <v-attach-list
        :attachments="detail.attachmentRecords"
        @change="changeAttachment"
        :ship-code="detail.shipCode"
        title="申请单附件列表"
      ></v-attach-list>
    </v-detail-view>
  </v-container>
</template>
<script>
import PortSelectDialog2 from '@/views/maritime-maintence/components/port-select-dialog2.vue'
export default {
  name: 'voyage-repair-apply-detail',
  components: {
    PortSelectDialog2,
  },
  created() {
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.backRouteName = 'voyage-repair-apply-list'
    this.subtitles = []
    this.currentProjects = []
    this.机务项目 = [
      '主机及其附属设备\nThe M/E and its accessory devices',
      '副机及其附属设备\nThe A/E and its accessory devices',
      '辅助设备\nAuxiliary equipment',
      '甲板机械\nDeck machinery',
      '机舱管路\nThe pipe of engine room',
      '水下检验\nUnderwater inspection',
      '其他\nOther',
    ]
    this.通导项目 = [
      '通导设备-雷达\nCommunication equipment-Radar',
      '通导设备-海图\nCommunication equipment-Nautical Charts',
      '通导设备-自动舵\nCommunication equipment-Autopilot',
      '通导设备-电罗经\nCommunication equipment-Gyrocompass',
      '通导设备-VDR\nCommunication equipment-VDR',
      '通导设备-计程仪\nCommunication equipment-Taximeter',
      '通导设备-电台\nCommunication equipment-Radio',
      '通导设备-卫通\nCommunication equipment-Satellite Communications',
      '通导设备-VSAT\nCommunication equipment-VSAT',
      '通导设备-可视化\nCommunication equipment-Visualization',
      '通导设备-备件\nCommunication equipment-Spare Parts',
      '通导设备-其他\nCommunication equipment-Other',
    ]
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
    canSelect() {
      if (this.$route.params.id !== 'new') {
        return false
      }
      return (
        [
          '主机及其附属设备\nThe M/E and its accessory devices',
          '副机及其附属设备\nThe A/E and its accessory devices',
          '辅助设备\nAuxiliary equipment',
          '甲板机械\nDeck machinery',
        ].includes(this.detail.applyPurpose) || this.detail.applyPurpose == null
      )
    },
    canSelect1() {
      if (this.$route.params.id == 'new') {
        return false
      }
      if (!this.detail.equipmentName) {
        return false
      }
      return true
    },
    computedEquipmentNumber() {
      const selectedEquipment = this.firstEquipments.find(
        (e) => e.value === this.detail.equipmentId,
      )
      return selectedEquipment ? selectedEquipment.equipmentNumber : ''
    },
    computedManufacture() {
      const selectedEquipment = this.firstEquipments.find(
        (e) => e.value === this.detail.equipmentId,
      )
      return selectedEquipment ? selectedEquipment.manufacture : ''
    },
    computedEquimentModel() {
      const selectedEquipment = this.firstEquipments.find(
        (e) => e.value === this.detail.equipmentId,
      )
      return selectedEquipment ? selectedEquipment.equimentModel : ''
    },
  },

  data() {
    return {
      detail: {
        attachmentIds: [],
        attachmentRecords: [],
        applyPurpose: '',
        needSendOa: 0,
      },
      repairType: '',
      initUser: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      currentProjects: [],
      initPort: {},
      firstEquipments: [],
    }
  },
  watch: {
    repairType(newVal) {
      if (newVal === '机务项目') {
        this.currentProjects = this.机务项目
      } else if (newVal === '通导项目') {
        this.currentProjects = this.通导项目
      } else {
        this.currentProjects = []
      }
    },
    'detail.shipCode': {
      handler() {
        this.loadFirstEquipment()
      },
    },
  },

  methods: {
    async loadFirstEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { current: 1, size: 999, shipCode: this.detail.shipCode },
      )
      const { records } = data
      this.firstEquipments = records?.map((i) => {
        return {
          text: i.equipmentEname,
          value: i.id,
          equipmentNumber: i.equipmentNumber,
          equimentModel: i.equimentModel,
          manufacture: i.manufacture,
        }
      })
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return false
      }
      let repairTypeValue
      if (this.repairType === '机务项目') {
        repairTypeValue = 0
      } else {
        repairTypeValue = 1
      }
      if (
        ![
          '主机及其附属设备\nThe M/E and its accessory devices',
          '副机及其附属设备\nThe A/E and its accessory devices',
          '辅助设备\nAuxiliary equipment',
          '甲板机械\nDeck machinery',
        ].includes(this.detail.applyPurpose)
      ) {
        this.detail.equipmentId = null
      }

      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/saveOrUpdateApply',
        {
          ...this.detail,
          repairType: repairTypeValue,
          applyPurpose: [
            this.detail.applyPurpose,
            this.detail.troubleshooting,
            this.detail.support,
          ].join('#:#'),
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return false
      if (!this.detail.portId) {
        this.$dialog.message.error('修理港口不能为空')
        return
      }
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/voyageRepair/submitApplyById',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },

    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/getApplyDetailById',
        { id: this.$route.params.id },
      )
      const [applyPurpose, troubleshooting, support] =
        data.applyPurpose.split('#:#')
      let repairTypeText = ''
      if (data.repairType === 0) {
        repairTypeText = '机务项目'
      } else if (data.repairType === 1) {
        repairTypeText = '通导项目'
      }
      this.detail = {
        ...data,
        applyPurpose,
        troubleshooting,
        support,
      }
      this.repairType = repairTypeText
      this.detail.shipCode = data.shipInfo.shipCode
      this.initUser = {
        id: data.applicantId,
        nickName: data.applicantNickName,
      }
      this.initPort = {
        id: data.portId,
        portCn: data.portName,
      }
      if (data.equipmentInfo) {
        this.equipmentInfo = data.equipmentInfo
      }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
