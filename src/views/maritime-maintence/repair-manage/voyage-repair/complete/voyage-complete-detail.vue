<template>
  <v-container fluid>
    <v-detail-view
      :title="`航修完工-${isEdit ? detail.completionNo : '新增'}`"
      :tooltip="isEdit ? detail.completionNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        (!detail.auditParams || detail.auditParams.taskId) &&
        this.detail.businessStatus !== '已提交待确认'
      "
      @save="save"
      @submit="submit"
      v-permission="['航修完工单:编辑']"
      :can-save="
        this.detail.status != 2 &&
        this.detail.status != 3 &&
        this.detail.businessStatus !== '已提交待确认'
      "
    >
      <template #custombtns>
        <v-btn
          :loading="loading"
          v-if="
            detail.businessStatus == '已提交待确认' &&
            detail.repairType == '机务项目' &&
            !isReallyRepairExpenseInvalid &&
            !isInvoiceInvalid
          "
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('已确认')"
          v-permission="['航修完工单:主管确认(机务)']"
        >
          主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="
            detail.businessStatus == '已提交待确认' &&
            detail.repairType == '通导项目' &&
            !isReallyRepairExpenseInvalid &&
            !isInvoiceInvalid
          "
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus('完工确认')"
          v-permission="['航修完工单:主管确认(通导)']"
        >
          通导信息主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="
            detail.businessStatus === '已提交待确认' &&
            !isReallyRepairExpenseInvalid &&
            !isInvoiceInvalid
          "
          width="120"
          tile
          color="error"
          small
          class="mx-1"
          @click="changeStatus('完工退回')"
          v-permission="['航修完工单:完工退回']"
        >
          完工退回
        </v-btn>
      </template>
      <template v-if="detail.auditParams && !isSupper" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12">
                航修项目:{{ detail.applyPurpose.split('#:#')[0] }}
              </v-col>
              <v-col cols="12">
                故障诊断:{{ detail.applyPurpose.split('#:#')[1] || '无' }}
              </v-col>
              <v-col cols="12">
                所需岸基支持:{{ detail.applyPurpose.split('#:#')[2] || '无' }}
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dialog-select
                  :readonly="isEdit"
                  max-width="1300"
                  label="修理单"
                  :req-url="reqUrl"
                  item-text="orderNo"
                  item-value="id"
                  :headers="isShip ? repairHeadersShip : repairHeaders"
                  :search-remain="searchObj"
                  :disabled="!detail.shipCode"
                  v-model="detail.orderId"
                  :rules="[rules.required]"
                  :init-selected="initOrder"
                  @select="selectApply"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  :readonly="isEdit1"
                  v-model="detail.completeDate"
                  label="完工日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  dense
                  outlined
                  :readonly="isEdit"
                  v-model="detail.dept"
                  label="申请部门"
                  :items="['甲板部', '轮机部']"
                  :rules="[rules.required]"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  dense
                  outlined
                  :readonly="isEdit"
                  v-model="detail.handlerId"
                  :rules="[rules.required]"
                  label="完工人"
                  :init-user="initHandler"
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3" v-if="isEdit">
                <v-select
                  dense
                  outlined
                  :readonly="isEdit"
                  v-model="detail.repairType"
                  :items="repairTypeOptions"
                  label="修理类型"
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3"></v-col>
              <v-col cols="12" md="3"></v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="原始报价修理费用(折算美金)"
                  dense
                  outlined
                  v-model="detail.repair"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="原始报价其他费用(折算美金)"
                  dense
                  outlined
                  v-model="detail.otherExpense"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="原始报价总费用(折算美金)"
                  dense
                  outlined
                  v-model="toUsd"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3"></v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="实际修理费用(折算美金)"
                  dense
                  outlined
                  v-model="reallyRepairExpense"
                  :readonly="true"
                  :error="isReallyRepairExpenseInvalid"
                  :error-messages="
                    isReallyRepairExpenseInvalid
                      ? '请提醒供应商填写修理单中实际修理费用'
                      : ''
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="实际其他费用(折算美金)"
                  dense
                  outlined
                  v-model="reallyOtherMoney"
                  :readonly="true"
                  :error="isReallyRepairExpenseInvalid"
                  :error-messages="
                    isReallyRepairExpenseInvalid
                      ? '请提醒供应商填写修理单中实际其他费用'
                      : ''
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="实际总金额（折算美金）"
                  dense
                  outlined
                  type="number"
                  v-model="reallyFinalMoney"
                  readonly="true"
                  :error="isReallyRepairExpenseInvalid"
                  :error-messages="
                    isReallyRepairExpenseInvalid
                      ? '请提醒供应商填写修理单中实际总金额'
                      : ''
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3"></v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="发票编号"
                  dense
                  outlined
                  type="string"
                  v-model="detail.invoiceCode"
                  readonly="true"
                  :error="isInvoiceInvalid"
                  :error-messages="
                    isInvoiceInvalid ? '请提醒供应商填写发票编号 ' : ''
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="发票金额"
                  dense
                  outlined
                  type="number"
                  v-model="detail.invoiceMoney"
                  readonly="true"
                  :error="isInvoiceInvalid"
                  :error-messages="
                    isInvoiceInvalid ? '请提醒供应商填写发票金额' : ''
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.businessStatus != '草稿'"
                  label="发票日期"
                  dense
                  outlined
                  type="date"
                  v-model="detail.invoiceDate"
                  readonly="true"
                  :error="isInvoiceInvalid"
                  :error-messages="
                    isInvoiceInvalid ? '请提醒供应商填写发票日期' : ''
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  class="py-0"
                  outlined
                  dense
                  :readonly="!canEdit"
                  v-model="detail.completion"
                  label="完工情况"
                ></v-textarea>
              </v-col>
              <!-- <v-col cols="12">
                <v-textarea
                  class="py-0"
                  outlined
                  dense
                  :readonly="!canEdit"
                  v-model="detail.remark"
                  label="备注"
                ></v-textarea>
              </v-col> -->
              <v-col cols="12">
                <v-card-text>
                  <v-attach-list
                    title="完工单附件列表"
                    :attachments="detail.attachmentRecords"
                    @change="(ids) => (detail.attachmentIds = ids)"
                    :ship-code="detail.shipCode"
                    :readonly="!canEdit"
                  ></v-attach-list>
                </v-card-text>
              </v-col>
              <v-col cols="12">
                <v-card-text v-if="!isShip">
                  <v-attach-list
                    title="发票/结算单附件"
                    :attachments="detail.attachmentRecords4"
                    readonly="true"
                  ></v-attach-list>
                </v-card-text>
              </v-col>
              <v-col cols="12">
                <v-card-text v-if="false">
                  <v-attach-list
                    title="完工附件列表"
                    :attachments="detail.attachmentRecords1"
                    readonly="true"
                  ></v-attach-list>
                </v-card-text>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template v-if="canEdit" #消耗单按钮>
        <v-btn
          color="primary"
          @click="setOutParam('spare')"
          class="mx-1"
          small
          outlined
          tile
          :disabled="!!spareOutId"
          v-permission="['航修完工单:编辑']"
        >
          备件消耗
        </v-btn>
        <v-btn
          color="primary"
          @click="setOutParam('material')"
          class="mx-1"
          small
          outlined
          tile
          :disabled="!!materialOutId"
          v-permission="['航修完工单:编辑']"
        >
          物料消耗
        </v-btn>
        <v-btn
          color="primary"
          @click="setOutParam('soil')"
          class="mx-1"
          small
          outlined
          tile
          :disabled="!!soilOutId"
          v-permission="['航修完工单:编辑']"
        >
          滑油消耗
        </v-btn>
      </template>
      <template #消耗单>
        <v-table-list :headers="isShip ? headersShip : headers" :items="outs">
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo && item.shipInfo.chShipName }}
          </template>
          <template v-slot:[`item.inoutMode`]="{ item }">
            {{
              [
                '订单入库',
                '调减入库',
                '期初入库',
                '维护保养消耗',
                '自修消耗',
                '航修消耗',
                '坞修消耗',
                '自由出库',
              ][item.inoutMode]
            }}
          </template>
          <template v-slot:[`item.inoutCode`]="{ item }">
            <router-link
              v-if="item.inoutNature == 0"
              :to="{ name: 'spare-out-detail', params: { id: item.id } }"
            >
              {{ item.inoutCode }}
            </router-link>
            <router-link
              v-else-if="item.inoutNature == 1"
              :to="{ name: 'spare-out-detail', params: { id: item.id } }"
            >
              {{ item.inoutCode }}
            </router-link>
            <router-link
              v-else-if="item.inoutNature == 2"
              :to="{ name: 'spare-out-detail', params: { id: item.id } }"
            >
              {{ item.inoutCode }}
            </router-link>
          </template>
          <template v-slot:[`item.inoutNature`]="{ item }">
            {{ ['备件', '物料', '滑油'][item.inoutNature] }}
          </template>
          <template v-slot:[`item.status`]="{ item }">
            {{ ['', '未提交', '审批中', '审批通过', '审批驳回'][item.status] }}
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
// applicantId	申请人id	string
// applicantNickName	申请人姓名	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// attachmentRecords	附件列表	array	CommonAttachment
// auditParams	流程参数	AuditParams	AuditParams
// businessStatus	业务状态	string
// completeDate	完工日期	string(date-time)
// completionNo	完工单单号	string
// componentOutId	备件出库单id	string
// dept	申请部门	string
// enquiryId	询价单id	string
// greaseOutId	滑油出库单id	string
// id	物理主键	string
// materialsOutId	物料出库单id	string
// orderId	修理单id	string
// otherExpense	其他费用	number
// quoteId	报价单id	string
// remark	备注	string
// repair	修理费用	number
export default {
  name: 'voyage-complete-detail',
  created() {
    this.backRouteName = 'voyage-complete-list'
    this.subtitles = ['基本信息', '消耗单']
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.reqUrl = '/business/shipAffairs/voyageRepair/orderPage'
    this.repairHeaders = [
      { text: '船舶', value: 'shipInfo' },
      { text: '修理单号', value: 'orderNo' },
      { text: '实际修理费用', value: 'reallyRepairExpense' },
      { text: '实际其他费用', value: 'reallyOtherMoney' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '出库日期', value: 'inoutDate' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '物品类型', value: 'inoutNature' },
      { text: '物品金额', value: 'amountInvolved' },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]

    this.repairHeadersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '修理单号', value: 'orderNo' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '出库日期', value: 'inoutDate' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '物品类型', value: 'inoutNature' },
      { text: '状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.pageId = this.isEdit
      ? this.$route.params.id
      : Math.round(Math.random() * 100000)
  },
  data() {
    return {
      detail: {
        attachmentIds: [],
        componentOutId: '',
        greaseOutId: '',
        materialsOutId: '',
        applyPurpose: '',
        repairType: '',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      repairTypeOptions: ['机务项目', '通导项目'],
      searchObj: {
        shipCode: '',
        businessStatus: '完工确认',
        status: '1',
      },
      initOrder: {},
      outs: [],
      initHandler: false,
      loading: false,
    }
  },
  mixins: [routerControl],
  computed: {
    isEdit() {
      return !(this.$route.params.id === 'new')
    },
    isEdit1() {
      //if (!this.$local.data.get('userInfo').isShipSyS) return true
      return !(
        this.$route.params.id === 'new' ||
        this.detail.businessStatus == '草稿' ||
        this.detail.status == 4
      )
    },
    canEdit() {
      //if (!this.$local.data.get('userInfo').isShipSyS) return false
      return this.detail.status != 2 && this.detail.status != 3
    },
    isSupper() {
      return this.$local.data.get('userInfo').userType == 4
    },
    spareOutId() {
      return (
        this.detail.componentOutId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.pageId &&
            b.itemType === 'spare-out-detail' &&
            b.businessType === 'voyage-complete-detail',
        )?.outId
      )
    },
    materialOutId() {
      return (
        this.detail.materialsOutId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.pageId &&
            b.itemType === 'materials-out-detail' &&
            b.businessType === 'voyage-complete-detail',
        )?.outId
      )
    },
    soilOutId() {
      return (
        this.detail.greaseOutId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.pageId &&
            b.itemType === 'soil-out-detail' &&
            b.businessType === 'voyage-complete-detail',
        )?.outId
      )
    },
    repair() {
      return ((this.detail.repair || 0) * this.detail.rateToMain).toFixed(2)
    },
    otherExpense() {
      return ((this.detail.otherExpense || 0) * this.detail.rateToMain).toFixed(
        2,
      )
    },
    toUsd() {
      return (
        (this.detail.repair || 0) * this.detail.rateToMain +
        (this.detail.otherExpense || 0) * this.detail.rateToMain
      ).toFixed(2)
    },
    isReallyRepairExpenseInvalid() {
      return (
        this.detail.businessStatus == '已提交待确认' &&
        (this.detail.reallyRepairExpense == null ||
          this.detail.reallyRepairExpense === 0)
      )
    },
    isInvoiceInvalid() {
      return (
        this.detail.businessStatus == '已提交待确认' &&
        (this.detail.invoiceMoney == null || this.detail.invoiceMoney === 0.0)
      )
    },
    reallyRepairExpense() {
      return (
        (this.detail.reallyRepairExpense || 0) * this.detail.rateToMain
      ).toFixed(2)
    },
    reallyOtherMoney() {
      return (
        (this.detail.reallyOtherMoney || 0) * this.detail.rateToMain
      ).toFixed(2)
    },
    reallyFinalMoney() {
      return (
        (this.detail.reallyRepairExpense || 0) * this.detail.rateToMain +
        (this.detail.reallyOtherMoney || 0) * this.detail.rateToMain
      ).toFixed(2)
    },
    isShip() {
      return this.$local.data.get('userInfo').isShipSyS
    },
  },
  watch: {
    spareOutId(val, oldVal) {
      if (val && val !== oldVal) this.loadOuts(val)
    },
    materialOutId(val, oldVal) {
      if (val && val !== oldVal) this.loadOuts(val)
    },
    soilOutId(val, oldVal) {
      if (val && val !== oldVal) this.loadOuts(val)
    },
    'detail.shipCode'(val) {
      if (val) {
        this.searchObj.shipCode = val
      }
    },
  },

  methods: {
    async changeStatus(businessStatus) {
      this.loading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/submitCompletionById',
        { id: this.$route.params.id, status: businessStatus },
      )
      this.loading = false
      if (!errorRaw) {
        await this.$dialog.message.success(`操作成功`)
        this.closeAndTo(this.backRouteName)
      }
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/saveOrUpdateCompletion',
        {
          ...this.detail,
          componentOutId: this.detail.componentOutId || this.spareOutId,
          materialsOutId: this.detail.materialsOutId || this.materialOutId,
          greaseOutId: this.detail.greaseOutId || this.soilOutId,
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      if (
        !this.detail.attachmentIds ||
        this.detail.attachmentIds.length === 0
      ) {
        this.$dialog.message.error(`请上传完工附件`)
        return
      }
      if (!this.detail.completion || this.detail.completion.length === 0) {
        this.$dialog.message.error(`请填写完工情况`)
        return
      }
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        if (
          !this.detail.auditParams ||
          this.detail.businessStatus == '填写申请单' ||
          this.detail.businessStatus == '草稿'
        ) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/voyageRepair/submitCompletionById',
            { id: data, status: null },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/getCompletionDetailById',
        { id: this.$route.params.id },
      )
      this.detail = {
        ...data,
        shipCode: data.shipInfo.shipCode,
      }
      this.initOrder = { id: data.orderId, orderNo: data.orderNo }
      if (this.detail.businessStatus == '草稿') {
        const userInfo = this.$local.data.get('userInfo')
        this.initHandler = {
          id: userInfo.id,
          nickName: userInfo.nickName,
        }
        this.detail.handlerId = userInfo.id
      } else
        this.initHandler = { id: data.handlerId, nickName: data.handlerName }
    },

    selectApply(order) {
      this.detail.orderId = order.id
      this.initOrder = { id: order.id, orderNo: order.orderNo }
      this.detail.applyPurpose = order.applyPurpose
    },

    setOutParam(type) {
      const types = {
        spare: 'spare-out-detail',
        material: 'materials-out-detail',
        soil: 'soil-out-detail',
      }
      const businessItemId = this.pageId
      const businessId = this.$route.params.id
      this.$store.commit('emitOut', {
        businessType: 'voyage-complete-detail',
        businessItemId,
        businessId,
        itemType: types[type],
      })
      this.$router.push({
        name: types[type],
        params: { id: 'new' },
      })
    },

    async loadOuts(id) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseStockInOutPage',
        { id },
      )
      if (data.records[0]) this.outs.push(data.records[0])
    },
  },
  beforeDestroy() {
    this.$store.commit('removeOutParamByItemId', this.pageId)
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
