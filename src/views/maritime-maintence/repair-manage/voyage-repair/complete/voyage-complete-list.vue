<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="isShip ? headersShip : headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      use-ship
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="status2"
            :items="statusMap"
            label="审批状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="业务状态"
            outlined
            dense
            clearable
            v-model="businessStatus2"
            :items="businessStatusMap"
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="完工单号"
            outlined
            dense
            clearable
            v-model="searchObj.completionNo"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          v-if="false"
          :to="{ name: 'voyage-complete-detail', params: { id: 'new' } }"
          v-permission="['航修完工单:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status != 1 && selected.status != 4"
          outlined
          tile
          color="error"
          class="mx-1"
          v-if="false"
          @click="del"
          v-permission="['航修完工单:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses2[item.status] }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applicantId	申请人id	string
// applicantPost	申请人岗位	string
// applyId	申请单id	string
// businessStatus	业务状态	string
// completionNo	完工单单号	string
// dept	申请部门	string
// enquiryId	询价单id	string
// id	物理主键	string
// orderId	修理单id	string
// otherExpense	其他费用	number
// quoteId	报价单id	string
// remark	备注	string
// repair	修理费用	number
// shipInfo	船舶编码	ShipInfoDO	ShipInfoDO
// status	流程状态	string
export default {
  name: 'voyage-complete-list',
  created() {
    this.tableName = '航修完工单'
    this.reqUrl = '/business/shipAffairs/voyageRepair/completionPage'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '完工单号', value: 'completionNo' },
      { text: '币种', value: 'ccyCode' },
      { text: '修理类型', value: 'repairType' },
      { text: '原始报价修理费用', value: 'repair' },
      { text: '原始报价其他费用', value: 'otherExpense' },
      { text: '实际修理费用', value: 'reallyRepairExpense' },
      { text: '实际其他费用', value: 'reallyOtherMoney' },
      { text: '申请部门', value: 'dept' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
      { text: '完工情况', value: 'completion' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '完工单号', value: 'completionNo' },
      { text: '申请部门', value: 'dept' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
      { text: '完工情况', value: 'completion' },
    ]
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
      { text: '废弃', value: '5' },
    ]
    this.businessStatusMap = [
      { text: '草稿', value: '草稿' },
      { text: '已提交待确认', value: '已提交待确认' },
    ]
    this.statuses2 = ['暂无审批', '草稿', '审批中', '已审批', '已驳回', '废弃']
    this.statusColors = ['info', '', 'warning', 'success', 'error', 'error']
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'voyage-complete-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: false,
      },
      status2: '',
      businessStatus2: '',
    }
  },
  watch: {
    status2(val) {
      this.searchObj.status = val
      this.$refs.table.loadTableData()
    },
    businessStatus2(val) {
      this.searchObj.businessStatus = val
      this.$refs.table.loadTableData()
    },
  },

  methods: {
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/deleteCompletion',
        {
          id: this.selected.id,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('删除成功')
        this.$refs.table.loadTableData()
        this.selected = false
      }
    },
  },

  mounted() {
    if (this.$route.query.businessStatus != undefined) {
      this.businessStatus2 = this.$route.query.businessStatus
      // this.status2 = this.$route.query.status
      // this.searchObj.repairType = this.$route.query.repairType
    }
    if (this.$route.query.status != undefined) {
      //this.searchObj.status = this.$route.query.status
      this.status2 = this.$route.query.status
      this.searchObj.isMe = this.$route.query.isMe
    }
  },
}
</script>

<style></style>
