<template>
  <v-dialog v-model="dialog" max-width="800" persistent>
    <v-card>
      <v-card-title>
        {{ initialData.id ? '编辑' : '新增' }}修理费用
        <v-spacer></v-spacer>
        <v-btn icon @click="close">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.itemName"
                  label="修理项目"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.price"
                  label="单价"
                  type="number"
                  dense
                  outlined
                  :rules="[rules.required]"
                  @input="calculateTotal"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.quotNum"
                  label="数量"
                  type="number"
                  dense
                  outlined
                  :rules="[rules.required]"
                  @input="calculateTotal"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.total"
                  label="总价"
                  type="number"
                  dense
                  outlined
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="form.remark"
                  label="备注"
                  dense
                  outlined
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" text @click="close">取消</v-btn>
        <v-btn color="primary" @click="save">确定</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'repair-item-dialog',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    shipCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      form: {
        itemName: '',
        price: 0,
        quotNum: 0,
        total: 0,
        remark: '',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        aboveZero: (v) => v > 0 || '必须大于0',
      },
    }
  },
  computed: {
    dialog: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.initForm()
      }
    },
  },
  methods: {
    initForm() {
      if (this.initialData.id) {
        this.form = { ...this.initialData }
      } else {
        this.form = {
          itemName: '',
          price: 0,
          quotNum: 0,
          total: 0,
          remark: '',
        }
      }
    },
    calculateTotal() {
      this.form.total = this.form.price * this.form.quotNum
    },
    close() {
      this.dialog = false
    },
    save() {
      if (!this.$refs.form.validate()) return

      const data = {
        ...this.form,
        vid: this.initialData.id || Date.now(),
        operationType: this.initialData.id ? 2 : 1,
      }

      this.$emit('success', data)
      this.close()
    },
  },
}
</script>
