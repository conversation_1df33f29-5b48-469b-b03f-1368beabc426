<template>
  <v-container fluid>
    <v-detail-view
      :title="`航修报价-${detail.quoteNo}`"
      :tooltip="detail.quoteNo"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="canSubmit"
      @save="save"
      @submit="submit"
      :can-save="canSubmit"
      v-permission="['航修报价:编辑']"
    >
      <template v-slot:custombtns>
        <v-btn
          tile
          color="success"
          small
          class="mx-1"
          v-if="
            detail.businessStatus == '未填报' ||
            detail.businessStatus == '填报中'
          "
          :loading="loading"
          @click="openSaveDraftDialog"
        >
          保存草稿
        </v-btn>
        <v-btn
          tile
          color="success"
          small
          class="mx-1"
          v-if="
            detail.businessStatus == '未填报' ||
            detail.businessStatus == '填报中'
          "
          :loading="loading"
          @click="submit()"
        >
          提交报价
        </v-btn>
      </template>
      <template v-slot:基本信息-BasicInfo>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form" :readonly="!canSubmit">
            <v-row>
              <!-- <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="true"
                  v-model="detail.shipInfo.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col> -->
              <v-col cols="12">
                <b>故障诊断/trouble shooting:</b>
                {{ detail.applyPurpose.split('#:#')[1] }}
              </v-col>
              <v-col cols="12">
                <b>所需岸基支持/land support:</b>
                {{ detail.applyPurpose.split('#:#')[2] }}
              </v-col>
              <v-col cols="12">
                <b>航修项目/repair project:</b>
                {{ detail.applyPurpose.split('#:#')[0] }}
              </v-col>
              <v-col cols="12" v-if="canSelect1">
                <b>设备主体/Equipment body:</b>
                {{ detail.equipmentName }}
                <b>设备型号/Equipment Model:</b>
                {{ detail.equimentModel }}
                <b>序列号/Serial Number:</b>
                {{ detail.equipmentNumber }}
                <b>生产厂家/Manufacturer:</b>
                {{ detail.manufacture }}
              </v-col>
              <v-col cols="12" md="12">发票抬头：{{ inHeader }}</v-col>
              <v-col cols="12" md="12">交货港口：{{ detail.portName }}</v-col>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="true"
                  v-model="detail.shipInfo.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.stockUpDays"
                  :rules="[rules.required]"
                  label="准备天数/prepareDays"
                  type="number"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  use-current
                  v-model="detail.filledBy"
                  :rules="[rules.required]"
                  label="填报人/filledBy"
                  readonly
                  dense
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.invoiceType"
                  :rules="[rules.required]"
                  label="发票类型/invoiceType"
                  dense
                  :items="发票类型"
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="repairTotalPrice"
                  :rules="[rules.required]"
                  label="修理费总价"
                  dense
                  type="number"
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="otherTotalPrice"
                  :rules="[rules.required]"
                  label="其它费用总价"
                  dense
                  type="number"
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="spareTotalPrice"
                  :rules="[rules.required]"
                  label="备件费总价"
                  dense
                  type="number"
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="materialTotalPrice"
                  :rules="[rules.required]"
                  label="物料费总价"
                  dense
                  type="number"
                  readonly
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  disabled
                  dense
                  v-model="detail.ccyCode"
                  label="币种/ccyCode"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  v-model="detail.termOfVail"
                  :rules="[rules.required]"
                  label="有效期"
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.taxRate"
                  :rules="[rules.required]"
                  label="发票税率/taxRate"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="tax"
                  :rules="[rules.required]"
                  label="税费/tax"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  :label="
                    detail.priceIncTax
                      ? '含税/taxIncluded'
                      : '不含税/excludingTax'
                  "
                  v-model="detail.priceIncTax"
                  dense
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="总价"
                  label="航修总价"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="USD"
                  label="折算美元/USD"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
          <v-col cols="12" md="12">
            <v-textarea
              label="报价备注"
              dense
              outlined
              v-model="detail.remark1"
              :readonly="
                detail.businessStatus !== '未填报' &&
                detail.businessStatus !== '填报中'
              "
            ></v-textarea>
          </v-col>
          <v-card-text>
            <v-attach-list
              title="申请单附件列表"
              :attachments="detail.attachmentRecords"
              disabled
              @change="(ids) => (detail.attachmentIds = ids)"
            ></v-attach-list>
          </v-card-text>
          <v-col cols="12" md="12">
            <v-attach-list
              :attachments="detail.attachmentRecords1"
              :disabled="isDisabled"
              title="报价附件列表"
              @change="(ids) => (detail.attachmentIds1 = ids)"
              :readonly="
                detail.businessStatus !== '未填报' &&
                detail.businessStatus !== '填报中'
              "
            ></v-attach-list>
          </v-col>
        </v-container>
      </template>

      <template #预估修理费&加工费>
        <v-card-text>
          <div class="d-flex justify-end mb-2">
            <v-chip color="primary" label>
              预估修理费总价：{{ repairTotalPrice }}
            </v-chip>
          </div>
          <v-table-list
            :headers="repairHeaders"
            :items="repairList"
            v-model="selectedRepair"
            :ship-code="detail.shipInfo.shipCode"
            item-key="vid"
          ></v-table-list>
          <v-btn
            :disabled="!detail.shipInfo.shipCode"
            outlined
            tile
            small
            color="success"
            class="mx-1 mt-2"
            @click.stop="createRepairItem"
            v-permission="['修理费用:新增']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            :disabled="!selectedRepair"
            outlined
            small
            tile
            color="error"
            class="mx-1 mt-2"
            @click="delRepairItem"
            v-permission="['修理费用:删除']"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-card-text>
      </template>

      <template #预估其他费用>
        <v-card-text>
          <div class="d-flex justify-end mb-2">
            <v-chip color="primary" label>
              预估其他费用总价：{{ otherTotalPrice }}
            </v-chip>
          </div>
          <v-table-list
            :headers="otherHeaders"
            :items="otherList"
            v-model="selectedOther"
            :ship-code="detail.shipInfo.shipCode"
            item-key="vid"
          ></v-table-list>
          <v-btn
            :disabled="!detail.shipInfo.shipCode"
            outlined
            tile
            small
            color="success"
            class="mx-1 mt-2"
            @click.stop="createOtherItem"
            v-permission="['其他费用:新增']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            :disabled="!selectedOther"
            outlined
            small
            tile
            color="error"
            class="mx-1 mt-2"
            @click="delOtherItem"
            v-permission="['其他费用:删除']"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-card-text>
      </template>

      <!-- 添加备件费用明细表格 -->
      <template #预估备件费用>
        <v-card-text>
          <div class="d-flex justify-end mb-2">
            <v-chip color="primary" label>
              预估备件费总价：{{ spareTotalPrice }}
            </v-chip>
          </div>
          <v-table-list
            :headers="spareHeaders"
            :items="spareList"
            v-model="selectedSpare"
            :ship-code="detail.shipInfo.shipCode"
            item-key="vid"
          ></v-table-list>
          <v-btn
            :disabled="!detail.shipInfo.shipCode"
            outlined
            tile
            small
            color="success"
            class="mx-1 mt-2"
            @click.stop="createSpareItem"
            v-permission="['备件费用:新增']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            :disabled="!selectedSpare"
            outlined
            small
            tile
            color="error"
            class="mx-1 mt-2"
            @click="delSpareItem"
            v-permission="['备件费用:删除']"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-card-text>
      </template>

      <!-- 添加物料费用明细表格 -->
      <template #预估物料费用>
        <v-card-text>
          <div class="d-flex justify-end mb-2">
            <v-chip color="primary" label>
              预估物料费总价：{{ materialTotalPrice }}
            </v-chip>
          </div>
          <v-table-list
            :headers="materialHeaders"
            :items="materialList"
            v-model="selectedMaterial"
            :ship-code="detail.shipInfo.shipCode"
            item-key="vid"
          ></v-table-list>
          <v-btn
            :disabled="!detail.shipInfo.shipCode"
            outlined
            tile
            small
            color="success"
            class="mx-1 mt-2"
            @click.stop="createMaterialItem"
            v-permission="['物料费用:新增']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            :disabled="!selectedMaterial"
            outlined
            small
            tile
            color="error"
            class="mx-1 mt-2"
            @click="delMaterialItem"
            v-permission="['物料费用:删除']"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </v-card-text>
      </template>
    </v-detail-view>
    <!-- 保存草稿弹窗 -->
    <v-dialog v-model="saveDraftDialog" max-width="400" max-height="200">
      <v-card style="overflow-y: hidden">
        <v-btn
          small
          outlined
          tile
          class="mx-1"
          @click="saveDraftDialog = false"
          style="position: absolute; top: 8px; right: 8px"
        >
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
        <v-card-title class="headline">温馨提醒</v-card-title>
        <v-card-text>
          草稿保存成功后，仍需点击"提交报价"按钮后船东方可收到报价。
        </v-card-text>
        <v-card-actions>
          <v-row justify="space-around" align="center">
            <v-col cols="4">
              <v-btn
                block
                tile
                small
                color="success"
                :loading="loading"
                @click="save()"
              >
                保存草稿
              </v-btn>
            </v-col>
            <v-col cols="4">
              <v-btn
                block
                tile
                small
                color="success"
                :loading="loading"
                @click="submit()"
              >
                提交报价
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 添加费用明细编辑弹窗 -->
    <repair-item-dialog
      v-model="repairDialog"
      @success="repairSuccess"
      :initial-data="repairInitialData"
      :shipCode="detail.shipInfo.shipCode"
    ></repair-item-dialog>

    <other-item-dialog
      v-model="otherDialog"
      @success="otherSuccess"
      :initial-data="otherInitialData"
      :shipCode="detail.shipInfo.shipCode"
    ></other-item-dialog>

    <spare-item-dialog
      v-model="spareDialog"
      @success="spareSuccess"
      :initial-data="spareInitialData"
      :shipCode="detail.shipInfo.shipCode"
    ></spare-item-dialog>

    <material-item-dialog
      v-model="materialDialog"
      @success="materialSuccess"
      :initial-data="materialInitialData"
      :shipCode="detail.shipInfo.shipCode"
    ></material-item-dialog>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import routerControl from '@/mixin/routerControl'
import RepairItemDialog from './private/repair-item-dialog.vue'
import OtherItemDialog from './private/other-item-dialog.vue'
import SpareItemDialog from './private/spare-item-dialog.vue'
import MaterialItemDialog from './private/material-item-dialog.vue'

export default {
  name: 'voyage-quote-detail',
  components: {
    RepairItemDialog,
    OtherItemDialog,
    SpareItemDialog,
    MaterialItemDialog,
  },
  mixins: [currencyHelper, routerControl],
  created() {
    this.backRouteName = 'voyage-quote-list'
    this.subtitles = [
      '基本信息-BasicInfo',
      '预估修理费&加工费',
      '预估其他费用',
      '预估备件费用',
      '预估物料费用',
    ]
    this.发票类型 = [
      { text: '增值税专用发票', value: '0' },
      { text: '普通发票', value: '1' },
      { text: '形式发票', value: '2' },
    ]
    this.repairHeaders = [
      { text: '修理项目', value: 'itemName' },
      { text: '单价', value: 'price' },
      { text: '数量', value: 'quotNum' },
      { text: '总价', value: 'total' },
      { text: '备注', value: 'remark' },
    ]

    this.otherHeaders = [
      { text: '费用名称', value: 'itemName' },
      { text: '单价', value: 'price' },
      { text: '数量', value: 'quotNum' },
      { text: '总价', value: 'total' },
      { text: '备注', value: 'remark' },
    ]

    this.spareHeaders = [
      { text: '备件名称', value: 'componentName' },
      { text: '备件号', value: 'componentNo' },
      { text: '图纸号', value: 'drawingSerialNumber' },
      { text: '序列号', value: 'serialNumber' },
      { text: '数量', value: 'quotNum' },
      { text: '单价', value: 'price' },
      { text: '总价', value: 'total' },
      { text: '备注', value: 'remark' },
    ]

    this.materialHeaders = [
      { text: '物料名称', value: 'itemName' },
      { text: '物料编码', value: 'code' },
      { text: '数量', value: 'quotNum' },
      { text: '单价', value: 'price' },
      { text: '总价', value: 'total' },
      { text: '备注', value: 'remark' },
    ]
  },
  data() {
    return {
      saveDraftDialog: false,
      loading: false,
      detail: {
        businessStatus: '',
        currencyId: '',
        quoteNo: '',
        applyPurpose: '',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      inHeader: '',
      repairDialog: false,
      otherDialog: false,
      spareDialog: false,
      materialDialog: false,
      repairInitialData: {},
      otherInitialData: {},
      spareInitialData: {},
      materialInitialData: {},
      selectedRepair: null,
      selectedOther: null,
      selectedSpare: null,
      selectedMaterial: null,
      detailList: [],
    }
  },

  computed: {
    repairList() {
      return this.detailList.filter((item) => item.model === 0)
    },
    otherList() {
      return this.detailList.filter((item) => item.model === 1)
    },
    spareList() {
      return this.detailList.filter((item) => item.model === 2)
    },
    materialList() {
      return this.detailList.filter((item) => item.model === 3)
    },
    canSelect1() {
      if (!this.detail.equipmentName) {
        return false
      }
      return true
    },
    总价() {
      const total =
        Number(this.repairTotalPrice || 0) +
        Number(this.otherTotalPrice || 0) +
        Number(this.spareTotalPrice || 0) +
        Number(this.materialTotalPrice || 0)

      return this.isJPY
        ? Math.round(total)
        : (Math.round(total * 100) / 100).toFixed(2)
    },
    tax() {
      return this.总价 * (this.detail.taxRate || 0)
    },
    canSubmit() {
      return ['填报中', '未填报'].includes(this.detail.businessStatus)
    },
    isJPY() {
      return this.detail.ccyCode === 'JPY' || this.detail.ccyCode === '日元'
    },
    USD() {
      return (
        Math.round(
          this.总价 *
            this.currencyInfo.find((item) => item.id === this.detail.currencyId)
              ?.rateToMain *
            100,
        ) / (100).toFixed(2)
      )
    },
    repairTotalPrice() {
      return this.isJPY
        ? Math.round(
            this.repairList.reduce((sum, item) => sum + (item.total || 0), 0),
          )
        : (
            Math.round(
              this.repairList.reduce(
                (sum, item) => sum + (item.total || 0),
                0,
              ) * 100,
            ) / 100
          ).toFixed(2)
    },
    otherTotalPrice() {
      return this.isJPY
        ? Math.round(
            this.otherList.reduce((sum, item) => sum + (item.total || 0), 0),
          )
        : (
            Math.round(
              this.otherList.reduce((sum, item) => sum + (item.total || 0), 0) *
                100,
            ) / 100
          ).toFixed(2)
    },
    spareTotalPrice() {
      return this.isJPY
        ? Math.round(
            this.spareList.reduce((sum, item) => sum + (item.total || 0), 0),
          )
        : (
            Math.round(
              this.spareList.reduce((sum, item) => sum + (item.total || 0), 0) *
                100,
            ) / 100
          ).toFixed(2)
    },
    materialTotalPrice() {
      return this.isJPY
        ? Math.round(
            this.materialList.reduce((sum, item) => sum + (item.total || 0), 0),
          )
        : (
            Math.round(
              this.materialList.reduce(
                (sum, item) => sum + (item.total || 0),
                0,
              ) * 100,
            ) / 100
          ).toFixed(2)
    },
  },

  methods: {
    async loadhEADER() {
      const { data } = await this.getAsync(
        '/business/common/ship/getPayCompanyHeader',
        {
          code: this.detail.shipInfo.shipCode,
        },
      )
      if (data) {
        this.inHeader = data
      }
      console.log(data)
    },
    async save(goBack, notMove = false) {
      this.loading = true
      this.detail.repairExpense = Number(this.repairTotalPrice || 0)
      this.detail.otherExpense = Number(this.otherTotalPrice || 0)
      this.detail.spareExpense = Number(this.spareTotalPrice || 0)
      this.detail.materialExpense = Number(this.materialTotalPrice || 0)
      this.detail.finalPrice = Number(this.总价 || 0)

      // 处理删除的项目
      const originalDetailList = this.detail.originalDetailList || []
      const deletedItems = originalDetailList
        .filter(
          (originalItem) =>
            !this.detailList.some(
              (currentItem) => currentItem.id === originalItem.id,
            ),
        )
        .map((item) => ({
          id: item.id,
          operationType: 3,
        }))

      // eslint-disable-next-line
      const cleanDetailList = this.detailList.map(({ vid, ...rest }) => rest)

      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/voyageRepair/saveOrUpdateQuote',
        {
          ...this.detail,
          detailList: [...cleanDetailList, ...deletedItems],
        },
      )
      this.loading = false
      if (errorRaw) return false
      if (notMove) return true
      this.$dialog.message.success('保存草稿成功')
      this.saveDraftDialog = false
      setTimeout(() => {
        this.closeAndTo(this.backRouteName)
      }, 1000)
    },
    async submit(goBack) {
      this.loading = true
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请完善必填项')
        this.loading = false
        this.saveDraftDialog = false
        return
      }
      if (
        (!this.detail.attachmentIds1 ||
          this.detail.attachmentIds1.length === 0) &&
        (!this.detail.attachmentRecords1 ||
          this.detail.attachmentRecords1.length === 0)
      ) {
        this.$dialog.message.error('请上传报价附件')
        this.loading = false
        this.saveDraftDialog = false
        return
      }
      const res = await this.save(goBack, true)
      if (!res) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/submitQuoteById',
        { id: this.$route.params.id },
      )
      if (errorRaw) return false
      this.loading = false
      if (!errorRaw) {
        this.$dialog.message.success('报价已提交至船东，请等待开标结果')
      }
      this.saveDraftDialog = false
      setTimeout(() => {
        this.closeAndTo(this.backRouteName)
      }, 1000)
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/voyageRepair/getQuoteDetailById',
        { id: this.$route.params.id },
      )
      this.detail = data

      // 处理后端返回的 detailList，添加前端所需的 vid 字段
      if (data.detailList && data.detailList.length > 0) {
        // 后端已经返回了统一的 detailList，直接使用
        this.detailList = data.detailList.map((item) => ({
          ...item,
          vid: item.id || Date.now() + Math.random(),
          operationType: 0,
        }))
      } else {
        // 兼容旧版本：如果后端还没有返回 detailList，则从四个独立数组合并
        const mergeListsWithModel = () => {
          const lists = [
            { list: data.repairList || [], model: 0 },
            { list: data.otherList || [], model: 1 },
            { list: data.spareList || [], model: 2 },
            { list: data.materialList || [], model: 3 },
          ]

          return lists.flatMap(({ list, model }) =>
            list.map((item) => ({
              ...item,
              model,
              vid: item.id || Date.now() + Math.random(),
              operationType: 0,
            })),
          )
        }
        this.detailList = mergeListsWithModel()
      }

      // 保存原始列表用于比较删除项
      this.detail.originalDetailList = [...this.detailList]

      this.loadhEADER()
      if (data.businessStatus === '未填报') return
    },
    openSaveDraftDialog() {
      this.saveDraftDialog = true
    },
    createRepairItem() {
      this.repairInitialData = {}
      this.repairDialog = true
    },
    repairSuccess(newItem) {
      if (this.repairList.some((item) => item.itemName === newItem.itemName)) {
        this.$dialog.message.error('修理项目重复录入')
        return
      }
      // 添加到 detailList，设置 model 为 0 (修理列表)
      this.detailList.push({
        ...newItem,
        model: 0,
        vid: Date.now() + Math.random(),
        operationType: newItem.id ? 2 : 1, // 有 id 表示更新，无 id 表示新增
      })
    },
    delRepairItem() {
      this.detailList = this.detailList.filter(
        (item) => item.vid !== this.selectedRepair.vid,
      )
    },

    createOtherItem() {
      this.otherInitialData = {}
      this.otherDialog = true
    },
    otherSuccess(newItem) {
      if (this.otherList.some((item) => item.itemName === newItem.itemName)) {
        this.$dialog.message.error('费用项目重复录入')
        return
      }
      // 添加到 detailList，设置 model 为 1 (其他列表)
      this.detailList.push({
        ...newItem,
        model: 1,
        vid: Date.now() + Math.random(),
        operationType: newItem.id ? 2 : 1, // 有 id 表示更新，无 id 表示新增
      })
    },
    delOtherItem() {
      this.detailList = this.detailList.filter(
        (item) => item.vid !== this.selectedOther.vid,
      )
    },
    createSpareItem() {
      this.spareInitialData = {}
      this.spareDialog = true
    },
    spareSuccess(newItem) {
      if (
        this.spareList.some((item) => item.componentNo === newItem.componentNo)
      ) {
        this.$dialog.message.error('备件重复录入')
        return
      }
      // 添加到 detailList，设置 model 为 2 (备件列表)
      this.detailList.push({
        ...newItem,
        model: 2,
        vid: Date.now() + Math.random(),
        operationType: newItem.id ? 2 : 1, // 有 id 表示更新，无 id 表示新增
      })
    },
    delSpareItem() {
      this.detailList = this.detailList.filter(
        (item) => item.vid !== this.selectedSpare.vid,
      )
    },
    createMaterialItem() {
      this.materialInitialData = {}
      this.materialDialog = true
    },
    materialSuccess(newItem) {
      if (this.materialList.some((item) => item.code === newItem.code)) {
        this.$dialog.message.error('物料重复录入')
        return
      }
      // 添加到 detailList，设置 model 为 3 (物料列表)
      this.detailList.push({
        ...newItem,
        model: 3,
        vid: Date.now() + Math.random(),
        operationType: newItem.id ? 2 : 1, // 有 id 表示更新，无 id 表示新增
      })
    },
    delMaterialItem() {
      this.detailList = this.detailList.filter(
        (item) => item.vid !== this.selectedMaterial.vid,
      )
    },
  },

  mounted() {
    this.loadDetail()
    this.loadhEADER()
  },
}
</script>

<style>
/* 隐藏宽度为60px的按钮 */
button[style*='width: 60px;'] {
  display: none;
}

/* 隐藏宽度为80px的按钮 */
button[style*='width: 80px;'] {
  display: none;
}
</style>
