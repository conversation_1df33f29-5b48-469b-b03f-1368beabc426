<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        物料选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="3">
              <v-select
                v-model="searchObj.materialsType"
                outlined
                dense
                clearable
                label="物料分类"
                :items="types"
              ></v-select>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.equipmentNameLike"
                label="物料名称"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.equipmentCodeLike"
                label="物料号"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col>
          </template>
          <template #btns></template>
          <template v-slot:[`item.isDockRepair`]="{ item }">
            {{ item.isDockRepair ? '是' : '否' }}
          </template>
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo.chShipName }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="dialog = false">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'materials-stock-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl =
      '/business/shipAffairs/purchaseManage/purchaseStocksMsgByParams'
    this.headers = [
      { text: '物料名', value: 'itemName' },
      { text: '物料号', value: 'itemNo' },
      // { text: '设备主体', value: 'equipmentName' },
      { text: '物料属性', value: 'componentProperty' },
      { text: '仓库', value: 'depositoryName' },
      { text: '坞修', value: 'isDockRepair' },
      { text: '库存数量', value: 'itemNumber' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: String,
    components: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {
        stocksType: '1',
      },
      selected: [],
      firstEquipments: [],
      types: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    // 'searchObj.equipmentSecondId': {
    //   handler(val) {
    //     if (val) this.loadSubEqu()
    //   },
    // },
    // 'searchObj.equipmentInformationId': {
    //   handler(val) {
    //     if (val) this.loadSecondEquipment()
    //   },
    // },
    searchRemain(val) {
      this.searchObj = val
    },
    components(val) {
      this.selected = val.map((i) => {
        return {
          ...i,
          vid: i.id,
          id: i.depositoryId + i.itemId,
          itemNo: i.itemNumber,
          itemNumber: i.inventoryNumber,
        }
      })
    },
    shipCode(val) {
      if (val) this.searchObj.shipCode = val
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    // 子设备获取
    async loadSubEqu() {
      this.loading = true
      const reqUrl = this.searchObj.equipmentSecondId
        ? '/business/shipAffairs/equipmentInformation/getEquipmentThridBySecondId'
        : '/business/shipAffairs/equipmentInformation/getEquipmentThirdByMainEquipmentId'
      const { data } = await this.getAsync(reqUrl, {
        equipmentId: this.searchObj?.equipmentId,
        secondEquipmentId: this.searchObj?.equipmentSecondId,
      })
      this.subEquipments = data?.map((i) => {
        return {
          text: i.subEquipmentCname,
          value: i.id,
        }
      })
      this.loading = false
    },
    // 大模块
    async loadSecondEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentSecondByMainEquipmentId',
        { MainEquipmentId: this.searchObj.equipmentInformationId },
      )
      this.secondEquipments = data?.map((i) => {
        return {
          text: i.name,
          value: i.id,
        }
      })
    },
    // 设备主体
    async loadFirstEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { current: 1, size: 99, shipCode: this.shipCode },
      )
      const { records } = data
      this.firstEquipments = records?.map((i) => {
        return {
          text: i.equipmentCname,
          value: i.id,
        }
      })
    },
    confirm() {
      const components = this.selected.map((i) => {
        const comp = {
          ...i,
          id: i.vid || i.id,
          itemNumber: i.itemNo,
          inventoryNumber: i.itemNumber,
          max: (a) => i.itemNumber >= a || '出库数量不得超过最大库存量',
        }
        return comp
      })
      this.$emit('update:components', components)
      this.$emit('change', false)
    },
    async loadTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        { current: 1, size: 1000 },
        false,
      )
      this.types = data.records.map((item) => ({
        ...item,
        value: item.id,
        text: item.cateName,
      }))
    },
  },
  mounted() {
    this.loadFirstEquipment()
    this.loadTypes()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
