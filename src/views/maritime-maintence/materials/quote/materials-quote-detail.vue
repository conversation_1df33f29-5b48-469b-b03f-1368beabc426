<template>
  <v-container fluid>
    <v-detail-view
      :title="`物料报价/SpareQuote ${detail.quoteNo}`"
      :tooltip="detail.quoteNo"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      :can-submit="canSubmit"
      @submit="submit"
      :can-save="canSubmit"
      v-permission="['物料报价:编辑']"
    >
      <template v-slot:custombtns>
        <v-btn
          tile
          color="success"
          small
          class="mx-1"
          :loading="loading"
          v-if="
            detail.businessStatus == '未填报' ||
            detail.businessStatus == '填报中'
          "
          @click="openSaveDraftDialog"
        >
          保存草稿
        </v-btn>
        <v-btn
          tile
          color="success"
          small
          class="mx-1"
          :loading="loading"
          v-if="
            detail.businessStatus == '未填报' ||
            detail.businessStatus == '填报中'
          "
          @click="submit()"
        >
          提交报价
        </v-btn>
        <v-btn
          tile
          color="success"
          small
          class="mx-1"
          v-if="detail.businessStatus == '已填报'"
          :loading="loading"
          @click="submit()"
        >
          重新提交报价
        </v-btn>
      </template>
      <template v-slot:基本信息-BasicInfo>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form" :readonly="!canSubmit">
            <v-row>
              <v-col cols="12" md="12">发票抬头：{{ inHeader }}</v-col>
              <v-col cols="12" md="12">交货港口：{{ detail.portName }}</v-col>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="true"
                  v-model="detail.shipInfoDO.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.stockUpDays"
                  :rules="[rules.required]"
                  label="备货天数/stockUpDays"
                  type="number"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.inquier"
                  :rules="[rules.required]"
                  label="询价人/inquier"
                  dense
                  outlined
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.filledBy"
                  :rules="[rules.required]"
                  label="填报人/filledBy"
                  dense
                  outlined
                  disabled
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.invoiceType"
                  :rules="[rules.required]"
                  label="发票类型/invoiceType"
                  dense
                  :items="发票类型"
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  disabled
                  dense
                  v-model="detail.ccyCode"
                  :rules="[rules.required]"
                  label="币种/ccyCode"
                  outlined
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.otherExpenses"
                  :rules="[rules.required]"
                  label="运费/otherExpenses"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.taxRate"
                  :rules="[rules.required]"
                  label="发票税率/taxRate"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="tax"
                  :rules="[rules.required]"
                  label="税费/tax"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  :label="
                    detail.priceIncTax
                      ? '含税/taxIncluded'
                      : '不含税/excludingTax'
                  "
                  v-model="detail.priceIncTax"
                  dense
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="明细总价"
                  :rules="[rules.required]"
                  label="明细总价/detailTotalPrice"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="折扣后明细总价"
                  :rules="[rules.required]"
                  label="折扣后明细总价/totalDetailPriceAfterDiscount"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="折扣后总价"
                  :rules="[rules.required]"
                  label="折扣后总价/totalPriceAfterDiscount"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!canSubmit"></v-col>
              <v-col cols="12" md="3" v-if="!canSubmit">
                <v-text-field
                  v-model="最终明细总价"
                  :rules="[rules.required]"
                  label="明细成交总价/detailTotalFinalPrice"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="!canSubmit">
                <v-text-field
                  v-model="最终折扣后总价"
                  :rules="[rules.required]"
                  label="成交总价/totalFinalPriceAfterDiscount"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
            <v-card-text>
              <v-attach-list
                title="公司附件"
                disabled
                :attachments="detail.attachmentRecords"
                @change="(ids) => (detail.attachmentIds = ids)"
              ></v-attach-list>
            </v-card-text>
            <v-col cols="12" md="12">
              <v-textarea
                label="项目说明"
                dense
                outlined
                v-model="detail.remark4"
                :readonly="true"
              ></v-textarea>
            </v-col>
          </v-form>
        </v-container>
      </template>
      <template v-if="canSubmit" #报价明细-QuoteDetail按钮>
        <v-btn
          target="_blank"
          :to="`/api//business/shipAffairs/purchaseManage/purchaseQuoteExcelExport?quoteId=${$route.params.id}`"
          outlined
          tile
          color="success"
          class="mx-1"
        >
          <v-icon left>mdi-download</v-icon>
          下载导入模板
        </v-btn>
        <v-import-btn
          import-url="/business/shipAffairs/purchaseManage/purchaseQuoteExcelImport"
          :other-params="{ quoteId: $route.params.id }"
          @importSuccess="importSuccess"
        ></v-import-btn>
      </template>
      <template #报价明细-QuoteDetail>
        <v-form ref="form2">
          <v-data-table
            :headers="canSubmit ? headers : headers2"
            :items="detail.detailList"
            hide-default-footer
            disable-pagination
            dense
            class="use-divider"
          >
            <template v-if="canSubmit" v-slot:[`item.quotNum`]="{ item }">
              <!-- <v-edit-dialog :return-value.sync="item.quotNumm">
              <div class="text-decoration-underline blue--text">
                {{ item.quotNum }}
              </div>
              <template v-slot:input>
                <v-text-field
                  v-model="item.quotNum"
                  label="报价数量"
                  type="number"
                  single-line
                ></v-text-field>
              </template>
            </v-edit-dialog> -->
              <v-text-field
                class="shrink"
                v-model="item.quotNum"
                label="报价数量/num"
                type="number"
                dense
                single-line
                :rules="[rules.required, rules.decimal]"
              ></v-text-field>
            </template>
            <template v-if="canSubmit" v-slot:[`item.price`]="{ item }">
              <v-text-field
                v-if="!item.haveMainPrice && isJPY"
                @input="validatePrice($event, item)"
                v-model="item.price"
                label="单价/price"
                type="number"
                single-line
                dense
                :rules="[rules.required, rules.int]"
              ></v-text-field>
              <v-text-field
                v-else-if="!item.haveMainPrice && !isJPY"
                @input="validatePrice($event, item)"
                v-model="item.price"
                label="单价/price"
                type="number"
                single-line
                dense
                :rules="[rules.required, rules.decimal4]"
              ></v-text-field>
              <v-text-field
                v-else-if="item.haveMainPrice && isJPY"
                @input="validatePrice($event, item)"
                v-model="item.price"
                label="单价/price"
                type="number"
                single-line
                dense
                :rules="[
                  rules.required,
                  rules.int,
                  (v) =>
                    Number(v) <= Number(item.originalPrice) ||
                    '价格不能高于原协议价',
                ]"
              ></v-text-field>
              <v-text-field
                v-else-if="item.haveMainPrice && !isJPY"
                @input="validatePrice($event, item)"
                v-model="item.price"
                label="单价/price"
                type="number"
                single-line
                dense
                :rules="[
                  rules.required,
                  rules.decimal4,
                  (v) =>
                    Number(v) <= Number(item.originalPrice) ||
                    '价格不能高于原协议价',
                ]"
              ></v-text-field>
            </template>
            <template v-if="canSubmit" v-slot:[`item.discount`]="{ item }">
              <v-numeric
                v-model="item.discount"
                precision="2"
                label="折扣率/discount"
                type="number"
                single-line
                dense
                :rules="[rules.required, rules.aboveZero]"
                max="1"
                min="0"
              ></v-numeric>
            </template>
            <template v-slot:[`item.total`]="{ item }">
              {{
                isJPY
                  ? Math.round(item.price * item.quotNum * item.discount)
                  : Math.round(
                      item.price * item.quotNum * item.discount * 100,
                    ) / 100
              }}
            </template>
            <template v-if="canSubmit" v-slot:[`item.remark4`]="{ item }">
              <v-text-field
                v-model="item.remark4"
                label="申请理由"
                readonly
                single-line
                dense
              ></v-text-field>
            </template>
            <template v-if="!canSubmit" v-slot:[`item.total2`]="{ item }">
              {{
                isJPY
                  ? Math.round(item.finalPrice * item.quotNum * item.discount)
                  : Math.round(
                      item.finalPrice * item.quotNum * item.discount * 100,
                    ) / 100
              }}
            </template>
            <template v-if="canSubmit" v-slot:[`item.remark`]="{ item }">
              <!-- <v-text-field
                v-model="item.remark"
                single-line
                dense
              ></v-text-field> -->
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    @click="editRemark(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.remark"
                    single-line
                    dense
                  ></v-text-field>
                </template>
                <span>{{ item.remark }}</span>
              </v-tooltip>
            </template>
            <template v-if="!canSubmit" v-slot:[`item.isWins`]="{ item }">
              <v-chip small color="success" v-if="item.isWins">是</v-chip>
              <v-chip small color="warning" v-if="!item.isWins">否</v-chip>
            </template>
          </v-data-table>
        </v-form>
      </template>
      <v-card-text>
        <v-attach-list
          title="报价附件"
          :attachments="detail.attachmentRecords2"
          @change="(ids) => (detail.attachmentIds2 = ids)"
        ></v-attach-list>
      </v-card-text>
    </v-detail-view>
    <v-dialog v-model="dialog1" max-width="600">
      <template v-slot:default="dialog1">
        <v-card style="height: 300px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <!-- 保存草稿弹窗 -->
    <v-dialog v-model="saveDraftDialog" max-width="400" max-height="200">
      <v-card style="overflow-y: hidden">
        <v-btn
          small
          outlined
          tile
          class="mx-1"
          @click="saveDraftDialog = false"
          style="position: absolute; top: 8px; right: 8px"
        >
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
        <v-card-title class="headline">温馨提醒</v-card-title>
        <v-card-text>
          草稿保存成功后，仍需点击"提交报价"按钮后船东方可收到报价。
        </v-card-text>
        <v-card-actions>
          <v-row justify="space-around" align="center">
            <v-col cols="4">
              <v-btn
                block
                tile
                small
                color="success"
                :loading="loading"
                @click="save()"
              >
                保存草稿
              </v-btn>
            </v-col>
            <v-col cols="4">
              <v-btn
                block
                tile
                small
                color="success"
                :loading="loading"
                @click="submit()"
              >
                提交报价
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
// discount	折扣		false number
// enquiryId	询价单id		false string
// filledBy	填报人		false
// 物理主键		false string
// invoiceType	发票类型		false  integer(int32)
// otherExpenses	其他费用		false  number
// priceIncTax	报价是否含税		false boolean
// remark	备注		false string
// stockUpDays	备货天数		false string
// supplierId	供应商id		false string
// tax	税费		false number
// taxRate	发票税率		false  number
// import currencyHelper from '@/mixin/currencyHelper'
import routerControl from '@/mixin/routerControl'
export default {
  name: 'materials-quote-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'materials-quote-list'
    this.subtitles = ['基本信息-BasicInfo', '报价明细-QuoteDetail']
    this.发票类型 = [
      { text: '增值税专用发票', value: 0 },
      { text: '普通发票', value: 1 },
      { text: '形式发票', value: 2 },
    ]
    this.headers = [
      { text: '物料名称/itemName', value: 'itemName' },
      { text: '物料编码/itemNo', value: 'code' },
      { text: 'IMPA/IMPA', value: 'itemNo' },
      { text: '物料描述/description', value: 'desc' },
      { text: '单位/unit', value: 'unit' },
      { text: '询价数量/enquiryNum', value: 'enquiryNum' },
      { text: '报价数量/quotNum', value: 'quotNum', width: 150 },
      { text: '单价/price', value: 'price', width: 150 },
      {
        text: '折扣率/discount',
        value: 'discount',
        width: 150,
        sortable: false,
      },
      { text: '总价/total', value: 'total' },
      { text: '申请理由/reason', value: 'remark4' },
      { text: '备注/remark', value: 'remark' },
    ]
    this.headers2 = [
      { text: '物料名称/itemName', value: 'itemName' },
      { text: '物料编码/itemNo', value: 'code' },
      { text: 'IMPA/IMPA', value: 'itemNo' },
      { text: '单位/unit', value: 'unit' },
      { text: '询价数量/enquiryNum', value: 'enquiryNum' },
      // { text: '报价数量/quotNum', value: 'quotNum', width: 150 },
      { text: '报价数量/finalNum', value: 'finalNum', width: 150 },
      { text: '成交数量/quotNum', value: 'quotNum', width: 150 },
      { text: '单价/price', value: 'price', width: 150 },
      { text: '成交单价/final price', value: 'finalPrice', width: 150 },
      {
        text: '折扣率/discount',
        value: 'discount',
        width: 150,
        sortable: false,
      },
      { text: '总价/total', value: 'total' },
      { text: '成交总价/finalTotal', value: 'total2' },
      { text: '申请理由/reason', value: 'remark4' },
      { text: '备注/remark', value: 'remark' },
      { text: '是否中标/successful bid', value: 'isWins' },
    ]
  },
  data() {
    return {
      saveDraftDialog: false,
      loading: false,
      detail: {
        quoteType: '02',
        detailList: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        aboveZero: (v) => v > 0 || '必须大于0',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
        decimal4: (v) =>
          /^\d+(\.\d{1,4})?$/.test(v) || '必须为整数或最多四位小数',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
      },
      dialog1: false,
      editRemarkDetails: {},
      inHeader: '',
    }
  },

  computed: {
    明细总价() {
      return this.isJPY
        ? this.detail.detailList.reduce(
            (x, { price, quotNum }) => x + Math.round(price * quotNum),
            0,
          )
        : this.detail.detailList
            .reduce(
              (x, { price, quotNum }) =>
                x + Math.round(price * quotNum * 100) / 100,
              0,
            )
            .toFixed(2)
    },
    折扣后明细总价() {
      return this.isJPY
        ? this.detail.detailList.reduce(
            (x, { price, quotNum, discount }) =>
              x + Math.round(price * quotNum * discount),
            0,
          )
        : this.detail.detailList
            .reduce(
              (x, { price, quotNum, discount }) =>
                x + Math.round(price * quotNum * discount * 100) / 100,
              0,
            )
            .toFixed(2)
    },
    折扣后总价() {
      return Number(this.折扣后明细总价) + (this.detail.otherExpenses || 0) * 1
    },
    最终明细总价() {
      return this.isJPY
        ? this.detail.detailList
            .filter(({ isWins }) => isWins)
            .reduce(
              (x, { finalPrice, quotNum }) =>
                x + Math.round(finalPrice * quotNum),
              0,
            )
        : this.detail.detailList
            .filter(({ isWins }) => isWins)
            .reduce(
              (x, { finalPrice, quotNum }) =>
                x + Math.round(finalPrice * quotNum * 100) / 100,
              0,
            )
    },
    最终折扣后总价() {
      return this.detail.businessStatus === '不中标'
        ? this.最终明细总价
        : this.最终明细总价 + (this.detail.otherExpenses || 0) * 1
    },
    tax() {
      return this.折扣后总价 * (this.detail.taxRate || 0)
    },
    canSubmit() {
      return ['填报中', '未填报', '已填报'].includes(this.detail.businessStatus)
    },
    isJPY() {
      return this.detail.ccyCode === 'JPY'
    },
  },

  methods: {
    editRemark(item) {
      // console.log(item)
      this.editRemarkDetails = item
      this.dialog1 = true
    },
    saveRemark() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1 = false
    },
    async save() {
      this.loading = true
      // 检查是否有价格高于协议价的情况
      const overPriceItem = this.detail.detailList.find(
        (item) =>
          item.haveMainPrice && Number(item.price) > Number(item.originalPrice),
      )
      if (overPriceItem) {
        this.$dialog.message.error(
          `"${overPriceItem.itemName}"价格高于原协议价，请修改后再提交`,
        )
        this.loading = false
        this.saveDraftDialog = false
        return
      }
      this.detail.detailList = this.detail.detailList.map((i) => ({
        ...i,
        operationType: 2,
      }))
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuoteSaveOrUpdate',
        this.detail,
      )
      this.loading = false
      if (errorRaw) return false
      this.$dialog.message.success('保存草稿成功')
      this.saveDraftDialog = false
      setTimeout(() => {
        this.closeAndTo(this.backRouteName)
      }, 1000)
    },
    async submit() {
      if (!this.$refs.form.validate() || !this.$refs.form2.validate()) {
        this.$dialog.message.error('请完善必填项')
        this.loading = false
        this.saveDraftDialog = false
        return
      }
      // 检查是否有价格高于协议价的情况
      const overPriceItem = this.detail.detailList.find(
        (item) =>
          item.haveMainPrice && Number(item.price) > Number(item.originalPrice),
      )
      if (overPriceItem) {
        this.$dialog.message.error(
          `"${overPriceItem.itemName}"价格高于原协议价，请修改后再提交`,
        )
        this.loading = false
        this.saveDraftDialog = false
        return
      }
      const zeroItems = this.detail.detailList.filter(
        (detail) => !detail.price || detail.price === 0,
      )
      if (zeroItems.length > 0) {
        const itemNames = zeroItems.map((detail) => detail.itemName).join('，')
        const confirmMsg = `
        <div style="padding: 10px; line-height: 1.5">
        <div style="color: #3399CC; font-weight: bold; margin-bottom: 8px">⚠️ 以下物料报价为0，是否确认提交?:</div>
        <div style="color: #424242; margin: 8px 0; padding-left: 12px">${itemNames}</div>
      </div>`
        if (!(await this.$dialog.msgbox.confirm(confirmMsg))) {
          return
        }
      }
      this.loading = true
      this.detail.detailList = this.detail.detailList.map((i) => ({
        ...i,
        operationType: 2,
      }))
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuoteSubmit',
        this.detail,
      )
      this.loading = false
      if (errorRaw) return false
      if (!errorRaw) {
        this.$dialog.message.success('报价已提交至船东，请等待开标结果。')
      }
      this.saveDraftDialog = false
      setTimeout(() => {
        this.closeAndTo(this.backRouteName)
      }, 1000)
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuoteDetailById',
        {
          quoteId: this.$route.params.id,
        },
      )
      if (data.businessStatus == '未填报') {
        delete data.invoiceType
        data.taxRate = 0
      }
      // 保存原始价格
      if (data.detailList) {
        data.detailList = data.detailList.map((item) => {
          if (item.haveMainPrice) {
            item.originalPrice = item.price // 保存原始协议价
          }
          return item
        })
      }
      this.detail = data
      this.loadhEADER()
      this.detail.filledBy = this.$local.data.get('userInfo').nickName
      this.$refs.form.resetValidation()
    },
    async importSuccess() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuoteDetailById',
        {
          quoteId: this.$route.params.id,
        },
      )
      if (data.businessStatus == '未填报') {
        delete data.invoiceType
      }
      this.detail.detailList = data.detailList
    },
    openSaveDraftDialog() {
      this.saveDraftDialog = true
    },
    async loadhEADER() {
      const { data } = await this.getAsync(
        '/business/common/ship/getPayCompanyHeader',
        {
          code: this.detail.shipInfoDO.shipCode,
        },
      )
      if (data) {
        this.inHeader = data
      }
      console.log(data)
    },
    validatePrice(value, item) {
      if (!value) return
      let numValue = Number(value)
      if (this.isJPY) {
        numValue = Math.floor(numValue)
      } else {
        numValue = Math.floor(numValue * 10000) / 10000
      }
      if (item.haveMainPrice && numValue > Number(item.originalPrice)) {
        //numValue = Number(item.originalPrice)
      }
      item.price = numValue
    },
  },

  mounted() {
    this.loadDetail()
    this.loadhEADER()
  },
}
</script>

<style>
/* 隐藏宽度为60px的按钮 */
button[style*='width: 60px;'] {
  display: none;
}

/* 隐藏宽度为80px的按钮 */
button[style*='width: 80px;'] {
  display: none;
}

/* 隐藏所有数字输入框的增减按钮 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
/* Firefox 兼容 */
/* input[type='number'] {
  -moz-appearance: textfield;
} */
</style>
