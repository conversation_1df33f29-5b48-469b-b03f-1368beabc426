<template>
  <v-container fluid>
    <v-detail-view
      :title="`调减入库-${detail.inoutCode}`"
      :tooltip="detail.inoutCode"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['物料入库:调减入库']"
    >
      <template
        v-if="detail.auditParams && detail.auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="!!detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <!-- TODO:
              根据用户信息读取用户所在部门
               -->
              <v-col cols="12" md="3">
                <v-ship-dept
                  outlined
                  dense
                  label="申请部门"
                  :disabled="!detail.shipCode"
                  v-model="detail.deptName"
                  :rules="[rules.required]"
                  :items="['甲板部', '轮机部']"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-station
                  v-model="handlerPost"
                  :disabled="$local.data.get('userInfo').userType == '2'"
                ></v-ship-station>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template #入库明细>
        <v-form ref="form2">
          <v-table-list :headers="detailHeaders" :items="detail.detailList">
            <template v-slot:[`item.stocksInOutNumber`]="{ item }">
              <v-text-field
                label="到货数量"
                v-model="item.stocksInOutNumber"
                single-line
                dense
                :rules="[rules.int, item.max]"
              ></v-text-field>
            </template>
            <template v-slot:[`item.depositoryId`]="{ item }">
              <v-select
                label="所在库位"
                v-model="item.depositoryId"
                single-line
                dense
                :items="depository"
                :rules="[rules.required]"
              ></v-select>
            </template>
          </v-table-list>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'materials-reduce-in-detail',
  created() {
    this.backRouteName = 'materials-in-list'
    this.subtitles = ['基本信息', '入库明细']
    this.detailHeaders = [
      { text: '物料名', value: 'itemName' },
      { text: '物料号', value: 'itemNumber' },
      { text: '批次号', value: 'itemBatch' },
      { text: '所在库位', value: 'depositoryId', width: 200 },
      { text: '订单数量', value: 'purchaseNum', width: 200 },
      { text: '实际到货数', value: 'stocksInOutNumber', width: 200 },
    ]
  },
  data() {
    return {
      detail: {
        detailList: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        int: (v) => /^\d+?$/.test(v) || '请输入整数数字',
      },
      handlerPost: '',
    }
  },

  watch: {
    handlerPost: {
      handler: function (val) {
        this.loadDepository(val)
      },
      immediate: true,
    },
  },

  methods: {
    async save(goBack) {
      goBack()
    },

    async loadDepository(manager) {
      // TODO:如果是非船端角色则将搜索所有的库位
      const { data } = await this.getAsync(
        '/business/shipAffairs/Depository/list',
        {
          size: 99,
          current: 1,
          manager,
          shipCode: this.detail.shipCode,
          type: this.detail.isDockRepair ? 2 : 1,
        },
      )
      const { records } = data
      this.depository = records.map((i) => ({ text: i.name, value: i.id }))
      if (this.depository.length === 0)
        this.$dialog.message.error(
          '当前角色库位为空，无法操作，需选择船上岗位。',
        )
      this.detail.detailList = this.detail.detailList.map((i) => ({
        ...i,
        depositoryId: i.depositoryId || this.depository[0]?.value,
      }))
    },

    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseStockInoutById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.detailList = data.detailList.map((i) => ({
        ...i,
        max: (a) => i.purchaseNum >= a || '调减数量不得超过原数量',
      }))
      this.detail.shipCode = data.shipInfo.shipCode
    },

    async stockIn() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/initiateStockInDetailByOrderId',
        { orderId: this.detail.orderId },
      )
      if (data) {
        this.closeAndTo('materials-in-detail', { id: data })
      }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
