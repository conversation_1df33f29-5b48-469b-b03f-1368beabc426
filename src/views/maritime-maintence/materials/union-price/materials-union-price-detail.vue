<template>
  <v-container fluid>
    <v-detail-view
      title="新建协议价"
      tooltip="新建协议价"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['物料统一报价:新建批量报价']"
    >
      <template #custombtns>
        <!-- <v-btn
          @click="
            closeAndTo(
              backRouteName,
              { subjectId: $route.params.subjectId },
              {},
            )
          "
          color="secondary"
          small
          tile
          class="mx-1"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn> -->
      </template>

      <template #基本信息>
        <v-form ref="form">
          <v-card-text>
            <v-row>
              <!-- <v-col md="3" cols="12">
                <v-dict-select
                  v-model="detail.portType"
                  label="港口分类"
                  dictType="port_type"
                  :rules="[rules.required]"
                  required
                ></v-dict-select>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.currencyId"
                  :items="currencyInfo"
                  item-text="ccyName"
                  item-value="id"
                  label="币种"
                  dense
                  outlined
                  :rules="[rules.required]"
                  required
                ></v-select>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-dialog-select
                  label="供应商"
                  item-text="name"
                  item-value="id"
                  v-model="detail.supplierId"
                  :headers="supHeaders"
                  :rules="[rules.required]"
                  req-url="/business/shipAffairs/Supplier/list"
                  :search-remain="searchObj2"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="中文名称"
                        v-model="searchObj2.name"
                      ></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col> -->
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  label="有效开始时间"
                  v-model="detail.validStartTime"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  label="有效截止时间"
                  v-model="detail.validDeadline"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-file-input
                  outlined
                  dense
                  accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
                  label="导入EXCEL"
                  :rules="[rules.required]"
                  v-model="file"
                ></v-file-input>
              </v-col>
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <template #报价内容按钮>
        <v-btn
          :disabled="components.length === 0"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="exportExcel"
          v-permission="['物料统一报价:导出所选']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出所选
        </v-btn>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['物料统一报价:选择物料']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择物料
        </v-btn>
      </template>
      <template #报价内容>
        <v-table-list
          item-key="materialId"
          :headers="componentHeaders"
          :items="components"
        ></v-table-list>
      </template>
    </v-detail-view>
    <materials-select
      v-model="dialog"
      :components.sync="components"
      :subjectId="$route.params.subjectId"
    ></materials-select>
    <v-dialog v-model="dialogError" max-width="600">
      <template v-slot:default="dialogError">
        <v-card style="height: 320px">
          <v-card-title>
            错误信息提示
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialogError.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <div
              class="error-content"
              style="
                white-space: pre-wrap;
                font-family: monospace;
                color: #d32f2f;
                background: #ffebee;
                padding: 12px;
                border-radius: 4px;
                overflow-y: auto;
                max-height: 200px;
                font-size: 20px;
                line-height: 1.6;
                tab-size: 4;
              "
            >
              {{ errorStr }}
            </div>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import MaterialsSelect from './private/materials-select.vue'
export default {
  components: { MaterialsSelect },
  name: 'materials-union-price-detail',
  mixins: [currencyHelper],
  created() {
    // materials-union-price-list
    // this.backRouteName = ''
    if (this.$route.params.subjectId != undefined) {
      // subjectId: '1581991857076989953', //油漆费 materials-union-price-list01
      //  subjectId: '1581991857064407043', //化学品materials-union-price-list02
      // subjectId: '1581991856967938050', //缆绳materials-union-price-list03
      // subjectId: '1581991857072795650', //绑扎件materials-union-price-list04
      // subjectId: '其他', //其他materials-union-price-list05
      // console.log(this.$route.params.subjectId)
      if (this.$route.params.subjectId == '1581991857076989953') {
        this.backRouteName = 'materials-union-price-list01'
      }
      if (this.$route.params.subjectId == '1581991857064407043') {
        this.backRouteName = 'materials-union-price-list02'
      }
      if (this.$route.params.subjectId == '1581991856967938050') {
        this.backRouteName = 'materials-union-price-list03'
      }
      if (this.$route.params.subjectId == '1581991857072795650') {
        this.backRouteName = 'materials-union-price-list04'
      }
      if (this.$route.params.subjectId == '其他') {
        this.backRouteName = 'materials-union-price-list05'
      }
    }
    this.subtitles = ['基本信息', '报价内容']
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
  },
  data() {
    return {
      detail: {},
      searchObj2: {},
      file: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      components: [],
      dialog: false,
      componentHeaders: [
        { text: '物料名称', value: 'nameCn' },
        { text: '物料编码', value: 'code' },
        { text: '物料描述', value: 'description' },
        // { text: '物料型号', value: 'model' },
        // { text: '物料规格', value: 'specs' },
        { text: '物料单位', value: 'unit' },
      ],
      dialogError: false,
      errorStr: '',
    }
  },

  methods: {
    async loadDetail() {
      //   const { data } = await this.getAsync('', { id: this.$$route.params.id })
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) return
      let formData = new FormData()
      formData.append('file', this.file, false)
      formData.append('itemType', '02')
      if (this.detail) {
        Object.keys(this.detail).forEach((key) => {
          formData.append(key, this.detail[key])
        })
      }
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchasePrice/purchasePriceTableImportMaterial',
        formData,
      )
      if (errorRaw) {
        this.errorStr = errorRaw.msg
        this.dialogError = true
      }
      if (!errorRaw) goBack()
    },

    createCom() {
      this.searchObj = {
        equipmentInformationId: this.detail.equipmentId,
        stopUse: false,
      }
      this.dialog = true
    },
    async exportExcel() {
      const items = this.components.map((i) => i.materialId)
      const { errorRaw } = await this.blobDownload(
        '/business/shipAffairs/purchasePrice/purchasePriceTableExport',
        { items, itemType: '02' },
        '物料批量报价导入模板.xlsx',
      )
      if (errorRaw) this.$dialog.message.error(errorRaw)
    },
  },

  mounted() {
    // if (this.$route.params.subjectId != undefined) {
    //   // subjectId: '1581991857076989953', //油漆费 materials-union-price-list01
    //   //  subjectId: '1581991857064407043', //化学品materials-union-price-list02
    //   // subjectId: '1581991856967938050', //缆绳materials-union-price-list03
    //   // subjectId: '1581991857072795650', //绑扎件materials-union-price-list04
    //   // subjectId: '其他', //其他materials-union-price-list05
    //   // console.log(this.$route.params.subjectId)
    //   if (this.$route.params.subjectId == '1581991857076989953') {
    //     this.backRouteName = 'materials-union-price-list01'
    //   }
    //   if (this.$route.params.subjectId == '1581991857064407043') {
    //     this.backRouteName = 'materials-union-price-list02'
    //   }
    //   if (this.$route.params.subjectId == '1581991856967938050') {
    //     this.backRouteName = 'materials-union-price-list03'
    //   }
    //   if (this.$route.params.subjectId == '1581991857072795650') {
    //     this.backRouteName = 'materials-union-price-list04'
    //   }
    //   if (this.$route.params.subjectId == '其他') {
    //     this.backRouteName = 'materials-union-price-list05'
    //   }
    // }
    this.loadDetail()
  },
}
</script>

<style></style>
