<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="4">
                <v-text-field
                  v-model="formData.itemName"
                  label="物料名称"
                  :rules="[rules.required]"
                  required
                  disabled
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <!-- <v-col md="2" cols="12">
                <v-dict-select
                  v-model="formData.portType"
                  label="港口分类"
                  dictType="port_type"
                  :rules="[rules.required]"
                  required
                ></v-dict-select>
              </v-col> -->
              <v-col cols="12" md="2">
                <v-select
                  v-model="formData.currencyId"
                  :items="currencyInfo"
                  item-text="ccyName"
                  item-value="id"
                  label="币种"
                  dense
                  outlined
                  :rules="[rules.required]"
                  required
                ></v-select>
              </v-col>
              <!-- <v-col cols="12" md="2">
                <v-dialog-select
                  label="供应商"
                  item-text="name"
                  item-value="id"
                  v-model="formData.supplierId"
                  :headers="supHeaders"
                  :rules="[rules.required]"
                  req-url="/business/shipAffairs/Supplier/list"
                  :init-selected="formData.initSup"
                  :search-remain="searchObj2"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="中文名称"
                        v-model="searchObj2.name"
                      ></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col> -->
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="formData.itemPrice"
                  label="价格"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  dense
                  label="有效开始时间"
                  v-model="formData.validStartTime"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  outlined
                  dense
                  label="有效截止时间"
                  v-model="formData.validDeadline"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['物料统一报价:修改']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      @dbclick="editItem"
      :single-select="false"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="3" md="2">
          <v-select
            v-model="searchObj.remark"
            :items="materialTypes"
            label="物料分类"
            outlined
            dense
            clearable
          ></v-select>
        </v-col>
        <v-col cols="3" md="2">
          <v-select
            v-model="searchObj.currencyId"
            :items="currencyInfo"
            item-text="ccyName"
            item-value="id"
            label="币种"
            dense
            clearable
            outlined
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :to="{
            name: 'materials-union-price-detail',
            params: { subjectId: $route.meta.subjectId },
          }"
          v-permission="['物料统一报价:新建批量报价']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新建协议价
        </v-btn>
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editItem"
          v-permission="['物料统一报价:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn> -->
        <v-btn
          :disabled="selected.length == 0"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['物料统一报价:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'union-price-list',
  mixins: [currencyHelper],
  created() {
    this.tableName = '物料协议价'
    this.reqUrl = '/business/shipAffairs/purchasePrice/purchasePricePage'
    this.searchDicts = [
      {
        dicType: 'port_type',
        label: '港口分类',
        key: 'portType',
      },
    ]
    this.headers = [
      { text: '物料名', value: 'itemName' },
      { text: '币种', value: 'ccyName' },
      { text: '版本时间', value: 'createTime' },
      // { text: '港口分类', value: 'portType' },
      // { text: '供应商名称', value: 'supplierName' },
      { text: '有效开始时间', value: 'validStartTime' },
      { text: '有效截止时间', value: 'validDeadline' },
      { text: '价格', value: 'itemPrice' },
      // { text: '备注', value: 'remark' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.fuzzyLabel = '物料名'
    this.searchDate = {
      label: '有效期截至',
      interval: true,
    }
  },

  data() {
    return {
      searchObj: { itemType: '02', subjectId: this.$route.meta.subjectId },
      searchObj2: {},
      selected: [],
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      materialTypes: [],
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除所选记录？'))) return
      // const { errorRaw } = await this.getAsync(
      //   '/business/shipAffairs/purchasePrice/purchasePriceDeleteBatch',
      //   { id: this.selected.id },
      //   false,
      // )
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchasePrice/purchasePriceDeleteBatch2',
        this.selected.map((item) => item.id),
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = []
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected[this.selected.length - 1],
        initSup: {
          name: this.selected[this.selected.length - 1].supplierName,
          id: this.selected[this.selected.length - 1].supplierId,
        },
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl =
        '/business/shipAffairs/purchasePrice/purchasePriceSaveOrUpdate'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
    async loadMaterialTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        {
          page: 1,
          size: 1000,
          ...this.searchObj,
        },
      )
      this.materialTypes = data.records.map((i) => {
        return {
          text: i.cateName,
          value: i.id,
        }
      })
    },
  },

  mounted() {
    if (this.$route.params.subjectId != undefined) {
      console.log(this.$route.params.subjectId)
    }
    this.loadMaterialTypes()
  },
}
</script>

<style></style>
