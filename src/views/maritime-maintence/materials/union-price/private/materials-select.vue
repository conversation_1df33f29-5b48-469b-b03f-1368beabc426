<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        物料选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          fuzzy-label="模糊搜索"
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="3">
              <v-select
                v-model="searchObj.type"
                outlined
                dense
                clearable
                label="物料分类"
                :items="types"
              ></v-select>
            </v-col>
          </template>
          <template #btns></template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" @click="chooseAll">全选</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
// materialCategory	物料分类	string
// materialCnName	物料中文名称	string
// materialCode	物料编码	string
// materialDescription	物料描述	string
// materialEnName	物料英文名称	string
// materialId	物料id	string
// materialModel	物料型号	string
// materialSpec	物料规格	string
// materialUnit	物料单位	string
export default {
  name: 'materials-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/shipAffairs/MaterialInfo/list'
    this.headers = [
      { text: '物料名称', value: 'nameCn' },
      { text: '物料编码', value: 'code' },
      { text: '物料描述', value: 'description' },
      // { text: '物料型号', value: 'model' },
      // { text: '物料规格', value: 'specs' },
      { text: '物料单位', value: 'unit' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    components: Array,
    type: String,
    subjectId: String,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      loading: false,
      searchObj: { type: '', subjectId: this.subjectId, stopUse: false },
      selected: [],
      types: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    type(val) {
      this.searchObj.type = val
    },
    searchRemain(val) {
      this.searchObj = val
    },
    components(val) {
      this.selected = val.map((i) => {
        return { ...i, vid: i.id, id: i.materialId }
      })
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const components = this.selected.map((i) => {
        const comp = {
          ...i,
          materialId: i.id,
          id: i.vid,
        }
        return comp
      })
      this.$emit('update:components', components)
      this.$emit('change', false)
    },
    async chooseAll() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/allList',
        {
          type: this.searchObj.type,
          subjectId: this.subjectId,
          stopUse: false,
        },
      )
      const components = data.map((i) => {
        const comp = {
          ...i,
          materialId: i.id,
          id: i.vid,
        }
        return comp
      })
      this.$emit('update:components', components)
      this.$emit('change', false)
    },
    async loadTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        { current: 1, size: 1000, subjectId: this.subjectId },
        false,
      )
      this.types = data.records.map((item) => ({
        ...item,
        value: item.id,
        text: item.cateName,
      }))
    },
  },
  mounted() {
    this.loadTypes()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
