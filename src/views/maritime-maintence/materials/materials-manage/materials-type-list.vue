<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          v-permission="['物料分类管理:修改']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '修改' : '新增' }}
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="formData.cateName"
                  :rules="[rules.required]"
                  outlined
                  dense
                  label="二级分类"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-dict-select
                  dict-type="material_first_class_new"
                  v-model="formData.belongCode"
                  :rules="[rules.required]"
                  outlined
                  @update="updateBelongCode"
                  dense
                  label="一级分类"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dialog-select
                  :req-url="sapReqUrl"
                  label="费用科目"
                  v-model="formData.sapCode"
                  :rules="[rules.required]"
                  :init-selected="initSubject"
                  :search-remain="searchObj"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  required
                  dense
                  :search-dicts="sapSearchDicts"
                  fuzzy-label="科目名称/编号"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dialog-select
                  :req-url="sapReqUrl"
                  label="坞修时费用科目"
                  v-model="formData.dockSubjectId"
                  :rules="[rules.required]"
                  :init-selected="initDockSubject"
                  :search-remain="searchObj2"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  required
                  dense
                  :search-dicts="sapSearchDicts"
                  fuzzy-label="科目名称/编号"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12">
                <v-text-field
                  v-model="formData.remark"
                  outlined
                  dense
                  label="备注"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :search-dicts="searchDicts"
      :fix-header="false"
      @dbclick="editItem"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['物料分类管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editItem"
          v-permission="['物料分类管理:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['物料分类管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>

      <!-- <template v-slot:[`item.belongCode`]="{ item }">
        {{ item.belongCode }}1213
      </template> -->
    </v-table-searchable>
  </v-container>
</template>
<script>
// belongCode	上级分类	string
// cateName	物料分类名称	string
// id	物理主键	string
// remark	备注	string
// sapCode	SAP代码	string
export default {
  name: 'materials-type-list',
  created() {
    this.tableName = '物料分类管理'
    this.reqUrl = '/business/shipAffairs/MaterialInfo/cate/page'
    this.sapReqUrl = '/business/shipAffairs/costSubject/page'
    this.searchDicts = [
      {
        dicType: 'material_first_class_new',
        label: '一级分类',
        key: 'belongCode',
      },
    ]
    this.headers = [
      { text: '二级分类', value: 'cateName' },
      { text: '一级分类', value: 'belongCode' },
      { text: '科目', value: 'subjectName' },
      { text: '坞修时科目', value: 'dockSubjectName' },
      { text: '备注', value: 'remark' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.sapSearchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      initSubject: {},
      initDockSubject: {},
      searchObj: { subjectType: '', materialsApply: '物料申请' },
      searchObj2: { subjectType: '坞修费', materialsApply: '物料申请' },
    }
  },
  watch: {
    // 'formData.belongCode'() {
    //   if (this.formData.belongCode == 0) {
    //     this.searchObj.subjectType = '招标物料费'
    //     this.initSubject = {}
    //   } else if (this.formData.belongCode == 1) {
    //     this.searchObj.subjectType = '应急物料费'
    //     this.initSubject = {}
    //   } else {
    //     this.searchObj.subjectType = ''
    //     this.initSubject = {}
    //   }
    // },
  },
  methods: {
    updateBelongCode() {
      if (this.formData.belongCode == 0) {
        this.searchObj.subjectType = '招标物料费'
        this.initSubject = {}
      } else if (this.formData.belongCode == 1) {
        this.searchObj.subjectType = '应急物料费'
        this.initSubject = {}
      } else {
        this.searchObj.subjectType = ''
        this.initSubject = {}
      }
    },
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/delete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected }
      // initSubject
      this.initDockSubject = {
        subjectName: this.selected.dockSubjectName,
        id: this.selected.dockSubjectId,
      }
      // initDockSubject
      this.initSubject = {
        subjectName: this.selected.subjectName,
        id: this.selected.sapCode,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/MaterialInfo/cate/saveOrUpdate'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.resetValidation()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
