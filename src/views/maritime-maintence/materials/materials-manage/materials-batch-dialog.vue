<template>
  <v-dialog
    attach="#mask"
    @input="(val) => $store.commit('setMaskLayer', val)"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        Excel导入物料基础信息
        <v-spacer></v-spacer>
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="dowExcel"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          导出模板
        </v-btn>
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="importExcel"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          导入
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" sm="6" md="2">
                <v-select
                  v-model="formData.type"
                  outlined
                  dense
                  clearable
                  label="物料分类"
                  :items="types"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-file-input
                  outlined
                  dense
                  accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
                  label="导入EXCEL"
                  v-model="file"
                ></v-file-input>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'materials-batch-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {},
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      draw: {},
      searchObj: { efFlag: 1, nickName: '', deptId: '1', paymentCompany: '' },
      file: null,
      types: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async dowExcel() {
      // const items = this.components.map((i) => i.materialId)
      const { errorRaw } = await this.blobDownload(
        '/business/shipAffairs/MaterialInfo/dowExcel',
        '物料基础信息导入模板.xlsx',
      )
      if (errorRaw) this.$dialog.message.error(errorRaw)
    },
    async importExcel() {
      if (this.formData.type == null) {
        this.$dialog.message.error('请选择物料分类！')
        return
      }
      if (this.file == null) {
        this.$dialog.message.error('请上传附件！')
        return
      }
      this.$emit('change', false)
      let formData = new FormData()
      formData.append('file', this.file)
      formData.append('type', this.formData.type)
      const { data } = await this.postAsync(
        '/business/shipAffairs/MaterialInfo/excelImportNew',
        formData,
      )
      if (data) {
        this.$dialog.message.success('保存成功')
        this.formData = {}
        this.file = null
        this.$emit('change', false)
      }
    },
    async loadTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        { current: 1, size: 1000 },
        false,
      )
      this.types = data.records.map((item) => ({
        ...item,
        value: item.id,
        text: item.cateName,
      }))
    },
  },
  mounted() {
    this.loadTypes()
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
