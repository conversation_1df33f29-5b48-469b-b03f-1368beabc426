<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          small
          v-permission="['物料管理:修改']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '修改' : '新增' }}
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-1 px-0" fluid>
            <v-row>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-for="(h, i) in headers"
                :key="i"
              >
                <v-select
                  v-if="h.value === 'type'"
                  v-model="formData.type"
                  :items="types"
                  outlined
                  return-object
                  dense
                  label="物料分类"
                  :rules="[rules.required]"
                ></v-select>
                <v-text-field
                  v-else
                  outlined
                  dense
                  v-model="formData[h.value]"
                  :label="h.text"
                  :rules="h.required ? [rules.required] : []"
                  :disabled="isEdit"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      fuzzy-label="模糊搜索"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      @dbclick="editItem"
      :showExportButton="true"
      :specialHeaders="specialHeaders"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.type"
            outlined
            dense
            clearable
            label="物料分类"
            :items="types"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            label="状态"
            v-model="searchObj.stopUse"
            dense
            outlined
            :items="[
              { text: '停用', value: true },
              { text: '启用', value: false },
            ]"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateStopUse"
          v-permission="['物料管理:变更状态']"
        >
          <v-icon left>mdi-pencil</v-icon>
          变更状态
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="openBatch"
          v-permission="['物料管理:Excel导入']"
        >
          <v-icon left>mdi-file-import-outline</v-icon>
          Excel导入基础信息
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['物料管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['物料管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.type`]="{ item }">
        {{
          types.find((t) => t.value === item.type)
            ? types.find((t) => t.value === item.type).text
            : ''
        }}
      </template>
      <template v-slot:[`item.stopUse`]="{ item }">
        <span style="color: red" v-if="item.stopUse == 1">停用</span>
        <span v-if="item.stopUse == 0">启用</span>
      </template>
    </v-table-searchable>
    <materialsBatchDialog
      v-model="dialogBatch"
      @success="success"
    ></materialsBatchDialog>
    <v-dialog v-model="updateUseDialog" max-width="600">
      <template v-slot:default="updateUseDialog">
        <v-card style="height: 320px">
          <v-card-title>
            修改状态
            <v-spacer></v-spacer>
            <v-btn
              :loading="loading"
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveStopUse"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              保存
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="updateUseDialog.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-text-field
                label="物料名称"
                v-model="updateUseData.nameCn"
                dense
                readonly
                required
                outlined
              ></v-text-field>
            </v-row>
            <v-row>
              <v-text-field
                label="IMPA编码"
                v-model="updateUseData.nameEn"
                dense
                readonly
                required
                outlined
              ></v-text-field>
            </v-row>
            <v-row>
              <v-select
                label="状态"
                v-model="updateUseData.stopUse"
                dense
                outlined
                :items="[
                  { text: '停用', value: true },
                  { text: '启用', value: false },
                ]"
              ></v-select>
            </v-row>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import materialsBatchDialog from './materials-batch-dialog.vue'
export default {
  components: { materialsBatchDialog },
  name: 'materials-list',
  created() {
    this.tableName = '物料管理'
    this.reqUrl = '/business/shipAffairs/MaterialInfo/list'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    //     brand	品牌	string
    // code	物料编码	string
    // description	物料描述	string
    // id	物理主键	string
    // isUnified	是否统一采购	boolean
    // model	型号	string
    // nameCn	物料名称	string
    // nameEn	物料英文名称	string
    // price	物料价格	number
    // remark	备注	string
    // specs	规格	string
    // type	物料分类	string
    // unit	单位	string
    this.headers = [
      { text: '物料名称', value: 'nameCn', required: true },
      { text: 'IMPA编码', value: 'nameEn' },
      // { text: '物料编码', value: 'code', required: true },
      { text: '物料编码', value: 'code' },
      { text: '物料描述', value: 'description' },
      // { text: '品牌', value: 'brand' },
      // { text: '型号', value: 'model' },
      // { text: '规格', value: 'specs' },
      { text: '单位', value: 'unit' },
      { text: '物料分类', value: 'type' },
      { text: '状态', value: 'stopUse' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      types: [],
      searchObj: {},
      dialogBatch: false,
      typeListSpecial: [],
      specialHeaders: [
        {
          text: 'type',
          value: this.typeListSpecial,
        },
        {
          text: 'stopUse',
          value: [
            { text: true, value: '停用' },
            { text: false, value: '启用' },
          ],
        },
      ],
      updateUseDialog: false,
      updateUseData: {},
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/MaterialInfo/deleteById/${this.selected.id}`,
        {},
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = { ...this.selected, type: { value: this.selected.type } }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = this.isEdit
        ? '/business/shipAffairs/MaterialInfo/update'
        : '/business/shipAffairs/MaterialInfo/insert'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        {
          ...this.formData,
          costSubjectId: this.formData.type.sapCode,
          type: this.formData.type.id,
        },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },

    async loadTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        { current: 1, size: 1000 },
        false,
      )
      this.types = data.records.map((item) => ({
        ...item,
        value: item.id,
        text: item.cateName,
      }))
      this.typeListSpecial = data.records.map((item) => ({
        value: item.cateName,
        text: item.id,
      }))
      console.log(this.typeListSpecial)
      this.specialHeaders = [
        {
          text: 'type',
          value: this.typeListSpecial,
        },
      ]
    },
    openBatch() {
      this.dialogBatch = true
    },
    updateStopUse() {
      this.updateUseData = {
        ...this.selected,
      }
      console.log(this.updateUseData)
      this.updateUseDialog = true
    },
    async saveStopUse() {
      const url = '/business/shipAffairs/MaterialInfo/updateStopUse'
      const { errorRaw } = await this.postAsync(url, {
        ...this.updateUseData,
      })
      this.loading = false
      if (!errorRaw) {
        this.updateUseDialog = false
        await this.$refs.table.loadTableData()
        await this.$nextTick()
      }
    },
  },

  mounted() {
    this.loadTypes()
  },
}
</script>

<style></style>
