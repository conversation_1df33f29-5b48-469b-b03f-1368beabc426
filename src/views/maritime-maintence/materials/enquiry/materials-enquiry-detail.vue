<template>
  <v-container fluid>
    <v-detail-view
      :title="`物料询价-${detail.enquiryNo || '新增'}`"
      :tooltip="detail.enquiryNo || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        detail.auditParams && detail.auditParams.taskId && detail.status == 2
      "
      @save="save"
      @submit="submit"
      v-permission="['物料询价:编辑']"
      :can-save="false"
    >
      <template v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          v-if="!isEdit"
          width="90"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          :loading="loadingSubmit"
          v-permission="['物料询价:发起询价']"
        >
          发起询价
        </v-btn>
        <v-btn
          v-if="
            detail.businessStatus == '超期' ||
            detail.businessStatus == '报价完成' ||
            detail.businessStatus == '用户开标' ||
            detail.businessStatus == '重新定标'
          "
          tile
          color="primary"
          small
          class="mx-1"
          @click="updateIsWinsJw"
          :loading="quoteLoading"
          v-permission="['物料询价:定标（机务）']"
        >
          <v-icon left>mdi-check</v-icon>
          定标（机务）
        </v-btn>
        <v-btn
          v-if="detail.businessStatus == '待商务主管定标'"
          tile
          color="primary"
          small
          class="mx-1"
          @click="updateIsWinsSw"
          :loading="quoteLoading"
          v-permission="['物料询价:定标（商务）']"
        >
          <v-icon left>mdi-check</v-icon>
          定标（物料采购主管）
        </v-btn>
        <v-btn
          v-if="detail.businessStatus == '待商务主管定标'"
          tile
          color="error"
          small
          class="mx-1"
          @click="returnJw"
          :loading="quoteLoading"
          v-permission="['物料询价:退回']"
        >
          <v-icon left>mdi-check</v-icon>
          退回
        </v-btn>
        <v-btn
          v-if="
            detail.businessStatus == '报价完成' ||
            detail.businessStatus == '超期' ||
            detail.businessStatus == '用户开标' ||
            detail.businessStatus == '重新定标'
          "
          tile
          color="error"
          small
          class="mx-1"
          @click="returnJw"
          :loading="quoteLoading"
          v-permission="['物料询价:退回重新询价']"
        >
          <v-icon left>mdi-check</v-icon>
          退回重新询价
        </v-btn>
        <v-btn
          v-if="
            detail.businessStatus == '超期无报价' ||
            detail.businessStatus == '机务主管退回'
          "
          tile
          color="primary"
          small
          class="mx-1"
          @click="resave"
          :loading="quoteLoading"
          v-permission="['物料询价:重新询价']"
        >
          <v-icon left>mdi-check</v-icon>
          重新发起询价
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="form">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <apply-selelct
                  :readonly="isEdit"
                  v-model="detail.applyId"
                  :rules="[rules.required]"
                  :initSelected="initApply"
                  :shipCode="detail.shipCode"
                ></apply-selelct>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  :disabled="detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  readonly
                  dense
                  outlined
                  v-model="detail.applyType"
                  label="申请类型"
                  :items="applyTypes"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <port-select-dialog
                  :readonly="isEdit || canBid"
                  :disabled="!detail.applyId"
                  v-model="detail.receivePortId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  outlined
                  label="交货日期"
                  v-model="detail.receiveDate"
                  :rules="[rules.required]"
                  :readonly="
                    isEdit &&
                    detail.businessStatus != '超期无报价' &&
                    detail.businessStatus != '通导信息主管退回' &&
                    detail.businessStatus != '机务主管退回' &&
                    detail.businessStatus != '报价完成' &&
                    detail.businessStatus != '超期' &&
                    detail.businessStatus != '重新定标' &&
                    detail.businessStatus != '用户开标' &&
                    detail.status != 4
                  "
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  outlined
                  label="创建日期"
                  v-model="detail.createDate"
                  use-today
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col
                v-if="
                  isEdit &&
                  detail.businessStatus != '超期无报价' &&
                  detail.businessStatus != '机务主管退回'
                "
                cols="12"
                md="3"
              >
                <vs-date-picker
                  dense
                  outlined
                  label="报价起始时间"
                  v-model="detail.startTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col
                v-if="
                  isEdit &&
                  detail.businessStatus != '超期无报价' &&
                  detail.businessStatus != '机务主管退回'
                "
                cols="12"
                md="3"
              >
                <vs-date-picker
                  dense
                  outlined
                  label="报价截至时间"
                  v-model="detail.endTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col
                v-if="
                  (isEdit && detail.businessStatus == '超期无报价') ||
                  (isEdit && detail.businessStatus == '机务主管退回')
                "
                cols="12"
                md="3"
              >
                <vs-date-picker
                  dense
                  outlined
                  label="原询价单报价起始时间"
                  v-model="detail.startTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col
                v-if="
                  (isEdit && detail.businessStatus == '超期无报价') ||
                  (isEdit && detail.businessStatus == '机务主管退回')
                "
                cols="12"
                md="3"
              >
                <vs-date-picker
                  dense
                  outlined
                  label="原询价单报价截至时间"
                  v-model="detail.endTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="询价人"
                  dense
                  outlined
                  readonly
                  v-model="detail.inquier"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="中标项目折算美金合计（开标后自动计算）"
                  dense
                  outlined
                  :readonly="true"
                  v-model="detail.total"
                ></v-text-field>
              </v-col>
              <!--          <v-col v-if="false" cols="12" md="12">    -->
              <v-col v-if="false" cols="12" md="12">
                <v-textarea
                  label="申请单备注"
                  dense
                  outlined
                  v-model="detail.remark3"
                  :readonly="true"
                ></v-textarea>
              </v-col>
              <v-col v-if="false" cols="12" md="12">
                <v-attach-list
                  :attachments="detail.attachmentRecords2"
                  title="船舶附件列表"
                  :readonly="true"
                ></v-attach-list>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="项目说明"
                  dense
                  outlined
                  v-model="detail.remark"
                  :readonly="
                    !(
                      idIsNew ||
                      detail.businessStatus == '未提交' ||
                      detail.businessStatus == '超期' ||
                      detail.businessStatus == '报价完成' ||
                      detail.status == 4 ||
                      detail.businessStatus == '待商务主管定标' ||
                      detail.businessStatus == '超期无报价' ||
                      detail.businessStatus == '机务主管退回' ||
                      detail.businessStatus == '通导信息主管退回'
                    )
                  "
                ></v-textarea>
              </v-col>
              <v-col v-if="isEdit" cols="12" md="12">
                <v-textarea
                  label="议价记录"
                  dense
                  outlined
                  v-model="detail.remark1"
                  :readonly="detail.businessStatus != '待商务主管定标'"
                ></v-textarea>
              </v-col>
              <v-col v-if="isEdit" cols="12" md="12">
                <v-attach-list
                  :attachments="detail.attachmentRecords1"
                  :disabled="isDisabled"
                  title="议价附件列表"
                  @change="updateAttachments"
                  :readonly="isAttachmentDisabled"
                ></v-attach-list>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="2">
                <b>申请部门:</b>
                {{ applyInfo.applyDept }}
              </v-col>
              <v-col cols="12" md="2">
                <b>坞修:</b>
                {{ applyInfo.isDockRepair ? '是' : '否' }}
              </v-col>
            </v-row>
            <v-row
              v-if="
                !isEdit ||
                detail.businessStatus == '超期无报价' ||
                detail.businessStatus === '机务主管退回'
              "
            >
              <v-col cols="12">
                <enquiry-form
                  ref="enquiry"
                  @close="formShow = false"
                  :shipCode="detail.shipCode"
                  :happenDate="detail.receiveDate"
                ></enquiry-form>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template #询价详情按钮 v-if="canAddItem">
        <v-btn
          :disabled="!applyInfo.detailList"
          tile
          small
          color="success"
          class="mx-1"
          @click="enquiryDialog = true"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择询价物料
        </v-btn>
      </template>
      <template #询价详情>
        <v-table-list
          :show-select="false"
          v-model="selectedEn"
          :items="enquiryList"
          :headers="enquiryHeaders"
          item-key="itemId"
        >
          <template v-if="canAddItem" v-slot:[`item.enquiryNum`]="{ item }">
            <v-text-field
              v-model="item.enquiryNum"
              label="询价数量"
              type="number"
              :rules="[rules.decimal]"
              single-line
              dense
            ></v-text-field>
          </template>
          <template v-if="canAddItem" v-slot:[`item.remark4`]="{ item }">
            <v-text-field
              v-model="item.remark4"
              label="申请理由"
              single-line
              dense
            ></v-text-field>
          </template>
          <template v-if="canAddItem" v-slot:[`item.remark`]="{ item }">
            <v-text-field
              v-model="item.remark"
              label="备注"
              single-line
              dense
            ></v-text-field>
          </template>
          <template v-slot:[`item.actions`]="{ item }">
            <v-icon small class="mr-2" @click="getHis(item)">
              mdi-clipboard-text-clock-outline
            </v-icon>
          </template>
        </v-table-list>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-if="canBid" #报价详情按钮>
        <!-- <v-btn
          :disabled="!selectedQuote"
          outlined
          tile
          color="primary"
          small
          class="mx-1"
          @click="quoteBid"
          :loading="quoteLoading"
          v-permission="['物料询价:定标']"
        >
          <v-icon left>mdi-check</v-icon>
          定标
        </v-btn> -->
      </template>
      <template #报价详情 v-if="isEdit">
        <div class="d-flex align-center mb-2">
          <div class="text-h6">报价详情</div>
          <v-btn
            v-if="
              detail.businessStatus == '超期' ||
              detail.businessStatus == '报价完成' ||
              detail.businessStatus == '用户开标' ||
              detail.businessStatus == '重新定标'
            "
            tile
            color="primary"
            small
            class="ml-4"
            @click="saveQuoteWins"
            :loading="quoteLoading"
            v-permission="['物料询价:退回重新询价']"
          >
            <v-icon left>mdi-content-save</v-icon>
            临时保存定标信息
          </v-btn>
        </div>
        <v-table-list
          v-if="
            !(
              detail.businessStatus == '询价中' ||
              detail.businessStatus == '重新报价'
            )
          "
          :show-select="canBid"
          v-model="selectedQuote"
          :headers="quoteHeaders"
          :items="quoteList"
        >
          <template v-slot:[`item.isWin`]="{ item }">
            {{ item.isWin ? '中标' : '不中标' }}
          </template>
          <template v-slot:[`item.actions`]="{ item }">
            <v-btn
              small
              text
              color="primary"
              :to="`/maritime-maintence/materials/materials-quote/detail/${item.id}`"
            >
              <v-icon small>mdi-link</v-icon>
              点我查看
            </v-btn>
          </template>
        </v-table-list>
        <compare-price-table
          v-if="
            !(
              detail.businessStatus == '询价中' ||
              detail.businessStatus == '重新报价' ||
              detail.businessStatus == '超期无报价' ||
              detail.businessStatus == '机务主管退回' ||
              detail.businessStatus == '通导信息主管退回'
            )
          "
          :quoteIds="filteredQuoteList"
          :businessStatus="detail.businessStatus"
          :enquiryId="detail.id"
          @refresh="refresh"
          @after-rebid="closeAndTo(backRouteName)"
          @update-win-status="updateQuoteWinStatus"
          ref="compareTable"
        ></compare-price-table>
      </template>
    </v-detail-view>
    <apply-item-selelct
      v-model="enquiryDialog"
      :items="applyInfo.detailList"
      :sitems.sync="enquiryList"
    ></apply-item-selelct>
    <v-dialog v-model="hisDialog" max-width="1200" hide-overlay attach="#mask">
      <v-card>
        <v-card-title class="text-h5">采购历史</v-card-title>
        <v-card-text>
          <v-table-searchable
            :show-select="false"
            outlined
            ref="table"
            :table-name="''"
            use-ship
            :headers="hisHeader"
            req-url="/business/shipAffairs/purchaseManage/getOrderInfoByItemId"
            :fix-header="false"
            :search-remain="searchObj"
          >
            <template #searchflieds>
              <v-col cols="12" md="4">
                <v-dialog-select
                  label="供应商"
                  item-text="name"
                  item-value="id"
                  v-model="searchObj.supplierId"
                  :headers="supHeaders"
                  req-url="/business/shipAffairs/Supplier/list"
                  fuzzy-label="供应商"
                  clearable
                  @clear="
                    () => {
                      searchObj.supplierId = ''
                    }
                  "
                ></v-dialog-select>
              </v-col>
            </template>
          </v-table-searchable>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="returnDialog"
      max-width="600"
      hide-overlay
      attach="#mask"
    >
      <v-card>
        <v-card-title class="text-h5">退回原因</v-card-title>
        <v-card-text>
          <v-form ref="returnForm">
            <v-textarea
              v-model="returnReason"
              label="请输入退回原因"
              outlined
              :rules="[rules.required]"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" text @click="returnDialog = false">取消</v-btn>
          <v-btn
            color="primary"
            text
            @click="confirmReturn"
            :loading="quoteLoading"
          >
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="showRemarkDialog"
      max-width="800"
      hide-overlay
      attach="#mask"
      persistent
    >
      <v-card>
        <v-card-title class="headline primary white--text">
          您选择了价格高于其余报价单的物料，请填写具体原因：
        </v-card-title>
        <v-card-text>
          <v-form ref="returnForm">
            <v-textarea
              v-model="highPriceReason"
              label="请输入具体原因"
              outlined
              :rules="[rules.required]"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" text @click="showRemarkDialog = false">
            取消
          </v-btn>
          <v-btn color="primary" text @click="quoteJw" :loading="quoteLoading">
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import EnquiryForm from '../../components/enquiry/enquiry-form.vue'
import PortSelectDialog from '../../components/port-select-dialog.vue'
import ApplyItemSelelct from './private/apply-item-selelct.vue'
import applySelelct from './private/apply-selelct.vue'
import ComparePriceTable from './private/compare-price-table.vue'
import VAttachList from '@/components/v-attach-list.vue'

const enquiryType = '02'

export default {
  components: {
    applySelelct,
    PortSelectDialog,
    ApplyItemSelelct,
    ComparePriceTable,
    EnquiryForm,
    VAttachList,
  },
  name: 'materials-enquiry-detail',
  created() {
    this.backRouteName = 'materials-enquiry-list'
    this.subtitles = ['基本信息', '询价详情', '报价详情']
    this.enquiryHeaders = [
      // TODO:物料号
      { text: '物料名称', value: 'nameCn' },
      { text: '物料编码', value: 'code' },
      { text: 'IMPA', value: 'nameEn' },
      { text: '物料型号', value: 'model' },
      { text: '审批数量', value: 'auditQuantity' },
      { text: '询价数量', value: 'enquiryNum', width: 140 },
      { text: '单位', value: 'unit' },
      { text: '申请理由', value: 'remark4' },
      { text: '采购历史', value: 'actions' },
    ]
    this.quoteHeaders = [
      { text: '报价单号', value: 'quoteNo' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '备货天数', value: 'stockUpDays' },
      //{ text: '是否中标', value: 'isWin' },
      { text: '总价', value: 'totalPrice' },
      { text: '币种', value: 'currencyName' },
      { text: '折算美金', value: 'toUsd' },
      //{ text: '报价单状态', value: 'businessStatus' },
      { text: '中标状态', value: 'winStatus' },
      { text: '报价附件', value: 'attachmentRecords' },
      { text: '链接', value: 'actions', width: 100 },
      // { text: '状态', value: 'businessStatus' },
    ]
    //     ccyCode		string
    // deliveryDate	交付日期	string
    // discount	折扣	number
    // orderId	采购订单id	string
    // price	单价	number
    // purchaseNum	订单购买数量	number
    // supplierName	供应商	string
    this.hisHeader = [
      { text: '船舶', value: 'shipInfo' },
      { text: '币种', value: 'ccyCode' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '单价', value: 'price' },
      { text: '折扣', value: 'discount' },
      { text: '订单购买数量', value: 'purchaseNum' },
      { text: '供应商', value: 'supplierName' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
    this.applyTypes = [
      { text: '常规', value: 1 },
      { text: '紧急', value: 2 },
      { text: '坞修', value: 3 },
      { text: '固定资产', value: 4 },
      { text: '通导固定资产', value: 5 },
    ]
  },
  mixins: [routerControl],
  data() {
    return {
      detail: {
        applyId: '',
        inquier: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        remark: '', // 现有的项目说明
        remark1: '', // 添加议价记录
        receiveDate: '',
      },
      initApply: {},
      initPort: {},
      enquiryList: [],
      applyInfo: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
      },
      selectedEn: false,
      enquiryDialog: false,
      engine: {},
      quoteList: [],
      quoteIds: [],
      history: [],
      selectedQuote: false,
      hisDialog: false,
      hisId: '',
      searchObj: { id: '' },
      quoteLoading: false,
      loadingSubmit: false,
      receiveDateModified: false,
      returnDialog: false,
      returnReason: '',
      showRemarkDialog: false,
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    idIsNew() {
      return this.$route.params.id == 'new'
    },
    canAddItem() {
      return (
        !this.isEdit ||
        ['未提交', '审批已驳回'].includes(this.detail.businessStatus) ||
        this.detail.status === '4'
      )
    },
    canBid() {
      return (
        ['超期', '报价完成'].includes(this.detail.businessStatus) &&
        this.detail.status === '1'
      )
    },
    filteredQuoteList() {
      if (this.detail.status == '1') {
        const filteredList = this.quoteList.filter(
          (item) =>
            String(item.businessStatus) === '已填报' ||
            String(item.businessStatus) === '中标' ||
            String(item.businessStatus) === '不中标',
        )
        return filteredList.map((item) => item.id)
      } else {
        return this.quoteList.map((item) => item.id)
      }
    },
    isAttachmentDisabled() {
      // 当 businessStatus 不是 '待商务主管定标' 时，禁用附件上传
      return this.detail.businessStatus !== '待商务主管定标'
    },
  },
  watch: {
    'detail.shipCode'(_, oldVal) {
      if (!oldVal) return
      this.clearApply()
    },
    'detail.applyId': {
      handler(val, oldVal) {
        this.loadApplyInfo(val)
        if (!oldVal) return
        this.clearComponents()
      },
      immediate: true,
    },
    'detail.receiveDate'(newDate, oldDate) {
      if (newDate !== oldDate) {
        console.log('newDate !== oldDate)')
        this.receiveDateModified = true
      }
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async getHis(item) {
      this.searchObj.id = item.itemId
      // console.log(this.searchObj.id)
      await this.$nextTick()
      this.hisDialog = true
      await this.$nextTick()
      this.searchObj.id = item.itemId
      this.$refs.table.ship = this.detail.shipCode
    },

    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.detail.remark == null) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.enquiryList.length === 0) {
        this.$dialog.message.error('询价明细不能为空')
        return
      }
      const detailList = this.getCompWithOperation()
      if (!this.isEdit) {
        const purchaseEnquirySubmitInputDTO = await this.$refs.enquiry.save()
        if (!purchaseEnquirySubmitInputDTO) return
        this.loadingSubmit = true
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/purchaseManage/purchaseEnquirySubmit',
          {
            purchaseEnquiryModifyDTO: {
              ...this.detail,
              enquiryType,
              detailList,
            },
            purchaseEnquirySubmitInputDTO,
          },
        )
        this.loadingSubmit = false
        if (!errorRaw) {
          this.closeAndTo(this.backRouteName)
        }
      } else {
        this.loadingSubmit = true
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/purchaseManage/purchaseEnquirySaveOrUpdate',
          { ...this.detail, enquiryType, detailList },
        )
        this.loadingSubmit = false
        if (notMove) return errorRaw
        if (!errorRaw) goBack()
      }
    },
    async resave() {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.detail.remark == null) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.enquiryList.length === 0) {
        this.$dialog.message.error('询价明细不能为空')
        return
      }
      const purchaseEnquirySubmitInputDTO = await this.$refs.enquiry.save()
      if (!purchaseEnquirySubmitInputDTO) return
      const detailList = this.getCompWithOperation()
      if (!this.receiveDateModified) {
        const confirm = await this.$dialog.msgbox.confirm(
          '交货日期与原申请单相同，是否确认继续提交？',
        )
        if (!confirm) {
          return
        }
      }
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquirySubmit',
        {
          purchaseEnquiryModifyDTO: {
            ...this.detail,
            enquiryType,
            detailList,
          },
          purchaseEnquirySubmitInputDTO,
        },
      )
      if (!errorRaw) {
        this.closeAndTo(this.backRouteName)
      }
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const errorRaw = await this.save(goBack, true)
      if (errorRaw) return
      const error = await this.$refs.audit.submit()
      if (!error) goBack()
    },

    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryDetailById',
        { enquiryId: this.$route.params.id },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode

      await this.$nextTick()

      if (
        this.detail.businessStatus == '机务主管退回' ||
        this.detail.businessStatus == '通导信息主管退回'
      ) {
        if (!this.$refs.enquiry) {
          await this.$nextTick()
        }

        if (this.$refs.enquiry) {
          this.$refs.enquiry.suppliers = data.suppliers.map((i) => {
            const currencys = i.supplierBankListOutputDTO.map((i) => {
              return {
                currencyType: i.currencyType,
                ccyName: i.ccyCode,
              }
            })
            return {
              currencys,
              selectCurrency: {},
              ...i.supplierPurchaserOutputDTO,
              ...i.supplierOutputDTO,
            }
          })
        } else {
          console.warn('enquiry组件暂未挂载，无法设置suppliers')
        }
      }
      this.enquiryList = data.detailList.map((i) => {
        return {
          ...i,
          nameCn: i.materialBaseInfo.nameCn,
          nameEn: i.materialBaseInfo.nameEn,
          code: i.materialBaseInfo.code,
          description: i.materialBaseInfo.description,
          model: i.materialBaseInfo.model,
          specs: i.materialBaseInfo.specs,
          unit: i.materialBaseInfo.unit,
          requireQuantity: i.applyNum,
        }
      })
      this.initPort = {
        portCn: data.portName,
        id: data.receivePortId,
      }
      this.initApply = {
        applicationNo: data.applicationNo,
        id: data.applyId,
      }
      if (this.businessStatus !== '未提交') {
        await this.loadQuote()
      }
      this.receiveDateModified = false
    },

    async loadApplyInfo(applyId) {
      if (!applyId) {
        this.applyInfo = {}
        return
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/materialApplyDetailById',
        { applyId },
      )
      this.detail.remark3 = data.remark
      this.applyInfo = data
      if (
        this.detail.businessStatus !== '机务主管退回' &&
        this.detail.businessStatus !== '通导信息主管退回'
      ) {
        this.$refs.enquiry.suppliers = data.suppliers.map((i) => {
          const currencys = i.supplierBankListOutputDTO.map((i) => {
            return {
              currencyType: i.currencyType,
              ccyName: i.ccyCode,
            }
          })
          return {
            currencys,
            selectCurrency: {},
            ...i.supplierPurchaserOutputDTO,
            ...i.supplierOutputDTO,
          }
        })
      }
      console.log(data.openDate)
      if (data.openDate != null) {
        this.$refs.enquiry.formData.endTime = data.openDate
      }

      console.log(this.$refs.enquiry.suppliers)
      this.applyInfo.detailList = data.detailList.map((i) => ({
        ...i,
        nameCn: i.materialCnName,
        code: i.materialCode,
        description: i.materialDescription,
        model: i.materialModel,
        specs: i.materialSpec,
        unit: i.materialUnit,
        remark4: i.remark,
      }))
      //   this.applyList = data.detailList
      if (!this.isEdit) {
        this.initPort = {
          portCn: data.port,
          id: data.portId,
        }
        this.detail.receivePortId = data.portId
      }
      this.initApply = {
        applyCode: data.applyCode,
        id: data.id,
      }
      this.detail.shipCode = data.shipInfo.shipCode
      this.detail.applyType = data.applyType
      if (
        !this.detail.businessStatus ||
        (this.detail.businessStatus &&
          this.detail.businessStatus != '机务主管退回' &&
          this.detail.businessStatus != '通导信息主管退回')
      ) {
        this.detail.remark = `物料申请单备注：${data.remark}\n物料询价单备注：`
      }
    },

    getCompWithOperation() {
      const ids = this.enquiryList.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.detailList
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.enquiryList.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },

    clearApply() {
      this.$dialog.message.info('由于船舶变更,自动清空所选申请单')
      this.detail.applyId = ''
    },
    clearComponents() {
      this.$dialog.message.info('由于申请单变更,自动清空所选物料')
      this.applyInfo = {}
      this.enquiryList = []
      this.selectedEn = false
    },

    async loadQuote() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuotePage',
        { enquiryId: this.$route.params.id },
      )
      this.quoteList = data.records.map((quote) => ({
        ...quote,
        winStatus: quote.winStatus || (quote.isWin ? '中标' : '不中标'),
      }))
      this.quoteIds = data.records.map((i) => i.id)
    },

    async quoteBid() {
      if (
        !(await this.$dialog.msgbox.confirm(
          `确定 ${this.selectedQuote.supplierName} 中标`,
        ))
      )
        return
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidOpen',
        { enquiryId: this.$route.params.id, quoteId: this.selectedQuote.id },
      )
      this.quoteLoading = false
      if (!errorRaw) await this.loadDetail()
    },
    refresh(value) {
      if (value) {
        this.loadDetail()
      }
    },
    async updateIsWinsJw() {
      const quoteWins = this.$refs.compareTable.getQuoteWins()
      if (quoteWins.length == 0) {
        this.$dialog.message.error('请勾选中标项目！')
        return
      }
      //this.quoteLoading = true
      const quotesss = this.$refs.compareTable.getQuote()
      // 获取所有报价单的中标状态
      const quoteStatuses = this.quoteList.map((quote) => ({
        id: quote.id,
        winStatus: quote.winStatus,
      }))
      // 先检查是否有价格较高但没有备注的备件
      const { data, errorRaw: validateError } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/validateHigherPricedItems',
        {
          enquiryId: this.detail.id,
          purchaseQuoteDetailCompareDTOS: quotesss,
        },
      )
      if (validateError) return

      if (data == 1) {
        await this.continueUpdateIsWinsJw(quoteStatuses, quotesss)
        this.showRemarkDialog = true
        return
      }

      await this.continueUpdateIsWinsJw1(quoteStatuses, quotesss)
    },
    async continueUpdateIsWinsJw(quoteStatuses, quotesss) {
      //this.quoteLoading = true
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidUpdateIsWins',
        {
          purchaseQuoteWinStatusDTO: quoteStatuses,
          purchaseQuoteDetailModifyDTOS: quotesss,
        },
      )
      //this.quoteLoading = false
      if (errorRaw) return
      //this.quoteJw()
    },
    async continueUpdateIsWinsJw1(quoteStatuses, quotesss) {
      this.quoteLoading = true
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidUpdateIsWins',
        {
          purchaseQuoteWinStatusDTO: quoteStatuses,
          purchaseQuoteDetailModifyDTOS: quotesss,
        },
      )
      this.quoteLoading = false
      if (errorRaw) return
      this.quoteJw()
    },
    async quoteJw() {
      if (this.detail.remark == null) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.highPriceReason != null && this.highPriceReason.length > 0) {
        this.showRemarkDialog = false
        let returnRole = ''
        if (
          (this.detail.businessStatus == '报价完成' ||
            this.detail.businessStatus == '超期' ||
            this.detail.businessStatus == '用户开标' ||
            this.detail.businessStatus == '重新定标') &&
          this.detail.applyType !== 5
        ) {
          returnRole = '机务主管'
        } else if (
          (this.detail.businessStatus == '报价完成' ||
            this.detail.businessStatus == '超期' ||
            this.detail.businessStatus == '用户开标' ||
            this.detail.businessStatus == '重新定标') &&
          this.detail.applyType == 5
        ) {
          returnRole = '通导信息主管'
        } else if (this.detail.businessStatus == '待商务主管定标') {
          returnRole =
            this.detail.applyType === 5 ? '通导采购主管' : '物料采购主管'
        }
        const formattedReason = returnRole
          ? `${returnRole}选择价格高于其他报价单物料的具体原因：${this.highPriceReason}`
          : this.returnReason
        this.detail.remark = this.detail.remark + `\n${formattedReason}`
      }
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidOpenJw',
        {
          id: this.detail.id,
          remark: this.detail.remark,
          receiveDate: this.detail.receiveDate,
        },
      )
      this.quoteLoading = false
      if (!errorRaw) {
        this.closeAndTo(this.backRouteName)
      }
    },
    async updateIsWinsSw() {
      const quoteWins = this.$refs.compareTable.getQuoteWins()
      if (quoteWins.length == 0) {
        this.$dialog.message.error('请勾选中标项目！')
        return
      }
      this.quoteLoading = true
      const quotesss = this.$refs.compareTable.getQuote()
      // 获取所有报价单的中标状态
      const quoteStatuses = this.quoteList.map((quote) => ({
        id: quote.id,
        winStatus: quote.winStatus,
      }))
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidUpdateIsWins',
        {
          purchaseQuoteWinStatusDTO: quoteStatuses,
          purchaseQuoteDetailModifyDTOS: quotesss,
        },
      )
      this.quoteLoading = false
      if (errorRaw) return
      this.quoteSw()
    },
    async quoteSw() {
      if (this.detail.remark == null) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidOpenSw',
        {
          id: this.detail.id,
          remark: this.detail.remark,
          remark1: this.detail.remark1,
          attachmentInfo: this.detail.attachmentInfo,
        },
      )
      this.quoteLoading = false
      if (!errorRaw) {
        this.closeAndTo(this.backRouteName)
      }
    },
    async returnJw() {
      this.returnDialog = true
    },
    async confirmReturn() {
      // 获取退回角色
      let returnRole = ''
      if (
        (this.detail.businessStatus == '报价完成' ||
          this.detail.businessStatus == '超期' ||
          this.detail.businessStatus == '用户开标' ||
          this.detail.businessStatus == '重新定标') &&
        this.detail.applyType !== 5
      ) {
        returnRole = '机务主管'
      } else if (
        (this.detail.businessStatus == '报价完成' ||
          this.detail.businessStatus == '超期' ||
          this.detail.businessStatus == '用户开标' ||
          this.detail.businessStatus == '重新定标') &&
        this.detail.applyType == 5
      ) {
        returnRole = '通导信息主管'
      } else if (this.detail.businessStatus == '待商务主管定标') {
        returnRole =
          this.detail.applyType === 5 ? '通导采购主管' : '物料采购主管'
      }

      // 拼接退回原因
      const formattedReason = returnRole
        ? `${returnRole}退回原因：${this.returnReason}`
        : this.returnReason

      this.detail.remark = this.detail.remark + `\n${formattedReason}`
      this.returnDialog = false
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryReturnSw',
        {
          id: this.detail.id,
          remark: this.detail.remark,
        },
      )
      this.quoteLoading = false
      if (!errorRaw) {
        this.$dialog.message.success('退回成功！')
        this.closeAndTo(this.backRouteName)
      }
    },
    updateAttachments(attachmentIds) {
      this.detail.attachmentInfo = attachmentIds
    },
    async saveQuoteWins() {
      if (!(await this.$dialog.msgbox.confirm('确定临时保存定标信息？'))) return

      const quoteWins = this.$refs.compareTable.getQuoteWins()
      if (quoteWins.length == 0) {
        this.$dialog.message.error('请勾选中标项目')
        return
      }
      this.quoteLoading = true
      const quotesss = this.$refs.compareTable.getQuote()
      // 获取所有报价单的中标状态
      const quoteStatuses = this.quoteList.map((quote) => ({
        id: quote.id,
        winStatus: quote.winStatus,
      }))
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidUpdateIsWins',
        {
          purchaseQuoteWinStatusDTO: quoteStatuses,
          purchaseQuoteDetailModifyDTOS: quotesss,
        },
      )
      this.quoteLoading = false
      if (errorRaw) return
      this.$dialog.message.success('保存成功')
      if (!errorRaw) await this.loadDetail()
    },
    updateQuoteWinStatus(quoteId, winStatus) {
      const quote = this.quoteList.find((q) => q.id === quoteId)
      if (quote) {
        quote.winStatus = winStatus
      }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
