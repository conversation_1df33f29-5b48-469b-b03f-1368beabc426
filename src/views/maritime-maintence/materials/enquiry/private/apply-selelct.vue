<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    label="物料申请单"
    :headers="headers"
    item-text="applyCode"
    :req-url="reqUrl"
    :search-remain="searchObj"
    @update="update"
    max-width="1300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
    :rules="[rules.required]"
  >
    <template #searchflieds>
      <v-col cols="3">
        <v-select
          label="业务状态"
          dense
          outlined
          clearable
          v-model="searchObj.businessStatus"
          :items="businessStatuses"
        ></v-select>
      </v-col>
    </template>
    <template v-slot:[`item.applyType`]="{ item }">
      <v-chip small v-if="item.applyType == 1">正常</v-chip>
      <v-chip small color="warning" v-if="item.applyType == 2">紧急</v-chip>
      <v-chip small color="error" v-if="item.applyType == 3">特急</v-chip>
    </template>
    <template v-slot:[`item.remark`]="{ item }">
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <span v-on="on">{{ item.remark.substring(0, 15) }}...</span>
        </template>
        <span>{{ item.remark }}</span>
      </v-tooltip>
    </template>
    <template v-slot:[`item.isDockRepair`]="{ item }">
      {{ item.isDockRepair ? '是' : '否' }}
    </template>
  </v-dialog-select>
</template>
<script>
export default {
  name: 'apply-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/business/shipAffairs/purchaseManage/materialApplyPage'
    this.headers = [
      { text: '申请单号', value: 'applyCode' },
      { text: '申请日期', value: 'applyDate' },
      { text: '申请部门', value: 'applyDept' },
      { text: '申请类型', value: 'applyType' },
      { text: '是否坞修', value: 'isDockRepair' },
      // { text: '申请目的', value: 'applyPurpose' },
      { text: '状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.businessStatuses = ['部分询价', '全部询价', '审批通过']
  },
  props: {
    shipCode: String,
    value: [String, Object],
    disabled: [String, Boolean],
    readonly: [String, Boolean],
    numbers: Array,
    initSelected: Object,
    // read
  },
  data() {
    return {
      searchObj: {
        shipCode: '',
        status: 3,
        businessStatus: '审批通过',
        isEnd: false,
      },
      val: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  watch: {
    value(val) {
      this.val = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
  },

  methods: {
    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },
    update() {
      this.$emit('update', this.val.id)
    },
    updateSearchObj() {
      //   // this.$nextTick(() => {
      //   // })
      //   if (this.searchObj.shipCode !== this.shipCode) {
      //     console.log('asd')
      //     this.searchObj.shipCode = ''
      //     this.$nextTick(() => {
      //       this.searchObj.shipCode = this.shipCode
      //     })
      //   }
    },
  },

  mounted() {
    this.searchObj.shipCode = this.shipCode
  },
}
</script>

<style></style>
