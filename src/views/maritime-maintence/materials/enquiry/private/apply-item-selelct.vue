<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1100"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        询价物料
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-data-table
          show-select
          :headers="headers"
          :items="items"
          item-key="materialId"
          hide-default-footer
          disable-pagination
          v-model="selected"
          @click:row="selectRow"
          dense
          class="use-divider"
        ></v-data-table>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn
          depressed
          color="primary"
          :disabled="selected.length === 0"
          @click="confirm"
        >
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'apply-item-selelct',
  model: {
    prop: 'open',
    event: 'update',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.headers = [
      { text: '物料名称', value: 'nameCn' },
      { text: '物料编码', value: 'code' },
      { text: 'IMPA', value: 'nameEn' },
      { text: '物料描述', value: 'description' },
      { text: '物料型号', value: 'model' },
      { text: '物料规格', value: 'specs' },
      { text: '物料单位', value: 'unit' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    items: Array,
    sitems: Array,
  },
  data() {
    return {
      dialog: false,
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$nextTick(() => {
        this.selected = this.sitems.map((i) => {
          return { ...i, vid: i.id, materialId: i.itemId }
        })
      })
    },
  },
  methods: {
    closeForm() {
      this.$emit('update', false)
    },
    async confirm() {
      const list = this.selected.map((i) => {
        return {
          ...i,
          id: i.vid,
          itemId: i.materialId,
          enquiryNum: i.enquiryNum || i.auditQuantity,
          remark: i.remarkk || '',
        }
      })
      this.$emit('update:sitems', list)
      this.$emit('update', false)
    },
    selectRow(_, { isSelected, item }) {
      this.selected = isSelected
        ? this.selected.filter((i) => i.id !== item.id)
        : [...this.selected, item]
    },
  },
  beforeDestroy() {
    this.$emit('update', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
