<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <port-select-dialog
                  v-model="portId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  label="发货日期"
                  v-model="deliveryDate"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="changeStatus(1002)"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  发货
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="isShip ? headersShip : headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      :showExportButton="true"
      :specialHeaders="specialHeaders"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.applyType"
            :items="applyTypeOptions"
            label="申请类型"
            clearable
            dense
            outlined
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <!-- 防抖赋值 -->
          <v-text-field
            label="订单号"
            outlined
            dense
            clearable
            v-model="searchObj.orderNo"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <!-- 防抖赋值 -->
          <v-text-field
            label="申请单号"
            outlined
            dense
            clearable
            @input="onInput"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            label="状态"
            outlined
            dense
            :items="businessStatusMap2"
            clearable
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <!-- <v-btn
          outlined
          :disabled="!selected || selected.businessStatus != 1000"
          tile
          color="primary"
          class="mx-1"
          @click="changeStatus(1001)"
          v-permission="['物料订单:主管确认']"
        >
          <v-icon left>mdi-check</v-icon>
          主管确认
        </v-btn>
        <v-btn
          :disabled="!selected || selected.businessStatus != 1001"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="editItem"
          v-permission="['物料订单:供应商发货']"
        >
          <v-icon left>mdi-package-variant-closed-check</v-icon>
          供应商发货
        </v-btn> -->
        <v-btn
          :disabled="!canAbort"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="changeStatus(1009)"
          v-permission="['物料订单:重新定标']"
        >
          <v-icon left>mdi-restart</v-icon>
          重新定标
        </v-btn>
        <v-btn
          :disabled="!canAbort"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="changeStatus(1007)"
          v-permission="['物料订单:废弃']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          废弃
        </v-btn>
        <v-btn
          :disabled="!canAbort1"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="changeStatus(1007)"
          v-permission="['物料订单:废弃(采购)']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          废弃
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'materials-order-new' }"
          v-permission="['物料订单:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          :loading="loading"
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="downloadExcel"
          v-permission="['物料订单:导出EXCEL']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="showExportDialog = true"
          v-permission="['物料订单:导出EXCEL']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          比较签收日期
        </v-btn>
      </template>
      <template v-slot:[`item.source`]="{ item }">
        {{ sourceMap[item.source] || '' }}
      </template>
      <template v-slot:[`item.isDockRepair`]="{ item }">
        {{ item.isDockRepair ? '是' : '否' }}
      </template>
      <template v-slot:[`item.businessStatus`]="{ item }">
        {{ businessStatusMap[item.businessStatus] }}
      </template>
      <template v-slot:[`item.applyType`]="{ item }">
        <v-chip small v-if="item.applyType != 99">
          系统采购
          <spand v-if="item.applyNo.includes('SITC')">-专项集采</spand>
        </v-chip>
        <v-chip small color="error" v-if="item.applyType == 99">
          邮件采购
          <spand v-if="item.applyNo.includes('SITC')">-专项集采</spand>
        </v-chip>
      </template>
    </v-table-searchable>
    <!-- 导出对话框 -->
    <v-dialog v-model="showExportDialog" max-width="500px">
      <v-card>
        <v-card-title class="headline">选择导出时间范围</v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col cols="12" sm="6">
                <vs-date-picker
                  label="开始时间"
                  v-model="exportStartDate"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" sm="6">
                <vs-date-picker
                  label="结束时间"
                  v-model="exportEndDate"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></vs-date-picker>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="blue darken-1" text @click="showExportDialog = false">
            取消
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="exportByDateRange"
            :loading="loading"
          >
            导出
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import portSelectDialog from '../../components/port-select-dialog.vue'
// applyNo	申请单编号	string
// businessStatus	业务状态	integer
// completeTime	完成日期	string
// createTime	创建日期	string
// deliveryDate	交付日期	string
// id	物理主键	string
// isDockRepair	是否坞修	boolean
// orderNo	采购订单编号	string
// orderNo	采购订单编号	string
export default {
  components: { portSelectDialog },
  name: 'materials-order-list',
  created() {
    this.tableName = '物料订单'
    this.reqUrl = '/business/shipAffairs/purchaseManage/purchaseOrderPage'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      // { text: '船舶', value: 'shipInfo' },
      { text: '船舶', value: 'shipName' },
      { text: '订单号', value: 'orderNo' },
      { text: '供应商', value: 'supplierName' },
      { text: '申请类型', value: 'applyType' },
      { text: '订单来源', value: 'source' },
      { text: '申请单号', value: 'applyNo' },
      { text: '创建日期', value: 'createTime' },
      // { text: '发货日期', value: 'deliveryDate' },
      { text: '交货港口', value: 'orderPortName' },
      { text: '入库完成日期', value: 'completeTime' },
      { text: '签收日期', value: 'signDate' },
      { text: '是否坞修', value: 'isDockRepair' },
      { text: '币种', value: 'ccyCode' },
      { text: '总价', value: 'totalPrice' },
      { text: '状态', value: 'businessStatus' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '询价人', value: 'inquier' },
      { text: '项目说明', value: 'remark4' },
    ]
    this.fuzzyLabel = '模糊搜索(订单号/申请单号/供应商/询价人/备注/项目说明)'
    this.headersShip = [
      // { text: '船舶', value: 'shipInfo' },
      { text: '船舶', value: 'shipName' },
      { text: '订单号', value: 'orderNo' },
      { text: '供应商', value: 'supplierName' },
      { text: '申请类型', value: 'applyType' },
      { text: '订单来源', value: 'source' },
      { text: '申请单号', value: 'applyNo' },
      { text: '创建日期', value: 'createTime' },
      { text: '发货日期', value: 'deliveryDate' },
      { text: '交货港口', value: 'orderPortName' },
      { text: '入库完成日期', value: 'completeTime' },
      { text: '签收日期', value: 'signDate' },
      { text: '是否坞修', value: 'isDockRepair' },
      { text: '状态', value: 'businessStatus' },
      { text: '申请岗位', value: 'applicantPost' },
    ]
    this.pushParams = { name: 'materials-order-detail' }
    this.businessStatusMap = {
      // 0: '未做凭证',
      // 1: '已做凭证',
      // 10: '未提交',
      // 11: '审批中',
      // 12: '审批失败',
      // 13: '审批通过',
      // 14: '已生成SAP报文',
      // 20: '未通过映射',
      // 21: '映射错误',
      // 22: '报文错误',
      // 23: '已通过映射',
      // 24: '已发送SAP',
      // 25: 'SAP审批未通过',
      // 26: 'SAP执行成功',
      // 27: 'SAP执行失败',
      // 28: 'SAP已作废',
      // 29: 'SAP已冲销',
      // 30: '付款审批中',
      // 31: '付款审批未通过',
      // 32: '付款审批通过',
      // 33: '已付款',
      // 34: '付款审批未提交',
      0: '费用项目未提交', //未做凭证
      1: '发票未提交', //已做凭证
      // 2: '审批中',
      // 3: '审批通过',
      // 4: '已生成SAP报文',
      // 5: '已发送SAP',
      // 6: 'SAP审批未通过',
      // 7: '付款审批中',
      // 8: '付款审批通过',
      // 9: '付款审批未通过',
      10: '发票未提交', //未提交
      11: '发票审批中', //凭证审批中
      12: '发票审批退回', //审批失败
      13: '发票已审批', //审批通过
      // 14: '已生成SAP报文',//已生成SAP报文
      // 20: '未通过映射',//未通过映射
      21: '发票财务未确认-业务', //映射错误
      22: '发票财务未确认-业务', //报文错误
      // 23: '已通过映射',//已通过映射
      24: '发票财务确认中', //已发送SAP
      // 25: 'SAP审批未通过',//SAP审批未通过
      26: '发票财务已确认', //SAP执行成功
      27: '发票财务未确认-SAP', //SAP执行失败
      // 28: 'SAP已作废',//SAP已作废
      // 29: 'SAP已冲销',//SAP已冲销
      30: '付款审批审批中', //付款审批中
      31: '付款审批退回', //付款审批未通过
      32: '付款审批已完成', //付款审批通过
      33: '已付款', //已付款
      34: '付款审批未提交', //付款审批未提交
      // 1000: '待确认',
      // 1001: '已确认',
      99: '邮件采购订单',
      999: '待供应商报价',
      1000: '待机务主管确认',
      100111: '待采购主管确认',
      100991: '供应商修改订单待确认',
      1001: '待供应商发货', //待供应商确认发货
      10011: '机务主管已确认，待OA审批通过',
      10099: '机务主管已确认,超二级科目预算审批中',
      100999: '机务主管已确认,超二级科目预算审批失败',
      1002: '供应商已发货', //已发货
      1003: '船端入库未提交', //已到货
      1008: '船端入库审批中', //入库中
      1004: '船端已入库', //入库完成
      10044: '已上传发票', //入库完成
      1005: '付款中',
      1006: '已付款',
      1007: '已废弃',
      1009: '重新定标',
    }
    this.businessStatusMap2 = [
      { text: '待供应商报价', value: '999' },
      { text: '待机务主管确认', value: '1000' },
      { text: '待采购主管确认', value: '100111' },
      { text: '待供应商发货', value: '1001' },
      { text: '供应商修改订单待确认', value: '100991' },
      { text: '供应商已发货', value: '1002' },
      { text: '船端入库未提交', value: '1003' },
      { text: '船端入库审批中', value: '1008' },
      { text: '船端已入库', value: '1004' },
      { text: '已上传发票', value: '10044' },
      { text: '已废弃', value: '1007' },
      { text: '发票未提交', value: '10' },
      { text: '发票审批中', value: '11' },
      { text: '发票审批退回', value: '12' },
      { text: '发票已审批', value: '13' },
      { text: '发票财务未确认-业务', value: '21' },
      { text: '发票财务未确认-业务', value: '22' },
      { text: '发票财务确认中', value: '24' },
      { text: '发票财务已确认', value: '26' },
      { text: '发票财务未确认-SAP', value: '27' },
      { text: '付款审批审批中', value: '30' },
      { text: '付款审批退回', value: '31' },
      { text: '付款审批已完成', value: '32' },
      { text: '已付款', value: '33' },
      { text: '付款审批未提交', value: '34' },
    ]
    this.specialHeaders = [
      {
        text: 'applyType',
        value: [
          { text: 1, value: '常规' },
          { text: 2, value: '紧急' },
          { text: 3, value: '坞修' },
          { text: 4, value: '固定资产' },
          { text: 5, value: '通导设备固定资产' },
          { text: 99, value: '邮件采购' },
        ],
      },
      {
        text: 'isDockRepair',
        value: [
          { text: true, value: '是' },
          { text: false, value: '否' },
        ],
      },
      {
        text: 'businessStatus',
        value: [
          { text: 0, value: '费用项目未提交' },
          { text: 1, value: '发票未提交' },
          { text: 10, value: '发票未提交' },
          { text: 11, value: '发票审批中' },
          { text: 12, value: '发票审批退回' },
          { text: 13, value: '发票已审批' },
          { text: 21, value: '发票财务未确认-业务' },
          { text: 22, value: '发票财务未确认-业务' },
          { text: 24, value: '发票财务确认中' },
          { text: 26, value: '发票财务已确认' },
          { text: 27, value: '发票财务未确认-SAP' },
          { text: 30, value: '付款审批审批中' },
          { text: 31, value: '付款审批退回' },
          { text: 32, value: '付款审批已完成' },
          { text: 33, value: '已付款' },
          { text: 34, value: '付款审批未提交' },
          { text: 99, value: '邮件采购订单' },
          { text: 999, value: '待供应商报价' },
          { text: 1000, value: '待机务主管确认' },
          { text: 100991, value: '供应商修改订单待确认' },
          { text: 1001, value: '待供应商发货' },
          { text: 10011, value: '机务主管已确认，待OA审批通过' },
          { text: 10099, value: '机务主管已确认,超二级科目预算审批中' },
          { text: 100999, value: '机务主管已确认,超二级科目预算审批失败' },
          { text: 1002, value: '供应商已发货' },
          { text: 1003, value: '船端入库未提交' },
          { text: 1008, value: '船端入库审批中' },
          { text: 1004, value: '船端已入库' },
          { text: 10044, value: '已上传发票' },
          { text: 1005, value: '付款中' },
          { text: 1006, value: '已付款' },
          { text: 1007, value: '作废' },
          { text: 1009, value: '重新定标' },
        ],
      },
    ]
  },

  data() {
    return {
      timer: null,
      selected: false,
      portId: '',
      deliveryDate: '',
      formShow: false,
      initPort: {},
      searchObj: { orderType: '02', fuzzyApplyNo: '' },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      applyTypeOptions: [
        { text: '常规', value: 1 },
        { text: '紧急', value: 2 },
        { text: '坞修', value: 3 },
      ],
      loading: false,
      showExportDialog: false,
      exportStartDate: '',
      exportEndDate: '',
      sourceMap: {
        0: '',
        1: '申请单',
        2: '询价单',
        3: '集采',
        4: '邮件采购',
        5: '单次预算',
      },
    }
  },

  computed: {
    canAbort() {
      return (
        this.selected &&
        [999, 1001, 100111, 100999, 100991].includes(
          this.selected.businessStatus,
        )
      )
    },
    canAbort1() {
      return this.selected && [1002].includes(this.selected.businessStatus)
    },
    canExcel() {
      return (
        this.selected &&
        (this.selected.businessStatus > 1004 ||
          this.selected.businessStatus <= 34) &&
        this.selected.businessStatus != 1007
      )
    },
  },

  methods: {
    onInput(val) {
      // 清除上一次的定时器
      if (this.timer) {
        clearTimeout(this.timer)
      }
      // 设置一个新的定时器，延迟500毫秒后获取值
      this.timer = setTimeout(() => {
        this.getValue(val)
      }, 500)
    },
    getValue(val) {
      // 这里是获取到值后要执行的逻辑
      this.searchObj.fuzzyApplyNo = val
    },

    async editItem() {
      this.portId = this.selected.enquiryPortId
      this.initPort = {
        portCn: this.selected.enquiryPortName,
        id: this.selected.enquiryPortId,
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },

    closeForm() {
      this.$refs.form.reset()
      this.portId = ''
      this.deliveryDate = ''
      this.initPort = {}
      this.formShow = false
      this.$refs.table.disabled = false
    },

    async changeStatus(purchaseOrderStatus) {
      if (!this.selected) return
      if (purchaseOrderStatus == 1002) {
        if (!this.$refs.form.validate()) return
      }
      if (purchaseOrderStatus == 1007) {
        if (!(await this.$dialog.msgbox.confirm('确定作废此记录？'))) return
      }
      if (purchaseOrderStatus == 1009) {
        if (!(await this.$dialog.msgbox.confirm('确定重新定标？'))) return
      }
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseOrderStatusUpdate2',
        {
          orderId: this.selected.id,
          purchaseOrderStatus,
          [purchaseOrderStatus == 1002 ? 'portId' : 'sdf']: this.portId,
          [purchaseOrderStatus == 1002 ? 'deliveryDate' : 'sdf']:
            this.deliveryDate,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('状态变更成功')
        this.selected = false
        this.closeForm()
        await this.$refs.table.loadTableData()
      }
    },
    async downloadExcel() {
      this.loading = true
      let params = {
        ...this.selected,
      }
      await this.getBlobDownload(
        '/business/shipAffairs/purchaseManage/excelExportOrder',
        params,
        // 时间戳后四位
        `物料订单-${this.selected.orderNo}.xlsx`,
      )
      this.loading = false
    },
    async exportByDateRange() {
      if (!this.exportStartDate || !this.exportEndDate) {
        this.$dialog.message.error('请选择开始时间和结束时间')
        return
      }

      this.loading = true
      try {
        const params = {
          startDate: this.exportStartDate,
          endDate: this.exportEndDate,
          orderType: '02',
        }

        await this.getBlobDownload(
          '/business/shipAffairs/purchaseManage/excelExportOrderByDate',
          params,
          `物料订单-${this.exportStartDate}至${this.exportEndDate}.xlsx`,
        )

        this.showExportDialog = false
        this.exportStartDate = ''
        this.exportEndDate = ''
      } catch (error) {
        this.$dialog.message.error('导出失败，请重试')
      } finally {
        this.loading = false
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
