<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    :label="label"
    :headers="headers"
    item-text="nameCn"
    :req-url="reqUrl"
    :search-remain="searchObj"
    fuzzy-label="选择物料"
    @update="update"
    max-width="1300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
  >
    <template v-slot:searchflieds>
      <v-col cols="12" sm="6" md="2">
        <v-select
          v-model="searchObj.type"
          outlined
          dense
          clearable
          label="物料分类"
          :items="types"
        ></v-select>
      </v-col>
    </template>
    <template v-slot:[`item.type`]="{ item }">
      {{
        types.find((t) => t.value === item.type)
          ? types.find((t) => t.value === item.type).text
          : ''
      }}
    </template>
  </v-dialog-select>
</template>
<script>
export default {
  name: 'materialsInfo-select-dialog',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/business/shipAffairs/MaterialInfo/list'
    this.headers = [
      { text: '物料名称', value: 'nameCn', required: true },
      { text: 'IMPA编码', value: 'nameEn' },
      // { text: '物料编码', value: 'code', required: true },
      { text: '物料编码', value: 'code' },
      { text: '物料描述', value: 'description' },
      // { text: '品牌', value: 'brand' },
      // { text: '型号', value: 'model' },
      // { text: '规格', value: 'specs' },
      { text: '单位', value: 'unit' },
      { text: '物料分类', value: 'type' },
      { text: '备注', value: 'remark' },
    ]
  },
  props: {
    value: [String, Object],
    numbers: Array,
    disabled: [String, Boolean],
    readonly: [String, Boolean],
    initSelected: Object,
    label: {
      type: String,
      default: '选择物料',
    },
    costSubjectId: String,
    detailList: Array,
    // materialIds: String,
    // read
  },
  data() {
    return {
      searchObj: {},
      val: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      types: [],
    }
  },

  watch: {
    costSubjectId: {
      handler(val) {
        // this.searchObj = {
        //   ...this.searchObj,
        //   subjectId: val,
        // }
        this.searchObj.subjectId = val
        this.loadTypes()
      },
      immediate: true,
    },
    initSelected: {
      handler(val) {
        this.searchObj.materialId = val.id
      },
      immediate: true,
    },
    // materialIds: {
    //   handler(val) {
    //     this.searchObj.materialIds = val
    //   },
    //   immediate: true,
    // },
    // detailList(val) {
    //   this.searchObj = {
    //     ...this.searchObj,
    //     hasItemList: val
    //       .map((item) => item.itemId)
    //       .filter((itemId) => itemId !== this.costSubjectId.id),
    //   }
    // },
  },

  methods: {
    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },
    update() {
      console.log(this.val)
      this.$emit('update', this.val.id)
      this.$emit('success', this.val)
    },
    async loadTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        { current: 1, size: 1000, ...this.searchObj },
        false,
      )
      this.types = data.records.map((item) => ({
        ...item,
        value: item.id,
        text: item.cateName,
      }))
    },
  },
  beforeDestroy() {
    this.form && this.form.unregister(this)
  },
  mounted() {
    this.loadTypes()
  },
}
</script>

<style></style>
