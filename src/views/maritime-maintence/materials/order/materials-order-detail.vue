<template>
  <v-container fluid>
    <v-detail-view
      :title="`物料订单-${detail.orderNo}`"
      :tooltip="detail.orderNo"
      :backRouteName="backRouteName"
      :subtitles="
        [
          99, 999, 100991, 1000, 1001, 10011, 10099, 100999, 1002, 1003, 1008,
          1007, 1009,
        ].includes(detail.businessStatus)
          ? subtitles
          : subtitles2
      "
      @save="save"
      v-permission="['物料订单:编辑']"
      ref="detail"
      :can-save="false"
    >
      <template #custombtns>
        <!-- @click="changeStatus(1001)" -->
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 1000 && detail.source == 1"
          width="120"
          tile
          color="error"
          small
          class="mx-1"
          @click="changeStatus(998)"
          v-permission="['物料订单:机务退回']"
        >
          退回补充报价
        </v-btn>

        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 100111 && detail.source == 1"
          width="120"
          tile
          color="error"
          small
          class="mx-1"
          @click="changeStatus(997)"
          v-permission="['物料订单:采购退回']"
        >
          退回补充报价
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 1000"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus(100111)"
          v-permission="['物料订单:主管确认']"
        >
          机务主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 100991"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus(100111)"
          v-permission="['物料订单:主管确认']"
        >
          机务主管确认(供应商已修改订单信息)
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 100111"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus(1001)"
          v-permission="['物料订单:物料采购主管确认']"
        >
          物料采购主管确认
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 1001"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus(1002)"
          v-permission="['物料订单:供应商发货']"
        >
          供应商发货
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 1002"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus(1002)"
          v-permission="['物料订单:供应商发货']"
        >
          修改订单并提交
        </v-btn>
        <!-- 待供应商报价，报价后，状态为待机务主管确认 -->
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 999"
          width="120"
          tile
          color="success"
          small
          class="mx-1"
          @click="changeStatus(999)"
          v-permission="['物料订单:供应商保存草稿']"
        >
          保存草稿
        </v-btn>
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 999"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="changeStatus(1000)"
          v-permission="['物料订单:供应商报价']"
        >
          提交并确认报价
        </v-btn>
        <!-- <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 1004 || detail.businessStatus == 10"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          @click="savAttachs()"
          v-permission="['物料订单:保存发票']"
        >
          保存并上传发票
        </v-btn> -->
        <v-btn
          :loading="loading"
          v-if="detail.businessStatus == 1002"
          width="120"
          tile
          color="warning"
          small
          class="mx-1"
          :to="{
            name: 'materials-in-detail',
            params: {
              id: 'new',
              orderId: detail.id,
              orderNo: detail.orderNo,
              signingDate: detail.deliveryDate,
            },
          }"
          v-permission="['物料订单:订单入库']"
        >
          订单入库
        </v-btn>
      </template>
      <template #订单信息>
        <v-form
          ref="form"
          :readonly="
            detail.businessStatus != 999 &&
            detail.businessStatus != 1001 &&
            detail.businessStatus != 1002
          "
        >
          <v-container fluid>
            <v-row
              v-if="
                detail.businessStatus != 999 &&
                detail.businessStatus != 1001 &&
                detail.businessStatus != 1002
              "
            >
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-for="(h, i) in isShip ? tableFeildsShip : tableFeilds"
                :key="i"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfoDO'"
                  v-model="detail.shipCode"
                  readonly
                ></v-ship-select>
                <port-select-dialog2
                  v-else-if="h.value === 'enquiryPortId'"
                  v-model="detail[h.value]"
                  :initSelected="initPort2"
                  readonly
                ></port-select-dialog2>
                <vs-date-picker
                  v-else-if="h.value === 'deliveryDate'"
                  outlined
                  :readonly="
                    detail.businessStatus !== 100991 &&
                    detail.businessStatus !== 999 &&
                    detail.businessStatus != 1002
                  "
                  dense
                  v-model="detail[h.value]"
                  label="供货日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
                <v-select
                  v-else-if="h.value === 'materialType'"
                  v-model="detail.materialType"
                  :items="types"
                  outlined
                  dense
                  label="物料分类"
                ></v-select>
                <v-switch
                  v-else-if="h.value === 'priceIncTax'"
                  class="mt-1"
                  :label="detail.priceIncTax ? '含税' : '不含税'"
                  v-model="detail[h.value]"
                  dense
                ></v-switch>
                <v-text-field
                  v-else
                  outlined
                  dense
                  v-model="detail[h.value]"
                  :label="h.text"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col class="py-0" md="12" cols="12">
                <v-textarea
                  outlined
                  dense
                  placeholder="填写发货信息:港口、发货日期..."
                  v-model="detail.remark"
                  label="备注"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-attach-list
                  :attachments="detail.attachmentRecords4"
                  title="供应商报价附件"
                  :readonly="detail.businessStatus != 999"
                  @change="updateAttachments"
                ></v-attach-list>
              </v-col>
            </v-row>

            <!-- 供应商填写港口、备货天数、是否含税、发票税率、税费 -->
            <v-row
              v-if="
                detail.businessStatus == 999 ||
                detail.businessStatus == 1001 ||
                detail.businessStatus == 1002
              "
            >
              <v-col cols="12" md="12" class="py-0">
                <span style="color: red">
                  提示：请确认物料明细订购数量、单价、运费、供货天数、港口等信息；如需换货请点击明细选择物料进行换货
                </span>
              </v-col>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-for="(h, i) in isShip ? tableFeildsShip : tableFeilds"
                :key="i"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfoDO'"
                  v-model="detail.shipCode"
                  readonly
                ></v-ship-select>
                <v-text-field
                  v-else-if="h.value === 'stockUpDays'"
                  v-model="detail.stockUpDays"
                  label="备货天数"
                  type="number"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else-if="h.value === 'otherExpenses'"
                  v-model="detail.otherExpenses"
                  label="运费/otherExpenses"
                  @change="sumToatl"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
                <materialsInfo-select-dialog
                  v-else-if="h.value === 'itemId'"
                  :initSelected="item.initMaterial"
                ></materialsInfo-select-dialog>
                <port-select-dialog2
                  v-else-if="h.value === 'enquiryPortId'"
                  v-model="detail[h.value]"
                  :initSelected="initPort2"
                ></port-select-dialog2>
                <vs-date-picker
                  v-else-if="h.value === 'deliveryDate'"
                  outlined
                  dense
                  :readonly="
                    detail.businessStatus !== 100991 &&
                    detail.businessStatus !== 999 &&
                    detail.businessStatus != 1002
                  "
                  v-model="detail[h.value]"
                  label="供货日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
                <v-select
                  v-else-if="h.value === 'materialType'"
                  v-model="detail.materialType"
                  :items="types"
                  outlined
                  dense
                  label="物料分类"
                ></v-select>
                <v-switch
                  v-else-if="h.value === 'priceIncTax'"
                  class="mt-1"
                  :label="detail.priceIncTax ? '含税' : '不含税'"
                  v-model="detail[h.value]"
                  dense
                ></v-switch>
                <v-text-field
                  v-else
                  outlined
                  dense
                  v-model="detail[h.value]"
                  :label="h.text"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col class="py-0" md="12" cols="12">
                <v-textarea
                  outlined
                  readonly
                  dense
                  placeholder="填写发货信息:港口、发货日期..."
                  v-model="detail.remark"
                  label="备注"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-attach-list
                  :attachments="detail.attachmentRecords2"
                  title="船舶附件列表"
                  :readonly="true"
                ></v-attach-list>
              </v-col>
              <v-col cols="12" md="12">
                <v-attach-list
                  :attachments="detail.attachmentRecords4"
                  title="供应商报价附件"
                  :readonly="detail.businessStatus != 999"
                  @change="updateAttachments"
                ></v-attach-list>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="项目说明"
                  dense
                  outlined
                  v-model="detail.remark2"
                  :readonly="true"
                ></v-textarea>
              </v-col>
              <!--              <v-col cols="12" md="12">
                <v-textarea
                  label="申请单备注"
                  dense
                  outlined
                  v-model="detail.remark1"
                  :readonly="true"
                ></v-textarea>
              </v-col>-->
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #发票信息>
        <!-- 船端已入库、发票未提交可上传发票 -->
        <v-card-text>
          <v-row>
            <v-col cols="12" md="2" class="py-0">
              <v-text-field
                :disabled="
                  detail.businessStatus != 1004 &&
                  detail.businessStatus != 10 &&
                  detail.businessStatus != 10044
                "
                outlined
                dense
                v-model="detail.invoiceCode"
                label="发票编号"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2" class="py-0">
              <vs-date-picker
                :disabled="
                  detail.businessStatus != 1004 &&
                  detail.businessStatus != 10 &&
                  detail.businessStatus != 10044
                "
                outlined
                dense
                v-model="detail.invoiceDate"
                label="发票日期"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2" class="py-0">
              <v-text-field
                :disabled="
                  detail.businessStatus != 1004 &&
                  detail.businessStatus != 10 &&
                  detail.businessStatus != 10044
                "
                type="number"
                outlined
                dense
                v-model="detail.money"
                label="发票金额"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2" class="py-0">
              <vs-date-picker
                :disabled="
                  detail.businessStatus != 1004 &&
                  detail.businessStatus != 10 &&
                  detail.businessStatus != 10044
                "
                outlined
                dense
                v-model="detail.signDate"
                label="签收日期"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2" class="py-0">
              <vs-date-picker
                :disabled="true"
                outlined
                dense
                v-model="detail.signingDate"
                label="签收日期(船端)"
              ></vs-date-picker>
            </v-col>
            <v-col cols="12" md="2" class="py-0">
              <v-btn
                :loading="loading"
                v-if="
                  detail.businessStatus == 1004 ||
                  detail.businessStatus == 10 ||
                  detail.businessStatus == 10044
                "
                width="120"
                tile
                color="warning"
                class="mx-1"
                @click="savAttachs()"
                v-permission="['备件订单:保存发票']"
              >
                提交发票
              </v-btn>
            </v-col>
          </v-row>

          <v-attach-list
            title="发票附件"
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            :ship-code="detail.shipCode"
            :readonly="
              !(
                detail.businessStatus == 1004 ||
                detail.businessStatus == 10 ||
                detail.businessStatus == 10044
              )
            "
          ></v-attach-list>
        </v-card-text>
        <v-card-text>
          <v-attach-list
            title="签收单附件"
            :attachments="detail.attachmentRecords3"
            :readonly="true"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template #物料明细>
        <v-row align="center" class="mx-2" v-if="detail.businessStatus == 999">
          <v-col cols="auto" class="d-flex align-center">
            <v-file-input
              v-model="file"
              outlined
              dense
              hide-details
              accept=".xls,.xlsx,.xlsm"
              label="上传报价明细"
              style="width: 300px"
              v-permission="['物料订单:上传明细']"
            ></v-file-input>
          </v-col>
          <v-col cols="auto" class="d-flex align-center">
            <v-btn
              color="primary"
              :loading="loading"
              @click="exportExcel"
              v-permission="['物料订单:明细导出']"
              class="ml-4"
            >
              <v-icon left>mdi-download</v-icon>
              导出报价模板
            </v-btn>
          </v-col>
        </v-row>
        <v-table-list
          :show-select="false"
          :headers="isShip ? headersShip : headers"
          :items="detail.detailList"
        >
          <template
            v-if="
              detail.businessStatus == 999 ||
              detail.businessStatus == 1001 ||
              detail.businessStatus == 1002 ||
              detail.businessStatus == 100111
            "
            v-slot:[`item.discountPrice`]="{ item }"
          >
            <v-text-field
              v-if="!item.hasMainPrice"
              v-model="item.discountPrice"
              label="成交单价"
              type="number"
              single-line
              dense
              :rules="isJPY ? [rules.int] : [rules.decimal4]"
              @change="sumToatl"
            ></v-text-field>
            <v-text-field
              v-else
              v-model="item.discountPrice"
              label="成交单价"
              type="number"
              single-line
              dense
              :rules="[
                ...(isJPY ? [rules.int] : [rules.decimal4]),
                (v) => v <= item.unitPrice || '价格不能高于原协议价',
              ]"
              :max="item.unitPrice"
              @change="sumToatl"
            ></v-text-field>
          </template>
          <template v-slot:[`item.itemId`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <div v-bind="attrs" v-on="on">
                  <materialsInfo-select-dialog
                    v-model="item.itemId"
                    :readonly="
                      detail.businessStatus !== 999 &&
                      detail.businessStatus !== 1001 &&
                      detail.businessStatus !== 1002
                    "
                    :initSelected="item.initMaterial"
                    :costSubjectId="detail.costSubjectId"
                    :detailList="detail.detailList"
                    @success="changeMaterial($event, item)"
                  ></materialsInfo-select-dialog>
                </div>
              </template>
              <span>
                {{
                  item.initMaterial
                    ? item.initMaterial.nameCn
                    : item.materialName
                }}
              </span>
            </v-tooltip>
          </template>
          <template
            v-if="
              detail.businessStatus == 999 ||
              detail.businessStatus == 1001 ||
              detail.businessStatus == 1002 ||
              detail.businessStatus == 1000 ||
              detail.businessStatus == 100991
            "
            v-slot:[`item.purchaseNum`]="{ item }"
          >
            <v-text-field
              v-model="item.purchaseNum"
              label="订购数量"
              type="number"
              single-line
              dense
              :rules="[rules.decimal]"
              @change="sumToatl"
            ></v-text-field>
          </template>
          <template
            v-if="
              detail.businessStatus == 999 ||
              detail.businessStatus == 1001 ||
              detail.businessStatus == 1002
            "
            v-slot:[`item.remark4`]="{ item }"
          >
            <v-text-field
              v-model="item.remark4"
              label="申请理由"
              type="text"
              single-line
              dense
            ></v-text-field>
          </template>
          <template v-slot:[`item.discountAllPrice`]="{ item }">
            {{
              isJPY
                ? Math.round(item.discountPrice * item.purchaseNum)
                : Math.round(item.discountPrice * item.purchaseNum * 100) / 100
            }}
          </template>
          <template v-slot:[`item.payAllPrice`]="{ item }">
            {{
              isJPY
                ? Math.round(item.payPrice * item.warehouseNum)
                : Math.round(item.payPrice * item.warehouseNum * 100) / 100
            }}
          </template>
          <template
            v-if="detail.businessStatus == 999"
            v-slot:[`item.remark`]="{ item }"
          >
            <v-text-field
              v-model="item.remark"
              label="备注"
              type="text"
              single-line
              dense
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template #供应商信息按钮>
        <!-- TODO:何时可以评价 -->
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['物料订单:评价供应商111']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          评价供应商
        </v-btn>
      </template>
      <template #供应商信息>
        <v-container fluid>
          <b>供应商名称:</b>
          {{ detail.supplierName }},
          <b>供应商编码:</b>
          {{ detail.supplierNo }}
          <v-card outlined class="mt-1 pt-2 px-2" v-if="comment">
            <v-row>
              <v-col cols="12" md="6">
                质量评分
                <v-rating
                  v-model="comment.score1"
                  background-color="purple lighten-3"
                  color="purple"
                  length="10"
                  readonly
                ></v-rating>
              </v-col>
              <v-col cols="12" md="6">
                服务评分
                <v-rating
                  v-model="comment.score2"
                  background-color="green lighten-3"
                  color="green"
                  length="10"
                  readonly
                ></v-rating>
              </v-col>
            </v-row>
            <v-card-text class="text-body-1">{{ comment.remark }}</v-card-text>
            <v-card-text>
              <v-attach-list
                title="评价附件"
                disabled
                :attachments="comment.attachmentRecords"
              ></v-attach-list>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <b>评论最后更改:</b>
              {{ comment.remarkTime }}，
              <b>评论人:</b>
              {{ comment.userNickName }}
            </v-card-actions>
          </v-card>
        </v-container>
      </template>
    </v-detail-view>
    <comment-dialog
      v-model="dialog"
      :initialData="initData"
      @success="loadCom"
    ></comment-dialog>
    <order-confirm-dialog
      ref="dialog"
      v-model="confirmDialog"
      :initialData="orderConfirmData"
    ></order-confirm-dialog>
  </v-container>
</template>
<script>
import commentDialog from '../../components/comment-dialog.vue'
import OrderConfirmDialog from '../../components/order-confirm-dialog.vue'
import PortSelectDialog2 from '../../components/port-select-dialog2.vue'
import materialsInfoSelectDialog from './materialsInfo-select-dialog.vue'
import VAttachList from '@/components/v-attach-list.vue'
// currencyName	币种	string
// detailList	明细列表	array	PurchaseOrderDetailOutputDTO
// detailTotalPrice	明细总价	number
// discountedTotalPrice	折后总价	number
// equimentModel	设备型号	string
// equipmentCname	设备中文名称	string
// equipmentEname	设备英文名称	string
// equipmentNumber	设备序列号	string
// id	物理主键	string
// manufacture	生产厂家	string
// otherExpenses	运费	number
// priceIncTax	报价是否含税	boolean
// rate	汇率	number
// remark	备注	string
// shipInfoDO	船舶基础信息	ShipInfoDO	ShipInfoDO
// stockUpDays	备货天数	string
// tax	税费	number
// taxRate	发票税率	number
// totalPrice	总价	number

// applicantPost	申请人岗位	string
// componentName	物料名称	string
// componentNumber	物料号	string
// discount	折扣	number
// id	物理主键	string
// purchaseNum	订购数量	integer
// remark	备注	string
// subEquipmentCname	子设备中文名称	string
// subEquipmentEname	子设备英文名称	string
// supplierName	供应商名称	string
// supplierNo	供应商编码	string
// unit	单位	string
// unitPrice	单价	number
// warehouseNum	入库数量	integer

export default {
  name: 'materials-order-detail',
  components: {
    VAttachList,
    commentDialog,
    OrderConfirmDialog,
    PortSelectDialog2,
    materialsInfoSelectDialog,
  },
  created() {
    this.backRouteName = 'materials-order-list'
    this.subtitles = ['订单信息', '物料明细', '供应商信息']
    this.subtitles2 = ['发票信息', '订单信息', '物料明细', '供应商信息']
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      // { text: '物料名', value: 'materialName' },
      { text: '物料名', value: 'itemId' },
      { text: '物料号', value: 'materialNo' },
      { text: 'IMPA', value: 'nameEn' },
      { text: '物料描述', value: 'desc' },
      { text: '订购数量', value: 'purchaseNum' },
      // { text: '成交单价', value: 'unitPrice' },
      { text: '成交单价', value: 'discountPrice' },
      { text: '成交总价', value: 'discountAllPrice' },
      { text: '付款单价', value: 'payPrice' },
      // { text: '折扣', value: 'discount' },
      // { text: '折后单价', value: 'discountPrice' },
      { text: '入库数量', value: 'warehouseNum' },
      { text: '付款总价', value: 'payAllPrice' },
      { text: '单位', value: 'unit' },
      { text: '申请理由', value: 'remark4' },
      { text: '备注', value: 'remark' },
    ]
    this.headersShip = [
      // { text: '物料名', value: 'materialName' },
      { text: '物料名', value: 'itemId' },
      { text: '物料号', value: 'materialNo' },
      { text: '物料描述', value: 'desc' },
      { text: '订购数量', value: 'purchaseNum' },
      { text: '入库数量', value: 'warehouseNum' },
      { text: '单位', value: 'unit' },
      { text: '备注', value: 'remark' },
    ]
    this.tableFeilds = [
      { text: '船舶', value: 'shipInfoDO' },
      { text: '备货天数', value: 'stockUpDays' },
      // { text: '交货港口', value: 'enquiryPortName' },
      { text: '交货港口', value: 'enquiryPortId' },
      { text: '供货日期', value: 'deliveryDate' },
      { text: '询价人', value: 'inquier' },
      // { text: '设备中文名称', value: 'equipmentCname' },
      // { text: '设备英文名称', value: 'equipmentEname' },
      // { text: '设备序列号', value: 'equipmentNumber' },
      // { text: '物料分类', value: 'materialType' },
      { text: '币种', value: 'currencyName' },
      { text: '汇率', value: 'rate' },
      { text: '运费', value: 'otherExpenses' },
      { text: '是否含税', value: 'priceIncTax' },
      { text: '发票税率', value: 'taxRate' },
      { text: '税费', value: 'tax' },
      // { text: '明细总价', value: 'detailTotalPrice' },
      // { text: '总价', value: 'totalPrice' },
      // { text: '明细总价', value: 'detailTotalPrice' },
      { text: '明细总价', value: 'detailTotalPrice2' },
      // { text: '总价', value: 'totalPrice' },
      { text: '总价', value: 'discountedTotalPrice' },
      // { text: '折后总价', value: 'discountedTotalPrice' },
      // { text: '备注', value: 'remark' },
    ]
    this.tableFeildsShip = [
      { text: '船舶', value: 'shipInfoDO' },
      { text: '备货天数', value: 'stockUpDays' },
      { text: '交货港口', value: 'enquiryPortId' },
      { text: '供货日期', value: 'deliveryDate' },
      // { text: '设备中文名称', value: 'equipmentCname' },
      // { text: '设备英文名称', value: 'equipmentEname' },
      // { text: '设备序列号', value: 'equipmentNumber' },
      // { text: '物料分类', value: 'materialType' },
      // { text: '备注', value: 'remark' },
    ]
  },
  data() {
    return {
      detail: {},
      types: [],
      dialog: false,
      initData: {},
      comment: {},
      confirmDialog: false,
      orderConfirmData: {},
      loading: false,
      rules: {
        required: (v) => !!v || v === false || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
        decimal4: (v) =>
          /^\d+(\.\d{1,4})?$/.test(v) || '必须为整数或最多四位小数',
        decimalTwoPlacesMaxOne: (v) =>
          (/^\d{0,1}(\.\d{2})?$/.test(v) && parseFloat(v) <= 1) ||
          '必须为两位小数且值不大于1',
      },
      initPort2: {},
      materialIds: '',
      file: null,
    }
  },
  computed: {
    isJPY() {
      return this.detail.currencyName === '日元'
    },
  },
  watch: {
    // 'detail.enquiryPortId'(val) {
    //   console.log(this.detail.enquiryPortId)
    //   console.log(val)
    //   // this.changeTotal()
    //   if (this.detail.businessStatus == 1001) {
    //     this.changeTotal()
    //   }
    // },
    'detail.detailList'(val) {
      this.materialIds = val.map((item) => item.itemId).join(',')
    },
    file(val) {
      if (val) {
        this.saveExcelDetails()
      }
    },
  },
  methods: {
    updateAttachments(attachmentIds) {
      this.detail.attachmentIds4 = attachmentIds
    },
    async save(goBack) {
      goBack()
    },
    async saveExcelDetails() {
      if (this.file == null) {
        this.$dialog.message.error('请上传附件！')
        return
      }
      let formData = new FormData()
      formData.append('file', this.file)
      formData.append(
        'purchaseOrderSearchInputDTO',
        new Blob([JSON.stringify(this.detail)], { type: 'application/json' }),
      )
      this.saveIng = true
      const { data } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/saveExcelDetails1',
        formData,
      )
      this.saveIng = false
      if (data) {
        this.$dialog.message.success('保存成功')
        this.file = null
        this.loadDetail()
      }
    },
    async exportExcel() {
      this.loading = true
      let params = {
        id: this.$route.params.id,
        //materialIds: this.materialIds,
      }
      await this.getBlobDownload(
        '/business/shipAffairs/purchaseManage/excelExportOrder1',
        params,
        // 时间戳后四位
        `物料订单-${this.detail.orderNo}.xlsx`,
      )
      this.loading = false
    },

    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseOrderById',
        {
          id: this.$route.params.id,
        },
      )
      this.detail = data
      this.detail.detailList.forEach((item) => {
        item['discountPrice'] = (item.discount * item.unitPrice).toFixed(4)
        item['initMaterial'] = {
          nameCn: item.materialName,
          id: item.itemId,
        }
      })
      this.detail.shipCode = this.detail.shipInfo.shipCode
      this.orderConfirmData = {
        initPort: {
          portCn: this.detail.enquiryPortName,
          id: this.detail.enquiryPortId,
        },
        portId: this.detail.enquiryPortId,
      }
      this.initPort2 = {
        portCn: this.detail.enquiryPortName,
        id: this.detail.enquiryPortId,
      }
      // 供应商发货 提醒供应商确认订购数量，运费，税费相关信息
      if (
        this.detail.businessStatus == 1001 &&
        (this.$local.data.get('userInfo').userType == '4' ||
          this.$local.data.get('userInfo').userType == '0')
      ) {
        this.$dialog.message.error(
          '请确认订单明细订购数量、单价、运费、供货天数、港口等信息',
        )
      }
    },
    async loadTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        { current: 1, size: 1000 },
        false,
      )
      this.types = data.records.map((item) => ({
        ...item,
        value: item.id,
        text: item.cateName,
      }))
    },
    async loadCom() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/SupplierAssessment/getByOrderId',
        {
          id: this.$route.params.id,
        },
      )
      this.comment = data
    },
    createCom() {
      this.dialog = true
      this.initData = {
        ...this.comment,
        supplierId: this.detail.supplierId,
        supplierName: this.detail.supplierName,
        orderId: this.detail.id,
        orderNo: this.detail.orderNo,
      }
    },

    async changeStatus(purchaseOrderStatus) {
      let formData = { purchaseOrderStatus, orderId: this.detail.id }
      // 检查是否有价格高于协议价的情况
      const overPriceItem = this.detail.detailList.find(
        (item) =>
          item.hasMainPrice &&
          Number(item.discountPrice) > Number(item.unitPrice),
      )
      if (overPriceItem) {
        this.$dialog.message.error(
          `"${overPriceItem.materialName}" 价格高于原协议价，请修改后再提交`,
        )
        return
      }
      if (purchaseOrderStatus == 1001) {
        this.orderConfirmData.purchaseOrderStatus = 1001
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        const now = new Date().toLocaleString('zh-CN')
        info.remark = `${this.detail.remark} 采购主管确认(${now})：${info.remark}\n`
        formData = { ...formData, ...info, isSupply: 'NO' }
      }
      if (purchaseOrderStatus == 100111) {
        this.orderConfirmData.purchaseOrderStatus = 100111
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        const now = new Date().toLocaleString('zh-CN')
        info.remark = `${this.detail.remark} 机务主管确认(${now})：${info.remark}\n`
        formData = { ...formData, ...info, isSupply: 'NO' }
      }
      if (purchaseOrderStatus == 1002) {
        this.orderConfirmData.purchaseOrderStatus = 1002
        this.orderConfirmData.jwReamrk = this.detail.remark
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        const now = new Date().toLocaleString('zh-CN')
        info.remark = `${this.detail.remark} 供应商发货(${now})：${info.remark}\n`
        formData = { ...formData, ...info, isSupply: 'YES' }
      }
      if (purchaseOrderStatus == 1000) {
        this.orderConfirmData.purchaseOrderStatus = 1000
        this.orderConfirmData.jwReamrk = this.detail.remark
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        const now = new Date().toLocaleString('zh-CN')
        info.remark = `${this.detail.remark} 供应商报价(${now})：${info.remark}\n`
        formData = { ...formData, ...info, isSupply: 'YES' }
      }
      if (purchaseOrderStatus == 999) {
        if (this.detail.stockUpDays == 'null') {
          this.$dialog.message.error('备货天数不能为空')
          return
        }
        let formData1 = {
          deliveryDate: this.detail.deliveryDate,
          portId: this.detail.enquiryPortId,
          stockUpDays: this.detail.stockUpDays,
        }
        this.orderConfirmData.purchaseOrderStatus = 999
        formData = { ...formData, ...formData1, isSupply: 'YES', remark: '' }
      }
      if (purchaseOrderStatus == 998) {
        this.orderConfirmData.purchaseOrderStatus = 998
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        const now = new Date().toLocaleString('zh-CN')
        info.remark = `${this.detail.remark} 机务主管退回(${now})：${info.remark}\n`
        formData = { ...formData, ...info, isSupply: 'NO' }
      }
      if (purchaseOrderStatus == 997) {
        this.orderConfirmData.purchaseOrderStatus = 997
        const info = await this.$refs.dialog.confirm()
        if (!info) return
        const now = new Date().toLocaleString('zh-CN')
        info.remark = `${this.detail.remark} 采购主管退回(${now})：${info.remark}\n`
        formData = { ...formData, ...info, isSupply: 'NO' }
      }
      this.detail.isSupply = formData.isSupply
      this.loading = true
      // 供应商发货 修改运费、明细等信息
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/updatePurchaseOrderMaterGetPrice',
        this.detail,
      )
      if (errorRaw) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseOrderStatusUpdate',
        formData,
      )
      this.loading = false
      if (!data) return
      this.$dialog.message.success('操作成功')
      this.$refs.detail.closeAndTo(this.backRouteName)
    },
    sumToatl() {
      console.log(11111111111111)
      this.detail.detailTotalPrice2 = this.isJPY
        ? this.detail.detailList
            .reduce(
              (x, { discountPrice, purchaseNum }) =>
                x + Math.round(discountPrice * purchaseNum),
              0,
            )
            .toFixed(2)
        : this.detail.detailList
            .reduce(
              (x, { discountPrice, purchaseNum }) =>
                x + Math.round(discountPrice * purchaseNum * 100) / 100,
              0,
            )
            .toFixed(2)
      this.detail.discountedTotalPrice =
        Number(this.detail.detailTotalPrice2) +
        Number(this.detail.otherExpenses)
    },
    async changeMaterial(event, item) {
      // 获取当前换货物料是否有年度价格、无年度价格可以修改；有年度价格自动带出单价
      // event 重新选择后的物料
      // item 当前物料订单明细信息
      console.log(event)
      console.log(item)
      if (item.initMaterial.id == event.id) {
        //未换货
        // 不做处理
      } else {
        //换货，校验物料是否重复
        if (
          this.detail.detailList.some(
            (s) => s.itemId === event.id && s.id != item.id,
          )
        ) {
          this.$dialog.message.error('物料已存在，请勿重复添加！')
          // event.id = item.initMaterial.id
          // event.nameCn == item.initMaterial.nameCn
          item.materialName = item.initMaterial.nameCn
          item.itemId = item.initMaterial.id
          item['initMaterial'] = {
            nameCn: item.materialName,
            id: item.itemId,
          }

          // console.log(3333333, item)
          // console.log(
          //   444444444,
          //   this.$set(
          //     this.detail.detailList,
          //     this.detail.detailList.findIndex((s) => s.id === item.id),
          //     item,
          //   ),
          // )
          // this.$set(
          //   this.detail.detailList,
          //   this.detail.detailList.findIndex((s) => s.id === item.id),
          //   item,
          // )
          // this.detail.detailList = this.detail.detailList.filter(
          //   (s) => s.id !== null,
          // )
        } else {
          item['initMaterial'] = {
            nameCn: event.nameCn,
            id: event.id,
          }
          item.itemId = event.id
          console.log(item)
          // 获取物料是否含有单价
          const { data, errorRaw } = await this.postAsync(
            '/business/shipAffairs/purchaseManage/purchaseOrderChangeMaterialGetPrice',
            item,
          )
          if (errorRaw) return
          item = data
          this.$set(
            this.detail.detailList,
            this.detail.detailList.findIndex((s) => s.id === item.id),
            item,
          )
        }
      }
      // this.materialIds = this.detail.detailList
      //   .map((item) => item.itemId)
      //   .join(',')
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async savAttachs() {
      if (!this.detail.invoiceCode) {
        this.$dialog.message.error('请填写发票号')
        return false
      }
      if (!this.detail.invoiceDate) {
        this.$dialog.message.error('请填写发票日期')
        return false
      }
      if (this.detail.money <= 0) {
        this.$dialog.message.error('请填写发票金额')
        return false
      }
      if (!this.detail.signDate) {
        this.$dialog.message.error('请填写签收日期')
        return false
      }
      if (this.detail.attachmentIds.length === 0) {
        this.$dialog.message.error('请上传发票附件！')
        return false
      }
      // 供应商发货 修改运费、明细等信息
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/savePurchaseOrderAttachmentIds',
        this.detail,
      )
      if (errorRaw) return
      this.$dialog.message.success('操作成功')
      this.$refs.detail.closeAndTo(this.backRouteName)
    },
  },

  mounted() {
    this.loadDetail()
    this.loadTypes()
    this.loadCom()
  },
}
</script>

<style>
/* 隐藏所有数字输入框的增减按钮 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
/* Firefox 兼容 */
/* input[type='number'] {
  -moz-appearance: textfield;
} */
</style>
