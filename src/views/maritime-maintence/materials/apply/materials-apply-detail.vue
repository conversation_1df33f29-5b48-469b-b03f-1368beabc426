<template>
  <v-container fluid>
    <v-detail-view
      :title="`物料申请-${isEdit ? detail.applyCode : '新增'}`"
      :tooltip="isEdit ? detail.applyCode : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      v-permission="['物料申请:编辑']"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: detail.systemReportId },
          }"
          color="info"
          small
          class="mx-1"
          v-permission="['物料申请:查看部门报表']"
        >
          查看部门报表
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  readonly
                  outlined
                  dense
                  v-model="detail.applyDate"
                  use-today
                  label="申请日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  :readonly="!canSubmit"
                  dense
                  outlined
                  v-model="detail.applyDept"
                  label="申请部门"
                  :items="depts"
                  :rules="[rules.required]"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :readonly="!canSubmit"
                  dense
                  outlined
                  v-model="detail.applyType"
                  label="申请类型"
                  :items="applyTypes"
                  :rules="[rules.required]"
                ></v-select>
              </v-col>

              <!-- <v-col cols="12" md="3">
                <v-select
                  :readonly="isEdit"
                  v-model="detail.materialType"
                  :items="types"
                  outlined
                  dense
                  label="物料分类"
                  :rules="[rules.required]"
                ></v-select>
              </v-col> -->
              <v-col cols="12" md="3">
                <port-select-dialog
                  :readonly="
                    detail.businessStatus !== '通导信息主管' &&
                    detail.businessStatus !== '机务主管' &&
                    !canSubmit
                  "
                  v-model="detail.portId"
                  :initSelected="initPort"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="
                    detail.businessStatus !== '通导信息主管' &&
                    detail.businessStatus !== '机务主管' &&
                    !canSubmit
                  "
                  outlined
                  dense
                  v-model="detail.arriveDate"
                  label="到港日期"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3" v-if="false">
                <v-select
                  dense
                  v-model="detail.isDockRepair"
                  label="坞修"
                  outlined
                  :items="yn"
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dialog-select
                  req-url="/business/shipAffairs/costSubject/page"
                  label="费用科目"
                  v-model="detail.costSubjectId"
                  :rules="[rules.required]"
                  :init-selected="detail.initSubject"
                  :search-remain="searchObj"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  :readonly="!canEdit"
                  required
                  dense
                  fuzzy-label="模糊搜索"
                  :disabled="!subjectType"
                ></v-dialog-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.applicantUserId"
                  label="申请人"
                  dense
                  use-current
                  :init-user="initUser"
                  :rules="[rules.required]"
                  disabled
                ></v-handler>
              </v-col>
              <!-- <v-col cols="12">
                <v-textarea
                  v-model="detail.applyPurpose"
                  label="申请目的"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col> -->
              <!-- 共用预算管理审批供应商表 -->
              <v-col cols="12" md="3" v-if="!isShip">
                <end-time-picker
                  v-model="detail.openDate"
                  label="报价截止时间"
                  :rules="[rules.required]"
                ></end-time-picker>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="detail.remark"
                  label="备注"
                  dense
                  :readonly="!canSubmit"
                  outlined
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <!-- 共用预算管理审批供应商表 -->
      <template v-if="detail.status != '3' && !isShip" #推荐供应商按钮>
        <v-btn
          :disabled="!detail.shipCode"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createSup"
          v-permission="['推荐供应商:选择供应商']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择供应商
        </v-btn>
        <v-btn
          :disabled="!selectedSup"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delSup"
          v-permission="['推荐供应商:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-if="!isShip" #推荐供应商>
        <v-form ref="form2">
          <v-table-list
            item-key="vid"
            v-model="selectedSup"
            :headers="supplierHeaders"
            :items="supplyPriceModifyList"
          >
            <template
              v-if="detail.status != 3"
              v-slot:[`item.remark`]="{ item }"
            >
              <!-- <v-text-field
                v-model="item.remark"
                label="申请理由"
                single-line
                dense
              ></v-text-field> -->
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    @click="editRemark2(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.remark"
                    label="推荐理由"
                    single-line
                    dense
                  ></v-text-field>
                </template>
                <span>{{ item.remark }}</span>
              </v-tooltip>
            </template>
          </v-table-list>
        </v-form>
      </template>
      <template v-if="detail.status != '3'" #物料列表按钮>
        <v-btn
          :disabled="!detail.costSubjectId"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-if="canEdit"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择物料
        </v-btn>
        <v-btn
          :disabled="!select"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCom"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #物料列表>
        <v-form ref="form2">
          <v-table-list
            v-model="select"
            item-key="materialId"
            :headers="componentHeaders"
            :items="components"
          >
            <template
              v-if="detail.status == 2"
              v-slot:[`item.auditQuantity`]="{ item }"
            >
              <v-text-field
                v-model="item.auditQuantity"
                label="审批数量"
                type="number"
                single-line
                dense
                :rules="[rules.required, rules.decimal, rules.aboveZero]"
              ></v-text-field>
            </template>
            <template
              v-if="detail.status != 2 && detail.status != 3"
              v-slot:[`item.applyNumber`]="{ item }"
            >
              <v-text-field
                v-model="item.applyNumber"
                label="申请数量"
                type="number"
                single-line
                dense
                :rules="[rules.required, rules.decimal, rules.aboveZero]"
              ></v-text-field>
            </template>
            <template
              v-if="detail.status != 2 && detail.status != 3"
              v-slot:[`item.remark`]="{ item }"
            >
              <v-text-field
                v-model="item.remark"
                label="申请理由"
                single-line
                dense
              ></v-text-field>
            </template>
          </v-table-list>
        </v-form>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <materials-select
      v-model="dialog"
      :searchRemain="searchObj2"
      :components.sync="components"
      :subject-id="detail.costSubjectId"
      :subject-type="subjectType"
    ></materials-select>
    <v-dialog v-model="dialog2" max-width="600">
      <template v-slot:default="dialog2">
        <v-card style="height: 320px">
          <v-card-title>
            编辑推荐理由
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark2"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog2.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-0" style="color: black">
                  供应商：{{ editRemarkDetails2.supplierName }}
                </v-col>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="推荐理由"
                    v-model="editRemarkDetails2.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <recommend-sup
      v-model="dialogSup"
      @success="success"
      :initial-data="initialData"
      :shipCode="detail.shipCode"
      :happenDate="detail.openDate"
    ></recommend-sup>
  </v-container>
</template>
<script>
import PortSelectDialog from '../../components/port-select-dialog.vue'
import recommendSup from '../../spare-part/apply/private/recommendSup.vue'
import materialsSelect from './private/materials-select.vue'
import EndTimePicker from '@/views/maritime-maintence/components/enquiry/end-time-picker.vue'

export default {
  components: {
    EndTimePicker,
    PortSelectDialog,
    materialsSelect,
    recommendSup,
  },
  name: 'materials-apply-detail',
  created() {
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.backRouteName = 'materials-apply-list'
    this.subtitles = ['基本信息', '推荐供应商', '物料列表']
    this.depts = ['甲板部', '轮机部']
    this.applyTypes = [
      { text: '常规', value: 1 },
      { text: '紧急', value: 2 },
      { text: '坞修', value: 3 },
      // { text: '特急', value: 3 },
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
    subjectType() {
      if (this.detail.applyType == 3) {
        return '坞修费'
      } else if (this.detail.applyType == 2) {
        return '应急物料费'
      } else if (this.detail.applyType == 1) {
        return '招标物料费'
      } else {
        return ''
      }
    },
  },
  data() {
    return {
      detail: {
        applicationNo: '',
        shipInfo: {
          shipCode: '',
        },
        attachmentIds: [],
        costSubjectId: '',
        supplyPriceModifyList: [],
      },
      numbers: [],
      mapping: {},
      needFields: [],
      select: false,
      componentHeaders: [
        { text: '物料名称', value: 'nameCn' },
        { text: '物料编码', value: 'code' },
        { text: '物料描述', value: 'materialDescription' },
        { text: '单位', value: 'unit' },
        { text: 'IMPA', value: 'nameEn' },
        // { text: '物料型号', value: 'model' },
        { text: '库存数量', value: 'stockQuantity' },
        { text: '申请数量', value: 'applyNumber', width: 100 },
        { text: '审批数量', value: 'auditQuantity', width: 150 },
        { text: '申请理由', value: 'remark', width: 300 },
      ],
      components: [],
      delList: [],
      dialog: false,
      searchObj: { subjectType: '', materialsApply: '物料申请' },
      searchObj2: { subjectId: '' },
      initEngine: {},
      initPort: {},
      rules: {
        required: (v) => !!v || v === false || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
      },
      types: [],
      initUser: false,
      supplierHeaders: [
        { text: '供应商名称', value: 'supplierName' },
        { text: '币种', value: 'currency' },
        { text: '推荐理由', value: 'remark' },
      ],
      dialog2: false,
      editRemarkDetails2: {},
      initialData: {},
      selectedSup: false,
      dialogSup: false,
      supplyPriceModifyList: [],
    }
  },
  watch: {
    'detail.shipCode'(_, oldVal) {
      if (!oldVal) return
      this.clearEngine()
    },
    'detail.equipmentId'(_, oldVal) {
      if (!oldVal) return
      this.clearComponents()
    },
    subjectType: {
      handler(val) {
        this.searchObj.subjectType = val
      },
      immediate: true,
    },
    'detail.applyType'(val) {
      if (val) {
        if (val == 3) {
          this.detail.isDockRepair = true
        } else if (val == 1) {
          this.detail.isDockRepair = false
        } else if (val == 2) {
          this.detail.isDockRepair = false
        }
        if (!this.isEdit) {
          this.detail.initSubject = {}
          this.detail.costSubjectId = ''
        }

        //   if (this.detail.isDockRepair) {
        //   return '坞修费'
        // } else if (this.detail.applyType == 2) {
        //   return '应急物料费'
        // } else if (this.detail.applyType == 1) {
        //   return '招标物料费'
        // } else {
        //   return ''
        // }
        // if (val == 4) {
        //   this.searchObj.notFixed = false
        // } else {
        //   this.searchObj.notFixed = true
        // }
      }
    },
    'detail.costSubjectId'(val) {
      if (val) {
        if (!this.isEdit) this.components = []
      }
    },
  },

  methods: {
    async loadTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        { current: 1, size: 1000 },
        false,
      )
      this.types = data.records.map((item) => ({
        ...item,
        value: item.id,
        text: item.cateName,
      }))
    },
    async save(goBack, notMove = false) {
      if (this.detail.businessStatus == '机务主管') {
        if (!this.detail.portId) {
          this.$dialog.message.error('请选择交货港口')
          return false
        }
      }
      if (!this.$refs.form.validate() || !this.$refs.form2.validate()) {
        return false
      }
      if (this.components.length === 0) {
        this.$dialog.message.warning('请添加物料')
        return false
      }
      const detailList = this.getCompWithOperation()
      console.log(this.detail.supplyPriceModifyList)
      const delList = this.detail.supplyPriceModifyList
        .filter((i) => !this.supplyPriceModifyList.includes(i))
        .map((i) => {
          return { id: i.id, operationType: 3 }
        })
      console.log(delList)
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/materialApplySaveOrUpdate',
        {
          ...this.detail,
          detailList,
          supplyPriceModifyList: [...this.supplyPriceModifyList, ...delList],
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            processInstanceId: this.detail.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent:
              this.mapping[f.mappingCode] ||
              this.$local.data.get('userInfo').id,
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) return
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/purchaseManage/materialApplySubmit',
            { applyId: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },

    async loadNeedFields() {
      if (!this.detail?.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        { processInstanceId: this.detail.auditParams.processInstanceId },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          this.mapping[t.mappingCode] = new Date(Date.now())
            .toISOString()
            .substr(0, 10)
        }
      }
    },

    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/materialApplyDetailById',
        { applyId: this.$route.params.id },
      )
      this.detail = {
        ...data,
        shipCode: data.shipInfo.shipCode,
        initSubject: {
          subjectName: data.costSubjectName,
          id: data.costSubjectId,
        },
      }
      this.components = data.detailList.map((i) => ({
        ...i,
        nameCn: i.materialCnName,
        nameEn: i.materialEnName,
        code: i.materialCode,
        description: i.materialDescription,
        model: i.materialModel,
        specs: i.materialSpec,
        unit: i.materialUnit,
      }))
      this.initPort = {
        portCn: data.port,
        id: data.portId,
      }
      this.supplyPriceModifyList = data.supplyPriceModifyList.map((s) => {
        return { ...s, vid: s.id, operationType: 0 }
      })
      this.detail.supplyPriceModifyList = this.supplyPriceModifyList
      this.initUser = {
        nickName: data.applicantName,
        id: data.applicantUserId,
      }
      await this.loadNeedFields()
    },

    createCom() {
      // this.searchObj = { type: this.detail.materialType }
      this.searchObj2 = { subjectId: this.detail.costSubjectId }
      this.dialog = true
    },
    async delCom() {
      // this.delList.push({ ...this.select, operationType: 3 })
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.components = this.components.filter(
        (i) => i.materialId !== this.select.materialId,
      )
      this.select = false
    },

    async clearEngine() {
      this.$dialog.message.info('由于船舶变更,自动清空设备')
      this.detail.equipmentId = ''
    },
    clearComponents() {
      this.$dialog.message.info('由于设备变更,自动清空物料/序列号')
      this.detail.equipmentNumber = ''
      this.numbers = []
      this.components = []
    },

    getCompWithOperation() {
      const ids = this.components.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.detailList
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.components.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },

    checkMaxInv() {
      for (const { stockQuantity, maximumInventory, requireQuantity } of this
        .components) {
        if (stockQuantity + requireQuantity > maximumInventory) {
          this.$dialog.message.error('申请数不得超出最大库存量!')
          return false
        }
      }
      return true
    },
    editRemark2(item) {
      // console.log(item)
      console.log(item)
      this.editRemarkDetails2 = item
      this.dialog2 = true
    },
    saveRemark2() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog2 = false
    },
    createSup() {
      this.initialData = {}
      this.dialogSup = true
    },
    updateSup() {
      this.initialData = this.selectedSup
      this.dialogSup = true
    },
    delSup() {
      this.supplyPriceModifyList = this.supplyPriceModifyList.filter(
        (s) => !(s.vid === this.selectedSup.vid),
      )
    },
    success(newSup) {
      if (
        this.supplyPriceModifyList.some(
          (s) => s.supplierId === newSup.supplierId,
        )
      ) {
        this.$dialog.message.error('供应商重复')
        return
      }
      this.supplyPriceModifyList.push(newSup)
    },
  },

  mounted() {
    this.loadDetail()
    this.loadTypes()
  },
}
</script>

<style></style>
