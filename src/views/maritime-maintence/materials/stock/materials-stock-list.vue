<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="isShip ? headersShip : headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      show-expand
      :show-select="false"
      :specialHeaders="specialHeaders"
      :showExportButton="true"
    >
      <template v-slot:[`item.isDockRepair`]="{ item }">
        {{ item.isDockRepair ? '是' : '否' }}
      </template>
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.materialsType"
            outlined
            dense
            clearable
            label="物料分类"
            :items="types"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.equipmentNameLike"
            label="物料名称"
            outlined
            dense
            :loading="loading"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.equipmentCodeLike"
            label="物料号"
            outlined
            dense
            :loading="loading"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="3">
          <v-switch
            class="mt-1"
            v-model="searchObj.all"
            :label="searchObj.all ? '显示全部' : '显示非0'"
            color="primary"
          ></v-switch>
        </v-col>
      </template>
      <template #btns></template>
      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length">
          <stock-detail
            :depositoryId="item.depositoryId"
            :itemId="item.itemId"
          ></stock-detail>
        </td>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import stockDetail from './private/stock-detail.vue'
export default {
  components: { stockDetail },
  name: 'materials-stock-list',
  created() {
    this.tableName = '物料库存'
    this.reqUrl =
      '/business/shipAffairs/purchaseManage/purchaseStocksMsgByParams'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      // { text: '船舶', value: 'shipInfo' },
      { text: '船舶', value: 'chShipName' },
      { text: '物料名', value: 'itemName' },
      { text: '物料号', value: 'itemNo' },
      { text: '物料属性', value: 'componentProperty' },
      { text: '仓库', value: 'depositoryName' },
      // { text: '仓库位置', value: 'depositoryAddress' },
      { text: '坞修', value: 'isDockRepair' },
      { text: '总金额', value: 'totalPrice' },
      { text: '库存数量', value: 'itemNumber' },
      { text: '单位', value: 'unit' },
      { text: '最低库存量', value: 'minimumInventory' },
      { text: '最高库存量', value: 'maximumInventory' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '物料名', value: 'itemName' },
      { text: '物料号', value: 'itemNo' },
      { text: '物料属性', value: 'componentProperty' },
      { text: '仓库', value: 'depositoryName' },
      // { text: '仓库位置', value: 'depositoryAddress' },
      { text: '坞修', value: 'isDockRepair' },
      { text: '库存数量', value: 'itemNumber' },
      { text: '单位', value: 'unit' },
      { text: '最低库存量', value: 'minimumInventory' },
      { text: '最高库存量', value: 'maximumInventory' },
    ]
    this.specialHeaders = [
      {
        text: 'isDockRepair',
        value: [
          { text: true, value: '是' },
          { text: false, value: '否' },
        ],
      },
    ]
  },

  watch: {
    'searchObj.shipCode'(val) {
      if (val) this.loadFirstEquipment()
    },
  },

  data() {
    return {
      selected: false,
      searchObj: { stocksType: '1' },
      types: [],
    }
  },

  methods: {
    async loadFirstEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { current: 1, size: 99, shipCode: this.shipCode },
      )
      const { records } = data
      this.firstEquipments = records?.map((i) => {
        return {
          text: i.equipmentCname,
          value: i.id,
        }
      })
    },
    async loadTypes() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/MaterialInfo/cate/page',
        { current: 1, size: 1000 },
        false,
      )
      this.types = data.records.map((item) => ({
        ...item,
        value: item.id,
        text: item.cateName,
      }))
    },
  },

  mounted() {
    this.loadTypes()
  },
}
</script>

<style></style>
