<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        年度计划设备列表
        <v-spacer></v-spacer>
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="confirm"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          :show-select="false"
        >
          <template #searchflieds></template>
          <template #btns></template>
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo.chShipName }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <!-- <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions> -->
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-dialog-equipment',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.tableName = '年度计划设备列表'
    this.reqUrl = '/business/shipAffairs/planYear/equipmentPage'
    this.headers = [
      { text: '备件名称', value: 'componentEname' },
      { text: '备件号', value: 'componentNumber' },
      { text: '预计消耗数量', value: 'numbers' },
      { text: '库存数量', value: 'stockNumbers' },
      { text: '预计采购数量', value: 'purchaseNumbers' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    components: Array,
    yearPlanId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: { planYearPlanId: this.yearPlanId },
      selected: [],
      targetShipCode: '',
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    yearPlanId(val) {
      this.searchObj.planYearPlanId = val
    },
    // 'searchObj.equipmentSecondId': {
    //   handler(val) {
    //     if (val) this.loadSubEqu()
    //   },
    // },
    // 'searchObj.equipmentInformationId': {
    //   handler(val) {
    //     if (val) this.loadSecondEquipment()
    //   },
    // },
    // searchRemain(val) {
    //   this.searchObj = val
    // },
    // components: {
    //   handler(val) {
    //     this.selected = val.map((i) => {
    //       return { ...i, vid: i.id, id: i.componentId, remarkk: i.remark }
    //     })
    //   },
    //   deep: true,
    // },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async confirm() {
      // // const mainIdArr = this.selected.map((i) => {
      // //   const comp = i.id
      // //   return comp
      // // })
      // // console.log(mainIdArr)
      // // console.log(this.targetShipCode)
      // if (!this.targetShipCode) {
      //   this.$dialog.message.error(`请选择目标船舶`)
      //   return
      // }
      // let formData = new FormData()
      // formData.append(
      //   'mainId',
      //   this.selected.map((item) => item.id),
      // )
      // formData.append('shipCode', this.targetShipCode)
      // const { errorRaw } = await this.postAsync(
      //   `/business/shipAffairs/equipmentInformation/copyEquipmentInformation`,
      //   formData,
      //   // {
      //   //   mainId: this.selected.map((item) => item.id),
      //   //   shipCode: this.targetShipCode,
      //   // },
      // )
      // if (errorRaw) {
      //   this.$dialog.message.error(`复制失败，请重试`)
      //   return
      // }
      // this.$dialog.message.success('复制成功')
      const { errorRaw } = await this.blobDownload(
        `/business/shipAffairs/planYear/exportEquipmentExcel`,
        { yearPlanId: this.yearPlanId },
      )
      if (errorRaw) {
        return
      }
      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
