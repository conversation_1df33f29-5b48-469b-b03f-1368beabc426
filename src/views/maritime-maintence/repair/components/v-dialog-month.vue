<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    max-width="2000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>未在本月度计划</v-card-title>
      <v-card-text>
        <v-table-list
          ref="table"
          :headers="headers"
          :items="items"
          v-model="selected"
        >
          <template v-slot:[`item.maintenanceCycle`]="{ item }">
            {{ item.maintenanceCycle }}
            <span v-if="item.maintenanceType == '定时'">
              / {{ item.maintenanceCycleTime }}H
            </span>
          </template>
        </v-table-list>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="addItem">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-dialog-month',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    params: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      selected: false,
      dialog: false,
      formData: {},
      items: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  created() {
    this.headers = [
      { text: '项目编号', value: 'projectCode' },
      { text: '维护保养部位', value: 'maintenanceSite' },
      { text: '维修保养内容', value: 'maintenanceContent' },
      { text: '首次执行日期', value: 'execDate' },
      { text: '检修周期', value: 'maintenanceCycle' },
      { text: '1月', value: 'january' },
      { text: '2月', value: 'february' },
      { text: '3月', value: 'march' },
      { text: '4月', value: 'april' },
      { text: '5月', value: 'may' },
      { text: '6月', value: 'june' },
      { text: '7月', value: 'july' },
      { text: '8月', value: 'auguest' },
      { text: '9月', value: 'september' },
      { text: '10月', value: 'october' },
      { text: '11月', value: 'november' },
      { text: '12月', value: 'december' },
      { text: '检修类型', value: 'maintenanceType' },
      { text: '检修负责人', value: 'maintenancePrincipal' },
      { text: '备注', value: 'remark' },
    ]
    this.getUnMonthInfo()
  },
  methods: {
    async addItem() {
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/monthMonth/selectOneNOMonthPlanItem`,
        {
          planMonthId: this.params,
          planYearItemId: this.selected.id,
        },
        false,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`保存成功`)
      this.selected = false
      this.getUnMonthInfo()
      this.$emit('getMonthDetail')
      this.$emit('change', false)
    },
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = this.isEdit ? '' : ''
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
    async getUnMonthInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/monthMonth/getNOMonthPlans`,
        {
          id: this.params,
        },
      )
      if (!errorRaw) {
        this.items = data.records
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
