<template>
  <div>
    <v-divider></v-divider>
    <v-data-table
      show-select
      single-select
      :headers="headers"
      :items="items"
      hide-default-footer
      disable-pagination
      v-model="selected"
      :item-key="itemKey"
      @click:row="selectRow"
      dense
      class="use-divider"
    >
      <template v-for="h in headers" v-slot:[`item.${h.value}`]="{ item }">
        <slot
          v-if="h.value === 'attachmentRecords'"
          :item="item"
          :name="`item.${h.value}`"
        >
          <v-btn
            @click.stop="openAttachmentDialog(item.attachmentRecords)"
            dark
            x-small
            color="primary"
            elevation="0"
          >
            {{ item.attachmentRecords.length }}
          </v-btn>
        </slot>
        <slot v-else :item="item" :name="`item.${h.value}`">
          {{ item[h.value] }}
        </slot>
      </template>
    </v-data-table>
    <v-divider></v-divider>
    <v-dialog v-model="attachmentDialog" max-width="700" hide-overlay>
      <v-card>
        <v-card-title class="text-h5">附件列表</v-card-title>
        <v-card-text>
          <v-data-table
            :headers="attachmentHeader"
            :items="attachments"
            hide-default-footer
          >
            <template v-slot:[`item.name`]="{ item }">
              <v-btn
                :href="`/api/system/file/download?fileName=${encodeURIComponent(
                  item.name,
                )}&filePath=${item.filePath}`"
                target="_blank"
                dark
                x-small
                color="primary"
                elevation="0"
              >
                {{ item.name }}
              </v-btn>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
export default {
  name: 'v-table-list',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    headers: {
      type: Array,
      default: () => [],
    },
    items: {
      type: Array,
      default: () => [],
    },
    value: [Object, Boolean],
    itemKey: {
      type: String,
      default: 'id',
    },
  },
  data() {
    return {
      selected: [],
      attachmentDialog: false,
      attachments: [],
      attachmentHeader: [
        { text: '名称', value: 'name' },
        { text: '大小(kb)', value: 'fileSize' },
        { text: '上传时间', value: 'createTime' },
        { text: '上传人', value: 'userName' },
      ],
    }
  },

  watch: {
    selected(val) {
      if (val.length > 0) {
        this.$emit('change', val[0])
      } else {
        this.$emit('change', false)
      }
    },
    value(val) {
      if (!val) {
        this.selected = []
      }
    },
  },

  methods: {
    selectRow(_, { isSelected, item }) {
      this.selected = isSelected ? [] : [item]
      this.$emit('change', !isSelected && item)
    },
    openAttachmentDialog(attachmentRecords) {
      this.attachments = attachmentRecords
      this.attachmentDialog = true
    },
  },

  mounted() {},
}
</script>

<style></style>
