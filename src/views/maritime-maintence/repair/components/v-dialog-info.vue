<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }} 年度计划子项目
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '保存' : '创建' }}
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3" class="py-0">
                <v-text-field
                  label="项目编号"
                  v-model="formData.projectCode"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="设备类别"
                  :items="equipmentType"
                  v-model="formData.equipType"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="设备"
                  v-model="formData.equipment"
                  :items="equipmentValue"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="检修负责人"
                  :items="stationItems"
                  v-model="formData.maintenancePrincipal"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-select>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="检修类型"
                  v-model="formData.maintenanceType"
                  :items="['定时', '定期']"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-select>
              </v-col>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="formData.maintenanceType === '定时'"
              >
                <v-select
                  label="上次维护保养类型"
                  v-model="formData.lastType"
                  :items="['维护保养', '换新']"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-select>
              </v-col>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="formData.maintenanceType === '定时'"
              >
                <v-text-field
                  label="总运行小时"
                  v-model="formData.totalTime"
                  outlined
                  dense
                  type="number"
                  :rules="[rules.required]"
                  :disabled="hasTotalTime || formData.id"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="formData.maintenanceType === '定时'"
              >
                <v-text-field
                  label="定时检修周期(H)"
                  v-model="formData.maintenanceCycleTime"
                  outlined
                  dense
                  type="number"
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="formData.maintenanceType === '定时'?3:9"
                class="py-0"
              ></v-col>
              <!-- </v-row> -->
              <!-- <v-card-title v-if="formData.maintenanceType === '定时'">
              初始化定时项目
            </v-card-title> -->
              <!-- <v-row> -->

              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="
                  (formData.maintenanceType === '定时' &&
                    formData.lastType === '维护保养') ||
                  formData.maintenanceType === '定期'
                "
              >
                <vs-date-picker
                  label="上次维护保养日期"
                  v-model="formData.maintenanceTime"
                  :maxDate="`${initialData.year}-12-31`"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></vs-date-picker>
              </v-col>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="
                  formData.maintenanceType === '定时' &&
                  formData.lastType === '维护保养'
                "
              >
                <v-text-field
                  label="维护时运行小时"
                  v-model="formData.curMaintenanceTime"
                  outlined
                  dense
                  type="number"
                  :disabled="formData.id"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="
                  formData.maintenanceType === '定时' &&
                  formData.lastType === '维护保养'
                "
              >
                <v-text-field
                  label="维护后运行小时"
                  v-model="formData.afterMaintenanceTime"
                  outlined
                  dense
                  readonly
                  :disabled="formData.id"
                ></v-text-field>
              </v-col>
              <!-- <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="
                  formData.maintenanceType === '定时' &&
                  formData.maintenanceType === '定时'
                "
              ></v-col> -->
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="
                  formData.maintenanceType === '定时' &&
                  formData.lastType === '换新'
                "
              >
                <vs-date-picker
                  label="换新日期"
                  v-model="formData.maintenanceTime"
                  :maxDate="`${initialData.year}-12-31`"
                  outlined
                  dense
                  :disabled="formData.id"
                ></vs-date-picker>
              </v-col>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="
                  formData.maintenanceType === '定时' &&
                  formData.lastType === '换新'
                "
              >
                <v-text-field
                  label="换新时运行小时"
                  v-model="formData.curMaintenanceTime"
                  outlined
                  dense
                  type="number"
                  :disabled="formData.id"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="
                  formData.maintenanceType === '定时' &&
                  formData.lastType === '换新'
                "
              >
                <v-text-field
                  label="换新后运行小时"
                  v-model="formData.afterMaintenanceTime"
                  outlined
                  dense
                  readonly
                  :disabled="formData.id"
                ></v-text-field>
              </v-col>
              <!-- </v-row>
            <v-row> -->
              <!-- <v-col cols="12" md="3" class="py-0">
                <vs-date-picker
                  label="上次维护保养日期"
                  v-model="formData.maintenanceTime"
                  :maxDate="`${initialData.year}-12-31`"
                  outlined
                  dense
                ></vs-date-picker>
              </v-col> -->
              <!-- <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="formData.maintenanceType === '定时'"
              >
                <v-text-field
                  label="定时检修周期（H） 维护周期"
                  v-model="formData.maintenanceCycleTime"
                  outlined
                  dense
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="6"
                class="py-0"
                v-if="formData.maintenanceType === '定时'"
              ></v-col> -->
              <!-- <v-col
                cols="12"
                md="3"
                class="py-0"
                v-if="formData.maintenanceType === '定时'"
              ></v-col> -->
              <v-col cols="12" md="3" class="py-0">
                <v-text-field
                  label="定期检修周期"
                  v-model="formData.maintenanceCycle"
                  outlined
                  dense
                  type="number"
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <v-select
                  label="定期日期单位"
                  :items="['月', '周']"
                  v-model="formData.unit"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :disabled="formData.id"
                ></v-select>
                <!-- <v-select
                  v-else
                  label="定期日期单位"
                  :items="['月', '周']"
                  v-model="formData.unit"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></v-select> -->
              </v-col>
              <v-col cols="12" md="3" class="py-0">
                <vs-date-picker
                  label="下次执行时间"
                  v-model="formData.execDate"
                  :maxDate="`${initialData.year}-12-31`"
                  outlined
                  dense
                  :rules="[rules.required]"
                  readonly
                  :disabled="formData.id"
                ></vs-date-picker>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" class="d-flex justify-space-between mb-6 py-0">
                <v-checkbox
                  label="1月"
                  value="√"
                  v-model="formData.january"
                  @change="maintenanceJan"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="2月"
                  value="√"
                  v-model="formData.february"
                  @change="maintenanceFeb"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="3月"
                  value="√"
                  v-model="formData.march"
                  @change="maintenanceMar"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="4月"
                  value="√"
                  v-model="formData.april"
                  @change="maintenanceApr"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="5月"
                  value="√"
                  v-model="formData.may"
                  @change="maintenanceMay"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="6月"
                  value="√"
                  v-model="formData.june"
                  @change="maintenanceJun"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="7月"
                  value="√"
                  v-model="formData.july"
                  @change="maintenanceJul"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="8月"
                  value="√"
                  v-model="formData.auguest"
                  @change="maintenanceAug"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="9月"
                  value="√"
                  v-model="formData.september"
                  @change="maintenanceSep"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="10月"
                  value="√"
                  v-model="formData.october"
                  @change="maintenanceOct"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="11月"
                  value="√"
                  v-model="formData.november"
                  @change="maintenanceNov"
                  readonly
                ></v-checkbox>
                <v-checkbox
                  label="12月"
                  value="√"
                  v-model="formData.december"
                  @change="maintenanceDec"
                  readonly
                ></v-checkbox>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" class="py-0">
                <v-textarea
                  label="维护保养部位"
                  v-model="formData.maintenanceSite"
                  rows="2"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  label="维修内容"
                  v-model="formData.maintenanceContent"
                  outlined
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  label="备注"
                  outlined
                  dense
                  v-model="formData.remark"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
            <template>
              <v-spacer></v-spacer>
              <v-btn
                outlined
                tile
                color="success"
                class="mx-1"
                small
                @click.stop="addEquments"
              >
                <v-icon left>mdi-plus-circle</v-icon>
                新增备件
              </v-btn>
              <v-btn
                small
                outlined
                tile
                class="mx-1"
                color="error"
                :disabled="!select"
                @click="delEquments"
              >
                <v-icon>mdi-close</v-icon>
                删除备件
              </v-btn>
            </template>
            <v-table-list
              v-model="select"
              item-key="componentId"
              :headers="headers"
              :items="components"
            >
              <template v-slot:[`item.numbers`]="{ item }">
                <v-text-field
                  v-model="item.numbers"
                  label="预计消耗数量"
                  type="number"
                  single-line
                  dense
                  :rules="[rules.required2, rules.int, rules.aboveZero]"
                ></v-text-field>
              </template>
            </v-table-list>
            <spare-part-select
              v-model="dialog2"
              :searchRemain="searchObj"
              :shipCode="initialData.shipCode"
              :components.sync="components"
            ></spare-part-select>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import { cacheGetDefault } from '@/util/cache'
import dictHelper from '@/mixin/dictHelper'
import SparePartSelect from './spare-part-select.vue'
export default {
  components: { SparePartSelect },
  name: 'v-dialog-info',
  model: {
    prop: 'open',
    event: 'change',
  },
  mixins: [dictHelper],
  activated() {
    this.dialog = this.open
  },
  created() {
    this.headers = [
      { text: '备件名称', value: 'componentEname' },
      { text: '备件号', value: 'componentNumber' },
      { text: '预计消耗数量', value: 'numbers' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialog: false,
      formData: {
        january: '',
        february: '',
        march: '',
        april: '',
        may: '',
        june: '',
        july: '',
        auguest: '',
        september: '',
        october: '',
        november: '',
        december: '',
      },
      equidistantArray: [],
      selectedMonth: [],
      equipmentType: [],
      equipmentList: [],
      equipmentValue: [],
      stationItems: [],
      equalDifferenceNumber: 0,
      canSelect: true,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        required2: (v) => !!v || v === false || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
      },
      hasTotalTime: true,
      searchObj: { shipCode: this.initialData.shipCode },
      dialog2: false,
      components: this.initialData.components,
      select: false,
    }
  },
  watch: {
    formData: {
      handler() {
        if (this.formData.id) return
        this.formData.afterMaintenanceTime =
          this.formData.totalTime - this.formData.curMaintenanceTime
        this.formData.afterRenewTime =
          this.formData.totalTime - this.formData.curRenewTime
      },
      deep: true,
    },
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.components = this.initialData.components
    },
    'formData.equipType': {
      handler(val) {
        this.equipmentValue = this.equipmentList.map((ele) => {
          if (ele.cssClass === val) {
            return ele.dictValue
          }
        })
        if (this.formData.maintenanceType === '定时') {
          this.getTotalTime()
        }
      },
    },
    'formData.equipment': {
      handler(val) {
        console.log(val)
        if (this.formData.maintenanceType === '定时') {
          this.getTotalTime()
        }
      },
    },
    'formData.maintenanceType': {
      handler(val) {
        if (val === '定时') {
          this.getTotalTime()
        }
        this.canSelect = val === '定期' ? true : false
        this.initAllMonth()
        // this.formData.maintenanceCycleTime = ''
        // this.formData.execDate = ''
        // this.formData.maintenanceCycle = ''
        // this.formData.unit = ''
      },
    },
    'formData.maintenanceTime': {
      handler(val) {
        const myDate = new Date(val)
        if (
          this.formData.unit != undefined &&
          this.formData.maintenanceCycle != undefined
        ) {
          if (this.formData.unit === '周') {
            myDate.setDate(
              myDate.getDate() +
                Number(7) * Number(this.formData.maintenanceCycle),
            )
          } else if (this.formData.unit === '月') {
            myDate.setMonth(
              myDate.getMonth() + Number(this.formData.maintenanceCycle),
            )
          }
          this.formData.execDate = myDate.toISOString().slice(0, 10)
        }
      },
    },
    'formData.unit': {
      handler(val) {
        // if (this.formData.maintenanceType === '定期') {
        if (
          this.formData.maintenanceCycle != undefined &&
          this.formData.maintenanceTime != undefined
        ) {
          const myDate = new Date(this.formData.maintenanceTime)
          if (val === '周') {
            myDate.setDate(
              myDate.getDate() +
                Number(7) * Number(this.formData.maintenanceCycle),
            )
          } else if (val === '月') {
            myDate.setMonth(
              myDate.getMonth() + Number(this.formData.maintenanceCycle),
            )
          }
          this.formData.execDate = myDate.toISOString().slice(0, 10)
        }

        if (val === '周') {
          if (this.formData.maintenanceCycle >= 4) {
            this.$dialog.message.info('检修周期超出一个月，日期单位请选择月')
            this.initAllMonth()
            return
          } else if (this.formData.maintenanceCycle) {
            const map = new Map([
              [1, 'january'],
              [2, 'february'],
              [3, 'march'],
              [4, 'april'],
              [5, 'may'],
              [6, 'june'],
              [7, 'july'],
              [8, 'auguest'],
              [9, 'september'],
              [10, 'october'],
              [11, 'november'],
              [12, 'december'],
            ])

            let month = this.formData.execDate.substring(5, 7) - '0'

            const ans = [month]

            while (month < 12) {
              month = month + 1
              ans.push(month)
            }
            this.initAllMonth()
            for (let i = 0; i < ans.length; i++) {
              let index = map.get(ans[i])
              this.formData[index] = '√'
            }
          } else {
            this.initAllMonth()
            return
          }
        } else if (val === '月') {
          if (!this.formData.execDate) {
            this.$dialog.message.info('请先输入首次执行日期')
            return
          }
          if (!this.formData.maintenanceCycle) {
            this.initAllMonth()
            return
          }

          const map = new Map([
            [1, 'january'],
            [2, 'february'],
            [3, 'march'],
            [4, 'april'],
            [5, 'may'],
            [6, 'june'],
            [7, 'july'],
            [8, 'auguest'],
            [9, 'september'],
            [10, 'october'],
            [11, 'november'],
            [12, 'december'],
          ])

          let month = this.formData.execDate.substring(5, 7) - '0'

          const ans = [month]

          while (month < 12) {
            month = month + (this.formData.maintenanceCycle - '0')
            ans.push(month)
          }

          this.initAllMonth()
          for (let i = 0; i < ans.length; i++) {
            let index = map.get(ans[i])
            this.formData[index] = '√'
          }
        }
        // }
      },
    },
    'formData.maintenanceCycle': {
      handler(val) {
        if (
          this.formData.unit != undefined &&
          this.formData.maintenanceTime != undefined
        ) {
          const myDate = new Date(this.formData.maintenanceTime)
          if (this.formData.unit === '周') {
            myDate.setDate(
              myDate.getDate() +
                Number(7) * Number(this.formData.maintenanceCycle),
            )
          } else if (this.formData.unit === '月') {
            myDate.setMonth(
              myDate.getMonth() + Number(this.formData.maintenanceCycle),
            )
          }
          this.formData.execDate = myDate.toISOString().slice(0, 10)
        }

        // if (this.formData.maintenanceType === '定期') {
        if (this.formData.unit === '周') {
          if (val >= 4) {
            this.$dialog.message.info('检修周期超出一个月，日期单位请选择月')
            this.initAllMonth()
            return
          } else if (val) {
            const map = new Map([
              [1, 'january'],
              [2, 'february'],
              [3, 'march'],
              [4, 'april'],
              [5, 'may'],
              [6, 'june'],
              [7, 'july'],
              [8, 'auguest'],
              [9, 'september'],
              [10, 'october'],
              [11, 'november'],
              [12, 'december'],
            ])

            let month = this.formData.execDate.substring(5, 7) - '0'

            const ans = [month]

            while (month < 12) {
              month = month + 1
              ans.push(month)
            }
            this.initAllMonth()
            for (let i = 0; i < ans.length; i++) {
              let index = map.get(ans[i])
              this.formData[index] = '√'
            }
          } else {
            this.initAllMonth()
            return
          }
        } else if (this.formData.unit === '月') {
          if (!this.formData.execDate) {
            this.$dialog.message.info('请先输入首次执行日期')
            return
          }
          if (!val) {
            this.initAllMonth()
            return
          }

          const map = new Map([
            [1, 'january'],
            [2, 'february'],
            [3, 'march'],
            [4, 'april'],
            [5, 'may'],
            [6, 'june'],
            [7, 'july'],
            [8, 'auguest'],
            [9, 'september'],
            [10, 'october'],
            [11, 'november'],
            [12, 'december'],
          ])

          let month = this.formData.execDate.substring(5, 7) - '0'

          const ans = [month]

          while (month < 12) {
            month = month + (val - '0')
            ans.push(month)
          }
          this.initAllMonth()
          for (let i = 0; i < ans.length; i++) {
            let index = map.get(ans[i])
            this.formData[index] = '√'
          }
        }
        // }
      },
    },
    'formData.execDate': {
      handler(val) {
        if (!val) {
          this.initAllMonth()
          return
        }
        // if (this.formData.maintenanceType === '定期') {
        if (this.formData.unit === '周') {
          if (this.formData.maintenanceCycle >= 4) {
            this.$dialog.message.info('检修周期超出一个月，日期单位请选择月')
            return
          } else {
            const map = new Map([
              [1, 'january'],
              [2, 'february'],
              [3, 'march'],
              [4, 'april'],
              [5, 'may'],
              [6, 'june'],
              [7, 'july'],
              [8, 'auguest'],
              [9, 'september'],
              [10, 'october'],
              [11, 'november'],
              [12, 'december'],
            ])

            let month = val.substring(5, 7) - '0'

            const ans = [month]

            while (month < 12) {
              month = month + 1
              ans.push(month)
            }
            this.initAllMonth()
            for (let i = 0; i < ans.length; i++) {
              let index = map.get(ans[i])
              this.formData[index] = '√'
            }
          }
        } else if (this.formData.unit === '月') {
          if (!val) {
            this.$dialog.message.info('请先输入首次执行日期')
            return
          }
          if (!val) {
            this.initAllMonth()
            return
          }

          const map = new Map([
            [1, 'january'],
            [2, 'february'],
            [3, 'march'],
            [4, 'april'],
            [5, 'may'],
            [6, 'june'],
            [7, 'july'],
            [8, 'auguest'],
            [9, 'september'],
            [10, 'october'],
            [11, 'november'],
            [12, 'december'],
          ])

          let month = val.substring(5, 7) - '0'

          const ans = [month]

          while (month < 12) {
            month = month + (this.formData.maintenanceCycle - '0')
            ans.push(month)
          }
          this.initAllMonth()
          for (let i = 0; i < ans.length; i++) {
            let index = map.get(ans[i])
            this.formData[index] = '√'
          }
        }
        // }
      },
    },
    // 'formData.january': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(1)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(1)
    //       }
    //     }
    //   },
    // },
    // 'formData.february': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(2)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(2)
    //       }
    //     }
    //   },
    // },
    // 'formData.march': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(3)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(3)
    //       }
    //     }
    //   },
    // },
    // 'formData.april': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(4)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(4)
    //       }
    //     }
    //   },
    // },
    // 'formData.may': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(5)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(5)
    //       }
    //     }
    //   },
    // },
    // 'formData.june': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(6)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(6)
    //       }
    //     }
    //   },
    // },
    // 'formData.july': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(7)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(7)
    //       }
    //     }
    //   },
    // },
    // 'formData.auguest': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(8)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(8)
    //       }
    //     }
    //   },
    // },
    // 'formData.september': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(9)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(9)
    //       }
    //     }
    //   },
    // },
    // 'formData.october': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(10)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(10)
    //       }
    //     }
    //   },
    // },
    // 'formData.november': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(11)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(11)
    //       }
    //     }
    //   },
    // },
    // 'formData.december': {
    //   handler(val) {
    //     if (this.formData.maintenanceType === '定时') {
    //       if (this.equidistantArray.length < 2 && val) {
    //         this.equidistantArray.push(12)
    //       }
    //       if (val) {
    //         this.selectedMonth.push(12)
    //       }
    //     }
    //   },
    // },
    equidistantArray: {
      handler(val) {
        if (val.length < 2) return
        else {
          this.equalDifferenceNumber = Math.max(...val) - Math.min(...val)
          this.cycleSelection()
        }
      },
    },
    selectedMonth: {
      handler(val) {
        if (val.length === 0) {
          // if (this.formData.maintenanceType !== '定期') {
          //   this.formData.execDate = ''
          // }
        } else {
          let month = Math.min(...val)
          switch (month) {
            case 1:
              month = '01'
              break
            case 2:
              month = '02'
              break
            case 3:
              month = '03'
              break
            case 4:
              month = '04'
              break
            case 5:
              month = '05'
              break
            case 6:
              month = '06'
              break
            case 7:
              month = '07'
              break
            case 8:
              month = '08'
              break
            case 9:
              month = '09'
              break
            case 10:
              month = '10'
              break
            case 11:
              month = '11'
              break
            case 12:
              month = '12'
              break
            default:
              break
          }
          this.formData.execDate = `${this.initialData.year}-${month}-01`
        }
      },
    },
  },
  computed: {
    januaryDisabled() {
      return (
        this.equidistantArray.includes(1) || this.equidistantArray.length < 2
      )
    },
  },
  methods: {
    closeForm() {
      this.equidistantArray = []
      this.$emit('change', false)
    },
    async save() {
      this.formData.components = this.components
      console.log(this.components)
      console.log(this.formData)
      if (!this.$refs.form.validate()) {
        return
      }
      // if (this.components.length <= 0) {
      //   this.$dialog.message.error('请添加备件信息')
      //   return
      // }

      // this.formData.components = this.components
      if (
        !this.initialData.shipCode ||
        !this.initialData.department ||
        !this.initialData.year
      ) {
        this.$dialog.message.error('请优先填写年度基本信息')
        return
      }
      if (
        this.list.find(
          (ele) => this.formData.projectCode === ele.projectCode,
        ) &&
        !this.isEdit
      ) {
        this.$dialog.message.error('该项目编号已存在')
        return
      }
      let newAdd = this.isEdit ? true : false
      let oldProject = this.initialData.projectCode
      const { errorRaw, data } = await this.postAsync(
        `/business/shipAffairs/planYear/getItemCycle`,
        {
          shipCode: this.initialData.shipCode,
          department: this.initialData.department,
          year: this.initialData.year,
          ...this.formData,
          maintenanceCycle: this.formData.maintenanceCycle + this.formData.unit,
        },
      )
      if (errorRaw) {
        return
      }
      if (newAdd) {
        data.oldProject = oldProject
      }
      data.newAdd = newAdd
      this.$emit('change', false)
      this.$emit('success', data)
    },
    maintenanceJan(val) {
      val
        ? this.selectedMonth.unshift(1)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 1))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceFeb(val) {
      val
        ? this.selectedMonth.push(2)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 2))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceMar(val) {
      val
        ? this.selectedMonth.push(3)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 3))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceApr(val) {
      val
        ? this.selectedMonth.push(4)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 4))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceMay(val) {
      val
        ? this.selectedMonth.push(5)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 5))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceJun(val) {
      val
        ? this.selectedMonth.push(6)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 6))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceJul(val) {
      val
        ? this.selectedMonth.push(7)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 7))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceAug(val) {
      val
        ? this.selectedMonth.push(8)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 8))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceSep(val) {
      val
        ? this.selectedMonth.push(9)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 9))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceOct(val) {
      val
        ? this.selectedMonth.push(10)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 10))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceNov(val) {
      val
        ? this.selectedMonth.push(11)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 11))
      if (!val) {
        this.initAllMonth()
      }
    },
    maintenanceDec(val) {
      val
        ? this.selectedMonth.push(12)
        : (this.selectedMonth = this.selectedMonth.filter((ele) => ele !== 12))
      if (!val) {
        this.initAllMonth()
      }
    },
    arrayMaintain(val) {
      switch (this.equidistantArray.length) {
        case 0:
          this.equidistantArray.push(val)
          break
        case 1:
          this.equidistantArray[0] > val
            ? this.equidistantArray.unshift(val)
            : this.equidistantArray.push(val)
          break
        case 2:
          this.equalDifferenceNumber =
            this.equidistantArray[1] - this.equidistantArray[0]
          break
        default:
          this.equalDifferenceNumber = 0
      }
    },
    cycleSelection() {
      let left = Math.min(...this.equidistantArray),
        right = Math.max(...this.equidistantArray)
      while (left > 0 || right < 13) {
        left = left - this.equalDifferenceNumber
        right = right + this.equalDifferenceNumber
        switch (left) {
          case 1:
            this.formData.january = '√'
            break
          case 2:
            this.formData.february = '√'
            break
          case 3:
            this.formData.march = '√'
            break
          case 4:
            this.formData.april = '√'
            break
          case 5:
            this.formData.may = '√'
            break
          case 6:
            this.formData.june = '√'
            break
          case 7:
            this.formData.july = '√'
            break
          case 8:
            this.formData.auguest = '√'
            break
          case 9:
            this.formData.september = '√'
            break
          case 10:
            this.formData.october = '√'
            break
          case 11:
            this.formData.november = '√'
            break
          case 12:
            this.formData.december = '√'
            break
          default:
            break
        }
        switch (right) {
          case 1:
            this.formData.january = '√'
            break
          case 2:
            this.formData.february = '√'
            break
          case 3:
            this.formData.march = '√'
            break
          case 4:
            this.formData.april = '√'
            break
          case 5:
            this.formData.may = '√'
            break
          case 6:
            this.formData.june = '√'
            break
          case 7:
            this.formData.july = '√'
            break
          case 8:
            this.formData.auguest = '√'
            break
          case 9:
            this.formData.september = '√'
            break
          case 10:
            this.formData.october = '√'
            break
          case 11:
            this.formData.november = '√'
            break
          case 12:
            this.formData.december = '√'
            break
          default:
            break
        }
      }
    },
    initAllMonth() {
      this.formData.january = ''
      this.formData.february = ''
      this.formData.march = ''
      this.formData.april = ''
      this.formData.may = ''
      this.formData.june = ''
      this.formData.july = ''
      this.formData.auguest = ''
      this.formData.september = ''
      this.formData.october = ''
      this.formData.november = ''
      this.formData.december = ''
      this.equidistantArray = []
      this.selectedMonth = []
    },
    async getShipStation() {
      let that = this
      let data = await cacheGetDefault('ship-station', async () => {
        const { data, errorRaw } = await that.getAsync(
          '/business/crew/infra/positionList',
          {},
          false,
        )
        if (errorRaw) {
          that.$dialog.message.error('船上岗位列表获取失败，请重试')
          return null
        }
        if (data.length === 0) {
          that.$dialog.message.error('船上岗位为空，部分功能受损')
        }
        return data
      })
      this.stationItems = data.map((item) => {
        return {
          text: item.positionName,
          value: item.positionName,
        }
      })
    },
    async getClassByType() {
      const { data, errorRaw } = await this.getAsync(
        '/system/dict-data/getClassByDictType',
        { dictType: 'meter_engine' },
      )
      if (errorRaw) {
        this.$dialog.message.error(`字典获取失败，部分功能受损`)
        return null
      }
      if (data.length === 0) {
        this.$dialog.message.error(`数据字典为空，部分功能受损`)
      }
      this.equipmentType = data
    },
    async getTotalTime() {
      if (!this.formData.equipment) {
        return
      }
      if (!this.formData.equipType) {
        return
      }
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/planRuntime/getTotalTimeByEquipType`,
        {
          shipCode: this.formData.shipCode,
          equipmentName: this.formData.equipment,
          equipType: this.formData.equipType,
        },
      )
      console.log(data)
      if (errorRaw) {
        return
      }
      if (data != 0) {
        this.formData.totalTime = data
        this.hasTotalTime = true
      }
      if (data === 0) {
        this.formData.totalTime = 0
        this.hasTotalTime = false
      }
    },
    addEquments() {
      this.searchObj = {
        shipCode: this.initialData.shipCode,
      }
      this.dialog2 = true
      // console.log(this.searchObj)
    },
    async delEquments() {
      // this.delList.push({ ...this.select, operationType: 3 })
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.components = this.components.filter(
        (i) => i.componentId !== this.select.componentId,
      )
      this.select = false
    },
  },
  async mounted() {
    this.getClassByType()
    this.equipmentList = await this.getDictByType(`meter_engine`)
    this.getShipStation()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
