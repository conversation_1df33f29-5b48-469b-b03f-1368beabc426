<template>
  <div>
    <input
      ref="uploader"
      class="d-none"
      type="file"
      accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
      @change="onFileChanged"
    />
    <v-btn
      :loading="isSelecting"
      outlined
      tile
      color="primary"
      class="mx-1"
      @click="onButtonClick"
    >
      <v-icon left>mdi-microsoft-excel</v-icon>
      导入excel
    </v-btn>
  </div>
</template>
<script>
export default {
  name: 'v-import-btn',
  props: {
    importUrl: {
      type: String,
      required: true,
    },
    otherParams: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      selectedFile: null,
      isSelecting: false,
    }
  },

  computed: {
    buttonText() {
      return this.selectedFile ? this.selectedFile.name : this.defaultButtonText
    },
  },

  methods: {
    onButtonClick() {
      window.addEventListener('focus', () => {}, {})
      this.$refs.uploader.click()
    },
    async onFileChanged(e) {
      this.isSelecting = true
      this.selectedFile = e.target.files[0]
      let formData = new FormData()
      formData.append('file', this.selectedFile, false)
      if (this.otherParams) {
        Object.keys(this.otherParams).forEach((key) => {
          formData.append(key, this.otherParams[key])
        })
      }
      const { errorRaw } = await this.postAsync(this.importUrl, formData)
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
      } else {
        this.$dialog.message.success('导入成功')
        this.$emit('importSuccess')
      }
      this.isSelecting = false
      this.selectedFile = null
    },
  },

  mounted() {},
}
</script>

<style></style>
