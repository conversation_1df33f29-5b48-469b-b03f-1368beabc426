<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        完工
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row v-if="formData.maintenanceType === '定时'">
              <v-col v-if="formDataItem.completeStatus !== `HAND_DELAY`">
                <v-radio-group
                  row
                  label="完工类型"
                  v-model="formDataItem.completeType"
                  class="pb-0"
                  :readonly="canEdit"
                  :rules="[rules.required]"
                  required
                >
                  <v-radio
                    label="维护保养"
                    color="primary"
                    value="维护保养"
                  ></v-radio>
                  <v-radio label="换新" color="primary" value="换新"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col v-else>
                <v-radio-group
                  row
                  label="完工类型"
                  v-model="formDataItem.completeType"
                  class="pb-0"
                  :readonly="canEdit"
                >
                  <v-radio
                    label="维护保养"
                    color="primary"
                    value="维护保养"
                  ></v-radio>
                  <v-radio label="换新" color="primary" value="换新"></v-radio>
                </v-radio-group>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-text-field
                  label="保养内容"
                  v-model="formData.maintenanceContent"
                  readonly
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <vs-date-picker
                  v-if="formDataItem.completeStatus !== `HAND_DELAY`"
                  label="完工时间"
                  v-model="formDataItem.completeDate"
                  :readonly="canEdit"
                  :max-date="`${initialData.year}-${initialData.month}-${maxTime}`"
                  :min-date="`${initialData.year}-${initialData.month}-01`"
                  :rules="[rules.required]"
                  required
                ></vs-date-picker>
                <vs-date-picker
                  v-else
                  label="完工时间"
                  v-model="formDataItem.completeDate"
                  :readonly="canEdit"
                  :max-date="`${initialData.year}-${initialData.month}-${maxTime}`"
                  :min-date="`${initialData.year}-${initialData.month}-01`"
                ></vs-date-picker>
              </v-col>
              <v-col>
                <vs-date-picker
                  label="计划日期"
                  v-model="formDataItem.planDate"
                  readonly
                ></vs-date-picker>
              </v-col>
              <v-col v-if="formData.maintenanceType === '定时'">
                <v-text-field
                  v-if="formDataItem.completeStatus !== `HAND_DELAY`"
                  :label="label"
                  v-model="formDataItem.curMaintenanceTime"
                  type="number"
                  :readonly="canEdit"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
                <v-text-field
                  v-else
                  :label="label"
                  v-model="formDataItem.curMaintenanceTime"
                  type="number"
                  :readonly="canEdit"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="4">
                <v-radio-group
                  row
                  label="完工状态"
                  v-model="formDataItem.completeStatus"
                  class="pb-0"
                  :rules="[rules.required]"
                  required
                >
                  <v-radio
                    v-if="formData.advanceFlag"
                    label="提前完工"
                    color="primary"
                    value="ADVANCE_COMPLETE"
                  ></v-radio>
                  <v-radio
                    label="完工"
                    color="primary"
                    value="NORMAL_COMPLETE"
                    v-else
                  ></v-radio>

                  <v-radio
                    label="延期"
                    color="primary"
                    value="HAND_DELAY"
                  ></v-radio>
                </v-radio-group>
              </v-col>
              <v-col cols="12" md="4">
                <vs-date-picker
                  v-if="formDataItem.completeStatus == `HAND_DELAY`"
                  label="下次执行时间"
                  v-model="formDataItem.nextTime"
                  :max-date="nextMax"
                  :min-date="nextMin"
                  :rules="[rules.required]"
                  required
                ></vs-date-picker>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-textarea
                  outlined
                  dense
                  label="检修情况"
                  v-model="formDataItem.completeContent"
                  :rules="[rules.required]"
                  required
                ></v-textarea>
              </v-col>
              <!-- <v-col>
                <v-textarea
                  v-if="advanceORdelay"
                  outlined
                  dense
                  label="提前完工原因"
                  :disabled="!formData.advanceFlag"
                  v-model="formDataItem.completeAdvance"
                ></v-textarea>
                <v-textarea
                  v-else
                  outlined
                  dense
                  label="延后原因"
                  :readonly="formDataItem.completeStatus !== `HAND_DELAY`"
                  v-model="formDataItem.completeAdvance"
                ></v-textarea>
              </v-col> -->
            </v-row>
            <v-row class="d-flex justify-space-around mb-6">
              <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('spare')"
                  :disabled="
                    !!spareOutId || formDataItem.completeStatus === `HAND_DELAY`
                  "
                >
                  备件消耗
                </v-btn>
              </v-col>
              <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('material')"
                  :disabled="
                    !!materialOutId ||
                    formDataItem.completeStatus === `HAND_DELAY`
                  "
                >
                  物料消耗
                </v-btn>
              </v-col>
              <v-col cols="3">
                <v-btn
                  color="primary"
                  @click="setOutParam('soil')"
                  :disabled="
                    !!soilOutId || formDataItem.completeStatus === `HAND_DELAY`
                  "
                >
                  滑油消耗
                </v-btn>
              </v-col>
            </v-row>
            <v-attach-list
              :ship-code="shipCode"
              :attachments="attachments"
              @change="changeAttachment"
            ></v-attach-list>

            <v-col cols="12">
              <v-btn
                outlined
                tile
                color="success"
                class="mx-1"
                @click="save"
                block
              >
                <v-icon left>mdi-plus-circle</v-icon>
                完成
              </v-btn>
            </v-col>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-dialog-audit',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    isUpdateMode: {
      type: Boolean,
      default: false,
    },
    shipCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      attachments: [],
      formDataItem: {},
      dialog: false,
      formData: {},
      canEdit: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      maxTime: 30,
      nextMax: '',
      nextMin: '',
    }
  },
  watch: {
    open: {
      handler(val) {
        this.dialog = val
      },
      immediate: true,
    },
    dialog: {
      handler(val) {
        this.$emit('change', val)
      },
    },
    initialData: {
      handler() {
        this.formData = this.initialData
        if (this.isUpdateMode && this.initialData.completeStatus === '完工') {
          this.formDataItem = JSON.parse(JSON.stringify(this.initialData))
          // if (this.formDataItem.completeStatus === '完工') {
          //   this.formDataItem.completeStatus = 'NORMAL_COMPLETE'
          // }
          if (this.initialData.attachmentRecords) {
            this.attachments = this.initialData.attachmentRecords || []
          }
        } else {
          this.formDataItem.planDate = this.formData.planDate
          this.formDataItem.department = this.formData.department
          this.formDataItem.year = this.formData.year
          this.formDataItem.month = this.formData.month
          this.formDataItem.shipCode = this.formData.shipCode
        }
        this.maxTime = new Date(
          this.initialData.year,
          this.initialData.month,
          0,
        )
        this.nextMin = new Date(this.formData.planDate || new Date())
        this.nextMax = new Date(this.nextMin)
        this.nextMax.setDate(this.nextMin.getDate() + 31)
      },
      deep: true,
      immediate: true,
    },
    isUpdateMode: {
      immediate: true,
      handler(val) {
        if (
          val &&
          this.initialData &&
          Object.keys(this.initialData).length > 0
        ) {
          this.$nextTick(() => {
            this.formData = this.initialData
            this.formDataItem = JSON.parse(JSON.stringify(this.initialData))
            // if (this.formDataItem.completeStatus === '完工') {
            //   this.formDataItem.completeStatus = 'NORMAL_COMPLETE'
            // }
          })
        }
      },
    },
    'formDataItem.completeStatus': {
      handler(newValue) {
        if (newValue === 'HAND_DELAY') {
          this.canEdit = true
          this.formDataItem.completeType = ''
          this.formDataItem.completeDate = ''
          this.formDataItem.curMaintenanceTime = null
          this.formDataItem.completeContent = ''
          this.formDataItem.attachmentIds = []

          this.FirstData
        } else {
          this.canEdit = false
        }
      },
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
    label() {
      return this.formDataItem.completeType === '维护保养'
        ? '维护时运行小时'
        : '换新时运行小时'
    },
    advanceORdelay() {
      if (
        this.formData.advanceFlag &&
        this.formDataItem.completeStatus !== 'HAND_DELAY'
      )
        return true
      else return false
    },
    spareOutId() {
      return (
        this.formData.componentId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.id &&
            b.itemType === 'spare-out-detail' &&
            b.businessType === 'monthplan-detail',
        )?.outId
      )
    },
    materialOutId() {
      return (
        this.formData.materialId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.id &&
            b.itemType === 'materials-out-detail' &&
            b.businessType === 'monthplan-detail',
        )?.outId
      )
    },
    soilOutId() {
      return (
        this.formData.greaseId ||
        this.$store.state.outParams.outParams.find(
          (b) =>
            b.businessItemId === this.formData.id &&
            b.itemType === 'soil-out-detail' &&
            b.businessType === 'monthplan-detail',
        )?.outId
      )
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formDataItem.attachmentIds = attachmentIds
    },
    closeForm() {
      this.dialog = false
      this.$emit('change', false)
      this.$emit('LoadAgain', false)
      this.formDataItem = {}
      this.formData = {}
      this.attachments = []
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const { errorRaw } = await this.postAsync(
        `/business/shipAffairs/monthMonth/completeMonthPlan`,
        {
          id: this.formData.id,
          ...this.formDataItem,
          componentId: this.spareOutId,
          materialId: this.materialOutId,
          greaseId: this.soilOutId,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success(`保存成功`)
        this.formDataItem = {}
        this.formData = {}
        this.dialog = false
        this.$emit('change', false)
        this.$emit('LoadAgain', false)
      } else {
        this.$dialog.message.error(`保存失败，请重试`)
      }
      this.attachments = []
      this.formDataItem.attachmentIds = ''
    },
    setOutParam(type) {
      const types = {
        spare: 'spare-out-detail',
        material: 'materials-out-detail',
        soil: 'soil-out-detail',
      }
      const businessItemId = this.formData.id
      const businessId = this.$route.params.id
      this.$store.commit('emitOut', {
        businessType: 'monthplan-detail',
        businessItemId,
        businessId,
        itemType: types[type],
      })
      this.$router.push({
        name: types[type],
        params: { id: 'new' },
      })
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
