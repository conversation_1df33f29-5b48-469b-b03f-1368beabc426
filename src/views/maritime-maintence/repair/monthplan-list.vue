<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          {{ isEdit ? '修改 ' : '新增' }}---月度计划表
          <v-spacer></v-spacer>
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            small
            @click="save"
            v-permission="['月度计划管理:修改']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            {{ isEdit ? '修改' : '新增' }}
          </v-btn>
          <v-btn small outlined tile class="mx-1" @click="closeForm">
            <v-icon>mdi-close</v-icon>
            关闭
          </v-btn>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0">
              <v-row>
                <v-col cols="12" md="2">
                  <v-ship-select v-model="ship.shipCode"></v-ship-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    v-model="ship.department"
                    label="所属部门"
                    :items="items"
                    :rules="[rules.required]"
                    outlined
                    dense
                    required
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-year-month-picker
                    outlined
                    v-model="ship.date"
                    :disabled="!ship.canEdits"
                  ></v-year-month-picker>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="shipParams"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-autocomplete
            dense
            label="部门"
            outlined
            v-model="shipParams.department"
            :items="items"
            clearable
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-year-month-picker
            outlined
            v-model="shipParams.date"
          ></v-year-month-picker>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-autocomplete
            dense
            label="审核状态"
            outlined
            v-model="shipParams.status"
            :items="statusItems"
            clearable
          ></v-autocomplete>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="#0277BD"
          class="mx-1"
          :disabled="!selected"
          :href="`/api/business/shipAffairs/monthMonth/exportPlanMoth/${selected.id}`"
          target="_blank"
          :loading="loading"
          v-permission="['月度计划管理:导出word文档']"
        >
          <v-icon left>mdi-microsoft-word</v-icon>
          导出word文档
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color=""
          class="mx-1"
          :disabled="!selected || selected.status !== `执行完毕`"
          @click="complete"
          v-permission="['月度计划管理:月度计划确认']"
        >
          <v-icon>mdi-file-arrow-left-right</v-icon>
          月度计划确认
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="newCard"
          v-permission="['月度计划管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          outlined
          tile
          color="error"
          class="mx-1"
          :disabled="!selected"
          @click="delItem"
          v-permission="['月度计划管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'monthplan-list',
  created() {
    this.tableName = '月度计划表'
    this.reqUrl = '/business/shipAffairs/monthMonth/page'
    this.importUrl = `/business/shipAffairs/planYear/excelImport`
    this.headers = [
      { text: '月度计划名', value: 'planName' },
      { text: '船舶名', value: 'shipName' },
      { text: '月份', value: 'month' },
      { text: '生成人', value: 'principal' },
      { text: '生成人岗位', value: 'principalDept' },
      { text: '状态', value: 'status' },
    ]
    this.items = [
      { text: '甲板部', value: '甲板部' },
      { text: '轮机部', value: '轮机部' },
    ]
    this.statusItems = [
      { text: '执行中', value: 'IN_EXEC' },
      { text: '执行完成', value: 'FINISH_EXEC' },
      { text: '未提交', value: 'NO_SUBMIT' },
    ]
    this.pushParams = {
      name: 'monthplan-detail',
    }
  },
  data() {
    return {
      useName: false,
      selected: false,
      isEdit: false,
      formShow: false,
      loading: false,
      menu: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        year: (v) => /^\d{4}$/.test(v) || '请输入正确的年格式如2022',
      },
      ship: {
        date: new Date().toISOString().substring(0, 7),
        canEdits: false,
      },
      shipItems: [],
      shipParams: {
        date: new Date().toISOString().substring(0, 7),
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
      },
    }
  },
  watch: {
    shipParams: {
      handler(val) {
        if (!val.date) {
          this.shipParams.year = null
          this.shipParams.month = null
          return
        }
        this.shipParams.year = parseInt(val.date.slice(0, 4))
        this.shipParams.month = parseInt(val.date.slice(5, 7))
        // if(!val) {
        //   delete this.shipParams.year
        // }
        // this.shipParams = {
        //   ...this.shipParams,
        //   year: parseInt(this.shipParams.date.slice(0, 4)),
        //   month: parseInt(this.shipParams.date.slice(5, 7)),
        // }
      },
      immediate: true,
      deep: true,
    },
    'ship.shipCode'(val) {
      if (val) {
        console.log(val)
        if (this.ship.department) {
          this.confirmYearAndMonth()
        }
      }
    },
    'ship.department'(val) {
      if (val) {
        if (this.ship.shipCode) {
          console.log(val)
          this.confirmYearAndMonth()
        }
      }
    },
  },
  methods: {
    newCard() {
      this.formShow = !this.formShow
      this.$refs.table.disabled = true
    },
    closeForm() {
      this.$refs.form.reset()
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
      this.ship = {
        shipName: '',
        month: '',
        year: '',
        department: '',
      }
    },
    async confirmYearAndMonth() {
      const { data } = await this.getAsync(
        `/business/shipAffairs/monthMonth/confirmYearAndMonth`,
        {
          shipCode: this.ship.shipCode,
          department: this.ship.department,
        },
      )
      if (data) {
        if (data.year == 1970) {
          this.ship.canEdits = true
          this.ship.date = ''
        } else {
          const time = new Date()
          time.setFullYear(data.year)
          time.setMonth(data.month - 1)
          time.setDate(1)
          this.ship.date = time.toISOString().substring(0, 7)
          console.log(this.ship.date)
          this.ship.canEdits = false
        }
      }
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (this.selected.status !== '未提交') {
        this.$dialog.message.error('已提交的记录无法删除')
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/monthMonth/deletePlayMonth/${this.selected.id}`,
        {},
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
    async save() {
      if (!this.$refs.form.validate()) return
      if (!this.isEdit) {
        const { errorRaw } = await this.getAsync(
          `/business/shipAffairs/monthMonth/createMonthPlan`,
          {
            ...this.ship,
            year: parseInt(this.ship?.date.slice(0, 4)),
            month: parseInt(this.ship?.date.slice(5, 7)),
          },
        )
        if (errorRaw) {
          return
        }
        this.$dialog.message.success(`保存成功`)
        await this.$refs.table.loadTableData()
        this.$refs.form.reset()
        this.ship = {
          shipName: '',
          month: '',
          year: '',
          department: '',
        }
      }

      this.formShow = false
      this.$refs.table.disabled = false
      this.selected = false
      this.isEdit = false
    },
    async submit() {
      if (!(await this.$dialog.msgbox.confirm('是否提交选中记录？'))) return
      let Selectid = this.selected.id
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/monthMonth/createPlans/${Selectid}`,
        {},
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`提交失败，请重试`)
        return
      }
      this.$dialog.message.success(`提交成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
    async complete() {
      if (!(await this.$dialog.msgbox.confirm('是否确认当前记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/monthMonth/confirmPlans/${this.selected.id}`,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('匹配完成')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },
  mounted() {},
}
</script>

<style></style>
