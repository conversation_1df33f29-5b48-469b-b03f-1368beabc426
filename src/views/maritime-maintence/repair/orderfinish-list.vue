<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        月度计划待做
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="dialog = true"
          :disabled="!selected"
          v-permission="['月度计划完成情况:提交']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          提交
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6" md="2">
            <v-ship-select
              label="船舶名称"
              v-model="shipItemInfo.shipCode"
            ></v-ship-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-year-month-picker outlined v-model="date"></v-year-month-picker>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-select
              outlined
              dense
              label="部门"
              :items="['轮机部', '甲板部']"
              v-model="shipItemInfo.department"
            ></v-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-select
              outlined
              dense
              label="检修类型"
              :items="['定期', '定时']"
              v-model="shipItemInfo.maintenanceType"
            ></v-select>
          </v-col>
        </v-row>
        <v-table-list
          :headers="headers"
          :items="items"
          v-model="selected"
          use-page
        ></v-table-list>
      </v-card-text>
    </v-card>
    <VDialogAudit
      v-model="dialog"
      :initialData="initialData"
      @LoadAgain="LoadAgain"
    ></VDialogAudit>
  </v-container>
</template>
<script>
import VDialogAudit from './components/v-dialog-audit.vue'
export default {
  components: { VDialogAudit },
  name: 'orderfinish-list',
  data() {
    return {
      selected: false,
      initialData: {},
      dialog: false,
      items: [],
      shipItemInfo: {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        completeStatus: 'IN_COMPLETE',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        year: (v) => /^\d{4}$/.test(v) || '请输入正确的年格式如2022',
      },
      date: new Date().toISOString().substring(0, 7),
    }
  },
  created() {
    this.headers = [
      { text: '项目编号', value: 'projectCode' },
      { text: '船舶名', value: 'shipName' },
      { text: '部门', value: 'department' },
      { text: '年月时间', value: 'yearAndmonth' },
      { text: '维护保养部位', value: 'maintenanceSite' },
      { text: '维修保养内容', value: 'maintenanceContent' },
      { text: '计划时间', value: 'planDate' },
      { text: '检修周期', value: 'maintenanceCycle' },
      { text: '检修负责人', value: 'maintenancePrincipal' },
      { text: '检修类型', value: 'maintenanceType' },
    ]
  },

  watch: {
    shipItemInfo: {
      handler() {
        this.getInDoInfo()
      },
      deep: true,
    },
    selected: {
      handler(newValue) {
        if (newValue === false) {
          this.initialData = {}
        } else {
          this.initialData = newValue
        }
        console.log(newValue)
      },
    },
    date: {
      handler(val) {
        if (val) {
          this.shipItemInfo = {
            ...this.shipItemInfo,
            year: parseInt(this.date.slice(0, 4)),
            month: parseInt(this.date.slice(5, 7)),
          }
        } else {
          delete this.shipItemInfo.year
          delete this.shipItemInfo.month
          this.getInDoInfo()
        }
      },
    },
  },

  methods: {
    async LoadAgain(val) {
      this.dialog = val
      await this.getInDoInfo()
    },
    async getInDoInfo() {
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/planComplete/page`,
        { ...this.shipItemInfo },
        false,
      )
      if (!errorRaw) {
        if (!data) {
          this.items = []
          return
        } else {
          data?.records.forEach((ele) => {
            ele.yearAndmonth = ele.year + '-' + ele.month
          })
          this.items = data.records
        }
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
