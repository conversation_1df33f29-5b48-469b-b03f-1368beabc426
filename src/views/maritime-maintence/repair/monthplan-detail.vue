<!-- 当前页面已弃用 -->
<template>
  <v-container fluid>
    <v-detail-view
      :title="shipMonthDetail.planName"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
    >
      <template v-slot:titlebtns>
        <v-btn
          @click="closeAndTo(backRouteName, {}, {})"
          color="secondary"
          small
          tile
          class="mx-1"
        >
          <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
          返回列表
        </v-btn>
        <v-btn
          width="40"
          tile
          color="success"
          small
          class="mx-1"
          :loading="loading"
          @click="save"
          v-permission="['月度计划管理:编辑']"
        >
          保存
        </v-btn>
        <v-btn
          v-permission="['月度计划管理:编辑']"
          v-if="shipMonthDetail.status === `未提交`"
          width="120"
          tile
          @click="submt"
          color="success"
          small
          class="mx-1"
          :loading="loading"
        >
          生成该月度计划
        </v-btn>
        <v-btn
          v-premission="['月度计划管理:编辑']"
          v-if="shipMonthDetail.status === '执行完毕'"
          width="120"
          tile
          color="info"
          small
          class="mx-1"
          :loading="loading"
          @click="completeItem"
        >
          确认当前月度计划
        </v-btn>
      </template>
      <template v-slot:月度计划基础信息>
        <v-card-text>
          <v-expansion-panels multiple accordion v-model="panel" focusable>
            <v-expansion-panel>
              <v-expansion-panel-header style="color: #3399cc">
                月度计划基础信息
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-card-text>
                  <v-container fluid class="py-0 px-0">
                    <v-row>
                      <v-col cols="6" class="body-1">
                        当前月度计划(word)：
                        <a :href="shipMonthId" target="_blank">
                          {{ shipMonthDetail.principalDept }}
                        </a>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col cols="12" md="2">
                        <v-ship-select
                          label="船名"
                          v-model="shipMonthDetail.shipCode"
                          disabled
                        ></v-ship-select>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-ship-dept
                          outlined
                          dense
                          clearable
                          :items="['甲板部', '轮机部']"
                          label="部门"
                          v-model="shipMonthDetail.department"
                          disabled
                        ></v-ship-dept>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="年份"
                          dense
                          outlined
                          v-model="shipMonthDetail.year"
                          disabled
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="月份"
                          outlined
                          dense
                          v-model="shipMonthDetail.month"
                          disabled
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="2">
                        <v-text-field
                          label="执行状态"
                          outlined
                          dense
                          v-model="shipMonthDetail.status"
                          disabled
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12">
                        <v-textarea
                          outlined
                          dense
                          label="备注"
                          v-model="shipMonthDetail.remark"
                        ></v-textarea>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </template>
      <template v-slot:月度计划子项目信息按钮>
        <v-btn
          v-if="
            shipMonthDetail.status === '已确认' ||
            shipMonthDetail.status === '执行完毕'
          "
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="
            shipMonthDetail.status === '已确认' ||
            shipMonthDetail.status === '执行完毕'
          "
          @click="createInfo"
          v-permission="['月度计划子项目信息:新增']"
          :loading="loading"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          v-else
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="
            shipMonthDetail.status === '已确认' ||
            shipMonthDetail.status === '执行完毕'
          "
          @click="createInfo"
          v-permission="['月度计划子项目信息:新增']"
          :loading="loading"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          outlined
          dense
          :loading="loading"
          color="error"
          class="mx-1"
          :disabled="
            !selected ||
            shipMonthDetail.status === '已确认' ||
            shipMonthDetail.status === '执行中' ||
            shipMonthDetail.status === '执行完毕' ||
            (!selected.advanceFlag && selected.completeStatus == '待做')
          "
          @click="delInfo"
          v-permission="['月度计划子项目信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          v-if="shipMonthDetail.status === '执行中'"
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="!selected || selected.completeStatus !== `待做`"
          @click="completeEnd"
          v-permission="['月度计划子项目信息:完工']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          完工
        </v-btn>
      </template>
      <template v-slot:月度计划子项目信息>
        <v-row>
          <v-col cols="12">
            <v-alert type="info" color="green" text dense class="mb-0">
              计划日期还有7天到期时自动变黄，还有3天到期时自动变红；未完工的子项目双击即可弹出完工框，已完工的子项目双击即可弹出更新框。
            </v-alert>
          </v-col>
        </v-row>
        <v-table-searchable
          ref="table"
          :headers="headers"
          :req-url="reqUrl2"
          v-model="selected"
          :table-name="name"
          :search-remain="searchObj"
          @dbclick="handleDoubleClick"
          use-page
          @loadCompelte="handleTableLoaded"
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="2">
              <v-select
                label="检修负责人岗位"
                :items="stationItems"
                v-model="searchObj.principal"
                outlined
                dense
                clearable
              ></v-select>
            </v-col>
          </template>
          <template v-slot:item="{ item }">
            <tr :class="getRowClass(item)">
              <td v-if="$refs.table && $refs.table.showSelect">
                <v-checkbox
                  v-model="$refs.table.selectedItem"
                  :value="item"
                  hide-details
                ></v-checkbox>
              </td>
              <td v-for="header in headers" :key="header.value">
                {{ item[header.value] }}
              </td>
            </tr>
          </template>
          <template v-slot:[`item.maintenanceCycle`]="{ item }">
            {{ item.maintenanceCycle }}
            <span v-if="item.maintenanceType == '定时'">
              / {{ item.maintenanceCycleTime }}H
            </span>
          </template>
          <!-- 阿打算多
          { text: '备件', value: 'componentId' },
      { text: '滑油', value: 'greaseId' },
      { text: '物料', value: 'materialId' }, -->
          <template v-slot:[`item.componentId`]="{ item }">
            <router-link
              v-if="item.componentId"
              :to="{
                name: 'spare-out-detail',
                params: { id: item.componentId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
          <template v-slot:[`item.greaseId`]="{ item }">
            <router-link
              v-if="item.greaseId"
              :to="{
                name: 'soil-out-detail',
                params: { id: item.greaseId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
          <template v-slot:[`item.materialId`]="{ item }">
            <router-link
              v-if="item.materialId"
              :to="{
                name: 'materials-out-detail',
                params: { id: item.materialId },
              }"
            >
              查看
            </router-link>
            <div v-else>-</div>
          </template>
        </v-table-searchable>
      </template>
    </v-detail-view>
    <VDialogMonth
      v-model="dialog"
      :params="this.$route.params.id"
      @getMonthDetail="getMonthDetail"
    ></VDialogMonth>
    <VDialogAudit
      :shipCode="shipMonthDetail.shipCode"
      v-model="dialogTwo"
      :initialData="initialData"
      @LoadAgain="LoadAgain"
    ></VDialogAudit>
    <VDialogAudit
      :shipCode="shipMonthDetail.shipCode"
      v-model="updateDialog"
      :initialData="initialData"
      :isUpdateMode="true"
      @LoadAgain="LoadAgain"
    ></VDialogAudit>
  </v-container>
</template>
<script>
import VDialogMonth from './components/v-dialog-month.vue'
import routerControl from '@/mixin/routerControl'
import VDialogAudit from './components/v-dialog-audit.vue'
import { cacheGetDefault } from '@/util/cache'
export default {
  components: { VDialogMonth, VDialogAudit },
  mixins: [routerControl],
  name: 'monthplan-detail',
  created() {
    this.shipMonthId = `/api/business/shipAffairs/monthMonth/exportPlanMoth/${this.$route.params.id}`
    this.backRouteName = 'monthplan-list'
    this.subtitles = ['月度计划基础信息', '月度计划子项目信息']
    this.headers = [
      { text: '项目编号', value: 'projectCode' },
      { text: '设备', value: 'equipment' },
      { text: '设备类别', value: 'equipType' },
      { text: '维护保养部位', value: 'maintenanceSite' },
      { text: '维修保养内容', value: 'maintenanceContent' },
      { text: '计划日期', value: 'planDate' },
      { text: '检修周期', value: 'maintenanceCycle' },
      { text: '检修类型', value: 'maintenanceType' },
      { text: '检修负责人岗位', value: 'maintenancePrincipal' },
      { text: '完工人', value: 'completeUserName' },
      { text: '备注', value: 'remark' },
      { text: '状态', value: 'completeStatus' },
      { text: '计划类型', value: 'planType' },
      { text: '完工类型', value: 'completeType' },
      { text: '完工时间', value: 'completeDate' },
      { text: '检修情况', value: 'completeContent' },
      { text: '备件', value: 'componentId' },
      { text: '滑油', value: 'greaseId' },
      { text: '物料', value: 'materialId' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.workheaders = [
      { text: '项目编号', value: 'projectCode' },
      { text: '船舶名', value: 'shipName' },
      { text: '部门', value: 'department' },
      { text: '维护保养部位', value: 'maintenanceSite' },
      { text: '维修保养内容', value: 'maintenanceContent' },
      { text: '计划时间', value: 'planDate' },
      { text: '检修周期', value: 'maintenanceCycle' },
      { text: '检修负责人', value: 'maintenancePrincipal' },
      { text: '检修类型', value: 'maintenanceType' },
      { text: '状态', value: 'completeStatus' },
      { text: '完工时间', value: 'completeDate' },
      { text: '备件', value: 'componentId' },
      { text: '滑油', value: 'greaseId' },
      { text: '物料', value: 'materialId' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.completeStatus = [
      { text: '提前完工', value: 'ADVANCE_COMPLETE' },
      { text: '完工', value: 'NORMAL_COMPLETE' },
      { text: '手动延期', value: 'HAND_DELAY' },
      { text: '强制延期', value: 'FORCE_DELAY' },
      { text: '待做', value: 'IN_COMPLETE' },
    ]
    this.reqUrl = '/business/shipAffairs/planComplete/page'
    this.reqUrl2 = `/business/shipAffairs/monthMonth/pageItem?id=${this.$route.params.id}`
  },
  data() {
    return {
      loading: false,
      dialog: false,
      dialogAudit: false,
      dialogComplete: false,
      dialogTwo: false,
      updateDialog: false,
      shipMonthDetail: {},
      shipMonthId: '',
      selected: null,
      panel: [0],
      complete: {
        one: false,
        two: false,
      },
      name: '',
      searchObj: {},
      //   searchObj: {   principal     if (this.isShip) {
      //   this.searchObj.principal = this.$local.data.get('userInfo')
      //     .userPosition
      //     ? this.$local.data.get('userInfo').userPosition
      //     : ''
      // }
      initialData: {},
      stationItems: [],
      headers: [
        { text: '序号', value: 'index' },
        { text: '项目名称', value: 'itemName' },
        { text: '完成状态', value: 'completeStatus' },
      ],
    }
  },
  watch: {
    complete: {
      handler(val) {
        if (val.one && val.two) this.loadComplete()
      },
      deep: true,
    },
    /*'searchObj.principal': {
      // eslint-disable-next-line
      handler(newVal) {
        if (this.$refs.table) {
          this.$refs.table.loadTableData()
        }
      },
      immediate: true,
    },*/
    selected: {
      handler(newValue) {
        if (newValue === false) {
          this.initialData = {}
        } else {
          this.initialData = newValue
        }
      },
    },
    'complete.two': function (val) {
      if (val) {
        this.$nextTick(() => {
          this.updateRowColors()
        })
      }
    },
    '$refs.table.options': {
      handler() {
        setTimeout(() => {
          this.updateRowColors()
        }, 100)
      },
      deep: true,
    },
  },
  methods: {
    async getShipStation() {
      let that = this
      let data = await cacheGetDefault('ship-station', async () => {
        const { data, errorRaw } = await that.getAsync(
          '/business/crew/infra/positionList',
          {},
          false,
        )
        if (errorRaw) {
          that.$dialog.message.error('船上岗位列表获取失败，请重试')
          return null
        }
        if (data.length === 0) {
          that.$dialog.message.error('船上岗位为空，部分功能受损')
        }
        return data
      })
      this.stationItems = data.map((item) => {
        return {
          text: item.positionName,
          value: item.positionName,
        }
      })
    },
    createInfo() {
      this.dialog = true
    },
    async LoadAgain(val) {
      this.dialogTwo = val
      let currentPage = 1
      let selectedId = null
      if (this.$refs.table) {
        currentPage = this.$refs.table.options
          ? this.$refs.table.options.page
          : 1
        selectedId = this.selected ? this.selected.id : null
        await this.$refs.table.loadTableData(false)
        this.$nextTick(() => {
          if (this.$refs.table && this.$refs.table.options) {
            this.$refs.table.options.page = currentPage
            if (selectedId && this.$refs.table.items) {
              const targetItem = this.$refs.table.items.find(
                (item) => item.id === selectedId,
              )
              if (targetItem) {
                this.selected = targetItem
              }
            }
          }
        })
      } else {
        this.$nextTick(async () => {
          if (this.$refs.table) {
            await this.$refs.table.loadTableData(false)
          }
        })
      }
    },
    handleDoubleClick(item) {
      if (this.shipMonthDetail.status !== '执行中') return
      if (item && item.completeStatus === '完工') {
        this.selected = item
        this.initialData = JSON.parse(JSON.stringify(item))
        this.$nextTick(() => {
          this.updateDialog = true
        })
        return
      }
      this.completeEnd()
    },
    completeEnd() {
      this.dialogTwo = true
    },
    async completeItem() {
      if (!(await this.$dialog.msgbox.confirm('是否确认当前月度计划？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/monthMonth/confirmPlans/${this.$route.params.id}`,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('确认成功')
      this.closeAndTo(this.backRouteName, {})
    },
    async submt() {
      this.loading = true
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/monthMonth/createPlans/${this.$route.params.id}`,
        {},
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      this.$dialog.message.success(`提交成功`)
      this.closeAndTo(this.backRouteName, {})
      this.loading = false
    },
    async save() {
      if (this.shipMonthDetail.status !== `未提交`) {
        this.closeAndTo(this.backRouteName, {})
        return
      }
      this.loading = true
      const { errorRaw } = await this.postAsync(
        `/business/shipAffairs/monthMonth/modifyPlanMonth`,
        this.shipMonthDetail,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('保存成功')
      this.closeAndTo(this.backRouteName, {})
      this.loading = false
    },
    async getMonthDetail() {
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/monthMonth/getDetailById/${this.$route.params.id}`,
        {},
        false,
      )
      if (!errorRaw) {
        this.shipMonthDetail = data
        this.items = data.planMonthItemOutputDTOS
        this.complete.two = true
      }

      this.$nextTick(async () => {
        if (this.$refs.table) {
          await this.$refs.table.loadTableData()
        }
      })
    },
    loadComplete() {
      this.searchRemain = {
        shipCode: this.shipMonthDetail.shipCode,
        year: this.shipMonthDetail.year,
        month: this.shipMonthDetail.month,
        department: this.shipMonthDetail.department,
        maintenancePrincipal: this.$local.data.get('userInfo').positionStation
          ? this.$local.data.get('userInfo').positionStation
          : '',
      }
    },
    async delInfo() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return

      this.loading = true
      const { errorRaw } = await this.postAsync(
        `/business/shipAffairs/monthMonth/deletePlayMonthItems`,
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.loading = false
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      this.selected = false
      this.loading = false
      await this.getMonthDetail()
    },
    async update() {
      this.loading = true
      const { errorRaw } = await this.postAsync(
        `/business/shipAffairs/monthMonth/modifyPlanMonthItem`,
        this.selected,
      )
      if (errorRaw) {
        this.loading = false
        return
      }
      await this.getMonthDetail()
      this.$dialog.message.success('修改成功')
      this.selected = false
      this.loading = false
    },
    getRowClass(item) {
      if (!item.planDate) return ''
      if (item.completeStatus == '完工') return ''
      const planDate = new Date(item.planDate)
      const today = new Date()

      const timeDiff = planDate.getTime() - today.getTime()
      const dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))

      if (dayDiff <= 3 && dayDiff >= 0) {
        return 'red-row'
      }
      if (dayDiff <= 7 && dayDiff > 3) {
        return 'yellow-row'
      }
      return ''
    },
    handleTableLoaded() {
      setTimeout(() => {
        this.updateRowColors()
      }, 200)
    },
    updateRowColors() {
      if (!this.$refs.table || !this.$refs.table.items) {
        return
      }

      const tableEl = this.$refs.table.$el.querySelector('.v-data-table')
      if (!tableEl) {
        return
      }

      const tableRows = tableEl.querySelectorAll('tbody tr')
      if (!tableRows || tableRows.length === 0) {
        return
      }

      this.$refs.table.items.forEach((item, index) => {
        if (!item.planDate) return
        if (item.completeStatus === '完工') return

        const planDate = new Date(item.planDate)
        const today = new Date()

        const timeDiff = planDate.getTime() - today.getTime()
        const dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))

        if (index < tableRows.length) {
          tableRows[index].classList.remove('red-row', 'yellow-row')

          if (dayDiff <= 3 && dayDiff >= 0) {
            tableRows[index].classList.add('red-row')
          } else if (dayDiff <= 7 && dayDiff > 3) {
            tableRows[index].classList.add('yellow-row')
          }
        }
      })
    },
  },
  computed: {
    isShip() {
      return this.$local.data.get('userInfo').isShipSyS
    },
  },

  updated() {
    this.updateRowColors()
  },

  async mounted() {
    // if (this.isShip) {
    //   const userInfo = this.$local.data.get('userInfo')
    //   this.searchObj.principal = userInfo.userPosition || ''
    // }
    await this.getMonthDetail()
    this.getShipStation()
    this.$nextTick(() => {
      if (this.$refs.table && this.$refs.table.$el) {
        // eslint-disable-next-line
        const observer = new MutationObserver((mutations) => {
          this.updateRowColors()
        })

        observer.observe(this.$refs.table.$el, {
          childList: true,
          subtree: true,
        })
      }
    })
  },
}
</script>

<style>
.red-row {
  background-color: rgba(255, 0, 0, 0.15) !important;
}

.red-row:hover {
  background-color: rgba(255, 0, 0, 0.25) !important;
}

.yellow-row {
  background-color: rgba(255, 204, 0, 0.15) !important;
}

.yellow-row:hover {
  background-color: rgba(255, 204, 0, 0.25) !important;
}
</style>
