<template>
  <v-form ref="form">
    <v-container fluid>
      <v-detail-view
        v-permission="['年度计划管理:编辑']"
        :title="shipInfo"
        :tooltip="shipInfo"
        :backRouteName="backRouteName"
        :subtitles="subtitles"
        :can-submit="
          !auditParams || !auditParams.taskId || auditParams.isReject
        "
      >
        <template v-slot:titlebtns>
          <v-btn
            @click="closeAndTo(backRouteName, {}, {})"
            color="secondary"
            small
            tile
            class="mx-1"
            :loading="loading"
          >
            <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
            返回列表
          </v-btn>
          <v-btn
            width="60"
            tile
            @click="save"
            color="success"
            small
            class="mx-1"
            :loading="loading"
            v-permission="['年度计划管理:编辑']"
          >
            保存
          </v-btn>
          <v-btn
            v-if="shipParInfo.status !== '3'"
            width="80"
            tile
            @click="submit"
            color="success"
            small
            class="mx-1"
            :loading="loading"
            v-permission="['年度计划管理:编辑']"
          >
            保存并提交
          </v-btn>
        </template>
        <template
          v-if="auditParams && auditParams.processInstanceId"
          v-slot:topcontent
        >
          <v-card-text class="mt-2 pb-0">
            <v-audit
              ref="audit"
              :auditParams="auditParams"
              :shipCode="shipParInfo.shipCode"
            ></v-audit>
          </v-card-text>
        </template>
        <template v-slot:年度计划基础信息>
          <v-card-text>
            <v-container fluid class="py-0 px-0">
              <v-row v-if="shipParInfo.status == '3'">
                <v-col cols="6" class="body-1">
                  当前年度计划(word)：
                  <a :href="ShipId" target="_blank">{{ shipInfo }}</a>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" md="2">
                  <v-ship-select
                    required
                    :rules="[rules.required]"
                    v-model="shipParInfo.shipCode"
                    :readonly="!isTrue"
                  ></v-ship-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    label="部门"
                    :rules="[rules.required]"
                    v-model="shipParInfo.department"
                    outlined
                    dense
                    :readonly="!isTrue"
                    :items="['轮机部', '甲板部']"
                  ></v-select>
                </v-col>
                <!-- <v-col cols="12" md="2">
                  <v-text-field
                    label="年份"
                    :rules="[rules.year]"
                    v-model="shipParInfo.year"
                    outlined
                    dense
                    :readonly="!isTrue"
                  ></v-text-field>
                </v-col> -->
                <v-col cols="12" md="2">
                  <v-autocomplete
                    label="年份"
                    outlined
                    dense
                    :rules="[rules.required]"
                    v-model="shipParInfo.year"
                    :readonly="!isTrue"
                    :items="yearList"
                    clearable
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="负责人"
                    v-model="shipParInfo.principal"
                    outlined
                    dense
                    :readonly="!isTrue"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="2">
                  <v-text-field
                    label="负责人岗位"
                    v-model="shipParInfo.principalDept"
                    outlined
                    dense
                    :readonly="!isTrue"
                  ></v-text-field>
                </v-col>
                <!-- <v-col cols="12" md="2">
                  <v-text-field
                    label="审核状态"
                    v-model="shipParInfo.auditStatus"
                    readonly
                    dense
                    outlined
                  ></v-text-field>
                </v-col> -->
                <v-col cols="12">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="shipParInfo.remark"
                    :readonly="!isTrue"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
          </v-card-text>
        </template>
        <template v-slot:年度计划子项目信息按钮>
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            :loading="loading"
            @click="createInfo"
            :disabled="!shipParInfo.shipCode || selected"
            v-permission="['年度计划子项目信息:新增']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            新增
          </v-btn>
          <v-btn
            outlined
            tile
            color="warning"
            class="mx-1"
            :disabled="!selected"
            @click="editInfo"
            :loading="loading"
            v-permission="['年度计划子项目信息:修改']"
          >
            <v-icon left>mdi-pencil</v-icon>
            修改
          </v-btn>
          <v-btn
            outlined
            :loading="loading"
            color="error"
            class="mx-1"
            :disabled="!selected || shipParInfo.status == '3'"
            @click="delInfo"
            v-permission="['年度计划子项目信息:删除']"
          >
            <v-icon left>mdi-delete-empty</v-icon>
            删除
          </v-btn>
        </template>
        <template v-slot:年度计划子项目信息>
          <v-table-list
            ref="table"
            :headers="headers"
            :items="items"
            item-key="projectCode"
            v-model="selected"
            usePage
          >
            <template v-slot:[`item.maintenanceCycle`]="{ item }">
              {{ item.maintenanceCycle }}
              <span v-if="item.maintenanceType == '定时'">
                / {{ item.maintenanceCycleTime }}H
              </span>
            </template>
          </v-table-list>
        </template>
      </v-detail-view>
      <v-dialog-info
        v-model="dialog"
        :initial-data="shipChildInfo"
        @success="success"
        :isEdit="isEdit"
        :list="list"
      ></v-dialog-info>
    </v-container>
  </v-form>
</template>
<script>
import vDialogInfo from './components/v-dialog-info.vue'
import routerControl from '@/mixin/routerControl'
export default {
  components: { vDialogInfo },
  name: 'yearplan-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'yearplan-list'
    this.subtitles = ['年度计划基础信息', '年度计划子项目信息']
    this.headers = [
      { text: '项目编号', value: 'projectCode' },
      { text: '设备', value: 'equipment' },
      { text: '设备类别', value: 'equipType' },
      { text: '维护保养部位', value: 'maintenanceSite' },
      { text: '维修保养内容', value: 'maintenanceContent' },
      { text: '下次执行日期', value: 'execDate' },
      { text: '检修周期', value: 'maintenanceCycle' },
      { text: '1月', value: 'january' },
      { text: '2月', value: 'february' },
      { text: '3月', value: 'march' },
      { text: '4月', value: 'april' },
      { text: '5月', value: 'may' },
      { text: '6月', value: 'june' },
      { text: '7月', value: 'july' },
      { text: '8月', value: 'auguest' },
      { text: '9月', value: 'september' },
      { text: '10月', value: 'october' },
      { text: '11月', value: 'november' },
      { text: '12月', value: 'december' },
      { text: '检修类型', value: 'maintenanceType' },
      { text: '检修负责人', value: 'maintenancePrincipal' },
      { text: '备注', value: 'remark' },
    ]
    this.ShipId = `/api/business/shipAffairs/planYear/wordExport/${this.$route.params.id}`

    this.idCard = this.$route.params.id
    this.rangeArray(2020, new Date().getFullYear() + 1)
  },
  data() {
    return {
      IdParam: this.$route.params.id,
      auditParams: false,
      dialog: false,
      selected: false,
      loading: false,
      items: [],
      shipInfo: '新增年度计划',
      shipParInfo: {
        principal: this.$local.data.get('userInfo').nickName,
        principalDept: this.$local.data.get('userInfo').userPosition,
        status: '1',
      },
      shipChildInfo: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        year: (v) => /^\d{4}$/.test(v) || '请输入正确的年格式如2022',
      },
      isEdit: false,
      list: [],
      yearList: Array.from({ length: 50 }, (_, i) => i + 1),
    }
  },
  computed: {
    isTrue() {
      return this.shipParInfo.status === '1' || this.shipParInfo.status === '4'
    },
    isEdit2() {
      return this.$route.params.id !== 'new'
    },
  },
  methods: {
    rangeArray(start, end) {
      let length = end - start + 1
      let step = start - 1
      this.yearList = Array.from({ length: length }, () => {
        step++
        return step
      })
    },
    async submit() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.loading = true
      if (this.isTrue) {
        const { errorRaw, data } = await this.postAsync(
          `/business/shipAffairs/planYear/modifyPlanYear`,
          { ...this.shipParInfo },
        )
        if (errorRaw) {
          console.log(111)
          this.loading = false
          return
        }
        this.IdParam = data
        if (!(await this.saveChildItems())) return
      }
      if (this.shipParInfo.status === '1') {
        const { errorRaw } = await this.getAsync(
          `/business/shipAffairs/planYear/process/start`,
          { id: this.IdParam },
        )
        if (errorRaw) {
          this.loading = false
          return
        }
      } else {
        await this.$refs.audit.submit()
      }
      this.$dialog.message.success('提交成功')
      this.loading = false
      this.closeAndTo(this.backRouteName, {})
    },
    async save() {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      if (this.items.length == 0) {
        this.$dialog.message.error('请添加子项目')
        return
      }
      this.loading = true
      if (!this.isTrue) {
        if (!(await this.saveChildItems())) return
        this.closeAndTo(this.backRouteName, {})
      } else {
        const { errorRaw, data } = await this.postAsync(
          `/business/shipAffairs/planYear/modifyPlanYear`,
          { ...this.shipParInfo },
        )
        if (errorRaw) {
          this.loading = false
          return false
        }
        this.IdParam = data
        if (!(await this.saveChildItems())) return
        this.loading = false
        this.$dialog.message.success('保存成功')
        this.closeAndTo(this.backRouteName, {})
      }
    },
    async save2() {
      if (!this.$refs.form.validate()) {
        this.$dialog.message.error('请确认信息是否填写完整')
        return
      }
      if (this.items.length == 0) {
        this.$dialog.message.error('请添加子项目')
        return
      }
      this.loading = true
      if (!this.isTrue) {
        if (!(await this.saveChildItems(true))) return
      } else {
        const { errorRaw, data } = await this.postAsync(
          `/business/shipAffairs/planYear/modifyPlanYear`,
          { ...this.shipParInfo },
        )
        if (errorRaw) {
          this.loading = false
          return false
        }
        this.IdParam = data
        this.$route.params.id = data
        if (!(await this.saveChildItems(true))) return
        this.$dialog.message.success('保存成功')
        this.loading = false
      }

      await this.getShipDetailItems()
    },
    async getShipDetailItems() {
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/planYear/getDetailById/${this.$route.params.id}`,
        {},
        false,
      )
      if (!errorRaw) {
        this.shipParInfo = data
        this.shipInfo = data.planName
        this.auditParams = data.auditParams
        this.items = data.planYearItemOutputDTOS
      }
      this.loading = false
    },
    async delInfo() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.loading = true
      if (this.selected.id) {
        const { errorRaw } = await this.postAsync(
          `/business/shipAffairs/planYear/deletePlayYearItems`,
          [this.selected.id],
        )
        if (errorRaw) {
          this.loading = false
          return
        }
        await this.getShipDetailItems()
        this.$dialog.message.success('删除成功')
      } else {
        this.items = this.items.filter((ele) => ele !== this.selected)
      }
      this.selected = false
      this.loading = false
    },
    async saveChildItems(flag) {
      // 新增和修改操作时
      if (flag) {
        let newItems = null
        if (this.selected) {
          newItems = this.items.filter((item) => item.id === this.selected.id)
          this.selected = false
        } else {
          //过滤出没有 ID 的子项目
          newItems = this.items.filter((item) => !item.id)
          newItems.forEach((item) => {
            item.shipPlanYearId = this.IdParam
          })
        }
        const response = await this.postAsync(
          `/business/shipAffairs/planYear/modifyPlanYearItem`,
          newItems,
        )
        const { errorRaw } = response
        if (!errorRaw) {
          this.loading = false
          return true
        }
        if (errorRaw.code !== 0 || errorRaw.data === false) {
          if (errorRaw.data === false) {
            this.$dialog.message.error(
              '请检查：新增子项目的下次执行日期是否在2025年内！！！',
            )
          }
          this.loading = false
          return false
        }
      }
      // 保存和保存并提交，直接使用已存在的子项目进行保存
      if (!flag) {
        this.items.forEach((ele) => {
          ele.shipPlanYearId = this.IdParam
        })
        const { errorRaw } = await this.postAsync(
          `/business/shipAffairs/planYear/modifyPlanYearItem`,
          this.items,
        )
        if (errorRaw) {
          this.loading = false
          return false
        }
      }
      this.loading = false
      return true
    },
    createInfo() {
      this.shipChildInfo = {
        shipCode: this.shipParInfo.shipCode,
        department: this.shipParInfo.department,
        year: this.shipParInfo.year,
        january: '',
        february: '',
        march: '',
        april: '',
        may: '',
        june: '',
        july: '',
        auguest: '',
        september: '',
        october: '',
        november: '',
        december: '',
        afterMaintenanceTime: 0,
        totalTime: 0,
        curMaintenanceTime: 0,
        afterRenewTime: 0,
        curRenewTime: 0,
        components: [],
      }
      this.list = this.items
      this.isEdit = false
      this.dialog = true
    },
    editInfo() {
      this.shipChildInfo = {
        shipPlanYearId: this.$route.params.id,
        shipCode: this.shipParInfo.shipCode,
        department: this.shipParInfo.department,
        year: this.shipParInfo.year,
        ...this.selected,
        maintenanceCycle: this.selected.maintenanceCycle.slice(
          0,
          this.selected.maintenanceCycle.length - 1,
        ),
        unit: this.selected.maintenanceCycle.slice(
          this.selected.maintenanceCycle.length - 1,
        ),
        components: this.selected.components,
      }
      this.list = this.items

      this.isEdit = true
      this.dialog = true
    },
    async success(data) {
      if (this.isEdit) {
        //console.log('编辑成功，更新数据:', data)
        this.items = this.items.map((ele) => {
          if (ele.projectCode === data.oldProject) {
            return data
          } else {
            return ele
          }
        })
      } else {
        //console.log('新增成功，插入新数据:', data)
        this.items.unshift(data)
      }
      // if (!(await this.saveChildItems())) return
      await this.save2()
    },
  },

  mounted() {
    this.getShipDetailItems()
  },
}
</script>

<style></style>
