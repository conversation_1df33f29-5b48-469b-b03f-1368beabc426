<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        维护保养历史记录
        <v-spacer></v-spacer>
      </v-card-title>
      <v-card-text>
        <v-row>
          <!-- <v-col cols="12" sm="6" md="1">
            <v-btn
              outlined
              tile
              color="primary"
              class="mx-1"
              @click.stop="getReadyInfo"
            >
              <v-icon left>mdi-magnify</v-icon>
              搜索
            </v-btn>
          </v-col> -->
          <v-col cols="12" sm="6" md="2">
            <v-ship-select
              label="船舶名称"
              v-model="shipItemInfo.shipCode"
              :rules="[rules.required]"
            ></v-ship-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-year-month-picker outlined v-model="date"></v-year-month-picker>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-select
              outlined
              dense
              label="部门"
              :items="['轮机部', '甲板部']"
              clearable
              v-model="shipItemInfo.department"
            ></v-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-select
              outlined
              dense
              label="检修类型"
              clearable
              :items="['定期', '定时']"
              v-model="shipItemInfo.maintenanceType"
            ></v-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-select
              outlined
              dense
              label="状态"
              :items="completeStatus"
              v-model="shipItemInfo.completeStatus"
              clearable
            ></v-select>
          </v-col>
          <v-col class="m1-auto" cols="12" md="2" sm="6">
            <v-text-field
              label="模糊查询"
              outlined
              dense
              append-icon="mdi-magnify"
              v-model="fuzzyParam"
            ></v-text-field>
          </v-col>
        </v-row>
        <v-table-list
          :show-select="false"
          :headers="headers"
          :items="items"
          v-model="selected"
          use-page
        ></v-table-list>
      </v-card-text>
    </v-card>
    <VDialogAudit v-model="dialog"></VDialogAudit>
  </v-container>
</template>
<script>
const debounce = (fn, delay = 300) => {
  let timer = null
  return function () {
    let context = this
    let args = arguments
    clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}
import VDialogAudit from './components/v-dialog-audit.vue'
export default {
  components: { VDialogAudit },
  name: 'oldrepair-list',
  data() {
    return {
      dialog: false,
      selected: false,
      items: [],
      shipItemInfo: {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        year: (v) => /^\d{4}$/.test(v) || '请输入正确的年格式如2022',
      },
      date: '',
      fuzzyParam: '',
    }
  },
  created() {
    this.headers = [
      { text: '项目编号', value: 'projectCode' },
      { text: '船舶名', value: 'shipName' },
      { text: '部门', value: 'department' },
      { text: '年月时间', value: 'yearAndmonth' },
      { text: '维护保养部位', value: 'maintenanceSite' },
      { text: '维修保养内容', value: 'maintenanceContent' },
      { text: '计划时间', value: 'planDate' },
      { text: '完工时间', value: 'completeDate' },
      { text: '检修周期', value: 'maintenanceCycle' },
      { text: '检修负责人', value: 'maintenancePrincipal' },
      { text: '检修类型', value: 'maintenanceType' },
      { text: '完工内容', value: 'completeContent' },
      { text: '完工类型', value: 'completeStatus' },
      { text: '备件', value: 'componentId' },
      { text: '滑油', value: 'greaseId' },
      { text: '物料', value: 'materialId' },
      { text: '备注', value: 'remark' },
      { text: '完工报告', value: 'attachmentRecords' },
    ]
    this.completeStatus = [
      { text: '提前完工', value: 'ADVANCE_COMPLETE' },
      { text: '完工', value: 'NORMAL_COMPLETE' },
      { text: '手动延期', value: 'HAND_DELAY' },
      { text: '强制延期', value: 'FORCE_DELAY' },
    ]
  },

  watch: {
    shipItemInfo: {
      handler() {
        this.getReadyInfo()
      },
      deep: true,
    },
    date: {
      handler(val) {
        if (val) {
          this.shipItemInfo = {
            ...this.shipItemInfo,
            year: parseInt(this.date.slice(0, 4)),
            month: parseInt(this.date.slice(5, 7)),
          }
        } else {
          delete this.shipItemInfo.year
          delete this.shipItemInfo.month
          this.getReadyInfo()
        }
      },
    },
    fuzzyParam: {
      handler() {
        this.debounceGetReadyInfo(this)
      },
    },
  },

  methods: {
    debounceGetReadyInfo: debounce(function (that) {
      that.getReadyInfo()
    }, 500),
    async getReadyInfo() {
      let params = { ...this.shipItemInfo }
      if (this.fuzzyParam) {
        params = {
          ...params,
          fuzzyParam: this.fuzzyParam,
        }
      }
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/planComplete/getHistory`,
        params,
        false,
      )
      if (!errorRaw) {
        data?.records.forEach((ele) => {
          ele.yearAndmonth = ele.year + '-' + ele.month
        })
        this.items = data?.records
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
