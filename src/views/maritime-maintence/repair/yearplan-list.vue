<template>
  <v-container fluid>
    <v-expand-transition>
      <v-card v-if="formShow" class="mb-2">
        <v-card-title>
          Excel---新增---年度计划表
          <v-spacer></v-spacer>
          <v-icon @click="closeForm">mdi-close</v-icon>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-form ref="form">
            <v-container class="py-0 px-0">
              <v-row>
                <v-col cols="12" md="2">
                  <v-ship-select
                    ref="select"
                    v-model="ship.shipCode"
                    :rules="[rules.required]"
                    required
                    :use-name="useName"
                  ></v-ship-select>
                </v-col>
                <!-- <v-col cols="12" md="2">
                  <v-text-field
                    label="年份"
                    outlined
                    dense
                    :rules="[rules.year]"
                    required
                    v-model="ship.year"
                  ></v-text-field>
                </v-col> -->
                <v-col cols="12" md="2">
                  <v-autocomplete
                    label="年份"
                    outlined
                    dense
                    :rules="[rules.required]"
                    required
                    v-model="ship.year"
                    :items="yearList"
                    clearable
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12" md="2">
                  <v-select
                    v-model="ship.department"
                    outlined
                    dense
                    label="所属部门"
                    :items="items"
                    :rules="[rules.required]"
                    required
                  ></v-select>
                </v-col>
                <v-spacer></v-spacer>
                <v-col cols="12" md="2">
                  <VImportBtn
                    :disabled="ship.year && ship.department && ship.shipCode"
                    :import-url="importUrl"
                    @importSuccess="importSuccess"
                    :otherParams="ship"
                  ></VImportBtn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
      </v-card>
    </v-expand-transition>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="shipParams"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-autocomplete
            dense
            label="部门"
            outlined
            v-model="shipParams.department"
            :items="items"
            clearable
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <!-- <v-text-field
            dense
            outlined
            v-model="shipParams.year"
            label="年份"
          ></v-text-field> -->
          <v-autocomplete
            label="年份"
            outlined
            dense
            v-model="shipParams.year"
            :items="yearList"
            clearable
          ></v-autocomplete>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-autocomplete
            dense
            label="审核状态"
            outlined
            v-model="shipParams.status"
            :items="statusItems"
            clearable
          ></v-autocomplete>
        </v-col> -->
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="shipParams.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="#00796B"
          class="mx-1"
          :disabled="!selected"
          @click="showEquipments"
          v-permission="['年度计划管理:查看备件']"
        >
          <v-icon left>mdi-store-search-outline</v-icon>
          查看备件
        </v-btn>
        <v-btn
          outlined
          tile
          color="#0277BD"
          class="mx-1"
          :disabled="selected.status != 3"
          :href="`/api/business/shipAffairs/planYear/wordExport/${selected.id}`"
          target="_blank"
          :loading="loading"
          v-permission="['年度计划管理:导出word文档']"
        >
          <v-icon left>mdi-microsoft-word</v-icon>
          导出word文档
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="#004D40"
          class="mx-1"
          href="/api/business/shipAffairs/planYear/downloadTemplate"
          target="_blank"
          :loading="loading"
          v-permission="['年度计划管理:下载EXCEL模板']"
        >
          <v-icon left>mdi-microsoft-excel</v-icon>
          下载EXCEL模板
        </v-btn> -->
        <v-btn
          outlined
          tile
          :disabled="
            selected.status != 3 || selected.businessStatus != '已完结'
          "
          color="#00796B"
          class="mx-1"
          @click="addNextYearPlan"
          v-permission="['年度计划管理:生成下一年年度计划']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          生成下一年年度计划
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          to="/maritime-maintence/maintenance/components/yearplan-detail/new"
          v-permission="['年度计划管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          class="mx-1"
          color="#006064"
          @click="formShow = true"
          v-permission="['年度计划管理:Excel导入创建']"
        >
          Excel导入创建
        </v-btn> -->

        <v-btn
          outlined
          tile
          color="error"
          class="mx-1"
          :disabled="!selected"
          @click="delItem"
          v-permission="['年度计划管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <!-- <template v-slot:[`item.auditStatus`]="{ item }">
        <v-chip small v-if="item.auditStatus === `未审核`" color="">
          未审核
        </v-chip>
        <v-chip small v-else-if="item.auditStatus === `船长审核`" color="info">
          等待船长审批
        </v-chip>
        <v-chip
          small
          v-else-if="item.auditStatus === `机务主管审批`"
          color="info"
        >
          等待机务主管审核
        </v-chip>
        <v-chip
          small
          v-else-if="item.auditStatus === `审核通过`"
          color="success"
        >
          审核通过
        </v-chip>
        <v-chip v-else-if="item.auditStatus === '已驳回'" color="error" small>
          已驳回
        </v-chip>
        <v-chip v-else color="#004D40">完结</v-chip>
      </template> -->
    </v-table-searchable>
    <VdialogEquipment
      v-model="equipmentShow"
      :yearPlanId="selected.id"
    ></VdialogEquipment>
  </v-container>
</template>
<script>
import VImportBtn from './components/v-import-btn.vue'
import VdialogEquipment from './components/v-dialog-equipment'

export default {
  name: 'yearplan-list',
  components: { VImportBtn, VdialogEquipment },
  created() {
    this.tableName = '年度计划表'
    this.reqUrl = '/business/shipAffairs/planYear/page'
    this.importUrl = `/business/shipAffairs/planYear/excelImport`
    this.headers = [
      { text: '船舶名', value: 'shipName' },
      { text: '年份', value: 'year' },
      { text: '所属部门', value: 'department' },
      { text: '负责人', value: 'principal' },
      { text: '负责人岗位', value: 'principalDept' },
      { text: '更新时间', value: 'updateTime' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.items = [
      { text: '甲板部', value: '甲板部' },
      { text: '轮机部', value: '轮机部' },
    ]
    this.statusItems = [
      { text: '未审核', value: 'NO_AUDIT' },
      { text: '等待船长审批', value: 'CAPTAIN_AUDIT' },
      { text: '等待机务主管审核', value: 'MAINTENANCE_AUDIT' },
      { text: '审核通过', value: 'FINISH_AUDIT' },
      { text: '已驳回', value: 'REJECT' },
      { text: '已完结', value: 'COMPLETE' },
    ]
    this.pushParams = {
      name: 'yearplan-detail',
    }
    this.rangeArray(2020, new Date().getFullYear() + 1)
  },
  data() {
    return {
      useName: false,
      selected: false,
      formShow: false,
      loading: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        year: (v) => /^\d{4}$/.test(v) || '请输入正确的年格式如2022',
      },
      ship: {
        shipCode: '',
        year: '',
        department: '',
      },
      shipItems: [],
      shipParams: {
        shipCode: '',
        year: new Date().getFullYear(),
        department: '',
        status: '',
        isMe: true,
      },
      yearList: Array.from({ length: 50 }, (_, i) => i + 1),
      equipmentShow: false,
    }
  },
  methods: {
    rangeArray(start, end) {
      let length = end - start + 1
      let step = start - 1
      this.yearList = Array.from({ length: length }, () => {
        step++
        return step
      })
    },
    async importSuccess() {
      await this.$refs.table.loadTableData()
      this.isEdit = false
      this.formShow = false
      this.$refs.table.disabled = false
      this.ship = {
        shipName: '',
        year: 0,
        department: '',
      }
      await this.$nextTick()
    },
    closeForm() {
      this.$refs.form.reset()
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
      this.ship = {
        shipName: '',
        year: '',
        department: '',
      }
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/planYear/deletePlayYear/${this.selected.id}`,
        {},
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      await this.$nextTick()
    },
    async addNextYearPlan() {
      if (
        !(await this.$dialog.msgbox.confirm('是否生成选中记录的下一年计划？'))
      )
        return
      if (this.selected.status !== '3') {
        this.$dialog.message.error('当前记录审批还未通过')
        return
      }
      const { errorRaw } = await this.getAsync(
        `/business/shipAffairs/planYear/nextYearPlan/${this.selected.id}`,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('生成计划成功')
      this.selected = false
      await this.$refs.table.loadTableData()
    },
    showEquipments() {
      this.equipmentShow = true
    },
  },
  mounted() {},
}
</script>

<style></style>
