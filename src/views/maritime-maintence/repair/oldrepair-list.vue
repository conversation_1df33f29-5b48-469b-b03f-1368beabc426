<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :show-select="false"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            outlined
            dense
            label="部门"
            :items="['轮机部', '甲板部']"
            clearable
            v-model="searchObj.department"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-year-month-picker outlined v-model="date"></v-year-month-picker>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            label="检修负责人岗位"
            :items="stationItems"
            v-model="searchObj.maintenancePrincipal"
            outlined
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            outlined
            dense
            label="检修类型"
            clearable
            :items="['定期', '定时']"
            v-model="searchObj.maintenanceType"
          ></v-select>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="2">
          <v-select
            outlined
            dense
            label="状态"
            :items="completeStatus"
            v-model="searchObj.completeStatus"
            clearable
          ></v-select>
        </v-col> -->
      </template>
      <template #btns></template>
      <template v-slot:[`item.componentId`]="{ item }">
        <router-link
          v-if="item.componentId"
          :to="{
            name: 'spare-out-detail',
            params: { id: item.componentId },
          }"
        >
          查看
        </router-link>
        <div v-else>-</div>
      </template>
      <template v-slot:[`item.greaseId`]="{ item }">
        <router-link
          v-if="item.greaseId"
          :to="{
            name: 'soil-out-detail',
            params: { id: item.greaseId },
          }"
        >
          查看
        </router-link>
        <div v-else>-</div>
      </template>
      <template v-slot:[`item.materialId`]="{ item }">
        <router-link
          v-if="item.materialId"
          :to="{
            name: 'materials-out-detail',
            params: { id: item.materialId },
          }"
        >
          查看
        </router-link>
        <div v-else>-</div>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import { cacheGetDefault } from '@/util/cache'
export default {
  name: 'oldrepair-list',
  created() {
    this.tableName = '维护保养历史记录'
    this.reqUrl = '/business/shipAffairs/monthMonth/pageItemMonthItem'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '项目编号', value: 'projectCode' },
      { text: '船舶名', value: 'shipName' },
      { text: '部门', value: 'department' },
      { text: '年月时间', value: 'yearAndmonth' },
      { text: '维护保养部位', value: 'maintenanceSite' },
      { text: '维修保养内容', value: 'maintenanceContent' },
      { text: '计划时间', value: 'planDate' },
      { text: '完工时间', value: 'completeDate' },
      { text: '检修周期', value: 'maintenanceCycle' },
      { text: '检修负责人岗位', value: 'maintenancePrincipal' },
      { text: '完工人', value: 'completeUserName' },
      { text: '检修类型', value: 'maintenanceType' },
      { text: '完工内容', value: 'completeContent' },
      { text: '完工类型', value: 'completeStatus' },
      { text: '备件', value: 'componentId' },
      { text: '滑油', value: 'greaseId' },
      { text: '物料', value: 'materialId' },
      { text: '备注', value: 'remark' },
      { text: '完工报告', value: 'attachmentRecords' },
    ]
    this.completeStatus = [
      { text: '提前完工', value: '0' },
      { text: '完工', value: '1' },
      { text: '手动延期', value: '2' },
      { text: '待做', value: '4' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
      },
      date: '',
      stationItems: [],
    }
  },
  watch: {
    date: {
      handler(val) {
        if (val) {
          this.searchObj = {
            ...this.searchObj,
            year: parseInt(this.date.slice(0, 4)),
            month: parseInt(this.date.slice(5, 7)),
          }
        } else {
          delete this.searchObj.year
          delete this.searchObj.month
        }
      },
    },
  },
  methods: {
    async getShipStation() {
      let that = this
      let data = await cacheGetDefault('ship-station', async () => {
        const { data, errorRaw } = await that.getAsync(
          '/business/crew/infra/positionList',
          {},
          false,
        )
        if (errorRaw) {
          that.$dialog.message.error('船上岗位列表获取失败，请重试')
          return null
        }
        if (data.length === 0) {
          that.$dialog.message.error('船上岗位为空，部分功能受损')
        }
        return data
      })
      this.stationItems = data.map((item) => {
        return {
          text: item.positionName,
          value: item.positionName,
        }
      })
    },
  },

  mounted() {
    this.getShipStation()
  },
}
</script>

<style></style>
