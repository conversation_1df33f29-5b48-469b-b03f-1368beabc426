<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        SAP库存比对
        <v-spacer></v-spacer>
        <v-btn
          :disabled="!shipCode"
          outlined
          tile
          color="info"
          class="mx-1"
          @click.stop="loadStatistic"
          v-permission="['SAP库存比对:拉取SAP数据']"
        >
          <v-icon left>mdi-magnify-expand</v-icon>
          拉取SAP数据
        </v-btn>
        <v-btn
          :disabled="!shipCode"
          outlined
          tile
          color="info"
          class="mx-1"
          :href="excelUrl"
          v-permission="['SAP库存比对:导出EXCEL']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-form ref="form">
          <v-row>
            <v-col cols="12" sm="6" md="2">
              <v-ship-select v-model="shipCode"></v-ship-select>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-divider></v-divider>
      <v-data-table
        :headers="headers"
        :items="list"
        hide-default-footer
        disable-pagination
        dense
        class="use-divider"
      >
        <template v-slot:[`item.name`]="{ item }">
          {{ `${item.name}/${item.nameEn}` }}
        </template>
        <template v-slot:[`item.shipInfo`]="{ item }">
          {{ item.shipInfo.chShipName }}
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>
<script>
// emNumber	本系统物件库存	integer(int32)
// itemSapCode	物件sap编码	string
// itemSapNum	物件sap库存	integer(int32)
export default {
  name: 'component-compare',
  created() {
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '物件名称', value: 'itemName' },
      { text: '物件编号', value: 'itemNo' },
      { text: '物件sap编码', value: 'itemSapCode' },
      { text: '本系统库存', value: 'itemNumber' },
      { text: 'sap库存', value: 'itemSapNum' },
      { text: '库存差额', value: 'itemDiffNumber' },
    ]
  },
  data() {
    return {
      list: [],
      shipCode: '',
    }
  },

  computed: {
    excelUrl() {
      return `/api/business/shipAffairs/sapStocks/exportSapStocksDiff?shipCode=${this.shipCode}`
    },
  },

  methods: {
    async loadStatistic() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/sapStocks/getSapStocks',
        { shipCode: this.shipCode },
      )
      this.list = data
    },
  },

  mounted() {},
}
</script>

<style></style>
