<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :specialHeaders="specialHeaders"
      v-model="selected"
      :headers="isShip ? headersShip : headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      show-expand
      :show-select="false"
      :search-dicts="searchDicts"
      :showExportButton="true"
    >
      <template v-slot:[`item.isDockRepair`]="{ item }">
        {{ item.isDockRepair ? '是' : '否' }}
      </template>
      <template #searchflieds>
        <v-col cols="12" md="3">
          <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
        </v-col>
        <v-col cols="12" md="3">
          <v-switch
            class="mt-1"
            v-model="searchObj.all"
            :label="searchObj.all ? '显示全部' : '显示非0'"
            color="primary"
          ></v-switch>
        </v-col>
      </template>
      <template #btns></template>
      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length">
          <stock-detail
            :depositoryId="item.depositoryId"
            :itemId="item.itemId"
          ></stock-detail>
        </td>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import stockDetail from './private/stock-detail.vue'
export default {
  components: { stockDetail },
  name: 'soil-stock-list',
  created() {
    this.tableName = '滑油库存'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.reqUrl =
      '/business/shipAffairs/purchaseManage/purchaseStocksMsgByParams'
    this.headers = [
      // { text: '船舶', value: 'shipInfo' },
      { text: '船舶', value: 'chShipName' },
      { text: '滑油名', value: 'itemName' },
      { text: '滑油号', value: 'itemNo' },
      { text: 'SAP滑油编码', value: 'sapNumber', hideDefault: true },
      { text: '滑油类型', value: 'componentProperty' },
      { text: '油柜名称', value: 'depositoryName' },
      // { text: '仓库位置', value: 'depositoryAddress' },
      { text: '坞修', value: 'isDockRepair' },
      { text: '总金额', value: 'totalPrice' },
      { text: '库存数量', value: 'itemNumber' },
      { text: '单位', value: 'unit' },
      { text: '最低库存量', value: 'minimumInventory' },
      { text: '最高库存量', value: 'maximumInventory' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '滑油名', value: 'itemName' },
      { text: '滑油号', value: 'itemNo' },
      { text: 'SAP滑油编码', value: 'sapNumber', hideDefault: true },
      { text: '滑油类型', value: 'componentProperty' },
      { text: '油柜名称', value: 'depositoryName' },
      // { text: '仓库位置', value: 'depositoryAddress' },
      { text: '坞修', value: 'isDockRepair' },
      { text: '库存数量', value: 'itemNumber' },
      { text: '单位', value: 'unit' },
      { text: '最低库存量', value: 'minimumInventory' },
      { text: '最高库存量', value: 'maximumInventory' },
    ]
    this.searchDicts = [
      {
        dicType: 'ship_grease_info_type',
        label: '滑油类型',
        key: 'greaseType',
      },
    ]
    this.specialHeaders = [
      // {
      //   text: 'componentProperty',
      //   value: [
      //     { text: '0', value: '普通备件' },
      //     { text: '1', value: 'SAP备件' },
      //     { text: '2', value: '固定资产' },
      //     { text: '3', value: '通导固定资产' },
      //   ],
      // },
      {
        text: 'isDockRepair',
        value: [
          { text: true, value: '是' },
          { text: false, value: '否' },
        ],
      },
    ]
  },

  watch: {
    'searchObj.shipCode'(val) {
      if (val) this.loadFirstEquipment()
    },
  },

  data() {
    return {
      selected: false,
      searchObj: { stocksType: '2' },
      firstEquipments: [],
    }
  },

  methods: {
    async loadFirstEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { current: 1, size: 99, shipCode: this.shipCode },
      )
      const { records } = data
      this.firstEquipments = records?.map((i) => {
        return {
          text: i.equipmentCname,
          value: i.id,
        }
      })
    },
  },

  mounted() {},
}
</script>

<style></style>
