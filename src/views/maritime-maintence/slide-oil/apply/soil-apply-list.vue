<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-dicts="searchDicts"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :push-params="pushParams"
      :search-remain="searchObj"
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.applyType"
            :items="applyTypeOptions"
            label="申请类型"
            clearable
            dense
            outlined
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            :items="businessStatusOptions"
            label="业务状态"
            clearable
            dense
            outlined
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="selected.status !== '3'"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="genOrder"
          v-permission="['滑油申请:生成订单']"
        >
          <v-icon left>mdi-order-numeric-ascending</v-icon>
          生成订单
        </v-btn>
        <v-btn
          :disabled="!canEnd"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="isItemEnd"
          v-permission="['滑油申请:完结']"
        >
          <v-icon left>mdi-close-circle</v-icon>
          完结
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'soil-apply-detail', params: { id: 'new' } }"
          v-permission="['滑油申请:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['滑油申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <!-- <template v-slot:[`item.isEnd`]="{ item }">
        <v-chip small color="warning" v-if="item.isEnd == false">未完结</v-chip>
        <v-chip small v-if="item.isEnd == true">已完结</v-chip>
      </template> -->
      <template v-slot:[`item.applyType`]="{ item }">
        <v-chip small v-if="item.applyType == 1">常规</v-chip>
        <v-chip small color="warning" v-if="item.applyType == 2">紧急</v-chip>
        <v-chip small color="error" v-if="item.applyType == 3">特急</v-chip>
      </template>
      <!-- <template v-slot:[`item.isDockRepair`]="{ item }">
        {{ item.isDockRepair ? '是' : '否' }}
      </template> -->
    </v-table-searchable>
  </v-container>
</template>
<script>
// applicantName	申请人姓名（冗余）	string
// applicantPost	申请人岗位	string
// applicantUserId	申请人id	string
// applyCode	申请单号	string
// applyDate	申请日期	string
// applyDept	船舶端申请部门	string
// applyType	申请单类型;申请类型;申请类型:1 常规 2 紧急 3 特急	integer
// businessStatus	业务状态	string
// greaseType	滑油类型	integer
// id	物理主键	string
// isDockRepair	是否坞修	boolean
// isLand	单据提交来源1：岸基；2：船舶端	string
// port	送货港口（冗余）	string
// portId	港口id；送货港口	string
// remark	备注	string
// sendoilDate	送货日期	string
export default {
  name: 'soil-apply-list',
  created() {
    this.tableName = '滑油申请'
    this.reqUrl = '/business/shipAffairs/purchaseManage/greaseApplyPage'
    this.searchDicts = [
      {
        dicType: 'ship_grease_info_type',
        label: '滑油类型',
        key: 'greaseType',
      },
    ]
    this.headers = [
      { text: '所属船舶', value: 'shipInfo' },
      { text: '申请单号', value: 'applyCode' },
      { text: '申请日期', value: 'applyDate' },
      { text: '申请部门', value: 'applyDept' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '申请类型', value: 'applyType' },
      // { text: '是否坞修', value: 'isDockRepair' },
      { text: '送货港口', value: 'port' },
      // { text: '是否完结', value: 'isEnd' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '申请时间',
      interval: true,
    }
    this.pushParams = { name: 'soil-apply-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: true,
      },
      applyTypeOptions: [
        { text: '常规', value: 1 },
        { text: '紧急', value: 2 },
      ],
      businessStatusOptions: [
        { text: '未提交', value: '未提交' },
        { text: '填写申请单', value: '填写申请单' },
        { text: '大副', value: '大副' },
        { text: '轮机长', value: '轮机长' },
        { text: '船长', value: '船长' },
        { text: '机务主管', value: '机务主管' },
        { text: '滑油采购主管', value: '滑油采购主管' },
        { text: '审批通过', value: '审批通过' },
        { text: '审批已驳回', value: '审批已驳回' },
        { text: '全部询价', value: '全部询价' },
        { text: '已完结', value: '已完结' },
      ],
    }
  },
  computed: {
    canEnd() {
      return this.selected.status == 3 && this.selected.isEnd == false
    },
  },
  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/greaseApplyDelete',
        { applyId: this.selected.id },
      )
      if (!errorRaw) {
        this.selected = false
        this.$refs.table.loadTableData()
      }
    },
    async isItemEnd() {
      if (!(await this.$dialog.msgbox.confirm('确定完结此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/greaseApplyIsEnd',
        { applyId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`完结失败，请重试`)
        return
      }
      this.$dialog.message.success(`完结成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async genOrder() {
      if (!(await this.$dialog.msgbox.confirm('确定生成订单吗？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/greaseApplyGenOrder',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
        return
      }
      this.$dialog.message.success(`操作成功，请前往滑油订单查看详细信息！`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
