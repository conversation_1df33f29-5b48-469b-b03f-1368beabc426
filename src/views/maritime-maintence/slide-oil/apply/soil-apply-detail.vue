<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['滑油申请:编辑']"
      :title="`滑油申请-${isEdit ? detail.applyCode : '新增'}`"
      :tooltip="isEdit ? detail.applyCode : '新增'"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      @submit="submit"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: detail.systemReportId },
          }"
          color="info"
          small
          class="mx-1"
        >
          查看部门报表
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  readonly
                  outlined
                  dense
                  v-model="detail.applyDate"
                  use-today
                  label="申请日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  :readonly="!canSubmit"
                  dense
                  outlined
                  v-model="detail.applyDept"
                  label="申请部门"
                  :items="depts"
                  :rules="[rules.required]"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-station-jw
                  v-model="detail.applicantPost"
                  :disabled="
                    ($local.data.get('userInfo').userType == '2' &&
                      !$local.data
                        .get('userInfo')
                        .roleName.includes('轮机长')) ||
                    !canSubmit
                  "
                ></v-ship-station-jw>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :readonly="isEdit"
                  dense
                  outlined
                  v-model="detail.applyType"
                  label="申请类型"
                  :items="applyTypes"
                  :rules="[rules.required]"
                ></v-select>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-dict-select
                  v-model="detail.greaseType"
                  label="滑油类型"
                  dictType="ship_grease_info_type"
                  :rules="[rules.required]"
                  required
                  :readonly="!canSubmit"
                ></v-dict-select>
              </v-col> -->
              <v-col cols="12" md="3">
                <port-select-dialog2
                  :readonly="
                    detail.businessStatus !== '通导信息主管' &&
                    detail.businessStatus !== '机务主管' &&
                    !canSubmit
                  "
                  v-model="detail.portId"
                  :initSelected="initPort"
                  :rules="[rules.required]"
                ></port-select-dialog2>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="
                    detail.businessStatus !== '通导信息主管' &&
                    detail.businessStatus !== '机务主管' &&
                    !canSubmit
                  "
                  outlined
                  dense
                  v-model="detail.arriveDate"
                  :rules="[rules.required]"
                  label="到港日期"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  v-model="detail.remark"
                  label="备注"
                  dense
                  :readonly="!canSubmit1"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template v-if="canEdit" #滑油列表按钮>
        <v-btn
          :disabled="!detail.shipCode"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['滑油列表:选择滑油']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择滑油
        </v-btn>
        <v-btn
          :disabled="!select"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCom"
          v-permission="['滑油列表:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #滑油列表>
        <v-form ref="form2">
          <v-table-list
            v-model="select"
            item-key="greaseId"
            :headers="soilHeaders"
            :items="soils"
          >
            <template
              v-if="detail.status == 2"
              v-slot:[`item.auditQuantity`]="{ item }"
            >
              <v-text-field
                v-model="item.auditQuantity"
                label="审批数量"
                type="number"
                single-line
                dense
                :rules="
                  item.type == '1649340717607944194'
                    ? [rules.required, rules.decimal, rules.aboveZero]
                    : [rules.required, rules.int, rules.aboveZero]
                "
              ></v-text-field>
            </template>
            <template
              v-if="detail.status != 2 && detail.status != 3"
              v-slot:[`item.applyNumber`]="{ item }"
            >
              <v-text-field
                v-model="item.applyNumber"
                label="申请数量"
                type="number"
                single-line
                dense
                :rules="
                  item.type == '1649340717607944194'
                    ? [rules.required, rules.decimal, rules.aboveZero]
                    : [rules.required, rules.int, rules.aboveZero]
                "
              ></v-text-field>
            </template>
            <template v-slot:[`item.type`]="{ item }">
              <span v-if="item.type == '1581991857030852611'">主机系统油</span>
              <span v-if="item.type == '1581991857047629825'">副机系统油</span>
              <span v-if="item.type == '1581991857022464003'">主机气缸油</span>
              <span v-if="item.type == '1649340717607944194'">
                其他小品种油
              </span>
            </template>
          </v-table-list>
        </v-form>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <soil-select-dialog
      v-model="dialog"
      :searchRemain="searchObj"
      :soils.sync="soils"
    ></soil-select-dialog>
  </v-container>
</template>
<script>
import PortSelectDialog2 from '../../components/port-select-dialog2.vue'
import SoilSelectDialog from './private/soil-select-dialog.vue'
export default {
  components: { SoilSelectDialog, PortSelectDialog2 },
  name: 'soil-apply-detail',
  created() {
    this.backRouteName = 'soil-apply-list'
    this.subtitles = ['基本信息', '滑油列表']
    this.depts = ['甲板部', '轮机部']
    this.applyTypes = [
      { text: '常规', value: 1 },
      { text: '紧急', value: 2 },
      // { text: '特急', value: 3 },
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
  },
  data() {
    return {
      detail: {
        shipInfo: {
          shipCode: '',
        },
        attachmentIds: [],
      },
      select: false,
      mapping: {},
      needFields: [],
      soilHeaders: [
        { text: '英文名', value: 'greaseEnName' },
        // { text: '滑油号', value: 'greaseNo' },
        { text: '厂家', value: 'greaseFactory' },
        { text: '规格', value: 'greaseSpec' },
        { text: '滑油类型', value: 'type' },
        { text: '申请数量', value: 'applyNumber', width: 150 },
        { text: '审批数量', value: 'auditQuantity', width: 150 },
        { text: '单位', value: 'greaseUnit' },
      ],
      soils: [],
      delList: [],
      dialog: false,
      searchObj: {},
      initPort: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
      },
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    canSubmit1() {
      return (
        !this.detail.auditParams ||
        !!this.detail.auditParams?.isReject ||
        this.detail.businessStatus === '滑油采购主管'
      )
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
  },

  methods: {
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/greaseApplyDetailById',
        { applyId: this.$route.params.id },
      )
      this.detail = data
      this.soils = data.detailList.map((i) => ({
        ...i,
        // max: (a) => i.inventoryNumber >= a || '出库数量不得超过最大库存量',
      }))
      this.detail = { ...data, shipCode: data.shipInfo.shipCode }
      this.initPort = {
        portCn: data.port,
        id: data.portId,
      }
      await this.loadNeedFields()
    },

    createCom() {
      this.searchObj = {
        // type: this.detail.greaseType,
        shipCode: this.detail.shipCode,
        stopUse: false,
      }
      this.dialog = true
    },
    delCom() {
      // this.delList.push({ ...this.select, operationType: 3 })
      this.soils = this.soils.filter((i) => i.greaseId !== this.select.greaseId)
      this.select = false
    },

    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },

    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate() || !this.$refs.form2.validate()) {
        return false
      }
      if (this.soils.length === 0) {
        this.$dialog.message.warning('请添加滑油')
        return false
      }
      const detailList = this.getCompWithOperation()
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/greaseApplySaveOrUpdate',
        // TODO:根据后续判断
        { ...this.detail, detailList, isLand: 0 },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            processInstanceId: this.detail.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent:
              this.mapping[f.mappingCode] ||
              this.$local.data.get('userInfo').id,
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) return
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/purchaseManage/greaseApplySubmit',
            { applyId: data },
          )
          if (!errorRaw) goBack()
        } else {
          // 最后一个人审批时候生成订单数据，this.$refs.audit.adopt审批通过时候
          // if (
          //   this.detail.businessStatus == '滑油采购主管' &&
          //   this.$refs.audit.adopt
          // ) {
          //   const flag = await this.genOrder()
          //   console.log('11111111111111:' + flag)
          //   if (flag) {
          //     // console.log('11111111111111:' + flag)
          //     const error = await this.$refs.audit.submit()
          //     if (!error) goBack()
          //   }
          // } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
          // }
          // const error = await this.$refs.audit.submit()
          // if (!error) goBack()
        }
      }
    },
    async genOrder() {
      const { data, errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/greaseApplyGenOrder',
        { id: this.detail.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
        return false
      }
      if (data) {
        //不超预算 可以提交
        return true
      }
    },

    async loadNeedFields() {
      if (!this.detail?.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        { processInstanceId: this.detail.auditParams.processInstanceId },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          this.mapping[t.mappingCode] = new Date(Date.now())
            .toISOString()
            .substr(0, 10)
        }
      }
    },

    getCompWithOperation() {
      const ids = this.soils.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.detailList
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.soils.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
