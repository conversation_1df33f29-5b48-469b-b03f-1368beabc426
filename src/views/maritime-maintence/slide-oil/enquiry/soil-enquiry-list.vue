<template>
  <v-container fluid>
    <enquiry-form
      v-if="selected && formShow"
      @close="formShow = false"
      :enquiry="selected || { shipInfo: {} }"
      @success="loadTableData"
    ></enquiry-form>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :push-params="pushParams"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="status2"
            :items="statusMap"
            label="审批状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            :items="businessStatusMap"
            label="业务状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'soil-enquiry-detail', params: { id: 'new' } }"
          v-permission="['滑油询价:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!-- <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :disabled="selected.businessStatus !== '未提交'"
          @click="formShow = true"
          v-permission="['滑油询价:发起询价']"
        >
          <v-icon left>mdi-comment-question-outline</v-icon>
          发起询价
        </v-btn> -->
        <v-btn
          outlined
          tile
          color="info"
          class="mx-1"
          :disabled="
            selected.businessStatus !== '询价中' &&
            selected.businessStatus !== '重新报价' &&
            selected.businessStatus !== '超期' &&
            selected.businessStatus !== '超期无报价'
          "
          @click="dialogDelay = true"
          v-permission="['滑油询价:延期']"
        >
          <v-icon left>mdi-timer-plus-outline</v-icon>
          延期
        </v-btn>
        <v-btn
          :disabled="selected.businessStatus !== '未提交'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
          v-permission="['滑油询价:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="
            selected.businessStatus !== '未提交' &&
            selected.businessStatus !== '超期' &&
            selected.businessStatus !== '报价完成' &&
            selected.businessStatus !== '超期无报价' &&
            selected.businessStatus !== '询价中' &&
            selected.businessStatus !== '用户开标'
          "
          outlined
          tile
          color="error"
          class="mx-1"
          @click="disuse"
          v-permission="['滑油询价:废弃']"
        >
          <v-icon left>mdi-cancel</v-icon>
          废弃
        </v-btn>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses2[item.status] }}
        </v-chip>
      </template>
      <template v-slot:[`item.isDockRepair`]="{ item }">
        {{ item.isDockRepair ? '是' : '否' }}
      </template>
    </v-table-searchable>
    <delay-dialog
      v-model="dialogDelay"
      :enquiryId="selected.id"
      @success="loadTableData"
    ></delay-dialog>
  </v-container>
</template>
<script>
import enquiryForm from '../../components/enquiry/enquiry-form.vue'
import delayDialog from '../../components/enquiry/delay-dialog.vue'
// businessStatus	业务状态	string
// endTime	截至报价时间	string
// enquiryNo	询价单号;根据编码规则生成	string
// enquiryType	询价类型;物品类型，01 备件；02 物料；03 滑油；99 其他	integer
// exacctId	费用科目id;费用科目id	string
// handler	操作人/经办人	string
// id	物理主键	string
// inquier	询价人;名称	string
// processInstanceId	流程实例id	string
// receiveDate	交货日期	string
// receivePortId	交货港口id	string
// remark	备注
export default {
  components: { enquiryForm, delayDialog },
  name: 'soil-enquiry-list',
  created() {
    this.tableName = '滑油询价'
    this.reqUrl = '/business/shipAffairs/purchaseManage/purchaseEnquiryPage'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '询价单号', value: 'enquiryNo' },
      { text: '是否坞修', value: 'isDockRepair' },
      { text: '创建日期', value: 'createDate' },
      { text: '交货日期', value: 'receiveDate' },
      { text: '截至报价时间', value: 'endTime' },
      { text: '询价人', value: 'inquier' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
    ]
    this.fuzzyLabel = '模糊搜索'
    this.pushParams = { name: 'soil-enquiry-detail' }
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
      { text: '废弃', value: '5' },
    ]
    this.businessStatusMap = [
      { text: '未提交', value: '未提交' },
      { text: '询价中', value: '询价中' },
      { text: '超期', value: '超期' },
      { text: '报价完成', value: '报价完成' },
      { text: '超期,报价完成', value: '超期,报价完成' },
      { text: '超期无报价', value: '超期无报价' },
      { text: '审批通过', value: '审批通过' },
    ]
    this.statuses2 = ['暂无审批', '草稿', '审批中', '已审批', '已驳回', '废弃']
    this.statusColors = ['info', '', 'warning', 'success', 'error', 'error']
  },

  data() {
    return {
      selected: false,
      formShow: false,
      searchObj: {
        enquiryType: '03',
        isMe: true,
        status: '2',
        businessStatus: '',
      },
      dialogDelay: false,
      status2: '2',
    }
  },
  watch: {
    status2(val) {
      this.searchObj.status = val
    },
  },

  methods: {
    async delEnquiry() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryDelete',
        { recordId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async loadTableData() {
      await this.$refs.table.loadTableData()
    },
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryDelete',
        { recordId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async disuse() {
      if (!(await this.$dialog.msgbox.confirm('确定废弃此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/disusePurchaseEnquiry',
        { enquiryId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`废弃失败，请重试`)
        return
      }
      this.$dialog.message.success(`废弃成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {
    if (this.$route.query.businessStatus != undefined) {
      // console.log(1)
      this.searchObj.businessStatus = this.$route.query.businessStatus
      this.status2 = this.$route.query.status
      // 在3秒后执行一次任务
      // setTimeout(() => {
      //   this.searchObj.businessStatus = ''
      //   // console.log(1)
      // }, 1000)
    }
  },
}
</script>

<style></style>
