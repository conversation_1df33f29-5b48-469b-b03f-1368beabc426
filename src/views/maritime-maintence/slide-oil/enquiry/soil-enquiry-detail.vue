<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['滑油询价:编辑']"
      :title="`滑油询价-${detail.enquiryNo || '新增'}`"
      :tooltip="detail.enquiryNo || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="
        detail.auditParams && detail.auditParams.taskId && detail.status == 2
      "
      @save="save"
      @submit="submit"
      :can-save="false"
    >
      <template v-if="!isEdit" v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          @click="save"
          color="success"
          small
          class="mx-1"
          v-permission="['滑油询价:发起询价']"
        >
          发起询价
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="form">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <apply-selelct
                  :readonly="isEdit"
                  v-model="detail.applyId"
                  :rules="[rules.required]"
                  :initSelected="initApply"
                  :shipCode="detail.shipCode"
                ></apply-selelct>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  :disabled="detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <port-select-dialog
                  :readonly="isEdit || canBid"
                  :disabled="!detail.applyId"
                  v-model="detail.receivePortId"
                  :rules="[rules.required]"
                  :initSelected="initPort"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  outlined
                  label="交货日期"
                  v-model="detail.receiveDate"
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  dense
                  outlined
                  label="创建日期"
                  v-model="detail.createDate"
                  use-today
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col v-if="isEdit" cols="12" md="3">
                <vs-date-picker
                  dense
                  outlined
                  label="报价起始时间"
                  v-model="detail.startTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col v-if="isEdit" cols="12" md="3">
                <vs-date-picker
                  dense
                  outlined
                  label="报价截至时间"
                  v-model="detail.endTime"
                  disabled
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="询价人"
                  dense
                  outlined
                  :readonly="isEdit"
                  v-model="detail.inquier"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="项目说明"
                  dense
                  outlined
                  v-model="detail.remark"
                  :readonly="
                    !(
                      idIsNew ||
                      detail.businessStatus == '未提交' ||
                      detail.businessStatus == '超期' ||
                      detail.businessStatus == '报价完成' ||
                      detail.status == 4 ||
                      detail.businessStatus == '待商务主管定标'
                    )
                  "
                ></v-textarea>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="2">
                <b>申请部门:</b>
                {{ applyInfo.applyDept }}
              </v-col>
              <!-- <v-col cols="12" md="2">
                <b>滑油分类:</b>
                {{ greaseOil }}
              </v-col> -->
            </v-row>
            <v-row v-if="!isEdit">
              <v-col cols="12">
                <enquiry-form
                  ref="enquiry"
                  @close="formShow = false"
                  :shipCode="detail.shipCode"
                  :businessType="'滑油'"
                ></enquiry-form>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template #询价详情按钮 v-if="canAddItem">
        <v-btn
          :disabled="!applyInfo.detailList"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click="enquiryDialog = true"
          v-permission="['滑油询价详情:选择询价滑油']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择询价滑油
        </v-btn>
      </template>
      <template #询价详情>
        <v-table-list
          :show-select="false"
          v-model="selectedEn"
          :items="enquiryList"
          :headers="enquiryHeaders"
          item-key="itemId"
        >
          <template v-slot:[`item.type`]="{ item }">
            <span v-if="item.type == '1581991857030852611'">主机系统油</span>
            <span v-if="item.type == '1581991857047629825'">副机系统油</span>
            <span v-if="item.type == '1581991857022464003'">主机气缸油</span>
            <span v-if="item.type == '1649340717607944194'">其他小品种油</span>
          </template>
          <template v-if="canAddItem" v-slot:[`item.enquiryNum`]="{ item }">
            <v-text-field
              v-model="item.enquiryNum"
              dense
              label="询价数量"
              type="number"
              :rules="
                item.type == '1649340717607944194'
                  ? [rules.decimal]
                  : [rules.int]
              "
              single-line
            ></v-text-field>
          </template>
          <template v-if="canAddItem" v-slot:[`item.remark`]="{ item }">
            <v-text-field
              v-model="item.remark"
              label="备注"
              single-line
              dense
            ></v-text-field>
          </template>
          <template v-slot:[`item.actions`]="{ item }">
            <v-icon small class="mr-2" @click="getHis(item)">
              mdi-clipboard-text-clock-outline
            </v-icon>
          </template>
        </v-table-list>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-if="canBid" #报价详情按钮>
        <!-- <v-btn
          :disabled="!selectedQuote"
          tile
          color="primary"
          small
          class="mx-1"
          @click="quoteBid"
          :loading="quoteLoading"
          v-permission="['滑油报价详情:定标']"
        >
          <v-icon left>mdi-check</v-icon>
          定标
        </v-btn> -->
      </template>
      <template #报价详情 v-if="isEdit">
        <!-- <v-table-list
          v-if="
            !(
              detail.businessStatus == '询价中' ||
              detail.businessStatus == '重新报价'
            )
          "
          :show-select="canBid"
          v-model="selectedQuote"
          :headers="quoteHeaders"
          :items="quoteList"
        >
          <template v-slot:[`item.isWin`]="{ item }">
            {{ item.isWin ? '是' : '否' }}
          </template>
        </v-table-list> -->
        <compare-price-table
          v-if="
            !(
              detail.businessStatus == '询价中' ||
              detail.businessStatus == '重新报价' ||
              detail.businessStatus == '超期无报价'
            )
          "
          :quoteIds="quoteIds"
          :businessStatus="detail.businessStatus"
          :canBid="canBid"
          @refresh="refresh"
          ref="compareTable"
        ></compare-price-table>
      </template>
    </v-detail-view>
    <apply-item-selelct
      v-model="enquiryDialog"
      :items="applyInfo.detailList"
      :sitems.sync="enquiryList"
    ></apply-item-selelct>
    <v-dialog v-model="hisDialog" max-width="1200" hide-overlay attach="#mask">
      <v-card>
        <v-card-title class="text-h5">采购历史</v-card-title>
        <v-card-text>
          <v-table-searchable
            :show-select="false"
            outlined
            ref="table"
            :table-name="''"
            use-ship
            :headers="hisHeader"
            req-url="/business/shipAffairs/purchaseManage/getOrderInfoByItemId"
            :fix-header="false"
            :search-remain="searchObj"
          >
            <template #searchflieds>
              <v-col cols="12" md="4">
                <v-dialog-select
                  label="供应商"
                  item-text="name"
                  item-value="id"
                  v-model="searchObj.supplierId"
                  :headers="supHeaders"
                  req-url="/business/shipAffairs/Supplier/list"
                  fuzzy-label="供应商"
                  clearable
                  @clear="
                    () => {
                      searchObj.supplierId = ''
                    }
                  "
                ></v-dialog-select>
              </v-col>
            </template>
          </v-table-searchable>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import EnquiryForm from '../../components/enquiry/enquiry-form.vue'
import PortSelectDialog from '../../components/port-select-dialog.vue'
import ApplyItemSelelct from './private/apply-item-selelct.vue'
import applySelelct from './private/apply-selelct.vue'
import ComparePriceTable from './private/compare-price-table.vue'
import dictHelper from '@/mixin/dictHelper'

const enquiryType = '03'

export default {
  components: {
    applySelelct,
    PortSelectDialog,
    ApplyItemSelelct,
    ComparePriceTable,
    EnquiryForm,
  },
  name: 'soil-enquiry-detail',
  mixins: [dictHelper, routerControl],
  created() {
    this.backRouteName = 'soil-enquiry-list'
    this.subtitles = ['基本信息', '询价详情', '报价详情']
    //     code	滑油编码	string
    // factory	厂家	string
    // id	物理主键	string
    // isSap	是否sap	boolean
    // nameCn	中文名称	string
    // nameEn	英文名称	string
    // price	价格	number
    // specs	规格	string
    // type	滑油类型（0：主机系统油；1 副机系统油：；2 主机汽缸油；3 其他小品种油）	string
    // unit	单位	string
    // haveHistoryPur	历史采购;是否有过历史采购记录
    this.enquiryHeaders = [
      // TODO:滑油号
      { text: '滑油类型', value: 'type' },
      { text: '英文名称', value: 'nameEn' },
      { text: '滑油号', value: 'code' },
      { text: '单位', value: 'unit' },
      { text: '库存数量', value: 'stockQuantity' },
      { text: '审批数量', value: 'auditQuantity' },
      { text: '询价数量', value: 'enquiryNum', width: 140 },
      { text: '采购历史', value: 'actions' },
      { text: '备注', value: 'remark', width: 140 },
    ]
    this.quoteHeaders = [
      { text: '报价单号', value: 'quoteNo' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '备货天数', value: 'stockUpDays' },
      //{ text: '是否中标', value: 'isWin' },
      { text: '总价', value: 'totalPrice' },
      { text: '币种', value: 'currencyName' },
      { text: '折算美金', value: 'toUsd' },
      { text: '状态', value: 'businessStatus' },
    ]
    //     ccyCode		string
    // deliveryDate	交付日期	string
    // discount	折扣	number
    // orderId	采购订单id	string
    // price	单价	number
    // purchaseNum	订单购买数量	number
    // supplierName	供应商	string
    this.hisHeader = [
      { text: '船舶', value: 'shipInfo' },
      { text: '币种', value: 'ccyCode' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '单价', value: 'price' },
      { text: '折扣', value: 'discount' },
      { text: '订单购买数量', value: 'purchaseNum' },
      { text: '供应商', value: 'supplierName' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
  },
  data() {
    return {
      detail: {
        applyId: '',
        inquier: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
      },
      initApply: {},
      initPort: {},
      enquiryList: [],
      applyInfo: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
      },
      selectedEn: false,
      enquiryDialog: false,
      engine: {},
      quoteList: [],
      quoteIds: [],
      history: [],
      selectedQuote: false,
      hisDialog: false,
      hisId: '',
      searchObj: { id: '' },
      soilTypes: [],
      quoteLoading: false,
    }
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    idIsNew() {
      return this.$route.params.id == 'new'
    },
    canAddItem() {
      return (
        !this.isEdit ||
        ['未提交', '审批已驳回'].includes(this.detail.businessStatus) ||
        this.detail.status === '4'
      )
    },
    canBid() {
      return (
        (['超期', '报价完成'].includes(this.detail.businessStatus) &&
          this.detail.status === '1') ||
        this.detail.status === '4'
      )
    },
    greaseOil() {
      return this.soilTypes.find(
        (item) => item.value === this.applyInfo.greaseType || 0,
      )?.label
    },
  },
  watch: {
    'detail.shipCode'(_, oldVal) {
      if (!oldVal) return
      this.clearApply()
    },
    'detail.applyId': {
      handler(val, oldVal) {
        this.loadApplyInfo(val)
        if (!oldVal) return
        this.clearComponents()
      },
      immediate: true,
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },

    async getHis(item) {
      this.searchObj.id = item.itemId
      // console.log(this.searchObj.id)
      await this.$nextTick()
      this.hisDialog = true
      await this.$nextTick()
      this.searchObj.id = item.itemId
      this.$refs.table.ship = this.detail.shipCode
    },

    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.detail.remark.length == 0) {
        this.$dialog.message.error('请填写项目说明！')
        return
      }
      if (this.enquiryList.length === 0) {
        this.$dialog.message.error('询价明细不能为空')
        return
      }
      const detailList = this.getCompWithOperation()
      if (!this.isEdit) {
        const purchaseEnquirySubmitInputDTO = await this.$refs.enquiry.save()
        if (!purchaseEnquirySubmitInputDTO) return
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/purchaseManage/purchaseEnquirySubmit',
          {
            purchaseEnquiryModifyDTO: {
              ...this.detail,
              enquiryType,
              detailList,
            },
            purchaseEnquirySubmitInputDTO,
          },
        )
        if (!errorRaw) {
          this.closeAndTo(this.backRouteName)
        }
      } else {
        const { errorRaw } = await this.postAsync(
          '/business/shipAffairs/purchaseManage/purchaseEnquirySaveOrUpdate',
          { ...this.detail, enquiryType, detailList },
        )
        if (notMove) return errorRaw
        if (!errorRaw) {
          this.goBack()
        }
      }
    },
    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const errorRaw = await this.save(goBack, true)
      if (errorRaw) return
      const error = await this.$refs.audit.submit()
      if (!error) goBack()
    },

    async loadDetail() {
      if (!this.isEdit) {
        return
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryDetailById',
        { enquiryId: this.$route.params.id },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
      this.enquiryList = data.detailList.map((i) => {
        return {
          ...i,
          nameEn: i.greaseBaseInfo.nameEn,
          code: i.greaseBaseInfo.code,
          nameCn: i.greaseBaseInfo.nameCn,
          unit: i.greaseBaseInfo.unit,
          stockQuantity: i.inventoryNumber,
          requireQuantity: i.applyNum,
        }
      })
      this.initPort = {
        portCn: data.portName,
        id: data.receivePortId,
      }
      this.initApply = {
        applicationNo: data.applicationNo,
        id: data.applyId,
      }
      if (this.businessStatus !== '未提交') {
        await this.loadQuote()
      }
    },

    async loadApplyInfo(applyId) {
      if (!applyId) {
        this.applyInfo = {}
        return
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/greaseApplyDetailByIdEnquiry',
        { applyId },
      )
      this.applyInfo = data
      this.$refs.enquiry.suppliers = data.suppliers.map((i) => {
        const currencys = i.supplierBankListOutputDTO.map((i) => {
          return {
            currencyType: i.currencyType,
            ccyName: i.ccyCode,
          }
        })
        return {
          currencys,
          selectCurrency: {},
          ...i.supplierPurchaserOutputDTO,
          ...i.supplierOutputDTO,
        }
      })
      console.log(this.$refs.enquiry.suppliers)
      this.applyInfo.detailList = data.detailList.map((i) => {
        return {
          ...i,
          nameEn: i.greaseEnName,
          // TODO: 滑油号
          code: i.greaseNo,
          nameCn: i.greaseCnName,
          unit: i.greaseUnit,
          // TODO: 库存数量
          stockQuantity: i.inventoryNumber,
          requireQuantity: i.auditQuantity,
        }
      })
      //   this.applyList = data.detailList
      if (!this.isEdit) {
        this.initPort = {
          portCn: data.port,
          id: data.portId,
        }
        this.detail.receivePortId = data.portId
      }
      this.initApply = {
        applicationNo: data.applyCode,
        id: data.id,
      }
      this.detail.shipCode = data.shipInfo.shipCode
    },

    getCompWithOperation() {
      const ids = this.enquiryList.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.detailList
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.enquiryList.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },

    clearApply() {
      this.$dialog.message.info('由于船舶变更,自动清空所选申请单')
      this.detail.applyId = ''
    },
    clearComponents() {
      this.$dialog.message.info('由于申请单变更,自动清空所选滑油')
      this.applyInfo = {}
      this.enquiryList = []
      this.selectedEn = false
    },

    async loadQuote() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuotePage',
        { enquiryId: this.$route.params.id, isReBid: false },
      )
      this.quoteList = data.records
      this.quoteIds = data.records.map((i) => i.id)
    },

    async quoteBid() {
      if (
        !(await this.$dialog.msgbox.confirm(
          `确定 ${this.selectedQuote.supplierName} 中标`,
        ))
      )
        return
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidOpen',
        { enquiryId: this.$route.params.id, quoteId: this.selectedQuote.id },
      )
      this.quoteLoading = false
      if (!errorRaw) await this.loadDetail()
    },

    async loadSoilType() {
      const types = await this.getDictByType('ship_grease_info_type')
      this.soilTypes = types.map((i) => ({
        label: i.dictLabel,
        value: i.dictValue,
      }))
    },
    refresh(value) {
      if (value) {
        this.loadDetail()
      }
    },
  },

  mounted() {
    this.loadDetail()
    this.loadSoilType()
  },
}
</script>

<style></style>
