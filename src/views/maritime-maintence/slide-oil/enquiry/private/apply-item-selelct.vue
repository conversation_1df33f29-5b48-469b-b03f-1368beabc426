<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1100"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        询价滑油
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-data-table
          show-select
          :headers="headers"
          :items="items"
          item-key="greaseId"
          hide-default-footer
          disable-pagination
          v-model="selected"
          @click:row="selectRow"
          dense
          class="use-divider"
        >
          <template v-slot:[`item.type`]="{ item }">
            <span v-if="item.type == '1581991857030852611'">主机系统油</span>
            <span v-if="item.type == '1581991857047629825'">副机系统油</span>
            <span v-if="item.type == '1581991857022464003'">主机气缸油</span>
            <span v-if="item.type == '1649340717607944194'">其他小品种油</span>
          </template>
        </v-data-table>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn
          depressed
          color="primary"
          :disabled="selected.length === 0"
          @click="confirm"
        >
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'apply-item-selelct',
  model: {
    prop: 'open',
    event: 'update',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.headers = [
      { text: '滑油类型', value: 'type' },
      { text: '英文名称', value: 'nameEn' },
      { text: '滑油号', value: 'greaseNo' },
      { text: '中文名称', value: 'nameCn' },
      { text: '单位', value: 'unit' },
      { text: '库存数量', value: 'stockQuantity' },
      { text: '审批数量', value: 'auditQuantity' },
      { text: '询价数量', value: 'enquiryNum' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    items: Array,
    sitems: Array,
  },
  data() {
    return {
      dialog: false,
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$nextTick(() => {
        this.selected = this.sitems.map((i) => {
          return { ...i, vid: i.id, greaseId: i.itemId, remarkk: i.remark }
        })
      })
    },
  },
  methods: {
    closeForm() {
      this.$emit('update', false)
    },
    async confirm() {
      this.$emit(
        'update:sitems',
        this.selected.map((i) => {
          return {
            ...i,
            id: i.vid,
            itemId: i.greaseId,
            enquiryNum: i.enquiryNum || i.auditQuantity,
            remark: i.remarkk || '',
          }
        }),
      )
      this.$emit('update', false)
    },
    selectRow(_, { isSelected, item }) {
      this.selected = isSelected
        ? this.selected.filter((i) => i.id !== item.id)
        : [...this.selected, item]
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
