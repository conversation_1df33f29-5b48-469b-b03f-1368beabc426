<template>
  <v-card>
    <v-card-title>比价详情</v-card-title>
    <v-divider></v-divider>
    <v-simple-table class="use-divider">
      <template v-slot:default>
        <thead>
          <tr>
            <th colspan="4" class="text-left">采购订单</th>
            <th
              v-for="sup of quotes"
              :key="sup.id"
              class="text-left"
              colspan="5"
            >
              {{ sup.supplierName }}
              <v-btn
                v-if="sup.businessStatus == '已填报'"
                width="90"
                tile
                color="error"
                small
                class="mx-1"
                :loading="quoteLoading"
                @click="reBid(sup)"
                v-permission="['滑油询价:重新报价']"
              >
                重新报价
              </v-btn>
              <v-btn
                v-if="canBid"
                tile
                color="primary"
                small
                class="mx-1"
                @click="quoteBid(sup)"
                :loading="quoteLoading"
                v-permission="['滑油报价详情:定标']"
              >
                <v-icon left>mdi-check</v-icon>
                定标
              </v-btn>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colspan="4">是否中标</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id">
              {{ sup.isWin ? '是' : '否' }}
            </td>
          </tr>
          <tr>
            <td colspan="4">备货天数</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id">
              {{ sup.stockUpDays }}
            </td>
          </tr>
          <tr>
            <td colspan="4">币种</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id">
              {{ sup.ccyCode }}
            </td>
          </tr>
          <tr>
            <td colspan="4">运费</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id + '0'">
              {{ sup.otherExpenses }}
            </td>
          </tr>
          <tr>
            <td colspan="4">报价总价格(折后)</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id + '1'">
              {{ sup.total }}
            </td>
          </tr>
          <tr>
            <td colspan="4">报价总价格(折后美元)</td>
            <td colspan="5" v-for="sup of quotes" :key="sup.id + '2'">
              {{ sup.changeTotal }}
            </td>
          </tr>
          <tr>
            <td colspan="4">中标总价格</td>
            <td colspan="7" v-for="sup of quotes" :key="sup.id + '1'">
              {{ sup.total2 }}
            </td>
          </tr>
          <tr>
            <td colspan="4">中标总价格(折后美元)</td>
            <td colspan="7" v-for="sup of quotes" :key="sup.id + '2'">
              {{ sup.changeTotal2 }}
            </td>
          </tr>
          <tr>
            <th class="text-left">滑油英文名</th>
            <th class="text-left">滑油类型</th>
            <th class="text-left">滑油号</th>
            <th class="text-left">询价数量</th>
            <template v-for="sup of quotes">
              <th
                :key="'报价数量' + sup.id"
                class="text-left"
                style="white-space: nowrap"
              >
                报价数量
              </th>
              <th
                :key="'成交数量' + sup.id"
                class="text-left"
                style="white-space: nowrap"
              >
                成交数量
              </th>
              <th
                :key="'单价' + sup.id"
                class="text-left"
                style="white-space: nowrap"
              >
                单价
              </th>
              <th
                :key="'单价美元' + sup.id"
                class="text-left"
                style="white-space: nowrap"
              >
                单价美元
              </th>
              <!-- <th :key="'总价' + sup.id" class="text-left">总价(折后)</th>
              <th :key="'总价美元' + sup.id" class="text-left">折后美元</th> -->
              <th
                :key="'备注' + sup.id"
                class="text-left"
                style="white-space: nowrap"
              >
                备注
              </th>
            </template>
          </tr>
          <tr v-for="(sup, i) of quotes[0].detailList" :key="i">
            <template>
              <td :key="i + '滑油英文名'">
                {{ quotes[0].detailList[i].itemName }}
              </td>
              <td :key="i + '滑油类型'">
                <span
                  v-if="quotes[0].detailList[i].type == '1581991857030852611'"
                >
                  主机系统油
                </span>
                <span
                  v-if="quotes[0].detailList[i].type == '1581991857047629825'"
                >
                  副机系统油
                </span>
                <span
                  v-if="quotes[0].detailList[i].type == '1581991857022464003'"
                >
                  主机气缸油
                </span>
                <span
                  v-if="quotes[0].detailList[i].type == '1649340717607944194'"
                >
                  其他小品种油
                </span>
              </td>
              <td :key="i + '滑油号'">
                {{ quotes[0].detailList[i].itemNo }}
              </td>
              <td :key="i + '询价数量'">
                {{ quotes[0].detailList[i].enquiryNum }}
              </td>
              <template v-for="sup of quotes">
                <td :key="'报价数量' + sup.id">
                  {{ sup.detailList[i].finalNum }}
                </td>
                <td :key="'成交数量' + sup.id">
                  <v-text-field
                    class="shrink"
                    v-model="sup.detailList[i].quotNum"
                    @change="
                      () =>
                        (sup.detailList[i].quotNum =
                          sup.detailList[i].type == '1649340717607944194'
                            ? sup.detailList[i].quotNum
                            : Math.round(sup.detailList[i].quotNum))
                    "
                    :rules="
                      sup.detailList[i].type == '1649340717607944194'
                        ? [rules.decimal]
                        : []
                    "
                    label="成交数量"
                    type="number"
                    @input="updateFinalPrice"
                    :disabled="
                      businessStatus != '超期' &&
                      businessStatus != '报价完成' &&
                      businessStatus != '用户开标'
                    "
                    dense
                    single-line
                  ></v-text-field>
                </td>
                <td :key="'单价' + sup.id">
                  <!-- {{ sup.detailList[i].price }} -->
                  {{
                    Math.round(
                      sup.detailList[i].price *
                        sup.detailList[i].discount *
                        10000,
                    ) / (10000).toFixed(4)
                  }}
                </td>
                <td :key="'单价美元' + sup.id">
                  {{
                    Math.round(
                      sup.detailList[i].price *
                        sup.detailList[i].discount *
                        sup.rate *
                        10000,
                    ) / (10000).toFixed(4)
                  }}
                </td>
                <!-- <td :key="'总价' + sup.id">
                  {{ sup.detailList[i].price * sup.detailList[i].quotNum }}
                </td>
                <td :key="'总价美元' + sup.id">
                  折算:{{
                    Math.round(
                      sup.detailList[i].price *
                        sup.detailList[i].quotNum *
                        sup.rate *
                        100,
                    ) / (100).toFixed(2)
                  }}
                </td> -->
                <td :key="'备注' + sup.id">
                  {{ sup.detailList[i].remark }}
                </td>
              </template>
            </template>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
  </v-card>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'compare-price-table',
  mixins: [currencyHelper],
  props: {
    quoteIds: [],
    businessStatus: {
      type: String,
    },
    canBid: {
      type: Boolean,
    },
    winSupply: {},
  },
  data() {
    return {
      quotes: [{ equipmentInformationBaseInfo: {}, detailList: {} }],
      quoteLoading: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        aboveZero: (v) => v > 0 || '必须大于0',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
      },
    }
  },

  watch: {
    quoteIds(val) {
      if (val.length > 0) this.loadPrice()
    },
    businessStatus(val) {
      if (val == '报价完成' || val == '待商务主管定标' || val == '超期') {
        this.canEdit = false
      }
    },
  },

  methods: {
    async loadPrice() {
      let reqs = []
      for (const quoteId of this.quoteIds) {
        reqs.push(
          this.getAsync(
            '/business/shipAffairs/purchaseManage/purchaseQuoteDetailById',
            {
              quoteId,
            },
          ),
        )
      }
      const results = await Promise.all(reqs)
      await this.loadCurrencyInfo()
      this.quotes = results.map((i) => ({
        ...i.data,
        rate: this.currencyInfo.find((c) => c.id == i.data.currencyId)
          .rateToMain,
        total: (
          i.data.detailList.reduce(
            (x, { price, finalNum, discount }) =>
              x + Math.round(price * finalNum * discount * 100) / 100,
            0,
          ) + i.data.otherExpenses
        ).toFixed(2),
        changeTotal: (
          (i.data.detailList.reduce(
            (x, { price, finalNum, discount }) =>
              x + Math.round(price * finalNum * discount * 100) / 100,
            0,
          ) +
            i.data.otherExpenses) *
          this.currencyInfo.find((c) => c.id == i.data.currencyId).rateToMain
        ).toFixed(2),
        total2: (
          i.data.detailList.reduce(
            (x, { price, quotNum }) =>
              x + Math.round(price * quotNum * 100) / 100,
            0,
          ) + i.data.otherExpenses
        ).toFixed(2),
        changeTotal2: (
          (i.data.detailList.reduce(
            (x, { price, quotNum }) =>
              x + Math.round(price * quotNum * 100) / 100,
            0,
          ) +
            i.data.otherExpenses) *
          this.currencyInfo.find((c) => c.id == i.data.currencyId).rateToMain
        ).toFixed(2),
      }))
    },
    async reBid(sup) {
      console.log(sup)
      if (!(await this.$dialog.msgbox.confirm('确定重新报价吗？'))) return
      this.quoteLoading = true
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/reBid',
        {
          ...sup,
        },
      )
      this.quoteLoading = false
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('已发送邮箱提醒供应商重新报价！')
      this.$emit('refresh', true)
    },
    updateFinalPrice() {
      this.quotes = this.quotes.map((i) => ({
        ...i,
        rate: this.currencyInfo.find((c) => c.id == i.currencyId).rateToMain,
        total: (
          i.detailList.reduce(
            (x, { price, finalNum, discount }) =>
              x + Math.round(price * finalNum * discount * 100) / 100,
            0,
          ) + i.otherExpenses
        ).toFixed(2),
        changeTotal: (
          (i.detailList.reduce(
            (x, { price, finalNum, discount }) =>
              x + Math.round(price * finalNum * discount * 100) / 100,
            0,
          ) +
            i.otherExpenses) *
          this.currencyInfo.find((c) => c.id == i.currencyId).rateToMain
        ).toFixed(2),
        total2: (
          i.detailList.reduce(
            (x, { finalPrice, quotNum }) =>
              x + Math.round(finalPrice * quotNum * 100) / 100,
            0,
          ) + i.otherExpenses
        ).toFixed(2),
        changeTotal2: (
          (i.detailList.reduce(
            (x, { finalPrice, quotNum }) =>
              x + Math.round(finalPrice * quotNum * 100) / 100,
            0,
          ) +
            i.otherExpenses) *
          this.currencyInfo.find((c) => c.id == i.currencyId).rateToMain
        ).toFixed(2),
      }))
    },
    async quoteBid(sup) {
      console.log(sup)
      console.log(this.$route.params.id)

      // 增加保存子表数据
      this.updateDetail()

      if (!(await this.$dialog.msgbox.confirm(`确定 ${sup.supplierName} 中标`)))
        return
      this.quoteLoading = true
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidOpen',
        { enquiryId: this.$route.params.id, quoteId: sup.id },
      )
      this.quoteLoading = false
      if (!errorRaw) this.$emit('refresh', true)
    },

    async updateDetail() {
      const quotess = []
      this.quotes.forEach((item) => {
        item.detailList.forEach((detail) => {
          // if (detail.isWins) {
          quotess.push(detail)
          // }
        })
      })
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseEnquiryBidUpdateIsWins',
        quotess,
      )
      this.quoteLoading = false
      if (errorRaw) return
    },
  },

  mounted() {},
}
</script>

<style></style>
