<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        滑油选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          :search-dicts="searchDicts"
        >
          <template #searchflieds></template>
          <template #btns></template>
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo.chShipName }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'soil-stock-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl =
      '/business/shipAffairs/purchaseManage/purchaseStocksMsgByParams'
    this.headers = [
      { text: '滑油名', value: 'itemName' },
      { text: '滑油号', value: 'itemNo' },
      { text: '船舱', value: 'depositoryName' },
      { text: '库存数量', value: 'itemNumber' },
    ]
    this.fuzzyLabel = '模糊查询'
    this.searchDicts = [
      {
        dicType: 'ship_grease_info_type',
        label: '滑油类型',
        key: 'greaseType',
      },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: String,
    components: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {
        stocksType: '2',
      },
      selected: [],
      firstEquipments: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    // 'searchObj.equipmentSecondId': {
    //   handler(val) {
    //     if (val) this.loadSubEqu()
    //   },
    // },
    // 'searchObj.equipmentInformationId': {
    //   handler(val) {
    //     if (val) this.loadSecondEquipment()
    //   },
    // },
    searchRemain(val) {
      this.searchObj = val
    },
    components(val) {
      this.selected = val.map((i) => {
        return {
          ...i,
          vid: i.id,
          id: i.depositoryId + i.itemId,
          itemNo: i.itemNumber,
          itemNumber: i.inventoryNumber,
        }
      })
    },
    shipCode(val) {
      if (val) this.searchObj.shipCode = val
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    // 子设备获取
    async loadSubEqu() {
      this.loading = true
      const reqUrl = this.searchObj.equipmentSecondId
        ? '/business/shipAffairs/equipmentInformation/getEquipmentThridBySecondId'
        : '/business/shipAffairs/equipmentInformation/getEquipmentThirdByMainEquipmentId'
      const { data } = await this.getAsync(reqUrl, {
        equipmentId: this.searchObj?.equipmentId,
        secondEquipmentId: this.searchObj?.equipmentSecondId,
      })
      this.subEquipments = data?.map((i) => {
        return {
          text: i.subEquipmentCname,
          value: i.id,
        }
      })
      this.loading = false
    },
    // 大模块
    async loadSecondEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentSecondByMainEquipmentId',
        { MainEquipmentId: this.searchObj.equipmentInformationId },
      )
      this.secondEquipments = data?.map((i) => {
        return {
          text: i.name,
          value: i.id,
        }
      })
    },
    // 设备主体
    async loadFirstEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { current: 1, size: 99, shipCode: this.shipCode },
      )
      const { records } = data
      this.firstEquipments = records?.map((i) => {
        return {
          text: i.equipmentCname,
          value: i.id,
        }
      })
    },
    confirm() {
      const components = this.selected.map((i) => {
        const comp = {
          ...i,
          cid: i.id,
          id: i.vid,
          itemNumber: i.itemNo,
          inventoryNumber: i.itemNumber,
          max: (a) => i.itemNumber >= a || '出库数量不得超过最大库存量',
        }
        return comp
      })
      console.log(components)
      this.$emit('update:components', components)
      this.$emit('change', false)
    },
  },
  mounted() {
    this.loadFirstEquipment()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
