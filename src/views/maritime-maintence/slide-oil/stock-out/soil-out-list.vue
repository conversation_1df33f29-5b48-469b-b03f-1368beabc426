<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-alert type="info" color="green" text dense class="mb-0">
          月末最后一天自动生成滑油消耗草稿单，请填写相关内容并及时提交。
        </v-alert>
      </v-col>
    </v-row>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="isShip ? headersShip : headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :search-remain="searchObj"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="status2"
            :items="statusMap"
            label="审批状态"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-text-field
            label="出库单号"
            outlined
            dense
            clearable
            v-model="searchObj.inoutCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="selected.businessStatus !== '审批通过，生成预算数据失败'"
          @click="jwConfirm"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['滑油消耗:主管确认']"
        >
          <v-icon left>mdi-account-alert-outline</v-icon>
          主管确认（预算）
        </v-btn>
        <v-btn
          :to="{
            name: 'soil-out-detail',
            params: { id: 'new', stockOutType: 7 },
          }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['滑油消耗:新增自由出库']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增自由出库
        </v-btn>
        <v-btn
          :disabled="
            (selected.status !== '1' && selected.status !== '4') ||
            selected.inoutMode == 10
          "
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['滑油消耗:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.inoutMode`]="{ item }">
        {{
          [
            '订单入库',
            '调减入库',
            '期初入库',
            '维护保养消耗',
            '自修消耗',
            '航修消耗',
            '坞修消耗',
            '自由出库',
            '盘盈入库',
            '盘亏出库',
            '月末出库',
          ][item.inoutMode]
        }}
      </template>
      <template v-slot:[`item.usd`]>美元</template>
      <template v-slot:[`item.status`]="{ item }">
        <v-chip small :color="statusColors[item.status]" :dark="true">
          {{ statuses2[item.status] }}
        </v-chip>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'soil-out-list',
  created() {
    this.tableName = '滑油消耗'
    this.reqUrl = '/business/shipAffairs/purchaseManage/purchaseStockInOutPage'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '创建日期', value: 'createTime' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '币种', value: 'usd' },
      { text: '涉及金额', value: 'amountInvolved' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '出库日期', value: 'inoutDate' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
      { text: '备注', value: 'remark' },
    ]
    this.statusMap = [
      { text: '草稿', value: '1' },
      { text: '审批中', value: '2' },
      { text: '已审批', value: '3' },
      { text: '已驳回', value: '4' },
    ]
    this.statuses2 = ['暂无审批', '草稿', '审批中', '已审批', '已驳回', '废弃']
    this.statusColors = ['info', '', 'warning', 'success', 'error', 'error']
    // this.searchObj = { inoutNature: 2, inoutType: 1 }
    this.pushParams = { name: 'soil-out-detail' }
  },

  data() {
    return {
      selected: false,
      status2: '2',
      searchObj: { inoutNature: 2, inoutType: 1, isMe: true, status: 2 },
    }
  },
  watch: {
    status2(val) {
      this.searchObj.status = val
      this.$refs.table.loadTableData()
    },
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/stocksInOutDelete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async jwConfirm() {
      const { errorRaw, data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/stocksInOutGenBudget',
        { id: this.selected.id },
        false,
      )
      console.log(data)
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
        return
      }
      this.$dialog.message.success(`预算数据生成成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {
    if (this.$route.query.status != undefined) {
      this.status2 = this.$route.query.status
    }
  },
}
</script>

<style></style>
