<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['滑油消耗:编辑']"
      :title="`滑油消耗-${detail.inoutCode || '新增'}`"
      :tooltip="detail.inoutCode || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="canSubmit"
      @save="save"
      @submit="submit"
      :can-save="canEdit"
    >
      <template
        v-if="detail.auditParams && detail.auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-row>
          <v-col cols="12">
            <v-alert type="info" color="green" text dense class="mb-0">
              为保证数据准确性，请填写每月最后一天中午12:00的滑油消耗数据。
            </v-alert>
          </v-col>
        </v-row>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="!!detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <!-- TODO:
              根据用户信息读取用户所在部门
               -->
              <v-col cols="12" md="3">
                <v-ship-dept
                  outlined
                  dense
                  label="申请部门"
                  :disabled="!detail.shipCode"
                  :readonly="isEdit"
                  v-model="detail.deptName"
                  :rules="[rules.required]"
                  :items="['甲板部', '轮机部']"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  outlined
                  dense
                  :readonly="isEdit"
                  label="物品金额（美元）"
                  :value="
                    detail.status == '3'
                      ? detail.amountInvolved
                      : '审批未通过不展示'
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.handler"
                  use-current
                  :init-user="initUser"
                  :rules="[rules.required]"
                  disabled
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  outlined
                  dense
                  label="出库类型"
                  disabled
                  v-model="detail.stockOutType"
                  :rules="[rules.required]"
                  :items="出库类型"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="主机滑油循环柜存油(L)"
                  :readonly="isEdit1"
                  v-model="detail.meloc"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="气缸油注油率(g/kwh)(L)"
                  :readonly="isEdit1"
                  v-model="detail.cylFillingRate"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3"></v-col>
              <v-col cols="12" md="3" v-if="isShip"></v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="主机运行总时间"
                  :readonly="isEdit1"
                  v-model="detail.meRunningTime"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="No.1副机运行总时间(h)"
                  :readonly="isEdit1"
                  v-model="detail.aeRunningTime1"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="No.2副机运行总时间(h)"
                  :readonly="isEdit1"
                  v-model="detail.aeRunningTime2"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  outlined
                  dense
                  label="No.3副机运行总时间(h)"
                  :readonly="isEdit1"
                  v-model="detail.aeRunningTime3"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="detail.meRunningTimeThis">
                <v-text-field
                  outlined
                  dense
                  label="本次消耗主机运行时长(h)"
                  :readonly="detail.status != 2 && detail.status != 4"
                  v-model="detail.meRunningTimeThis"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="detail.aeRunningTime1This">
                <v-text-field
                  outlined
                  dense
                  label="本次消耗NO.1副机运行时长(h)"
                  :readonly="detail.status != 2 && detail.status != 4"
                  v-model="detail.aeRunningTime1This"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="detail.aeRunningTime2This">
                <v-text-field
                  outlined
                  dense
                  label="本次消耗NO.2副机运行时长(h)"
                  :readonly="detail.status != 2 && detail.status != 4"
                  v-model="detail.aeRunningTime2This"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3" v-if="detail.aeRunningTime3This">
                <v-text-field
                  outlined
                  dense
                  label="本次消耗NO.3副机运行时长(h)"
                  :readonly="detail.status != 2 && detail.status != 4"
                  v-model="detail.aeRunningTime3This"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-btn
                v-if="detail.meRunningTimeThis"
                width="130"
                tile
                @click="saveThisTimes"
                color="success"
                small
                class="mx-1"
                v-permission="['滑油消耗:保存运行时长']"
              >
                保存本次运行时长
              </v-btn>
              <v-col cols="12">
                <v-textarea
                  :readonly="!canEdit"
                  outlined
                  dense
                  label="备注"
                  v-model="detail.remark"
                  :rules="[detail.stockOutType == 7 ? rules.required : true]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template v-if="canEdit" #出库明细按钮>
        <v-btn
          :disabled="!detail.shipCode"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="dialog = true"
          v-permission="['滑油出库明细:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择
        </v-btn>
        <v-btn
          :disabled="!selected"
          small
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #出库明细>
        <v-form ref="form2">
          <v-table-list
            v-model="selected"
            :headers="isEdit1 ? detailHeaders1 : detailHeaders"
            :items="items"
            item-key="cid"
          >
            <template v-slot:[`item.type`]="{ item }">
              <span v-if="item.type == '1581991857030852611'">主机系统油</span>
              <span v-if="item.type == '1581991857047629825'">副机系统油</span>
              <span v-if="item.type == '1581991857022464003'">主机气缸油</span>
              <span v-if="item.type == '1649340717607944194'">
                其他小品种油
              </span>
            </template>
            <template
              v-if="canEdit"
              v-slot:[`item.stocksInOutNumber`]="{ item }"
            >
              <v-text-field
                label="出库数量"
                v-model="item.stocksInOutNumber"
                single-line
                dense
                :rules="
                  item.componentProperty == '其他小品种油'
                    ? [rules.decimal, item.max]
                    : [rules.int, item.max]
                "
              ></v-text-field>
            </template>
            <template v-slot:[`item.PreInventoryNumber`]="{ item }">
              <span>
                {{ item.inventoryNumberAfter + item.stocksInOutNumber }}
              </span>
            </template>
            <template v-slot:[`item.inventoryNumberAfter`]="{ item }">
              <span>
                {{ item.inventoryNumberAfter + 0 }}
              </span>
            </template>
          </v-table-list>
        </v-form>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="(ids) => (detail.attachmentIds = ids)"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template v-if="false" #其他油柜存量按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createTank"
          v-permission="['油柜存量:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!oilTank"
          outlined
          tile
          small
          color="error"
          class="mx-1"
          @click="delTank"
          v-permission="['油柜存量:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #其他油柜存量>
        <v-table-list
          :show-select="canDdd"
          v-model="oilTank"
          :headers="headersBox"
          :items="detail.oilTankList"
          item-key="id"
        >
          <template v-slot:[`item.type`]="{ item }">
            <span v-if="item.type == '1581991857030852611'">主机系统油</span>
            <span v-if="item.type == '1581991857047629825'">副机系统油</span>
            <span v-if="item.type == '1581991857022464003'">主机气缸油</span>
            <span v-if="item.type == '1649340717607944194'">其他小品种油</span>
          </template>
          <template v-slot:[`item.stockNums`]="{ item }" v-if="canDdd">
            <v-text-field
              v-model="item.stockNums"
              outlined
              dense
              type="number"
            ></v-text-field>
          </template>
          <template v-if="canDdd" v-slot:[`item.remark`]="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  @click="editRemarkTank(item)"
                  v-bind="attrs"
                  v-on="on"
                  v-model="item.remark"
                  dense
                  outlined
                  single-line
                ></v-text-field>
              </template>
              <span>{{ item.remark }}</span>
            </v-tooltip>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <v-dialog v-model="dialog12" max-width="600">
      <template v-slot:default="dialog12">
        <v-card style="height: 300px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemarkTank"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog12.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails12.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <soil-stock-select
      v-model="dialog"
      :components.sync="items"
      :shipCode="detail.shipCode"
    ></soil-stock-select>
    <addOilTanks
      v-model="dialog1"
      @success="success"
      :initial-data="initialData"
      :shipCode="detail.shipCode"
    ></addOilTanks>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import soilStockSelect from './private/soil-stock-select.vue'
import addOilTanks from '@/views/maritime-maintence/system-reports/grease-consum/addOilTanks.vue'
export default {
  components: { addOilTanks, soilStockSelect },
  name: 'soil-out-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'soil-out-list'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.subtitles = ['基本信息', '出库明细', '其他油柜存量']
    // TODO:其他出库方式
    this.businessTypes = [
      '',
      '',
      '',
      'monthplan-detail',
      'self-repair-detail',
      'voyage-complete-detail',
      'dock-complete-detail',
    ]
    this.出库类型 = [
      { text: '维护保养消耗', value: 3 },
      { text: '自修消耗', value: 4 },
      { text: '航修消耗', value: 5 },
      { text: '坞修消耗', value: 6 },
      { text: '自由出库', value: 7 },
      { text: '月末出库', value: 10 },
    ]
    this.detailHeaders = [
      { text: '滑油名', value: 'itemName' },
      { text: '滑油号', value: 'itemNumber' },
      { text: '滑油类型', value: 'type' },
      { text: '滑油SAPCODE', value: 'code' },
      { text: '所在库位', value: 'depositoryName' },
      { text: '库存数量', value: 'inventoryNumber' },
      { text: '出库数量', value: 'stocksInOutNumber', width: 200 },
      { text: '单位', value: 'unit' },
    ]
    this.detailHeaders1 = [
      { text: '滑油类别', value: 'type' },
      { text: '滑油牌号', value: 'itemName' },
      //{ text: '滑油号', value: 'itemNumber' },
      { text: '滑油SAPCODE', value: 'code' },
      { text: '所在库位', value: 'depositoryName' },
      { text: '本次消耗前库存', value: 'PreInventoryNumber' },
      { text: '加装量', value: 'addNumber' },
      { text: '本次消耗', value: 'stocksInOutNumber', width: 200 },
      { text: '本次消耗后库存', value: 'inventoryNumberAfter' },
      { text: '单位', value: 'unit' },
    ]
    this.headersBox = [
      { text: '油柜名称', value: 'tankName' },
      { text: '滑油类别', value: 'type' },
      { text: '滑油牌号', value: 'brand' },
      { text: '存量', value: 'stockNums' },
      { text: '备注', value: 'remark' },
    ]
    this.outParam = this.$store.state.outParams.outParams.find(
      (item) => item.itemType === 'soil-out-detail',
    )
    this.detail.stockOutType = this.outParam
      ? this.businessTypes.indexOf(this.outParam.businessType)
      : 7
    if (this.$route.params.stockOutType == 7) {
      this.detail.stockOutType = 7
    }
    Object.freeze(this.outParam)
    // this.$store.state.outParams.outParams = []
  },
  data() {
    return {
      detail: {
        oilTankList: [],
        detailList: [],
        inoutNature: 2,
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        int: (v) => /^\d+?$/.test(v) || '请输入整数数字',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
      },
      dialog: false,
      dialog1: false,
      dialog12: false,
      oilTank: false,
      items: [],
      initUser: false,
      selected: false,
      initialData: {},
    }
  },

  computed: {
    canDdd() {
      return (
        this.$route.params.id == 'new' ||
        this.detail.status == '1' ||
        this.detail.status == '4'
      )
    },
    isEdit() {
      return this.$route.params.id !== 'new' && this.detail.status != 1
    },
    isEdit1() {
      return (
        this.$route.params.id !== 'new' &&
        this.detail.status != 1 &&
        this.detail.status != 4
      )
    },
    isNew() {
      return this.$route.params.id == 'new' || this.detail.status == 1
    },
    canAddItem() {
      return (
        !this.isEdit ||
        ['未提交', '审批已驳回'].includes(this.detail.businessStatus) ||
        this.detail.status === '4'
      )
    },
    canEdit() {
      return this.detail.status != 2 && this.detail.status != 3
    },
    canSubmit() {
      return this.detail.status != 3
    },
  },

  methods: {
    async saveThisTimes() {
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/updateSoilRunningTimes',
        {
          meRunningTimeThis: this.detail.meRunningTimeThis,
          aeRunningTime1This: this.detail.aeRunningTime1This,
          aeRunningTime2This: this.detail.aeRunningTime2This,
          aeRunningTime3This: this.detail.aeRunningTime3This,
          id: this.detail.id,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success(
          '本次运行时间保存成功,页面即将自动刷新请稍后',
        )
        this.loadDetail()
      }
    },
    editRemarkTank(item) {
      this.editRemarkDetails12 = item
      this.dialog12 = true
    },
    saveRemarkTank() {
      this.dialog12 = false
    },
    createTank() {
      this.initialData = {}
      this.dialog1 = true
    },
    editTank() {
      this.initialData = this.oilTank
      this.dialog1 = true
    },
    delTank() {
      this.detail.oilTankList = this.detail.oilTankList.filter(
        (s) => !(s.id === this.oilTank.id),
      )
    },
    success(newOil) {
      //console.log('newOil', newOil)
      if (
        this.detail.oilTankList.some(
          (s) => s.greaseId === newOil.greaseId && s.tankId === newOil.tankId,
        )
      ) {
        this.$dialog.message.error('油柜及滑油牌号重复重复')
        return
      }
      this.detail.oilTankList.push(newOil)
      //console.log('this.detail.oilTankList', this.detail.oilTankList)
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return false
      if (!this.$refs.form2.validate()) return false
      const newItems = this.items
        .filter((i) => !this.detail.detailList.find((j) => i.id === j.id))
        .map((i) => ({
          ...i,
          outNumber: i.stocksInOutNumber,
          operationType: 1,
        }))
      const remainItems = this.items
        .filter((i) => this.detail.detailList.find((j) => i.id === j.id))
        .map((i) => ({
          ...i,
          outNumber: i.stocksInOutNumber,
          operationType: 2,
        }))
      const delItems = this.detail.detailList
        .filter((i) => !this.items.find((j) => i.id === j.id))
        .map((i) => ({ ...i, operationType: 3 }))
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/saveOrUpdatePurchaseStockOutOfComponent',
        {
          ...this.detail,
          inoutNature: 2,
          stockOutType: this.detail.stockOutType ?? this.detail.inoutMode,
          stockOutDetailModifyList: [...remainItems, ...newItems, ...delItems],
        },
      )
      if (notMove) return data
      if (!errorRaw) {
        if (this.outParam) {
          // console.log(this.outParam)
          this.$store.commit('setOutId', {
            ...this.outParam,
            outId: data,
          })
          this.closeAndTo(this.outParam.businessType, {
            id: this.outParam.businessId,
          })
        } else {
          goBack()
        }
      }
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/purchaseManage/submitPurchaseStockOutOfComponent',
          { id: data },
        )
        if (
          errorRaw?.msg ===
          '存在审批中的入库记录，滑油消耗单草稿已保存，请等待入库单批完后再提交滑油出库单！'
        ) {
          goBack()
        } else if (errorRaw) return
      } else {
        if (this.detail.status == 4) {
          // 驳回状态提交重新启动流程，校验是否重复出库
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/purchaseManage/submitPurchaseStockOutOfComponent',
            { id: data },
          )
          if (errorRaw) return
        } else {
          // 最后一个人审批时候判断是否超年度预算this.$refs.audit.adopt审批通过时候
          if (
            this.detail.businessStatus == '机务监督主管' &&
            this.$refs.audit.adopt
          ) {
            const flag = await this.checkBudgetYear()
            console.log('11111111111111:' + flag)
            if (flag) {
              // console.log('11111111111111:' + flag)
              const error = await this.$refs.audit.submit()
              // if (!error) goBack()
              if (!error) {
                const { data } = await this.getAsync(
                  '/business/shipAffairs/purchaseManage/purchaseStockInoutById2',
                  { id: this.$route.params.id },
                )
                console.log(data)
                goBack()
              }
            }
          } else {
            const error = await this.$refs.audit.submit()
            // if (!error) goBack()
            if (!error) {
              const { data } = await this.getAsync(
                '/business/shipAffairs/purchaseManage/purchaseStockInoutById2',
                { id: this.$route.params.id },
              )
              console.log(data)
              goBack()
            }
          }

          // const error = await this.$refs.audit.submit()
          // if (error) return
        }
      }
      if (this.outParam) {
        this.$store.commit('setOutId', {
          ...this.outParam,
          outId: data,
        })
        this.closeAndTo(this.outParam.businessType, {
          id: this.outParam.businessId,
        })
      } else {
        goBack()
      }
    },
    async checkBudgetYear() {
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/checkAndGenBudgetNew',
        {
          // date: this.detail.happenDate,
          // shipCode: this.detail.shipCode,
          // subjectId: this.detail.initSubject.id,
          // money: this.detail.money,
          // type: '2', //生成费用项目
          budgetId: this.detail.id,
        },
      )
      if (errorRaw) {
        return false
      }
      if (data) {
        //不超预算 可以提交
        return true
      }
      // this.$dialog.message.warning('当前')
    },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseStockInoutById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.stockOutType = data.inoutMode
      if (this.detail.status == 1) {
        const userInfo = this.$local.data.get('userInfo')
        this.initHandler = {
          id: userInfo.id,
          nickName: userInfo.nickName,
        }
        this.detail.handler = userInfo.id
      } else
        this.initHandler = { id: data.handlerId, nickName: data.handlerName }
      this.items = data.detailList.map((i) => ({
        ...i,
        max: (a) => i.inventoryNumber >= a || '出库数量不得超过最大库存量',
      }))
      this.detail.shipCode = data.shipInfo.shipCode
    },
    async del() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.items = this.items.filter((i) => i.cid !== this.selected.cid)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
