<template>
  <v-container fluid>
    <v-detail-view
      title="滑油订单-新增"
      tooltip="新增"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      v-permission="['滑油订单:新增']"
    >
      <template #订单信息>
        <v-form ref="form">
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                md="3"
                class="py-0"
                v-for="(h, i) in tableFeilds"
                :key="i"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfoDO'"
                  v-model="detail.shipCode"
                ></v-ship-select>
                <v-switch
                  v-else-if="h.value === 'priceIncTax'"
                  class="mt-1"
                  :label="detail.priceIncTax ? '含税' : '不含税'"
                  v-model="detail[h.value]"
                  dense
                ></v-switch>
                <v-supply-select
                  v-else-if="h.value === 'supplyId'"
                  :disabled="!detail.shipCode"
                  v-model="detail.supplyId"
                  :rules="[rules.required]"
                  :ship-code="detail.shipCode"
                  @select="
                    (item) => {
                      currencys = item.currency
                    }
                  "
                ></v-supply-select>
                <v-select
                  v-else-if="h.value === 'currencyName'"
                  v-model="detail.currencyId"
                  :items="currencys"
                  item-text="ccyCode"
                  item-value="currencyType"
                  label="币种"
                  outlined
                  dense
                  :rules="[rules.required]"
                  required
                  :disabled="currencys.length === 0"
                ></v-select>
                <v-text-field
                  v-else
                  outlined
                  dense
                  v-model="detail[h.value]"
                  :label="h.text"
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12" class="py-0">
                <v-file-input
                  outlined
                  dense
                  accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
                  label="导入EXCEL"
                  :rules="[rules.required]"
                  v-model="file"
                ></v-file-input>
              </v-col>
              <v-col md="3" cols="12" class="py-0">
                <v-handler v-model="detail.applicantId" use-current></v-handler>
              </v-col>
              <v-col md="3" cols="12" class="py-0">
                <port-select-dialog
                  v-model="detail.portId"
                  :rules="[rules.required]"
                ></port-select-dialog>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import portSelectDialog from '../../components/port-select-dialog.vue'
export default {
  components: { portSelectDialog },
  name: 'soil-order-new',
  mixins: [currencyHelper],
  created() {
    this.backRouteName = 'soil-order-list'
    this.subtitles = ['订单信息']
    this.tableFeilds = [
      { text: '船舶', value: 'shipInfoDO' },
      { text: '供应商', value: 'supplyId' },
      { text: '币种', value: 'currencyName' },
      { text: '其他费用', value: 'otherExpenses' },
      { text: '是否含税', value: 'priceIncTax' },
      { text: '发票税率', value: 'taxRate' },
      { text: '税费', value: 'tax' },
      { text: '备货天数', value: 'stockUpDays' },
    ]
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
  },
  data() {
    return {
      detail: {},
      file: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      currencys: [],
    }
  },

  methods: {
    async save(goBack) {
      if (!this.$refs.form.validate()) return
      let formData = new FormData()
      formData.append('file', this.file, false)
      formData.append('itemType', '03')
      if (this.detail) {
        Object.keys(this.detail).forEach((key) => {
          formData.append(key, this.detail[key])
        })
      }
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/importOrderByExcel',
        formData,
      )
      if (!errorRaw) goBack()
    },
  },

  mounted() {},
}
</script>

<style></style>
