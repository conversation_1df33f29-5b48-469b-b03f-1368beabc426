<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1100"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        订单入库
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          use-ship
        >
          <template #searchflieds></template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn
          depressed
          color="primary"
          :disabled="selected.length === 0"
          @click="confirm"
        >
          确认入库
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'order-select-dialog',
  model: {
    prop: 'open',
    event: 'update',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '订单号', value: 'orderNo' },
      { text: '申请单号', value: 'applyNo' },
      { text: '创建日期', value: 'createTime' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '入库完成日期', value: 'completeTime' },
      { text: '是否坞修', value: 'isDockRepair' },
      { text: '状态', value: 'businessStatus' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    items: Array,
    sitems: Array,
  },
  data() {
    return {
      dialog: false,
      selected: [],
      searchObj: { orderType: '03', businessStatus: 1002 },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$nextTick(() => {
        this.selected = this.sitems.map((i) => {
          return { ...i, vid: i.id, componentId: i.itemId }
        })
      })
    },
  },
  methods: {
    closeForm() {
      this.$emit('update', false)
    },
    async confirm() {
      this.$emit(
        'update:sitems',
        this.selected.map((i) => {
          return {
            ...i,
            id: i.vid,
            itemId: i.componentId,
            enquiryNum: i.enquiryNum || i.requireQuantity,
          }
        }),
      )
      this.$emit('update', false)
    },
    selectRow(_, { isSelected, item }) {
      this.selected = isSelected
        ? this.selected.filter((i) => i.id !== item.id)
        : [...this.selected, item]
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
