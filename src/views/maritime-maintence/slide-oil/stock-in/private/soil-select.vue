<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        滑油选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          item-key="greaseId"
          fuzzy-label="英文名/滑油号/厂家"
        >
          <template #searchflieds></template>
          <template #btns></template>
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo.chShipName }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'soil-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/business/shipAffairs/greaseDistribution/pageByParams'
    this.searchDicts = [
      {
        dicType: 'ship_grease_info_type',
        label: '滑油类型',
        key: 'greaseType',
      },
    ]
    this.headers = [
      { text: '滑油编号', value: 'code' },
      { text: '英文名', value: 'nameEn' },
      { text: '厂家', value: 'factory' },
      { text: '规格', value: 'specs' },
      { text: '单位', value: 'unit' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: String,
    hasPrice: Boolean,
    soils: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      loading: false,
      searchObj: { shipCode: '', type: '1649340717607944194' }, // 期初入库只允许小品种油
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    searchRemain(val) {
      this.searchObj = val
    },
    soils(val) {
      this.selected = val.map((i) => {
        return { ...i, vid: i.id }
      })
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
    hasPrice(val) {
      if (val) {
        this.searchObj.type = ''
      }
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    confirm() {
      const soils = this.selected.map((i) => {
        const comp = {
          ...i,
          id: i.vid,
          itemName: i.nameEn || i.itemName,
          itemNumber: i.code || i.itemNumber,
          itemId: i.greaseId,
          price: 0.01,
        }
        return comp
      })
      this.$emit('update:soils', soils)
      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
