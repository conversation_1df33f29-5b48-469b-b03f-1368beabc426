<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      :push-params="pushParams"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            label="业务状态"
            outlined
            dense
            :items="buses"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-text-field
            v-model="searchObj.quoteNo"
            label="报价单号"
            outlined
            dense
            clearable
          ></v-text-field>
        </v-col>
      </template>
      <template #btns>
        <v-switch
          v-permission="['滑油报价:翻译']"
          class="mt-1"
          :label="isEnglish ? 'ENGLISH ON' : 'ENGLISH OFF'"
          v-model="isEnglish"
          dense
        ></v-switch>
      </template>
      <template v-slot:[`item.isWin`]="{ item }">
        {{ item.isWin ? '是' : '否' }}
      </template>
      <template v-slot:[`item.isWinEn`]="{ item }">
        {{ item.isWin ? 'Yes' : 'No' }}
      </template>
      <template v-slot:[`item.enBusinessStatus`]="{ item }">
        {{ busesEn[buses.indexOf(item.businessStatus)] }}
      </template>
      <template v-slot:[`item.shipInfoDO`]="{ item }">
        {{ item.shipInfoDO.chShipName }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// businessStatus	业务状态 : 未填报，填报中，已填报，通过，不通过，废弃	string
// currencyId	币种id;货币汇率表id	string
// currencyName		string
// enquiryId	询价单id	string
// filledBy	填报人	string
// id	物理主键	string
// invoiceType	发票类型	integer
// isWin	是否中标	boolean
// otherExpenses	其他费用	number
// priceIncTax	报价是否含税	boolean
// quoteNo	供应商报价单编号;根据标号规则生成	string
// remark	备注	string
// stockUpDays	备货天数	string
// supplierId	供应商id	string
// supplierName	供应商名称	string
// tax	税费	number
// taxRate	发票税率	number
export default {
  name: 'soil-quote-list',
  created() {
    this.tableName = '滑油报价/soil parts quote'
    this.reqUrl = '/business/shipAffairs/purchaseManage/purchaseQuotePage'
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.chHeaders = [
      { text: '船舶', value: 'shipInfoDO' },
      { text: '报价单号', value: 'quoteNo' },
      { text: '供应商名称', value: 'supplierName' },
      { text: '报价开始时间', value: 'startTime' },
      { text: '报价截止时间', value: 'endTime' },
      { text: '是否中标', value: 'isWin' },
      { text: '币种', value: 'ccyCode' },
      { text: '状态', value: 'businessStatus' },
    ]
    this.enHeaders = [
      { text: 'Quote No', value: 'quoteNo' },
      { text: 'Supplier Name', value: 'supplierName' },
      { text: 'Quote Start Time', value: 'startTime' },
      { text: 'Quote End Time', value: 'endTime' },
      { text: 'Bid', value: 'isWinEn' },
      // TODO:变更为CCYCode
      { text: 'Currency', value: 'ccyCode' },
      { text: 'Status', value: 'enBusinessStatus' },
    ]
    this.buses = ['未填报', '填报中', '已填报', '通过', '不通过', '废弃']
    // TODO:英文状态返回
    this.busesEn = [
      'not filled',
      'filling',
      'filled',
      'approved',
      'rejected',
      'abandoned',
    ]
    this.headers = this.chHeaders
    this.pushParams = { name: 'soil-quote-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        quoteType: '03',
      },
      isEnglish: false,
      headers: [],
    }
  },
  watch: {
    isEnglish(val) {
      if (val) {
        this.headers = this.enHeaders
        this.$nextTick(() => {})
      } else {
        this.headers = this.chHeaders
        this.$nextTick(() => {})
      }
    },
  },
  methods: {},

  mounted() {},
}
</script>

<style></style>
