<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['滑油报价:编辑']"
      :title="`滑油报价/Lube Oil Quote ${detail.quoteNo}`"
      :tooltip="detail.quoteNo"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
      :can-submit="canSubmit"
      @submit="submit"
      :can-save="canSubmit"
    >
      <template v-slot:基本信息-BasicInfo>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form" :readonly="!canSubmit">
            <v-row>
              <v-col cols="12" md="12">发票抬头：{{ inHeader }}</v-col>
              <v-col cols="12" md="12">交货港口：{{ detail.portName }}</v-col>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="true"
                  v-model="detail.shipInfoDO.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.stockUpDays"
                  :rules="[rules.required]"
                  label="备货天数/stockUpDays"
                  type="number"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.filledBy"
                  :rules="[rules.required]"
                  label="填报人/filledBy"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.invoiceType"
                  :rules="[rules.required]"
                  label="发票类型/invoiceType"
                  dense
                  :items="发票类型"
                  outlined
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  disabled
                  dense
                  v-model="detail.ccyCode"
                  :rules="[rules.required]"
                  label="币种/ccyCode"
                  outlined
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.otherExpenses"
                  :rules="[rules.required]"
                  label="运费/otherExpenses"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.taxRate"
                  :rules="[rules.required]"
                  label="发票税率/taxRate"
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="tax"
                  :rules="[rules.required]"
                  label="税费/tax"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  class="mt-1"
                  :label="
                    detail.priceIncTax
                      ? '含税/taxIncluded'
                      : '不含税/excludingTax'
                  "
                  v-model="detail.priceIncTax"
                  dense
                ></v-switch>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="明细总价"
                  :rules="[rules.required]"
                  label="明细总价/detailTotalPrice"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="折扣后明细总价"
                  :rules="[rules.required]"
                  label="折扣后明细总价/totalDetailPriceAfterDiscount"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="折扣后总价"
                  :rules="[rules.required]"
                  label="折扣后总价/totalPriceAfterDiscount"
                  readonly
                  dense
                  type="number"
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
            <v-card-text>
              <v-attach-list
                disabled
                :attachments="detail.attachmentRecords"
                @change="(ids) => (detail.attachmentIds = ids)"
              ></v-attach-list>
            </v-card-text>
          </v-form>
        </v-container>
      </template>
      <template v-if="canSubmit" #报价明细-QuoteDetail按钮>
        <v-btn
          target="_blank"
          :to="`/api//business/shipAffairs/purchaseManage/purchaseQuoteExcelExport?quoteId=${$route.params.id}`"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['滑油报价明细:下载导入模板']"
        >
          <v-icon left>mdi-download</v-icon>
          下载导入模板
        </v-btn>
        <v-import-btn
          import-url="/business/shipAffairs/purchaseManage/purchaseQuoteExcelImport"
          :other-params="{ quoteId: $route.params.id }"
          @importSuccess="importSuccess"
          v-permission="['滑油报价明细:导入EXCEL']"
        ></v-import-btn>
      </template>
      <template #报价明细-QuoteDetail>
        <v-form ref="form2">
          <v-data-table
            :headers="headers"
            :items="detail.detailList"
            hide-default-footer
            disable-pagination
            dense
            class="use-divider"
          >
            <template v-if="canSubmit" v-slot:[`item.quotNum`]="{ item }">
              <!-- <v-edit-dialog :return-value.sync="item.quotNumm">
              <div class="text-decoration-underline blue--text">
                {{ item.quotNum }}
              </div>
              <template v-slot:input>
                <v-text-field
                  v-model="item.quotNum"
                  label="报价数量"
                  type="number"
                  single-line
                ></v-text-field>
              </template>
            </v-edit-dialog> -->
              <v-text-field
                class="shrink"
                v-model="item.quotNum"
                @change="
                  () =>
                    (item.quotNum =
                      item.type == '1649340717607944194'
                        ? item.quotNum
                        : Math.round(item.quotNum))
                "
                label="报价数量/num"
                type="number"
                dense
                single-line
                :rules="
                  item.type == '1649340717607944194'
                    ? [rules.required, rules.decimal]
                    : [rules.required]
                "
              ></v-text-field>
            </template>
            <template v-if="canSubmit" v-slot:[`item.price`]="{ item }">
              <v-text-field
                v-if="!item.haveMainPrice && isJPY"
                @change="() => (item.price = Math.round(item.price))"
                v-model="item.price"
                label="单价/price"
                type="number"
                single-line
                dense
                :rules="item.quotNum == 0 ? [] : [rules.required]"
              ></v-text-field>
              <v-text-field
                v-else-if="!item.haveMainPrice"
                @change="
                  () =>
                    (item.price = (
                      Math.round(item.price * 10000) / 10000
                    ).toFixed(4))
                "
                v-model="item.price"
                label="单价/price"
                type="number"
                single-line
                dense
                :rules="[rules.required]"
              ></v-text-field>
              <div v-else>{{ item.price.toFixed(4) }}</div>
            </template>
            <template v-if="canSubmit" v-slot:[`item.discount`]="{ item }">
              <!-- <v-numeric
                v-if="!item.haveMainPrice"
                v-model="item.discount"
                precision="2"
                label="折扣率/discount"
                type="number"
                single-line
                dense
                :rules="[rules.required]"
                :max="1"
                :min="0"
              ></v-numeric>
              <div v-else>{{ item.discount }}</div>
              都可修改折扣 -->
              <v-numeric
                v-model="item.discount"
                precision="2"
                label="折扣率/discount"
                type="number"
                single-line
                dense
                :rules="[rules.required]"
                :max="1"
                :min="0"
              ></v-numeric>
            </template>
            <template v-slot:[`item.total`]="{ item }">
              {{
                isJPY
                  ? Math.round(item.price * item.quotNum * item.discount)
                  : (
                      Math.round(
                        item.price * item.quotNum * item.discount * 100,
                      ) / 100
                    ).toFixed(2)
              }}
            </template>
          </v-data-table>
        </v-form>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
// discount	折扣		false number
// enquiryId	询价单id		false string
// filledBy	填报人		false
// 物理主键		false string
// invoiceType	发票类型		false  integer(int32)
// otherExpenses	其他费用		false  number
// priceIncTax	报价是否含税		false boolean
// remark	备注		false string
// stockUpDays	备货天数		false string
// supplierId	供应商id		false string
// tax	税费		false number
// taxRate	发票税率		false  number
// import currencyHelper from '@/mixin/currencyHelper'
export default {
  name: 'soil-quote-detail',
  // mixins: [currencyHelper],
  created() {
    this.backRouteName = 'soil-quote-list'
    this.subtitles = ['基本信息-BasicInfo', '报价明细-QuoteDetail']
    this.发票类型 = [
      { text: '增值税专用发票', value: 0 },
      { text: '普通发票', value: 1 },
      { text: '形式发票', value: 2 },
    ]
    this.headers = [
      { text: '滑油英文/itemName', value: 'itemName' },
      { text: '滑油编号/itemNo', value: 'itemNo' },
      { text: '单位/unit', value: 'unit' },
      { text: '询价数量/enquiryNum', value: 'enquiryNum' },
      { text: '报价数量/quotNum', value: 'quotNum', width: 150 },
      { text: '单价/price', value: 'price', width: 150 },
      {
        text: '折扣率/discount',
        value: 'discount',
        width: 150,
        sortable: false,
      },
      { text: '总价/total', value: 'total' },
      { text: '备注/remark', value: 'remark' },
    ]
  },
  data() {
    return {
      detail: {
        detailList: [],
        equipmentInformationBaseInfo: {},
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        aboveZero: (v) => v > 0 || '必须大于0',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
      },
      inHeader: '',
    }
  },

  computed: {
    明细总价() {
      return this.isJPY
        ? this.detail.detailList.reduce(
            (x, { price, quotNum }) => x + Math.round(price * quotNum),
            0,
          )
        : this.detail.detailList
            .reduce(
              (x, { price, quotNum }) =>
                x + Math.round(price * quotNum * 10000) / 10000,
              0,
            )
            .toFixed(2)
    },
    折扣后明细总价() {
      return this.isJPY
        ? this.detail.detailList.reduce(
            (x, { price, quotNum, discount }) =>
              x + Math.round(price * quotNum * discount),
            0,
          )
        : this.detail.detailList
            .reduce(
              (x, { price, quotNum, discount }) =>
                x + Math.round(price * quotNum * discount * 10000) / 10000,
              0,
            )
            .toFixed(2)
    },
    折扣后总价() {
      return this.isJPY
        ? Number(this.折扣后明细总价) + (this.detail.otherExpenses || 0) * 1
        : Number(this.折扣后明细总价) + (this.detail.otherExpenses || 0) * 1
    },
    tax() {
      return this.isJPY
        ? this.折扣后总价 * (this.detail.taxRate || 0)
        : this.折扣后总价 * (this.detail.taxRate || 0)
    },
    canSubmit() {
      return ['填报中', '未填报'].includes(this.detail.businessStatus)
    },
    isJPY() {
      return this.detail.ccyCode === 'JPY'
    },
  },

  methods: {
    async save(goBack) {
      this.detail.detailList = this.detail.detailList.map((i) => ({
        ...i,
        operationType: 2,
      }))
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuoteSaveOrUpdate',
        this.detail,
      )
      if (!errorRaw) goBack()
    },
    async submit(goBack) {
      if (!this.$refs.form.validate() || !this.$refs.form2.validate()) {
        return
      }
      this.detail.detailList = this.detail.detailList.map((i) => ({
        ...i,
        operationType: 2,
      }))
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuoteSubmit',
        this.detail,
      )
      if (!errorRaw) goBack()
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuoteDetailById',
        {
          quoteId: this.$route.params.id,
        },
      )
      if (data.businessStatus == '未填报') {
        delete data.invoiceType
        data.taxRate = 0
      }
      this.detail = data
      this.loadhEADER()
      this.detail.filledBy = this.$local.data.get('userInfo').nickName
      this.$refs.form.resetValidation()
    },
    async importSuccess() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseQuoteDetailById',
        {
          quoteId: this.$route.params.id,
        },
      )
      if (data.businessStatus == '未填报') {
        delete data.invoiceType
      }
      this.detail.detailList = data.detailList
    },
    async loadhEADER() {
      const { data } = await this.getAsync(
        '/business/common/ship/getPayCompanyHeader',
        {
          code: this.detail.shipInfoDO.shipCode,
        },
      )
      if (data) {
        this.inHeader = data
      }
      console.log(data)
    },
  },

  mounted() {
    this.loadDetail()
    this.loadhEADER()
  },
}
</script>

<style></style>
