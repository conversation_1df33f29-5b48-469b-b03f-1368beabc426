<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['滑油盘点:编辑']"
      :title="`库存盘点-${detail.checkCode || '新增'}`"
      :tooltip="detail.checkCode || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      @save="save"
      @submit="submit"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
    >
      <!-- <template v-if="detail.status == 3" v-slot:custombtns>
        <v-btn
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: detail.systemReportId },
          }"
          color="info"
          small
          class="mx-1"
        >
          查看部门报表
        </v-btn>
      </template> -->
      <template v-if="!isEdit" #titlebtns>
        <!-- <v-btn
          v-show="detail.originId"
          tile
          small
          color="warning"
          class="mx-1"
          @click.stop="initCheck"
          v-permission="['滑油盘点基本信息:发起盘点']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          发起盘点
        </v-btn> -->
        <v-btn
          v-show="detail.deptName"
          tile
          small
          color="warning"
          class="mx-1"
          @click.stop="initCheck"
          v-permission="['滑油盘点基本信息:发起盘点']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          发起盘点
        </v-btn>
      </template>
      <template
        v-if="detail.auditParams && detail.auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="!!detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <!-- TODO:
              根据用户信息读取用户所在部门
               -->
              <v-col cols="12" md="3">
                <v-ship-dept
                  :readonly="!canEdit"
                  dense
                  outlined
                  v-model="detail.deptName"
                  label="申请部门"
                  :items="depts"
                  :rules="[rules.required]"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <!-- <v-select
                  dense
                  outlined
                  label="盘点仓库"
                  :disabled="!detail.shipCode"
                  v-model="detail.originId"
                  :rules="[rules.required]"
                  :items="depository"
                ></v-select> -->
                <!-- <engine-select
                  :readonly="isEdit"
                  :disabled="!detail.shipCode"
                  v-model="detail.equipmentInformationId"
                  :shipCode="detail.shipCode"
                  :initSelected="initEngine"
                  :rules="[rules.required]"
                ></engine-select> -->
              </v-col>
            </v-row>
            <v-row v-if="isEdit">
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="!canEdit"
                  outlined
                  dense
                  label="盘点时间"
                  v-model="detail.stockCheckDate"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="!canEdit"
                  outlined
                  dense
                  label="申请时间"
                  use-today
                  v-model="detail.applyDate"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="!canEdit"
                  outlined
                  dense
                  label="操作人"
                  v-model="detail.applyUserName"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  :readonly="!canEdit"
                  outlined
                  dense
                  label="监督人"
                  v-model="detail.supervisor"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="12">
                <span style="color: red; font-size: 15px">
                  1、每盘点完一页，需点击“单页保存”按钮，否则翻页会丢失本页数据
                  2、本单盘点完成后，需点击“保存”或“保存并提交”按钮
                </span>
              </v-col> -->
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template v-slot:盘点明细按钮>
        <v-btn
          :disabled="!selected || !canEdit"
          target="_blank"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="saveQuesList"
          v-permission="['滑油盘点:保存']"
        >
          <v-icon left>mdi-pencil</v-icon>
          单页保存
        </v-btn>
        <v-btn
          target="_blank"
          :to="`/api//business/shipAffairs/purchaseManage/dowExcelStockCheckDetail?checkId=${$route.params.id}`"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['滑油盘点:导出盘点明细']"
        >
          <v-icon left>mdi-download</v-icon>
          导出盘点明细
        </v-btn>
        <v-import-btn
          v-if="canEdit"
          :disable="!canEdit"
          import-url="/business/shipAffairs/purchaseManage/importExcelStockCheckDetail"
          :other-params="{ checkId: $route.params.id }"
          @importSuccess="importSuccess"
          v-permission="['滑油盘点:导入盘点明细']"
        ></v-import-btn>

        <!-- <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="dowExcel"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          导出盘点明细
        </v-btn>
        <v-btn
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="importExcel"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          导入盘点明细
        </v-btn> -->
      </template>
      <template #盘点明细>
        <v-form ref="tform">
          <v-col cols="12" md="12">
            <span style="color: red; font-size: 15px">
              1、每盘点完一页，需点击“单页保存”按钮，否则翻页会丢失本页数据
              2、本单盘点完成后，需点击“保存”或“保存并提交”按钮
            </span>
          </v-col>
          <!-- <v-table-list
            :show-select="false"
            :headers="detailHeaders"
            :items="detail.detailList"
          > -->
          <v-table-searchable
            ref="table"
            itemid="detailList"
            :req-url="reqUrl2"
            :headers="detailHeaders"
            v-model="selected"
            :table-name="name"
            :search-remain="searchObj"
            :single-select="false"
          >
            <template #searchflieds>
              <!-- <v-col cols="12" sm="6" md="4">
                <engine-select
                  v-model="searchObj.equipmentInformationId"
                  :shipCode="detail.shipCode"
                  :initSelected="initEngine"
                  :rules="[false]"
                ></engine-select>
              </v-col> -->
              <!-- <v-col cols="12" sm="6" md="2">
                <v-switch
                  class="mt-1"
                  dense
                  v-model="searchObj.isMe"
                  label="未盘点"
                  color="success"
                ></v-switch>
              </v-col> -->
              <v-col cols="12" md="2">
                <v-select
                  dense
                  outlined
                  label="盘点状态"
                  v-model="searchObj.checkConfirmStatus"
                  :items="checkConfirmStatusList"
                  clearable
                ></v-select>
              </v-col>
            </template>
            <template v-if="canEdit" v-slot:[`item.actualNumber`]="{ item }">
              <v-text-field
                dense
                v-model="item.actualNumber"
                :rules="
                  item.greaseType == '1649340717607944194'
                    ? [rules.required, rules.decimal]
                    : [rules.required, rules.int]
                "
                single-line
                type="number"
              ></v-text-field>
            </template>
            <template v-slot:[`item.checkConfirm`]="{ item }">
              <v-switch
                class="mt-1"
                dense
                v-model="item.checkConfirm"
                :label="`${item.checkConfirm ? '已盘点' : '未盘点'}`"
                color="success"
              ></v-switch>
            </template>
            <template v-slot:[`item.greaseType`]="{ item }">
              <span v-if="item.greaseType == '1581991857030852611'">
                主机系统油
              </span>
              <span v-if="item.greaseType == '1581991857047629825'">
                副机系统油
              </span>
              <span v-if="item.greaseType == '1581991857022464003'">
                主机气缸油
              </span>
              <span v-if="item.greaseType == '1649340717607944194'">
                其他小品种油
              </span>
            </template>
            <template v-if="canEdit" v-slot:[`item.remark`]="{ item }">
              <!-- <v-text-field
                dense
                v-model="item.remark"
                single-line
                :rules="[
                  item.actualNumber - item.stocksNumber == 0
                    ? true
                    : rules.required,
                ]"
              ></v-text-field> -->

              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    @click="editRemark(item)"
                    v-bind="attrs"
                    v-on="on"
                    dense
                    v-model="item.remark"
                    single-line
                    :rules="[
                      item.actualNumber - item.stocksNumber == 0
                        ? true
                        : rules.required,
                    ]"
                  ></v-text-field>
                </template>
                <span>{{ item.remark }}</span>
              </v-tooltip>
            </template>
            <template v-slot:[`item.profitAndLossNumber`]="{ item }">
              {{ calculateDifference(item.actualNumber, item.stocksNumber) }}
            </template>
          </v-table-searchable>
        </v-form>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <v-dialog v-model="dialog1" max-width="600">
      <template v-slot:default="dialog1">
        <v-card style="height: 320px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-0" style="color: black">
                  滑油名称：{{ editRemarkDetails.itemName }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  账面数量：{{ editRemarkDetails.stocksNumber }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  实际数量：{{ editRemarkDetails.actualNumber }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  盈亏数量：{{
                    calculateDifference(
                      editRemarkDetails.actualNumber,
                      editRemarkDetails.stocksNumber,
                    )
                  }}
                </v-col>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import Big from 'big.js'
export default {
  name: 'soil-check-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'soil-stock-check-list'
    this.subtitles = ['基本信息', '盘点明细']
    this.depts = ['甲板部', '轮机部']
    this.reqUrl2 = `/business/shipAffairs/purchaseManage/stockCheckDetailListById?id=${this.$route.params.id}`
    this.name = ''
  },
  data() {
    return {
      detail: {
        detailList: [],
        checkType: '2',
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        decimal: (v) =>
          /^\d+(\.\d{1,2})?$/.test(v) || '必须为整数或最多两位小数',
      },
      depository: [],
      initEngine: {},
      checkConfirmStatusList: [
        { value: '0', text: '未盘点' },
        { value: '1', text: '已盘点' },
      ],
      detailHeaders: [
        { text: '仓库名称', value: 'depositoryName' },
        { text: '滑油名称', value: 'itemName' },
        { text: '滑油编号', value: 'itemNo' },
        { text: '滑油类型', value: 'greaseType' },
        { text: '单位', value: 'unit' },
        { text: '库存单价-参考', value: 'stocksToUsd' },
        { text: '库存价值-参考', value: 'stocksValue' },
        { text: '账面数量', value: 'stocksNumber' },
        { text: '实际数量', value: 'actualNumber' },
        { text: '盈亏数量', value: 'profitAndLossNumber' },
        { text: '已盘点', value: 'checkConfirm' },
        { text: '备注', value: 'remark' },
      ],
      station: '',
      needFields: [],
      mapping: {},
      dialog1: false,
      editRemarkDetails: {},
      searchObj: {},
      selected: [],
    }
  },

  watch: {
    'detail.shipCode'(val) {
      if (val) {
        this.loadDepository()
      }
    },
    detail(val) {
      // if (val.originId && !this.isEdit) {
      //   this.initCheck()
      // }
      if (val.deptName && !this.isEdit) {
        this.initCheck()
      }
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
  },

  methods: {
    calculateDifference(actualNumber, stocksNumber) {
      const result = new Big(actualNumber).minus(new Big(stocksNumber))
      return result.toFixed(2)
    },
    editRemark(item) {
      // console.log(item)
      this.editRemarkDetails = item
      this.dialog1 = true
    },
    saveRemark() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1 = false
    },
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/stockCheckDetailById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
      this.detail.originId = data.despositoryId
      this.$refs.form.resetValidation()
      // await this.loadNeedFields()
    },

    async loadNeedFields() {
      if (!this.detail?.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        { processInstanceId: this.detail.auditParams.processInstanceId },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          this.mapping[t.mappingCode] = new Date(Date.now())
            .toISOString()
            .substr(0, 10)
        }
      }
    },

    async loadDepository() {
      // TODO:如果是非船端角色则将搜索所有的库位
      const { data } = await this.getAsync('/business/shipAffairs/Cabin/list', {
        size: 99,
        current: 1,
        shipCode: this.detail.shipCode,
        type: 0,
      })
      const { records } = data
      this.depository = records.map((i) => ({ text: i.name, value: i.id }))
    },

    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return false
      }
      await this.saveQuesList()
      const stockCheckDetailModifyDTOS = this.detail.detailList.map((item) => {
        return {
          ...item,
          operationType: 2,
        }
      })
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/saveOrUpdateStockCheck',
        { ...this.detail, stockCheckDetailModifyDTOS },
      )
      if (errorRaw) {
        return false
      }
      if (notMove) return true
      goBack()
    },
    async submit(goBack) {
      // if (!(this.$refs?.aform?.validate() ?? true)) return
      // if (!(this.$refs?.tform?.validate() ?? true)) return
      if (!(this.$refs?.aform?.validate() ?? true)) {
        this.$dialog.message.error('有必填项未填写！')
        return
      }
      // if (!(this.$refs?.tform?.validate() ?? true)) {
      //   this.$dialog.message.error('盘点明细备注未填写！')
      //   return
      // }
      const res = await this.save(goBack, true)
      if (!res) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/stockCheckSubmitCheck',
        { id: this.$route.params.id },
      )
      console.log(data)
      if (data == 'FAIL|1') {
        this.$dialog.message.error('未盘点完成，请全部盘点完成后提交！')
        return
      } else if (data == 'FAIL|2') {
        this.$dialog.message.error('有盘点明细备注未填写，请填写后提交！')
        return
      }
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/purchaseManage/stockCheckSubmit',
          { id: this.$route.params.id },
        )
        if (errorRaw) return
      } else {
        // let mappingDetails = []
        // for (let f of this.needFields) {
        //   mappingDetails.push({
        //     processInstanceId: this.detail.auditParams.processInstanceId,
        //     mappingCode: f.mappingCode,
        //     mappingContent:
        //       this.mapping[f.mappingCode] ||
        //       this.$local.data.get('userInfo').id,
        //     mappingType: f.mappingType,
        //   })
        // }
        // let { errorRaw } = await this.postAsync(
        //   '/business/seaAffairs/templateMapping/saveMappingDetail',
        //   mappingDetails,
        // )
        // if (errorRaw) return
        const error = await this.$refs.audit.submit()
        if (error) return
      }
      goBack()
    },
    async initCheck() {
      if (!this.$refs.form.validate()) {
        return false
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/saveOrUpdateStockCheck',
        this.detail,
      )
      if (!data) return
      this.closeAndTo('soil-check-detail', { id: data })
    },
    async importSuccess() {
      // const { data } = await this.getAsync(
      //   '/business/shipAffairs/purchaseManage/purchaseQuoteDetailById',
      //   {
      //     quoteId: this.$route.params.id,
      //   },
      // )
      // if (data.businessStatus == '未填报') {
      //   delete data.invoiceType
      // }
      // this.detail.detailList = data.detailList
      this.loadDetail()
    },
    async saveQuesList() {
      // this.get('detailList')
      // console.log(this.detailList)
      // console.log(this.$refs?.tform)
      // console.log(this.$refs?.tform?.data)
      // console.log(this.$refs?.table)
      // console.log(this.$refs?.table.$data) //有效
      // console.log(this.$refs?.table.$data.items) //有效
      // console.log(this.$refs?.table.data)
      // if (!this.selected || this.selected.length == 0) {
      //   this.$dialog.message.success(`请选择需要保存的记录！`)
      //   return
      // }
      const url =
        '/business/shipAffairs/purchaseManage/detail/updateStockCheckDetailList'
      const { errorRaw } = await this.postAsync(
        url,
        this.$refs?.table.$data.items,
      )
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        this.$dialog.message.success(`保存成功`)
      }
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
