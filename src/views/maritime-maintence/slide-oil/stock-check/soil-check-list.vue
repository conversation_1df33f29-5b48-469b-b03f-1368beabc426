<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      :search-remain="searchObj"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="盘点单号"
            outlined
            dense
            clearable
            v-model="searchObj.checkCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="
            selected.businessStatus !== '已完成' &&
            selected.businessStatus !== '审批通过'
          "
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="genSapMes"
          v-permission="['滑油盘点:生成盘点报文']"
        >
          <v-icon left>mdi-message-badge-outline</v-icon>
          生成盘点报文
        </v-btn>
        <v-btn
          :to="{ name: 'soil-check-detail', params: { id: 'new' } }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['滑油盘点:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['滑油盘点:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'soil-stock-check-list',
  //   applyDate	申请时间	string
  // applyUserId	操作人id	string
  // applyUserName	操作人	string
  // attachmentRecords	附件列表	array	CommonAttachment
  // auditParams	流程参数	AuditParams	AuditParams
  // businessStatus	业务状态	integer
  // checkCode	盘点编号	string
  // deptName	部门名称	string
  // despositoryId	仓库id	string
  // detailList	明细列表	array	StockCheckDetailOutputDTO
  // id	物理主键	string
  // remark	备注	string
  // shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
  // status	流程状态	string
  // stockCheckDate	盘点时间	string
  // stocksType	仓库类型	string
  // supervisor	监督人	string
  created() {
    this.tableName = '库存盘点'
    this.reqUrl = '/business/shipAffairs/purchaseManage/stockCheckPageByParams'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '盘点编号', value: 'checkCode' },
      { text: '部门', value: 'deptName' },
      { text: '申请时间', value: 'applyDate' },
      { text: '操作人', value: 'applyUserName' },
      { text: '盘点时间', value: 'stockCheckDate' },
      { text: '监督人', value: 'supervisor' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '审批状态', value: 'status' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
    this.pushParams = { name: 'soil-check-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: { stocksType: '03', isMe: true },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/deleteStockCheck',
        { Id: this.selected.id },
      )
      if (!errorRaw) {
        this.selected = false
        this.$refs.table.loadTableData()
      }
    },
    async genSapMes() {
      if (
        this.selected.businessStatus !== '已完成' &&
        this.selected.businessStatus !== '审批通过'
      )
        return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/generateSapMsg',
        { Id: this.selected.id },
      )
      if (!errorRaw) {
        this.selected = false
        this.$refs.table.loadTableData()
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
