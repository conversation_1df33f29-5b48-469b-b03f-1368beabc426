<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ isEdit ? '修改' : '创建' }}大模块(二级分类)
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <!-- <v-col cols="12" md="4">
                <v-ship-select
                  v-model="formData.shipCode"
                  disabled
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  dense
                  disabled
                  outlined
                  label="设备编码"
                  v-model="formData.equipmentCode"
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="4">
                <v-text-field
                  dense
                  outlined
                  label="大模块名称"
                  v-model="formData.name"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'moudle-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      if (!this.formData.mainEquipmentId) {
        if (!this.isEdit) {
          this.$emit('add', { ...this.formData, id: Math.random() })
        } else {
          this.$emit('update', this.formData)
        }
        this.$emit('change', false)
        return
      }
      const url =
        '/business/shipAffairs/equipmentInformation/saveOrUpdateSecond'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
