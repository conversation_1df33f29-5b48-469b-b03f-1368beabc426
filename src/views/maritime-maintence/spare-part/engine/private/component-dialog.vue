<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        {{ isEdit ? '编辑' : '创建' }}备件
        <v-spacer></v-spacer>
        <v-btn
          :loading="loading"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '保存' : '创建' }}
        </v-btn>
        <v-btn
          :loading="loading"
          small
          outlined
          tile
          class="mx-1"
          @click="closeForm"
        >
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col
                class="py-0"
                cols="12"
                md="3"
                v-for="(h, i) in headers"
                :key="i"
              >
                <v-select
                  v-if="['componentProperty'].includes(h.value)"
                  v-model="formData[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  required
                  outlined
                  :disabled="formData.sapComponentNumberNew"
                  dense
                  :items="
                    initialData.euipmentType == '3'
                      ? comPropertiesTD
                      : comProperties
                  "
                ></v-select>
                <v-text-field
                  v-else-if="['maximumInventory'].includes(h.value)"
                  v-model="formData[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                  type="number"
                ></v-text-field>
                <v-dialog-select
                  v-else-if="h.value === 'costSubjectId'"
                  req-url="/business/shipAffairs/costSubject/pageFixed"
                  label="费用科目"
                  v-model="formData[h.value]"
                  :rules="[rules.required]"
                  :init-selected="formData.initSubject"
                  :search-remain="searchObj"
                  item-text="subjectName"
                  item-value="id"
                  :headers="subHeaders"
                  :disabled="
                    formData.componentProperty !== '2' &&
                    formData.componentProperty !== '3'
                  "
                  dense
                  fuzzyLabel="模糊查询"
                ></v-dialog-select>
                <!-- <v-select
                  dense
                  outlined
                  v-else-if="h.value === 'standardType'"
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  required
                  :items="standardTypes"
                ></v-select> -->
                <v-dict-select
                  v-else-if="h.value === 'standardType'"
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  clearable
                  dense
                  outlined
                  dict-type="component_standard_ype"
                ></v-dict-select>
                <!-- :disabled="formData.sapComponentNumberNew" -->
                <v-text-field
                  v-else-if="h.value === 'componentNumber'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  outlined
                  dense
                  required
                ></v-text-field>
                <!-- :disabled="formData.sapComponentNumberNew" -->
                <v-text-field
                  v-else-if="h.value === 'componentEname'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  :rules="
                    ['componentNumber', 'componentEname'].includes(h.value)
                      ? [rules.required]
                      : []
                  "
                  outlined
                  dense
                  required
                ></v-text-field>
                <!-- :disabled="isEdit" -->
                <v-text-field
                  v-else
                  v-model="formData[h.value]"
                  :label="h.text"
                  :rules="
                    ['componentNumber', 'componentEname'].includes(h.value)
                      ? [rules.required]
                      : []
                  "
                  outlined
                  dense
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" class="py-0">
                <v-textarea
                  v-model="formData.remark"
                  label="备注"
                  outlined
                  dense
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <!-- 重复数据校验及引用 -->
    <v-dialog
      v-model="dialogDuplicate"
      max-width="1500"
      hide-overlay
      attach="#mask"
    >
      <v-card>
        <v-card-title class="text-h5">图纸号、备件号相同备件确认</v-card-title>
        <v-card-text>
          <v-alert type="info" color="green" text dense class="mb-0">
            其他船舶存在该图纸号、备件号相同备件，是否直接引用。
            <br />
            如选择“引用选中备件数据”，该备件信息全部一并带入；
            <br />
            或选择“不引用，保存当前录入数据”，保存当前录入备件数据。
          </v-alert>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn depressed color="primary" @click="closeForm2">取消</v-btn>
          <v-btn depressed color="primary" @click="confirm">
            不引用，保存当前录入数据
          </v-btn>
          <v-btn depressed color="primary" @click="confirmCopy">
            引用选中备件数据
          </v-btn>
        </v-card-actions>
        <v-card-text>
          <v-table-list
            :table-name="''"
            v-model="selectedCopy"
            :headers="headers"
            :items="duplicateList"
          >
            <template v-slot:[`item.shipInfo`]="{ item }">
              {{ item.shipInfo == null ? '' : item.shipInfo.chShipName }}
            </template>
          </v-table-list>
        </v-card-text>
        <!-- <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn depressed @click="closeForm2">取消</v-btn>
            <v-btn depressed color="primary" @click="confirm">确定</v-btn>
          </v-card-actions> -->
      </v-card>
    </v-dialog>
  </v-dialog>
</template>

<script>
const costSubjectMap = {
  0: '1581991856988909569',
  1: '1581991856997298179',
  2: '1581991857005686787',
}
export default {
  name: 'component-dialog',
  created() {
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      // { text: '在图编号', value: 'drawingNo' },
      { text: '*备件名称', value: 'componentEname' },
      // { text: '型号', value: 'componentModel' },
      { text: '*备件号', value: 'componentNumber' },
      { text: 'NOx编号', value: 'nox' },
      // { text: '参数', value: 'componentParameter' },
      // { text: '规格', value: 'componentSpecifications' },
      { text: '单位', value: 'componentUnit' },
      { text: '*备件类型', value: 'componentProperty' },
      { text: '*最高库存', value: 'maximumInventory' },
      { text: '最低库存', value: 'minimumInventory' },
      { text: '*费用科目', value: 'costSubjectId' },
      { text: '标准化类别', value: 'standardType' },
    ]
    this.comProperties = [
      { text: '普通备件', value: '0' },
      { text: 'SAP备件', value: '1', disabled: true },
      { text: '固定资产', value: '2' },
      // { text: '通导设备固定资产', value: '3' },
    ]
    this.comPropertiesTD = [
      // { text: '普通备件', value: '0' },
      // { text: 'SAP备件', value: '1', disabled: true },
      // { text: '固定资产', value: '2' },
      { text: '通导设备固定资产', value: '3' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDicts = [
      {
        dicType: 'cost_subject_type',
        label: '业务分类',
        key: 'subjectType',
      },
    ]
    this.standardTypes = [
      { text: '标准1', value: '1' },
      { text: '标准2', value: '2' },
      { text: '标准3', value: '3' },
      { text: '标准4', value: '4' },
    ]
    this.costSubjectMap = costSubjectMap
  },
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    euipmentType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dialog: false,
      formData: { componentProperty: '' },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      searchObj: {
        subjectType: '',
      },
      loading: false,
      dialogDuplicate: false,
      duplicateList: [],
      yinYong: false,
      selectedCopy: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.formData.initSubject = {
        id: this.formData.costSubjectId,
        subjectName: this.formData.costSubjectName,
      }
    },
    'formData.componentProperty'(val) {
      // if (this.isEdit) return
      if (val === '0') {
        this.formData.initSubject = {
          id: this.costSubjectMap[this.formData.euipmentType],
          subjectName: ['主机备件', '副机备件', '辅助设备备件'][
            this.formData.euipmentType
          ],
        }
        this.formData.costSubjectId =
          this.costSubjectMap[this.formData.euipmentType]
      } else if (val === '2') {
        this.searchObj = {
          subjectType: '固定资产费用',
        }
        this.formData.initSubject = {}
        this.formData.costSubjectId = ''
      } else if (val === '3') {
        this.searchObj = {
          subjectType: '通导设备固定资产',
        }
        this.formData.initSubject = {}
        this.formData.costSubjectId = ''
      }
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.loading = true
      // if (!this.isEdit) {
      //   if (!(await this.checkDuplicate())) {
      //     return
      //   }
      // }
      // if (this.yinYong) {
      //   //
      //   console.log('调用引用方法')
      // } else {
      const url =
        '/business/shipAffairs/equipmentInformation/saveOrUpdateComponent'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      this.loading = false
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
      // }
    },
    closeForm2() {
      this.dialogDuplicate = false
      this.loading = false
      this.resolveFn(false)
    },

    async confirm() {
      this.dialogDuplicate = false
      this.yinYong = false
      this.resolveFn(true)
    },
    async confirmCopy() {
      if (!this.selectedCopy) {
        this.$dialog.message.error('请勾选记录！')
        return
      }
      console.log(this.selectedCopy)
      this.dialogDuplicate = false
      this.yinYong = true
      this.resolveFn(true)
    },
    async checkDuplicate() {
      const re0 = this.getAsync(
        '/business/shipAffairs/equipmentInformation/getBySapComponentNumberNew',
        {
          sapComponentNumberNew:
            this.formData.drawingSerialNumber + this.formData.componentNumber,
        },
      )
      console.log(***********)
      const [res0] = await Promise.all([re0])
      console.log(222222222222)
      if (res0.data.length) {
        this.duplicateList = res0.data
        this.dialogDuplicate = true
      }
      console.log(33333333333333)
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
