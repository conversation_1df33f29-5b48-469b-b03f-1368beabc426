<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        选择子设备
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          :table-name="''"
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :single-select="true"
          :search-remain="searchObj"
          fuzzy-label="模糊查询"
        >
          <template #searchflieds></template>
          <template #btns></template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
// defaultBonus	自修项目的标准奖金	number
// description	自修项目描述	string
// id	物理主键	string
// name	自修项目名	string
// property	此项目为所有船共用项目还是某个船舶专属，如果为共用项目，不填，否则填专属船舶的ID。
export default {
  name: 'component-third-dialog-select',
  created() {
    this.reqUrl = '/business/shipAffairs/equipmentInformation/ThirdPage'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '英文名称', value: 'subEquipmentEname' },
      // { text: '中文名称', value: 'subEquipmentCname' },
      { text: '设备主体', value: 'equipmentFirstName' },
      { text: '图纸编号', value: 'drawingSerialNumber' },
      { text: '备注', value: 'remark' },
    ]
    this.selected = false
    this.searchDicts = [
      {
        dicType: 'self_repair_item_type',
        label: '自修项目分类',
        key: 'description',
      },
    ]
  },

  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialList: {
      type: Array,
      default: () => [],
    },
    shipCode: String,
    searchInitial: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      selected: false,
      searchObj: { shipCode: '' },
      formData: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      // this.selected = this.initialList
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
    searchInitial(val) {
      this.searchObj = val
    },
  },
  methods: {
    closeForm() {
      this.selected = false
      this.$emit('change', false)
    },
    async confirm() {
      // console.log(this.selected)
      // if (this.selected) {
      //   return
      // }
      if (!this.selected || !this.selected.id) {
        return
      }
      this.formData.id = this.searchObj.componentId
      this.formData.equipmentId = this.selected.id
      this.formData.shipCode = this.selected.shipCode
      const url =
        '/business/shipAffairs/equipmentInformation/saveOrUpdateComponentThirdId'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
      })
      this.loading = false
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
        this.$router.push({
          name: 'sub-engine-detail',
          params: {
            id: this.selected.id,
          },
        })
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
