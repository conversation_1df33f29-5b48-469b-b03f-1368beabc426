<template>
  <v-sheet class="my-3">
    <!-- <v-card-subtitle class="text-h6 py-1">修改明细</v-card-subtitle> -->
    <v-divider></v-divider>
    <!-- <v-data-table
      dense
      :headers="费用项目表头"
      :items="list"
      hide-default-footer
      disable-pagination
    ></v-data-table> -->
    <v-table-searchable
      ref="table"
      table-name="修改明细"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
    >
      <template v-slot:[`item.componentProperty`]="{ item }">
        {{
          ['普通备件', 'SAP备件', '固定资产', '通导设备固定资产'][
            item.componentProperty
          ]
        }}
      </template>
      <template v-slot:[`item.stopUse`]="{ item }">
        <span style="color: red" v-if="item.stopUse == 1">停用</span>
        <span v-if="item.stopUse == 0">启用</span>
      </template>
    </v-table-searchable>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
export default {
  name: 'component-log-list',
  created() {
    this.headers = [
      { text: '备件名称', value: 'componentEname' },
      // { text: '型号', value: 'componentModel' },
      { text: '备件号', value: 'componentNumber' },
      { text: '备件唯一标识符', value: 'sapComponentNumberNew' },
      { text: 'NOx编号', value: 'nox' },
      // { text: '参数', value: 'componentParameter' },
      // { text: '规格', value: 'componentSpecifications' },
      { text: '单位', value: 'componentUnit' },
      { text: '备件类型', value: 'componentProperty' },
      { text: '费用科目', value: 'costSubjectName' },
      // { text: 'SAP编码', value: 'sapComponentNumber' },
      { text: '标准化类别', value: 'standardType' },
      { text: '最高库存', value: 'maximumInventory' },
      { text: '最低库存', value: 'minimumInventory' },
      { text: '备注', value: 'remark' },
      { text: '状态', value: 'stopUse' },
      { text: '操作人', value: 'personName' },
      { text: '操作时间', value: 'operateTime' },
    ]
    this.reqUrl = '/business/shipAffairs/equipmentInformation/componentLogPage'
  },
  props: {
    itemId: String,
  },
  data() {
    return {
      list: [],
      searchObj: {
        componentId: this.itemId,
      },
    }
  },
  // watch: {
  //   itemId: {
  //     handler: function (val) {
  //       console.log('123121231aaaa', val)
  //       // this.searchObj.componentId = val
  //       this.searchObj = {
  //         componentId: val,
  //       }
  //     },
  //   },
  // },
  methods: {
    // async loadDetail() {
    //   const { data } = await this.getAsync(
    //     `/business/shipAffairs/costSapNew/componentLogPage/${this.itemId}`,
    //   )
    //   this.list = data.list
    // },
  },

  mounted() {
    // this.loadDetail()
  },
}
</script>

<style></style>
