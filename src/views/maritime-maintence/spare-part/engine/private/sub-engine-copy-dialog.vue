<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        设备复制
        <v-spacer></v-spacer>
        <v-btn
          :loading="loading"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="confirm"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          复制
        </v-btn>
        <v-btn
          :loading="loading"
          small
          outlined
          tile
          class="mx-1"
          @click="closeForm"
        >
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          :fuzzy-label="fuzzyLabel"
          use-ship
        >
          <template #searchflieds>
            <v-ship-select
              v-model="targetShipCode"
              :changeLabel="true"
            ></v-ship-select>
          </template>
          <template #btns></template>
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo == null ? '' : item.shipInfo.chShipName }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <!-- <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions> -->
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'sub-engine-copy-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.tableName = '设备复制'
    this.reqUrl = '/business/shipAffairs/equipmentInformation/firstPage'
    this.headers = [
      { text: '所属船舶', value: 'shipInfo' },
      { text: '设备英文名称', value: 'equipmentEname' },
      // { text: '设备中文名称', value: 'equipmentCname' },
      // { text: '设备编码', value: 'equipmentCode' },
      { text: '设备型号', value: 'equimentModel' },
      { text: '状态', value: 'equipmentStatus' },
      { text: '类型', value: 'euipmentType' },
      // { text: '安装位置', value: 'installationSite', hideDefault: true },
      { text: '生产厂家', value: 'manufacture', hideDefault: true },
      { text: '出厂日期', value: 'productDate', hideDefault: true },
      { text: '上次大修日期', value: 'repairDate', hideDefault: true },
      { text: '主管岗位', value: 'supervisorPosition' },
      { text: '现存数量', value: 'amount' },
      { text: '设备核心参数', value: 'parameter', hideDefault: true },
      { text: '设备序列号', value: 'equipmentNumber', hideDefault: true },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    components: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {},
      selected: [],
      targetShipCode: '',
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    // 'searchObj.equipmentSecondId': {
    //   handler(val) {
    //     if (val) this.loadSubEqu()
    //   },
    // },
    // 'searchObj.equipmentInformationId': {
    //   handler(val) {
    //     if (val) this.loadSecondEquipment()
    //   },
    // },
    // searchRemain(val) {
    //   this.searchObj = val
    // },
    // components: {
    //   handler(val) {
    //     this.selected = val.map((i) => {
    //       return { ...i, vid: i.id, id: i.componentId, remarkk: i.remark }
    //     })
    //   },
    //   deep: true,
    // },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async confirm() {
      // const mainIdArr = this.selected.map((i) => {
      //   const comp = i.id
      //   return comp
      // })
      // console.log(mainIdArr)
      // console.log(this.targetShipCode)
      if (!this.targetShipCode) {
        this.$dialog.message.error(`请选择目标船舶`)
        return
      }
      this.loading = true
      let formData = new FormData()
      formData.append(
        'mainId',
        this.selected.map((item) => item.id),
      )
      formData.append('shipCode', this.targetShipCode)
      formData.append('equipmentNumber', [])
      const { errorRaw } = await this.postAsync(
        `/business/shipAffairs/equipmentInformation/copyEquipmentInformation`,
        formData,
        // {
        //   mainId: this.selected.map((item) => item.id),
        //   shipCode: this.targetShipCode,
        // },
      )
      this.loading = false
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
        return
      }
      this.$dialog.message.success('复制成功')

      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
