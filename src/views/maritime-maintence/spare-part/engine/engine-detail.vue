<template>
  <v-container fluid>
    <v-detail-view
      :title="`设备主体-${isEdit ? detail.equipmentCname : '新增'}`"
      :tooltip="isEdit ? detail.equipmentCname : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #基本信息>
        <v-container fluid>
          <v-form ref="form">
            <v-row>
              <v-col
                class="py-0"
                cols="12"
                md="2"
                v-for="(h, i) in headers"
                :key="i"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfo'"
                  v-model="detail['shipCode']"
                  :rules="[rules.required]"
                  required
                ></v-ship-select>
                <vs-date-picker
                  v-else-if="['productDate', 'repairDate'].includes(h.value)"
                  v-model="detail[h.value]"
                  :label="h.text"
                  outlined
                  dense
                ></vs-date-picker>
                <v-select
                  v-else-if="h.value === 'equipmentStatus'"
                  v-model="detail[h.value]"
                  dense
                  outlined
                  :label="h.text"
                  :rules="[rules.required]"
                  required
                  :headers="moduleHeaders"
                  :items="equipmentStatuses"
                ></v-select>
                <v-select
                  dense
                  outlined
                  v-else-if="h.value === 'euipmentType'"
                  v-model="detail[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  required
                  :items="euipmentTypes"
                ></v-select>
                <v-text-field
                  v-else-if="h.value === 'remark'"
                  v-model="detail[h.value]"
                  dense
                  :label="h.text"
                ></v-text-field>
                <v-ship-station
                  v-else-if="h.value === 'supervisorPosition'"
                  v-model="detail[h.value]"
                  :label="h.text"
                ></v-ship-station>
                <v-combobox
                  v-else-if="h.value === 'equipmentNumber'"
                  v-model="detail[h.value]"
                  :label="h.text"
                  multiple
                  outlined
                  small-chips
                  dense
                ></v-combobox>
                <v-text-field
                  v-else
                  outlined
                  dense
                  v-model="detail[h.value]"
                  :label="h.text"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
export default {
  name: 'engine-detail',
  created() {
    this.backRouteName = 'report-emit-list'
    this.subtitles = ['基本信息', '大模块']
  },
  data() {
    return {}
  },

  methods: {
    async save(goBack) {
      goBack()
    },

    async loadDetail() {
      //   const { data } = await this.getAsync('')
    },
  },

  mounted() {},
}
</script>

<style></style>
