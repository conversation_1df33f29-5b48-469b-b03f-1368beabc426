<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-select
            :disabled="!searchObj.shipCode"
            v-model="searchObj.firstId"
            label="设备主体"
            outlined
            dense
            :items="firstEquipment"
            :loading="firstLoading"
            clearable
          ></v-select>
        </v-col>
        <!-- <v-col cols="12" sm="6" md="3">
          <v-select
            :disabled="!searchObj.firstId"
            v-model="searchObj.secondId"
            label="大模块"
            outlined
            dense
            :items="secondEquipment"
            :loading="secondLoading"
            clearable
          ></v-select>
        </v-col> -->
      </template>
      <template #btns>
        <v-btn
          v-permission="['子设备:新增']"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="addItem"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          v-permission="['子设备:删除']"
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipInfo`]="{ item }">
        {{ item.shipInfo == null ? '' : item.shipInfo.chShipName }}
      </template>
    </v-table-searchable>
    <sub-engine-add
      v-model="dialog"
      @success="success"
      :initialData="initialData"
    ></sub-engine-add>
  </v-container>
</template>
<script>
import subEngineAdd from './sub-engine-add.vue'
// drawId	图纸id	string
// drawSerialNo	图纸编号	string
// equipmentFirstName		string
// equipmentSecondName		string
// firstId	设备主体id	string
// id	物理主键	string
// remark	备注	string
// secondId	大功能模块id	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// subEquipmentCname	子设备中文名称	string
// subEquipmentEname	子设备英文名称	string
export default {
  components: { subEngineAdd },
  name: 'sub-engine-list',
  created() {
    this.tableName = '子设备管理'
    this.reqUrl = '/business/shipAffairs/equipmentInformation/ThirdPage'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '英文名称', value: 'subEquipmentEname' },
      // { text: '中文名称', value: 'subEquipmentCname' },
      { text: '设备主体', value: 'equipmentFirstName' },
      // { text: '大模块', value: 'equipmentSecondName' },
      { text: '图纸编号', value: 'drawingSerialNumber' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊查询'
    this.pushParams = { name: 'sub-engine-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {},
      firstEquipment: [],
      secondEquipment: [],
      firstLoading: false,
      secondLoading: false,
      dialog: false,
      initialData: {},
    }
  },
  watch: {
    'searchObj.shipCode': {
      handler(val) {
        if (val) this.loadFirstEquipment()
      },
    },
    'searchObj.firstId': {
      handler(val) {
        if (val) this.loadSecondEquipment()
      },
    },
  },

  methods: {
    async loadFirstEquipment() {
      this.firstLoading = true
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { shipCode: this.searchObj.shipCode, size: 2000 },
      )
      const { records } = data
      this.firstEquipment = records?.map((i) => {
        return {
          text: i.equipmentEname,
          value: i.id,
        }
      })
      this.firstLoading = false
    },
    async loadSecondEquipment() {
      this.secondLoading = true
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentSecondByMainEquipmentId',
        { MainEquipmentId: this.searchObj.firstId },
      )
      this.secondEquipment = data?.map((i) => {
        return {
          text: i.name,
          value: i.id,
        }
      })
      this.secondLoading = false
    },
    async success(id) {
      await this.$refs.table.loadTableData()
      this.$router.push({ name: 'sub-engine-detail', params: { id } })
    },
    addItem() {
      this.dialog = true
      this.initialData = {}
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/equipmentInformation/deleteThird',
        [this.selected.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success(`删除成功`)
        this.selected = false
        this.$refs.table.loadTableData()
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
