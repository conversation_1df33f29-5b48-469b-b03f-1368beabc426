<template>
  <v-dialog
    attach="#mask"
    @input="(val) => $store.commit('setMaskLayer', val)"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新增子设备
        <v-spacer></v-spacer>
        <v-btn
          :loading="loading"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          v-permission="['设备主体管理:编辑']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          保存
        </v-btn>
        <v-btn
          :loading="loading"
          small
          outlined
          tile
          class="mx-1"
          @click="closeForm"
        >
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12">
                <v-alert type="info" color="green" text dense class="mb-0">
                  部分设备不存在特定的图纸号，以设备型号+设备说明书页码拼接为图纸号
                  <br />
                  图纸号不允许出现简单的四位数字组合
                </v-alert>
              </v-col>
            </v-row>
            <v-row>
              <v-col md="3" cols="12">
                <v-ship-select
                  v-model="formData.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col md="3" cols="12">
                <engine-select
                  :disabled="!formData.shipCode"
                  v-model="formData.firstId"
                  :shipCode="formData.shipCode"
                  :equimentModel.sync="formData.equimentModel"
                  :rules="[rules.required]"
                ></engine-select>
              </v-col>
              <!-- <v-col md="3" cols="12">
                <v-select
                  label="设备所属大模块"
                  :disabled="!formData.firstId"
                  v-model="formData.secondId"
                  outlined
                  dense
                  :items="secondEquipments"
                ></v-select>
              </v-col> -->
              <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  label="子设备英文名称"
                  v-model="formData.subEquipmentEname"
                  dense
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <v-select
                  v-model="formData.hasDrewNo"
                  :items="[
                    { text: '是', value: 1 },
                    { text: '否', value: 2 },
                  ]"
                  label="是否存在图纸号"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-select>
              </v-col>
              <!-- <v-col md="3" cols="12">
                <v-text-field
                  outlined
                  label="子设备中文名称"
                  v-model="formData.subEquipmentCname"
                  dense
                ></v-text-field>
              </v-col> -->
              <v-col
                md="3"
                cols="12"
                v-if="formData.hasDrewNo != null && formData.hasDrewNo == 1"
              >
                <v-text-field
                  outlined
                  label="图纸号"
                  v-model="formData.drawingSerialNumber"
                  dense
                  :rules="[rules.required]"
                  required
                ></v-text-field>
                <!-- :rules="[rules.required, rules.minLength]" -->
              </v-col>
              <v-col
                md="3"
                cols="12"
                v-if="formData.hasDrewNo != null && formData.hasDrewNo == 2"
              >
                <v-text-field
                  outlined
                  label="设备型号"
                  v-model="formData.equimentModel"
                  dense
                  readonly
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col
                md="3"
                cols="12"
                v-if="formData.hasDrewNo != null && formData.hasDrewNo == 2"
              >
                <v-text-field
                  outlined
                  label="说明书页码"
                  v-model="formData.pageNo"
                  dense
                  :rules="[rules.required, rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col
                md="3"
                cols="12"
                v-if="formData.hasDrewNo != null && formData.hasDrewNo == 2"
              >
                <v-text-field
                  outlined
                  label="图纸号(自动拼接型号+页码)"
                  v-model="formData.drawingSerialNumber"
                  dense
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="备注"
                  v-model="formData.remark"
                  dense
                  :rules="[rules.required]"
                  required
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
          <v-attach-list
            title="图纸附件"
            :attachments="formData.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-form>
      </v-card-text>
      <!-- 重复数据校验及引用 -->
      <v-dialog
        v-model="dialogDuplicate"
        max-width="1500"
        hide-overlay
        attach="#mask"
      >
        <v-card>
          <v-card-title class="text-h5">图纸号相同子设备确认</v-card-title>
          <v-card-text>
            <v-alert type="info" color="green" text dense class="mb-0">
              其他船舶存在该图纸号子设备，是否直接引用。
              <br />
              如选择“引用选中子设备数据”，该子设备以及下属备件信息全部一并带入；
              <br />
              或选择“不引用，保存当前录入数据”，自修录入所需备件数据。
            </v-alert>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn depressed color="primary" @click="closeForm2">取消</v-btn>
            <v-btn depressed color="primary" @click="confirm">
              不引用，保存当前录入数据
            </v-btn>
            <v-btn depressed color="primary" @click="confirmCopy">
              引用选中子设备数据
            </v-btn>
          </v-card-actions>
          <v-card-text>
            <v-table-list
              :table-name="''"
              v-model="selectedCopy"
              :headers="headers"
              :items="duplicateList"
            >
              <template v-slot:[`item.shipInfo`]="{ item }">
                {{ item.shipInfo == null ? '' : item.shipInfo.chShipName }}
              </template>
            </v-table-list>
          </v-card-text>
          <!-- <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn depressed @click="closeForm2">取消</v-btn>
            <v-btn depressed color="primary" @click="confirm">确定</v-btn>
          </v-card-actions> -->
        </v-card>
      </v-dialog>
    </v-card>
  </v-dialog>
</template>

<script>
import engineSelect from '../apply/private/engine-select.vue'
//   "drawId": "",
//   "firstId": "",
//   "id": "",
//   "remark": "",
//   "secondId": "",
//   "shipCode": "",
//   "subEquipmentCname": "",
//   "subEquipmentEname": ""
export default {
  components: { engineSelect },
  name: 'sub-engine-add',
  model: {
    prop: 'open',
    event: 'change',
  },
  created() {
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '英文名称', value: 'subEquipmentEname' },
      // { text: '中文名称', value: 'subEquipmentCname' },
      { text: '设备主体', value: 'equipmentFirstName' },
      // { text: '大模块', value: 'equipmentSecondName' },
      { text: '图纸编号', value: 'drawingSerialNumber' },
      { text: '备注', value: 'remark' },
    ]
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: { equimentModel: '' },
      secondEquipments: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        minLength: (v) => {
          // 当输入为空或等于0时，不执行长度验证
          if (!v || v === 0) return true

          // 将输入值转换为字符串，并去除可能的前导零
          const strValue = String(v).replace(/\s*0+/, '')

          // 验证字符串长度是否大于4   数字不能是4位及以下
          return strValue.length > 4 || '图纸号长度需大于4'
        },
        customRequired: (v) => {
          // 检查是否为空或不是有效的输入（这里假设0是有效输入）
          if (!v && v !== 0) {
            return '必填项不能为空'
          }

          // 检查是否为4位或更少的纯数字
          const isShortNumber = /\d{1,4}$/.test(v)
          if (isShortNumber) {
            return '数字不能是4位及以下'
          }

          // 如果不是纯数字或者数字位数超过4位，则视为有效输入
          return true
        },
      },
      draw: {},
      dialogDuplicate: false,
      duplicateList: [],
      yinYong: false,
      selectedCopy: false,
      loading: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.formData.attachmentRecords = []
      this.draw = {}
    },
    'formData.firstId'(val) {
      if (val) this.loadSecondEquipments(val)
    },
    'formData.equimentModel'(val) {
      console.log(val)
      this.genDrawingSerialNumber()
    },
    'formData.pageNo'(val) {
      console.log(val)
      this.genDrawingSerialNumber()
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      if (this.formData.drawingSerialNumber.toLowerCase().includes('page')) {
        this.$dialog.message.success(`图纸号不允许填写page，保存失败！`)
        return
      }
      const isNumeric = Number.isInteger(
        Number(this.formData.drawingSerialNumber),
      )
      if (isNumeric) {
        if (this.formData.drawingSerialNumber.length <= 4) {
          this.$dialog.message.success(`图纸号不允许4位及以下数字`)
          return
        }
      }
      this.loading = true
      if (!(await this.checkDuplicate())) {
        return
      }
      // const { data } = await this.postAsync(
      //   '/business/shipAffairs/equipmentInformation/saveOrUpdateDrawing',
      //   {
      //     attachmentIds: [],
      //     drawingSerialNumber: this.draw.drawingSerialNumber,
      //     shipCode: this.formData.shipCode,
      //   },
      // )
      // if (!data) return
      if (this.yinYong) {
        console.log(55555555555555)
        this.formData.copyEdId = this.selectedCopy.id
        const url =
          '/business/shipAffairs/equipmentInformation/saveOrUpdateThirdCopy'
        const res = await this.postAsync(url, {
          ...this.formData,
          // drawId: data,
        })
        this.loading = false
        if (!res.errorRaw) {
          this.$emit('change', false)
          this.$emit('success', res.data)
        }
      } else {
        const url =
          '/business/shipAffairs/equipmentInformation/saveOrUpdateThird'
        const res = await this.postAsync(url, {
          ...this.formData,
          // drawId: data,
        })
        this.loading = false
        if (!res.errorRaw) {
          this.$emit('change', false)
          this.$emit('success', res.data)
        }
      }
    },
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async loadSecondEquipments(MainEquipmentId) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentSecondByMainEquipmentId',
        {
          MainEquipmentId,
        },
      )
      this.secondEquipments = data.map((i) => ({ text: i.name, value: i.id }))
    },
    genDrawingSerialNumber() {
      if (
        this.formData.equimentModel == undefined ||
        this.formData.pageNo == undefined
      ) {
        return
      }
      this.formData.drawingSerialNumber =
        this.formData.equimentModel + '+' + this.formData.pageNo
    },
    closeForm2() {
      this.dialogDuplicate = false
      this.loading = false
      this.resolveFn(false)
    },

    async confirm() {
      this.dialogDuplicate = false
      this.yinYong = false
      this.resolveFn(true)
    },
    async confirmCopy() {
      if (!this.selectedCopy) {
        this.$dialog.message.error('请勾选记录！')
        return
      }
      console.log(this.selectedCopy)
      this.dialogDuplicate = false
      this.yinYong = true
      this.resolveFn(true)
    },
    async checkDuplicate() {
      const re0 = this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentThirdByDrewNo',
        {
          drawingSerialNumber: this.formData.drawingSerialNumber,
        },
      )
      console.log(***********)
      const [res0] = await Promise.all([re0])
      console.log(222222222222)
      if (res0.data.length) {
        this.duplicateList = res0.data
        this.dialogDuplicate = true
      } else {
        console.log(res0.data.length)
        const url =
          '/business/shipAffairs/equipmentInformation/saveOrUpdateThird'
        const res = await this.postAsync(url, {
          ...this.formData,
          // drawId: data,
        })
        this.loading = false
        if (!res.errorRaw) {
          this.$emit('change', false)
          this.$emit('success', res.data)
        }
      }
      console.log(33333333333333)
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },
  },

  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
