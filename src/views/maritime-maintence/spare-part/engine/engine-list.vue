<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-btn
          :loading="loading"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          v-permission="['设备主体管理:编辑']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          保存
        </v-btn>
        <v-btn
          :loading="loading"
          small
          outlined
          tile
          class="mx-1"
          @click="closeForm"
        >
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="pb-0 px-0" fluid>
            <v-row>
              <v-col
                class="py-0"
                cols="12"
                :md="['equipmentNumber', 'parameter'].includes(h.value) ? 4 : 2"
                v-for="(h, i) in headers"
                :key="i"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfo'"
                  v-model="formData['shipCode']"
                  :rules="[rules.required]"
                  required
                  :clearable="false"
                ></v-ship-select>
                <vs-date-picker
                  v-else-if="['productDate', 'repairDate'].includes(h.value)"
                  v-model="formData[h.value]"
                  :label="h.text"
                  outlined
                  dense
                ></vs-date-picker>
                <v-select
                  v-else-if="h.value === 'equipmentStatus'"
                  v-model="formData[h.value]"
                  dense
                  outlined
                  :label="'*' + h.text"
                  :rules="[rules.required]"
                  required
                  :headers="moduleHeaders"
                  :items="equipmentStatuses"
                ></v-select>
                <v-select
                  dense
                  outlined
                  v-else-if="h.value === 'euipmentType'"
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  :rules="[rules.required]"
                  required
                  :items="euipmentTypes"
                ></v-select>
                <v-ship-station
                  v-else-if="h.value === 'supervisorPosition'"
                  v-model="formData[h.value]"
                  :label="h.text"
                ></v-ship-station>
                <v-combobox
                  v-else-if="h.value === 'equipmentNumber'"
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  multiple
                  outlined
                  small-chips
                  dense
                  hint="输入设备编号后回车即保存"
                  :rules="[rules.required]"
                ></v-combobox>
                <v-text-field
                  v-else-if="h.value === 'parameter'"
                  outlined
                  dense
                  v-model="formData[h.value]"
                  :label="h.text"
                ></v-text-field>
                <v-text-field
                  v-else-if="h.value === 'amount'"
                  outlined
                  dense
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  :rules="[rules.required]"
                  type="number"
                ></v-text-field>
                <v-text-field
                  v-else-if="h.value === 'equimentModel'"
                  outlined
                  dense
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  :rules="[rules.required]"
                ></v-text-field>
                <v-supply-select-list
                  v-else-if="h.value === 'originalSupplier'"
                  v-model="formData[h.value]"
                  :label="原厂供应商"
                  :init-selected="initSupply"
                  outlined
                  clearable
                  dense
                ></v-supply-select-list>
                <!-- <v-select
                  dense
                  outlined
                  v-else-if="h.value === 'standardType'"
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  required
                  :items="standardTypes"
                ></v-select> -->
                <v-dict-select
                  v-else-if="h.value === 'standardType'"
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  clearable
                  dense
                  outlined
                  dict-type="equipment_standard_ype"
                ></v-dict-select>
                <v-text-field
                  v-else-if="h.value !== 'remark'"
                  outlined
                  dense
                  v-model="formData[h.value]"
                  :label="'*' + h.text"
                  :rules="[rules.required]"
                  required
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="formData.remark"
                  label="备注"
                  outlined
                  dense
                ></v-textarea>
              </v-col>
              <v-col class="py-0" cols="12">
                <v-card-text>
                  <v-attach-list
                    :attachments="formData.attachmentRecords"
                    @change="(ids) => (formData.attachmentIds = ids)"
                    title="图纸"
                    :ship-code="formData.shipCode"
                  ></v-attach-list>
                </v-card-text>
              </v-col>
              <!-- <v-col class="my-0 py-0" cols="12">
                <v-card-title class="pt-0">
                  <v-spacer></v-spacer>
                  <v-btn
                    outlined
                    tile
                    small
                    color="success"
                    class="mx-1"
                    @click="createModule"
                    v-permission="['设备主体管理:新增']"
                  >
                    <v-icon left>mdi-plus-circle</v-icon>
                    新增
                  </v-btn>
                  <v-btn
                    :disabled="!selectedEngine"
                    outlined
                    small
                    tile
                    color="warning"
                    class="mx-1"
                    @click="editModule"
                    v-permission="['设备主体管理:修改']"
                  >
                    <v-icon left>mdi-pencil</v-icon>
                    修改
                  </v-btn>
                  <v-btn
                    :disabled="!selectedEngine"
                    outlined
                    small
                    tile
                    color="error"
                    class="mx-1"
                    @click="delModule"
                    v-permission="['设备主体管理:删除']"
                  >
                    <v-icon left>mdi-delete-empty</v-icon>
                    删除
                  </v-btn>
                </v-card-title>

                <v-table-list
                  v-model="selectedEngine"
                  :headers="moduleHeaders"
                  :items="engineModules"
                ></v-table-list>
              </v-col> -->
            </v-row>

            <v-dialog
              v-model="dialog"
              max-width="1500"
              hide-overlay
              attach="#mask"
            >
              <v-card>
                <v-card-title class="text-h5">型号相同设备确认</v-card-title>
                <v-card-text>
                  <v-alert type="info" color="green" text dense class="mb-0">
                    其他船舶存在该型号设备，是否直接引用。
                    <br />
                    如选择“引用选中设备数据”，该设备以及下属所有子设备、备件信息全部一并带入；
                    <br />
                    或选择“不引用，保存当前录入数据”，自修录入所需子设备、备件数据。
                  </v-alert>
                </v-card-text>
                <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn depressed color="primary" @click="closeForm2">
                    取消
                  </v-btn>
                  <v-btn depressed color="primary" @click="confirm">
                    不引用，保存当前录入数据
                  </v-btn>
                  <v-btn depressed color="primary" @click="confirmCopy">
                    引用选中设备数据
                  </v-btn>
                </v-card-actions>
                <v-card-text>
                  <v-table-list
                    :table-name="''"
                    v-model="selectedCopy"
                    :headers="headers"
                    :items="duplicateList"
                  >
                    <template v-slot:[`item.shipInfo`]="{ item }">
                      {{
                        item.shipInfo == null ? '' : item.shipInfo.chShipName
                      }}
                    </template>
                  </v-table-list>
                </v-card-text>
                <!-- <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn depressed @click="closeForm2">取消</v-btn>
                  <v-btn depressed color="primary" @click="confirm">确定</v-btn>
                </v-card-actions> -->
              </v-card>
            </v-dialog>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      @dbclick="editItem"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['设备主体管理编辑:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editItem"
          v-permission="['设备主体管理编辑:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          @click="openCopy"
          v-permission="['设备主体管理编辑:复制']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          设备主体复制
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['设备主体管理编辑:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipInfo`]="{ item }">
        {{ item.shipInfo == null ? '' : item.shipInfo.chShipName }}
      </template>
      <template v-slot:[`item.equipmentStatus`]="{ item }">
        {{ ['', '使用', '停用', '报废'][item.equipmentStatus] }}
      </template>
      <template v-slot:[`item.euipmentType`]="{ item }">
        {{ ['主机', '副机', '辅助设备', '通导设备'][item.euipmentType] }}
      </template>
      <template v-slot:[`item.equipmentNumber`]="{ item }">
        <v-chip-group color="primary" column>
          <v-chip small v-for="tag in item.equipmentNumber" :key="tag">
            {{ tag }}
          </v-chip>
        </v-chip-group>
      </template>
    </v-table-searchable>
    <moudle-dialog
      v-model="moduleFormShow"
      :initialData="module"
      @success="loadModules"
      @add="addModule"
      @update="updateModule"
    ></moudle-dialog>
    <sub-engine-copy-dialog v-model="copyDialog"></sub-engine-copy-dialog>
  </v-container>
</template>
<script>
import moudleDialog from './private/moudle-dialog.vue'
import subEngineCopyDialog from './private/sub-engine-copy-dialog.vue'
export default {
  components: { moudleDialog, subEngineCopyDialog },
  name: 'engine-list',
  created() {
    this.tableName = '设备主体'
    this.reqUrl = '/business/shipAffairs/equipmentInformation/firstPage'
    this.headers = [
      { text: '所属船舶', value: 'shipInfo' },
      { text: '设备英文名称', value: 'equipmentEname' },
      // { text: '设备中文名称', value: 'equipmentCname' },
      // { text: '设备编码', value: 'equipmentCode' },
      { text: '设备型号', value: 'equimentModel' },
      { text: '状态', value: 'equipmentStatus' },
      { text: '类型', value: 'euipmentType' },
      // { text: '安装位置', value: 'installationSite', hideDefault: true },
      { text: '生产厂家', value: 'manufacture', hideDefault: true },
      { text: '出厂日期', value: 'productDate', hideDefault: true },
      { text: '上次大修日期', value: 'repairDate', hideDefault: true },
      { text: '主管岗位', value: 'supervisorPosition' },
      { text: '现存数量', value: 'amount' },
      { text: '设备核心参数', value: 'parameter', hideDefault: true },
      { text: '设备序列号', value: 'equipmentNumber', hideDefault: true },
      { text: '原厂供应商', value: 'originalSupplier', hideDefault: true },
      { text: '标准化类别', value: 'standardType', hideDefault: true },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = '模糊查询'
    this.equipmentStatuses = [
      { text: '使用', value: 1 },
      { text: '停用', value: 2 },
      { text: '报废', value: 3 },
    ]
    this.euipmentTypes = [
      { text: '主机', value: '0' },
      { text: '副机', value: '1' },
      { text: '辅助设备', value: '2' },
      { text: '通导设备', value: '3' },
    ]
    this.standardTypes = [
      { text: '标准1', value: '1' },
      { text: '标准2', value: '2' },
      { text: '标准3', value: '3' },
      { text: '标准4', value: '4' },
    ]
    this.moduleHeaders = [{ text: '大模块名称', value: 'name' }]
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      selectedEngine: false,
      engineModules: [],
      module: {},
      moduleFormShow: false,
      copyDialog: false,
      initSupply: {},
      dialog: false,
      duplicateList: [],
      yinYong: false,
      selectedCopy: false,
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/equipmentInformation/deleteFirst',
        [this.selected.id],
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(errorRaw.msg)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        equipmentNumber: [],
      }
      this.initSupply = {}
      this.formShow = true
      this.$refs.table.disabled = true
      document.documentElement.scrollTop = 0
    },
    async editItem() {
      this.formData = {
        ...this.selected,
        shipCode: this.selected.shipInfo.shipCode,
      }
      this.initSupply = {
        id: this.selected.originalSupplier,
        name: this.selected.originalSupplierName,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      document.documentElement.scrollTop = 0
      await this.loadModules()
    },

    async save() {
      if (!this.$refs.form.validate()) return
      this.loading = true
      if (!this.isEdit) {
        if (!(await this.checkDuplicate())) {
          return
        }
      }
      if (this.yinYong) {
        let formData2 = new FormData()
        formData2.append('mainId', this.selectedCopy.id)
        formData2.append('shipCode', this.formData.shipCode)
        formData2.append('equipmentNumber', this.formData.equipmentNumber)
        const { errorRaw } = await this.postAsync(
          `/business/shipAffairs/equipmentInformation/copyEquipmentInformation`,
          formData2,
          // {
          //   mainId: this.selected.map((item) => item.id),
          //   shipCode: this.targetShipCode,
          // },
        )
        this.loading = false
        if (errorRaw) {
          this.$dialog.message.error(errorRaw.msg)
          return
        }
        this.$dialog.message.success('引用成功')
        await this.$refs.table.loadTableData()
        this.closeForm()
      } else {
        const reqUrl = this.isEdit
          ? '/business/shipAffairs/equipmentInformation/saveOrUpdateFirst'
          : '/business/shipAffairs/equipmentInformation/saveMainAndSecond'
        const { errorRaw } = await this.postAsync(
          reqUrl,
          this.isEdit
            ? this.formData
            : {
                equipmentInformationModifyDTO: this.formData,
                equipmentSecondModifyDTO: this.engineModules,
              },
          false,
        )
        this.loading = false
        if (errorRaw) {
          this.$dialog.message.error(errorRaw.msg)
          return
        }
        this.$dialog.message.success(`保存成功`)
        await this.$refs.table.loadTableData()
        this.closeForm()
      }
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = { equipmentNumber: [] }
      this.engineModules = []
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },

    async loadModules(newModule) {
      if (!this.isEdit) {
        this.engineModules.push(newModule)
        return
      }
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentSecondByMainEquipmentId',
        { MainEquipmentId: this.formData.id },
      )
      this.engineModules = data
    },
    addModule(newModule) {
      this.engineModules.push(newModule)
    },
    updateModule(newModule) {
      this.engineModules = this.engineModules.map((item) =>
        item.id === newModule.id ? newModule : item,
      )
    },
    createModule() {
      this.module = {
        shipCode: this.formData.shipCode,
        equipmentCode: this.formData.equipmentCode,
        mainEquipmentId: this.formData.id,
      }
      this.moduleFormShow = true
    },
    editModule() {
      this.module = {
        ...this.selectedEngine,
        equipmentCode: this.formData.equipmentCode,
        shipCode: this.formData.shipCode,
      }
      this.moduleFormShow = true
    },
    async delModule() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      if (!this.formData.id) {
        this.engineModules = this.engineModules.filter(
          (item) => item.id !== this.selectedEngine.id,
        )
        return
      }
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/equipmentInformation/deleteSecond',
        [this.selectedEngine.id],
      )
      if (!errorRaw) {
        this.$dialog.message.success(`删除成功`)
        await this.loadModules()
        return
      }
    },
    openCopy() {
      this.copyDialog = true
    },
    closeForm2() {
      this.dialog = false
      this.loading = false
      this.resolveFn(false)
    },

    async confirm() {
      this.dialog = false
      this.yinYong = false
      this.resolveFn(true)
    },
    async confirmCopy() {
      if (!this.selectedCopy) {
        this.$dialog.message.error('请勾选记录！')
        return
      }
      console.log(this.selectedCopy)
      this.dialog = false
      this.yinYong = true
      this.resolveFn(true)
    },
    async checkDuplicate() {
      const re0 = this.getAsync(
        '/business/shipAffairs/equipmentInformation/mainByModel',
        {
          equimentModel: this.formData.equimentModel,
        },
      )
      console.log(***********)
      const [res0] = await Promise.all([re0])
      console.log(222222222222)
      if (res0.data.length) {
        this.duplicateList = res0.data
        this.dialog = true
      } else {
        const reqUrl = this.isEdit
          ? '/business/shipAffairs/equipmentInformation/saveOrUpdateFirst'
          : '/business/shipAffairs/equipmentInformation/saveMainAndSecond'
        const { errorRaw } = await this.postAsync(
          reqUrl,
          this.isEdit
            ? this.formData
            : {
                equipmentInformationModifyDTO: this.formData,
                equipmentSecondModifyDTO: this.engineModules,
              },
          false,
        )
        this.loading = false
        if (errorRaw) {
          this.$dialog.message.error(errorRaw.msg)
          return
        }
        this.$dialog.message.success(`保存成功`)
        await this.$refs.table.loadTableData()
        this.closeForm()
      }
      console.log(33333333333333)
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },
  },
  watch: {
    copyDialog(val) {
      if (!val) {
        this.$refs.table.loadTableData()
        console.log(1)
      }
    },
  },
  mounted() {},
}
</script>

<style></style>
