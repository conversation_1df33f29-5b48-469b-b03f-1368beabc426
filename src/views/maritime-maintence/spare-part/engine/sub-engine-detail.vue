<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['子设备管理:编辑']"
      :title="`子设备详情-${detail.subEquipmentEname}`"
      :tooltip="detail.subEquipmentEname"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #设备主体信息>
        <v-card-text>
          <v-row>
            <v-col cols="3">
              <b>所属船舶:</b>
              {{ detail.shipInfo.chShipName }}
            </v-col>
            <v-col cols="3">
              <b>设备型号:</b>
              {{ detail.equipmentInformation.equimentModel }}
            </v-col>
            <!-- <v-col cols="3">
              <b>设备中文名称:</b>
              {{ detail.equipmentInformation.equipmentCname }}
            </v-col> -->
            <v-col cols="3">
              <b>设备英文名称:</b>
              {{ detail.equipmentInformation.equipmentEname }}
            </v-col>
            <!-- <v-col cols="3">
              <b>设备编码:</b>
              {{ detail.equipmentInformation.equipmentCode }}
            </v-col> -->
            <v-col cols="3">
              <b>设备状态:</b>
              {{
                ['', '使用', '停用', '报废'][
                  detail.equipmentInformation.equipmentStatus
                ]
              }}
            </v-col>
            <!-- <v-col cols="3">
              <b>安装位置:</b>
              {{ detail.equipmentInformation.installationSite }}
            </v-col> -->
            <v-col cols="3">
              <b>生产厂家:</b>
              {{ detail.equipmentInformation.manufacture }}
            </v-col>
            <v-col cols="3">
              <b>设备类型:</b>
              {{
                ['主机', '副机', '辅助设备', '通导设备'][
                  detail.equipmentInformation.euipmentType
                ]
              }}
            </v-col>
            <v-col cols="6">
              <b>设备核心参数:</b>
              {{ detail.equipmentInformation.parameter }}
            </v-col>
            <v-col cols="6">
              <b>设备序列号:</b>
              <v-chip
                x-small
                v-for="tag in detail.equipmentInformation.equipmentNumber"
                :key="tag"
              >
                {{ tag }}
              </v-chip>
            </v-col>
          </v-row>
        </v-card-text>
      </template>
      <!-- <template #大模块信息>
        <v-card-text>
          <b>所属大模块:</b>
          {{
            detail.equipmentSecond
              ? detail.equipmentSecond.name
              : '未绑定大模块'
          }}
        </v-card-text>
      </template> -->
      <template #子设备信息>
        <v-form>
          <v-container fluid>
            <v-row>
              <!-- <v-col md="3" cols="12">
                <v-text-field
                  label="子设备中文名称"
                  v-model="detail.subEquipmentCname"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-text-field>
              </v-col> -->
              <v-col md="3" cols="12">
                <v-text-field
                  label="子设备英文名称"
                  v-model="detail.subEquipmentEname"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col md="3" cols="12">
                <!-- :disabled="!detail.drawingSerialNumberEmpty && !isShip"
                 如已生成SAP备件编码，图纸号可修改 -->
                <!-- :disabled="detail.hasChangeSap" 放开修改，保留操作记录 -->
                <v-text-field
                  label="图纸号"
                  v-model="detail.drawingSerialNumber"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  label="备注"
                  v-model="detail.remark"
                  dense
                  outlined
                ></v-textarea>
              </v-col>
              <v-expansion-panels multiple accordion v-model="panel" focusable>
                <v-expansion-panel>
                  <v-expansion-panel-header style="color: #3399cc">
                    子设备修改记录
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-card-text>
                      <v-table-searchable
                        ref="table"
                        table-name="修改明细"
                        :headers="thirdHeaders"
                        :req-url="reqUrl"
                        :fix-header="false"
                        :search-remain="searchObj"
                      ></v-table-searchable>
                    </v-card-text>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #备件信息按钮>
        <span style="color: red">如图纸号为空，请保存图纸号后再新增备件！</span>
        <v-btn
          :disabled="
            !selectedComponents.length || selectedComponents.length > 1
          "
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateThirdId"
          v-permission="['备件信息:变更子设备']"
        >
          <v-icon left>mdi-pencil</v-icon>
          变更子设备
        </v-btn>
        <v-btn
          :disabled="
            !selectedComponents.length || selectedComponents.length > 1
          "
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateStopUse"
          v-permission="['备件信息:变更状态']"
        >
          <v-icon left>mdi-pencil</v-icon>
          变更状态
        </v-btn>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCompent"
          :disabled="detail.drawingSerialNumberEmpty"
          v-permission="['备件信息:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="
            !selectedComponents.length || selectedComponents.length > 1
          "
          small
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="updateCompent"
          v-permission="['备件信息:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!selectedComponents.length"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCom"
          v-permission="['备件信息:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #备件信息>
        <v-data-table
          :headers="componentHeaders"
          :items="components"
          v-model="selectedComponents"
          item-value="id"
          show-expand
          show-select
        >
          <template v-slot:[`item.componentProperty`]="{ item }">
            {{
              ['普通备件', 'SAP备件', '固定资产', '通导设备固定资产'][
                item.componentProperty
              ]
            }}
          </template>
          <template v-slot:[`item.stopUse`]="{ item }">
            <span style="color: red" v-if="item.stopUse == 1">停用</span>
            <span v-if="item.stopUse == 0">启用</span>
          </template>
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length">
              <componentLogList :item-id="item.id" />
              <!-- <cost-sap-mes-002
            v-if="item.sapType == 'JMM002'"
            :item-id="item.id"
          />
          <cost-sap-mes-003
            v-if="item.sapType == 'JMM003'"
            :item-id="item.id"
          /> -->
            </td>
          </template>
        </v-data-table>
        <v-card-text>
          <v-attach-list
            title="图纸附件"
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <component-dialog
      v-model="comDialog"
      :initialData="initData"
      @success="loadComponents"
    ></component-dialog>
    <component-third-dialog-select
      v-model="comThirdDialog"
      :searchInitial="searchInitialObj"
    ></component-third-dialog-select>
    <v-dialog v-model="updateUseDialog" max-width="600">
      <template v-slot:default="updateUseDialog">
        <v-card style="height: 320px">
          <v-card-title>
            修改状态
            <v-spacer></v-spacer>
            <v-btn
              :loading="loading"
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveStopUse"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              保存
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="updateUseDialog.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-text-field
                label="备件名称"
                v-model="updateUseData.componentEname"
                dense
                readonly
                required
                outlined
              ></v-text-field>
            </v-row>
            <v-row>
              <v-text-field
                label="备件号"
                v-model="updateUseData.componentNumber"
                dense
                readonly
                required
                outlined
              ></v-text-field>
            </v-row>
            <v-row>
              <v-select
                label="状态"
                v-model="updateUseData.stopUse"
                dense
                outlined
                :items="[
                  { text: '停用', value: true },
                  { text: '启用', value: false },
                ]"
              ></v-select>
            </v-row>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
  </v-container>
</template>
<script>
import componentDialog from './private/component-dialog.vue'
import componentLogList from './private/component-log-list.vue'
import componentThirdDialogSelect from './private/component-third-dialog-select.vue'
// componentCname	备件中文名称	string
// componentEname	备件英文名称	string
// componentModel	型号	string
// componentNumber	备件号	string
// componentParameter	参数	string
// componentSpecifications	规格	string
// componentUnit	单位	string
// drawingNo	在图编号	string
// equipmentId	绑定子设备id	string
// expenseAccountId	费用科目id	string
// id	物理主键	string
// isFixedAssets	是否固定资产	boolean
// isSapComponent	是否SAP备件	boolean
// maximumInventory	最高库存量	integer
// remark	备注	string
// sapComponentNumber	SAP备件编码	string
// shipCode	船舶编码	string
export default {
  components: { componentDialog, componentLogList, componentThirdDialogSelect },
  name: 'sub-engine-detail',
  created() {
    this.reqUrl = '/business/shipAffairs/equipmentInformation/componentLogPage'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.backRouteName = 'sub-engine-list'
    // this.subtitles = ['设备主体信息', '大模块信息', '子设备信息', '备件信息']
    this.subtitles = ['设备主体信息', '子设备信息', '备件信息']
    this.componentHeaders = [
      { text: '', value: 'data-table-expand' },
      // { text: '在图编号', value: 'drawingNo' },
      { text: '备件名称', value: 'componentEname' },
      // { text: '型号', value: 'componentModel' },
      { text: '备件号', value: 'componentNumber' },
      { text: '备件唯一标识符', value: 'sapComponentNumberNew' },
      { text: 'NOx编号', value: 'nox' },
      // { text: '参数', value: 'componentParameter' },
      // { text: '规格', value: 'componentSpecifications' },
      { text: '单位', value: 'componentUnit' },
      { text: '备件类型', value: 'componentProperty' },
      { text: '费用科目', value: 'costSubjectName' },
      // { text: 'SAP编码', value: 'sapComponentNumber' },
      { text: '标准化类别', value: 'standardType' },
      { text: '最高库存', value: 'maximumInventory' },
      { text: '最低库存', value: 'minimumInventory' },
      { text: '备注', value: 'remark' },
      { text: '状态', value: 'stopUse' },
    ]
    this.thirdHeaders = [
      { text: '子设备英文名称', value: 'componentEname' },
      { text: '图纸号', value: 'drawingNo' },
      { text: '备注', value: 'remark' },
      { text: '操作人', value: 'personName' },
      { text: '操作时间', value: 'operateTime' },
    ]
  },
  data() {
    return {
      detail: {
        drawSerialNo: '',
        subEquipmentCname: '',
        subEquipmentEname: '',
        equipmentInformation: {},
        equipmentSecond: {},
        shipInfo: {
          chShipName: '',
        },
        drawingSerialNumber: '',
        commonAttachments: [],
        attachmentIds: [],
        panel: [0], //根据需要调整panel数组中的值，以控制哪些面板是展开的
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      components: [],
      selectedComponents: [],
      comDialog: false,
      initData: {},
      draw: {
        drawingSerialNumber: '',
        remark: '',
        attachmentIds: [],
      },
      searchObj: {
        componentId: this.$route.params.id,
      },
      comThirdDialog: false,
      searchInitialObj: {},
      updateUseDialog: false,
      updateUseData: {},
    }
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },
    async save(goBack) {
      // const { data } = await this.postAsync(
      //   '/business/shipAffairs/equipmentInformation/saveOrUpdateDrawing',
      //   this.draw,
      // )
      if (this.detail.drawingSerialNumber.toLowerCase().includes('page')) {
        this.$dialog.message.success(`图纸号不允许填写page，保存失败！`)
        return
      }
      const isNumeric = Number.isInteger(
        Number(this.detail.drawingSerialNumber),
      )
      if (isNumeric) {
        if (this.detail.drawingSerialNumber.length <= 4) {
          this.$dialog.message.success(`图纸号不允许4位及以下数字`)
          return
        }
      }
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/equipmentInformation/saveOrUpdateThird',
        {
          ...this.detail,
          // drawId: data,
          firstId: this.detail.equipmentInformation.id,
          secondId: this.detail.equipmentSecond?.id,
          shipCode: this.detail.shipInfo.shipCode,
        },
      )
      if (errorRaw) return
      goBack()
    },
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getThirdDetailById',
        { id: this.$route.params.id },
      )
      this.detail = data
      // if (this.detail.drawId) {
      //   const { data } = await this.getAsync(
      //     '/business/shipAffairs/equipmentInformation/getDrawingById',
      //     { id: this.detail.drawId },
      //   )
      //   this.draw = {
      //     ...data,
      //     shipCode: this.detail.shipInfo.shipCode,
      //   }
      // }
    },
    async loadComponents() {
      this.selectedComponents = []
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentComponentByThirdId',
        { thirdEquipmentId: this.$route.params.id },
      )
      this.components = data
    },
    createCompent() {
      this.initData = {
        equipmentId: this.$route.params.id,
        shipCode: this.detail.shipInfo.shipCode,
        euipmentType: this.detail.equipmentInformation.euipmentType,
        componentNumberEmpty: true,
      }
      this.comDialog = true
    },
    updateCompent() {
      if (!this.selectedComponents.length) return
      this.initData = {
        ...this.selectedComponents[0],
        euipmentType: this.detail.equipmentInformation.euipmentType,
        shipCode: this.detail.shipInfo.shipCode,
      }
      this.comDialog = true
    },
    updateThirdId() {
      if (!this.selectedComponents.length) return
      this.searchInitialObj = {
        // ...this.selectedComponents[0],
        firstId: this.selectedComponents[0].equipmentInformationId,
        shipCode: this.detail.shipInfo.shipCode,
        componentId: this.selectedComponents[0].id,
      }
      this.comThirdDialog = true
    },
    updateStopUse() {
      if (!this.selectedComponents.length) return
      this.updateUseData = {
        ...this.selectedComponents[0],
        firstId: this.selectedComponents[0].equipmentInformationId,
        shipCode: this.detail.shipInfo.shipCode,
      }
      console.log(this.updateUseData)
      this.updateUseDialog = true
    },
    async saveStopUse() {
      const url = '/business/shipAffairs/equipmentInformation/updateStopUse'
      const { errorRaw } = await this.postAsync(url, {
        ...this.updateUseData,
      })
      this.loading = false
      if (!errorRaw) {
        this.loadComponents()
        this.updateUseDialog = false
      }
    },
    async delCom() {
      if (!this.selectedComponents.length) return
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const idsToDelete = this.selectedComponents.map(
        (component) => component.id,
      )
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/equipmentInformation/deleteComponent',
        idsToDelete,
      )
      if (!errorRaw) {
        this.$dialog.message.success(`删除成功`)
        this.selectedComponents = []
        await this.loadComponents()
        return
      }
    },
  },

  mounted() {
    this.loadDetail()
    this.loadComponents()
  },
}
</script>

<style></style>
