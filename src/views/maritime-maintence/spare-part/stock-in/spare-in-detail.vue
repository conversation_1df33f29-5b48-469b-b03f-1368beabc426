<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['备件入库:编辑']"
      :title="`备件入库-${detail.inoutCode || '新增'}`"
      :tooltip="detail.inoutCode || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="canSubmit"
      @save="save"
      @submit="submit"
      can-print
      :can-save="canEdit"
    >
      <template v-if="!isEdit && detail.inoutMode == 0" #titlebtns>
        <!-- <v-btn
          width="120"
          tile
          @click="stockIn"
          color="success"
          small
          class="mx-1"
          v-permission="['备件入库:编辑']"
        >
          入库当前订单
        </v-btn> -->
      </template>
      <template
        v-if="
          detail.auditParams &&
          detail.auditParams.processInstanceId &&
          !isSupper
        "
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <!-- <v-col cols="12" md="12" class="py-0">
                <span style="color: red">
                  订单入库注意事项：一个订单只能入库一次，请货物到齐、并确认无误后再全部入库！
                </span>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  label="入库方式"
                  :disabled="isEdit"
                  dense
                  outlined
                  v-model="detail.inoutMode"
                  :rules="[rules.required]"
                  :items="inoutModes"
                ></v-select>
              </v-col>
              <!-- TODO:
              根据用户信息读取用户所在部门
               -->
              <v-col cols="12" md="3">
                <v-ship-dept
                  outlined
                  dense
                  label="申请部门"
                  :disabled="!detail.shipCode || isEdit"
                  v-model="detail.deptName"
                  :rules="[rules.required]"
                  :items="['甲板部', '轮机部']"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.handler"
                  use-current
                  :init-user="initUser"
                  :rules="[rules.required]"
                  disabled
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="!canEdit"
                  outlined
                  dense
                  label="签收日期"
                  v-model="detail.signingDate"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  v-model="detail.inoutDate"
                  use-today
                  disabled
                  outlined
                  label="入库日期"
                  dense
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-station-jw
                  v-model="detail.handlerPost"
                  :disabled="
                    $local.data.get('userInfo').userType == '2' &&
                    !$local.data.get('userInfo').roleName.includes('轮机长')
                  "
                ></v-ship-station-jw>
              </v-col>
              <v-col v-if="detail.inoutMode === 0" cols="12" md="3">
                <v-dialog-select
                  :readonly="!!detail.orderId"
                  :disabled="
                    !detail.shipCode || !detail.deptName || !detail.signingDate
                  "
                  v-model="detail.orderId"
                  :init-selected="initOrder"
                  :headers="orderHeaders"
                  item-value="id"
                  max-width="1300"
                  item-text="orderNo"
                  label="备件订单(仅本岗位申请)"
                  req-url="/business/shipAffairs/purchaseManage/purchaseOrderPageSpareIn"
                  :search-remain="searchObj"
                >
                  <template v-slot:[`item.isDockRepair`]="{ item }">
                    {{ item.isDockRepair ? '是' : '否' }}
                  </template>
                </v-dialog-select>
                <v-col cols="12" md="12" class="py-0">
                  <span style="color: green">
                    订单入库注意事项：一个订单只能入库一次，请货物到齐、并确认无误后再全部入库！
                  </span>
                </v-col>
              </v-col>
              <v-col cols="12" md="3">
                <v-switch
                  disabled
                  class="mt-1"
                  :label="detail.isDockRepair ? '坞修' : '非坞修'"
                  v-model="detail.isDockRepair"
                  dense
                ></v-switch>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  v-if="detail.inoutMode != 1"
                  :readonly="!canEdit"
                  outlined
                  dense
                  label="备注"
                  v-model="detail.remark"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  :readonly="true"
                  outlined
                  dense
                  label="申请单备注"
                  v-model="detail.remark1"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  v-if="detail.inoutMode == 1"
                  :readonly="true"
                  outlined
                  dense
                  label="调减原因"
                  v-model="detail.remark2"
                ></v-textarea>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6">
                <b>供应商名称:</b>
                {{ detail.supplierName }}
              </v-col>
              <v-col cols="6">
                <b>供应商SAP代码:</b>
                {{ detail.supplierCode }}
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template
        v-if="detail.inoutMode == 2 && detail.status != 2 && detail.status != 3"
        #入库明细按钮
      >
        <v-btn
          :disabled="!detail.shipCode"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['入库明细:选择备件']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择备件
        </v-btn>
        <v-btn
          :disabled="!select"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCom"
          v-permission="['入库明细:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #入库明细>
        <v-form ref="form2">
          <v-table-list
            :show-select="
              detail.inoutMode == 2 && detail.status != 2 && detail.status != 3
            "
            v-model="select"
            :headers="isShip ? detailHeadersShip : detailHeaders"
            item-key="cid"
            :items="detail.detailList"
          >
            <template v-if="canEdit" v-slot:[`item.stocksInNumber`]="{ item }">
              <v-text-field
                label="到货数量"
                v-model="item.stocksInNumber"
                single-line
                dense
                :rules="[rules.int, item.max]"
              ></v-text-field>
            </template>
            <template v-slot:[`item.total`]="{ item }">
              {{ (item.price * item.stocksInNumber).toFixed(2) }}
            </template>
            <template v-slot:[`item.depositoryId`]="{ item }">
              <v-select
                v-if="canEdit"
                label="所在库位"
                v-model="item.depositoryId"
                single-line
                dense
                :items="depository"
                :rules="[rules.required]"
              ></v-select>
              <div v-else>{{ item.depositoryName }}</div>
            </template>
          </v-table-list>
        </v-form>
        <v-card-text>
          <v-attach-list
            v-if="false"
            :attachments="detail.attachmentRecords"
            @change="(ids) => (detail.attachmentIds = ids)"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
        <v-card-text>
          <v-attach-list
            v-if="detail.inoutMode != 2"
            title="签收单附件"
            :attachments="detail.attachmentRecords2"
            @change="(ids) => (detail.attachmentIds2 = ids)"
          ></v-attach-list>
        </v-card-text>
      </template>
      <template
        #供应商评价按钮
        v-if="this.$route.params.id != 'new' && detail.inoutMode == 0"
      >
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom1"
          v-permission="['备件入库:评价供应商']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          评价供应商
        </v-btn>
      </template>
      <template #供应商评价>
        <v-container fluid>
          <b>供应商名称:</b>
          {{ detail.supplierName }}
          <!--          <b>供应商编码:</b>
          {{ detail.supplierNo }}-->
          <v-card
            v-for="(item, index) in comments"
            :key="index"
            outlined
            class="mt-1 pt-2 px-2 mb-4"
          >
            <v-row>
              <v-col cols="12" md="6">
                质量评分
                <v-rating
                  v-model="item.score1"
                  background-color="purple lighten-3"
                  color="purple"
                  length="10"
                  readonly
                ></v-rating>
              </v-col>
              <v-col cols="12" md="6">
                服务评分
                <v-rating
                  v-model="item.score2"
                  background-color="green lighten-3"
                  color="green"
                  length="10"
                  readonly
                ></v-rating>
              </v-col>
            </v-row>
            <v-card-text class="text-body-1">{{ item.remark }}</v-card-text>
            <v-card-text>
              <v-attach-list
                title="评价附件"
                disabled
                :attachments="item.attachmentRecords"
              ></v-attach-list>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <b>评论时间:</b>
              {{ item.remarkTime }}，
              <b>评论人:</b>
              {{ item.userNickName }}
            </v-card-actions>
          </v-card>
        </v-container>
      </template>
    </v-detail-view>
    <spare-part-select
      v-model="dialog"
      :shipCode="detail.shipCode"
      :components.sync="detail.detailList"
    ></spare-part-select>
    <comment-dialog
      v-model="dialog1"
      :initialData="initData"
      @success="loadCom"
    ></comment-dialog>
  </v-container>
</template>
<script>
import sparePartSelect from './private/spare-part-select.vue'
import routerControl from '@/mixin/routerControl'
import CommentDialog from '@/views/maritime-maintence/components/comment-dialog1.vue'
export default {
  name: 'spare-in-detail',
  components: { CommentDialog, sparePartSelect },
  mixins: [routerControl],
  created() {
    this.backRouteName = 'spare-in-list'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.subtitles = ['基本信息', '入库明细', '供应商评价']
    this.orderHeaders = [
      { text: '船舶', value: 'shipInfo' },
      { text: '订单号', value: 'orderNo' },
      { text: '申请单号', value: 'applyNo' },
      { text: '创建日期', value: 'createTime' },
      { text: '交付日期', value: 'deliveryDate' },
      { text: '入库完成日期', value: 'completeTime' },
      { text: '是否坞修', value: 'isDockRepair' },
    ]
    // depositoryId	仓库id	string
    // id	物理主键	string
    // inoutId	出入库主表id	string
    // itemBatch	物品采购批次	string
    // itemId	物件id	string
    // remark	备注	string
    // shipCode	船舶编码	string
    // stocksInOutNumber	出入库数量	number
    // stocksType	物品类型;物品类型，0 备件；1 物料；2 滑油；3淡水；4 其他	integer
    this.detailHeaders = [
      { text: '设备主体名称', value: 'equipmentFirstCName' },
      { text: '子设备', value: 'equipmentThirdCName' },
      { text: '备件名', value: 'itemName' },
      { text: '费用科目', value: 'costSubjectName' },
      { text: '备件号', value: 'itemNumber' },
      { text: '备件SAPCODE', value: 'code' },
      { text: '图纸号', value: 'drawingSerialNumber' },
      { text: '单位', value: 'unit' },
      { text: '批次号', value: 'itemBatch' },
      { text: '所在库位', value: 'depositoryId', width: 200 },
      // { text: '位置', value: 'address' },
      { text: '订单数量', value: 'purchaseNum', width: 200 },
      { text: '实际到货数', value: 'stocksInNumber', width: 200 },
      { text: '单价', value: 'price' },
      { text: '总价', value: 'total' },
    ]
    this.detailHeadersShip = [
      { text: '子设备', value: 'equipmentThirdCName' },
      { text: '备件名', value: 'itemName' },
      { text: '备件号', value: 'itemNumber' },
      { text: '备件SAPCODE', value: 'code' },
      { text: '图纸号', value: 'drawingSerialNumber' },
      { text: '单位', value: 'unit' },
      { text: '批次号', value: 'itemBatch' },
      { text: '所在库位', value: 'depositoryId', width: 200 },
      // { text: '位置', value: 'address' },
      { text: '订单数量', value: 'purchaseNum', width: 200 },
      { text: '实际到货数', value: 'stocksInNumber', width: 200 },
    ]
    this.inoutModes = [
      { text: '订单入库', value: 0 },
      { text: '调减入库', value: 1, disabled: true },
      { text: '期初入库', value: 2 },
    ]
    this.userHeaders = [
      { text: '用户名', value: 'nickName' },
      { text: '部门名称', value: 'deptName' },
      { text: '手机号', value: 'phoneNumber' },
    ]
  },
  data() {
    return {
      detail: {
        detailList: [],
        inoutMode: 0,
        orderId: '',
        handlerPost: '',
        attachmentIds2: [],
      },
      initOrder: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        int: (v) => /^\d+?$/.test(v) || '请输入整数数字',
      },
      searchObj: {
        businessStatus: 1002,
        applicantPost: '',
        orderType: '01',
      },
      depository: [],
      detailHeaders: [
        { text: '备件名', value: 'itemName' },
        { text: '备件号', value: 'itemNumber' },
        { text: '批次号', value: 'itemBatch' },
        { text: '所在库位', value: 'depositoryId', width: 200 },
        {
          text: '订单数量/未调减数量(期初入库不填)',
          value: 'purchaseNum',
          width: 200,
        },
        { text: '实际到货数', value: 'stocksInNumber', width: 200 },
      ],
      orderHeaders: [
        { text: '船舶', value: 'shipInfo' },
        { text: '订单号', value: 'orderNo' },
        { text: '申请单号', value: 'applyNo' },
        { text: '创建日期', value: 'createTime' },
        { text: '交付日期', value: 'deliveryDate' },
        { text: '入库完成日期', value: 'completeTime' },
        { text: '是否坞修', value: 'isDockRepair' },
      ],
      dialog: false,
      components: [],
      select: false,
      initUser: false,
      comments: [],
      initData: {},
      dialog1: false,
    }
  },

  watch: {
    'detail.shipCode'(val) {
      if (val) {
        this.searchObj.shipCode = val
        // this.loadDepository()
      }
    },
    'detail.orderId'(val) {
      if (val && !this.isEdit) {
        this.stockIn()
      }
    },
    'detail.inoutMode'(val) {
      // this.detailHeaders.find((i) => i.value === 'purchaseNum').text =
      //   val === 0 ? '订单数量' : val === 1 ? '未调减数量' : '订单数(不填)'
      this.detailHeaders.find((i) => i.value === 'purchaseNum').text =
        val === 0 ? '订单数量' : val === 1 ? '订单数量' : '订单数(不填)'
    },
    'detail.handlerPost'(val) {
      if (val) {
        this.searchObj.applicantPost = val
        this.loadDepository(val)
      }
    },
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return this.detail.status != 2 && this.detail.status != 3
    },
    canSubmit() {
      return this.detail.status != 3
    },
    isSupper() {
      return this.$local.data.get('userInfo').userType == 4
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return false
      if (!this.$refs.form2.validate()) return false
      // 订单入库 调减入库 草稿或退回需校验是否上传签收单附件
      if (
        this.detail.status != 2 &&
        this.detail.status != 3 &&
        this.detail.attachmentIds2.length === 0 &&
        this.detail.inoutMode != 2
      ) {
        this.$dialog.message.error('请上传签收单附件！')
        return
      }
      const reqUrl =
        this.detail.inoutMode === 0
          ? '/business/shipAffairs/purchaseManage/saveOrUpdatePurchaseStockInOfOrder'
          : this.detail.inoutMode === 1
          ? '/business/shipAffairs/purchaseManage/saveOrUpdatePurchaseDecreaseReceipt'
          : '/business/shipAffairs/purchaseManage/saveOrUpdateFreeStocksIn'
      const stockInDetailModifyList = this.getCompWithOperation()
      const { errorRaw, data } = await this.postAsync(reqUrl, {
        ...this.detail,
        stockInType: 1,
        stockInDetailModifyList,
      })
      if (notMove) return data
      if (!errorRaw) goBack()
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      if (!this.detail.auditParams) {
        const reqUrl =
          this.detail.inoutMode === 0
            ? '/business/shipAffairs/purchaseManage/submitPurchaseStockInOfOrder'
            : this.detail.inoutMode === 1
            ? '/business/shipAffairs/purchaseManage/submitPurchaseDecreaseReceipt'
            : '/business/shipAffairs/purchaseManage/submitPurchaseStockInOfOrder'
        const { errorRaw } = await this.getAsync(reqUrl, {
          inoutId: data,
          id: data,
        })
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        // if (!error) goBack()
        if (!error) {
          const { data } = await this.getAsync(
            '/business/shipAffairs/purchaseManage/purchaseStockInoutById2',
            { id: this.$route.params.id },
          )
          if (data) {
            goBack()
          }
        }
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseStockInoutById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.initUser = { id: data.handler, nickName: data.handlerName }
      if (this.detail.inoutMode == 2) {
        //期初入库不校验订单数量
        this.detail.detailList = data.detailList.map((i) => ({
          ...i,
          stocksInNumber: i.stocksInOutNumber,
          cid: `${i.itemName}:${i.itemNumber}`,
        }))
      } else {
        this.detail.detailList = data.detailList.map((i) => ({
          ...i,
          stocksInNumber: i.stocksInOutNumber,
          cid: `${i.itemName}:${i.itemNumber}`,
          max: (a) => i.purchaseNum >= a || '入库数量不得超过最大订单/未调减数',
        }))
      }
      this.components = [...this.detail.detailList]
      this.detail.shipCode = data.shipInfo.shipCode
      this.initOrder = { id: data.orderId, orderNo: data.orderNo }
    },

    async stockIn() {
      // if (!this.$refs.form.validate()) return false
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/initiateStockInDetailByOrderId',
        this.detail,
      )
      if (data) {
        this.closeAndTo('spare-in-detail', { id: data })
      }
    },

    async loadDepository(manager) {
      if (this.detail.status == 2 || this.detail.status == 3) {
        return
      }
      // TODO:如果是非船端角色则将搜索所有的库位
      const { data } = await this.getAsync(
        '/business/shipAffairs/Depository/list',
        {
          size: 99,
          current: 1,
          manager,
          shipCode: this.detail.shipCode,
          type: this.detail.isDockRepair ? 2 : 0,
        },
      )
      const { records } = data
      this.depository = records.map((i) => ({ text: i.name, value: i.id }))
      if (this.depository.length === 0)
        this.$dialog.message.error(
          '当前角色库位为空，无法操作，需选择船上岗位。',
        )
      this.detail.detailList = this.detail.detailList.map((i) => ({
        ...i,
        depositoryId: i.depositoryId || this.depository[0]?.value,
      }))
    },
    createCom() {
      this.searchObj = { equipmentInformationId: this.detail.equipmentId }
      this.dialog = true
    },
    createCom1() {
      this.searchObj = { equipmentInformationId: this.detail.equipmentId }
      //this.dialog = true
      this.dialog1 = true
      this.initData = {
        supplierId: this.detail.supplierId,
        supplierName: this.detail.supplierName,
        orderId: this.detail.orderId,
        orderNo: this.detail.orderNo,
      }
    },
    async delCom() {
      this.detail.detailList = this.detail.detailList.filter(
        (i) => i.cid !== this.select.cid,
      )
      this.select = false
    },

    getCompWithOperation() {
      const ids = this.detail.detailList.map((i) => i.cid)
      const delList = this.isEdit
        ? this.components
            .filter((i) => !ids.includes(i.cid))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.detail.detailList.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },
    async loadCom() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/SupplierAssessment/getByOrderId1',
        {
          id: this.detail.orderId,
        },
      )
      this.comments = data || []
      this.dialog1 = false
    },
  },

  async mounted() {
    await this.loadDetail()
    if (this.$route.params.id != 'new' && this.detail.inoutMode == 0)
      await this.loadCom()
  },
}
</script>

<style></style>
