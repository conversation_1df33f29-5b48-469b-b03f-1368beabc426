<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-alert type="info" color="green" text dense class="mb-0">
          只有做了"订单入库"的单子，才能在此页面找到入库单。
        </v-alert>
      </v-col>
    </v-row>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      use-status
      :search-remain="searchObj"
      :push-params="pushParams"
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="入库单号"
            outlined
            dense
            clearable
            v-model="searchObj.inoutCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.inquier"
            :items="inquierMap"
            label="询价人"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.inoutMode"
            :items="inoutModeMap"
            label="入库方式"
            outlined
            clearable
            dense
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['备件入库:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
        <v-btn
          :disabled="selected.status != 3"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="reduceIn"
          v-permission="['备件入库:调减入库']"
        >
          <v-icon left>mdi-package-variant-minus</v-icon>
          调减入库
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :to="{ name: 'spare-in-detail', params: { id: 'new' } }"
          v-permission="['备件入库:订单入库(自由)']"
        >
          <v-icon left>mdi-package-variant-plus</v-icon>
          订单入库(期初)
        </v-btn>
      </template>
      <template v-slot:[`item.inoutMode`]="{ item }">
        {{ ['订单入库', '调减入库', '期初入库'][item.inoutMode] }}
      </template>
    </v-table-searchable>
    <reduce-in-dialog
      ref="dialog"
      :initialData="{
        signingDate: selected.signingDate || '',
        remark: selected.remark,
      }"
    ></reduce-in-dialog>
  </v-container>
</template>
<script>
import reduceInDialog from '../../components/reduce-in-dialog.vue'
// handler	操作人	string
// handlerPost	入库人岗位	string
// id	物理主键	string
// inoutCode	出入库单号	string
// inoutDate	出入库日期	string
// inoutMode	出入库方式;0 - 正常入库 1-调减入库 2-期初入库 3-维护保养消耗 4-自修消耗 5-航修消耗 6-坞修消耗 7-自由出库	integer
// inoutNature	出入库物品类型;物品类型，0 备件；1 物料；2 滑油；3淡水；4 其他	integer
// inoutType	出入库类型;0-入库 1-出库	string
// isDockRepair	是否坞修	boolean
// orderNo	采购订单号	string
// parentId	父级id	string
// remark	备注	string
// shipCode	船舶编码	string
// status	流程状态	string
export default {
  components: { reduceInDialog },
  name: 'spare-in-list',
  created() {
    this.tableName = '备件入库'
    this.reqUrl = '/business/shipAffairs/purchaseManage/purchaseStockInOutPage'
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '入库单号', value: 'inoutCode' },
      { text: '入库日期', value: 'inoutDate' },
      { text: '入库岗位', value: 'handlerPost' },
      { text: '入库人', value: 'handler' },
      { text: '入库方式', value: 'inoutMode' },
      { text: '采购订单号', value: 'orderNo' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '询价人', value: 'inquier' },
      { text: '备注', value: 'remark' },
      { text: '申请单备注', value: 'remark1' },
    ]
    this.inquierMap = [
      { text: '王涛', value: '王涛' },
      { text: '王玮', value: '王玮' },
      { text: '石宇生', value: '石宇生' },
      { text: '竺嘉慧', value: '竺嘉慧' },
      { text: '朱广卫', value: '朱广卫' },
    ]
    this.inoutModeMap = [
      { text: '订单入库', value: '0' },
      { text: '调减入库', value: '1' },
      { text: '期初入库', value: '2' },
      { text: '维护保养消耗', value: '3' },
      { text: '自修消耗', value: '4' },
      { text: '航修消耗', value: '5' },
      { text: '坞修消耗', value: '6' },
      { text: '自由出库', value: '7' },
    ]
    // this.searchObj = { inoutNature: 0, inoutType: 0, isMe: true }
    this.pushParams = { name: 'spare-in-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: { inoutNature: 0, inoutType: 0, isMe: true },
    }
  },

  methods: {
    async reduceIn() {
      if (!(await this.$dialog.msgbox.confirm('确定要进行调减吗'))) return
      const { signingDate, remark } = await this.$refs.dialog.confirm()
      if (!signingDate) {
        this.$dialog.message.warning('未完成调减')
        return
      }
      // TODO:权限是否需要限制，谁可以调减
      const { data, errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/decreaseReceiptInitiate',
        { stockInoutId: this.selected.id, signingDate, remark },
      )
      if (errorRaw) return
      this.$router.push({
        name: 'spare-in-detail',
        params: { id: data },
      })
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/stocksInOutDelete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
