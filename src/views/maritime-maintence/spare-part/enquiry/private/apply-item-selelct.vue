<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1100"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        询价备件
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-data-table
          show-select
          :headers="headers"
          :items="items"
          item-key="componentId"
          hide-default-footer
          disable-pagination
          v-model="selected"
          @click:row="selectRow"
          dense
          class="use-divider"
        ></v-data-table>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn
          depressed
          color="primary"
          :disabled="selected.length === 0"
          @click="confirm"
        >
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'apply-item-selelct',
  model: {
    prop: 'open',
    event: 'update',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.headers = [
      { text: '备件号', value: 'componentNo' },
      // { text: '在图编号', value: 'drawingNo' },
      { text: '备件名称', value: 'componentEname' },
      { text: '库存数量', value: 'stockQuantity' },
      { text: '最高库存', value: 'maximumInventory' },
      { text: '申请数量', value: 'requireQuantity' },
      { text: '已采购数量', value: 'enquiryQuantity' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    items: Array,
    sitems: Array,
  },
  data() {
    return {
      dialog: false,
      selected: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$nextTick(() => {
        this.selected = this.sitems.map((i) => {
          return { ...i, vid: i.id, componentId: i.itemId, remarkk: i.remark }
        })
      })
    },
  },
  methods: {
    closeForm() {
      this.$emit('update', false)
    },
    async confirm() {
      this.$emit(
        'update:sitems',
        this.selected.map((i) => {
          return {
            ...i,
            id: i.vid,
            itemId: i.componentId,
            enquiryNum: i.enquiryNum || i.auditQuantity,
            remark: i.remarkk || '',
          }
        }),
      )
      this.$emit('update', false)
    },
    selectRow(_, { isSelected, item }) {
      this.selected = isSelected
        ? this.selected.filter((i) => i.id !== item.id)
        : [...this.selected, item]
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
