<template>
  <v-card>
    <v-card-title class="title-container">
      <div class="title-group">
        <span>比价详情</span>
        <v-btn
          class="full-screen-btn"
          :class="{ 'is-fullscreen': isFullScreen }"
          @click="toggleFullScreen"
          :title="isFullScreen ? '退出全屏' : '全屏比价'"
          color="primary"
          elevation="2"
        >
          <v-icon left>
            {{ isFullScreen ? 'mdi-fullscreen-exit' : 'mdi-fullscreen' }}
          </v-icon>
          {{ isFullScreen ? '退出全屏' : '全屏比价' }}
        </v-btn>
        <v-btn
          class="full-screen-btn"
          @click="downLoadExcel"
          :title="导出ECXEL"
          color="primary"
          :loading="loading"
          elevation="2"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出比价ECXEL
        </v-btn>
      </div>
    </v-card-title>
    <v-divider></v-divider>
    <v-simple-table class="use-divider" ref="table">
      <template v-slot:default>
        <thead>
          <tr>
            <th colspan="4" class="text-left">供应商</th>
            <th
              v-for="sup of quotes"
              :key="sup.id"
              class="text-left"
              colspan="8"
            >
              {{ sup.supplierName }}
              <v-btn
                v-if="
                  (sup.businessStatus === '已填报' ||
                    sup.businessStatus == '中标' ||
                    sup.businessStatus == '未中标') &&
                  businessStatus == '待商务主管定标'
                "
                width="90"
                tile
                color="error"
                small
                class="mx-1"
                @click="reBid(sup)"
                v-permission="['备件询价:重新报价']"
              >
                重新报价
              </v-btn>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colspan="4">报价单号</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id">
              {{ sup.quoteNo }}
            </td>
          </tr>
          <tr>
            <td colspan="4">备货天数</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id">
              {{ sup.stockUpDays }}
            </td>
          </tr>
          <tr>
            <td colspan="4">币种</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id">
              {{ sup.ccyCode }}
            </td>
          </tr>
          <tr>
            <td colspan="4">发票类型</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id">
              {{ getInvoiceTypeText(sup.invoiceType) }}
            </td>
          </tr>
          <tr>
            <td colspan="4">运费</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id + '0'">
              {{ sup.otherExpenses }}
            </td>
          </tr>
          <tr>
            <td colspan="4">报价总价格(折后)</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id + '1'">
              {{ sup.total }}
            </td>
          </tr>
          <tr>
            <td colspan="4">报价总价格(折算美元)</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id + '2'">
              {{ sup.changeTotal }}
            </td>
          </tr>
          <tr>
            <td colspan="4">中标总价格</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id + '1'">
              {{ sup.total2 }}
            </td>
          </tr>
          <tr>
            <td colspan="4">中标总价格(折后美元)</td>
            <td colspan="8" v-for="sup of quotes" :key="sup.id + '2'">
              {{ sup.changeTotal2 }}
            </td>
          </tr>
          <tr>
            <td colspan="100%">
              <MyAgGrid
                :columnDefs="getColumnDefs()"
                :rowData="getAllRowData()"
                :gridOptions="gridOptions"
                :context="{ parentComponent: this }"
                class="ag-theme-alpine full-width-grid"
              />
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-dialog v-model="delayDialog" max-width="500" hide-overlay attach="#mask">
      <v-card>
        <v-card-title>重新报价</v-card-title>
        <v-card-text>
          <v-form ref="delayForm">
            <end-time-picker
              v-model="newEndTime"
              label="新的报价截止时间"
              :rules="[rules.required]"
            ></end-time-picker>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" text @click="delayDialog = false">取消</v-btn>
          <v-btn color="primary" text @click="confirmReBid()">确定</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
import EndTimePicker from '@/views/maritime-maintence/components/enquiry/end-time-picker.vue'
import MyAgGrid from '@/components/modules/MyAgGrid/index.vue'
import GridSetter from '@/components/modules/MyAgGrid/components/Setter.vue'
import SelectAllHeader from '@/components/modules/MyAgGrid/components/SelectAllHeader.vue'

export default {
  name: 'compare-price-table',
  mixins: [currencyHelper],
  props: {
    quoteIds: {
      type: String,
    },
    businessStatus: {
      type: String,
    },
    enquiryId: {
      type: String,
    },
  },
  components: {
    EndTimePicker,
    MyAgGrid,
    // eslint-disable-next-line
    GridSetter,
    // eslint-disable-next-line
    SelectAllHeader,
  },
  data() {
    return {
      quotes: [{ equipmentInformationBaseInfo: {}, detailList: {} }],
      canEdit: true,
      canEdit1: false,
      canEdit2: false,
      canEdit3: false,
      delayDialog: false,
      newEndTime: '',
      currentSup: null,
      rules: {
        required: (v) => !!v || '必填项',
      },
      invoiceTypes: [
        { text: '增值税专用发票', value: 0 },
        { text: '普通发票', value: 1 },
        { text: '形式发票', value: 2 },
      ],
      gridOptions: {
        defaultColDef: {
          resizable: true,
          sortable: true,
          filter: true,
          minWidth: 100,
        },
        maintainRowOrder: true,
        suppressScrollOnNewData: true,
        suppressHorizontalScroll: false,
        suppressRowVirtualisation: true,
        suppressColumnVirtualisation: true,
        rowBuffer: 10,
        animateRows: true,
        suppressPropertyNamesCheck: true,
        suppressFieldDotNotation: true,
        onGridReady: (params) => {
          this.gridApi = params.api
          this.columnApi = params.columnApi

          setTimeout(() => {
            params.api.sizeColumnsToFit()
          }, 100)

          const gridElement = document.querySelector('.full-width-grid')
          if (gridElement) {
            const container = gridElement.closest('.ag-root-wrapper')
            if (container) {
              container.style.height = '400px'
            }
          }

          const observer = new ResizeObserver(
            this.debounce(() => {
              params.api.sizeColumnsToFit()
            }, 200),
          )

          if (gridElement) {
            observer.observe(gridElement)
          }
        },
        onFirstDataRendered: (params) => {
          setTimeout(() => {
            params.api.refreshCells({ force: true })
          }, 100)
        },
        components: {
          SelectAllHeader: SelectAllHeader,
        },
      },
      isFullScreen: false,
      loading: false,
      scrollPosition: undefined,
    }
  },

  watch: {
    quoteIds(val) {
      if (val.length > 0) this.loadPrice()
    },
    businessStatus(val) {
      if (
        val == '报价完成' ||
        val == '待商务主管定标' ||
        val == '超期' ||
        val == '重新定标' ||
        val == '用户开标'
      ) {
        this.canEdit = false
      }
      if (
        val == '报价完成' ||
        val == '超期' ||
        val == '重新定标' ||
        val == '用户开标'
      ) {
        this.canEdit1 = true
      }
      if (val == '待商务主管定标') {
        this.canEdit2 = true
      }
      if (
        val == '报价完成' ||
        val == '待商务主管定标' ||
        val == '超期' ||
        val == '重新定标' ||
        val == '用户开标'
      ) {
        this.canEdit3 = true
      }
    },
    quotes: {
      deep: true,
      handler() {
        this.$nextTick(() => {
          if (this.gridApi) {
            this.gridApi.refreshCells({
              force: true,
              suppressFlash: false,
            })
          }
        })
      },
    },
  },

  methods: {
    getQuoteWins() {
      const quoteWins = []
      this.quotes.forEach((item) => {
        item.detailList.forEach((detail) => {
          if (detail.isWins) {
            quoteWins.push(detail)
          }
        })
      })
      return quoteWins
    },
    getQuote() {
      const quotess = []
      this.quotes.forEach((item) => {
        item.detailList.forEach((detail) => {
          // if (detail.isWins) {
          quotess.push(detail)
          // }
        })
      })
      return quotess
    },
    async reBid(sup) {
      this.currentSup = sup
      this.delayDialog = true
    },
    async confirmReBid() {
      this.delayDialog = false
      try {
        // 先调用延期接口
        const { errorRaw: delayError } = await this.getAsync(
          '/business/shipAffairs/purchaseManage/delayPurchaseEnquiry',
          {
            enquiryId: this.currentSup.enquiryId,
            delayDate: this.newEndTime,
          },
        )

        if (delayError) {
          this.$dialog.message.error('延期失败')
          return
        }

        // 再调用重新报价接口
        const { errorRaw: reBidError } = await this.postAsync(
          '/business/shipAffairs/purchaseManage/reBid',
          {
            ...this.currentSup,
          },
        )

        if (!reBidError) {
          this.$dialog.message.success('已发送邮箱提醒供应商重新报价！')
          this.newEndTime = ''
          this.currentSup = null
          this.$emit('refresh')
          this.$emit('after-rebid')
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$dialog.message.error('操作失败')
      }
    },
    trackingStateChanged(value, itemName, itemNo, id, indexs) {
      if (value) {
        this.quotes.forEach((item) => {
          item.detailList.forEach((detail, index) => {
            if (
              detail.id !== id &&
              detail.itemName == itemName &&
              detail.itemNo == itemNo &&
              index == indexs
            ) {
              detail.isWins = !value
              this.$set(item.detailList, index, detail)
            }
          })
        })
      }
      if (this.gridApi) {
        this.gridApi.refreshCells({ force: true, suppressFlash: false })
      }
      this.quotes.forEach((item) => {
        // 计算中标状态
        const totalItems = item.detailList.length
        const winItems = item.detailList.filter(
          (detail) => detail.isWins,
        ).length

        let winStatus
        if (winItems === 0) {
          winStatus = '不中标'
          item.allSelect = false
        } else if (winItems === totalItems) {
          winStatus = '中标'
          item.allSelect = true
        } else {
          winStatus = '部分中标'
          item.allSelect = true
        }

        // 通知父组件更新报价列表中标状态
        this.$emit('update-win-status', item.id, winStatus)
      })
      this.updateFinalPrice()
    },
    trackingStateChangedAll(event, sup) {
      // 先处理当前供应商
      sup.detailList.forEach((detail, index) => {
        if (detail.finalPrice == 0 || detail.quotNum == 0) {
          console.log(index)
        } else {
          detail.isWins = event
          this.$set(sup.detailList, index, detail)
        }
      })

      // 计算当前供应商的中标状态
      const validItems = sup.detailList.filter(
        (detail) => detail.finalPrice != 0 && detail.quotNum != 0,
      ).length
      const winItems = sup.detailList.filter(
        (detail) =>
          detail.finalPrice != 0 && detail.quotNum != 0 && detail.isWins,
      ).length

      let winStatus
      if (winItems === 0) {
        winStatus = '不中标'
      } else if (winItems === validItems) {
        winStatus = '中标'
      } else {
        winStatus = '部分中标'
      }

      // 通知父组件更新当前供应商的中标状态
      this.$emit('update-win-status', sup.id, winStatus)

      if (event) {
        // 处理其他供应商
        this.quotes.forEach((item) => {
          if (item.id != sup.id) {
            item.allSelect = !event
            item.detailList.forEach((detail, index) => {
              detail.isWins = !event
              this.$set(item.detailList, index, detail)
            })
            // 通知父组件更新其他供应商为不中标
            this.$emit('update-win-status', item.id, '不中标')
          }
        })
      }

      this.updateFinalPrice()
    },
    async loadPrice() {
      let reqs = []
      for (const quoteId of this.quoteIds) {
        reqs.push(
          this.getAsync(
            '/business/shipAffairs/purchaseManage/purchaseQuoteDetailById',
            {
              quoteId,
            },
          ),
        )
      }
      const results = await Promise.all(reqs)
      await this.loadCurrencyInfo()
      this.quotes = results
        .map((i) => {
          const detailList = Array.isArray(i.data.detailList)
            ? i.data.detailList
            : []

          return {
            ...i.data,
            detailList,
            rate: this.currencyInfo.find((c) => c.id == i.data.currencyId)
              .rateToMain,
            // total: (
            //   detailList.reduce(
            //     (x, { price, finalNum, discount }) =>
            //       x +
            //       (i.data.ccyCode === 'JPY'
            //         ? Math.round(price * finalNum * discount)
            //         : Math.round(price * finalNum * discount * 100) / 100),
            //     0,
            //   ) + i.data.otherExpenses
            // ).toFixed(i.data.ccyCode === 'JPY' ? 0 : 2),
            total: (() => {
              // console.log('计算总价:', i.data.supplierName)
              // console.log('data:', i.data)
              const subtotal = detailList.reduce(
                (x, { price, finalNum, discount }) => {
                  const itemTotal =
                    i.data.currencyId === '2'
                      ? Math.round(price * discount) * finalNum
                      : Math.round(price * finalNum * discount * 100) / 100
                  //console.log('单项:', { price, finalNum, discount, itemTotal })
                  return x + itemTotal
                },
                0,
              )
              //console.log('小计:', subtotal, '其他费用:', i.data.otherExpenses)
              const total = subtotal + i.data.otherExpenses
              //console.log('总计:', total)
              return total.toFixed(i.data.currencyId === '2' ? 0 : 2)
            })(),
            changeTotal: (() => {
              const subtotal = detailList.reduce(
                (x, { price, finalNum, discount }) => {
                  const itemTotal =
                    i.data.currencyId === '2'
                      ? Math.round(price * discount) * finalNum
                      : Math.round(price * finalNum * discount * 100) / 100
                  return x + itemTotal
                },
                0,
              )
              const rate = this.currencyInfo.find(
                (c) => c.id == i.data.currencyId,
              ).rateToMain
              const total = (subtotal + i.data.otherExpenses) * rate
              return total.toFixed(i.data.currencyId === '2' ? 2 : 2)
            })(),
            // changeTotal: (
            //   (detailList.reduce(
            //     (x, { price, finalNum, discount }) =>
            //       x + Math.round(price * finalNum * discount * 100) / 100,
            //     0,
            //   ) +
            //     i.data.otherExpenses) *
            //   this.currencyInfo.find((c) => c.id == i.data.currencyId)
            //     .rateToMain
            // ).toFixed(2),
            total2: (
              detailList.reduce(
                (x, { finalPrice, quotNum, isWins }) =>
                  x +
                  (isWins ? Math.round(finalPrice * quotNum * 100) / 100 : 0),
                0,
              ) + i.data.otherExpenses
            ).toFixed(2),
            changeTotal2: (
              (detailList.reduce(
                (x, { finalPrice, quotNum, isWins }) =>
                  x +
                  (isWins ? Math.round(finalPrice * quotNum * 100) / 100 : 0),
                0,
              ) +
                i.data.otherExpenses) *
              this.currencyInfo.find((c) => c.id == i.data.currencyId)
                .rateToMain
            ).toFixed(2),
            allSelect: detailList.some((detail) => detail.isWins == true),
          }
        })
        .filter(
          (quote) =>
            quote.businessStatus !== '废弃' &&
            quote.businessStatus !== '未填报' &&
            quote.businessStatus !== '填报中' &&
            quote.finshQuote,
        )
    },
    saveScrollPosition() {
      const gridElement = document.querySelector('.ag-body-viewport')
      if (gridElement) {
        this.scrollPosition = {
          top: gridElement.scrollTop,
          left: gridElement.scrollLeft,
        }
      }
    },
    restoreScrollPosition() {
      if (this.scrollPosition) {
        this.$nextTick(() => {
          const gridElement = document.querySelector('.ag-body-viewport')
          if (gridElement) {
            gridElement.scrollTop = this.scrollPosition.top
            gridElement.scrollLeft = this.scrollPosition.left
          }
        })
      }
    },
    updateFinalPrice() {
      this.saveScrollPosition()
      const updatedQuotes = this.quotes.map((i) => ({
        ...i,
        rate: this.currencyInfo.find((c) => c.id == i.currencyId).rateToMain,
        total: (
          i.detailList.reduce(
            (x, { price, finalNum, discount }) =>
              x + Math.round(price * finalNum * discount * 100) / 100,
            0,
          ) + i.otherExpenses
        ).toFixed(2),
        changeTotal: (
          (i.detailList.reduce(
            (x, { price, finalNum, discount }) =>
              x + Math.round(price * finalNum * discount * 100) / 100,
            0,
          ) +
            i.otherExpenses) *
          this.currencyInfo.find((c) => c.id == i.currencyId).rateToMain
        ).toFixed(2),
        total2: (
          i.detailList.reduce(
            (x, { finalPrice, quotNum, isWins }) =>
              x + (isWins ? Math.round(finalPrice * quotNum * 100) / 100 : 0),
            0,
          ) + i.otherExpenses
        ).toFixed(2),
        changeTotal2: (
          (i.detailList.reduce(
            (x, { finalPrice, quotNum, isWins }) =>
              x + (isWins ? Math.round(finalPrice * quotNum * 100) / 100 : 0),
            0,
          ) +
            i.otherExpenses) *
          this.currencyInfo.find((c) => c.id == i.currencyId).rateToMain
        ).toFixed(2),
      }))
      this.$nextTick(() => {
        this.quotes = updatedQuotes
        this.$nextTick(() => {
          this.restoreScrollPosition()
        })
      })
    },
    getInvoiceTypeText(value) {
      const found = this.invoiceTypes.find(
        (type) => type.value === Number(value),
      )
      return found ? found.text : ''
    },
    getColumnDefs() {
      const baseColumns = [
        {
          field: 'itemName',
          headerName: '备件名称',
          pinned: 'left',
          width: 140,
          minWidth: 140,
          flex: 1,
          hide: false,
          lockPosition: true,
          suppressMovable: true,
          tooltipValueGetter: (p) => p.value,
        },
        {
          field: 'itemNo',
          headerName: '备件号',
          pinned: 'left',
          width: 100,
          minWidth: 100,
          tooltipValueGetter: (p) => p.value,
        },
        {
          field: 'equipmentCname',
          headerName: '设备',
          pinned: 'left',
          width: 90,
          minWidth: 90,
          tooltipValueGetter: (p) => p.value,
        },
        {
          field: 'enquiryNum',
          pinned: 'left',
          headerName: '询价数量',
          width: 105,
          minWidth: 105,
        },
      ]

      const supplierColumns = this.quotes.flatMap((quote, index) => {
        const columnGroup = {
          headerName:
            quote.supplierName + ' （年度协议：' + quote.annual + '）',
          children: [
            {
              field: `supplier${index}_finalNum`,
              headerName: '报价数量',
              width: 120,
              minWidth: 120,
              valueFormatter: (params) => {
                return params.value || '0'
              },
            },
            {
              field: `supplier${index}_quotNum`,
              headerName: '成交数量',
              width: 120,
              minWidth: 120,
              editable: this.canEdit1,
              cellStyle: (params) => ({
                backgroundColor: this.canEdit1 ? '#afe4af' : 'transparent',
                ...this.getBaseCellStyle(params),
              }),
              onCellValueChanged: (params) => {
                if (params.oldValue !== params.newValue) {
                  params.newValue = Number(
                    (Math.round(Number(params.newValue) * 100) / 100).toFixed(
                      2,
                    ),
                  )
                  const rowIndex = params.node.rowIndex
                  this.handleQuotNumChange(params.newValue, quote, rowIndex)
                }
              },
              valueFormatter: (params) => {
                const value = params.value || '0'
                return value
              },
            },
            {
              field: `supplier${index}_price`,
              headerName: '单价',
              width: 120,
              minWidth: 120,
              valueFormatter: (params) => {
                const data = params.data[`supplier${index}_data`]
                return data
                  ? (
                      Math.round(data.price * data.discount * 100) / 100
                    ).toFixed(2)
                  : '0.00'
              },
            },
            {
              field: `supplier${index}_price_usd`,
              headerName: '单价美元',
              width: 120,
              minWidth: 120,
              valueFormatter: (params) => {
                const data = params.data[`supplier${index}_data`]
                return data
                  ? (
                      Math.round(
                        data.price * data.discount * quote.rate * 100,
                      ) / 100
                    ).toFixed(2)
                  : '0.00'
              },
            },
            {
              field: `supplier${index}_finalPrice`,
              headerName: `成交单价`,
              width: 120,
              minWidth: 120,
              editable: this.canEdit2,
              cellStyle: () => {
                const baseStyle = {
                  padding: '4px 8px',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                }

                if (this.canEdit2) {
                  return {
                    ...baseStyle,
                    backgroundColor: '#afe4af',
                    border: '1px solid #dcdfe6',
                    cursor: 'pointer',
                  }
                } else {
                  return {
                    ...baseStyle,
                    borderBottom: '1px dashed #dcdfe6',
                  }
                }
              },
              onCellValueChanged: (params) => {
                if (params.oldValue !== params.newValue) {
                  const detail = quote.detailList[params.node.rowIndex]
                  if (quote.ccyCode === 'JPY') {
                    detail.finalPrice = Math.round(Number(params.newValue))
                  } else {
                    detail.finalPrice = Number(
                      (Math.round(Number(params.newValue) * 100) / 100).toFixed(
                        2,
                      ),
                    )
                  }
                  this.updateFinalPrice()
                }
              },
              valueFormatter: (params) => {
                if (quote.ccyCode === 'JPY') {
                  return params.value
                    ? Math.round(params.value).toString()
                    : '0'
                }
                return params.value ? params.value.toFixed(2) : '0.00'
              },
            },
            {
              field: `supplier${index}_manufacturer`,
              headerName: '生产厂家',
              width: 120,
              minWidth: 120,
              tooltipValueGetter: (p) => p.value,
            },
            {
              field: `supplier${index}_remark`,
              headerName: '备注',
              width: 90,
              minWidth: 90,
              tooltipValueGetter: (p) => p.value,
            },
            {
              field: `supplier${index}_isWins`,
              headerName: `是否中标`,
              width: 145,
              minWidth: 145,
              filter: false,
              sortable: false,
              suppressMovable: true,
              menuTabs: [],
              suppressMenu: true,
              headerComponent: 'SelectAllHeader',
              headerComponentParams: {
                canEdit3: this.canEdit3,
                quote: quote,
                onHeaderChange: (checked) => {
                  // 先处理当前供应商
                  quote.detailList.forEach((detail, index) => {
                    if (detail.finalPrice != 0 && detail.quotNum != 0) {
                      detail.isWins = checked
                      this.$set(quote.detailList, index, detail)
                    } else {
                      detail.isWins = false
                      this.$set(quote.detailList, index, detail)
                    }
                  })

                  // 如果是选中状态，则处理其他供应商
                  if (checked) {
                    this.quotes.forEach((item) => {
                      if (item.id !== quote.id) {
                        item.allSelect = false
                        item.detailList.forEach((otherDetail, otherIndex) => {
                          otherDetail.isWins = false
                          this.$set(item.detailList, otherIndex, otherDetail)
                        })
                      }
                    })
                  }

                  // 计算并更新中标状态
                  const validItems = quote.detailList.filter(
                    (detail) => detail.finalPrice != 0 && detail.quotNum != 0,
                  ).length
                  const winItems = quote.detailList.filter(
                    (detail) =>
                      detail.finalPrice != 0 &&
                      detail.quotNum != 0 &&
                      detail.isWins,
                  ).length

                  let winStatus
                  if (winItems === 0) {
                    winStatus = '不中标'
                  } else if (winItems === validItems) {
                    winStatus = '中标'
                  } else {
                    winStatus = '部分中标'
                  }

                  // 更新当前供应商的中标状态
                  this.$emit('update-win-status', quote.id, winStatus)

                  // 如果是选中状态，更新其他供应商为不中标
                  if (checked) {
                    this.quotes.forEach((item) => {
                      if (item.id !== quote.id) {
                        this.$emit('update-win-status', item.id, '不中标')
                      }
                    })
                  }

                  // 更新价格并刷新表格
                  this.updateFinalPrice()
                  this.$nextTick(() => {
                    if (this.gridApi) {
                      this.gridApi.refreshCells({
                        force: true,
                        suppressFlash: false,
                      })
                    }
                  })
                },
              },
              cellRenderer: (params) => {
                const switchContainer = document.createElement('div')
                switchContainer.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 4px;
              `

                const switchLabel = document.createElement('label')
                switchLabel.className = 'switch'
                switchLabel.style.cssText = `
                position: relative;
                display: inline-block;
                width: 40px;
                height: 20px;
              `

                const input = document.createElement('input')
                input.type = 'checkbox'
                input.checked = params.value
                input.disabled = !this.canEdit3
                input.style.cssText = `
                opacity: 0;
                width: 0;
                height: 0;
              `

                const slider = document.createElement('span')
                slider.className = 'slider'
                slider.style.cssText = `
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                transition: .4s;
                border-radius: 34px;
              `

                slider.innerHTML = `
                <span style="
                  position: absolute;
                  content: '';
                  height: 16px;
                  width: 16px;
                  left: 2px;
                  bottom: 2px;
                  background-color: white;
                  transition: .4s;
                  border-radius: 50%;
                  transform: ${
                    params.value ? 'translateX(20px)' : 'translateX(0)'
                  };
                "></span>
              `

                input.addEventListener('change', (event) => {
                  if (!this.canEdit3) return

                  const detail =
                    this.quotes[
                      params.column.colId.split('_')[0].replace('supplier', '')
                    ].detailList[params.node.rowIndex]
                  detail.isWins = event.target.checked

                  slider.querySelector('span').style.transform = event.target
                    .checked
                    ? 'translateX(20px)'
                    : 'translateX(0)'
                  slider.style.backgroundColor = event.target.checked
                    ? '#4CAF50'
                    : '#ccc'

                  this.trackingStateChanged(
                    event.target.checked,
                    detail.itemName,
                    detail.itemNo,
                    detail.id,
                    params.node.rowIndex,
                  )

                  params.node.setDataValue(
                    params.colDef.field,
                    event.target.checked,
                  )
                })

                slider.style.backgroundColor = params.value ? '#4CAF50' : '#ccc'

                switchLabel.appendChild(input)
                switchLabel.appendChild(slider)
                switchContainer.appendChild(switchLabel)

                return switchContainer
              },
            },
          ],
        }
        return [columnGroup]
      })

      return [...baseColumns, ...supplierColumns]
    },

    getBaseCellStyle() {
      return {
        display: 'flex',
        alignItems: 'center',
        padding: '4px 8px',
        height: '100%',
      }
    },

    getAllRowData() {
      if (!this.quotes || this.quotes.length === 0) {
        return []
      }
      const firstQuote = this.quotes[0]
      if (!Array.isArray(firstQuote.detailList)) {
        return []
      }

      return firstQuote.detailList.map((item, index) => {
        const rowData = {
          itemName: item.itemName,
          itemNo: item.itemNo,
          enquiryNum: item.enquiryNum,
          equipmentCname:
            firstQuote.equipmentInformationBaseInfo?.equipmentCname || '',
        }
        this.quotes.forEach((quote, supplierIndex) => {
          if (Array.isArray(quote.detailList) && quote.detailList[index]) {
            const detail = quote.detailList[index]
            rowData[`supplier${supplierIndex}_manufacturer`] =
              detail.manufacturer
            rowData[`supplier${supplierIndex}_itemName`] = detail.itemName
            rowData[`supplier${supplierIndex}_itemNo`] = detail.itemNo
            rowData[`supplier${supplierIndex}_finalNum`] = detail.finalNum
            rowData[`supplier${supplierIndex}_quotNum`] = detail.quotNum
            rowData[`supplier${supplierIndex}_price`] = detail.price
            rowData[`supplier${supplierIndex}_finalPrice`] = detail.finalPrice
            rowData[`supplier${supplierIndex}_remark`] = detail.remark
            rowData[`supplier${supplierIndex}_isWins`] = detail.isWins
            rowData[`supplier${supplierIndex}_rate`] = detail.rate
            rowData[`supplier${supplierIndex}_data`] = detail
          }
        })

        return rowData
      })
    },

    handleQuotNumChange(value, quote, rowIndex) {
      quote.detailList[rowIndex].quotNum = value
      this.updateFinalPrice()
    },

    saveColumnState() {
      if (this.columnApi) {
        const state = this.columnApi.getColumnState()
        localStorage.setItem('comparePrice_columnState', JSON.stringify(state))
      }
    },

    loadColumnState() {
      const saved = localStorage.getItem('comparePrice_columnState')
      if (saved && this.columnApi) {
        const state = JSON.parse(saved)
        this.columnApi.applyColumnState({
          state,
          applyOrder: true,
        })
      }
    },

    refreshTable() {
      if (this.gridApi) {
        this.gridApi.refreshCells({ force: true })
      }
    },

    debounce(fn, delay) {
      let timer = null
      return function () {
        const context = this
        const args = arguments
        clearTimeout(timer)
        timer = setTimeout(() => {
          fn.apply(context, args)
        }, delay)
      }
    },

    toggleColumnVisibility(columnId, visible) {
      if (this.columnApi) {
        this.columnApi.setColumnsVisible([columnId], visible, true)

        setTimeout(() => {
          this.gridApi.sizeColumnsToFit()
        }, 100)
      }
    },

    toggleFullScreen() {
      if (!this.isFullScreen) {
        const element = this.$el
        if (element.requestFullscreen) {
          element.requestFullscreen()
        } else if (element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen()
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      }
      this.isFullScreen = !this.isFullScreen
    },

    async downLoadExcel() {
      this.loading = true
      try {
        await this.getBlobDownload(
          '/business/shipAffairs/purchaseManage/excelExportComparePrice',
          {
            enquiryId: this.enquiryId,
          },
        )
      } catch (e) {
        this.$dialog.message.error('操作失败,请稍后重试')
      } finally {
        this.loading = false
      }
    },
  },

  mounted() {
    this.$nextTick(() => {
      if (this.gridApi) {
        setTimeout(() => {
          this.gridApi.refreshCells({
            force: true,
            suppressFlash: false,
          })
        }, 100)
      }
    })

    window.addEventListener('resize', this.refreshTable)

    // 监听全屏变化
    document.addEventListener('fullscreenchange', () => {
      this.isFullScreen = !!document.fullscreenElement
    })
    document.addEventListener('webkitfullscreenchange', () => {
      this.isFullScreen = !!document.webkitFullscreenElement
    })
    document.addEventListener('msfullscreenchange', () => {
      this.isFullScreen = !!document.msFullscreenElement
    })
  },

  beforeDestroy() {
    this.saveColumnState()
    window.removeEventListener('resize', this.refreshTable)

    // 移除监听器
    document.removeEventListener('fullscreenchange', () => {})
    document.removeEventListener('webkitfullscreenchange', () => {})
    document.removeEventListener('msfullscreenchange', () => {})
  },
}
</script>

<style lang="scss" scoped>
.v-card {
  max-width: 100%;
  overflow-x: auto;
}

.use-divider {
  width: 100%;
  overflow-x: visible;
}

.full-width-grid {
  width: 100%;
}

:deep(.ag-root-wrapper) {
  width: 100% !important;
  overflow: visible;
}

:deep(.ag-root) {
  width: 100% !important;
}

:deep(.ag-body-viewport) {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

:deep(.ag-body-horizontal-scroll) {
  width: 100% !important;
}

:deep(.ag-center-cols-container) {
  min-width: 100%;
  width: auto !important;
}

:deep(.ag-header-container) {
  min-width: 100%;
  width: auto !important;
}

:deep(.ag-body-horizontal-scroll-viewport) {
  overflow-x: scroll !important;
}

:deep(.ag-body-vertical-scroll-viewport) {
  overflow-y: scroll !important;
}

td {
  padding: 0 !important;
  vertical-align: top !important;
}

.v-card__text {
  overflow-x: auto;
}

:deep(.switch) {
  input:checked + .slider {
    background-color: #4caf50;
  }

  input:disabled + .slider {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.full-screen-btn {
  margin-left: 0;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 0 20px;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

    .v-icon {
      animation: pulse 1s infinite;
    }
  }

  &.is-fullscreen {
    background-color: #ff5252 !important;

    &:hover {
      background-color: #ff1744 !important;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

:deep(.v-card) {
  &:fullscreen {
    background: white;
    padding: 20px;

    .v-card__title {
      position: sticky;
      top: 0;
      background: white;
      z-index: 1;
    }
  }
}
</style>
