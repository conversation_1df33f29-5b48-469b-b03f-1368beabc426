<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        备件选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          fuzzy-label="模糊搜索"
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="3">
              <v-autocomplete
                v-model="searchObj.equipmentInformationId"
                label="设备主体"
                outlined
                dense
                :items="firstEquipments"
                clearable
              ></v-autocomplete>
            </v-col>
            <!-- <v-col cols="12" sm="6" md="3">
              <v-autocomplete
                v-model="searchObj.equipmentSecondId"
                label="大模块"
                outlined
                dense
                :items="secondEquipments"
                clearable
              ></v-autocomplete>
            </v-col> -->
            <v-col cols="12" sm="6" md="3">
              <v-autocomplete
                v-model="searchObj.equipmentId"
                label="子设备"
                outlined
                dense
                :items="subEquipments"
                :loading="loading"
                clearable
              ></v-autocomplete>
            </v-col>
          </template>
          <template #btns></template>
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo.chShipName }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'spare-part-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.tableName = '子设备管理'
    this.reqUrl = '/business/shipAffairs/equipmentInformation/componentPage'
    this.headers = [
      { text: '备件号', value: 'componentNumber' },
      { text: '备件名称', value: 'componentEname' },
      { text: '单位', value: 'componentUnit' },
      // { text: '在图编号', value: 'drawingNo' },
      { text: '型号', value: 'componentModel' },
      { text: '参数', value: 'componentParameter' },
      { text: '规格', value: 'componentSpecifications' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    shipCode: String,
    components: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {},
      selected: [],
      firstEquipments: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    'searchObj.equipmentSecondId': {
      handler(val) {
        if (val) this.loadSubEqu()
      },
    },
    'searchObj.equipmentInformationId': {
      handler(val) {
        if (val) this.loadSecondEquipment()
      },
    },
    searchRemain(val) {
      this.searchObj = val
    },
    components(val) {
      this.selected = val.map((i) => {
        return { ...i, vid: i.id, id: i.componentId }
      })
    },
    shipCode(val) {
      if (val) this.searchObj.shipCode = val
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    // 子设备获取
    async loadSubEqu() {
      this.loading = true
      const reqUrl = this.searchObj.equipmentSecondId
        ? '/business/shipAffairs/equipmentInformation/getEquipmentThridBySecondId'
        : '/business/shipAffairs/equipmentInformation/getEquipmentThirdByMainEquipmentId'
      const { data } = await this.getAsync(reqUrl, {
        equipmentId: this.searchObj?.equipmentId,
        secondEquipmentId: this.searchObj?.equipmentSecondId,
      })
      this.subEquipments = data?.map((i) => {
        return {
          text: i.subEquipmentCname,
          value: i.id,
        }
      })
      this.loading = false
    },
    // 大模块
    async loadSecondEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentSecondByMainEquipmentId',
        { MainEquipmentId: this.searchObj.equipmentInformationId },
      )
      this.secondEquipments = data?.map((i) => {
        return {
          text: i.name,
          value: i.id,
        }
      })
    },
    // 设备主体
    async loadFirstEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { current: 1, size: 99, shipCode: this.shipCode },
      )
      const { records } = data
      this.firstEquipments = records?.map((i) => {
        return {
          text: i.equipmentCname,
          value: i.id,
        }
      })
    },
    confirm() {
      const components = this.selected.map((i) => {
        const comp = {
          ...i,
          cid: `${i.componentEname}:${i.componentNumber}`,
        }
        return comp
      })
      this.$emit('update:components', components)
      this.$emit('change', false)
    },
  },
  mounted() {
    this.loadFirstEquipment()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
