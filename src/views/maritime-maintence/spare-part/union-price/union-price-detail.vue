<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['批量报价:新建报价']"
      title="新建报价"
      tooltip="新建批量报价"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      @save="save"
    >
      <template #基本信息>
        <v-form ref="form">
          <v-card-text>
            <v-row>
              <!-- <v-col md="3" cols="12">
                <v-dict-select
                  v-model="detail.portType"
                  label="港口分类"
                  dictType="port_type"
                  :rules="[rules.required]"
                  required
                ></v-dict-select>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="detail.currencyId"
                  :items="currencyInfo"
                  item-text="ccyName"
                  item-value="id"
                  label="币种"
                  dense
                  outlined
                  :rules="[rules.required]"
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.itemPrice"
                  label="单价"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-dict-select
                  v-model="detail.itemId"
                  label="备件标准化类别"
                  clearable
                  dense
                  outlined
                  dict-type="component_standard_ype"
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  v-model="detail.itemUnit"
                  label="单位"
                  required
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-dialog-select
                  label="供应商"
                  item-text="name"
                  item-value="id"
                  v-model="detail.supplierId"
                  :headers="supHeaders"
                  :rules="[rules.required]"
                  req-url="/business/shipAffairs/Supplier/list"
                  :search-remain="searchObj2"
                >
                  <template #searchflieds>
                    <v-col cols="12" sm="6" md="3">
                      <v-text-field
                        label="中文名称"
                        v-model="searchObj2.name"
                      ></v-text-field>
                    </v-col>
                  </template>
                </v-dialog-select>
              </v-col> -->
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  label="有效开始时间"
                  v-model="detail.validStartTime"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  outlined
                  dense
                  label="有效截止时间"
                  v-model="detail.validDeadline"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-file-input
                  outlined
                  dense
                  accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
                  label="导入EXCEL"
                  :rules="[rules.required]"
                  v-model="file"
                ></v-file-input>
              </v-col> -->
            </v-row>
          </v-card-text>
        </v-form>
      </template>
      <!-- <template #报价内容按钮>
        <v-btn
          :disabled="components.length === 0"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="exportExcel"
          v-permission="['报价内容:导出所选']"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出所选
        </v-btn>
        <v-btn
          :disabled="!detail.shipCode"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['报价内容:选择备件']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择备件
        </v-btn>
      </template> -->
      <!-- <template #报价内容>
        <v-col cols="12" md="3">
          <v-ship-select
            outlined
            dense
            v-model="detail.shipCode"
          ></v-ship-select>
        </v-col>
        <v-table-list
          item-key="componentId"
          :headers="componentHeaders"
          :items="components"
        ></v-table-list>
      </template> -->
    </v-detail-view>
    <!-- <spare-part-select
      v-model="dialog"
      :shipCode="detail.shipCode"
      :components.sync="components"
    ></spare-part-select> -->
  </v-container>
</template>
<script>
import currencyHelper from '@/mixin/currencyHelper'
// import sparePartSelect from './private/spare-part-select.vue'
export default {
  // components: { sparePartSelect },
  name: 'union-price-detail',
  mixins: [currencyHelper],
  created() {
    this.backRouteName = 'union-price-list'
    this.subtitles = ['基本信息']
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
  },
  data() {
    return {
      detail: {},
      searchObj2: {},
      file: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      components: [],
      dialog: false,
      componentHeaders: [
        { text: '备件名称:备件号', value: 'cid' },
        { text: '库存数量', value: 'stockQuantity' },
        { text: '最高库存', value: 'maximumInventory' },
      ],
    }
  },

  methods: {
    async loadDetail() {
      //   const { data } = await this.getAsync('', { id: this.$$route.params.id })
    },
    async save(goBack) {
      if (!this.$refs.form.validate()) return
      // let formData = new FormData()
      // formData.append('file', this.file, false)
      // formData.append('itemType', '01')
      // if (this.detail) {
      //   Object.keys(this.detail).forEach((key) => {
      //     formData.append(key, this.detail[key])
      //   })
      // }
      // const { errorRaw } = await this.postAsync(
      //   '/business/shipAffairs/purchasePrice/purchasePriceTableImport',
      //   formData,
      // )
      this.detail.itemType = '01'
      const reqUrl =
        '/business/shipAffairs/purchasePrice/purchasePriceSaveOrUpdate'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.detail },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      if (!errorRaw) goBack()
    },

    createCom() {
      this.searchObj = { equipmentInformationId: this.detail.equipmentId }
      this.dialog = true
    },
    async exportExcel() {
      const items = this.components.map((i) => i.cid)
      const { errorRaw } = await this.blobDownload(
        '/business/shipAffairs/purchasePrice/purchasePriceTableExport',
        { items, itemType: '01' },
        '备件批量报价导入模板.xlsx',
      )
      if (errorRaw) this.$dialog.message.error(errorRaw)
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
