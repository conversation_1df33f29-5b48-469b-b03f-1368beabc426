<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-supply-select-happen-date
                  v-model="formData.supplierId"
                  :rules="[rules.required]"
                  :ship-code="shipCode"
                  :happen-date="happenDate"
                  :readonly="isEdit"
                  @select="
                    (item) => {
                      currency = item.currency
                      formData.supplierName = `${item.name}/${
                        item.nameEn || ''
                      }`
                    }
                  "
                  :init-selected="initSupply"
                ></v-supply-select-happen-date>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="formData.currencyId"
                  :items="currency"
                  item-text="ccyCode"
                  item-value="currencyType"
                  label="币种"
                  :rules="[rules.required]"
                  outlined
                  dense
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="推荐理由"
                  v-model="formData.remark"
                  outlined
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import currencyHelper from '@/mixin/currencyHelper'
import vSupplySelectHappenDate from '@/components/v-supply-select-happenDate.vue'
export default {
  components: { vSupplySelectHappenDate },
  mixins: [currencyHelper],
  name: 'recommendSup',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '账号', value: 'account' },
      { text: 'sap代码', value: 'sapCode' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    shipCode: {
      type: String,
      default: '',
    },
    happenDate: {
      type: Date,
      default: null,
    },
  },
  data() {
    return {
      searchObj2: {},
      dialog: false,
      formData: {},
      supplier: {},
      currency: [],
      rules: {
        required: (v) => !!v || v == 0 || '必填项不能为空',
      },
      initSupply: {},
      currencyId: '',
    }
  },
  watch: {
    open(val) {
      this.formData = { ...this.initialData }
      this.initSupply = {
        id: this.initialData.supplierId,
        name: this.initialData.supplierName,
      }
      this.dialog = val
      this.$refs?.form?.resetValidation()
    },
  },
  computed: {
    isEdit() {
      return !!this.initialData?.vid
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      this.formData.currency = this.currencyInfo.find(
        (i) => i.id === this.formData.currencyId,
      )?.ccyCode
      if (!this.$refs.form.validate()) {
        return
      }
      this.$emit('change', false)
      if (!this.isEdit)
        this.$emit('success', {
          vid: Math.floor(Math.random() * 1000 + 1),
          // supplyId:this.$route.params.id,
          ...this.formData,
          operationType: this.isEdit ? 2 : 1,
        })
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
