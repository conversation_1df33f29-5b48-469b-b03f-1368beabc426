<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        备件选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
        >
          <template #searchflieds>
            <!-- <v-col cols="12" sm="6" md="3">
              <v-autocomplete
                v-model="searchObj.equipmentSecondId"
                label="大模块"
                outlined
                dense
                :items="secondEquipments"
                clearable
              ></v-autocomplete>
            </v-col> -->
            <v-col cols="12" sm="6" md="3">
              <v-autocomplete
                v-model="searchObj.equipmentId"
                label="子设备"
                outlined
                dense
                :items="subEquipments"
                :loading="loading"
                clearable
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.drawingSerialNumberLike"
                label="图纸号"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.equipmentCodeLike"
                label="备件号"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                v-model="searchObj.equipmentNameLike"
                label="备件名"
                outlined
                dense
                :loading="loading"
                clearable
              ></v-text-field>
            </v-col>
          </template>
          <template #btns></template>
          <template v-slot:[`item.shipInfo`]="{ item }">
            {{ item.shipInfo.chShipName }}
          </template>
          <template v-slot:[`item.componentProperty`]="{ item }">
            {{ ['普通备件', 'SAP备件', '固定资产'][item.componentProperty] }}
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn depressed color="primary" :disabled="!selected" @click="confirm">
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'spare-part-select',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.tableName = '子设备管理'
    this.reqUrl = '/business/shipAffairs/equipmentInformation/componentPage'
    this.headers = [
      { text: '子设备', value: 'equipmentThirdCname' },
      { text: '图纸号', value: 'drawingSerialNumber' },
      // { text: '在图编号', value: 'drawingNo' },
      { text: '备件名称', value: 'componentEname' },
      { text: '备件类型', value: 'componentProperty' },
      { text: '单位', value: 'componentUnit' },
      { text: '型号', value: 'componentModel' },
      { text: '备件号', value: 'componentNumber' },
      { text: '参数', value: 'componentParameter' },
      { text: '规格', value: 'componentSpecifications' },
      { text: '图纸号', value: 'drawingSerialNumber' },
      { text: '标准化分类', value: 'standardType' },
    ]
    this.fuzzyLabel = '模糊查询'
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    searchRemain: {
      type: Object,
      default: () => ({}),
    },
    components: Array,
  },
  data() {
    return {
      dialog: false,
      formData: {},
      subEquipments: [],
      loading: false,
      secondEquipments: [],
      secondId: '',
      searchObj: {},
      selected: [],
      maoPurchase: false,
    }
  },
  watch: {
    open(val) {
      this.dialog = val
    },
    'searchObj.equipmentSecondId': {
      handler(val) {
        if (val) this.loadSubEqu()
      },
    },
    'searchObj.equipmentInformationId': {
      handler(val) {
        // if (val) this.loadSecondEquipment()
        if (val) this.loadSubEqu()
      },
    },
    searchRemain(val) {
      this.searchObj = val
    },
    components: {
      handler(val) {
        this.selected = val.map((i) => {
          return { ...i, vid: i.id, id: i.componentId, remarkk: i.remark }
        })
      },
      deep: true,
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    // 子设备获取
    async loadSubEqu() {
      this.loading = true
      // const reqUrl = this.searchObj.equipmentSecondId
      //   ? '/business/shipAffairs/equipmentInformation/getEquipmentThridBySecondId'
      //   : '/business/shipAffairs/equipmentInformation/getEquipmentThirdByMainEquipmentId'
      const reqUrl =
        '/business/shipAffairs/equipmentInformation/getEquipmentThirdByMainEquipmentId'
      const { data } = await this.getAsync(reqUrl, {
        MainEquipmentId: this.searchObj.equipmentInformationId,
        // secondEquipmentId: this.searchObj?.equipmentSecondId,
      })
      this.subEquipments = data?.map((i) => {
        return {
          text: i.subEquipmentCname,
          value: i.id,
        }
      })
      this.loading = false
    },
    // 大模块
    async loadSecondEquipment() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/getEquipmentSecondByMainEquipmentId',
        { MainEquipmentId: this.searchObj.equipmentInformationId },
      )
      this.secondEquipments = data?.map((i) => {
        return {
          text: i.name,
          value: i.id,
        }
      })
    },
    confirm() {
      // 判断备件基础信息标准化分类、费用科目是否都是是锚、锚链  锚、锚链申请不可与其他固定资产同时申请
      // 先判断标准版分类
      const filteredItems = this.selected.filter((item) =>
        ['锚', '锚链'].includes(item.standardType),
      )
      console.log('filteredItems:', filteredItems)
      if (filteredItems.length > 0) {
        // 判断是否有其他值
        const hasOtherValue = this.selected.some(
          (item) => !['锚', '锚链'].includes(item.standardType),
        )
        console.log('hasOtherValue:', hasOtherValue)
        // hasOtherValue 为 true，表示存在其他值‌
        // console.log(hasOtherValue)
        if (!hasOtherValue) {
          //不存在其他值，判断值是否一致
          // 获取第一个元素的 standardType 作为基准值
          const firstStandardType = filteredItems[0].standardType

          // 使用 every() 方法遍历数组，检查每个元素的 standardType 是否与基准值相同
          if (
            filteredItems.every(
              (item) => item?.standardType === firstStandardType,
            )
          ) {
            // 判断费用科目
            // 158199185722opqst25	辅助备件-锚
            // 1581991857290899459	辅助备件-锚链
            const firstCostSubjectId = filteredItems[0].costSubjectId
            if (
              filteredItems.every(
                (item) => item?.costSubjectId === firstCostSubjectId,
              )
            ) {
              // 走锚、锚链申请流程
              this.maoPurchase = true
            }
          } else {
            this.$dialog.message.error(
              '锚、锚链不可与其他备件一起申请，操作失败!',
            )
            return false
          }
        } else {
          this.$dialog.message.error(
            '锚、锚链不可与其他备件一起申请，操作失败!',
          )
          return false
        }
      } else {
        console.log('有其他值')
      }
      const components = this.selected.map((i) => {
        const comp = {
          ...i,
          componentId: i.id,
          id: i.vid,
          requireQuantity: i.requireQuantity || 0,
          componentNo: i.componentNumber,
          unit: i.componentUnit,
          equipmentThirdCName: i.equipmentThirdCname,
          remark: i.remarkk || '',
          standardType: i.standardType,
          costSubjectId: i.costSubjectId,
          maoPurchase: this.maoPurchase,
        }
        return comp
      })
      this.$emit('update:components', components)
      this.$emit('change', false)
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
