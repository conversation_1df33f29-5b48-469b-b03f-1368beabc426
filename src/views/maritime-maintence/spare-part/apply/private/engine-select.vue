<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    label="设备主体"
    :headers="headers"
    item-text="equipmentEname"
    :req-url="reqUrl"
    :search-remain="searchObj"
    @update="update"
    @open="updateSearchObj"
    max-width="1300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
    :rules="[rules.required]"
    fuzzyLabel="模糊查询"
  >
    <!-- //6 查看全部分类 //5查看通导  //0查看机务 -->
    <template v-slot:searchflieds>
      <v-col cols="12" sm="6" md="3">
        <v-select
          v-model="searchObj.euipmentType"
          outlined
          label="设备类型"
          dense
          clearable
          :items="
            euipmentTypeTD == 5
              ? euipmentTypesTD
              : euipmentTypeTD == 0
              ? euipmentTypes
              : euipmentTypesNull
          "
        ></v-select>
      </v-col>
    </template>
    <template v-slot:[`item.equipmentStatus`]="{ item }">
      {{ ['', '使用', '停用', '报废'][item.equipmentStatus] }}
    </template>
    <template v-slot:[`item.euipmentType`]="{ item }">
      {{ ['主机', '副机', '辅助设备', '通导设备'][item.euipmentType] }}
    </template>
    <template v-slot:[`item.equipmentNumber`]="{ item }">
      <v-chip-group color="primary" column>
        <v-chip small v-for="tag in item.equipmentNumber" :key="tag">
          {{ tag }}
        </v-chip>
      </v-chip-group>
    </template>
  </v-dialog-select>
</template>
<script>
export default {
  name: 'engine-select',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/business/shipAffairs/equipmentInformation/firstPage'
    this.headers = [
      { text: '英文名称', value: 'equipmentEname' },
      // { text: '中文名称', value: 'equipmentCname' },
      // { text: '设备编码', value: 'equipmentCode' },
      { text: '设备型号', value: 'equimentModel' },
      { text: '状态', value: 'equipmentStatus' },
      { text: '类型', value: 'euipmentType' },
      { text: '生产厂家', value: 'manufacture' },
      { text: '设备核心参数', value: 'parameter' },
    ]
    this.equipmentStatuses = [
      { text: '使用', value: 1 },
      { text: '停用', value: 2 },
      { text: '报废', value: 3 },
    ]
    this.euipmentTypes = [
      { text: '主机', value: '0' },
      { text: '副机', value: '1' },
      { text: '辅助设备', value: '2' },
      // { text: '通导设备', value: '3' },
    ]
    this.euipmentTypesTD = [
      // { text: '主机', value: '0' },
      // { text: '副机', value: '1' },
      // { text: '辅助设备', value: '2' },
      { text: '通导设备', value: '3' },
    ]
    this.euipmentTypesNull = [
      { text: '主机', value: '0' },
      { text: '副机', value: '1' },
      { text: '辅助设备', value: '2' },
      { text: '通导设备', value: '3' },
    ]
  },
  props: {
    shipCode: String,
    applyType: String,
    value: [String, Object],
    disabled: [String, Boolean],
    readonly: [String, Boolean],
    numbers: Array,
    initSelected: Object,
    // read
  },
  data() {
    return {
      searchObj: { shipCode: '', euipmentType: '' },
      val: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      euipmentTypeTD: 6, //6 查看全部分类 //5查看通导  //0查看机务
    }
  },

  watch: {
    value(val) {
      this.val = val
    },
    shipCode(val) {
      this.searchObj.shipCode = val
    },
    applyType(val) {
      // console.log(val) //6 查看全部分类 //5查看通导  //0查看机务  //4固定资产
      //       { text: '主机', value: '0' },
      // { text: '副机', value: '1' },
      // { text: '辅助设备', value: '2' },
      // { text: '通导设备', value: '3' },
      // this.searchObj.euipmentType = 99  去除通导设备类型
      console.log(13123123131212312)
      if (val == 5) {
        this.searchObj.euipmentType = 3
        this.searchObj.updateSearchObj = 3
        this.euipmentTypeTD = 5
      } else if (val == 6) {
        this.searchObj.euipmentType = 99
        this.searchObj.updateSearchObj = null
        this.euipmentTypeTD = 6
      } else if (val == 3) {
        this.searchObj.euipmentType = null
        this.searchObj.updateSearchObj = null
        this.euipmentTypeTD = 6
      } else {
        this.searchObj.euipmentType = 99
        this.searchObj.updateSearchObj = null
        this.euipmentTypeTD = 0
      }
    },
    'searchObj.euipmentType'(val) {
      // console.log(val) //6 查看全部分类 //5查看通导  //0查看机务  //4固定资产
      //       { text: '主机', value: '0' },
      // { text: '副机', value: '1' },
      // { text: '辅助设备', value: '2' },
      // { text: '通导设备', value: '3' },
      // this.searchObj.euipmentType = 99  去除通导设备类型
      console.log(13123123131212312)
      if (val != 3) {
        if (this.applyType != 3) {
          this.searchObj.euipmentType = 99
        }
      }
    },
  },

  methods: {
    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },
    update() {
      this.$emit('update', this.val.id)
      // console.log(this.val)
      this.$emit('update:numbers', this.val.equipmentNumber)
      this.$emit('update:manufacture', this.val.manufacture)
      this.$emit('update:equimentModel', this.val.equimentModel)
    },
    updateSearchObj() {
      //   // this.$nextTick(() => {
      //   // })
      //   if (this.searchObj.shipCode !== this.shipCode) {
      //     console.log('asd')
      //     this.searchObj.shipCode = ''
      //     this.$nextTick(() => {
      //       this.searchObj.shipCode = this.shipCode
      //     })
      //   }
    },
  },

  mounted() {
    this.searchObj.shipCode = this.shipCode
  },
}
</script>

<style></style>
