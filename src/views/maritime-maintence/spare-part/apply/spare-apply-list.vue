<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :search-date="searchDate"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :push-params="pushParams"
      :search-remain="searchObj"
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.applyType"
            :items="applyTypeOptions"
            label="申请类型"
            clearable
            dense
            outlined
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-select
            v-model="searchObj.businessStatus"
            :items="businessStatusOptions"
            label="业务状态"
            clearable
            dense
            outlined
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :disabled="!canCopy"
          outlined
          tile
          color="info"
          class="mx-1"
          @click="copyApplies"
          v-permission="['备件申请:复制']"
        >
          <v-icon left>mdi-content-copy</v-icon>
          复制
        </v-btn>
        <v-btn
          :disabled="!canEnd"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="isItemEnd"
          v-permission="['备件申请:完结']"
        >
          <v-icon left>mdi-close-circle</v-icon>
          完结
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :to="{ name: 'spare-apply-detail', params: { id: 'new' } }"
          v-permission="['备件申请:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['备件申请:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.shipInfo`]="{ item }">
        {{ item.shipInfo.chShipName }}
      </template>
      <!-- <template v-slot:[`item.isEnd`]="{ item }">
        <v-chip small color="warning" v-if="item.isEnd == false">未完结</v-chip>
        <v-chip small v-if="item.isEnd == true">已完结</v-chip>
      </template> -->
      <template v-slot:[`item.applyType`]="{ item }">
        <v-chip small v-if="item.applyType == 1">常规</v-chip>
        <v-chip small color="warning" v-if="item.applyType == 2">紧急</v-chip>
        <v-chip small color="error" v-if="item.applyType == 3">坞修</v-chip>
        <v-chip small color="error" v-if="item.applyType == 4">固定资产</v-chip>
        <v-chip small color="error" v-if="item.applyType == 5">
          通导设备固定资产
        </v-chip>
      </template>
      <!-- <template v-slot:[`item.isDockRepair`]="{ item }">
        {{ item.isDockRepair ? '是' : '否' }}
      </template> -->
    </v-table-searchable>
  </v-container>
</template>
<script>
// applicationNo	申请单号;根据编码规则生成	string
// applyDate	申请日期	string
// applyDept	申请部门	string
// applyPurpose	申请目的	string
// applyType	申请类型;申请类型:1 常规 2 紧急 3 特急	integer
// businessStatus	业务状态	string
// commonAttachments	附件id列表	array	CommonAttachment
// equipmentId	设备主体ID	string
// id	物理主键	string
// isDockRepair	是否坞修	boolean
// portId	港口id;送货港口	string
// remark	备注	string
// shipCode	船舶编码	string
// status	流程状态	string
export default {
  name: 'spare-apply-list',
  created() {
    this.tableName = '备件申请'
    this.reqUrl = '/business/shipAffairs/purchaseManage/componentApplyPage'
    this.headers = [
      { text: '所属船舶', value: 'shipInfo' },
      { text: '申请单号', value: 'applicationNo' },
      { text: '申请日期', value: 'applyDate' },
      { text: '申请部门', value: 'applyDept' },
      { text: '申请岗位', value: 'applicantPost' },
      { text: '申请类型', value: 'applyType' },
      // { text: '是否坞修', value: 'isDockRepair' },
      { text: '备注', value: 'remark' },
      // { text: '是否完结', value: 'isEnd' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '附件', value: 'attachmentRecords' },
    ]
    this.searchDate = {
      label: '申请时间',
      interval: true,
    }
    this.fuzzyLabel = '模糊查询'
    this.pushParams = { name: 'spare-apply-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        isMe: true,
        applyType: null,
        businessStatus: null,
      },
      applyTypeOptions: [
        { text: '常规', value: 1 },
        { text: '紧急', value: 2 },
        { text: '坞修', value: 3 },
        { text: '固定资产', value: 4 },
        { text: '通导设备固定资产', value: 5 },
      ],
      businessStatusOptions: [
        { text: '未提交', value: '未提交' },
        { text: '填写申请单', value: '填写申请单' },
        { text: '大副', value: '大副' },
        { text: '轮机长', value: '轮机长' },
        { text: '船长', value: '船长' },
        { text: '机务主管', value: '机务主管' },
        { text: '通导信息主管', value: '通导信息主管' },
        { text: '备件采购主管', value: '备件采购主管' },
        { text: '通导采购主管', value: '通导采购主管' },
        { text: '审批通过', value: '审批通过' },
        { text: '审批已驳回', value: '审批已驳回' },
        { text: '全部询价', value: '全部询价' },
        { text: '已完结', value: '已完结' },
      ],
    }
  },
  computed: {
    canDelte() {
      return this.selected && this.selected.status === '1'
    },
    canEnd() {
      return this.selected.status == 3 && this.selected.isEnd == false
    },
    canCopy() {
      return this.selected
    },
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/componentApplyDelete',
        { applyId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async isItemEnd() {
      if (!(await this.$dialog.msgbox.confirm('确定完结此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/componentApplyIsEnd',
        { applyId: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`完结失败，请重试`)
        return
      }
      this.$dialog.message.success(`完结成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    async download() {
      await this.getBlobDownload(
        '/business/shipAffairs/purchaseStatistic/getComparisonDetail?itemCode=1070-0220-0005-99&itemName=Hex key&itemType=01&year=2022',
        {},
        'a.xlsx',
      )
    },
    async copyApplies() {
      if (!(await this.$dialog.msgbox.confirm('确定复制所选申请单？'))) return

      try {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/purchaseManage/componentApplyCopy',
          { applyId: this.selected.id },
        )
        if (errorRaw) {
          this.$dialog.message.error(
            `复制申请单 ${this.selected.applicationNo} 失败`,
          )
          return
        }
        this.$dialog.message.success('复制申请单成功')
        await this.$refs.table.loadTableData()
        this.selected = []
      } catch (error) {
        console.error('复制失败:', error)
        this.$dialog.message.error('复制失败，请重试')
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
