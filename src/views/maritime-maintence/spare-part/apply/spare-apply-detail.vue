<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['备件申请:编辑']"
      :title="`备件申请-${isEdit ? detail.applicationNo : '新增'}`"
      :tooltip="isEdit ? detail.applicationNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="!detail.auditParams || detail.auditParams.taskId"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
      @save="save2"
      @submit="submit"
    >
      <template v-if="detail.status == 3" v-slot:custombtns>
        <!-- <template v-slot:custombtns> -->
        <v-btn
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: detail.systemReportId },
          }"
          color="info"
          small
          class="mx-1"
          v-permission="['备件申请:查看部门报表']"
        >
          查看部门报表
        </v-btn>
      </template>
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12">
                <v-alert type="info" color="green" text dense class="mb-0">
                  操作顺序：选择备件-保存-下载明细-上传附件-保存并提交
                </v-alert>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="isEdit"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  readonly
                  outlined
                  dense
                  v-model="detail.applyDate"
                  use-today
                  label="申请日期"
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-dept
                  :readonly="!canSubmit"
                  dense
                  outlined
                  v-model="detail.applyDept"
                  label="申请部门"
                  :items="depts"
                  :rules="[rules.required]"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3">
                <v-ship-station-jw
                  v-model="detail.applicantPost"
                  :disabled="
                    ($local.data.get('userInfo').userType == '2' &&
                      !$local.data
                        .get('userInfo')
                        .roleName.includes('轮机长')) ||
                    !canSubmit
                  "
                ></v-ship-station-jw>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :readonly="!canSubmit"
                  dense
                  outlined
                  v-model="detail.applyType"
                  label="申请类型"
                  :items="applyTypes"
                  :rules="[rules.required]"
                ></v-select>
              </v-col>

              <v-col cols="12" md="3">
                <!-- <v-text-field
                  v-if="detail.equipmentChName"
                  label="设备主体"
                  dense
                  readonly
                  outlined
                  v-model="detail.equipmentChName"
                ></v-text-field> -->
                <engine-select
                  :readonly="isEdit"
                  :disabled="!detail.shipCode || !detail.applyType"
                  v-model="detail.equipmentId"
                  :shipCode="detail.shipCode"
                  :applyType="detail.applyType"
                  :numbers.sync="numbers"
                  :manufacture.sync="detail.manufacture"
                  :equimentModel.sync="detail.equimentModel"
                  :initSelected="initEngine"
                  :rules="[rules.required]"
                  @change="clearComponents"
                ></engine-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :readonly="isEdit"
                  dense
                  outlined
                  v-model="detail.equipmentNumber"
                  label="设备序列号"
                  :items="numbers"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <port-select-dialog2
                  :readonly="
                    detail.businessStatus !== '通导信息主管' &&
                    detail.businessStatus !== '机务主管' &&
                    !canSubmit
                  "
                  v-model="detail.portId"
                  :initSelected="initPort"
                ></port-select-dialog2>
              </v-col>
              <v-col cols="12" md="3">
                <vs-date-picker
                  :readonly="
                    detail.businessStatus !== '通导信息主管' &&
                    detail.businessStatus !== '机务主管' &&
                    !canSubmit
                  "
                  outlined
                  dense
                  v-model="detail.arriveDate"
                  :rules="[rules.required]"
                  label="到港日期"
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" md="3" v-if="false">
                <v-select
                  dense
                  v-model="detail.isDockRepair"
                  label="坞修"
                  outlined
                  :items="yn"
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-select>
              </v-col>
              <!-- 岸端审批需填写 -->
              <v-col cols="12" md="3" v-if="!isShip">
                <end-time-picker
                  v-model="detail.openDate"
                  label="报价截止时间"
                ></end-time-picker>
              </v-col>
              <!-- <v-col cols="12" md="3" v-if="!isShip">
                <v-select
                  v-model="detail.needSendOa"
                  :items="[
                    { text: '是', value: 1 },
                    { text: '否', value: 0 },
                  ]"
                  label="是否有OA前置立项"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  :disabled="detail.status == 3"
                  :readonly="detail.status == 3"
                ></v-select>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <v-text-field
                  v-if="detail.needSendOa == 1"
                  label="OA项目编号"
                  v-model="detail.itemNo"
                  dense
                  :rules="[rules.required]"
                  required
                  outlined
                  :disabled="detail.status == 3"
                  :readonly="detail.status == 3"
                ></v-text-field>
              </v-col>-->
              <!-- <v-col class="py-0" cols="12">
                <v-textarea
                  v-model="detail.applyPurpose"
                  label="申请目的"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col> -->
              <v-col cols="12" class="py-0">
                <v-textarea
                  v-model="detail.remark"
                  label="备注"
                  dense
                  outlined
                  :readonly="!canSubmit1"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
          <v-row>
            <v-col cols="6">
              <b>生产厂家:</b>
              {{ detail.manufacture }}
            </v-col>
            <v-col cols="6">
              <b>设备型号:</b>
              {{ detail.equimentModel }}
            </v-col>
          </v-row>
        </v-container>
      </template>
      <!-- 共用预算管理审批供应商表 -->
      <template
        v-if="detail.status != '3' && !isShip && !isBulkSupplierSubject"
        #推荐供应商按钮
      >
        <v-btn
          :disabled="!detail.shipCode || components.length == 0"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createSup"
          v-permission="['推荐供应商:选择供应商']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择供应商
        </v-btn>
        <v-btn
          :disabled="!selectedSup"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delSup"
          v-permission="['推荐供应商:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-if="!isShip" #推荐供应商>
        <v-form ref="form2">
          <v-table-list
            item-key="vid"
            v-model="selectedSup"
            :headers="supplierHeaders"
            :items="supplyPriceModifyList"
          >
            <template
              v-if="detail.status != 3"
              v-slot:[`item.remark`]="{ item }"
            >
              <!-- <v-text-field
                v-model="item.remark"
                label="申请理由"
                single-line
                dense
              ></v-text-field> -->
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    @click="editRemark2(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.remark"
                    label="推荐理由"
                    single-line
                    dense
                  ></v-text-field>
                </template>
                <span>{{ item.remark }}</span>
              </v-tooltip>
            </template>
          </v-table-list>
        </v-form>
      </template>
      <template v-if="detail.status != '3'" #备件列表按钮>
        <!-- <v-col cols="12" md="2">
          <v-file-input
            v-if="canEdit"
            :disabled="detail.hasDownload != 'YES'"
            small
            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
            label="导入EXCEL"
            v-model="file"
            v-permission="['备件申请:导入明细']"
          ></v-file-input>
        </v-col> -->
        <v-btn
          v-if="canEdit"
          :disabled="!detail.fileName"
          small
          tile
          color="error"
          class="mx-1"
          @click="dowExcel"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          下载明细
        </v-btn>
        <!-- v-permission="['备件申请:下载明细']" -->
        <v-btn
          v-if="canEdit"
          :disabled="!detail.equipmentId || !detail.arriveDate"
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createCom"
          v-permission="['备件列表:选择备件']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择备件
        </v-btn>
        <v-btn
          :disabled="!select"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delCom"
          v-permission="['备件列表:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #备件列表>
        <v-form ref="form2">
          <v-table-list
            v-model="select"
            item-key="componentId"
            :headers="componentHeaders"
            :items="components"
          >
            <template
              v-if="detail.status == 2"
              v-slot:[`item.auditQuantity`]="{ item }"
            >
              <v-text-field
                v-model="item.auditQuantity"
                label="审批数量"
                type="number"
                single-line
                dense
                :rules="[rules.required, rules.int, rules.aboveZero]"
              ></v-text-field>
            </template>
            <template
              v-if="detail.status != 2 && detail.status != 3"
              v-slot:[`item.requireQuantity`]="{ item }"
            >
              <v-text-field
                v-model="item.requireQuantity"
                label="申请数量"
                type="number"
                single-line
                dense
                :rules="[rules.required, rules.int, rules.aboveZero]"
              ></v-text-field>
            </template>
            <template
              v-if="detail.status != 3"
              v-slot:[`item.remark`]="{ item }"
            >
              <!-- <v-text-field
                v-model="item.remark"
                label="申请理由"
                single-line
                dense
              ></v-text-field> -->
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    @click="editRemark(item)"
                    v-bind="attrs"
                    v-on="on"
                    v-model="item.remark"
                    label="申请理由"
                    single-line
                    dense
                  ></v-text-field>
                </template>
                <span>{{ item.remark }}</span>
              </v-tooltip>
            </template>
          </v-table-list>
        </v-form>
        <v-card-text>
          <v-file-input
            v-if="canEdit"
            :disabled="detail.hasDownload != 'YES'"
            small
            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12"
            label="导入EXCEL(下载的明细在此处上传)"
            v-model="file"
          ></v-file-input>
          <!-- v-permission="['备件申请:导入明细']" -->
        </v-card-text>
        <v-card-text>
          （用于上传其他附件）
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="changeAttachment"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <spare-part-select
      v-model="dialog"
      :searchRemain="searchObj"
      :components.sync="components"
    ></spare-part-select>
    <v-dialog v-model="dialog1" max-width="600">
      <template v-slot:default="dialog1">
        <v-card style="height: 320px">
          <v-card-title>
            编辑申请理由
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-0" style="color: black">
                  备件号：{{ editRemarkDetails.componentNo }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  图纸号：{{ editRemarkDetails.drawingSerialNumber }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  备件名称：{{ editRemarkDetails.componentEname }}
                </v-col>
                <v-col cols="12" class="py-0" style="color: black">
                  申请数量：{{ editRemarkDetails.requireQuantity }}
                </v-col>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="申请理由"
                    v-model="editRemarkDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <v-dialog v-model="dialog2" max-width="600">
      <template v-slot:default="dialog2">
        <v-card style="height: 320px">
          <v-card-title>
            编辑推荐理由
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark2"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog2.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-0" style="color: black">
                  供应商：{{ editRemarkDetails2.supplierName }}
                </v-col>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="推荐理由"
                    v-model="editRemarkDetails2.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <recommend-sup
      v-model="dialogSup"
      @success="success"
      :initial-data="initialData"
      :shipCode="detail.shipCode"
      :happenDate="detail.openDate"
    ></recommend-sup>
  </v-container>
</template>
<script>
// componentChName	备件中文名称	string
// componentId	备件id	string
// conpinentEnName	备件英文名称	string
// drawingNo	在图编号	string
// equipmentId	子设备名称	string
// id	物理主键	string
// remark	备注	string
// requireQuantity	申请数量	integer
// stockQuantity	库存数量	integer
import PortSelectDialog2 from '../../components/port-select-dialog2.vue'
import engineSelect from './private/engine-select.vue'
import SparePartSelect from './private/spare-part-select.vue'
import recommendSup from './private/recommendSup.vue'
import EndTimePicker from '@/views/maritime-maintence/components/enquiry/end-time-picker.vue'
// import routerControl from '@/mixin/routerControl'
export default {
  components: {
    EndTimePicker,
    engineSelect,
    PortSelectDialog2,
    SparePartSelect,
    recommendSup,
  },
  name: 'spare-apply-detail',
  // mixins: [routerControl],
  created() {
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.backRouteName = 'spare-apply-list'
    this.subtitles = ['基本信息', '推荐供应商', '备件列表']
    this.depts = ['甲板部', '轮机部']
    this.applyTypes = [
      { text: '常规', value: 1 },
      { text: '紧急', value: 2 },
      { text: '坞修', value: 3 },
      { text: '固定资产', value: 4 },
      { text: '通导固定资产', value: 5 },
      // { text: '特急', value: 3 },
    ]
    this.yn = [
      { text: '是', value: true },
      { text: '否', value: false },
    ]
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canSubmit() {
      return !this.detail.auditParams || !!this.detail.auditParams?.isReject
    },
    canSubmit1() {
      return (
        !this.detail.auditParams ||
        !!this.detail.auditParams?.isReject ||
        this.detail.businessStatus === '备件采购主管'
      )
    },
    canEdit() {
      return (
        ['1', '4'].includes(this.detail.status) || this.detail.status == null
      )
    },
    isRejected() {
      if (!this.$refs.audit) return false
      return this.$refs.audit.adopt
    },
  },
  data() {
    return {
      detail: {
        applicationNo: '',
        shipInfo: {
          shipCode: '',
        },
        attachmentIds: [],
        supplyPriceModifyList: [],
        needSendOa: 0,
        checkFlag: false,
        hasDownload: 'NO',
      },
      numbers: [],
      mapping: {},
      needFields: [],
      select: false,
      componentHeaders: [
        { text: '备件名称', value: 'componentEname' },
        { text: '备件号', value: 'componentNo' },
        { text: '子设备', value: 'equipmentThirdCName' },
        { text: '图纸号', value: 'drawingSerialNumber' },
        { text: '单位', value: 'unit' },
        // { text: '在图编号', value: 'drawingNo' },
        { text: '库存数量', value: 'stockQuantity' },
        { text: '最高库存', value: 'maximumInventory' },
        { text: '申请数量', value: 'requireQuantity', width: 100 },
        { text: '审批数量', value: 'auditQuantity', width: 150 },
        { text: '已采购数量', value: 'enquiryQuantity', width: 150 },
        { text: '申请理由', value: 'remark', width: 300 },
      ],
      supplierHeaders: [
        { text: '供应商名称', value: 'supplierName' },
        { text: '币种', value: 'currency' },
        { text: '推荐理由', value: 'remark' },
      ],
      components: [],
      delList: [],
      dialog: false,
      searchObj: {},
      initEngine: {},
      initPort: {},
      rules: {
        required: (v) => !!v || v === false || v === 0 || '必填项不能为空',
        aboveZero: (v) => parseInt(v) > 0 || '必须大于0',
        int: (v) => /^[0-9]*$/.test(v) || '必须为整数',
      },
      equipmentInformation: {
        equimentModel: '',
        manufacture: '',
        equipmentNumberShow: '',
      },
      dialog1: false,
      editRemarkDetails: {},
      dialog2: false,
      editRemarkDetails2: {},
      initialData: {},
      selectedSup: false,
      dialogSup: false,
      supplyPriceModifyList: [],
      isBulkSupplierSubject: false,
      file: null,
    }
  },
  watch: {
    components(val) {
      console.log(val)
      if (val) {
        if (val[0].maoPurchase) {
          if (!this.isShip) {
            this.isBulkSupplierSubject = true
            this.checkBulkSupplier()
          }
        }
      }
    },
    'detail.shipCode'(_, oldVal) {
      if (!oldVal) return
      this.clearEngine()
    },
    'detail.equipmentId'(_, oldVal) {
      if (!oldVal) return
      this.clearComponents()
    },
    'detail.applyType'(_, oldVal) {
      // console.log(_)
      // console.log(oldVal)
      if (oldVal) this.clearEngineType()
      if (_) {
        if (_ == 3) {
          this.detail.isDockRepair = true
        } else {
          this.detail.isDockRepair = false
        }
        // if (val == 4) {
        //   this.searchObj.notFixed = false
        // } else {
        //   this.searchObj.notFixed = true
        // }
      }
    },
    // 'detail.applyType'(val) {
    //   if (val) {
    //     if (val == 3) {
    //       this.detail.isDockRepair = true
    //     } else {
    //       this.detail.isDockRepair = false
    //     }
    //     this.clearEngineType()
    //     // if (val == 4) {
    //     //   this.searchObj.notFixed = false
    //     // } else {
    //     //   this.searchObj.notFixed = true
    //     // }
    //   }
    // },
    file(val) {
      if (val) {
        this.importExcel()
      }
    },
  },

  methods: {
    editRemark(item) {
      // console.log(item)
      this.editRemarkDetails = item
      this.dialog1 = true
    },
    saveRemark() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1 = false
    },
    editRemark2(item) {
      // console.log(item)
      console.log(item)
      this.editRemarkDetails2 = item
      this.dialog2 = true
    },
    saveRemark2() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog2 = false
    },
    async save(goBack, notMove = false) {
      if (
        (this.detail.businessStatus == '通导信息主管' ||
          this.detail.businessStatus == '机务主管') &&
        this.isRejected &&
        this.isRejected == true
      ) {
        if (!this.detail.portId) {
          this.$dialog.message.error('请选择交货港口')
          return false
        }
        if (!this.detail.openDate) {
          this.$dialog.message.error('请选择报价截止时间')
          return false
        }
      }
      if (!this.checkMaxInv()) {
        return false
      }
      if (this.components.length === 0) {
        this.$dialog.message.warning('请添加备件')
        return false
      }
      const detailList = this.getCompWithOperation()
      if (this.detail.status == 2) {
        const isAllZero = detailList.every((item) => item.auditQuantity == 0)
        if (isAllZero) {
          this.$dialog.message.warning('审批数量不能全为0')
          return false
        }
      }
      const delList = this.detail.supplyPriceModifyList
        .filter((i) => !this.supplyPriceModifyList.includes(i))
        .map((i) => {
          return { id: i.id, operationType: 3 }
        })
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/componentApplySaveOrUpdate',
        {
          ...this.detail,
          detailList,
          supplyPriceModifyList: [...this.supplyPriceModifyList, ...delList],
        },
      )
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async save2() {
      if (
        (this.detail.businessStatus == '通导信息主管' ||
          this.detail.businessStatus == '机务主管') &&
        this.isRejected &&
        this.isRejected == true
      ) {
        if (!this.detail.portId) {
          this.$dialog.message.error('请选择交货港口')
          return false
        }
        if (!this.detail.openDate) {
          this.$dialog.message.error('请选择报价截止时间')
          return false
        }
      }
      if (!this.checkMaxInv()) {
        return false
      }
      if (this.components.length === 0) {
        this.$dialog.message.warning('请添加备件')
        return false
      }
      if (this.detail.fileName == undefined || this.detail.fileName == '') {
        const timestamp = Date.now() // 返回当前时间戳（毫秒）
        this.detail.fileName =
          this.detail.shipCode + timestamp + '备件申请明细' + '.xlsx'
      }
      const detailList = this.getCompWithOperation()
      const delList = this.detail.supplyPriceModifyList
        .filter((i) => !this.supplyPriceModifyList.includes(i))
        .map((i) => {
          return { id: i.id, operationType: 3 }
        })
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/componentApplySaveOrUpdate',
        {
          ...this.detail,
          detailList,
          supplyPriceModifyList: [...this.supplyPriceModifyList, ...delList],
        },
      )
      if (errorRaw) return false
      this.loadDetail2(data)
      // this.$router.push({
      //   name: 'spare-apply-detail',
      //   params: {
      //     id: data + 'load' + Date.now(),
      //   },
      // })
      // this.closeAndTo({ name: 'spare-apply-detail', params: { id: data } })
    },
    async submit(goBack) {
      if (this.detail.fileName == undefined || this.detail.fileName == '') {
        this.$dialog.message.error('操作失败，请保存后再提交！')
        return
      }
      if (this.canEdit) {
        if (!this.detail.checkFlag) {
          this.$dialog.message.error(
            '操作失败，请上传附件，并确认附件中的备件信息与系统数据一致，请核实后再提交！',
          )
          return
        }
      }

      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            processInstanceId: this.detail.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent: this.mapping[f.mappingCode],
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) return
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/purchaseManage/componentApplySubmit',
            { applyId: data },
          )
          if (!errorRaw) goBack()
          if (errorRaw) this.loadDetail2(this.detail.id)
        } else {
          if (this.detail.status == 4) {
            const { errorRaw } = await this.getAsync(
              '/business/shipAffairs/purchaseManage/componentApplySubmit',
              { applyId: data },
            )
            if (!errorRaw) goBack()
            if (errorRaw) this.loadDetail2(this.detail.id)
          } else {
            const error = await this.$refs.audit.submit()
            if (!error) goBack()
          }
        }
      }
    },

    async loadNeedFields() {
      if (!this.detail?.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        { processInstanceId: this.detail.auditParams.processInstanceId },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          this.mapping[t.mappingCode] = new Date(Date.now())
            .toISOString()
            .substr(0, 10)
        }
      }
    },

    changeAttachment(attachmentIds) {
      this.detail.attachmentIds = attachmentIds
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/componentApplyDetailById',
        {
          applyId: this.$route.params.id.replace(/load.*/, ''),
        },
      )
      this.detail = { ...data, shipCode: data.shipInfo.shipCode }
      this.components = [...data.detailList]
      this.initEngine = {
        equipmentEname: data.equipmentEname,
        id: data.equipmentId,
      }
      this.initPort = {
        portCn: data.portName,
        id: data.portId,
      }
      this.numbers = [data.equipmentNumber]
      this.supplyPriceModifyList = data.supplyPriceModifyList.map((s) => {
        return { ...s, vid: s.id, operationType: 0 }
      })
      this.detail.supplyPriceModifyList = this.supplyPriceModifyList
      if (!this.isShip) {
        this.checkBulkSupplier()
      }
      await this.loadNeedFields()
    },

    async loadDetail2(id) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/componentApplyDetailById',
        {
          applyId: id.replace(/load.*/, ''),
        },
      )
      this.detail = { ...data, shipCode: data.shipInfo.shipCode }
      this.components = []
      this.components = [...data.detailList]
      this.initEngine = {
        equipmentEname: data.equipmentEname,
        id: data.equipmentId,
      }
      this.initPort = {
        portCn: data.portName,
        id: data.portId,
      }
      this.numbers = [data.equipmentNumber]
      this.supplyPriceModifyList = data.supplyPriceModifyList.map((s) => {
        return { ...s, vid: s.id, operationType: 0 }
      })
      this.detail.supplyPriceModifyList = this.supplyPriceModifyList
      if (!this.isShip) {
        this.checkBulkSupplier()
      }
      await this.loadNeedFields()
    },
    createCom() {
      this.searchObj = {
        stopUse: false,
        equipmentInformationId: this.detail.equipmentId,
        notFixed:
          this.detail.applyType == 4 || this.detail.applyType == 5
            ? false
            : true,
        componentProperty:
          this.detail.applyType == 4
            ? '2'
            : this.detail.applyType == 5
            ? '3'
            : '',
        dockRepair: this.detail.applyType == 3 ? true : false,
      }
      this.dialog = true
    },
    async delCom() {
      // this.delList.push({ ...this.select, operationType: 3 })
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      this.components = this.components.filter(
        (i) => i.componentId !== this.select.componentId,
      )
      this.select = false
    },

    async clearEngine() {
      this.$dialog.message.info('由于船舶变更,自动清空设备')
      this.detail.equipmentId = ''
    },
    async clearEngineType() {
      this.$dialog.message.info('由于申请类型变更,自动清空设备')
      this.detail.equipmentId = ''
    },
    clearComponents() {
      this.$dialog.message.info('由于设备变更,自动清空备件/序列号')
      this.detail.equipmentNumber = ''
      this.components = []
    },

    getCompWithOperation() {
      const ids = this.components.map((i) => i.id)
      const delList = this.isEdit
        ? this.detail.detailList
            .filter((i) => !ids.includes(i.id))
            .map((i) => {
              return { ...i, operationType: 3 }
            })
        : []
      const others = this.components.map((i) => {
        return { ...i, operationType: i.id ? 2 : 1 }
      })
      return [...delList, ...others]
    },

    checkMaxInv() {
      for (const { stockQuantity, maximumInventory, requireQuantity } of this
        .components) {
        if (stockQuantity * 1 + requireQuantity * 1 > maximumInventory) {
          this.$dialog.message.error('申请数不得超出最大库存量!')
          return false
        }
      }
      for (const { stockQuantity, maximumInventory, auditQuantity } of this
        .components) {
        if (stockQuantity * 1 + (auditQuantity * 1 || 0) > maximumInventory) {
          this.$dialog.message.error('审批数不得超出最大库存量!')
          return false
        }
      }
      return true
    },
    createSup() {
      this.initialData = {}
      this.dialogSup = true
    },
    updateSup() {
      this.initialData = this.selectedSup
      this.dialogSup = true
    },
    delSup() {
      this.supplyPriceModifyList = this.supplyPriceModifyList.filter(
        (s) => !(s.vid === this.selectedSup.vid),
      )
    },
    success(newSup) {
      if (
        this.supplyPriceModifyList.some(
          (s) => s.supplierId === newSup.supplierId,
        )
      ) {
        this.$dialog.message.error('供应商重复')
        return
      }
      this.supplyPriceModifyList.push(newSup)
    },
    async saveExcelDetails() {
      if (this.file == null) {
        this.$dialog.message.error('请上传附件！')
        return
      }
      let formData = new FormData()
      formData.append('file', this.file)
      // formData.append('materialApplyModifyDTO', JSON.stringify(this.detail))
      formData.append(
        'materialApplyModifyDTO',
        new Blob([JSON.stringify(this.detail)], { type: 'application/json' }),
      )
      this.saveIng = true
      const { data } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/saveExcelDetails',
        formData,
      )
      this.saveIng = false
      if (data) {
        this.$dialog.message.success('保存成功')
        this.file = null
        this.loadDetail()
      }
      // 导入后重新加载数据
    },
    async checkBulkSupplier() {
      if (this.isShip) {
        return
      }
      if (this.detail.status == 3) {
        return
      }
      if (this.$local.data.get('userInfo').userType == 2) {
        //船员不提示大宗供应商
        return
      }
      if (!this.components[0].costSubjectId) {
        // this.$dialog.message.error('请选择费用科目')
        return
      }
      //             // 158199185722opqst25	辅助备件-锚
      // 1581991857290899459	辅助备件-锚链
      if (
        this.components[0].costSubjectId != '158199185722opqst25' && //辅助备件-锚
        this.components[0].costSubjectId != '1581991857290899459' //辅助备件-锚链
      ) {
        return
      }
      if (!this.detail.shipCode) {
        // this.$dialog.message.error('请选择船舶')
        return
      }
      if (!this.detail.arriveDate) {
        // this.$dialog.message.error('请选择到港时间')
        return
      }
      const { data, errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchasePrice/bulkSupplierByShipCodeAndSubject',
        {
          shipCode: this.detail.shipCode,
          costSubjectId: this.components[0].costSubjectId,
          arriveDate: this.detail.arriveDate,
        },
      )
      if (errorRaw) {
        // this.$dialog.message.error(errorRaw.msg)
        return
      }
      if (data) {
        if (this.detail.supplyPriceModifyList.length === 0) {
          data.operationType = 1
          this.supplyPriceModifyList.push(data)
        } else {
          if (
            !this.detail.supplyPriceModifyList.some(
              (item) => item.supplierId === data.supplierId,
            )
          ) {
            this.supplyPriceModifyList = []
            data.operationType = 1
            this.supplyPriceModifyList.push(data)
          }
        }
      }
    },
    async dowExcel() {
      // (船名+时间戳+备件申请明细)
      if (this.detail.fileName == undefined) {
        const timestamp = Date.now() // 返回当前时间戳（毫秒）
        this.detail.fileName =
          this.detail.shipCode + timestamp + '备件申请明细' + '.xlsx'
      }
      if (this.components.length === 0) {
        this.$dialog.message.warning('请添加备件')
        return false
      }
      const detailList = this.getCompWithOperation()
      this.loading = true
      const { errorRaw } = await this.blobDownload(
        '/business/shipAffairs/purchaseManage/componentApplyExcel',
        {
          ...this.detail,
          detailList,
        },
        // this.detail.fileName + '.xlsx',
        this.detail.fileName,
      )
      this.loading = false
      if (errorRaw) this.$dialog.message.error(errorRaw)
      this.loadDetail2(this.detail.id)
      this.detail.hasDownload = 'YES'
      // this.$router.push({
      //   name: 'spare-apply-detail',
      //   params: {
      //     id: this.detail.id + 'load' + Date.now(),
      //   },
      // })
    },
    async importExcel() {
      // 增加文件名称校验，判断是否为系统导出文件
      let formData = new FormData()
      formData.append('file', this.file)
      const detailList = this.getCompWithOperation()
      let componentApplyModifyDTO = {
        ...this.detail,
        detailList: detailList,
      }
      formData.append(
        'componentApplyModifyDTO',
        new Blob([JSON.stringify(componentApplyModifyDTO)], {
          type: 'application/json',
        }),
      )
      const { data, errorRaw } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/componentApplyImportExcel',
        formData,
      )
      if (errorRaw) {
        // this.detail.checkFlag = false
        this.$dialog.message.error(errorRaw.msg)
        return
      }
      if (data) {
        if (data == 99) {
          this.$dialog.message.success('备件基础信息校验通过，可提交审批')
          this.detail.checkFlag = true
          // this.$router.push({
          //   name: 'spare-apply-detail',
          //   params: {
          //     id: this.detail.id + 'load' + Date.now(),
          //   },
          // })
          this.loadDetail2(this.detail.id)
        } else if (data == 1) {
          // 不是当前申请单下载的附件
          // this.detail.checkFlag = false
          this.$dialog.message.error(
            '上传的附件与当前申请单不一致，备件基础信息校验失败！',
          )
          return
        } else if (data == 2) {
          this.detail.checkFlag = false
          this.$dialog.message.error(
            '附件中的备件基础信息与系统不一致，备件基础信息校验失败！',
          )
          return
        }
      }
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
