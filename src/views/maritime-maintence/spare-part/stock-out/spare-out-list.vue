<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="isShip ? headersShip : headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      :push-params="pushParams"
      use-ship
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="出库单号"
            outlined
            dense
            clearable
            v-model="searchObj.inoutCode"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          :to="{
            name: 'spare-out-detail',
            params: { id: 'new', stockOutType: 7 },
          }"
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['备件消耗:新增自由出库']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增自由出库
        </v-btn>
        <v-btn
          :disabled="selected.status !== '1' && selected.status !== '4'"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['备件消耗:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.inoutMode`]="{ item }">
        {{
          [
            '订单入库',
            '调减入库',
            '期初入库',
            '维护保养消耗',
            '自修消耗',
            '航修消耗',
            '坞修消耗',
            '自由出库',
          ][item.inoutMode]
        }}
      </template>
      <template v-slot:[`item.usd`]>美元</template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'spare-out-list',
  created() {
    this.tableName = '备件消耗'
    this.reqUrl = '/business/shipAffairs/purchaseManage/purchaseStockInOutPage'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '出库日期', value: 'inoutDate' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '币种', value: 'usd' },
      { text: '涉及金额', value: 'amountInvolved' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '出库单号', value: 'inoutCode' },
      { text: '出库日期', value: 'inoutDate' },
      { text: '出库人', value: 'handler' },
      { text: '出库方式', value: 'inoutMode' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '备注', value: 'remark' },
    ]
    // this.searchObj = { inoutNature: 0, inoutType: 1, isMe: true }
    this.pushParams = { name: 'spare-out-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: { inoutNature: 0, inoutType: 1, isMe: true },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/stocksInOutDelete',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
  },

  mounted() {},
}
</script>

<style></style>
