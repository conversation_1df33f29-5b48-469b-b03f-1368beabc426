<template>
  <v-container fluid>
    <v-detail-view
      v-permission="['备件消耗:编辑']"
      :title="`备件出库-${detail.inoutCode || '新增'}`"
      :tooltip="detail.inoutCode || '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="canSubmit"
      @save="save"
      @submit="submit"
      :can-save="canEdit"
    >
      <template
        v-if="detail.auditParams && detail.auditParams.processInstanceId"
        v-slot:topcontent
      >
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit
              ref="audit"
              :auditParams="detail.auditParams"
              :shipCode="detail.shipCode"
            ></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-container fluid>
          <!-- <v-form :readonly="isEdit" ref="form"> -->
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  :readonly="!!detail.shipCode"
                  v-model="detail.shipCode"
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <!-- TODO:
              根据用户信息读取用户所在部门
               -->
              <v-col cols="12" md="3">
                <v-ship-dept
                  outlined
                  dense
                  label="申请部门"
                  :disabled="!detail.shipCode"
                  :readonly="isEdit"
                  v-model="detail.deptName"
                  :rules="[rules.required]"
                  :items="['甲板部', '轮机部']"
                ></v-ship-dept>
              </v-col>
              <v-col cols="12" md="3" v-if="!isShip">
                <!-- 物品金额（美元）:{{
                  detail.status == '3'
                    ? detail.amountInvolved
                    : '审批未通过不展示'
                }} -->
                <v-text-field
                  outlined
                  dense
                  :readonly="isEdit"
                  label="物品金额（美元）"
                  :value="
                    detail.status == '3'
                      ? detail.amountInvolved
                      : '审批未通过不展示'
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-handler
                  v-model="detail.handler"
                  use-current
                  :init-user="initUser"
                  :rules="[rules.required]"
                  disabled
                ></v-handler>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  outlined
                  dense
                  label="出库类型"
                  disabled
                  v-model="detail.stockOutType"
                  :rules="[rules.required]"
                  :items="出库类型"
                ></v-select>
              </v-col>
              <!-- 分设备出库 -->
              <v-col cols="12" md="3">
                <engine-select
                  :readonly="!canEdit"
                  :applyType="6"
                  :disabled="!detail.shipCode"
                  v-model="detail.equipmentId"
                  :shipCode="detail.shipCode"
                  :initSelected="initEngine"
                  :rules="[rules.required]"
                  @change="clearComponents"
                ></engine-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  :readonly="!canEdit"
                  outlined
                  dense
                  label="备注"
                  v-model="detail.remark"
                  :rules="[detail.stockOutType == 7 ? rules.required : true]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template v-if="canEdit" #出库明细按钮>
        <v-btn
          :disabled="!detail.equipmentId"
          small
          outlined
          tile
          color="success"
          class="mx-1"
          @click.stop="dialog = true"
          v-permission="['出库明细:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          选择
        </v-btn>
        <v-btn
          :disabled="!selected"
          small
          outlined
          tile
          color="error"
          class="mx-1"
          @click="del"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #出库明细>
        <v-form ref="form2">
          <v-table-list
            v-model="selected"
            :headers="detailHeaders"
            :items="items"
          >
            <template
              v-if="canEdit"
              v-slot:[`item.stocksInOutNumber`]="{ item }"
            >
              <v-text-field
                label="出库数量"
                v-model="item.stocksInOutNumber"
                single-line
                dense
                :rules="[rules.int, item.max]"
              ></v-text-field>
            </template>
          </v-table-list>
        </v-form>
        <v-card-text>
          <v-attach-list
            :attachments="detail.attachmentRecords"
            @change="(ids) => (detail.attachmentIds = ids)"
            :ship-code="detail.shipCode"
          ></v-attach-list>
        </v-card-text>
      </template>
    </v-detail-view>
    <spare-stock-select
      v-model="dialog"
      :components.sync="items"
      :shipCode="detail.shipCode"
      :equipmentInformationId="detail.equipmentId"
    ></spare-stock-select>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import spareStockSelect from './private/spare-stock-select.vue'
import engineSelect from '../apply/private/engine-select.vue'
export default {
  components: { spareStockSelect, engineSelect },
  name: 'spare-out-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'spare-out-list'
    this.subtitles = ['基本信息', '出库明细']
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    // TODO:其他出库方式
    this.businessTypes = [
      '',
      '',
      '',
      'monthplan-detail',
      'self-repair-detail',
      'voyage-complete-detail',
      'dock-complete-detail',
    ]
    this.出库类型 = [
      { text: '维护保养消耗', value: 3 },
      { text: '自修消耗', value: 4 },
      { text: '航修消耗', value: 5 },
      { text: '坞修消耗', value: 6 },
      { text: '自由出库', value: 7 },
    ]
    this.detailHeaders = [
      { text: '设备主体', value: 'equipmentName' },
      { text: '备件名', value: 'itemName' },
      { text: '备件号', value: 'itemNumber' },
      { text: '备件SAPCODE', value: 'code' },
      { text: '图纸号', value: 'drawingSerialNumber' },
      { text: '所在库位', value: 'depositoryName' },
      { text: '库存数量', value: 'inventoryNumber' },
      { text: '出库数量', value: 'stocksInOutNumber', width: 200 },
      { text: '单位', value: 'unit' },
    ]
    this.outParam = this.$store.state.outParams.outParams.find(
      (item) => item.itemType === 'spare-out-detail',
    )
    this.detail.stockOutType = this.outParam
      ? this.businessTypes.indexOf(this.outParam.businessType)
      : 7
    if (this.$route.params.stockOutType == 7) {
      this.detail.stockOutType = 7
    }
    Object.freeze(this.outParam)
    // this.$store.state.outParams.outParams = []
  },
  data() {
    return {
      detail: {
        detailList: [],
        inoutNature: 0,
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        int: (v) => /^\d+?$/.test(v) || '请输入整数数字',
      },
      dialog: false,
      items: [],
      initUser: false,
      selected: false,
      initEngine: {},
    }
  },
  watch: {
    'detail.shipCode'(_, oldVal) {
      if (!oldVal) return
      this.clearEngine()
    },
    'detail.equipmentId'(_, oldVal) {
      if (!oldVal) return
      this.clearComponents()
    },
  },
  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canEdit() {
      return this.detail.status != 2 && this.detail.status != 3
    },
    canSubmit() {
      return this.detail.status != 3
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) return false
      if (!this.$refs.form2.validate()) return false
      const newItems = this.items
        .filter((i) => !this.detail.detailList.find((j) => i.id === j.id))
        .map((i) => ({
          ...i,
          outNumber: i.stocksInOutNumber,
          operationType: 1,
        }))
      const remainItems = this.items
        .filter((i) => this.detail.detailList.find((j) => i.id === j.id))
        .map((i) => ({
          ...i,
          outNumber: i.stocksInOutNumber,
          operationType: 2,
        }))
      const delItems = this.detail.detailList
        .filter((i) => !this.items.find((j) => i.id === j.id))
        .map((i) => ({ ...i, operationType: 3 }))
      const { errorRaw, data } = await this.postAsync(
        '/business/shipAffairs/purchaseManage/saveOrUpdatePurchaseStockOutOfComponent',
        {
          ...this.detail,
          inoutNature: 0,
          stockOutType: this.detail.stockOutType ?? this.detail.inoutMode,
          stockOutDetailModifyList: [...remainItems, ...newItems, ...delItems],
        },
      )
      if (notMove) return data
      if (!errorRaw) {
        if (this.outParam) {
          // console.log(this.outParam)
          this.$store.commit('setOutId', {
            ...this.outParam,
            outId: data,
          })
          this.closeAndTo(this.outParam.businessType, {
            id: this.outParam.businessId,
          })
        } else {
          goBack()
        }
      }
    },

    async submit(goBack) {
      if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/purchaseManage/submitPurchaseStockOutOfComponent',
          { id: data },
        )
        if (errorRaw) return
      } else {
        const error = await this.$refs.audit.submit()
        if (error) return
        if (!error) {
          const { data } = await this.getAsync(
            '/business/shipAffairs/purchaseManage/purchaseStockInoutById2',
            { id: this.$route.params.id },
          )
          console.log(data)
        }
      }
      if (this.outParam) {
        this.$store.commit('setOutId', {
          ...this.outParam,
          outId: data,
        })
        this.closeAndTo(this.outParam.businessType, {
          id: this.outParam.businessId,
        })
      } else {
        goBack()
      }
    },

    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseStockInoutById',
        { id: this.$route.params.id },
      )
      this.detail = data
      this.detail.stockOutType = data.inoutMode
      this.initUser = { id: data.handler, nickName: data.handlerName }
      this.items = data.detailList.map((i) => ({
        ...i,
        max: (a) => i.inventoryNumber >= a || '出库数量不得超过最大库存量',
      }))
      this.detail.shipCode = data.shipInfo.shipCode
      this.initEngine = {
        equipmentEname: data.equipmentEname,
        id: data.equipmentId,
      }
    },
    del() {
      this.items = this.items.filter((i) => i.id !== this.selected.id)
    },
    async clearEngine() {
      this.$dialog.message.info('由于船舶变更,自动清空设备')
      this.detail.equipmentId = ''
    },
    clearComponents() {
      this.$dialog.message.info('由于设备变更,自动清空出库明细')
      this.items = []
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
