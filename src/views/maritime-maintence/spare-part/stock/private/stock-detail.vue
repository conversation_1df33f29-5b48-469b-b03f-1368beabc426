<template>
  <v-sheet class="my-3">
    <v-card-subtitle class="text-h6 py-1">库存明细</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="isShip ? 明细表头船端 : 明细表头"
      :items="stocksDetailList"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
    <v-card-subtitle class="text-h6 py-1">入库历史</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="isShip ? 入库表头船端 : 入库表头"
      :items="stocksInHistoryList"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
    <v-card-subtitle class="text-h6 py-1">消耗历史</v-card-subtitle>
    <v-divider></v-divider>
    <v-data-table
      dense
      :headers="isShip ? 出库表头船端 : 出库表头"
      :items="stocksOutHistoryList"
      hide-default-footer
      disable-pagination
    ></v-data-table>
    <v-divider></v-divider>
  </v-sheet>
</template>
<script>
// itemBatch	批次	string
// itemNumber	库存数量	integer
// orderNo	订单号	string
// price	单价	number
// totalPrice	总金额	number
// unit	单位
export default {
  name: 'stock-detail',
  created() {
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.明细表头 = [
      { text: '订单号', value: 'orderNo' },
      { text: '批次', value: 'itemBatch' },
      { text: '库存数量', value: 'itemNumber' },
      { text: '单价', value: 'price' },
      { text: '总金额', value: 'totalPrice' },
    ]
    this.入库表头 = [
      { text: '入库单号', value: 'stocksInOutCode' },
      { text: '批次', value: 'operateBatch' },
      { text: '操作人', value: 'handler' },
      { text: '单价', value: 'price' },
      { text: '更新数量', value: 'updateNumber' },
      { text: '总价', value: 'totalPrice' },
      { text: '更新时间', value: 'updateTime' },
    ]
    this.出库表头 = [
      { text: '出库单号', value: 'stocksInOutCode' },
      { text: '批次', value: 'operateBatch' },
      { text: '操作人', value: 'handler' },
      { text: '单价', value: 'price' },
      { text: '更新数量', value: 'updateNumber' },
      { text: '总价', value: 'totalPrice' },
      { text: '更新时间', value: 'updateTime' },
    ]
    this.明细表头船端 = [
      { text: '订单号', value: 'orderNo' },
      { text: '批次', value: 'itemBatch' },
      { text: '库存数量', value: 'itemNumber' },
    ]
    this.入库表头船端 = [
      { text: '入库单号', value: 'stocksInOutCode' },
      { text: '批次', value: 'operateBatch' },
      { text: '操作人', value: 'handler' },
      { text: '更新数量', value: 'updateNumber' },
      { text: '更新时间', value: 'updateTime' },
    ]
    this.出库表头船端 = [
      { text: '出库单号', value: 'stocksInOutCode' },
      { text: '批次', value: 'operateBatch' },
      { text: '操作人', value: 'handler' },
      { text: '更新数量', value: 'updateNumber' },
      { text: '更新时间', value: 'updateTime' },
    ]
  },
  props: {
    itemId: String,
    depositoryId: String,
  },
  data() {
    return {
      stocksDetailList: [],
      stocksInHistoryList: [],
      stocksOutHistoryList: [],
    }
  },

  methods: {
    async loadDetail() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/purchaseManage/purchaseStocksDetailMsgByItemId',
        {
          itemId: this.itemId,
          depositoryId: this.depositoryId,
        },
      )
      this.stocksDetailList = data.stocksDetailList
      this.stocksInHistoryList = data.stocksInHistoryList
      this.stocksOutHistoryList = data.stocksOutHistoryList
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
