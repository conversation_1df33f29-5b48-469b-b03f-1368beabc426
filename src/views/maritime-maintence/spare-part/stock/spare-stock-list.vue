<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :headers="isShip ? headersShip : headers"
      :specialHeaders="specialHeaders"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
      show-expand
      :show-select="false"
      :showExportButton="true"
    >
      <template v-slot:[`item.isDockRepair`]="{ item }">
        {{ item.isDockRepair ? '是' : '否' }}
      </template>
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-ship-select v-model="searchObj.shipCode"></v-ship-select>
        </v-col>
        <v-col cols="12" md="3">
          <v-autocomplete
            v-model="searchObj.equipmentInformationId"
            label="设备主体"
            :disabled="!searchObj.shipCode"
            outlined
            dense
            :items="firstEquipments"
            clearable
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6" md="3" v-if="searchObj.equipmentInformationId">
          <v-autocomplete
            v-model="searchObj.equipmentId"
            label="子设备"
            outlined
            dense
            :items="subEquipments"
            :loading="loading"
            clearable
          ></v-autocomplete>
        </v-col>
        <v-col cols="12" sm="6" md="3" v-if="searchObj.equipmentInformationId">
          <v-text-field
            v-model="searchObj.drawingSerialNumberLike"
            label="图纸号"
            outlined
            dense
            :loading="loading"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3" v-if="searchObj.equipmentInformationId">
          <v-text-field
            v-model="searchObj.equipmentCodeLike"
            label="备件号"
            outlined
            dense
            :loading="loading"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3" v-if="searchObj.equipmentInformationId">
          <v-text-field
            v-model="searchObj.equipmentNameLike"
            label="备件名"
            outlined
            dense
            :loading="loading"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            dense
            outlined
            label="备件仓库"
            v-model="searchObj.depositoryIdSearch"
            :items="depository"
            :disabled="!searchObj.shipCode"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            dense
            outlined
            label="备件属性"
            v-model="searchObj.stockType"
            :items="stockType"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="3">
          <v-switch
            class="mt-1"
            v-model="searchObj.all"
            :label="searchObj.all ? '显示全部' : '显示非0'"
            color="primary"
          ></v-switch>
        </v-col>
      </template>
      <template #btns></template>
      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length">
          <stock-detail
            :depositoryId="item.depositoryId"
            :itemId="item.itemId"
          ></stock-detail>
        </td>
      </template>
      <template v-slot:[`item.componentProperty`]="{ item }">
        {{
          ['普通备件', 'SAP备件', '固定资产', '通导固定资产'][
            item.componentProperty
          ]
        }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import stockDetail from './private/stock-detail.vue'
export default {
  components: { stockDetail },
  name: 'spare-stock-list',
  created() {
    this.tableName = '备件库存'
    this.reqUrl =
      '/business/shipAffairs/purchaseManage/purchaseStocksMsgByParams'
    this.isShip = this.$local.data.get('userInfo').isShipSyS
    this.headers = [
      { text: '船舶', value: 'chShipName' },
      { text: '备件名', value: 'itemName' },
      { text: '备件号', value: 'itemNo' },
      { text: '图纸号', value: 'drawingSerialNumber' },
      { text: '设备主体', value: 'equipmentName' },
      { text: 'SAP备件编码', value: 'sapNumber', hideDefault: true },
      { text: '备件属性', value: 'componentProperty' },
      { text: '仓库', value: 'depositoryName' },
      // { text: '仓库位置', value: 'depositoryAddress' },
      { text: '坞修', value: 'isDockRepair' },
      { text: '总金额', value: 'totalPrice' },
      { text: '库存数量', value: 'itemNumber' },
      { text: '单位', value: 'unit' },
      { text: '最低库存量', value: 'minimumInventory' },
      { text: '最高库存量', value: 'maximumInventory' },
    ]
    this.headersShip = [
      { text: '船舶', value: 'shipInfo' },
      { text: '备件名', value: 'itemName' },
      { text: '备件号', value: 'itemNo' },
      { text: '设备主体', value: 'equipmentName' },
      { text: 'SAP备件编码', value: 'sapNumber', hideDefault: true },
      { text: '备件属性', value: 'componentProperty' },
      { text: '仓库', value: 'depositoryName' },
      // { text: '仓库位置', value: 'depositoryAddress' },
      { text: '坞修', value: 'isDockRepair' },
      { text: '库存数量', value: 'itemNumber' },
      { text: '单位', value: 'unit' },
      { text: '最低库存量', value: 'minimumInventory' },
      { text: '最高库存量', value: 'maximumInventory' },
    ]
    this.specialHeaders = [
      {
        text: 'componentProperty',
        value: [
          { text: '0', value: '普通备件' },
          { text: '1', value: 'SAP备件' },
          { text: '2', value: '固定资产' },
          { text: '3', value: '通导固定资产' },
        ],
      },
      {
        text: 'isDockRepair',
        value: [
          { text: true, value: '是' },
          { text: false, value: '否' },
        ],
      },
    ]
  },

  watch: {
    'searchObj.shipCode'(val) {
      if (val) this.loadFirstEquipment(val)
      if (val) this.loadDepository(val)
    },
    'searchObj.equipmentInformationId'(val) {
      if (val) {
        // this.searchObj.equipmentId = val
        this.searchObj.equipmentInformationId = val
        this.loadSubEqu()
      }
    },
  },

  data() {
    return {
      selected: false,
      searchObj: { stocksType: '0' },
      firstEquipments: [],
      subEquipments: [],
      depository: [],
      stockType: [
        { value: '0', text: '普通备件' },
        { value: '1', text: 'SAP备件' },
        { value: '2', text: '固定资产' },
        { value: '3', text: '通导固定资产' },
        { value: '4', text: '其他' },
      ],
    }
  },

  methods: {
    async loadDepository(shipCode) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/Depository/list',
        {
          size: 99,
          current: 1,
          shipCode: shipCode,
          type: 0,
        },
      )
      const { records } = data
      this.depository = records.map((i) => ({ text: i.name, value: i.id }))
    },
    async loadFirstEquipment(shipCode) {
      const { data } = await this.getAsync(
        '/business/shipAffairs/equipmentInformation/firstPage',
        { current: 1, size: 999, shipCode },
      )
      const { records } = data
      this.firstEquipments = records?.map((i) => {
        return {
          text: i.equipmentEname,
          value: i.id,
        }
      })
    },
    // 子设备获取
    async loadSubEqu() {
      this.loading = true
      const reqUrl =
        '/business/shipAffairs/equipmentInformation/getEquipmentThirdByMainEquipmentId'
      const { data } = await this.getAsync(reqUrl, {
        MainEquipmentId: this.searchObj?.equipmentInformationId,
      })
      this.subEquipments = data?.map((i) => {
        return {
          text: i.subEquipmentCname,
          value: i.id,
        }
      })
      this.loading = false
    },
  },

  mounted() {},
}
</script>

<style></style>
