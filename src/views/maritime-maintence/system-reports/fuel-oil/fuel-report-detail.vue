<template>
  <v-container fluid>
    <v-detail-view
      :title="`燃油管理报表-${isEdit ? detail.ismFuelOilNo : '新增'}`"
      :tooltip="isEdit ? detail.ismFuelOilNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="canSubmit"
      @save="save"
      @submit="submit"
      v-permission="['燃油管理:编辑']"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-form ref="aform">
          <v-card-text class="mt-2 pb-0">
            <v-audit ref="audit" :auditParams="auditParams"></v-audit>
          </v-card-text>
        </v-form>
      </template>
      <template #基本数据>
        <v-form
          :readonly="detail.status == '2' || detail.status == '3'"
          ref="form"
        >
          <v-container fluid>
            <v-row>
              <v-col class="py-0" cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <vs-date-picker
                  :disabled="!isInit"
                  v-model="detail.fromTime"
                  label="记录开始时间"
                  outlined
                  dense
                  :rules="isInit ? [rules.required] : []"
                ></vs-date-picker>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <vs-date-picker
                  v-model="detail.toTime"
                  label="记录结束时间"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <vs-date-picker
                  v-model="detail.formDate"
                  label="报表时间"
                  outlined
                  dense
                  :rules="[rules.required]"
                  use-today
                ></vs-date-picker>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  type="number"
                  v-model="detail.runningHoursOfMe"
                  label="主机运行时间"
                  dense
                  outlined
                  :rules="[rules.number2]"
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  type="number"
                  v-model="detail.maneuveringHours"
                  label="副机运行时间"
                  dense
                  outlined
                  :rules="[rules.number2]"
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  type="number"
                  v-model="detail.boilerHours"
                  label="锅炉运行时间"
                  dense
                  outlined
                  :rules="[rules.number2]"
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  :disabled="!isInit"
                  type="number"
                  v-model="detail.lastDieselOilStoredQuan"
                  label="上月末结存轻油数量"
                  dense
                  outlined
                  :rules="[rules.number2]"
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  :disabled="!isInit"
                  type="number"
                  v-model="detail.lastFuelOilStoredQuan"
                  label="上月末结存重油数量"
                  dense
                  outlined
                  :rules="[rules.number2]"
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  disabled
                  type="number"
                  v-model="detail.dieselOilStoredQuan"
                  label="轻油本月末结存数量"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  disabled
                  type="number"
                  v-model="detail.fuelOilStoredQuan"
                  label="重油本月末结存数量"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="12">
                <v-text-field
                  v-model="detail.remark"
                  label="备注"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template v-if="!isEdit && !isInit" #燃油记录按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="saveAndMove"
          v-permission="['燃油管理:编辑']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          保存并填写详情
        </v-btn>
      </template>
      <template v-else #燃油记录按钮>
        <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createItem"
          v-permission="['燃油管理:新增加装记录']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增加装记录
        </v-btn>
        <v-btn
          :disabled="!fuel"
          outlined
          small
          tile
          color="warning"
          class="mx-1"
          @click="editItem"
          v-permission="['燃油管理:修改加装记录']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!fuel"
          outlined
          small
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['燃油管理:删除加装记录']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #加装记录>
        <v-table-list
          :show-select="false"
          :headers="headers"
          :items="加装记录"
          item-key="fixNo"
        >
          <template
            v-for="(h, i) in headers"
            v-slot:[`item.${h.value}`]="{ item }"
          >
            <v-text-field
              type="number"
              :key="h.value + i"
              v-if="canSubmit && h.type === 'number'"
              v-model="item[h.value]"
              dense
              single-line
            ></v-text-field>
            <v-text-field
              type="number"
              :key="h.value + i"
              v-else-if="canSubmit && h.type === 'number2'"
              v-model="item[h.value]"
              :rules="[rules.number2]"
              dense
              single-line
            ></v-text-field>
            <v-text-field
              :key="h.value + item.fixNo"
              v-else-if="canSubmit && h.type === 'string'"
              v-model="item[h.value]"
              dense
            ></v-text-field>
            <vs-date-picker
              :key="h.value + item.fixNo"
              v-else-if="canSubmit && h.type === 'date'"
              v-model="item[h.value]"
              dense
            ></vs-date-picker>
            <span v-else :key="h.value + item.fixNo">
              {{ item[h.value] }}
            </span>
          </template>
        </v-table-list>
      </template>
      <template #消耗记录>
        <v-table-list
          :show-select="false"
          :headers="消耗表头"
          :items="消耗记录"
          item-key="fixNo"
        >
          <template v-slot:[`item.dieselOilQuantity`]="{ item }">
            <v-text-field
              v-if="canSubmit"
              type="number"
              v-model="item.dieselOilQuantity"
              dense
              :rules="[rules.number2]"
            ></v-text-field>
            <span v-else>{{ item.dieselOilQuantity }}</span>
          </template>
          <template v-slot:[`item.fuelOilQuantity`]="{ item }">
            <v-text-field
              v-if="canSubmit"
              type="number"
              v-model="item.fuelOilQuantity"
              dense
              :rules="[rules.number2]"
            ></v-text-field>
            <span v-else>{{ item.fuelOilQuantity }}</span>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'fuel-report-detail',
  mixins: [routerControl],
  created() {
    this.backRouteName = 'fuel-report-list'
    this.subtitles = ['基本数据', '加装记录', '消耗记录']
    this.headers = [
      { text: '加装时间', value: 'date', type: 'date', width: 180 },
      { text: '加装港口', value: 'detailName', type: 'string' },
      { text: '轻油比重KG/L', value: 'dieselOilDensity', type: 'number' },
      { text: '轻油数量T', value: 'dieselOilQuantity', type: 'number2' },
      { text: '轻油规格CST', value: 'dieselOilSpecification', type: 'string' },
      { text: '轻油含硫量%', value: 'dieselOilSulfurConten', type: 'number' },
      { text: '重油比重KG/L', value: 'fuelOilDensity', type: 'number' },
      { text: '重油数量T', value: 'fuelOilQuantity', type: 'number2' },
      { text: '重油规格CST', value: 'fuelOilSpecification', type: 'string' },
      { text: '重油含硫量%', value: 'fuelOilSulfurConten', type: 'number' },
    ]
    this.消耗表头 = [
      { text: '类型', value: 'detailName' },
      { text: '轻油数量T', value: 'dieselOilQuantity' },
      { text: '重油数量T', value: 'fuelOilQuantity' },
    ]
  },
  data() {
    return {
      detail: {
        detailList: [
          {
            date: null,
            detailName: '主机Main engine',
            dieselOilDensity: 0,
            dieselOilQuantity: 0,
            dieselOilSpecification: null,
            dieselOilSulfurConten: 0,
            fixNo: 1,
            fuelOilDensity: 0,
            fuelOilQuantity: 0,
            fuelOilSpecification: null,
            fuelOilSulfurConten: 0,
            id: null,
            inoutType: 1,
            mainId: null,
          },
          {
            date: null,
            detailName: '副机Auxiliary engine',
            dieselOilDensity: 0,
            dieselOilQuantity: 0,
            dieselOilSpecification: null,
            dieselOilSulfurConten: 0,
            fixNo: 2,
            fuelOilDensity: 0,
            fuelOilQuantity: 0,
            fuelOilSpecification: null,
            fuelOilSulfurConten: 0,
            id: null,
            inoutType: 1,
            mainId: null,
          },
          {
            date: null,
            detailName: '锅炉Boiler',
            dieselOilDensity: 0,
            dieselOilQuantity: 0,
            dieselOilSpecification: null,
            dieselOilSulfurConten: 0,
            fixNo: 3,
            fuelOilDensity: 0,
            fuelOilQuantity: 0,
            fuelOilSpecification: null,
            fuelOilSulfurConten: 0,
            id: null,
            inoutType: 1,
            mainId: null,
          },
          {
            date: null,
            detailName: '',
            dieselOilDensity: 0,
            dieselOilQuantity: 0,
            dieselOilSpecification: null,
            dieselOilSulfurConten: 0,
            fixNo: 4,
            fuelOilDensity: 0,
            fuelOilQuantity: 0,
            fuelOilSpecification: null,
            fuelOilSulfurConten: 0,
            id: null,
            inoutType: 0,
            mainId: null,
          },
          {
            date: null,
            detailName: '',
            dieselOilDensity: 0,
            dieselOilQuantity: 0,
            dieselOilSpecification: null,
            dieselOilSulfurConten: 0,
            fixNo: 5,
            fuelOilDensity: 0,
            fuelOilQuantity: 0,
            fuelOilSpecification: null,
            fuelOilSulfurConten: 0,
            id: null,
            inoutType: 0,
            mainId: null,
          },
        ],
        lastDieselOilStoredQuan: 0,
        lastFuelOilStoredQuan: 0,
        dieselOilStoredQuan: 0,
        fuelOilStoredQuan: 0,
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        number2: (v) =>
          /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(v) ||
          '填写格式错误，允许填写数字两位小数',
      },
      records: 1,
      fuel: {},
      initData: {},
      dialog: false,
      delList: [],
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new'
    },
    canSubmit() {
      return !this.isEdit || this.detail.status === '0'
    },
    isInit() {
      return this.records === 0
    },
    加装记录() {
      return this.detail.detailList.filter((item) => item.inoutType == 0)
    },
    消耗记录() {
      return this.detail.detailList.filter((item) => item.inoutType == 1)
    },
    重油变动() {
      // 重油变动 = 重油加装 - 重油消耗
      return (
        this.加装记录.reduce((acc, cur) => acc + cur.fuelOilQuantity * 1, 0) -
        this.消耗记录.reduce((acc, cur) => acc + cur.fuelOilQuantity * 1, 0)
      )
    },
    轻油变动() {
      // 轻油变动 = 轻油加装 - 轻油消耗
      return (
        this.加装记录.reduce((acc, cur) => acc + cur.dieselOilQuantity * 1, 0) -
        this.消耗记录.reduce((acc, cur) => acc + cur.dieselOilQuantity * 1, 0)
      )
    },
  },

  watch: {
    'detail.shipCode'(val) {
      if (val && !this.isEdit) this.loadRecords(val)
    },
    重油变动(val) {
      this.detail.fuelOilStoredQuan =
        val + this.detail.lastFuelOilStoredQuan * 1
    },
    轻油变动(val) {
      this.detail.dieselOilStoredQuan =
        val + this.detail.lastDieselOilStoredQuan * 1
    },
    'detail.lastFuelOilStoredQuan'(val) {
      this.detail.fuelOilStoredQuan = val * 1 + this.重油变动
    },
    'detail.lastDieselOilStoredQuan'(val) {
      this.detail.dieselOilStoredQuan = val * 1 + this.轻油变动
    },
  },

  methods: {
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/shipAffairs/ism/saveOrUpdateFuelOil'
      const detailList = [
        ...this.加装记录.map((i) => ({
          ...i,
          operationType: this.isEdit ? 2 : 1,
        })),
        ...this.消耗记录.map((i) => ({
          ...i,
          operationType: this.isEdit ? 2 : 1,
        })),
      ]
      const { errorRaw, data } = await this.postAsync(url, {
        ...this.detail,
        detailList,
      })
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      // if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) return
      if (!this.detail.auditParams) {
        const { errorRaw } = await this.getAsync(
          '/business/shipAffairs/ism/submitFuelOilById',
          { id: data },
        )
        if (!errorRaw) goBack()
      } else {
        const error = await this.$refs.audit.submit()
        if (!error) goBack()
      }
    },
    async saveAndMove() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/shipAffairs/ism/saveOrUpdateFuelOil'
      const detailList = [...this.delList, ...this.detail.detailList]
      const { data } = await this.postAsync(url, {
        ...this.detail,
        detailList,
        isInitiate: this.isInit,
      })
      if (!data) return
      this.closeAndTo('fuel-report-detail', { id: data })
    },

    async loadRecords(shipCode) {
      const { errorRaw, data } = await this.getAsync(
        '/business/shipAffairs/ism/getPreOfFuelOil',
        { shipCode },
      )
      if (errorRaw) return
      this.detail.fromTime = data.fromTime
      this.detail.lastDieselOilStoredQuan = data.lastDieselOilStoredQuan
      this.detail.lastFuelOilStoredQuan = data.lastFuelOilStoredQuan
      this.detail.chiefEngineerId = data.chiefEngineerId
      this.detail.lastFormId = data.lastFormId
      // this.detail.detailList = data.detailList
      if (!data.fromTime) {
        this.$dialog.message.warning(
          '该船舶没有上报过燃油消耗记录,当前为初始化',
        )
        this.records = 0
      }
    },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/ism/getDetailOfFuelOilById',
        {
          id: this.$route.params.id,
        },
      )
      this.detail = data
      this.detail.shipCode = data.shipInfo.shipCode
    },

    createItem() {
      this.initData = {}
      this.dialog = true
    },
    editItem() {
      this.initData = this.fuel
      this.dialog = true
    },
    delItem() {
      if (this.fuel.id) this.delList.push({ ...this.fuel, operationType: 3 })
      this.detail.detailList = this.detail.detailList.filter(
        (i) => i.vid !== this.fuel.vid,
      )
      this.fuel = false
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
