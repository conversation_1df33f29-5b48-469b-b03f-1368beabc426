<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        燃油加装/消耗明细
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col
                class="py-1"
                v-for="h in headers"
                :key="h.value"
                cols="12"
                md="3"
              >
                <v-text-field
                  outlined
                  dense
                  v-model="formData[h.value]"
                  :label="h.text"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'fuel-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.headers = [
      { text: '何时何地加油或消耗', value: 'detailName', type: 'string' },
      { text: '轻油比重', value: 'dieselOilDensity', type: 'number' },
      { text: '轻油数量', value: 'dieselOilQuantity', type: 'number' },
      { text: '轻油规格', value: 'dieselOilSpecification', type: 'string' },
      { text: '轻油含硫量', value: 'dieselOilSulfurConten', type: 'number' },
      { text: '重油比重', value: 'fuelOilDensity', type: 'number' },
      { text: '重油数量', value: 'fuelOilQuantity', type: 'number' },
      { text: '重油规格', value: 'fuelOilSpecification', type: 'string' },
      { text: '重油含硫量', value: 'fuelOilSulfurConten', type: 'number' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return !!this.initialData?.vid
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      if (!this.isEdit) {
        this.$emit('add', {
          ...this.formData,
          vid: Math.random(),
          operationType: 1,
        })
      } else {
        this.formData.operationType = 2
      }
      this.$emit('change', false)
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
