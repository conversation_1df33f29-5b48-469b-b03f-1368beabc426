<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :push-params="pushParams"
      use-ship
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          v-permission="['燃油管理:新增']"
          :to="{ name: 'fuel-report-detail', params: { id: 'new' } }"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          v-permission="['燃油管理:删除']"
          @click="delItem"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.sysreport`]="{ item }">
        <v-btn
          v-if="item.systemReportId"
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: item.systemReportId },
          }"
          color="info"
          x-small
          class="mx-1"
          v-permission="['燃油管理:查看部门报表']"
        >
          查看部门报表
        </v-btn>
        <div v-else>暂无报表</div>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// applicantId	申请人id	string
// applicantName	申请人姓名	string
// chiefEngineerId	轮机长id	string
// chiefEngineerName	轮机长姓名	string
// formDate	日期	string
// fromTime	记录开始时间	string
// id	物理主键	string
// ismFuelOilNo	单号	string
// remark	备注	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// systemReportId	部门报表id	string
// toTime	记录结束时间	string
export default {
  name: 'fuel-report-list',
  created() {
    this.tableName = '燃油管理'
    this.reqUrl = '/business/shipAffairs/ism/getPageOfFuelOil'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '单号', value: 'ismFuelOilNo' },
      { text: '日期', value: 'formDate' },
      { text: '记录开始时间', value: 'fromTime' },
      { text: '记录结束时间', value: 'toTime' },
      { text: '申请人', value: 'applicantName' },
      { text: '轮机长', value: 'chiefEngineerName' },
      { text: '报表', value: 'sysreport' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '报表日期',
      value: 'formDate',
    }
    this.pushParams = { name: 'fuel-report-detail' }
  },

  data() {
    return {
      selected: false,
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/ism/deleteFuelOil',
        {
          id: this.selected.id,
        },
      )
      if (errorRaw) return
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
