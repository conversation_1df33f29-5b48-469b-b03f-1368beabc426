<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-btn
          v-if="canSubmit"
          small
          outlined
          v-permission="['淡水管理:修改']"
          tile
          color="success"
          class="mx-1"
          @click="save"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          保存
        </v-btn>
        <v-btn
          v-if="canSubmit"
          small
          outlined
          v-permission="['淡水管理:修改']"
          tile
          color="success"
          class="mx-1"
          @click="submit"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          保存并提交
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-2 px-0" fluid>
            <v-row>
              <v-col
                class="py-0"
                v-for="h in tableFields"
                :key="h.value"
                cols="12"
                md="3"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfo'"
                  v-model="formData.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
                <vs-date-picker
                  v-else-if="h.value === 'fromTime'"
                  :disabled="!isInit"
                  v-model="formData.fromTime"
                  label="记录开始时间"
                  outlined
                  dense
                  :rules="isInit ? [rules.required] : []"
                ></vs-date-picker>
                <vs-date-picker
                  v-else-if="h.value === 'toTime'"
                  v-model="formData.toTime"
                  label="记录结束时间"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></vs-date-picker>
                <vs-date-picker
                  v-else-if="h.value == 'addWaterDate'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  outlined
                  dense
                ></vs-date-picker>
                <vs-date-picker
                  v-else-if="h.value == 'date'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  outlined
                  dense
                  readonly
                ></vs-date-picker>
                <v-text-field
                  v-else-if="h.value == 'lastMonthStore'"
                  :disabled="!isInit"
                  type="number"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
                <port-select-dialog2
                  v-else-if="h.value == 'portName'"
                  :disabled="isEdit"
                  @select="(val) => (formData[h.value] = val.portCn)"
                  :label="h.text"
                  :initSelected="initPort"
                  dense
                  outlined
                  :rules="[rules.required]"
                ></port-select-dialog2>
                <v-text-field
                  v-else-if="h.type == 'number'"
                  type="number"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      @dbclick="editItem"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="selected.chiefCommit !== false"
          @click="confirm"
          v-permission="['垃圾接收:主管确认']"
        >
          <v-icon left>mdi-check</v-icon>
          主管确认
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['淡水管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!-- <v-btn
          :disabled="!selected"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editItem"
          v-permission="['淡水管理:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn> -->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['淡水管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.systemReportId`]="{ item }">
        <v-btn
          v-if="item.systemReportId"
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: item.systemReportId },
          }"
          color="info"
          x-small
          class="mx-1"
          v-permission="['淡水管理:查看部门报表']"
        >
          查看部门报表
        </v-btn>
        <div v-else>暂无报表</div>
      </template>
      <template v-slot:[`item.chiefCommit`]="{ item }">
        {{ item.chiefCommit ? '是' : '否' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import portSelectDialog2 from '../../components/port-select-dialog2.vue'
export default {
  components: { portSelectDialog2 },
  name: 'fresh-water-list',
  created() {
    this.tableName = '淡水管理'
    this.reqUrl = '/business/shipAffairs/ism/getPageOfFreshWater'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '单号', value: 'freshWaterNo', notFill: true },
      { text: '报表日期', value: 'date' },
      { text: '消耗统计开始时间', value: 'fromTime' },
      { text: '消耗统计结束时间', value: 'toTime' },
      { text: '船长', value: 'captainName', notFill: true },
      {
        text: '大副',
        value: 'chiefMateName',
        hideDefault: true,
        notFill: true,
      },
      {
        text: '轮机长',
        value: 'chiefEngineerName',
        hideDefault: true,
        notFill: true,
      },
      { text: '上月存量', value: 'lastMonthStore', hideDefault: true },
      { text: '本月加装', value: 'thisMonthAdd' },
      { text: '本月消耗', value: 'thisMonthCom' },
      { text: '本月存量', value: 'thisMonthStore' },
      { text: '造水量', value: 'waterProduction' },
      { text: '其他事项', value: 'otherMsg', hideDefault: true },
      { text: '备注', value: 'remark', hideDefault: true },
      { text: '港口名称', value: 'portName', hideDefault: true },
      { text: '加水日期', value: 'addWaterDate' },
      { text: '主管确认', value: 'chiefCommit', notFill: true },
      { text: '报表', value: 'systemReportId', notFill: true },
    ]
    this.fuzzyLabel = ''
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      records: 1,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      isInit: true,
      initPort: {},
    }
  },

  computed: {
    canSubmit() {
      return !this.isEdit || this.formData.status === '0'
    },
    tableFields() {
      return this.headers.filter((h) => !h.notFill)
    },
    thisMonthStore() {
      return (
        Number(this.formData.lastMonthStore) +
          Number(this.formData.waterProduction) +
          Number(this.formData.thisMonthAdd) -
          Number(this.formData.thisMonthCom) || 0
      )
    },
  },

  watch: {
    'formData.shipCode'(val) {
      if (!this.isEdit && val) this.loadRecords(val)
    },
    thisMonthStore(val) {
      if (this.canSubmit) this.formData.thisMonthStore = val
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/ism/deleteFreshWater',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        date: new Date().toISOString().substr(0, 10),
      }
      this.initPort = {}
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected,
        shipCode: this.selected.shipInfo.shipCode,
      }
      this.initPort = {
        portCn: this.selected.portName,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/ism/saveOrUpdateFreshWater'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    async submit() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/ism/saveOrUpdateFreshWater'
      const { data } = await this.postAsync(reqUrl, { ...this.formData }, false)
      if (!data) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/ism/submitFreshWaterById',
        { id: data },
      )
      if (errorRaw) return
      this.$dialog.message.success(`提交成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
      this.closeForm()
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },

    async loadRecords(shipCode) {
      const { errorRaw, data } = await this.getAsync(
        '/business/shipAffairs/ism/getPreOfFreshWater',
        { shipCode },
      )
      if (errorRaw) return
      this.formData = data
      this.formData.shipCode = shipCode
      this.formData.date = new Date().toISOString().substr(0, 10)
      if (!data.fromTime) {
        this.isInit = true
        this.$dialog.message.warning(
          '该船舶没有上报过淡水管理记录,当前为初始化',
        )
      } else {
        this.isInit = false
      }
    },
    async confirm() {
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/ism/saveOrUpdateFreshWater',
        {
          id: this.selected.id,
          chiefCommit: true,
          shipCode: this.selected.shipInfo.shipCode,
          toTime: this.selected.toTime,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('确认成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
