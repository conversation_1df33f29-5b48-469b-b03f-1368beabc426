<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col
                class="py-0"
                v-for="h in tableFields"
                :key="h.value"
                cols="12"
                md="3"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfo'"
                  v-model="formData.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
                <vs-date-picker
                  v-else-if="h.value == 'receiveDate'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  use-today
                  outlined
                  dense
                ></vs-date-picker>
                <v-text-field
                  v-else-if="h.type == 'number'"
                  type="number"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                  v-permission="['淡水加装:修改']"
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '修改' : '新增' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :single-select="false"
      :search-remain="searchObj"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="isEdit"
          @click="exportExcel"
        >
          <v-icon left>mdi-file-excel</v-icon>
          导出EXCEL
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['淡水加装:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!singleSelect"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editItem"
          v-permission="['淡水加装:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn>
        <v-btn
          :disabled="!singleSelect"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['淡水加装:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// actualReceiveNum	实际接受数量	string
// handler	处理人	string
// id	物理主键	string
// portCountry	港口国家	string
// portName	港口名称	string
// receiveDate	加装淡水日期/垃圾接收日期	string
// remark	备注	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// trashType	加装前数量/接收垃圾品种	string
// type	类型：0-淡水加装/1-垃圾接收	string
// waterAdditionNo	单号	string
export default {
  name: 'water-addition-list',
  created() {
    this.tableName = '淡水加装'
    this.reqUrl = '/business/shipAffairs/ism/getPageOfWaterAddition'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '单号', value: 'waterAdditionNo', notFill: true },
      { text: '加装前数量', value: 'trashType', type: 'number' },
      { text: '实际加装数量', value: 'actualReceiveNum', type: 'number' },
      { text: '加装淡水日期', value: 'receiveDate' },
      { text: '处理人', value: 'handler' },
      { text: '港口名称', value: 'portName' },
      { text: '港口国家', value: 'portCountry' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      selected: [],
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      searchObj: { type: 0 },
    }
  },

  computed: {
    singleSelect() {
      return this.selected.length === 1
    },
    tableFields() {
      return this.headers.filter((h) => !h.notFill)
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/ism/deleteWaterAddition',
        { id: this.selected[0].id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        type: 0,
      }
      this.formShow = true
      this.$refs.table.disabled = true
    },
    async editItem() {
      this.formData = {
        ...this.selected[0],
        shipCode: this.selected[0].shipInfo.shipCode,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/ism/saveOrUpdateWaterAddition'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    async exportExcel() {
      await this.blobDownload(
        '/business/shipAffairs/ism/exportWaterAdditionByIds',
        this.selected.map((item) => item.id),
        '淡水加装.xlsx',
      )
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
  },

  mounted() {},
}
</script>

<style></style>
