<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        {{ isEdit ? '修改' : '新增' }}{{ tableName }}
        <v-spacer></v-spacer>
        <v-btn
          v-if="!ischiefCommit"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="save"
          small
          v-permission="['垃圾接收:修改']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          {{ isEdit ? '修改' : '新增' }}
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>

      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0" fluid>
            <v-row>
              <v-col
                class="py-0"
                v-for="h in tableFields"
                :key="h.value"
                cols="12"
                :md="h.type == 'number' ? 6 : h.value == 'remark' ? 12 : 3"
              >
                <v-ship-select
                  v-if="h.value === 'shipInfo'"
                  v-model="formData.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                ></v-ship-select>
                <vs-date-picker
                  v-else-if="h.value === 'fromTime'"
                  :disabled="!isInit"
                  v-model="formData.fromTime"
                  label="开始时间"
                  outlined
                  dense
                  :rules="isInit ? [rules.required] : []"
                ></vs-date-picker>
                <vs-date-picker
                  v-else-if="h.value === 'toTime'"
                  v-model="formData.toTime"
                  label="结束时间"
                  outlined
                  dense
                  :rules="[rules.required]"
                  :min-date="minDate"
                ></vs-date-picker>
                <vs-date-picker
                  v-else-if="h.type == 'date'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  outlined
                  dense
                  :max-date="maxDate"
                  :min-date="minDate"
                ></vs-date-picker>
                <port-select-dialog2
                  v-else-if="h.type == 'port'"
                  :disabled="isEdit"
                  @select="(val) => (formData[h.value] = val.portCn)"
                  :label="h.text"
                  :initSelected="{
                    portCn: formData[h.value],
                  }"
                  dense
                  outlined
                ></port-select-dialog2>
                <v-text-field
                  v-else-if="h.type == 'number'"
                  type="number"
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else-if="h.value == 'remark'"
                  v-model="formData[h.value]"
                  :label="h.text"
                  outlined
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="formData[h.value]"
                  :label="h.text"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      @dbclick="editItem"
      :search-remain="searchObj"
    >
      <template #searchflieds></template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="selected.chiefCommit !== false"
          @click="confirm"
          v-permission="['垃圾接收:主管确认']"
        >
          <v-icon left>mdi-check</v-icon>
          主管确认
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          :disabled="!selected"
          @click="exportExcel"
        >
          <v-icon left>mdi-file-pdf-box</v-icon>
          导出PDF
        </v-btn>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="isEdit"
          @click="createItem"
          v-permission="['垃圾接收:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <!-- <v-btn
          :disabled="!singleSelect"
          outlined
          tile
          color="warning"
          class="mx-1"
          @click="editItem"
          v-permission="['垃圾接收:修改']"
        >
          <v-icon left>mdi-pencil</v-icon>
          修改
        </v-btn> -->
        <v-btn
          :disabled="!selected"
          outlined
          tile
          color="error"
          class="mx-1"
          @click="delItem"
          v-permission="['垃圾接收:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.chiefCommit`]="{ item }">
        {{ item.chiefCommit ? '是' : '否' }}
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
import portSelectDialog2 from '../../components/port-select-dialog2.vue'
// actualReceiveNum	实际接受数量	string
// captain	船长id	string
// captainName	船长	string
// chiefEngineer	轮机长id	string
// chiefEngineerName	轮机长	string
// chiefMate	大幅id	string
// chiefMateName	大幅id	string
// fromTime	从	string
// handler	处理人	string
// id	物理主键	string
// portCountry	港口国家	string
// portName	港口名称	string
// receiveDate	加装淡水日期/垃圾接收日期	string
// remark	备注	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// toTime	到	string
// traFoodRecDate	食品垃圾接收日期	string
// traFoodRecNum	食品垃圾接收数量	string
// traFoodRecPort	食品垃圾接收港口	string
// traLifeRecDate	生活垃圾接收日期	string
// traLifeRecNum	生活垃圾接收数量	string
// traLifeRecPort	生活垃圾接收港口	string
// traOilRecDate	污油接收日期	string
// traOilRecNum	污油接收数量	string
// traOilRecPort	污油接收港口	string
// traOtherRecDate	其他垃圾接收日期	string
// traOtherRecNum	其他垃圾接收数量	string
// traOtherRecPort	其他垃圾接收港口	string
// traWaterRecDate	污水接收日期	string
// traWaterRecNum	污水接收数量	string
// traWaterRecPort	污水接收港口	string
// trashType	加装前数量/接收垃圾品种	string
// type	类型：0-淡水加装/1-垃圾接收	string
// waterAdditionNo	单号	string
export default {
  components: { portSelectDialog2 },
  name: 'garbage-accept-list',
  created() {
    this.tableName = '垃圾接收'
    this.reqUrl = '/business/shipAffairs/ism/getPageOfWaterAddition'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '单号', value: 'waterAdditionNo', notFill: true },
      { text: '船长', value: 'captainName', notFill: true, hideDefault: true },
      {
        text: '大副',
        value: 'chiefMateName',
        notFill: true,
        hideDefault: true,
      },
      {
        text: '轮机长',
        value: 'chiefEngineerName',
        notFill: true,
        hideDefault: true,
      },
      { text: '开始时间', value: 'fromTime' },
      { text: '结束时间', value: 'toTime' },
      { text: '处理人', value: 'handler' },
      { text: '主管确认', value: 'chiefCommit', notFill: true },
      { text: '食品垃圾接收日期', value: 'traFoodRecDate', type: 'date' },
      { text: '食品垃圾接收港口', value: 'traFoodRecPort', type: 'port' },
      { text: '食品垃圾接收数量', value: 'traFoodRecNum', type: 'number' },
      { text: '生活垃圾接收日期', value: 'traLifeRecDate', type: 'date' },
      { text: '生活垃圾接收港口', value: 'traLifeRecPort', type: 'port' },
      { text: '生活垃圾接收数量', value: 'traLifeRecNum', type: 'number' },
      { text: '污水接收日期', value: 'traWaterRecDate', type: 'date' },
      { text: '污水接收港口', value: 'traWaterRecPort', type: 'port' },
      { text: '污水接收数量', value: 'traWaterRecNum', type: 'number' },
      { text: '污油接收日期', value: 'traOilRecDate', type: 'date' },
      { text: '污油接收港口', value: 'traOilRecPort', type: 'port' },
      { text: '污油接收数量', value: 'traOilRecNum', type: 'number' },
      { text: '其他垃圾接收日期', value: 'traOtherRecDate', type: 'date' },
      { text: '其他垃圾接收港口', value: 'traOtherRecPort', type: 'port' },
      { text: '其他垃圾接收数量', value: 'traOtherRecNum', type: 'number' },
      { text: '备注', value: 'remark' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '',
      value: '',
    }
  },

  data() {
    return {
      selected: false,
      formData: {},
      isEdit: false,
      ischiefCommit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      searchObj: { type: 1 },
      isInit: false,
      minDate: '',
      maxDate: '',
    }
  },

  computed: {
    tableFields() {
      return this.headers.filter((h) => !h.notFill)
    },
  },

  watch: {
    'formData.shipCode'(val) {
      if (!this.isEdit && val) this.loadRecords(val)
    },
    'formData.fromTime'(val) {
      this.minDate = val
    },
    'formData.toTime'(val) {
      this.maxDate = val
    },
  },

  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/ism/deleteWaterAddition',
        { id: this.selected.id },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`删除失败，请重试`)
        return
      }
      this.$dialog.message.success(`删除成功`)
      await this.$refs.table.loadTableData()
      this.selected = false
    },
    createItem() {
      this.formData = {
        handler: this.$local.data.get('userInfo').nickName,
        attachmentIds: [],
        type: 1,
        receiveDate: new Date().toISOString().substr(0, 10),
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.ischiefCommit = false
    },
    async editItem() {
      this.formData = {
        ...this.selected,
        shipCode: this.selected.shipInfo.shipCode,
      }
      this.formShow = true
      this.$refs.table.disabled = true
      this.isEdit = true
      this.ischiefCommit = this.formData.chiefCommit
    },

    async save() {
      if (!this.$refs.form.validate()) return
      const reqUrl = '/business/shipAffairs/ism/saveOrUpdateWaterAddition'
      const { errorRaw } = await this.postAsync(
        reqUrl,
        { ...this.formData, type: 1, chiefCommit: false },
        false,
      )
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      this.closeForm()
    },

    async exportExcel() {
      await this.getBlobDownload(
        '/business/shipAffairs/ism/exportWaterAdditionByIds',
        { id: this.selected.id },
      )
    },

    closeForm() {
      this.$refs.form.reset()
      this.formData = {
        attachmentIds: [],
      }
      this.formShow = false
      this.$refs.table.disabled = false
      this.isEdit = false
    },
    async loadRecords(shipCode) {
      const { errorRaw, data } = await this.getAsync(
        '/business/shipAffairs/ism/getPreOfWaterAddition',
        { shipCode },
      )
      if (errorRaw) return
      this.formData = data
      this.formData.shipCode = shipCode
      this.formData.handler = this.$local.data.get('userInfo').nickName
      this.formData.receiveDate = new Date().toISOString().substr(0, 10)
      if (!data.fromTime) {
        this.isInit = true
        this.$dialog.message.warning(
          '该船舶没有上报过垃圾接受记录,当前为初始化',
        )
      } else {
        this.isInit = false
      }
    },
    async confirm() {
      const { errorRaw } = await this.postAsync(
        '/business/shipAffairs/ism/saveOrUpdateWaterAddition',
        {
          id: this.selected.id,
          chiefCommit: true,
          shipCode: this.selected.shipInfo.shipCode,
          toTime: this.selected.toTime,
          type: 1,
        },
      )
      if (!errorRaw) {
        this.$dialog.message.success('确认成功')
        await this.$refs.table.loadTableData()
        this.selected = false
      }
    },
  },

  mounted() {},
}
</script>

<style></style>
