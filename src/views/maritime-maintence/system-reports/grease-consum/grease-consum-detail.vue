<template>
  <v-container fluid>
    <v-detail-view
      :title="`滑油消耗报表-${
        this.$route.params.id !== 'new' ? detail.greaseConsumptionNo : '新增'
      }`"
      :tooltip="isEdit ? detail.greaseConsumptionNo : '新增'"
      :backRouteName="backRouteName"
      :subtitles="subtitles"
      :can-submit="canSubmit"
      :can-save="this.detail.status != 2 && this.detail.status != 3"
      @save="save"
      @submit="submit"
      v-permission="['滑油消耗管理:编辑']"
    >
      <template v-if="detail.auditParams" v-slot:topcontent>
        <v-card-text class="mt-2 pb-0">
          <v-form ref="aform">
            <v-audit ref="audit" :auditParams="detail.auditParams"></v-audit>
          </v-form>
        </v-card-text>
      </template>
      <template #基本信息>
        <v-form
          :readonly="detail.status == '2' || detail.status == '3'"
          ref="form"
        >
          <v-container fluid>
            <v-row>
              <v-col class="py-0" cols="12" md="3">
                <v-ship-select
                  v-model="detail.shipCode"
                  required
                  dense
                  :rules="[rules.required]"
                  :readonly="isEdit"
                ></v-ship-select>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  label="船长"
                  v-model="detail.captainName"
                  dense
                  outlined
                  disabled
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  label="轮机长"
                  v-model="detail.chiefEngineerName"
                  dense
                  outlined
                  disabled
                ></v-text-field>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <vs-date-picker
                  v-model="detail.tableDate"
                  label="填报日期"
                  outlined
                  dense
                  readonly
                  use-today
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <vs-date-picker
                  :disabled="!isInit"
                  v-model="detail.fromTime"
                  label="记录开始时间"
                  outlined
                  dense
                  :rules="isInit ? [rules.required] : []"
                ></vs-date-picker>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <vs-date-picker
                  disabled
                  v-model="detail.toTime"
                  label="记录结束时间"
                  outlined
                  dense
                  :rules="[rules.required]"
                ></vs-date-picker>
              </v-col>
              <!--              <v-col class="py-0" cols="12" md="3">
                <port-select-dialog2
                  @select="(port) => (detail.portOfBunkering = port.portCn)"
                  :rules="[isEdit ? rules.required : true]"
                  :ship-code="detail.shipCode"
                  :disabled="isEdit"
                  outlined
                  dense
                  :init-selected="initPort"
                ></port-select-dialog2>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                &lt;!&ndash; :disabled="!detail.shipCode" &ndash;&gt;
                <v-supply-select
                  :disabled="true"
                  v-model="detail.supplierId"
                  :ship-code="detail.shipCode"
                  :readonly="isEdit"
                  @select="
                    (item) => {
                      currency = item.currency
                    }
                  "
                  :init-selected="initSupply"
                ></v-supply-select>
                &lt;!&ndash; <v-supply-select
                  :disabled="!detail.shipCode"
                  v-model="detail.supplierId"
                  :rules="[isEdit ? rules.required : true]"
                  :ship-code="detail.shipCode"
                  :readonly="isEdit"
                  @select="
                    (item) => {
                      currency = item.currency
                    }
                  "
                  :init-selected="initSupply"
                ></v-supply-select> &ndash;&gt;
              </v-col>-->
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  v-if="true"
                  outlined
                  dense
                  v-model="detail.ccyCode"
                  readonly
                  label="币种"
                ></v-text-field>
                <v-select
                  v-else
                  v-model="detail.currencyId"
                  :items="currency"
                  item-text="ccyCode"
                  item-value="currencyType"
                  :readonly="isEdit"
                  label="币种"
                  outlined
                  dense
                  :disabled="true"
                ></v-select>
              </v-col>
              <v-col class="py-0" cols="12" md="3">
                <v-text-field
                  disabled
                  label="总价"
                  v-model="totalPrice"
                  dense
                  outlined
                ></v-text-field>
              </v-col>
              <v-col
                class="py-0"
                v-for="h in formFields"
                :key="h.value"
                cols="12"
                md="3"
              >
                <!--                this.formFields = [-->
                <!--                { text: '主机运行时间总计(h)', value: 'meRunningTime' },-->
                <!--                { text: '副机运行时间总计(h)', value: 'aeRunningTime' },-->
                <!--                { text: '主机滑油循环柜存油(L)', value: 'meloc', type: 'number' },-->
                <!--                { text: '气缸油日均消耗(L/D)', value: 'cylConsumption', type: 'number' },-->
                <!--                { text: '气缸油注油率(g/kwh)', value: 'cylFillingRate', type: 'number2' },-->
                <!--                { text: '报表月份', value: 'yearMonthTime', type: 'month' },-->
                <!--                ]-->

                <vs-date-picker
                  v-if="h.value == 'dateOfBunkering'"
                  v-model="detail[h.value]"
                  :label="h.text"
                  outlined
                  dense
                  :disabled="isEdit"
                  :readonly="!canSubmit"
                ></vs-date-picker>
                <v-year-month-picker
                  :disabled="true"
                  v-else-if="h.type == 'month'"
                  v-model="detail[h.value]"
                  :label="h.text"
                  dense
                  outlined
                  :rules="[rules.required]"
                  :readonly="h.readonly"
                ></v-year-month-picker>
                <v-text-field
                  v-else-if="h.type == 'number'"
                  type="number"
                  v-model="detail[h.value]"
                  :label="h.text"
                  dense
                  outlined
                  :readonly="h.readonly"
                  :rules="[rules.required]"
                ></v-text-field>
                <v-text-field
                  v-else-if="h.type == 'number2'"
                  type="number"
                  v-model="detail[h.value]"
                  :label="h.text"
                  dense
                  outlined
                  :readonly="h.readonly"
                  :rules="[rules.required]"
                ></v-text-field>
                <v-text-field
                  v-else
                  v-model="detail[h.value]"
                  :label="h.text"
                  dense
                  outlined
                  :readonly="h.readonly"
                  :rules="[rules.required]"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="detail.remark"
                  label="备注"
                  outlined
                  dense
                  :readonly="!canSubmit"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </template>
      <template #加油信息>
        <v-table-list
          :show-select="false"
          v-model="grease"
          :headers="headers1"
          :items="detail.ismGreaseConsumptionBunkerDTO"
          item-key="id"
        >
          <template v-if="true" v-slot:[`item.portOfBunkering`]="{ item }">
            <v-text-field
              v-model="item.portNameOfBunkering"
              dense
              outlined
              single-line
              readonly
            ></v-text-field>
          </template>
          <template v-if="true" v-slot:[`item.dateOfBunkering`]="{ item }">
            <v-text-field
              v-model="item.dateOfBunkering"
              dense
              outlined
              single-line
              readonly
            ></v-text-field>
          </template>
          <template v-if="true" v-slot:[`item.supplierName`]="{ item }">
            <v-text-field
              v-model="item.supplierName"
              dense
              outlined
              single-line
              readonly
            ></v-text-field>
          </template>
        </v-table-list>
      </template>
      <template v-if="!isEdit" #消耗明细按钮>
        <!-- <v-btn
          outlined
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="saveAndMove"
          v-permission="['滑油消耗管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          保存并查看明细
        </v-btn> -->
      </template>
      <template #消耗明细>
        <v-table-list
          :show-select="false"
          v-model="grease"
          :headers="headers"
          :items="detail.detailList"
          item-key="greaseId"
        >
          <template
            v-if="
              (detail.status == '0' ||
                detail.status == '1' ||
                detail.status == '4') &&
              !hasRecords
            "
            v-slot:[`item.oilStoredLastMonth`]="{ item }"
          >
            <v-text-field
              v-if="isInit"
              v-model="item.oilStoredLastMonth"
              type="number"
              dense
              outlined
              single-line
            ></v-text-field>
            <div v-else>{{ item.oilStoredLastMonth }}</div>
          </template>
          <template
            v-if="
              (detail.status == '0' ||
                detail.status == '1' ||
                detail.status == '4') &&
              !hasRecords
            "
            v-slot:[`item.bunkering`]="{ item }"
          >
            <v-text-field
              v-model="item.bunkering"
              type="number"
              dense
              outlined
              single-line
            ></v-text-field>
          </template>
          <template
            v-if="
              (detail.status == '0' ||
                detail.status == '1' ||
                detail.status == '4') &&
              !hasRecords
            "
            v-slot:[`item.consumption`]="{ item }"
          >
            <v-text-field
              v-model="item.consumption"
              type="number"
              dense
              outlined
              single-line
            ></v-text-field>
          </template>
          <template
            v-if="
              (detail.status == '0' ||
                detail.status == '1' ||
                detail.status == '4') &&
              !hasRecords
            "
            v-slot:[`item.price`]="{ item }"
          >
            <v-text-field
              v-model="item.price"
              type="number"
              dense
              outlined
              single-line
            ></v-text-field>
          </template>
          <template
            v-if="
              (detail.status == '0' ||
                detail.status == '1' ||
                detail.status == '4') &&
              !hasRecords
            "
            v-slot:[`item.totalPrice`]="{ item }"
          >
            {{ (item.bunkering * item.price).toFixed(4) }}
          </template>
          <template
            v-if="
              detail.status == '0' ||
              detail.status == '1' ||
              detail.status == '4'
            "
            v-slot:[`item.remark`]="{ item }"
          >
            <!-- <v-text-field
              v-model="item.remark"
              dense
              single-line
            ></v-text-field> -->
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  @click="editRemark(item)"
                  v-bind="attrs"
                  v-on="on"
                  v-model="item.remark"
                  dense
                  outlined
                  single-line
                ></v-text-field>
              </template>
              <span>{{ item.remark }}</span>
            </v-tooltip>
          </template>
          <template
            v-if="
              (detail.status == '0' ||
                detail.status == '1' ||
                detail.status == '4') &&
              !hasRecords
            "
            v-slot:[`item.oilStoredThisMonth`]="{ item }"
          >
            {{
              item.oilStoredLastMonth * 1 -
              item.consumption * 1 +
              item.bunkering * 1
            }}
          </template>
        </v-table-list>
      </template>
      <template
        v-if="
          detail.status == '0' || detail.status == '1' || detail.status == '4'
        "
        #其他油柜存量按钮
      >
        <v-btn
          :disabled="!detail.shipCode"
          outlined
          v-if="false"
          tile
          small
          color="success"
          class="mx-1"
          @click.stop="createTank"
          v-permission="['油柜存量:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="!oilTank"
          v-if="false"
          outlined
          tile
          small
          color="error"
          class="mx-1"
          @click="delTank"
          v-permission="['油柜存量:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template #其他油柜存量>
        <v-table-list
          :show-select="
            detail.status == '0' || detail.status == '1' || detail.status == '4'
          "
          v-model="oilTank"
          :headers="headersBox"
          :items="detail.oilTankList"
          item-key="id"
        >
          <template v-slot:[`item.type`]="{ item }">
            <span v-if="item.type == '1581991857030852611'">主机系统油</span>
            <span v-if="item.type == '1581991857047629825'">副机系统油</span>
            <span v-if="item.type == '1581991857022464003'">主机气缸油</span>
            <span v-if="item.type == '1649340717607944194'">其他小品种油</span>
          </template>
          <template v-slot:[`item.stockNums`]="{ item }" v-if="false">
            <v-text-field
              v-model="item.stockNums"
              outlined
              dense
              type="number"
            ></v-text-field>
          </template>
          <template
            v-if="
              detail.status == '0' ||
              detail.status == '1' ||
              detail.status == '4'
            "
            v-slot:[`item.remark`]="{ item }"
          >
            <!-- <v-text-field
              v-model="item.remark"
              dense
              single-line
            ></v-text-field> -->
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  @click="editRemarkTank(item)"
                  v-bind="attrs"
                  v-on="on"
                  v-model="item.remark"
                  dense
                  outlined
                  single-line
                ></v-text-field>
              </template>
              <span>{{ item.remark }}</span>
            </v-tooltip>
          </template>
        </v-table-list>
      </template>
    </v-detail-view>
    <v-dialog v-model="dialog1" max-width="600">
      <template v-slot:default="dialog1">
        <v-card style="height: 300px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemark"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog1.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <v-dialog v-model="dialog12" max-width="600">
      <template v-slot:default="dialog12">
        <v-card style="height: 300px">
          <v-card-title>
            编辑备注
            <v-spacer></v-spacer>
            <v-btn
              small
              outlined
              tile
              color="success"
              class="mx-1"
              @click="saveRemarkTank"
            >
              <v-icon left>mdi-plus-circle</v-icon>
              确定
            </v-btn>
            <v-btn
              small
              outlined
              tile
              class="mx-1"
              @click="dialog12.value = false"
            >
              <v-icon>mdi-close</v-icon>
              关闭
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-form ref="form12">
              <v-row>
                <v-col cols="12" class="py-1">
                  <v-textarea
                    outlined
                    label="备注"
                    v-model="editRemarkDetails12.remark"
                    dense
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </template>
    </v-dialog>
    <addOilTanks
      v-model="dialog"
      @success="success"
      :initial-data="initialData"
      :shipCode="detail.shipCode"
    ></addOilTanks>
  </v-container>
</template>
<script>
import routerControl from '@/mixin/routerControl'
import currencyHelper from '@/mixin/currencyHelper'
import addOilTanks from './addOilTanks.vue'
import moment from 'moment'
// aeRunningTime	副机运行时间	string
// attachmentRecords	附件ids	array	CommonAttachment
// chiefEngineer	轮机长id	string
// chiefEngineerName	轮机长名称	string
// cylConsumption	汽缸油消耗	string
// cylFillingRate	注油率	string
// dateOfBunkering	加油日期	string(date-time)
// detailList	明细列表	array	IsmGreaseConsumptionDetailOutputDTO
// fromTime	从时间	string(date-time)
// greaseConsumptionNo	报表单号	string
// id	物理主键	string
// meRunningTime	主机运行时间	string
// meloc	主机滑油循坏柜存油	string
// portOfBunkering	加油港口	string
// shipCode	船舶编码	string
// status	状态	string
// systemReportId	部门报表id	string
// tableDate	报表日期	string(date-time)
// toTime	到时间	string(date-time)

// brand	滑油牌号	string
// bunkering	加装	integer
// category	滑油类别	string
// consumption	消耗	integer
// createTime	创建时间	string
// delFlag	逻辑删除标志	boolean
// greaseId	滑油id	string
// id	物理主键	string
// mainId	主表id	string
// oilStoredLastMonth	上月末结存	integer
// oilStoredThisMonth	本月结存	integer
// remark	备注	string
// updateTime	更新时间	string
export default {
  components: { addOilTanks },
  name: 'grease-consum-detail',
  mixins: [routerControl, currencyHelper],
  created() {
    this.backRouteName = 'grease-consum-list'
    this.subtitles = ['基本信息', '加油信息', '消耗明细', '其他油柜存量']
    this.formFields = [
      {
        text: '本月度主机运行时长(h)',
        value: 'meRunningTime',
        type: 'number',
        readonly: true,
      },
      {
        text: '本月度副机运行时长(h)',
        value: 'aeRunningTime',
        type: 'number',
        readonly: true,
      },
      {
        text: '主机滑油循环柜存油(L)',
        value: 'meloc',
        type: 'number',
        readonly: true,
      },
      {
        text: '气缸油日均消耗(L/D)',
        value: 'cylConsumption',
        type: 'number',
        readonly: true,
      },
      {
        text: '气缸油注油率(g/kwh)',
        value: 'cylFillingRate',
        type: 'number2',
        readonly: true,
      },
      {
        text: '报表月份',
        value: 'yearMonthTime',
        type: 'month',
        readonly: true,
      },
    ]
    this.headers1 = [
      { text: '加油港口', value: 'portOfBunkering' },
      { text: '加油日期', value: 'dateOfBunkering' },
      { text: '供应商', value: 'supplierName' },
    ]
    this.headers = [
      { text: '滑油类别', value: 'category' },
      { text: '滑油牌号', value: 'brand' },
      { text: '上月消耗后库存(L)', value: 'oilStoredLastMonth' },
      { text: '本月消耗(L)', value: 'consumption' },
      { text: '本月消耗后库存(L)', value: 'oilStoredThisMonth' },
      { text: '本月加装(L)', value: 'bunkering' },
      // { text: '本月结存', value: 'oilStoredThisMonth' },
      // { text: '加装', value: 'bunkering' },
      // { text: '消耗', value: 'consumption' },
      { text: '单价', value: 'price' },
      { text: '加装总价', value: 'totalPrice' },
      { text: '备注', value: 'remark' },
    ]
    this.headersBox = [
      { text: '油柜名称', value: 'tankName' },
      { text: '滑油类别', value: 'type' },
      { text: '滑油牌号', value: 'brand' },
      { text: '存量', value: 'stockNums' },
      { text: '备注', value: 'remark' },
    ]
    this.detail.toTime = moment().endOf('month').format('YYYY-MM-DD')
  },
  data() {
    return {
      detail: {
        status: '0',
        detailList: [],
        detailList2: [],
        oilTankList: [],
        ismGreaseConsumptionBunkerDTO: [],
      },
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      grease: false,
      isInit: false,
      currency: [],
      initSupply: {},
      initPort: {},
      needFields: [],
      dialog1: false,
      editRemarkDetails: {},
      dialog: false,
      initialData: {},
      oilTank: false,
      dialog12: false,
      editRemarkDetails12: {},
      hasRecords: false,
    }
  },

  computed: {
    isEdit() {
      return this.$route.params.id !== 'new' && this.detail.status !== '1'
    },
    canSubmit() {
      return this.detail.status != '3'
    },
    totalPrice() {
      return this.detail.detailList.reduce(
        (total, item) => total + (item.bunkering * item.price).toFixed(4) * 1,
        0,
      )
    },
  },

  watch: {
    // 'detail.shipCode'(val) {
    //   // if (val && !this.isEdit) this.loadRecords(val)
    //   if (val && !this.isEdit) {
    //     this.loadRecords2(val)
    //     // this.loadRecordDates()
    //   }
    // },
    // 'detail.fromTime'(val) {
    //   if (val && !this.isEdit) this.loadRecordDates()
    // },
    // 'detail.toTime'(val) {
    //   if (val && !this.isEdit) this.loadRecordDates()
    // },
    // 'detail.aeRunningTime1'() {
    //   this.detail.aeRunningTime =
    //     Number(
    //       this.detail.aeRunningTime1 == null ||
    //         this.detail.aeRunningTime1 == undefined ||
    //         this.detail.aeRunningTime1 == ''
    //         ? 0
    //         : this.detail.aeRunningTime1,
    //     ) +
    //     Number(
    //       this.detail.aeRunningTime2 == null ||
    //         this.detail.aeRunningTime2 == undefined ||
    //         this.detail.aeRunningTime2 == ''
    //         ? 0
    //         : this.detail.aeRunningTime2,
    //     ) +
    //     Number(
    //       this.detail.aeRunningTime3 == null ||
    //         this.detail.aeRunningTime3 == undefined ||
    //         this.detail.aeRunningTime3 == ''
    //         ? 0
    //         : this.detail.aeRunningTime3,
    //     )
    // },
    // 'detail.aeRunningTime2'() {
    //   this.detail.aeRunningTime =
    //     Number(
    //       this.detail.aeRunningTime1 == null ||
    //         this.detail.aeRunningTime1 == undefined ||
    //         this.detail.aeRunningTime1 == ''
    //         ? 0
    //         : this.detail.aeRunningTime1,
    //     ) +
    //     Number(
    //       this.detail.aeRunningTime2 == null ||
    //         this.detail.aeRunningTime2 == undefined ||
    //         this.detail.aeRunningTime2 == ''
    //         ? 0
    //         : this.detail.aeRunningTime2,
    //     ) +
    //     Number(
    //       this.detail.aeRunningTime3 == null ||
    //         this.detail.aeRunningTime3 == undefined ||
    //         this.detail.aeRunningTime3 == ''
    //         ? 0
    //         : this.detail.aeRunningTime3,
    //     )
    // },
    // 'detail.aeRunningTime3'() {
    //   this.detail.aeRunningTime =
    //     Number(
    //       this.detail.aeRunningTime1 == null ||
    //         this.detail.aeRunningTime1 == undefined ||
    //         this.detail.aeRunningTime1 == ''
    //         ? 0
    //         : this.detail.aeRunningTime1,
    //     ) +
    //     Number(
    //       this.detail.aeRunningTime2 == null ||
    //         this.detail.aeRunningTime2 == undefined ||
    //         this.detail.aeRunningTime2 == ''
    //         ? 0
    //         : this.detail.aeRunningTime2,
    //     ) +
    //     Number(
    //       this.detail.aeRunningTime3 == null ||
    //         this.detail.aeRunningTime3 == undefined ||
    //         this.detail.aeRunningTime3 == ''
    //         ? 0
    //         : this.detail.aeRunningTime3,
    //     )
    // },
  },

  methods: {
    editRemark(item) {
      // console.log(item)
      this.editRemarkDetails = item
      this.dialog1 = true
    },
    saveRemark() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog1 = false
    },
    async save(goBack, notMove = false) {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/shipAffairs/ism/saveOrUpdateGreaseConsumation'
      const { errorRaw, data } = await this.postAsync(url, {
        ...this.detail,
        detailList: this.detail.detailList.map((item) => ({
          ...item,
          totalPrice: (item.bunkering * item.price).toFixed(4),
          oilStoredThisMonth:
            item.oilStoredLastMonth * 1 -
            item.consumption * 1 +
            item.bunkering * 1,
        })),
      })
      if (errorRaw) return false
      if (notMove) return data
      goBack()
    },
    async submit(goBack) {
      // if (!(this.$refs?.aform?.validate() ?? true)) return
      const data = await this.save(goBack, true)
      if (!data) {
        return
      } else {
        let mappingDetails = []
        for (let f of this.needFields) {
          mappingDetails.push({
            processInstanceId: this.detail.auditParams.processInstanceId,
            mappingCode: f.mappingCode,
            mappingContent: this.mapping[f.mappingCode],
            mappingType: f.mappingType,
          })
        }
        let { errorRaw } = await this.postAsync(
          '/business/seaAffairs/templateMapping/saveMappingDetail',
          mappingDetails,
        )
        if (errorRaw) return
        if (!this.detail.auditParams) {
          const { errorRaw } = await this.getAsync(
            '/business/shipAffairs/ism/submitGreaseConsumptionById',
            { id: data },
          )
          if (!errorRaw) goBack()
        } else {
          const error = await this.$refs.audit.submit()
          if (!error) goBack()
        }
      }
    },
    async saveAndMove() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = '/business/shipAffairs/ism/saveOrUpdateGreaseConsumation'
      const { data } = await this.postAsync(url, {
        ...this.detail,
      })
      if (!data) return
      this.closeAndTo('grease-consum-detail', { id: data })
    },

    async loadRecords(shipCode) {
      const { errorRaw, data } = await this.getAsync(
        '/business/shipAffairs/ism/getPreOfGreaseConsumation',
        { shipCode },
      )
      if (errorRaw) return

      this.detail.detailList = data.detailList
      this.detail.fromTime = data.fromTime
      this.detail.captain = data.captain
      this.detail.captainName = data.captainName
      this.detail.chiefEngineer = data.chiefEngineer
      this.detail.chiefEngineerName = data.chiefEngineerName
      this.detail.fromTime = data.fromTime
      this.detail.yearMonthTime = data.yearMonthTime
      if (!data.fromTime) {
        this.isInit = true
        this.$dialog.message.warning(
          '该船舶没有上报过滑油消耗记录,当前为初始化',
        )
      }
      await this.loadNeedFields()
    },
    // 校验是否初次录入
    async loadRecords2(shipCode) {
      const { errorRaw, data } = await this.getAsync(
        '/business/shipAffairs/ism/getPreOfGreaseConsumationStartTime',
        { shipCode },
      )
      if (errorRaw) return

      // this.detail.detailList = data.detailList
      // this.detail.fromTime = data.fromTime
      this.detail.captain = data.captain
      this.detail.captainName = data.captainName
      this.detail.chiefEngineer = data.chiefEngineer
      this.detail.chiefEngineerName = data.chiefEngineerName
      this.detail.fromTime = data.fromTime
      this.detail.yearMonthTime = data.yearMonthTime
      if (!data.fromTime) {
        this.isInit = true
        this.$dialog.message.warning(
          '该船舶没有上报过滑油消耗记录,当前为初始化',
        )
      }
      // await this.loadNeedFields()
    },
    async loadRecordDates() {
      if (!this.detail.fromTime) return
      if (!this.detail.toTime) return
      if (!this.detail.shipCode) {
        this.$dialog.message.warning('请选择船舶')
        return
      }
      let time1 = new Date(this.detail.toTime)
      let time2 = new Date(this.detail.fromTime)

      if (time1 < time2) {
        this.$dialog.message.warning('记录结束时间不能小于开始时间')
        return
      }
      const { errorRaw, data } = await this.getAsync(
        '/business/shipAffairs/ism/getPreOfGreaseConsumptionDate',
        {
          shipCode: this.detail.shipCode,
          fromTime: this.detail.fromTime,
          toTime: moment(this.detail.toTime)
            .add(1, 'days')
            .format('YYYY-MM-DD'),
        },
      )
      if (errorRaw) return

      //console.log('loadRecordDates 返回的油柜数据:', data.oilTankList)
      this.detail.oilTankList = data.oilTankList
      this.detail.detailList = data.detailList
      this.detail.captain = data.captain
      this.detail.captainName = data.captainName
      this.detail.chiefEngineer = data.chiefEngineer
      this.detail.chiefEngineerName = data.chiefEngineerName
      this.detail.yearMonthTime = data.yearMonthTime
      this.detail.ismGreaseConsumptionBunkerDTO =
        data.ismGreaseConsumptionBunkerDTO
      this.initSupply = {
        id: data.supplierId,
        name: data.supplierName,
      }
      //this.detail.meloc = data.meloc
      this.detail.meRunningTime = data.meRunningTime
      this.detail.aeRunningTime = data.aeRunningTime
      this.detail.cylFillingRate = data.cylFillingRate
      this.detail.cylConsumption = data.cylConsumption
      this.detail.supplierId = data.supplierId
      this.currency = data.supplierBankListOutputDTO
      this.detail.currencyId = data.currencyId
      this.detail.ccyCode = data.ccyCode
      this.hasRecords = data.hasRecords
      await this.loadNeedFields()
    },
    async loadDetail1() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/ism/getDetailOfGreaseConsumptionByShipCode',
        {
          shipCode: this.detail.shipCode,
        },
      )
      this.detail = data
    },
    async loadDetail() {
      if (!this.isEdit) return
      const { data } = await this.getAsync(
        '/business/shipAffairs/ism/getDetailOfGreaseConsumptionById',
        {
          id: this.$route.params.id,
        },
      )
      console.log('loadDetail 返回的data数据:', data)
      // console.log('loadDetail 返回的油柜数据:', data.oilTankList)
      this.detail = data
      this.detail.aeRunningTime
      this.initPort = {
        portCn: data.portOfBunkering,
      }
      this.initSupply = {
        id: data.supplierId,
        name: data.supplierName,
      }
      this.hasRecords = data.hasRecords
      console.log('loadDetail 返回的数据:', this.detail)
    },

    createItem() {
      this.initData = {}
      this.dialog = true
    },
    editItem() {
      this.initData = this.grease
      this.dialog = true
    },
    delItem() {
      if (this.grease.id)
        this.delList.push({ ...this.grease, operationType: 3 })
      this.detail.detailList = this.detail.detailList.filter(
        (i) => i.vid !== this.grease.vid,
      )
      this.grease = false
    },
    async loadNeedFields() {
      if (!this.detail?.auditParams?.processInstanceId) return
      const { data } = await this.getAsync(
        '/business/seaAffairs/templateMapping/getReportNeedFieldByprocessInsId',
        { processInstanceId: this.detail.auditParams.processInstanceId },
      )
      this.needFields = data || []
      // 初始化签名字段的用户id
      for (const t of this.needFields) {
        if (t.mappingType === '0') {
          this.mapping[t.mappingCode] = this.$local.data.get('userInfo').userId
        } else {
          this.mapping[t.mappingCode] = new Date(Date.now())
            .toISOString()
            .substr(0, 10)
        }
      }
    },
    createTank() {
      this.initialData = {}
      this.dialog = true
    },
    editTank() {
      this.initialData = this.oilTank
      this.dialog = true
    },
    delTank() {
      this.detail.oilTankList = this.detail.oilTankList.filter(
        (s) => !(s.id === this.oilTank.id),
      )
      // if (this.grease.id)
      //   this.delList.push({ ...this.grease, operationType: 3 })
      // this.detail.detailList = this.detail.detailList.filter(
      //   (i) => i.vid !== this.grease.vid,
      // )
      // this.grease = false
    },
    success(newOil) {
      console.log(newOil)
      if (
        this.detail.oilTankList.some(
          (s) => s.greaseId === newOil.greaseId && s.tankId === newOil.tankId,
        )
      ) {
        this.$dialog.message.error('油柜及滑油牌号重复重复')
        return
      }
      this.detail.oilTankList.push(newOil)
      console.log(this.detail.oilTankList)
    },
    editRemarkTank(item) {
      // console.log(item)
      this.editRemarkDetails12 = item
      this.dialog12 = true
    },
    saveRemarkTank() {
      // console.log(item)
      // this.editRemarkDetails =
      this.dialog12 = false
    },
  },

  mounted() {
    this.loadDetail()
  },
}
</script>

<style></style>
