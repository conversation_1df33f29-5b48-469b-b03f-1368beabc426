<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :search-date="searchDate"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      use-ship
      :push-params="pushParams"
      :search-remain="searchObj"
      use-status
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-year-month-picker
            v-model="searchObj.yearMonthTime"
            label="年月"
            outlined
          ></v-year-month-picker>
        </v-col>
        <v-col cols="12" sm="6" md="2">
          <v-switch
            class="mt-1"
            dense
            v-model="searchObj.isMe"
            label="待我审批"
            color="success"
          ></v-switch>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          v-if="true"
          :to="{ name: 'grease-consum-detail', params: { id: 'new' } }"
          v-permission="['滑油消耗管理:新增']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          新增
        </v-btn>
        <v-btn
          :disabled="
            selected.status !== '0' &&
            selected.status !== '4' &&
            selected.status !== '1'
          "
          outlined
          tile
          color="error"
          class="mx-1"
          v-if="true"
          @click="delItem"
          v-permission="['滑油消耗管理:删除']"
        >
          <v-icon left>mdi-delete-empty</v-icon>
          删除
        </v-btn>
      </template>
      <template v-slot:[`item.systemReportId`]="{ item }">
        <v-btn
          v-if="item.systemReportId"
          width="90"
          tile
          :to="{
            name: 'dept-report-detail',
            params: { id: item.systemReportId },
          }"
          color="info"
          x-small
          class="mx-1"
          v-permission="['滑油消耗管理:查看部门报表']"
        >
          查看部门报表
        </v-btn>
        <div v-else>暂无报表</div>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
// aeRunningTime	副机运行时间	string
// chiefEngineer	轮机长id	string
// chiefEngineerName	轮机长名称	string
// cylConsumption	汽缸油消耗	string
// cylFillingRate	注油率	string
// dateOfBunkering	加油日期	string
// fromTime	从时间	string
// greaseConsumptionNo	报表单号	string
// id	物理主键	string
// meRunningTime	主机运行时间	string
// meloc	主机滑油循坏柜存油	string
// portOfBunkering	加油港口	string
// shipInfo	船舶信息	ShipInfoDO	ShipInfoDO
// status	状态	string
// tableDate	报表日期	string
// toTime	到时间	string
export default {
  name: 'grease-consum-list',
  created() {
    this.tableName = '滑油消耗管理'
    this.reqUrl = '/business/shipAffairs/ism/getPageOfGreaseConsumption'
    this.searchDicts = [
      {
        dicType: '',
        label: '',
        key: '',
      },
    ]
    this.headers = [
      { text: '船舶', value: 'shipInfo' },
      { text: '报表单号', value: 'greaseConsumptionNo' },
      { text: '报表日期', value: 'tableDate' },
      { text: '开始日期', value: 'fromTime' },
      { text: '结束日期', value: 'toTime' },
      { text: '轮机长', value: 'chiefEngineerName' },
      { text: '主机运行时间(h)', value: 'meRunningTime', hideDefault: true },
      { text: '副机运行时间(h)', value: 'aeRunningTime', hideDefault: true },
      // { text: '主机滑油循环柜存油', value: 'meloc' },
      { text: '汽缸油日耗(L/D)', value: 'cylConsumption' },
      { text: '汽缸油注油率(g/kwh)', value: 'cylFillingRate' },
      { text: '加油港口', value: 'portOfBunkering' },
      { text: '加油日期', value: 'dateOfBunkering' },
      { text: '审批状态', value: 'status' },
      { text: '业务状态', value: 'businessStatus' },
      { text: '报表', value: 'systemReportId' },
    ]
    this.fuzzyLabel = ''
    this.searchDate = {
      label: '加油日期',
      value: 'dateOfBunkering',
    }
    this.pushParams = { name: 'grease-consum-detail' }
  },

  data() {
    return {
      selected: false,
      searchObj: {
        yearMonthTime: '',
        isMe: true,
      },
    }
  },

  methods: {
    async delItem() {
      if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
      const { errorRaw } = await this.getAsync(
        '/business/shipAffairs/ism/deleteGreaseConsumptionById',
        {
          id: this.selected.id,
        },
      )
      if (errorRaw) return
      this.selected = false
      await this.$refs.table.loadTableData()
    },
  },

  mounted() {},
}
</script>

<style></style>
