<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1300"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        新增油柜存量
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-select
                  v-model="formData.tankId"
                  :items="tanks"
                  item-text="name"
                  item-value="id"
                  label="油柜名称"
                  :rules="[rules.required]"
                  outlined
                  dense
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-dict-select
                  v-model="formData.type"
                  label="滑油类型"
                  dictType="ship_grease_info_type"
                  :rules="[rules.required]"
                  required
                  outlined
                  dense
                ></v-dict-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  :disabled="!formData.type"
                  v-model="formData.greaseId"
                  :items="oils"
                  item-text="nameEn"
                  item-value="id"
                  label="滑油名称/滑油号"
                  :rules="[rules.required]"
                  outlined
                  dense
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="存量"
                  v-model="formData.stockNums"
                  :rules="[rules.number2]"
                  outlined
                  dense
                  type="number"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="12">
                <v-textarea
                  label="备注"
                  v-model="formData.remark"
                  outlined
                  dense
                ></v-textarea>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '创建' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'addOilTanks',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {},
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    shipCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchObj2: {},
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v == 0 || '必填项不能为空',
        number2: (v) =>
          /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(v) ||
          '填写格式错误，允许填写数字两位小数',
      },
      tanks: [],
      oilType: [],
      oils: [],
    }
  },
  watch: {
    open(val) {
      this.formData = {}
      this.loadShipCarbin()
      //   this.loadOil()
      this.dialog = val
      this.$refs?.form?.resetValidation()
    },
    'formData.type'(val) {
      if (val) {
        this.loadOil()
      }
    },
  },
  computed: {
    isEdit() {
      return !!this.initialData?.vid
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.$emit('change', false)
      this.formData.vid = Math.floor(Math.random() * 1000 + 1)
      this.formData.id = this.formData.vid
      this.formData.operationType = this.isEdit ? 2 : 1
      this.formData.tankName = this.tanks.find(
        (tank) => tank.id === this.formData.tankId,
      )?.name
      //   this.formData.brand = this.oils.find(
      //     (tank) => tank.id === this.formData.greaseId,
      //   )?.code
      this.formData.brand = this.oils.find(
        (tank) => tank.id === this.formData.greaseId,
      )?.nameEn

      console.log(this.formData)
      if (!this.isEdit) this.$emit('success', this.formData)
      // this.$emit('success', {
      //   vid: Math.floor(Math.random() * 1000 + 1),
      //   usd: this.usd,
      //   // supplyId:this.$route.params.id,
      //   ...this.formData,
      //   operationType: this.isEdit ? 2 : 1,
      //   currency: this.currencyInfo.find((i) => i.id === this.currencyId)
      //     ?.ccyCode,
      //   currencyId: this.currencyId,
      // })
    },
    async loadShipCarbin() {
      const { data } = await this.getAsync('/business/shipAffairs/Cabin/list', {
        shipCode: this.shipCode,
        type: 0,
        size: 1000,
        current: 1,
      })
      this.tanks = data.records
    },
    async loadOil() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/greaseDistribution/pageByParams',
        {
          shipCode: this.shipCode,
          type: this.formData.type,
          size: 1000,
          current: 1,
        },
      )
      this.oils = data.records
      this.oils.forEach((oil) => {
        oil.nameEn = oil.nameEn + ':' + oil.code
      })
    },
  },
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
