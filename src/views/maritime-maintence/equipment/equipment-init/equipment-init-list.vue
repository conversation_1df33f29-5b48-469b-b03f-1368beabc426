<template>
  <v-container fluid>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      v-model="selected"
      :fuzzy-label="fuzzyLabel"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchRemain"
      :filter-func="fliterFunc"
      use-ship
    >
      <template #searchflieds>
        <v-col cols="12" md="2">
          <v-text-field
            label="设备主体名"
            outlined
            dense
            v-model="searchRemain.equipmentName"
            clearable
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="设备类别"
            outlined
            dense
            :items="['主机', '辅助设备', '泵浦', '辅机']"
            v-model="searchRemain.equipType"
            clearable
          ></v-select>
        </v-col>
        <v-col cols="12" md="2">
          <v-select
            label="设备状态"
            outlined
            dense
            :items="[
              { text: '未初始化', value: 0 },
              { text: '已初始化', value: 1 },
            ]"
            v-model="searchRemain.type"
          ></v-select>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          dense
          tile
          class="mx-1"
          color="info"
          @click="open"
          :disabled="!selected || searchRemain.type === 1"
          v-permission="['待初始化设备:待初始化设备']"
        >
          <v-icon left>mdi-eye</v-icon>
          待初始化设备
        </v-btn>
      </template>
    </v-table-searchable>
  </v-container>
</template>
<script>
export default {
  name: 'equipment-init-list',
  created() {
    this.tableName = '初始化设备'
    this.reqUrl = '/business/shipAffairs/planRuntime/getInitEquip'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '设备主体名', value: 'equipmentName' },
      { text: '设备类别', value: 'equipType' },
    ]
    this.fuzzyLabel = ''
    this.fliterFunc = (items) =>
      items.map((i) => ({
        ...i,
        id: `${i.shipCode}${i.equipmentName}${i.equipType}${i.year}${i.month}`,
      }))
  },

  data() {
    return {
      selected: false,
      searchRemain: {
        type: 0,
      },
    }
  },
  computed: {
    pushParams() {
      return {
        name: 'equipment-init-items',
        params: {
          ...this.selected,
        },
      }
    },
  },
  watch: {
    'searchRemain.type': {
      handler() {
        this.selected = false
      },
    },
  },
  methods: {
    open() {
      this.$router.push({
        name: this.pushParams.name,
        params: {
          ...this.pushParams.params,
        },
      })
    },
  },

  mounted() {},
}
</script>

<style></style>
