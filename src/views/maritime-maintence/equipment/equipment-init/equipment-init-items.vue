<template>
  <v-form ref="form">
    <v-container fluid>
      <v-card>
        <v-card-title>
          公共数据
          <v-spacer></v-spacer>
          <v-btn outlined tile color="" class="mx-1" @click="goBack">
            <v-icon left dark>mdi-format-list-bulleted-square</v-icon>
            返回初始化设备界面
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-col cols="12" md="2">
            <v-text-field
              label="总运行小时"
              dense
              v-model="totalTime"
              :rules="[rules.required]"
              type="number"
              required
            ></v-text-field>
          </v-col>
        </v-card-text>
      </v-card>
      <v-card>
        <v-card-title>
          {{ tableName }}
          <v-spacer></v-spacer>
          <v-btn
            outlined
            tile
            color="success"
            class="mx-1"
            @click.stop="create"
            :disabled="selected.length === 0"
            v-permission="['待初始化设备:待初始化设备']"
          >
            <v-icon left>mdi-plus-circle</v-icon>
            项目初始化
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-divider></v-divider>
          <v-data-table
            :headers="headers"
            v-model="selected"
            :items="items"
            item-key="idKey"
            dense
            show-select
            class="use-divider"
          >
            <template v-slot:[`item.curMaintenanceTime`]="{ item }">
              <v-text-field
                label="维护时运行小时"
                v-model="item.curMaintenanceTime"
                single-line
                dense
                type="number"
                :rules="[
                  selected.map((e) => e.idKey).includes(item.idKey)
                    ? rules.required
                    : true,
                ]"
                required
              ></v-text-field>
            </template>
            <template v-slot:[`item.curRenewTime`]="{ item }">
              <v-text-field
                label="换新时运行小时"
                v-model="item.curRenewTime"
                single-line
                dense
                type="number"
                :rules="[
                  selected.map((e) => e.idKey).includes(item.idKey)
                    ? rules.required
                    : true,
                ]"
                required
              ></v-text-field>
            </template>
            <template v-slot:[`item.afterRenewTime`]="{ item }">
              <v-text-field
                label="换新后运行小时"
                v-model="item.afterRenewTime"
                single-line
                dense
                type="number"
                :rules="[
                  selected.map((e) => e.idKey).includes(item.idKey)
                    ? rules.required
                    : true,
                ]"
                required
                readonly
              ></v-text-field>
            </template>
            <template v-slot:[`item.afterMaintenanceTime`]="{ item }">
              <v-text-field
                label="维护后运行时间"
                v-model="item.afterMaintenanceTime"
                single-line
                dense
                type="number"
                :rules="[
                  selected.map((e) => e.idKey).includes(item.idKey)
                    ? rules.required
                    : true,
                ]"
                required
                readonly
              ></v-text-field>
            </template>
            <template v-slot:[`item.maintenanceTime`]="{ item }">
              <vs-date-picker
                single-line
                dense
                v-model="item.maintenanceTime"
                :rules="[item.curMaintenanceTime ? rules.required : true]"
              ></vs-date-picker>
            </template>
            <template v-slot:[`item.renewTime`]="{ item }">
              <vs-date-picker
                single-line
                dense
                v-model="item.renewTime"
              ></vs-date-picker>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </v-container>
  </v-form>
</template>
<script>
import routerControl from '@/mixin/routerControl'
export default {
  name: 'equipment-init-items',
  created() {
    this.tableName = '待初始化项目'
    this.reqUrl = '/business/shipAffairs/planRuntime/getNeedInitProject'
    this.headers = [
      { text: '项目编号', value: 'projectCode' },
      { text: '维护项目名', value: 'projectName' },
      { text: '设备类型', value: 'equipType' },
      { text: '设备主体', value: 'equipmentName' },
      { text: '维护保养日期', value: 'maintenanceTime' },
      { text: '维护时运行小时', value: 'curMaintenanceTime' },
      { text: '维护后运行小时', value: 'afterMaintenanceTime' },
      { text: '换新日期', value: 'renewTime' },
      { text: '换新时运行小时', value: 'curRenewTime' },
      { text: '换新后运行小时', value: 'afterRenewTime' },
      { text: '维护周期', value: 'maintenanceCycle' },
    ]
    this.fuzzyLabel = ''
    this.backRouteName = 'equipment-init-list'
  },
  mixins: [routerControl],
  watch: {
    selected: {
      handler() {
        this.selected.forEach((e) => {
          e.afterMaintenanceTime = this.totalTime - e.curMaintenanceTime
          e.afterRenewTime = this.totalTime - e.curRenewTime
        })
      },
      deep: true,
    },
    totalTime: {
      handler() {
        this.selected.forEach((e) => {
          e.afterMaintenanceTime = this.totalTime - e.curMaintenanceTime
          e.afterRenewTime = this.totalTime - e.curRenewTime
        })
      },
    },
  },
  data() {
    return {
      selected: [],
      items: [],
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        int: (v) => /^\d+?$/.test(v) || '请输入整数数字',
      },
      totalTime: null,
    }
  },
  methods: {
    async loadTableData() {
      const { errorRaw, data } = await this.getAsync(this.reqUrl, {
        ...this.$route.params,
      })
      if (errorRaw) {
        return
      }
      let i = 0
      this.items = data.map((val) => {
        return {
          ...val,
          idKey: ++i,
        }
      })
      this.selected = this.items
    },
    async create() {
      if (!this.$refs.form.validate()) return
      if (!(await this.$dialog.msgbox.confirm('确定初始化选中记录？'))) return
      let items = this.selected.map((val) => {
        return {
          ...val,
          totalTime: parseInt(this.totalTime),
        }
      })
      const { errorRaw } = await this.postAsync(
        `/business/shipAffairs/planRuntime/initPlanRuntime`,
        items,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success('初始化完成')
      await this.loadTableData()
      this.closeAndTo(this.backRouteName)
      this.selected = []
    },
    dialogTips() {
      this.$dialog.message.info('请先填写公共数据部分')
    },
    goBack() {
      this.$router.push({
        name: 'equipment-init-list',
      })
    },
  },

  mounted() {
    this.loadTableData()
    this.dialogTips()
  },
}
</script>

<style></style>
