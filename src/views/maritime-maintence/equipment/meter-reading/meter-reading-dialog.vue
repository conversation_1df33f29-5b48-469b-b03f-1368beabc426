<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        设备运行时间
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-year-month-picker
                  :outlined="false"
                  :dense="false"
                  :use-current="false"
                  v-model="formData.date"
                ></v-year-month-picker>
              </v-col>
              <!-- <v-col cols="12" md="3">
                <v-text-field
                  label="年份"
                  v-model="formData.year"
                  :rules="[rules.int]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <v-text-field
                  label="月份"
                  v-model="formData.month"
                  :rules="[rules.int]"
                  required
                ></v-text-field>
              </v-col> -->
              <v-col cols="12" md="3">
                <v-text-field
                  label="总运行时间"
                  v-model="formData.runtime"
                  type="number"
                  :rules="[rules.number]"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
export default {
  name: 'meter-reading-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        int: (v) => /^[0-9]*$/.test(v) || '请输入整数',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url = `/business/shipAffairs/planRuntime/readMeter`
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        year: parseInt(this.formData.date.slice(0, 4)),
        month: parseInt(this.formData.date.slice(5, 7)),
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
