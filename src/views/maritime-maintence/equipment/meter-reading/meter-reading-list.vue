<template>
  <v-container fluid>
    <v-card v-if="formShow" class="mb-2">
      <v-card-title>
        修改上次抄表
        <v-spacer></v-spacer>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          small
          @click="update"
          v-permission="['抄表管理:修改']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          修改
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-form ref="form">
          <v-container class="py-0 px-0">
            <v-row>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="editItem.shipName"
                  label="船舶名称"
                  single-line
                  disabled
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="editItem.equipmentName"
                  label="设备主体名"
                  single-line
                  disabled
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="editItem.equipType"
                  label="设备类别"
                  single-line
                  disabled
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <v-text-field
                  v-model="editItem.totalTime"
                  label="上次抄表总运行小时"
                  single-line
                  dense
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="2">
                <vs-date-picker
                  v-model="editItem.meterReadingDate"
                  label="上次抄表日期"
                  outlined
                  disabled
                  dense
                ></vs-date-picker>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
    <v-table-searchable
      ref="table"
      :table-name="tableName"
      :single-select="false"
      v-model="selected"
      :headers="headers"
      :req-url="reqUrl"
      :fix-header="false"
      :search-remain="searchObj"
    >
      <template #searchflieds>
        <v-col cols="12" sm="6" md="2">
          <v-ship-select
            ref="select"
            v-model="searchObj.shipCode"
          ></v-ship-select>
        </v-col>
        <v-col cols="12" md="2">
          <vs-date-picker
            v-model="searchObj.meterReadingDate"
            label="上次抄表日期"
            :max-date="`${initialData.year}-${initialData.month}-${initialData.date}`"
            outlined
            dense
          ></vs-date-picker>
        </v-col>
      </template>
      <template #btns>
        <v-btn
          outlined
          tile
          color="success"
          class="mx-1"
          :disabled="!selected"
          @click="save"
          v-permission="['抄表管理:保存抄表']"
        >
          <v-icon left>mdi-plus-circle</v-icon>
          保存抄表
        </v-btn>
        <v-btn
          outlined
          tile
          color="primary"
          class="mx-1"
          @click="upload"
          v-permission="['抄表管理:导出抄表记录']"
        >
          <v-icon left>mdi-arrow-collapse-down</v-icon>
          导出抄表记录
        </v-btn>
      </template>
      <template v-slot:[`item.totalTime`]="{ item }">
        {{ item.totalTime }}
        <v-btn
          v-if="compareDate(item.meterReadingDate) && item.canEdit"
          outlined
          tile
          color="success"
          class="mx-1"
          @click="exitDate(item)"
          small
          v-permission="['抄表管理:修改']"
        >
          修改
        </v-btn>
      </template>
      <template v-slot:[`item.totalTime2`]="{ item }">
        <v-text-field
          v-model="item.totalTime2"
          label="本次抄表总运行小时"
          type="number"
          single-line
          :disabled="!item.canEdit"
          dense
        ></v-text-field>
      </template>
      <template v-slot:[`item.meterReadingDate2`]="{ item }">
        <vs-date-picker
          v-model="item.meterReadingDate2"
          disabled
          dense
        ></vs-date-picker>
      </template>
    </v-table-searchable>
    <v-voyage-dialog
      v-model="dialogVoyage"
      :initialData="initialDataVoyage"
    ></v-voyage-dialog>
  </v-container>
</template>
<script>
import VVoyageDialog from '../equipment-search/v-voyage-dialog.vue'
// belongCode	上级分类	string
// cateName	物料分类名称	string
// id	物理主键	string
// remark	备注	string
// sapCode	SAP代码	string
export default {
  components: { VVoyageDialog },
  name: 'meter-reading-list',
  created() {
    this.tableName = '抄表管理'
    this.reqUrl = '/business/shipAffairs/planRuntime/page'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '设备主体名', value: 'equipmentName' },
      { text: '设备类别', value: 'equipType' },
      { text: '上次抄表总运行小时', value: 'totalTime' },
      { text: '上次抄表日期', value: 'meterReadingDate' },
      { text: '本次抄表总运行小时', value: 'totalTime2' },
      { text: '本次抄表日期', value: 'meterReadingDate2' },
    ]
    this.subHeaders = [
      { text: '科目名称', value: 'subjectName' },
      { text: '科目业务分类', value: 'subjectType' },
      { text: 'SAP代码', value: 'sapCode' },
    ]
    this.searchDate = {
      label: '抄表日期',
      interval: false,
      value: 'meterReadingDate',
    }
  },

  data() {
    return {
      selected: [],
      formData: {},
      isEdit: false,
      loading: false,
      formShow: false,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
      initSubject: {},
      initDockSubject: {},
      searchObj: {},
      initialData: {},
      maxTime: 30,
      editItem: {},
      dialogVoyage: false,
      initialDataVoyage: {},
    }
  },
  watch: {
    'searchObj.shipCode'(val) {
      if (!val) return
      this.getNewDate()
    },
  },

  methods: {
    upload() {
      this.dialogVoyage = true
    },
    compareDate(val) {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const date = now.getDate()

      const now2 = new Date(val)
      const year1 = now2.getFullYear()
      const month1 = now2.getMonth() + 1
      const date1 = now2.getDate()

      if (year1 == year && month1 == month && date1 == date) {
        return true
      } else {
        return false
      }
    },
    exitDate(item) {
      this.formShow = true

      // { text: '船舶名称', value: 'shipName' },
      // { text: '设备主体名', value: 'equipmentName' },
      // { text: '设备类别', value: 'equipType' },
      // { text: '上次抄表总运行小时', value: 'totalTime' },
      // { text: '上次抄表日期', value: 'meterReadingDate' },

      this.editItem.shipName = item.shipName
      this.editItem.equipmentName = item.equipmentName
      this.editItem.equipType = item.equipType
      this.editItem.totalTime = item.totalTime
      this.editItem.shipCode = item.shipCode
      this.editItem.year = item.year
      this.editItem.month = item.month
      this.editItem.id = item.id
      this.editItem.meterReadingDate = item.meterReadingDate
      console.log(this.editItem)
      this.$refs.table.disabled = true
    },
    closeForm() {
      this.$refs.form.reset()
      this.formShow = false
      this.$refs.table.disabled = false
      // this.isEdit = false
      // this.ship = {
      //   shipName: '',
      //   month: '',
      //   year: '',
      //   department: '',
      // }
    },
    async update() {
      const url = `/business/shipAffairs/planRuntime/updateYesterdayPlanRuntime`
      const { errorRaw } = await this.postAsync(url, this.editItem)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }

      this.formShow = false
      this.$refs.table.disabled = false
      await this.$refs.table.loadTableData()
    },
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    async getNewDate() {
      const { data } = await this.getAsync(
        '/business/shipAffairs/planRuntime/getNewDate?shipCode=' +
          this.searchObj.shipCode,
      )
      this.searchObj.meterReadingDate = data
      const now = new Date()
      this.initialData.year = now.getFullYear()
      this.initialData.month = now.getMonth() + 1
      this.initialData.date = now.getDate()
    },
    async save() {
      if (this.selected.length == 0) {
        this.$dialog.message.error(`请勾选抄表记录`)
        return
      }
      let hasError = false
      this.selected.forEach((item) => {
        if (hasError) return
        if (item.totalTime2 == 0) {
          this.$dialog.message.error(`本次抄表总运行小时不能为0`)
          hasError = true
          return
        }
        if (item.totalTime2 < item.totalTime) {
          this.$dialog.message.error(
            `本次抄表总运行小时不能小于上次抄表总运行小时`,
          )
          hasError = true
          return
        }
      })
      if (hasError) return
      const url = `/business/shipAffairs/planRuntime/dayPlanRuntime`
      const { errorRaw } = await this.postAsync(url, this.selected)
      if (errorRaw) {
        this.$dialog.message.error(`保存失败，请重试`)
        return
      }
      this.$dialog.message.success(`保存成功`)
      await this.$refs.table.loadTableData()
      // || (!selected.totalTime2 && selected.canEdit)
    },
    // async delItem() {
    //   if (!(await this.$dialog.msgbox.confirm('确定删除此记录？'))) return
    //   const { errorRaw } = await this.getAsync(
    //     '/business/shipAffairs/MaterialInfo/cate/delete',
    //     { id: this.selected.id },
    //     false,
    //   )
    //   if (errorRaw) {
    //     this.$dialog.message.error(`删除失败，请重试`)
    //     return
    //   }
    //   this.$dialog.message.success(`删除成功`)
    //   await this.$refs.table.loadTableData()
    //   this.selected = false
    // },

    // async save() {
    //   if (!this.$refs.form.validate()) return
    //   const reqUrl = '/business/shipAffairs/MaterialInfo/cate/saveOrUpdate'
    //   const { errorRaw } = await this.postAsync(
    //     reqUrl,
    //     { ...this.formData },
    //     false,
    //   )
    //   if (errorRaw) {
    //     this.$dialog.message.error(`保存失败，请重试`)
    //     return
    //   }
    //   this.$dialog.message.success(`保存成功`)
    //   await this.$refs.table.loadTableData()
    //   this.closeForm()
    // },

    // closeForm() {
    //   this.$refs.form.resetValidation()
    //   this.formData = {
    //     attachmentIds: [],
    //   }
    //   this.formShow = false
    //   this.$refs.table.disabled = false
    //   this.isEdit = false
    // },
  },

  mounted() {
    this.getNewDate()
  },
}
</script>

<style></style>
