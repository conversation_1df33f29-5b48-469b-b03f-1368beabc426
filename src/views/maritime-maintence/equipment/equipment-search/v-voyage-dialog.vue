<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        导出抄表记录
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <v-ship-select
                  @getShipName="getShipName"
                  v-model="formData.shipCode"
                  :rules="[rules.required]"
                  required
                ></v-ship-select>
              </v-col>
              <v-col cols="12" md="3">
                <v-year-month-picker
                  v-model="formData.date"
                  outlined
                  dense
                  :rules="[rules.required]"
                  required
                ></v-year-month-picker>
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  label="设备类别"
                  outlined
                  dense
                  v-model="formData.equipType"
                  :items="equipmentType"
                  :rules="[rules.required]"
                  required
                ></v-select>
              </v-col>

              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  导出
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-voyage-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {
        voyage: '',
      },
      flieName: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
      equipmentType: [],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    async getClassByType() {
      const { data, errorRaw } = await this.getAsync(
        '/system/dict-data/getClassByDictType',
        { dictType: 'meter_engine' },
      )
      if (errorRaw) {
        this.$dialog.message.error(`字典获取失败，部分功能受损`)
        return null
      }
      if (data.length === 0) {
        this.$dialog.message.error(`数据字典为空，部分功能受损`)
      }
      this.equipmentType = data
    },
    closeForm() {
      this.$emit('change', false)
    },
    getFileName() {
      this.formData.year = parseInt(this.formData.date.slice(0, 4))
      this.formData.month = parseInt(this.formData.date.slice(5, 7))
      switch (this.formData.equipType) {
        case '主机':
          this.flieName = `${this.formData.shipName}${this.formData.year}年${this.formData.month}月主机设备运行时间表.xls`
          break
        case '副机':
          this.flieName = `${this.formData.shipName}${this.formData.year}年${this.formData.month}月副机设备运行时间表.xlsx`
          break
        case '辅助设备':
          this.flieName = `${this.formData.shipName}${this.formData.year}年${this.formData.month}月辅助设备运行时间表.xlsx`
          break
        case '泵浦':
          this.flieName = `${this.formData.shipName}${this.formData.year}年${this.formData.month}月泵浦运行时间表.xlsx`
          break
        case '甲板':
          this.flieName = `${this.formData.shipName}${this.formData.year}年${this.formData.month}月甲板运行时间表.xlsx`
          break
        default:
          this.flieName = '设备运行时间表.xlsx'
      }
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.getFileName()
      const { errorRaw } = await this.blobDownload(
        `/business/shipAffairs/planRuntime/exportExcel`,
        this.formData,
      )
      if (errorRaw) {
        return
      }
      this.formData = {}
      this.$emit('change', false)
      this.$emit('success')
    },
    getShipName(val) {
      this.formData.shipName = val?.dictLabel
    },
  },
  async mounted() {
    this.getClassByType()
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
