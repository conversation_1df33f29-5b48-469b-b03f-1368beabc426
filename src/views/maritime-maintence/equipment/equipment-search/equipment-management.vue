<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="py-1">
        {{ tableName }}
        <v-spacer></v-spacer>
        <v-btn tile outlined color="info" class="mx-1" @click="pushGo">
          <v-icon left>mdi-eye</v-icon>
          查看未抄表信息
        </v-btn>
        <v-btn outlined tile color="success" class="mx-1" @click="upload">
          <v-icon left>mdi-microsoft-excel</v-icon>
          导出维护设备的EXCEL
        </v-btn>
      </v-card-title>
      <v-card-text class="py-1">
        <v-row>
          <v-col cols="12" sm="6" md="1">
            <v-btn
              outlined
              tile
              color="primary"
              class="mx-1"
              @click.stop="loadTableData"
            >
              <v-icon left>mdi-magnify</v-icon>
              搜索
            </v-btn>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-ship-select v-model="searchRemain.shipCode"></v-ship-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-text-field
              label="设备主体名"
              outlined
              dense
              v-model="searchRemain.equipmentName"
              clearable
            ></v-text-field>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-select
              label="设备类别"
              outlined
              dense
              v-model="searchRemain.equipType"
              :items="['主机', '副机', '泵浦', '辅助设备']"
              clearable
            ></v-select>
          </v-col>
          <v-col cols="12" sm="6" md="2">
            <v-year-month-picker
              outlined
              v-model="searchRemain.date"
              :clearable="false"
            ></v-year-month-picker>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-data-table
        class="use-divider"
        :footer-props="{ itemsPerPageOptions: [10, 15, 30, 50] }"
        v-model="selectedItem"
        dense
        :headers="headers"
        :items="items"
        :loading="loading"
        single-select
        item-key="id"
        max-height="500px"
        show-select
        :options.sync="options"
        @click:row="selectRow"
        @dblclick:row="dbclick"
      ></v-data-table>
      <v-voyage-dialog
        v-model="dialogVoyage"
        :initialData="initialDataVoyage"
      ></v-voyage-dialog>
      <v-equipment-dialog
        v-model="dialog"
        reqUrl="/business/shipAffairs/planRuntime/page"
        :initialData="initialData"
      ></v-equipment-dialog>
    </v-card>
  </v-container>
</template>
<script>
import vEquipmentDialog from './v-equipment-dialog.vue'
import VVoyageDialog from './v-voyage-dialog.vue'
// 请求搜索防抖
const debounce = (fn, delay = 300) => {
  let timer = null
  return function () {
    let context = this
    let args = arguments
    clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}
export default {
  components: { vEquipmentDialog, VVoyageDialog },
  name: 'equipment-management',
  created() {
    this.tableName = '设备查询'
    this.headers = [
      { text: '船舶名称', value: 'shipName' },
      { text: '设备主体名', value: 'equipmentName' },
      { text: '设备类别', value: 'equipType' },
      { text: '年份', value: 'year' },
      { text: '月份', value: 'month' },
    ]
    this.pushParams = {
      name: 'equipment-maintenance-list',
    }
  },
  data() {
    return {
      searchRemain: {
        date: new Date().toISOString().substring(0, 7),
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
      },
      options: {},
      selectedItem: [],
      computedBoolColumns: [],
      items: [],
      initialData: {},
      loading: false,
      dialog: false,
      dialogVoyage: false,
      initialDataVoyage: {},
    }
  },
  watch: {
    options: {
      handler() {
        this.loadTableData(false)
      },
      deep: true,
    },
    'searchRemain.date': {
      handler(val) {
        this.searchRemain.year = parseInt(val.slice(0, 4))
        this.searchRemain.month = parseInt(val.slice(5, 7))
        this.loadTableData(false)
      },
      immediate: true,
    },
    'searchRemain.shipCode': {
      handler() {
        this.loadTableData(false)
      },
      immediate: true,
    },
    'searchRemain.equipType': {
      handler() {
        this.loadTableData(false)
      },
      immediate: true,
    },
  },
  activated() {
    if (this.$route.query.reload) {
      this.loadDicts()
      this.loadTableData()
    }
  },
  computed: {},
  methods: {
    debounceLoadTableData: debounce(function (that) {
      that.loadTableData()
    }, 500),
    debounceImmedLoadTableData: debounce(function (that) {
      that.loadTableData()
    }, 0),
    selectRow(_, { item }) {
      this.selectedItem = [item]
    },
    dbclick() {
      this.initialData = this.selectedItem[0]
      this.dialog = true
    },
    async loadTableData(restPage = true) {
      this.loading = true
      restPage && (this.options.page = 1) && (this.options.size = 15)
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/planRuntime/getEquipByParams`,
        this.searchRemain,
      )
      if (!errorRaw) {
        this.items = data.records.map((val) => {
          return {
            ...val,
            id: `${val.shipCode}${val.equipmentName}${val.equipType}${val.year}${val.month}`,
          }
        })
        this.loading = false
      }
    },
    upload() {
      this.dialogVoyage = true
    },
    pushGo() {
      this.$router.push({
        name: 'meter-reading-list',
      })
    },
  },

  mounted() {},
}
</script>

<style></style>
