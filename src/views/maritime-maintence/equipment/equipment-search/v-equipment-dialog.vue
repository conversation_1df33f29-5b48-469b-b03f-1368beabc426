<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1500"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-table-searchable
              outlined
              :table-name="tableName"
              :headers="headers"
              req-url="/business/shipAffairs/planRuntime/page"
              :search-remain="formData"
            ></v-table-searchable>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'v-equipment-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      tableName: '',
      formData: {},
      items: [],
      searchRemain: {},
      headers: [
        { text: '项目编码', value: 'projectCode' },
        { text: '维护项目名', value: 'projectName' },
        { text: '维护周期', value: 'maintenanceCycle' },
        { text: '维护保养日期', value: 'maintenanceTime' },
        { text: '本月运行时间(H)', value: 'monthTime' },
        { text: '换新时间(H)', value: 'renewTime' },
        { text: '维护时运行小时(H)', value: 'curMaintenanceTime' },
        { text: '维护后运行小时(H)', value: 'afterMaintenanceTime' },
        { text: '换新后运行小时(H)', value: 'afterRenewTime' },
        { text: '总运行小时(H)', value: 'totalTime' },
        { text: '操作人', value: 'user' },
        { text: '操作人所属部门', value: 'userDept' },
      ],
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.tableName = this.formData.equipType + '---维护信息'
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async loadTableData() {
      const { errorRaw, data } = await this.getAsync(
        `/business/shipAffairs/planRuntime/page`,
        this.formData,
      )
      if (errorRaw) {
        return
      }
      this.items = data.records
    },
  },
  mounted() {},
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
