<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        修改签收日期（如需）
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" sm="6" md="4">
                <vs-date-picker
                  v-model="formData.signingDate"
                  label="签收日期"
                  outlined
                  dense
                  class="mb-2"
                  hide-details
                ></vs-date-picker>
              </v-col>
              <v-col cols="12" sm="6" md="8">
                <v-text-field
                  v-model="formData.remark"
                  label="调减入库原因"
                  outlined
                  dense
                  hide-details
                  placeholder="请输入调减入库原因"
                  :rules="rules.remark"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  保存
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'reduce-in-dialog',
  activated() {
    this.dialog = this.open
  },
  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      open: false,
      formData: {},
      resolveFn: null,
      rules: {
        remark: [(v) => !!v || '请输入调减入库原因'],
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.open = false
      this.resolveFn(false)
    },

    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.open = false
      this.resolveFn(this.formData)
    },

    async confirm() {
      this.open = true
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
