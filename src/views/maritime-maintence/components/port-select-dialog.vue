<template>
  <v-dialog-select
    ref="dialog"
    v-model="val"
    :label="label"
    :headers="headers"
    item-text="portCn"
    :req-url="reqUrl"
    :search-dicts="searchDicts"
    :search-remain="searchObj"
    fuzzy-label="港口中/英文名"
    @update="update"
    max-width="1300"
    :disabled="disabled"
    :readonly="readonly"
    :init-selected="initSelected"
    :rules="[rules.required]"
  >
    <template v-slot:searchflieds>
      <!-- <v-col cols="12" sm="6" md="3">
        <v-select
          v-model="searchObj.euipmentType"
          outlined
          label="设备类型"
          dense
          clearable
          :items="euipmentTypes"
        ></v-select>
      </v-col> -->
    </template>
  </v-dialog-select>
</template>
<script>
export default {
  name: 'port-select-dialog',
  model: {
    prop: 'value',
    event: 'update',
  },
  inject: {
    form: { default: null },
  },
  created() {
    this.form && this.form.register(this)
    // if (this.value) {
    //   this.val = this.initText
    // }
    this.reqUrl = '/business/shipAffairs/Port/list'
    this.headers = [
      { text: '中文名称', value: 'portCn' },
      { text: '英文名称', value: 'portEn' },
      { text: '港口代码', value: 'portCode' },
      { text: '3位码', value: 'codeThree' },
      { text: '国家代码', value: 'codeTwo' },
      { text: '分类', value: 'type' },
    ]
    this.searchDicts = [
      {
        dicType: 'port_type',
        label: '港口类型',
        key: 'type',
      },
    ]
  },
  props: {
    value: [String, Object],
    numbers: Array,
    disabled: [String, Boolean],
    readonly: [String, Boolean],
    initSelected: Object,
    label: {
      type: String,
      default: '交货港口',
    },
    // read
  },
  data() {
    return {
      searchObj: {},
      val: '',
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
      },
    }
  },

  watch: {},

  methods: {
    validate(force, value) {
      return this.$refs.dialog.validate(force, value)
    },
    reset() {
      this.$refs.dialog.reset()
    },
    resetValidation() {
      this.$refs.dialog.resetValidation()
    },
    update() {
      this.$emit('update', this.val.id)
      this.$emit('select', this.val)
    },
  },

  mounted() {},
}
</script>

<style></style>
