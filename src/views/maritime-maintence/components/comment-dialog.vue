<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        供应商评审
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">质量评分</v-col>
              <v-col cols="12" md="9">
                <v-rating
                  v-model="formData.score1"
                  background-color="purple lighten-3"
                  color="purple"
                  length="10"
                  :rules="[(v) => !!v || '质量评分不能为空']"
                ></v-rating>
              </v-col>
              <v-col cols="12" md="3">服务评分</v-col>
              <v-col cols="12" md="9">
                <v-rating
                  v-model="formData.score2"
                  background-color="green lighten-3"
                  color="green"
                  length="10"
                  :rules="[(v) => !!v || '服务评分不能为空']"
                ></v-rating>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  outlined
                  label="评价"
                  v-model="formData.remark"
                  :rules="[(v) => !!v || '评价不能为空']"
                ></v-textarea>
              </v-col>
              <v-card-text>
                <v-attach-list
                  :title="评价附件"
                  :attachments="formData.attachmentRecords"
                  @change="changeAttachment"
                ></v-attach-list>
              </v-card-text>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  {{ isEdit ? '保存' : '评价' }}
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'comment-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      types: {
        'spare-order-detail': '备件订单',
        'soil-order-detail': '滑油订单',
        'materials-order-detail': '物料订单',
        'voyage-repair-detail': '航修修理单',
        'dock-repair-detail': '坞修修理单',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
    orderType() {
      return this.types[this.$route.name]
    },
  },
  methods: {
    changeAttachment(attachmentIds) {
      this.formData.attachmentIds = attachmentIds
    },
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url =
        '/business/shipAffairs/SupplierAssessment/assessmentSaveOrUpdate'
      const { errorRaw } = await this.postAsync(url, {
        ...this.formData,
        orderType: this.orderType ?? '手动录入',
        score3: 0,
        score4: 0,
        score5: 0,
      })
      if (!errorRaw) {
        this.$emit('change', false)
        this.$emit('success')
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
