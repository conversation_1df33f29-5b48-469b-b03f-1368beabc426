<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        询价延期
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <v-col cols="12" md="3">
                <end-time-picker
                  v-model="formData.delayDate"
                  label="延期时间"
                  :rules="[rules.required]"
                ></end-time-picker>
              </v-col>
              <v-col cols="12">
                <v-btn
                  outlined
                  tile
                  color="success"
                  class="mx-1"
                  @click="save"
                  block
                >
                  <v-icon left>mdi-plus-circle</v-icon>
                  延期
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import EndTimePicker from './end-time-picker.vue'

export default {
  name: 'delay-dialog',
  components: { EndTimePicker },
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
    enquiryId: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'purchase',
    },
  },
  data() {
    return {
      dialog: false,
      formData: {},
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      const url =
        this.type === 'purchase'
          ? '/business/shipAffairs/purchaseManage/delayPurchaseEnquiry'
          : this.type === 'dock'
          ? '/dockRepairApply/delayPurchaseEnquiry'
          : this.type === 'voyage'
          ? '/business/shipAffairs/voyageRepair/delayPurchaseEnquiry'
          : '/business/shipAffairs/purchaseManage/delayPurchaseEnquiry'

      try {
        const { errorRaw } = await this.getAsync(url, {
          delayDate: this.formData.delayDate,
          enquiryId: this.enquiryId,
        })
        if (!errorRaw) {
          this.$dialog.message.success('延期成功')
          this.closeForm()
          this.$emit('success')
        } else {
          console.error('接口返回错误:', errorRaw)
        }
      } catch (error) {
        console.error('接口调用失败:', error)
      }
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
