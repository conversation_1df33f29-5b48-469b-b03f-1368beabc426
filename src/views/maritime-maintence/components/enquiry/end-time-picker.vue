<template>
  <v-menu
    ref="endTimeMenu"
    v-model="endTimeMenu"
    :close-on-content-click="false"
    transition="scale-transition"
    offset-y
    min-width="290px"
  >
    <template v-slot:activator="{ on, attrs }">
      <v-text-field
        v-model="formattedEndDateTime"
        :label="label || '截止报价时间'"
        readonly
        v-bind="attrs"
        v-on="on"
        outlined
        dense
        :rules="[rules.required]"
      ></v-text-field>
    </template>
    <v-card>
      <v-date-picker
        v-model="dateValue"
        :min="minDate"
        @change="onDateSelected"
        no-title
        scrollable
      ></v-date-picker>
      <v-divider></v-divider>
      <v-card-text>
        <v-select
          v-model="hourValue"
          :items="hours"
          label="请选择小时"
          outlined
          dense
          hide-details
          @change="onTimeSelected"
        ></v-select>
      </v-card-text>
    </v-card>
  </v-menu>
</template>

<script>
export default {
  name: 'EndTimePicker',
  props: {
    value: String,
    label: String,
  },
  data() {
    return {
      endTimeMenu: false,
      dateValue: '',
      hourValue: '23',
      minDate: new Date().toISOString().substr(0, 10),
      hours: Array.from({ length: 24 }, (_, i) => ({
        text: `${String(i).padStart(2, '0')}:00`,
        value: String(i).padStart(2, '0'),
      })),
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
    }
  },
  computed: {
    formattedEndDateTime() {
      if (!this.dateValue) return ''
      return `${this.dateValue} ${this.hourValue}:00`
    },
  },
  watch: {
    formattedEndDateTime(val) {
      this.$emit('input', val)
    },
    value: {
      immediate: true,
      handler(val) {
        if (val) {
          const [date, time] = val.split(' ')
          this.dateValue = date
          this.hourValue = time.split(':')[0]
        }
      },
    },
  },
  methods: {
    onDateSelected() {
      this.$nextTick(() => {
        this.endTimeMenu = false
      })
    },
    onTimeSelected() {
      this.$nextTick(() => {
        this.endTimeMenu = false
      })
    },
  },
}
</script>

<style scoped>
.v-menu__content {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}
.v-date-picker-table .v-btn {
  color: inherit;
}
</style>
