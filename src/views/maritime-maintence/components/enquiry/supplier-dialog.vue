<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        供应商选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-divider></v-divider>
        <v-table-searchable-pay
          :headers="headers"
          :items="suppliers"
          v-model="selected"
          :single-select="false"
          :req-url="reqUrl"
          table-name=""
          :search-remain="searchObj"
          fuzzy-label="供应商名称/SAP编号"
          outlined
          :filter-func="filterFunc"
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="5">
              <v-select
                label="业务类型"
                v-model="searchObj.businessType"
                dense
                outlined
                :items="[
                  { text: '船员', value: '船员' },
                  { text: '备件', value: '备件' },
                  { text: '物料', value: '物料' },
                  { text: '滑油', value: '滑油' },
                  { text: '化学品', value: '化学品' },
                  { text: '缆绳', value: '缆绳' },
                  { text: '绑扎件', value: '绑扎件' },
                  { text: '油漆', value: '油漆' },
                  { text: '锚、锚链', value: '锚、锚链' },
                  { text: '消防救生检验', value: '消防救生检验' },
                  { text: '海图', value: '海图' },
                  { text: '通导', value: '通导' },
                  { text: '坞修', value: '坞修' },
                  { text: '航修', value: '航修' },
                  { text: '固定资产', value: '固定资产' },
                  { text: '年度协议', value: '年度协议' },
                  { text: '大宗采购', value: '大宗采购' },
                  { text: '大宗采购', value: '大宗采购' },
                  { text: '查看全部（包含未分配业务类型的供应商）', value: '' },
                ]"
                clearable
              ></v-select>
            </v-col>
          </template>
          <template v-slot:[`item.status`]="{ item }">
            {{ ['有效', '暂停整顿', '冻结', '黑名单'][item.status] }}
          </template>
          <template v-slot:[`item.isDockWhite`]="{ item }">
            <v-chip v-if="item.isDockWhite" small color="success">是</v-chip>
            <v-chip v-else small>否</v-chip>
          </template>
          <template v-slot:[`item.annual`]="{ item }">
            <v-chip v-if="item.annual" small color="success">是</v-chip>
            <v-chip v-else small>否</v-chip>
          </template>
        </v-table-searchable-pay>
        <v-divider></v-divider>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="closeForm">取消</v-btn>
        <v-btn
          depressed
          color="primary"
          :disabled="selected.length === 0"
          @click="confirm"
        >
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import vTableSearchablePay from '@/components/v-table-searchablePay.vue'
export default {
  components: { vTableSearchablePay },
  name: 'supplier-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl =
      '/business/shipAffairs/Supplier/getSupplierMessageByShipCodeAndTypeNotDate'
    this.headers = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '年度协议', value: 'annual' },
      { text: '邮箱', value: 'supplierMail' },
      { text: 'sap代码', value: 'sapCode' },
      {
        text: '坞修白名单',
        value: 'isDockWhite',
        hideDefault: !this.dockRepair,
      },
      { text: '质量评分', value: 'supplierAvgScore1' },
      { text: '服务评分', value: 'supplierAvgScore2' },
      { text: '状态', value: 'businessStatus' },
    ]
    this.filterFunc = (items) =>
      items.map((i) => ({
        currencys: i.supplierBankListOutputDTO.map((i) => ({
          currencyType: i.currencyType,
          ccyName: i.ccyCode,
        })),
        selectCurrency: {},
        ...i.supplierPurchaserOutputDTO,
        ...i.supplierOutputDTO,
      }))
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    sitems: Array,
    shipCode: String,
    happenDate: Date,
    businessType: {
      type: String,
    },
    buss: {
      type: [Boolean, String],
      default: false,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      selected: [],
      suppliers: [],
      searchObj: {
        shipCode: '',
        isDockWrite: this.dockRepair,
        businessType: this.businessType,
        happenDate: this.happenDate,
      },
    }
  },
  watch: {
    shipCode: {
      handler(val) {
        this.searchObj.shipCode = val
      },
      immediate: true,
    },
    happenDate: {
      handler(val) {
        this.searchObj.happenDate = val
      },
      immediate: true,
    },
    businessType: {
      handler(val) {
        this.searchObj.businessType = val
      },
      immediate: true,
    },
    open(val) {
      this.dialog = val
      this.$nextTick(() => {
        this.selected = [...this.sitems]
        // this.loadSupplier()
      })
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },
    // 供应商获取
    async loadSupplier() {
      this.loading = true
      const { data } = await this.getAsync(
        '/business/shipAffairs/Supplier/getSupplierMessageByShipCodeAndTypeNotDate',
        {
          shipCode: this.shipCode,
          happenDate: this.happenDate,
        },
      )
      this.suppliers = data.map((i) => {
        const currencys = i.supplierBankListOutputDTO.map((i) => {
          return {
            currencyType: i.currencyType,
            ccyName: i.ccyName,
          }
        })
        return {
          currencys,
          selectCurrency: {},
          ...i.supplierPurchaserOutputDTO,
          ...i.supplierOutputDTO,
        }
      })
      this.loading = false
    },

    selectRow(_, { isSelected, item }) {
      this.selected = isSelected
        ? this.selected.filter((i) => i.id !== item.id)
        : [...this.selected, item]
    },

    confirm() {
      // if (this.selected.length > 3) {
      //   this.$dialog.message.error('最多选择三个供应商!')
      //   return
      // }
      this.$emit('update:sitems', this.selected)
      this.$emit('change', false)
    },
  },
  mounted() {},
  beforeDestroy() {
    this.$emit('change', false)
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
