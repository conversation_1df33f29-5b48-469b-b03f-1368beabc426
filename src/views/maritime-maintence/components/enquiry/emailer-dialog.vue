<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1200"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title>
        邮件抄送人选择
        <v-spacer></v-spacer>
        <v-icon @click="closeForm">mdi-close</v-icon>
      </v-card-title>
      <v-card-text>
        <v-table-searchable
          ref="table"
          table-name=""
          v-model="selected"
          :headers="headers"
          :req-url="reqUrl"
          :fix-header="false"
          :search-remain="searchObj"
          :single-select="false"
          outlined
        >
          <template #searchflieds>
            <v-col cols="12" sm="6" md="3">
              <v-text-field
                label="用户名"
                v-model="searchObj.nickName"
              ></v-text-field>
            </v-col>
          </template>
        </v-table-searchable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn depressed @click="dialog = false">取消</v-btn>
        <v-btn
          depressed
          color="primary"
          :disabled="selected.length === 0"
          @click="confirm"
        >
          确定
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'emailer-dialog',
  model: {
    prop: 'open',
    event: 'change',
  },
  activated() {
    this.dialog = this.open
  },
  created() {
    this.reqUrl = '/system/user/page'
    this.headers = [
      { text: '用户名', value: 'nickName' },
      { text: '部门名称', value: 'deptName' },
      { text: '用户邮箱', value: 'email' },
    ]
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    users: Array,
  },
  data() {
    return {
      dialog: false,
      selected: [],
      searchObj: {
        nickName: '',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$nextTick(() => {
        this.selected = [...this.users]
      })
    },
  },
  computed: {},
  methods: {
    closeForm() {
      this.$emit('change', false)
    },

    confirm() {
      this.$emit('update:users', this.selected)
      this.$emit('change', false)
    },
  },
  mounted() {},
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
