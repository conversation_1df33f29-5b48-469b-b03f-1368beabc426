<template>
  <v-card class="mb-2" outlined>
    <v-card-text class="pb-0 mx-2 mt-1">
      <v-form ref="form">
        <v-row>
          <v-col class="pb-0" cols="12" md="3">
            <vs-date-picker
              outlined
              dense
              label="开始报价时间"
              v-model="formData.startTime"
              use-today
              :rules="[rules.required]"
            ></vs-date-picker>
          </v-col>
          <v-col class="pb-0" cols="12" md="3">
            <end-time-picker
              v-model="formData.endTime"
              label="截止报价时间"
              :rules="[rules.required]"
            ></end-time-picker>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
    <v-card-title class="subtitle-1">
      供应商
      <v-spacer></v-spacer>
      <v-btn
        v-if="businessType != '滑油'"
        :disabled="!shipCode || !happenDate"
        outlined
        tile
        color="success"
        class="mx-1"
        @click.stop="supDialog = true"
        small
      >
        <v-icon left>mdi-plus-circle</v-icon>
        选择供应商
      </v-btn>
    </v-card-title>
    <v-card-text>
      <v-form ref="form2">
        <v-table-list
          :headers="supHeaders"
          :items="suppliers"
          :show-select="false"
        >
          <template v-slot:[`item.isDockWhite`]="{ item }">
            <v-chip v-if="item.isDockWhite" small color="success">是</v-chip>
            <v-chip v-else small>否</v-chip>
          </template>
          <template v-slot:[`item.annual`]="{ item }">
            <v-chip v-if="item.annual" small color="success">是</v-chip>
            <v-chip v-else small>否</v-chip>
          </template>
          <template v-slot:[`item.currencys`]="{ item }">
            <div v-if="item.currencys && item.currencys.length > 1">
              <v-select
                v-model="item.selectCurrency"
                label="币种"
                dense
                :items="item.currencys"
                item-text="ccyName"
                return-object
                :rules="[rules.required]"
                single-line
              ></v-select>
            </div>
            <div v-else-if="item.currencys && item.currencys.length === 1">
              <v-text-field
                v-model="item.currencys[0].ccyName"
                dense
                readonly
              ></v-text-field>
            </div>
          </template>
        </v-table-list>
      </v-form>
    </v-card-text>
    <v-card-title class="subtitle-1">
      邮件抄送人
      <v-spacer></v-spacer>
      <v-btn
        outlined
        tile
        color="success"
        class="mx-1"
        @click="emailDialog = true"
        small
      >
        <v-icon left>mdi-plus-circle</v-icon>
        选择抄送人
      </v-btn>
    </v-card-title>
    <v-card-text>
      <v-table-list
        :headers="emailHeaders"
        :items="emailers"
        :show-select="false"
      ></v-table-list>
    </v-card-text>
    <!-- <v-card-actions>
      <v-btn
        :loading="loading"
        outlined
        tile
        color="success"
        @click="save"
        block
      >
        <v-icon left>mdi-check</v-icon>
        确认
      </v-btn>
    </v-card-actions> -->
    <supplier-dialog
      v-model="supDialog"
      :shipCode="shipCode"
      :happenDate="happenDate"
      :sitems.sync="suppliers"
      :dock-repair="dockRepair"
      :businessType="businessType"
    ></supplier-dialog>
    <emailer-dialog
      v-model="emailDialog"
      :users.sync="emailers"
    ></emailer-dialog>
  </v-card>
</template>
<script>
import EmailerDialog from './emailer-dialog.vue'
import supplierDialog from './supplier-dialog.vue'
import EndTimePicker from './end-time-picker.vue'

export default {
  components: { supplierDialog, EmailerDialog, EndTimePicker },
  name: 'enquiry-form',
  created() {
    this.supHeaders = [
      { text: '供应商名称', value: 'name' },
      { text: '英文名称', value: 'nameEn' },
      { text: '邮箱', value: 'supplierMail' },
      { text: 'sap代码', value: 'sapCode' },
      { text: '年度协议', value: 'annual' },
      {
        text: '坞修白名单',
        value: 'isDockWhite',
        hideDefault: !this.dockRepair,
      },
      { text: '质量评分', value: 'supplierAvgScore1' },
      { text: '服务评分', value: 'supplierAvgScore2' },
      { text: '币种', value: 'currencys', width: 130 },
    ]
    this.emailHeaders = [
      { text: '用户名', value: 'nickName' },
      { text: '部门名称', value: 'deptName' },
      { text: '用户邮箱', value: 'email' },
    ]
  },

  props: {
    shipCode: {
      type: String,
    },
    happenDate: {
      type: Date,
      default: null,
    },
    businessType: {
      type: String,
    },
    reqUrl: {
      type: String,
      default: '/business/shipAffairs/purchaseManage/purchaseEnquirySubmit',
    },
    dockRepair: {
      type: [Boolean, String],
    },
  },
  data() {
    return {
      minDate: new Date().toISOString().substr(0, 10),
      suppliers: [],
      emailers: [
        {
          id: this.$local.data.get('userInfo').id,
          nickName: this.$local.data.get('userInfo').nickName,
          deptName: this.$local.data.get('userInfo').deptName,
          email: this.$local.data.get('userInfo').email,
        },
      ],
      supDialog: false,
      emailDialog: false,
      formData: {
        startTime: '',
        endTime: '',
      },
      rules: {
        required: (v) => !!v || '必填项不能为空',
      },
      loading: false,
    }
  },

  computed: {
    formattedEndDateTime() {
      if (!this.formData.endDate) return ''
      return `${this.formData.endDate} ${this.selectedHour}:00`
    },
  },

  methods: {
    allowedMinutes: () => [0],

    // 选择小时后自动关闭选择器
    onTimeSelected() {
      this.$nextTick(() => {
        this.endTimeMenu = false
      })
    },
    async save() {
      if (!this.$refs.form.validate() || !this.$refs.form2.validate()) {
        return
      }
      if (this.suppliers.length === 0) {
        this.$dialog.message.error('请至少选择一个供应商!')
        return
      }
      let quotes = []
      for (const s of this.suppliers) {
        const currencyId =
          s.currencys.length === 1
            ? s.currencys[0].currencyType
            : s?.selectCurrency?.currencyType
        if (!currencyId) {
          this.$dialog.message.error('存在多币种供应商,请选择币种!')
          return
        }
        quotes.push({ supplierId: s.id, currencyId })
      }
      const mailReceiverIds = this.emailers.map((i) => i.email)
      this.loading = true
      // const { errorRaw } = await this.postAsync(
      //   this.reqUrl,
      //   {
      //     ...this.formData,
      //     quotes,
      //     enquiryId: this.enquiry.id,
      //     mailReceiverIds,
      //   },
      //   false,
      // )
      return {
        ...this.formData,
        quotes,
        // enquiryId: this.enquiry.id,
        mailReceiverIds,
      }
      /*
      this.loading = false
      if (!errorRaw) {
        this.$dialog.message.success('已发起询价')
        this.$emit('success')
        this.close()
        return
      }
      if (errorRaw.msg.includes('发送简单邮件时发生异常')) {
        this.$dialog.message.error('已发起询价并生成报价单,但邮件发送异常')
        this.$emit('success')
        this.loading = false

        this.close()
      } else {
        this.$dialog.message.error(errorRaw.msg)
        this.loading = false
      }
      */
    },
    close() {
      this.suppliers = []
      this.emailers = [
        {
          id: this.$local.data.get('userInfo').id,
          nickName: this.$local.data.get('userInfo').nickName,
          deptName: this.$local.data.get('userInfo').deptName,
          email: this.$local.data.get('userInfo').email,
        },
      ]
      this.formData = {}
      this.$refs.form.resetValidation()
      this.$emit('close')
    },
  },

  mounted() {},
}
</script>

<style scoped>
.v-menu__content {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}

.v-time-picker-clock {
  background-color: #f5f5f5 !important;
}
</style>
