<template>
  <v-dialog
    attach="#mask"
    hide-overlay
    width="1000"
    persistent
    v-model="dialog"
  >
    <v-card>
      <v-card-title v-if="this.initialData.purchaseOrderStatus == 1000">
        订单报价
        <v-spacer></v-spacer>
        <span style="color: red">
          提示：请确认明细订购数量、单价、运费、供货天数、港口等信息
        </span>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          提交并确认报价
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-title v-if="this.initialData.purchaseOrderStatus == 1002">
        订单发货
        <v-spacer></v-spacer>
        <span style="color: red">
          提示：请确认明细订购数量、单价、运费、供货天数、港口等信息
        </span>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          发货
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-title v-if="this.initialData.purchaseOrderStatus == 998">
        订单退回
        <v-spacer></v-spacer>
        <span style="color: red">提示：请仔细核对订单并填写退回原因等信息</span>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          退回
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-title v-if="this.initialData.purchaseOrderStatus == 997">
        订单退回
        <v-spacer></v-spacer>
        <span style="color: red">提示：请仔细核对订单并填写退回原因等信息</span>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          退回
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-title
        v-if="
          this.initialData.purchaseOrderStatus == 100111 ||
          this.initialData.purchaseOrderStatus == 1001
        "
      >
        订单确认
        <v-spacer></v-spacer>
        <v-btn outlined tile color="success" class="mx-1" @click="save" small>
          <v-icon left>mdi-plus-circle</v-icon>
          确认
        </v-btn>
        <v-btn small outlined tile class="mx-1" @click="closeForm">
          <v-icon>mdi-close</v-icon>
          关闭
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form">
          <v-container>
            <v-row>
              <!-- <v-col cols="12" md="4">
                <port-select-dialog
                  v-model="formData.portId"
                  :rules="[rules.required]"
                  :initSelected="formData.initPort"
                ></port-select-dialog>
              </v-col>
              <v-col cols="12" md="4">
                <vs-date-picker
                  label="发货日期"
                  v-model="formData.deliveryDate"
                  :rules="[rules.required]"
                  dense
                  outlined
                ></vs-date-picker>
              </v-col> -->
              <v-col
                v-if="
                  greaseTypeList &&
                  this.initialData.purchaseOrderStatus == 100111
                "
                class="py-0"
                md="12"
                cols="12"
              >
                <v-select
                  v-model="formData.subjectIdYf"
                  outlined
                  label="运费分摊"
                  dense
                  :items="greaseTypeList"
                  :rules="[rules.required]"
                  clearable
                ></v-select>
              </v-col>
              <v-col
                class="py-0"
                md="12"
                cols="12"
                v-if="this.initialData.purchaseOrderStatus == 1002"
              >
                <!-- {{ this.initialData.jwReamrk }} -->
                <v-textarea
                  outlined
                  dense
                  v-model="formData.jwReamrk"
                  label="公司备注"
                  disabled
                  readonly
                ></v-textarea>
              </v-col>
              <v-col class="py-0" md="12" cols="12">
                <v-textarea
                  outlined
                  dense
                  placeholder="填写发货信息:港口、发货日期..."
                  v-model="formData.remark"
                  label="备注"
                  :rules="[rules.required]"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
// import portSelectDialog from './port-select-dialog.vue'
export default {
  // components: { portSelectDialog },
  name: 'reduce-in-dialog',
  activated() {
    this.dialog = this.open
  },
  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
    status: {
      type: Number,
    },
    // 选择滑油运费分摊到哪个科目
    greaseTypeList: {
      type: Array,
    },
  },
  data() {
    return {
      dialog: false,
      open: false,
      formData: {},
      resolveFn: null,
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
      },
    }
  },
  watch: {
    open(val) {
      this.dialog = val
      this.$refs?.form?.resetValidation()
      this.formData = this.initialData
      this.greaseTypeList.forEach((item) => {
        item.text = item.category
        item.value = item.costSubjectId
      })
    },
  },
  computed: {
    isEdit() {
      return this.initialData?.id
    },
  },
  methods: {
    closeForm() {
      this.open = false
      this.resolveFn(false)
    },

    async save() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.open = false
      this.resolveFn(this.formData)
    },

    async confirm() {
      this.open = true
      return new Promise((resolve) => {
        this.resolveFn = resolve
      })
    },
  },
}
</script>

<style scoped>
.v-dialog__content {
  position: absolute !important;
}
</style>
