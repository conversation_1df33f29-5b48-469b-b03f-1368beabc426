<template>
  <v-app>
    <v-main>
      <!--Hey! This is the original version
of Simple CSS Waves-->

      <v-flex class="header">
        <!--Content before waves-->
        <v-flex class="inner-header">
          <v-row>
            <v-col class="py-7 my-10">
              <div class="text-h2">海 丰 国 际 | SITC</div>
              <div class="text-h2">入职注册</div>
            </v-col>
          </v-row>
          <v-row>
            <v-col md="12">
              <v-card
                color="primary"
                width="400"
                dark
                class="mx-auto mt-10 px-3 py-3"
              >
                <v-form ref="form">
                  <v-text-field
                    label="注册姓名"
                    v-model="personInfo.creName"
                    filled
                    :rules="[rules.required]"
                  ></v-text-field>
                  <v-text-field
                    label="身份证号"
                    v-model="personInfo.idNumber"
                    filled
                    :rules="[rules.idcard]"
                  ></v-text-field>
                  <v-select
                    label="入职途径"
                    v-model="personInfo.type"
                    :items="['校招', '社招']"
                    filled
                    :rules="[rules.required]"
                  ></v-select>
                  <v-card-actions class="px-0">
                    <v-btn
                      color="teal darken-2"
                      depressed
                      block
                      large
                      :loading="loading"
                      @click="submit"
                    >
                      注册
                    </v-btn>
                  </v-card-actions>
                </v-form>
              </v-card>
            </v-col>
          </v-row>
        </v-flex>

        <!--Waves Container-->

        <!--Waves end-->
      </v-flex>
      <!--Header ends-->

      <!--Content starts-->

      <!--Content ends-->
    </v-main>
  </v-app>
</template>
<script>
export default {
  name: 'registerPerson',
  data() {
    return {
      rules: {
        required: (v) => !!v || v === 0 || '必填项不能为空',
        number: (v) => /^(-|\+)?\d+(\.\d+)?$/.test(v) || '请输入数字',
        idcard: (v) => /^\d{17}(\d|x|X)$/.test(v) || '请正确输入身份证号',
      },
      loading: false,
      personInfo: {},
    }
  },

  methods: {
    async submit() {
      if (!this.$refs.form.validate()) {
        return
      }
      this.loading = true
      const { errorRaw } = await this.postAsync(
        `/business/crew/registration/register`,
        this.personInfo,
      )
      if (errorRaw) {
        return
      }
      this.$dialog.message.success(`注册成功`)
      this.personInfo = {}
      this.loading = false
    },
  },

  mounted() {},
}
</script>

<style>
/* 去除Google游览器自动填充账号出现的背景 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
}
input:-webkit-autofill {
  -webkit-text-fill-color: #fff;
}
</style>
<style scoped>
.header {
  position: relative;
  text-align: center;
  background: linear-gradient(
    60deg,
    rgba(84, 58, 183, 1) 0%,
    rgba(0, 172, 193, 1) 100%
  );
  color: white;
}

.inner-header {
  height: 75vh;
  width: 100%;
  margin: 0;
  padding: 0;
}

.waves {
  position: relative;
  width: 100%;
  height: 10vh;
  margin-bottom: -7px; /*Fix for safari gap*/
  min-height: 100px;
  max-height: 150px;
}

/* Animation */

.parallax > use {
  animation: move-forever 25s cubic-bezier(0.55, 0.5, 0.45, 0.5) infinite;
}
.parallax > use:nth-child(1) {
  animation-delay: -2s;
  animation-duration: 7s;
}
.parallax > use:nth-child(2) {
  animation-delay: -3s;
  animation-duration: 10s;
}
.parallax > use:nth-child(3) {
  animation-delay: -4s;
  animation-duration: 13s;
}
.parallax > use:nth-child(4) {
  animation-delay: -5s;
  animation-duration: 20s;
}
@keyframes move-forever {
  0% {
    transform: translate3d(-90px, 0, 0);
  }
  100% {
    transform: translate3d(85px, 0, 0);
  }
}
/*Shrinking for mobile*/
@media (max-width: 768px) {
  .waves {
    height: 40px;
    min-height: 40px;
  }
  .content {
    height: 20vh;
  }
}
</style>
