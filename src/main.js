// Third party modules imports
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import vuetify from './plugins/vuetify'
import '@babel/polyfill'
import VuetifyProDialog from 'vuetify-pro-dialog'
import VCalendar from 'v-calendar'
import VNumeric from 'vuetify-numeric/vuetify-numeric.umd'
import ECharts from 'vue-echarts' // 在 webpack 环境下指向 components/ECharts.vue
import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/pie'
import 'echarts/lib/chart/line'
import 'echarts/lib/component/title'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/toolbox'
import 'echarts/lib/component/markLine'
import 'echarts/lib/component/graphic'
import 'echarts/lib/component/legend'

// Project modules imports
import Net from './util/net'
import ComponentsHelper from './util/ComponentsHelper'
import localStorageHepler from './util/localStorageHepler'
import tools from './util/tools'
import KeepAlive from './util/keepAlive4Dev'
import directives from './directives'

// Style sheets
import 'roboto-fontface/css/roboto/roboto-fontface.css'
import '@mdi/font/css/materialdesignicons.css'
import './styles/common.css'
import './styles/print.css'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'
import vnTextField from './components/vn-text-field'
// import ElementUI from 'element-ui'
// import 'element-ui/lib/theme-chalk/index.css'

import maskControl from './mixin/maskControl'
import draggableDialog from './mixin/draggableDialog'

// Third party modules
Vue.use(VuetifyProDialog, { vuetify })
Vue.use(VCalendar, {
  componentPrefix: 'vc', // Use <vc-calendar /> instead of <v-calendar />
})
Vue.use(require('vue-moment'))
Vue.use(VNumeric)

// Project modules
Vue.use(Net)
// Vue.use(ElementUI)
Vue.use(ComponentsHelper)
Vue.use(directives)
Vue.use(localStorageHepler)
Vue.use(tools)
Vue.component('vnTextField', vnTextField)
Vue.component('v-chart', ECharts)
// process.env.NODE_ENV === 'development' && Vue.component('KeepAlive', KeepAlive) // keep-alive problems in dev
Vue.component('KeepAlive', KeepAlive)

Vue.mixin(draggableDialog)

Vue.mixin(maskControl)

Vue.config.productionTip = false

new Vue({
  router,
  store,
  vuetify,
  render: (h) => h(App),
}).$mount('#app')
