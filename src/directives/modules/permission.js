export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    const roles = vnode.context.$local.data.get('userInfo').resources

    if (value && value instanceof Array && value.length > 0) {
      const permissionRoles = value

      const hasPermission = roles?.some((role) => {
        return permissionRoles.includes(role) || roles.includes('ROLE_ADMIN')
      })

      if (!hasPermission) {
        if (vnode.tag.includes('v-detail-view')) {
          vnode.componentInstance.hideBtn = true
        } else {
          el.parentNode && el.parentNode.removeChild(el)
        }
      }
    } else {
      throw new Error(`need roles! Like v-permission="['admin','editor']"`)
    }
  },
}
