<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  created() {
    console.log('app created')
    this._injectToLogin(this.gotoLogin)
    this._injectToToast(this.$dialog.message)
  },
  methods: {
    gotoLogin() {
      localStorage.clear()
      this.$dialog.notify.error('登录失效，请重新登录')
      this.$router.push('/login')
    },
  },
}
</script>

<style>
#app {
  height: 100%;
}
</style>
