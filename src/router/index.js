import store from '@/store'
import Vue from 'vue'
import VueRouter from 'vue-router'
import { flattenRoutes } from './flattenUrl'

Vue.use(VueRouter)

const scrollableElementId = 'main-content'

const routes = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/layout-view'),
    children: [
      {
        path: '/home',
        name: 'HomeView',
        component: () => import('../views/HomeView.vue'),
        meta: {
          title: '首页',
        },
      },
      {
        path: '/user-index',
        name: 'UserIndex',
        component: () => import('../views/UserIndex.vue'),
        meta: {
          title: '用户信息',
        },
      },
      ...flattenRoutes,
    ],
  },
  {
    path: '/login',
    name: 'LoginView',
    component: () => import('@/views/login2'),
  },
  {
    path: '/about',
    name: 'about',
    // 项目自带路由，测试项目启动时用
    component: () =>
      import(/* webpackChunkName: "about" */ '../views/AboutView.vue'),
  },
  {
    path: '/entry',
    name: 'registerPerson',
    component: () => import('@/views/registerPerson.vue'),
  },
]

const router = new VueRouter({
  mode: 'history',
  routes,
  scrollBehavior(to) {
    let beforeRoute = store.state.viewTags.viewTags.filter(
      (item) => item.path === to.path,
    )[0]
    return { y: beforeRoute?.scrollTop || 0 }
  },
})

router.beforeEach((to, from, next) => {
  const scrollTop = document.getElementById(scrollableElementId)
  if (scrollTop) {
    store.commit('updateViewTags', {
      path: from.path,
      scrollTop:
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop ||
        0,
    })
  }
  if (to.path === '/login') {
    next()
  } else if (to.path === '/entry') {
    next()
  } else {
    const token = sessionStorage[process.env.VUE_APP_TOKEN]
    if (!token) {
      next('/login')
    } else {
      const userInfo = localStorage.getItem('userInfo')
      if (to.path === '/home') {
        next()
      } else {
        const resources = JSON.parse(userInfo)?.resources
        if (
          !to.meta?.access ||
          resources?.includes(to.meta.access) ||
          resources?.includes('ROLE_ADMIN')
        ) {
          next()
        } else {
          next('/home')
        }
      }
      // next()
    }
  }
})

export default router
