// 导入sysRoutes目录下所有router并合成为一个对象
const files = require.context('./sysRoutes', false, /\.js$/)
const routes = {}
files.keys().forEach((key) => {
  routes[key.replace(/(\.\/|\.js)/g, '')] = files(key).default
})

// 将三级路由拍扁为一级,并将一级路由的path添加在前
function flatRoutes(routes) {
  let res = []
  for (const key in routes) {
    const baseUrl = routes[key].path
    for (const r of routes[key]?.children) {
      for (const n of r?.children) {
        res.push({ ...n, path: baseUrl + n.path })
      }
    }
  }
  return res
}

let flattenRoutes = flatRoutes(routes)

export { flattenRoutes, routes }
