export default {
  path: '/purchase-business',
  title: '采购管理',
  icon: 'mdi-purse',
  access: '采购管理',

  children: [
    {
      title: '供应商管理',
      icon: 'mdi-account-group',
      group: '/supplier',
      children: [
        {
          path: '/supplier/list',
          name: 'supplier-main-list',
          component: () =>
            import('@/views/common-business/supplier/supplier-main-list'),
          meta: {
            title: '供应商列表',
            access: '供应商列表:列表',
          },
        },
        {
          path: '/supplier/detail/:id',
          name: 'supplier-detail',
          component: () =>
            import('@/views/common-business/supplier/supplier-detail'),
          meta: {
            title: '供应商详情',
            access: '供应商列表:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/purchase-business/supplier-in-list',
          name: 'supplier-in-list',
          component: () => import('@/views/purchase-business/supplier-in-list'),
          meta: {
            title: '供应商准入/退出/评级',
            access: '供应商准入:列表',
          },
        },
        {
          path: '/purchase-business/supplier-in-detail/:id',
          name: 'supplier-in-detail',
          component: () =>
            import('@/views/purchase-business/supplier-in-detail'),
          meta: {
            title: '供应商准入/退出/评级详情',
            access: '供应商准入:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/supplier/supplier-assess/list',
          name: 'supplier-assess-list',
          component: () =>
            import(
              '@/views/purchase-business/supplier-assess/supplier-assess-list'
            ),
          meta: {
            title: '供应商评审',
            access: '供应商评审:列表',
          },
        },
        {
          path: '/supplier/supplier-assess/supplier-assess-detail/:id',
          name: 'supplier-assess-detail',
          component: () =>
            import(
              '@/views/purchase-business/supplier-assess/supplier-assess-detail'
            ),
          meta: {
            title: '供应商评审详情',
            access: '供应商评审:列表',
            notShowInNav: true,
          },
        },
        // {
        //   path: '/supplier/supplier-assess/supplier-assess-detail-item/:id',
        //   name: 'supplier-assess-detail-item',
        //   component: () =>
        //     import(
        //       '@/views/purchase-business/supplier-assess/private/supplier-assess-detail-item'
        //     ),
        //   meta: {
        //     title: '供应商评审子表详情',
        //     access: '供应商评审:列表',
        //     notShowInNav: true,
        //   },
        // },
      ],
    },
    {
      title: '船舶物资采购',
      icon: 'mdi-ferry',
      group: /spare-parts|repair-manage/,
      children: [
        {
          path: '/spare-parts/enquiry-list',
          name: 'spare-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/enquiry/spare-enquiry-list'
            ),
          meta: {
            title: '备件询价',
            access: '备件询价',
          },
        },
        {
          path: '/spare-parts/enquiry-detail/:id',
          name: 'spare-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/enquiry/spare-enquiry-detail'
            ),
          meta: {
            title: '备件询价详情',
            access: '备件询价',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/soil-enquiry/list',
          name: 'soil-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/enquiry/soil-enquiry-list'
            ),
          meta: {
            title: '滑油询价',
            access: '滑油询价',
          },
        },
        {
          path: '/lub-oil/soil-enquiry/detail/:id',
          name: 'soil-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/enquiry/soil-enquiry-detail'
            ),
          meta: {
            title: '滑油询价-详情',
            access: '滑油询价',
            notShowInNav: true,
          },
        },
        // {
        //   path: '/lub-oil/union-price-list',
        //   name: 'soil-union-price-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/slide-oil/union-price/union-price-list'
        //     ),
        //   meta: {
        //     title: '滑油批量报价',
        //     access: '滑油批量报价',
        //   },
        // },
        {
          path: '/materials/materials-enquiry/list',
          name: 'materials-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/enquiry/materials-enquiry-list'
            ),
          meta: {
            title: '物料询价',
            access: '物料询价',
          },
        },
        {
          path: '/materials/materials-enquiry/detail/:id',
          name: 'materials-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/enquiry/materials-enquiry-detail'
            ),
          meta: {
            title: '物料询价-详情',
            notShowInNav: true,
            access: '物料询价',
          },
        },
        // {
        //   path: '/materials/materials-union-price/list',
        //   name: 'materials-union-price-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
        //     ),
        //   meta: {
        //     title: '物料批量报价',
        //     access: '物料批量报价',
        //   },
        // },
        // {
        //   path: '/materials/materials-union-price/new',
        //   name: 'materials-union-price-detail',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/materials/union-price/materials-union-price-detail'
        //     ),
        //   meta: {
        //     title: '物料批量报价',
        //     notShowInNav: true,
        //     access: '物料批量报价',
        //   },
        // },
        {
          path: '/repair-manage/voyage-enquiry-list',
          name: 'voyage-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/enquiry/voyage-enquiry-list'
            ),
          meta: {
            title: '航修询价',
            access: '航修询价',
          },
        },
        {
          path: '/repair-manage/voyage-enquiry-detail/:id',
          name: 'voyage-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/enquiry/voyage-enquiry-detail'
            ),
          meta: {
            title: '航修询价详情',
            notShowInNav: true,
            access: '航修询价',
          },
        },
      ],
    },
    {
      title: '专项集采',
      icon: 'mdi-dharmachakra',
      group: /special|component-batch-purchase/,
      children: [
        {
          path: 'special/purchase/component-batch-purchase-list',
          name: 'component-batch-purchase-list',
          component: () =>
            import(
              '@/views/purchase-business/component-batch-purchase/component-batch-purchase-list'
            ),
          meta: {
            title: '备件专项集采',
            access: '备件专项集采:列表',
            type: '机务',
          },
        },
        {
          path: '/special/purchase/component-batch-purchase-detail/:id',
          name: 'component-batch-purchase-detail',
          component: () =>
            import(
              '@/views/purchase-business/component-batch-purchase/component-batch-purchase-detail'
            ),
          meta: {
            title: '备件专项集采详情',
            access: '备件专项集采:列表',
            notShowInNav: true,
            // type: '机务',
          },
        },
        {
          path: 'special/purchase/component-batch-purchase-listTD',
          name: 'component-batch-purchase-listTD',
          component: () =>
            import(
              '@/views/purchase-business/component-batch-purchase/component-batch-purchase-listTD'
            ),
          meta: {
            title: '通导专项集采',
            access: '通导专项集采:列表',
            type: '通导',
          },
        },
        // {
        //   path: '/special/purchase/component-batch-purchase-detail/:id',
        //   name: 'component-batch-purchase-detail',
        //   component: () =>
        //     import(
        //       '@/views/purchase-business/component-batch-purchase/component-batch-purchase-detail'
        //     ),
        //   meta: {
        //     title: '通导专项集采详情',
        //     access: '通导专项集采:列表',
        //     notShowInNav: true,
        //     type: '通导',
        //   },
        // },
      ],
    },
    {
      title: '物料集采',
      icon: 'mdi-format-paint',
      group: '/procurement',
      children: [
        // {
        //   path: '/materials/materials-union-price/list',
        //   name: 'materials-union-price-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
        //     ),
        //   meta: {
        //     title: '物料协议价',
        //     access: '物料协议价',
        //   },
        // },
        // {
        //   path: '/materials/materials-union-price/new',
        //   name: 'materials-union-price-detail',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/materials/union-price/materials-union-price-detail'
        //     ),
        //   meta: {
        //     title: '物料协议价',
        //     notShowInNav: true,
        //     access: '物料协议价',
        //   },
        // },
        {
          path: '/procurement/procurement-union-price/list05',
          name: 'procurement-union-price-list05',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
            ),
          meta: {
            title: '季度物料协议价',
            access: '季度物料协议价',
            subjectId: '其他', //其他
          },
        },
        {
          path: '/procurement/purchase/material-batch-purchase-list',
          name: 'material-batch-purchase-list',
          component: () =>
            import(
              '@/views/purchase-business/material-batch-purchase/material-batch-purchase-list'
            ),
          meta: {
            title: '物料专项集采',
            access: '物料专项集采:列表',
          },
        },
        {
          path: '/procurement/purchase/material-batch-purchase-detail/:id',
          name: 'material-batch-purchase-detail',
          component: () =>
            import(
              '@/views/purchase-business/material-batch-purchase/material-batch-purchase-detail'
            ),
          meta: {
            title: '物料专项集采详情',
            access: '物料专项集采:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '大宗集采',
      icon: 'mdi-water-sync',
      group: /lub-oil|materials|beijian/,
      children: [
        {
          path: '/lub-oil/union-price-list',
          name: 'soil-union-price-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/union-price/union-price-list'
            ),
          meta: {
            title: '滑油协议价',
            access: '滑油协议价',
          },
        },
        {
          path: '/lub-oil/union-price-new',
          name: 'soil-union-price-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/union-price/union-price-detail'
            ),
          meta: {
            title: '滑油协议价-详情',
            access: '滑油协议价',
            notShowInNav: true,
          },
        },
        {
          path: '/materials/materials-union-price/list01',
          name: 'materials-union-price-list01',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
            ),
          meta: {
            title: '油漆协议价',
            access: '油漆协议价',
            subjectId: '1581991857076989953', //油漆费
          },
        },
        {
          path: '/materials/materials-union-price/list02',
          name: 'materials-union-price-list02',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
            ),
          meta: {
            title: '化学品协议价',
            access: '化学品协议价', //后台权限相关
            subjectId: '1581991857064407043', //化学品
          },
        },
        {
          path: '/materials/materials-union-price/list03',
          name: 'materials-union-price-list03',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
            ),
          meta: {
            title: '缆绳协议价',
            access: '缆绳协议价',
            subjectId: '1581991856967938050', //缆绳
          },
        },
        {
          path: '/materials/materials-union-price/list04',
          name: 'materials-union-price-list04',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
            ),
          meta: {
            title: '绑扎件协议价',
            access: '绑扎件协议价',
            subjectId: '1581991857072795650', //绑扎件
          },
        },
        {
          path: '/beijian/union-price-list',
          name: 'union-price-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/union-price/union-price-list'
            ),
          meta: {
            title: '锚和锚链协议价',
            access: '锚和锚链协议价',
          },
        },
        {
          path: '/beijian/union-price-new',
          name: 'union-price-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/union-price/union-price-detail'
            ),
          meta: {
            title: '锚和锚链协议价',
            notShowInNav: true,
            access: '锚和锚链协议价',
          },
        },
        // {
        //   path: '/materials/materials-union-price/list05',
        //   name: 'materials-union-price-list05',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
        //     ),
        //   meta: {
        //     title: '其他物料协议价',
        //     access: '物料协议价',
        //     subjectId: '其他', //其他
        //   },
        // },
        {
          path: '/materials/materials-union-price/new',
          name: 'materials-union-price-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/union-price/materials-union-price-detail'
            ),
          meta: {
            title: '物料协议价',
            notShowInNav: true,
            access: '物料协议价',
          },
        },
        // {
        //   path: '/spare-parts/union-price-list',
        //   name: 'union-price-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/spare-part/union-price/union-price-list'
        //     ),
        //   meta: {
        //     title: '备件批量报价',
        //     access: '备件批量报价',
        //   },
        // },
        // {
        //   path: '/spare-parts/union-price-new',
        //   name: 'union-price-detail',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/spare-part/union-price/union-price-detail'
        //     ),
        //   meta: {
        //     title: '备件批量报价-详情',
        //     access: '备件批量报价',
        //     notShowInNav: true,
        //   },
        // },
        // group: /spare-parts|lub-oil|materials|repair-manage|special/,
      ],
    },
  ],
}
