export default {
  path: '/ism-management',
  title: '体系运行',
  icon: 'mdi-information-variant',
  access: 'ISM办',
  children: [
    {
      title: '船舶证书审核',
      icon: 'mdi-certificate-outline',
      group: '/ship-audit',
      children: [
        {
          path: '/ship-audit/inner/list',
          name: 'ship-audit-inner-list',
          component: () =>
            import('@/views/ism-management/ship-audit/ship-audit-inner-list'),
          meta: {
            title: '船舶内审',
            access: '船舶内审:列表',
          },
        },
        {
          path: '/ship-audit/inner/create',
          name: 'ship-audit-inner-create',
          component: () =>
            import('@/views/ism-management/ship-audit/ship-audit-inner-create'),
          meta: {
            title: '船舶内审-新增',
            access: '船舶内审:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/ship-audit/inner/:id',
          name: 'ship-audit-inner-detail',
          component: () =>
            import('@/views/ism-management/ship-audit/ship-audit-inner-detail'),
          meta: {
            title: '船舶内审-详情',
            access: '船舶内审:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-audit/outter/list',
          name: 'ship-audit-outter-list',
          component: () =>
            import('@/views/ism-management/ship-audit/ship-audit-outter-list'),
          meta: {
            title: '船舶外审',
            access: '船舶外审:列表',
          },
        },
        {
          path: '/ship-audit/outter/create',
          name: 'ship-audit-outter-create',
          component: () =>
            import(
              '@/views/ism-management/ship-audit/ship-audit-outter-create'
            ),
          meta: {
            title: '船舶外审-新增',
            access: '船舶外审:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-audit/outter/:id',
          name: 'ship-audit-outter-detail',
          component: () =>
            import(
              '@/views/ism-management/ship-audit/ship-audit-outter-detail'
            ),
          meta: {
            title: '船舶外审-详情',
            access: '船舶外审:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-audit/ssp/list',
          name: 'ship-ssp-list',
          component: () => import('@/views/ism-management/ship-audit/ship-SSP'),
          meta: {
            title: 'SSP',
            access: 'SSP:列表',
          },
        },
        {
          path: '/ship-audit/dmlc-part1/list',
          name: 'ship-dmlc1-list',
          component: () =>
            import('@/views/ism-management/ship-audit/ship-DMLC1'),
          meta: {
            title: 'DMLC PARTⅠ',
            access: 'DMLC PARTⅠ:列表',
          },
        },
        {
          path: '/ship-audit/dmlc-part2/list',
          name: 'ship-dmlc2-list',
          component: () =>
            import('@/views/ism-management/ship-audit/ship-DMLC2'),
          meta: {
            title: 'DMLC PARTⅡ',
            access: 'DMLC PARTⅡ:列表',
          },
        },
        {
          path: '/ship-audit/csr/list',
          name: 'ship-csr-list',
          component: () => import('@/views/ism-management/ship-audit/ship-CSR'),
          meta: {
            title: 'CSR',
            access: 'CSR:列表',
          },
        },
      ],
    },
    {
      title: '船舶报表',
      icon: 'mdi-file-pdf-box',
      group: '/ship-report',
      children: [
        {
          path: '/ship-report/tempelate/list',
          name: 'report-template-list',
          component: () =>
            import('@/views/ism-management/report/report-template-list'),
          meta: {
            title: '报表模板库',
            access: '报表模板库:列表',
          },
        },
        {
          path: '/ship-report/tempelate/:id',
          name: 'report-template-detail',
          component: () =>
            import('@/views/ism-management/report/report-template-detail'),
          meta: {
            title: '报表库详情',
            access: '报表模板库:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-report/emit/list',
          name: 'report-emit-list',
          component: () =>
            import('@/views/ism-management/report/report-emit-list'),
          meta: {
            title: '报表发起中心',
            access: '报表发起中心:列表',
          },
        },
        {
          path: '/ship-report/emit/:id',
          name: 'report-emit-detail',
          component: () =>
            import('@/views/ism-management/report/report-emit-detail'),
          meta: {
            title: '报表发起',
            access: '报表发起中心:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-report/dept/list',
          name: 'dept-report-list',
          component: () =>
            import('@/views/ism-management/report/dept-report-list'),
          meta: {
            title: '部门报表',
            access: '部门报表:列表',
          },
        },
        {
          path: '/ship-report/dept/pending',
          name: 'dept-report-pending',
          component: () =>
            import('@/views/ism-management/report/dept-report-pending'),
          meta: {
            title: '待审批报表',
            access: '待审批报表:列表',
          },
        },
        {
          path: '/ship-report/dept/history',
          name: 'dept-report-history',
          component: () =>
            import('@/views/ism-management/report/dept-report-history'),
          meta: {
            title: '报表记录库',
            access: '报表记录库:列表',
          },
        },
        //详情界面
        {
          path: '/ship-report/dept/:id',
          name: 'dept-report-detail',
          component: () =>
            import('@/views/ism-management/report/dept-report-detail'),
          meta: {
            title: '部门报表',
            access: '部门报表:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-report/dept_info/:id',
          name: 'dept-report-info-detail',
          component: () =>
            import('@/views/ism-management/report/dept-report-info-detail'),
          meta: {
            title: '部门报表info',
            access: '部门报表info',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-report/dept_info1/:id',
          name: 'dept-report-info-detail1',
          component: () =>
            import('@/views/ism-management/report/dept-report-info-detail1'),
          meta: {
            title: '不符合报告NEW',
            access: '不符合报告NEW:编辑',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船舶能效管理',
      icon: 'mdi-battery-charging-100',
      group: '/ship-energy',
      children: [
        {
          path: '/ship-energy/quarterbag',
          name: 'ship-quarterbag',
          component: () =>
            import('@/views/maritime-affairs/ship-energy/ship-quarterbag'),
          meta: {
            title: '每季度数据包',
            access: '每季度数据包:列表',
          },
        },
        {
          path: '/ship-energy/halfyeartable',
          name: 'ship-halfyeartable',
          component: () =>
            import('@/views/maritime-affairs/ship-energy/ship-halfyeartable'),
          meta: {
            title: '每半年措施报表收集',
            access: '每半年措施报表收集:列表',
          },
        },
        {
          path: '/ship-energy/peryeardata',
          name: 'ship-peryeardata',
          component: () =>
            import('@/views/maritime-affairs/ship-energy/ship-peryeardata'),
          meta: {
            title: '每年能效审核资料',
            access: '每年能效审核资料:列表',
          },
        },
        {
          path: '/ship-energy/yearreport',
          name: 'ship-yearreport',
          component: () =>
            import('@/views/maritime-affairs/ship-energy/ship-yearreport'),
          meta: {
            title: '能效证书',
            access: '能效证书及审核报告:列表',
          },
        },
        {
          path: '/ship-energy/seemp',
          name: 'ship-seemp',
          component: () =>
            import('@/views/maritime-affairs/ship-energy/ship-seemp'),
          meta: {
            title: '能效管理计划及审核报告',
            access: '能效管理计划及审核报告:列表',
          },
        },
        {
          path: '/ship-energy/soc',
          name: 'ship-soc',
          component: () =>
            import('@/views/maritime-affairs/ship-energy/ship-soc'),
          meta: {
            title: '排放符合声明',
            access: '排放符合声明:列表',
          },
        },
      ],
    },
    {
      title: '岸基文件',
      icon: 'mdi-note-multiple-outline',
      group: '/shore-doc',
      children: [
        {
          path: '/shore-doc/risk-ass/list',
          name: 'risk-ass-list',
          component: () =>
            import('@/views/ism-management/shore-doc/risk-ass-list'),
          meta: {
            title: '岸基风险评估',
            access: '岸基风险评估:列表',
          },
        },
        {
          path: '/shore-doc/shore-train/list',
          name: 'shore-train-list',
          component: () =>
            import('@/views/ism-management/shore-doc/shore-train-list'),
          meta: {
            title: '公司有效性评价/管理复查', //'岸基培训',
            access: '岸基培训:列表',
          },
        },
        {
          path: '/shore-doc/company-audit/list',
          name: 'company-audit',
          component: () =>
            import('@/views/ism-management/shore-doc/company-audit'),
          meta: {
            title: '公司审核',
            access: '公司审核:列表',
          },
        },
        {
          path: '/shore-doc/train-meeting/list',
          name: 'train-meeting',
          component: () =>
            import('@/views/ism-management/shore-doc/train-meeting'),
          meta: {
            title: '安全例会/培训', //'公司例会-培训',
            access: '公司例会-培训:列表',
          },
        },
        {
          path: '/shore-doc/accident-meeting/list',
          name: 'accident-meeting',
          component: () =>
            import('@/views/ism-management/shore-doc/accident-meeting'),
          meta: {
            title: '船舶事故会议纪要',
            access: '船舶事故会议纪要:列表',
          },
        },
        // {
        //   path: '/shore-doc/sass/list',
        //   name: 'sass-list',
        //   component: () => import('@/views/ism-management/shore-doc/sass-list'),
        //   meta: {
        //     title: 'SSAS记录',
        //     access: 'SSAS记录:列表',
        //   },
        // },
        // {
        //   path: '/shore-doc/security-drill/list',
        //   name: 'security-drill',
        //   component: () =>
        //     import('@/views/ism-management/shore-doc/security-drill'),
        //   meta: {
        //     title: '保安联合演习',
        //     access: '保安联合演习:列表',
        //   },
        // },
        {
          path: '/shore-doc/modify-doc/list',
          name: 'modify-doc',
          component: () =>
            import('@/views/ism-management/shore-doc/modify-doc'),
          meta: {
            title: '岸基文件修改',
            access: '岸基文件修改:列表',
          },
        },
        // {
        //   path: '/shore-doc/companydocuments',
        //   name: 'ship-companydocuments',
        //   component: () =>
        //     import(
        //       '@/views/maritime-affairs/ship-energy/ship-companydocuments'
        //     ),
        //   meta: {
        //     title: '文件修改&推送',
        //     access: '公司文件推送:列表',
        //   },
        // },
        {
          path: '/shore-doc/extra',
          name: 'ship-extra',
          component: () =>
            import('@/views/maritime-affairs/ship-energy/ship-extra'),
          meta: {
            title: '其他技术文件资料',
            access: '其他技术文件资料:列表',
          },
        },
      ],
    },
    {
      title: '培训演习',
      icon: 'mdi-account-details-outline',
      group: '/crew-onship-train',
      children: [
        {
          path: '/crew-onship-train/train-item',
          name: 'train-item',
          component: () =>
            import('@/views/ism-management/crew-onship-train/train-item'),
          meta: {
            title: '培训/演习项目库',
            access: '培训项目库:列表',
          },
        },
        {
          path: '/crew-onship-train/annual-train-plan-temp',
          name: 'annual-train-plan-temp',
          component: () =>
            import(
              '@/views/ism-management/crew-onship-train/annual-train-plan-temp.vue'
            ),
          meta: {
            title: '年度培训/演习计划模板',
            access: '年度培训计划模板:列表',
          },
        },
        {
          path: '/crew-onship-train/annual-train-plan-temp-detail/:id',
          name: 'annual-train-plan-temp-detail',
          component: () =>
            import(
              '@/views/ism-management/crew-onship-train/annual-train-plan-temp-detail'
            ),
          meta: {
            title: '年度培训/演习计划模板详情',
            access: '年度培训计划模板:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-onship-train/annual-train-ship-plan',
          name: 'annual-train-ship-plan',
          component: () =>
            import(
              '@/views/ism-management/crew-onship-train/annual-train-ship-plan'
            ),
          meta: {
            title: '年度培训/演习计划',
            access: '年度培训计划:列表',
          },
        },
        {
          path: '/crew-onship-train/annual-train-ship-plan-detail/:id',
          name: 'annual-train-ship-plan-detail',
          component: () =>
            import(
              '@/views/ism-management/crew-onship-train/annual-train-ship-plan-detail'
            ),
          meta: {
            title: '年度培训/演习计划详情',
            access: '年度培训计划:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-onship-train/month-train-ship-record',
          name: 'month-train-ship-record',
          component: () =>
            import(
              '@/views/ism-management/crew-onship-train/month-train-ship-record.vue'
            ),
          meta: {
            title: '月度培训/演习记录',
            access: '月度培训记录:列表',
          },
        },
        {
          path: '/crew-onship-train/month-train-ship-record-detail/:id',
          name: 'month-train-ship-record-detail',
          component: () =>
            import(
              '@/views/ism-management/crew-onship-train/month-train-ship-record-detail.vue'
            ),
          meta: {
            title: '月度培训/演习记录详情',
            access: '月度培训记录:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    // {
    //   title: '季度考评',
    //   icon: 'mdi-application-edit-outline',
    //   group: '/quarter-evaluate',
    //   children: [
    //     {
    //       path: '/quarter-evaluate/evaluate-item',
    //       name: 'evaluate-item',
    //       component: () =>
    //         import('@/views/ism-management/quarter-evaluate/evaluate-item'),
    //       meta: {
    //         title: '考核项目库',
    //         access: '考核项目库:列表',
    //       },
    //     },
    //     {
    //       path: '/quarter-evaluate/crew-evaluate-temp',
    //       name: 'crew-evaluate-temp',
    //       component: () =>
    //         import(
    //           '@/views/ism-management/quarter-evaluate/crew-evaluate-temp'
    //         ),
    //       meta: {
    //         title: '季度考评表',
    //         access: '季度考评表:列表',
    //       },
    //     },
    //     {
    //       path: '/quarter-evaluate/crew-evaluate-temp-detail/:id',
    //       name: 'crew-evaluate-temp-detail',
    //       component: () =>
    //         import(
    //           '@/views/ism-management/quarter-evaluate/crew-evaluate-temp-detail'
    //         ),
    //       meta: {
    //         title: '季度考评表详情',
    //         access: '季度考评表:列表',
    //         notShowInNav: true,
    //       },
    //     },
    //   ],
    // },
    {
      title: '重点防控',
      icon: 'mdi-alert-box-outline',
      group: '/accident-prevent',
      children: [
        {
          path: '/accident-prevent/inspect-type',
          name: 'inspect-type',
          component: () =>
            import('@/views/ism-management/accident-prevent/inspect-type'),
          meta: {
            title: '检查类型',
            access: '检查类型:列表',
          },
        },
        {
          path: '/accident-prevent/inspect-item',
          name: 'inspect-item',
          component: () =>
            import('@/views/ism-management/accident-prevent/inspect-item'),
          meta: {
            title: '检查项目库',
            access: '检查项目库:列表',
          },
        },
        {
          path: '/accident-prevent/check-list-temp',
          name: 'check-list-temp',
          component: () =>
            import('@/views/ism-management/accident-prevent/check-list-temp'),
          meta: {
            title: '检查清单模板',
            access: '检查清单模板:列表',
          },
        },
        {
          path: '/accident-prevent/check-list-temp-detail/:id',
          name: 'check-list-temp-detail',
          component: () =>
            import(
              '@/views/ism-management/accident-prevent/check-list-temp-detail'
            ),
          meta: {
            title: '检查清单模板详情',
            access: '检查清单模板:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/accident-prevent/month-check-list',
          name: 'month-check-list',
          component: () =>
            import('@/views/ism-management/accident-prevent/month-check-list'),
          meta: {
            title: '防止重大事故检查清单',
            access: '月度检查清单:列表',
          },
        },
        {
          path: '/accident-prevent/month-check-list-detail/:id',
          name: 'month-check-list-detail',
          component: () =>
            import(
              '@/views/ism-management/accident-prevent/month-check-list-detail'
            ),
          meta: {
            title: '防止重大事故检查清单详情',
            access: '月度检查清单:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    // {
    //   title: '文件收发',
    //   icon: 'mdi-note-check-outline',
    //   group: '/recheck-management',
    //   children: [
    //     {
    //       path: '/recheck-management/list',
    //       name: 'manage-recheck-list',
    //       component: () =>
    //         import('@/views/ism-management/manage-recheck/manage-recheck-list'),
    //       meta: {
    //         title: '管理复查报表',
    //         access: '管理复查报表:列表',
    //       },
    //     },
    //     {
    //       path: '/recheck-management/detail/:id',
    //       name: 'manage-recheck-detail',
    //       component: () =>
    //         import(
    //           '@/views/ism-management/manage-recheck/manage-recheck-detail'
    //         ),
    //       meta: {
    //         title: '管理复查报表',
    //         access: '管理复查报表:列表',
    //         notShowInNav: true,
    //       },
    //     },
    //     {
    //       path: '/recheck-management/sass/list',
    //       name: 'sass-list',
    //       component: () => import('@/views/ism-management/shore-doc/sass-list'),
    //       meta: {
    //         title: 'SSAS记录',
    //         access: 'SSAS记录:列表',
    //       },
    //     },
    //     {
    //       path: '/recheck-management/security-drill/list',
    //       name: 'security-drill',
    //       component: () =>
    //         import('@/views/ism-management/shore-doc/security-drill'),
    //       meta: {
    //         title: '保安联合演习',
    //         access: '保安联合演习:列表',
    //       },
    //     },
    //     {
    //       path: '/recheck-management/companydocuments',
    //       name: 'ship-companydocuments',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/ship-energy/ship-companydocuments'
    //         ),
    //       meta: {
    //         title: '文件修改&推送',
    //         access: '公司文件推送:列表',
    //       },
    //     },
    //   ],
    // },
    {
      title: '保安模块',
      icon: 'mdi-alpha-s-circle', //mdi-mdi-alpha-s-circle
      group: '/security-management',
      children: [
        // {
        //   path: '/security-management/list',
        //   name: 'manage-recheck-list',
        //   component: () =>
        //     import('@/views/ism-management/manage-recheck/manage-recheck-list'),
        //   meta: {
        //     title: '管理复查报表',
        //     access: '管理复查报表:列表',
        //   },
        // },
        // {
        //   path: '/security-management/detail/:id',
        //   name: 'manage-recheck-detail',
        //   component: () =>
        //     import(
        //       '@/views/ism-management/manage-recheck/manage-recheck-detail'
        //     ),
        //   meta: {
        //     title: '管理复查报表',
        //     access: '管理复查报表:列表',
        //     notShowInNav: true,
        //   },
        // },
        {
          path: '/security-management/sass/list',
          name: 'sass-list',
          component: () => import('@/views/ism-management/shore-doc/sass-list'),
          meta: {
            title: 'SSAS记录',
            access: 'SSAS记录:列表',
          },
        },
        {
          path: '/security-management/security-drill/list',
          name: 'security-drill',
          component: () =>
            import('@/views/ism-management/shore-doc/security-drill'),
          meta: {
            title: '保安联合演习',
            access: '保安联合演习:列表',
          },
        },
        {
          path: '/security-management/report-annual-review/list',
          name: 'report-annual-review',
          component: () =>
            import('@/views/ism-management/shore-doc/report-annual-review'),
          meta: {
            title: '保安年度评审',
            access: '报告年度评审:列表',
          },
        },
      ],
    },
    {
      title: '文件推送',
      icon: 'mdi-note-check-outline',
      group: '/companydocuments-management',
      children: [
        {
          path: '/companydocuments-management/companydocuments',
          name: 'ship-companydocuments',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-energy/ship-companydocuments'
            ),
          meta: {
            title: '文件修改&推送',
            access: '公司文件推送:列表',
          },
        },
      ],
    },
    {
      title: '船员管理',
      icon: 'mdi-table-account', //mdi-table-account//mdi-note-check-outline
      group: '/crew-management',
      children: [
        {
          path: '/crew-management/crew-health-information-list',
          name: 'crew-health-information-form-list',
          component: () =>
            import(
              '@/views/ism-management/crew-management/crew-health-information-list'
            ),
          meta: {
            title: '船员健康记录',
            access: '船员健康记录:列表',
          },
        },
        {
          path: '/crew-management/crew-health-information-detail/:id',
          name: 'crew-health-information-form-detail',
          component: () =>
            import(
              '@/views/ism-management/crew-management/crew-health-information-detail'
            ),
          meta: {
            title: '船员健康记录',
            access: '船员健康记录:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-management/crew-work-agreement/agreement-list',
          name: 'agreement-list',
          component: () =>
            import(
              '@/views/ism-management/crew-management/crew-work-agreement/agreement-list.vue'
            ),
          meta: {
            title: '海员就业协议',
            access: '海员就业协议:列表',
          },
        },
        {
          path: '/crew-management/crew-work-agreement/agreement-detail/:id',
          name: 'agreement-detail',
          component: () =>
            import(
              '@/views/ism-management/crew-management/crew-work-agreement/agreement-detail.vue'
            ),
          meta: {
            title: '海员就业协议详情',
            access: '海员就业协议:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船舶管理',
      icon: 'mdi-hydro-power', //mdi-hydro-power//mdi-note-check-outline
      group: '/ship-management',
      children: [
        {
          path: '/ship-management/list',
          name: 'manage-recheck-list',
          component: () =>
            import('@/views/ism-management/manage-recheck/manage-recheck-list'),
          meta: {
            title: '管理复查报告',
            access: '管理复查报表:列表',
          },
        },
        {
          path: '/ship-management/detail/:id',
          name: 'manage-recheck-detail',
          component: () =>
            import(
              '@/views/ism-management/manage-recheck/manage-recheck-detail'
            ),
          meta: {
            title: '管理复查报告',
            access: '管理复查报表:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-management/ship-condition-assessment-report',
          name: 'ship-condition-assessment-report',
          component: () =>
            import(
              '@/views/ism-management/ship-management/ship-condition-assessment-report'
            ),
          meta: {
            title: '船况评估报告',
            access: '船况评估报告:列表',
          },
        },
        {
          path: '/ship-management/ship-condition-assessment-report-detail/:id',
          name: 'ship-condition-assessment-report-detail',
          component: () =>
            import(
              '@/views/ism-management/ship-management/ship-condition-assessment-detail'
            ),
          meta: {
            title: '船况评估报告',
            access: '船况评估报告:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-management/ship-work-performance-report',
          name: 'ship-work-performance-report',
          component: () =>
            import(
              '@/views/ism-management/ship-management/ship-work-performance-report'
            ),
          meta: {
            title: '船舶述职报告',
            access: '船舶述职报告:列表',
          },
        },
        {
          path: '/ship-management/ship-work-performance-report-detail/:id',
          name: 'ship-work-performance-report-detail',
          component: () =>
            import(
              '@/views/ism-management/ship-management/ship-work-performance-report-detail'
            ),
          meta: {
            title: '船舶述职报告',
            access: '船舶述职报告:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    // {
    //   title: '船舶安全自查结果汇总表',
    //   icon: 'mdi-clipboard-plus-outline',
    //   group: '/ship-safety-self',
    //   children: [
    //     {
    //       path: '/report/ship-safety-self-list',
    //       name: 'ship-safety-self-list',
    //       component: () =>
    //         import('@/views/ism-management/report/ship-safety-self-list'),
    //       meta: {
    //         title: '船舶安全自查结果汇总表',
    //         access: '舶安全自查结果汇总申请',
    //       },
    //     },
    //     {
    //       path: '/report/ship-safety-self-detail/:id',
    //       name: 'ship-safety-self-detail',
    //       component: () =>
    //         import('@/views/ism-management/report/ship-safety-self-detail'),
    //       meta: {
    //         title: '船舶安全自查结果汇总表详情',
    //         access: '舶安全自查结果汇总申请',
    //         notShowInNav: true,
    //       },
    //     },
    //   ],
    // },
    /**
     * 旧版安全检查
    {
      title: '船舶检查', //安全检查
      icon: 'mdi-shield-check',
      group: '/ship-security',
      children: [
        {
          path: '/ship-security/ship-security-list',
          name: 'security-check-list',
          component: () =>
            import(
              '@/views/maritime-affairs/security-check/security-check-list'
            ),
          meta: {
            title: '安全检查',
            access: '安全检查:列表',
          },
        },
        // {
        //   path: '/ship-security-report-1',
        //   name: 'security-check-report-1',
        //   component: () =>
        //     import(
        //       '@/views/maritime-affairs/security-check/security-check-report-1'
        //     ),
        //   meta: {
        //     title: '安全检查报表',
        //     access: '安全检查报表',
        //   },
        // },
        // {
        //   path: '/ship-security-report-2',
        //   name: 'security-check-report-2',
        //   component: () =>
        //     import(
        //       '@/views/maritime-affairs/security-check/security-check-report-2'
        //     ),
        //   meta: {
        //     title: '安全检查-不符合报告',
        //     access: '安全检查-不符合报告',
        //   },
        // },
        {
          path: '/ship-security/ship-security-report-3',
          name: 'security-check-report-3',
          component: () =>
            import(
              '@/views/maritime-affairs/security-check/security-check-report-3'
            ),
          meta: {
            title: '安全检查-不符合报告new',
            access: '不符合报告NEW',
          },
        },
        {
          path: '/ship-security/ship-security-detail/:id',
          name: 'security-check-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/security-check/security-check-detail'
            ),
          meta: {
            title: '安全检查-详情',
            access: '安全检查:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-security/ship-security-total',
          name: 'security-check-statistic',
          component: () =>
            import(
              '@/views/maritime-affairs/security-check/security-check-statistic'
            ),
          meta: {
            title: '检查次数统计',
            access: '检查次数统计',
          },
        },
        {
          path: '/ship-security/ship-security-question-total',
          name: 'security-check-ques-stat',
          component: () =>
            import(
              '@/views/maritime-affairs/security-check/security-check-ques-stat'
            ),
          meta: {
            title: '检查项目详细统计',
            access: '检查项目详细统计',
          },
        },
        {
          path: '/ship-security/ship-security-dept-report-list-inspection',
          name: 'dept-report-list-inspection',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-accident/dept-report-list-inspection'
            ),
          meta: {
            title: '港口国/船旗国检查报告',
            access: '港口国/船旗国检查报告',
          },
        },
        {
          path: '/ship-security/ship-security-report-1new',
          name: 'security-check-report-1new',
          component: () =>
            import(
              '@/views/maritime-affairs/security-check/security-check-report-1new'
            ),
          meta: {
            title: '安全检查报表NEW',
            access: '安全检查报表NEW',
          },
        },
        {
          path: '/ship-security/ship-security-dept_inspection/:id',
          name: 'dept-report-inspection-detail',
          component: () =>
            import(
              '@/views/ism-management/report/dept-report-inspection-detail'
            ),
          meta: {
            title: '港口国/船旗国检查报告',
            access: '港口国/船旗国检查报告',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-security/ship-dept-security-check/:id',
          name: 'dept-report-security-check',
          component: () =>
            import('@/views/ism-management/report/dept-report-security-check'),
          meta: {
            title: '安全检查报表NEW',
            access: '安全检查报表NEW',
            notShowInNav: true,
          },
        },
      ],
    },
         */
    {
      title: '船舶检查',
      icon: 'mdi-shield-check',
      group: '/ship-new-security',
      children: [
        {
          path: '/ship-new-security/list-new',
          name: 'security-check-list-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/security-check-list-new'
            ),
          meta: {
            title: '安全检查',
            access: '船舶检查:列表',
          },
        },
        {
          path: '/ship-new-security/ship-new-security-dept-report-list-inspection-new',
          name: 'dept-report-list-inspection-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/dept-report-list-inspection-new'
            ),
          meta: {
            title: 'PSC/FSC检查',
            access: 'FSC/FSC检查',
          },
        },
        {
          path: '/ship-new-security/ship-new-security-dept-report-inspection-detail-new/:id',
          name: 'dept-report-inspection-detail-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/dept-report-inspection-detail-new'
            ),
          meta: {
            title: 'PSC/FSC检查-详情',
            access: 'FSC/FSC检查',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-new-security/ship-new-safety-self-list',
          name: 'ship-safety-self-list-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/ship-safety-self-list-new'
            ),
          meta: {
            title: '船舶自查',
            access: '船舶自查',
          },
        },
        {
          path: '/ship-new-security/ship-new-safety-self-detail/:id',
          name: 'ship-safety-self-detail-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/ship-safety-self-detail-new'
            ),
          meta: {
            title: '船舶自查-详情',
            access: '船舶自查',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-new-security/ship-new-security-report-3-new',
          name: 'security-check-report-3-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/security-check-report-3-new'
            ),
          meta: {
            title: '不符合报告',
            access: '船舶检查不符合报告',
          },
        },
        {
          path: '/ship-new-security/dept-report-info-detail1-new/:id',
          name: 'dept-report-info-detail1-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/dept-report-info-detail1-new'
            ),
          meta: {
            title: '不符合报告-详情',
            access: '船舶检查不符合报告',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-new-security/ship-new-security-detail-new/:id',
          name: 'security-check-detail-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/security-check-detail-new'
            ),
          meta: {
            title: '安全检查-详情',
            access: '船舶检查:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-new-security/ship-new-security-total-new',
          name: 'security-check-statistic-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/security-check-statistic-new'
            ),
          meta: {
            title: '检查次数统计',
            access: '船舶检查检查次数统计',
          },
        },
        {
          path: '/ship-new-security/ship-new-security-question-total-new',
          name: 'security-check-ques-stat-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/security-check-ques-stat-new'
            ),
          meta: {
            title: '检查项目详细统计',
            access: '船舶检查检查项目详细统计',
          },
        },
      ],
    },
    /** 
     * 旧版船舶事故
    {
      title: '事故与险情', //船舶事故
      icon: 'mdi-sail-boat-sink',
      group: '/ship-accident',
      children: [
        {
          path: '/ship-accident-list',
          name: 'accident-list',
          component: () =>
            import('@/views/maritime-affairs/ship-accident/accident-list'),
          meta: {
            title: '船舶事故记录',
            access: '船舶事故记录:列表',
          },
        },
        {
          path: '/ship-accident-total',
          name: 'accident-total',
          component: () =>
            import('@/views/maritime-affairs/ship-accident/accident-total'),
          meta: {
            title: '船舶事故统计',
            access: '船舶事故统计',
          },
        },
        // {
        //   path: '/ship-accident-report-list',
        //   name: 'accident-report-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-affairs/ship-accident/accident-report-list'
        //     ),
        //   meta: {
        //     title: '船舶事故-不符合报告审批',
        //     access: '船舶事故-不符合报告',
        //   },
        // },
        {
          path: '/ship-accident-report-list-new',
          name: 'accident-report-list-new',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-accident/accident-report-list-new'
            ),
          meta: {
            title: '不符合报告/险情',
            access: '船舶事故-不符合报告',
          },
        },
        {
          path: '/ship-accident/dept_info1/:id',
          name: 'dept-report-info-detail2',
          component: () =>
            import('@/views/ism-management/report/dept-report-info-detail1'),
          meta: {
            title: '不符合报告/险情',
            access: '船舶事故-不符合报告:编辑',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-accident-report2-list',
          name: 'accident-report2-list',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-accident/accident-report2-list'
            ),
          meta: {
            title: '海损机损事故报告审批',
            access: '船舶事故-事故报告:列表',
          },
        },
        {
          path: '/ship-accident/:id',
          name: 'accident-detail',
          component: () =>
            import('@/views/maritime-affairs/ship-accident/accident-detail'),
          meta: {
            title: '船舶事故-详情',
            access: '船舶事故记录:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-accident-sysreport/:id',
          name: 'accident-report-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-accident/sys-report/accident-report-detail'
            ),
          meta: {
            title: '机损海损事故报告',
            access: '船舶事故-事故报告:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/ship-accident-casualties/list',
          name: 'accident-casualties-list',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-accident/accident-casualties-list'
            ),
          meta: {
            title: '船舶事故-人身伤亡事故报告审批',
            access: '事故人员伤亡报表:列表',
          },
        },
        {
          path: '/ship-accident-casualties-sysreport/:id',
          name: 'casualties-report-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-accident/sys-report/casualties-report-detail'
            ),
          meta: {
            title: '船舶事故-人身伤亡事故报告审批',
            access: '事故人员伤亡报表:列表',

            notShowInNav: true,
          },
        },
      ],
    },
    */
    {
      title: '事故与险情',
      icon: 'mdi-sail-boat-sink',
      group: '/ship-new-accident',
      children: [
        {
          path: '/ship-new-accident-list',
          name: 'accident-list-new',
          component: () =>
            import('@/views/ism-management/ship-accident-new/accident-list'),
          meta: {
            title: '船舶事故记录',
            access: '事故与险情:列表',
          },
        },
        {
          path: '/ship-new-accident-total',
          name: 'accident-total-new',
          component: () =>
            import('@/views/ism-management/ship-accident-new/accident-total'),
          meta: {
            title: '船舶事故统计',
            access: '事故与险情统计',
          },
        },
        // {
        //   path: '/ship-accident-report-list',
        //   name: 'accident-report-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-affairs/ship-accident/accident-report-list'
        //     ),
        //   meta: {
        //     title: '船舶事故-不符合报告审批',
        //     access: '船舶事故-不符合报告',
        //   },
        // },
        {
          path: '/ship-new-accident-report-list-new',
          name: 'accident-report-list1-new',
          component: () =>
            import(
              '@/views/ism-management/ship-accident-new/accident-report-list-new'
            ),
          meta: {
            title: '不符合报告',
            access: '事故与险情不符合报告',
          },
        },
        {
          path: '/ship-new-accident/accident-report-near-miss-list',
          name: 'accident-report-near-miss-list',
          component: () =>
            import(
              '@/views/ism-management/ship-accident-new/accident-report-near-miss-list'
            ),
          meta: {
            title: '险情报告',
            access: '事故与险情险情报告',
          },
        },
        {
          path: '/ship-new-accident/dept-report-info-detail1-new/:id',
          name: 'dept-report-info-detail2-new',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/dept-report-info-detail1-new'
            ),
          meta: {
            title: '不符合报告/险情-详情',
            access: '事故与险情不符合报告',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-new-accident/dept-report-info-near-miss-detail/:id',
          name: 'dept-report-info-near-miss-detail',
          component: () =>
            import(
              '@/views/ism-management/security-check-new/dept-report-info-near-miss-detail'
            ),
          meta: {
            title: '险情报告-详情',
            access: '事故与险情险情报告',
            notShowInNav: true,
          },
        },
        // {
        //   path: '/ship-accident-report2-list',
        //   name: 'accident-report2-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-affairs/ship-accident/accident-report2-list'
        //     ),
        //   meta: {
        //     title: '海损机损事故报告审批',
        //     access: '船舶事故-事故报告:列表1',
        //   },
        // },
        {
          path: '/ship-new-accident/:id',
          name: 'accident-detail-new',
          component: () =>
            import('@/views/ism-management/ship-accident-new/accident-detail'),
          meta: {
            title: '船舶事故-详情',
            access: '事故与险情记录:列表',
            notShowInNav: true,
          },
        },
        // {
        //   //后期屏蔽
        //   path: '/ship-new-accident-sysreport/:id',
        //   name: 'accident-report-detail-new',
        //   component: () =>
        //     import(
        //       '@/views/ism-management/ship-accident-new/sys-report/accident-report-detail'
        //     ),
        //   meta: {
        //     title: '机损海损事故报告',
        //     access: '事故与险情事故报告:列表',

        //     notShowInNav: true,
        //   },
        // },
        // {
        //   //后期屏蔽
        //   path: '/ship-new-accident-casualties/list',
        //   name: 'accident-casualties-list-new',
        //   component: () =>
        //     import(
        //       '@/views/ism-management/ship-accident-new/accident-casualties-list'
        //     ),
        //   meta: {
        //     title: '船舶事故-人身伤亡事故报告审批',
        //     access: '事故人员伤亡报表:列表1',
        //   },
        // },
        // {
        //   //后期屏蔽
        //   path: '/ship-new-accident-casualties-sysreport/:id',
        //   name: 'casualties-report-detail-new',
        //   component: () =>
        //     import(
        //       '@/views/ism-management/ship-accident-new/sys-report/casualties-report-detail'
        //     ),
        //   meta: {
        //     title: '船舶事故-人身伤亡事故报告审批',
        //     access: '事故人员伤亡报表:列表1',

        //     notShowInNav: true,
        //   },
        // },
      ],
    },
  ],
}
