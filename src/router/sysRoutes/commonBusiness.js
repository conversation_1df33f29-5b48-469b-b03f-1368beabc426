export default {
  path: '/common-business',
  title: '基础业务',
  icon: 'mdi-cog',
  access: '基础业务',

  children: [
    {
      title: '船舶管理',
      icon: 'mdi-ship-wheel',
      group: '/ship',
      children: [
        {
          path: '/ship-info/list',
          name: 'common-ship-info-list',
          component: () =>
            import('@/views/common-business/ship-info/common-ship-info-list'),
          meta: {
            title: '船舶信息',
            access: '船舶信息:列表',
          },
        },
        {
          path: '/ship-info/detail/:id',
          name: 'common-ship-info-detail',
          component: () =>
            import('@/views/common-business/ship-info/common-ship-info-detail'),
          meta: {
            title: '船舶详情',
            access: '船舶信息:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/ship-certification/list',
          name: 'ship-certification-list',
          component: () =>
            import(
              '@/views/common-business/certification/ship-certification-list'
            ),
          meta: {
            title: '船舶证书',
            access: '船舶证书:列表',
          },
        },
        {
          path: '/ship-house/list',
          name: 'ship-house-list',
          component: () =>
            import('@/views/common-business/ship-house/ship-house-list'),
          meta: {
            title: '船舶仓库',
            access: '船舶仓库:列表',
          },
        },
        //手动录入
        {
          path: '/ship-carbin/list',
          name: 'carbin-list',
          component: () => import('@/views/common-business/carbin/carbin-list'),
          meta: {
            title: '船舱信息',
            access: '船舱信息:列表',
          },
        },
        //手动录入
        {
          path: '/ship-port/list',
          name: 'port-list',
          component: () => import('@/views/common-business/port/port-list'),
          meta: {
            title: '港口信息',
            access: '港口信息:列表',
          },
        },
        {
          path: '/ship-manage/list',
          name: 'common-ship-director',
          component: () =>
            import('@/views/common-business/ship-info/common-ship-director'),
          meta: {
            title: '船舶分管',
            access: '船舶分管:列表',
          },
        },
      ],
    },
    {
      title: '电子签名',
      icon: 'mdi-pen',
      group: '/e-signature',
      children: [
        {
          path: '/e-signature/list',
          name: 'e-signature-list',
          component: () =>
            import('@/views/common-business/e-signature/e-signature-list'),
          meta: {
            title: '电子签名列表',
            access: '电子签名列表:列表',
          },
        },
      ],
    },
    {
      title: '编号规则',
      icon: 'mdi-counter',
      group: '/num-rules',
      children: [
        {
          path: '/num-rules/list',
          name: 'numbering-rules-list',
          component: () =>
            import(
              '@/views/common-business/numbering-rules/numbering-rules-list'
            ),
          meta: {
            title: '编号规则列表',
            access: '编号规则列表:列表',
          },
        },
      ],
    },
    {
      title: '货币汇率',
      icon: 'mdi-currency-usd',
      group: '/currency',
      children: [
        {
          path: '/currency/list',
          name: 'currency-list',
          component: () =>
            import('@/views/common-business/currency/currency-list'),
          meta: {
            title: '货币汇率维护',
            access: '货币汇率维护:列表',
          },
        },
      ],
    },
    {
      title: '供应商信息管理',
      icon: 'mdi-account-group',
      group: '/supplier',
      children: [
        {
          path: '/supplier/list',
          name: 'supplier-main-list',
          component: () =>
            import('@/views/common-business/supplier/supplier-main-list'),
          meta: {
            title: '供应商列表',
            access: '供应商列表:列表',
          },
        },
        {
          path: '/supplier/detail/:id',
          name: 'supplier-detail',
          component: () =>
            import('@/views/common-business/supplier/supplier-detail'),
          meta: {
            title: '供应商详情',
            access: '供应商列表:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '文件管理',
      icon: 'mdi-file',
      group: '/file',
      children: [
        {
          path: '/file/base-file-management',
          name: 'base-file-management',
          component: () =>
            import('@/views/common-business/file/base-file-management'),
          meta: {
            title: '文件信息管理',
            access: '文件信息管理:列表',
          },
        },
      ],
    },
  ],
}
