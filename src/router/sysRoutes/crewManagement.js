export default {
  path: '/crew-management',
  title: '船员系统',
  icon: 'mdi-account-group-outline',
  access: '船员系统',

  children: [
    {
      title: '船员端',
      icon: 'mdi-account-details',
      group: '/crew-account-details',
      children: [
        {
          path: '/crew-account-details/crew-personal-information',
          name: 'crew-personal-information',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-personal-information.vue'
            ),
          meta: { title: '船员个人信息', access: '船员个人信息:列表' },
        },
        {
          path: '/crew-account-details/crew-personal-information-approve-list',
          name: 'crew-personal-information-approve-list',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-personal-information-approve-list'
            ),
          meta: {
            title: '船员个人信息修改审批',
            access: '船员个人信息修改审批:列表',
          },
        },
        {
          path: '/crew-account-details/crew-personal-information-approve-detail/:id',
          name: 'crew-personal-information-approve-detail',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-personal-information-approve-detail'
            ),
          meta: {
            title: '船员个人信息修改审批详情',
            access: '船员个人信息修改审批:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-account-details/crew-certificate-management',
          name: 'crew-certificate-management',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-certificate-management.vue'
            ),
          meta: {
            title: '船员个人证书管理',
            access: '船员个人证书管理:列表',
          },
        },
        {
          path: '/crew-account-details/crew-certificate-management-detail/:id',
          name: 'crew-certificate-management-detail',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-certificate-management-detail.vue'
            ),
          meta: {
            title: '船员个人证书管理详情',
            access: '船员个人证书管理:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-account-details/crew-sign-up',
          name: 'crewSignUp-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/positionSignUp/crewSignUp-management.vue'
            ),
          meta: {
            title: '上船前岗位查询及报名',
            access: '上船前岗位查询及报名:列表',
          },
        },
        {
          path: '/crew-account-details/crew-sign-own-up-over',
          name: 'crewSignUp-own-over',
          component: () =>
            import(
              '@/views/crew-management/deployment/positionSignUp/crewSignUp-own-over.vue'
            ),
          meta: {
            title: '岗位报名结果查询',
            access: '岗位报名结果查询:列表',
          },
        },
        {
          path: '/crew-account-details/daily-training-crew-select',
          name: 'daily-training-crew-select',
          component: () =>
            import(
              '@/views/crew-management/daily-training/daily-training-crew-select.vue'
            ),
          meta: {
            title: '日常培训方案报名',
          },
        },
        {
          path: '/crew-account-details/on-broad-crew',
          name: 'on-broad-crew',
          component: () =>
            import('@/views/crew-management/onBroad/on-broad-crew.vue'),
          meta: {
            title: '船员个人在船信息',
            access: '船员个人在船信息:列表',
          },
        },
        {
          path: '/crew-account-details/on-boat-salary',
          name: 'on-boat-personal-salary',
          component: () =>
            import(
              '@/views/crew-management/wage/crew-on-broad-wage/on-boat-personal-salary.vue'
            ),
          meta: {
            title: '个人费用明细',
            access: '个人费用明细:列表',
          },
        },
      ],
    },
    {
      title: '船员入职及离职',
      icon: 'mdi-account-multiple-plus',
      group: '/crew-account-entry',
      children: [
        {
          path: '/crew-account-entry/account-entry',
          name: 'entry-management',
          component: () =>
            import('@/views/crew-management/entry/entry-management'),
          meta: {
            title: '船员入职审核管理',
            access: '船员入职审核管理:列表',
          },
        },
        {
          path: '/crew-account-entry/crew-entry',
          name: 'crew-entry-information',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-entry-information'
            ),
          meta: {
            title: '船员入职信息审批',
            access: '船员入职信息审批:列表',
          },
        },
        {
          path: '/crew-account-entry/crew-entry/:id',
          name: 'crew-entry-detail',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-entry-detail'
            ),
          meta: {
            title: '船员入职信息审批详情信息',
            access: '船员入职信息审批:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-account-entry/account-entry-info',
          name: 'account-entry-info',
          component: () =>
            import('@/views/crew-management/entry/account-entry-info.vue'),
          meta: {
            title: '船员入职信息',
            access: '船员入职信息:列表',
          },
        },
        {
          path: '/crew-account-entry/account-entry-info-detail/:id',
          name: 'account-entry-info-detail',
          component: () =>
            import(
              '@/views/crew-management/entry/account-entry-info-detail.vue'
            ),
          meta: {
            title: '船员入职信息详情',
            access: '船员入职信息:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-account-entry/account-quit',
          name: 'quit-management',
          component: () =>
            import('@/views/crew-management/quit/quit-management'),
          meta: {
            title: '船员离职管理',
            access: '船员离职管理:列表',
          },
        },
        {
          path: '/crew-account-entry/account-quit-detail/:id',
          name: 'quit-detail',
          component: () => import('@/views/crew-management/quit/quit-detail'),
          meta: {
            title: '船员离职详情',
            access: '船员离职管理:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船员信息管理',
      icon: 'mdi-account-box-multiple',
      group: '/crew-account-information',
      children: [
        {
          path: '/crew-account-information/crew-information-management',
          name: 'crew-management',
          component: () =>
            import('@/views/crew-management/crew-information/crew-management'),
          meta: {
            title: '船员信息管理',
            access: '船员信息管理:列表',
          },
        },
        {
          path: '/crew-account-information/crew-person-totalInfo-approve-list',
          name: 'crew-person-totalInfo-approve-list',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-person-totalInfo-approve-list.vue'
            ),
          meta: {
            title: '船员信息审批',
            access: '船员信息审批:列表',
          },
        },
        {
          path: '/crew-account-information/crew-person-totalInfo-approve-detail/:id',
          name: 'crew-person-totalInfo-approve-detail',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-person-totalInfo-approve-detail'
            ),
          meta: {
            title: '船员信息审批详情',
            access: '船员信息审批:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-account-information/crew-information/:id',
          name: 'crew-information-detail',
          component: () =>
            import(
              '@/views/crew-management/crew-information/crew-information-detail'
            ),
          meta: {
            title: '船员个人信息详情',
            access: '船员信息管理:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-account-informationaccount-attribute',
          name: 'crew-attribute-management',
          component: () =>
            import(
              '@/views/crew-management/attribute/crew-attribute-management'
            ),
          meta: {
            title: '船员属性管理',
            access: '船员属性管理:列表',
          },
        },
        {
          path: '/crew-account-information-labor-contract-management',
          name: 'contract-management',
          component: () =>
            import(
              '@/views/crew-management/labor-contract/contract-management'
            ),
          meta: {
            title: '劳动合同查询',
            access: '劳动合同查询:列表',
          },
        },
        {
          path: '/crew-account-information-labor-contract-detail/:id',
          name: 'contract-detail',
          component: () =>
            import('@/views/crew-management/labor-contract/contract-detail'),
          meta: {
            title: '劳动合同详情',
            access: '劳动合同查询:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-account-information/crew-special',
          name: 'special-crew',
          component: () =>
            import('@/views/crew-management/crew-information/special-crew.vue'),
          meta: {
            title: '船员特殊管理',
            access: '船员特殊管理:列表',
          },
        },
        {
          path: '/crew-account-information/crew-black',
          name: 'black-crew',
          component: () =>
            import('@/views/crew-management/crew-information/black-crew.vue'),
          meta: {
            title: '船员黑名单管理',
            access: '船员黑名单管理:列表',
          },
        },
        {
          path: '/crew-account-information/crew-black-detail/:id',
          name: 'black-crew-detail',
          component: () =>
            import(
              '@/views/crew-management/crew-information/black-crew-detail.vue'
            ),
          meta: {
            title: '船员黑名单详情',
            access: '船员黑名单管理:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船员业务管理',
      icon: 'mdi-book-open-outline',
      group: '/business-list',
      children: [
        {
          path: '/business-list/on-broad-management',
          name: 'on-broad-management',
          component: () =>
            import('@/views/crew-management/onBroad/on-broad-management.vue'),
          meta: {
            title: '当前在船船员',
            access: '当前在船船员信息:列表',
          },
        },
        {
          path: '/business-list/on-broad-detail/:creId',
          name: 'on-broad-detail',
          component: () =>
            import('@/views/crew-management/onBroad/on-broad-detail.vue'),
          meta: {
            title: '当前在船船员详情',
            access: '当前在船船员信息:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/business-list/on-broad-salary-detail/:id',
          name: 'on-broad-salary-detail',
          component: () =>
            import(
              '@/views/crew-management/onBroad/on-broad-salary-detail.vue'
            ),
          meta: {
            title: '当前在船船员工资详情',
            access: '当前在船船员工资信息:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/business-list/on-broad-postion-up-list',
          name: 'on-broad-postion-up-list',
          component: () =>
            import(
              '@/views/crew-management/postion-up/on-broad-postion-up/on-broad-postion-up-list'
            ),
          meta: {
            title: '在船船员调薪',
            access: '在船船员调薪:列表',
          },
        },
        {
          path: '/business-list/on-broad-postion-up-detail/:id',
          name: 'on-broad-postion-up-detail',
          component: () =>
            import(
              '@/views/crew-management/postion-up/on-broad-postion-up/on-broad-postion-up-detail'
            ),
          meta: {
            title: '在船船员调薪详情',
            access: '在船船员调薪详情:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/business-list/on-broad-history-management',
          name: 'on-broad-history-management',
          component: () =>
            import(
              '@/views/crew-management/onBroad/on-broad-history-management.vue'
            ),
          meta: {
            title: '历史在船船员',
            access: '历史在船船员信息:列表',
          },
        },
        {
          path: '/business-list/on-broad-history-detail/:creId',
          name: 'on-broad-history-detail',
          component: () =>
            import(
              '@/views/crew-management/onBroad/on-broad-history-detail.vue'
            ),
          meta: {
            title: '历史在船船员详情',
            access: '历史在船船员信息:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/business-list/vacation-crew',
          name: 'vacation-crew',
          component: () =>
            import('@/views/crew-management/onBroad/vacation-crew.vue'),
          meta: {
            title: '公休船员',
            access: '公休船员名单:列表',
          },
        },
        {
          path: '/business-list/vacation-crew-detail/:creId',
          name: 'vacation-crew-detail',
          component: () =>
            import('@/views/crew-management/onBroad/vacation-crew-detail.vue'),
          meta: {
            title: '公休船员详情',
            access: '公休船员名单:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/business-list/service-period-not-expired',
          name: 'service-period-not-expired',
          component: () =>
            import(
              '@/views/crew-management/deployment/service-period/service-period-not-expired'
            ),
          meta: {
            title: '未满服务期船员',
            access: '未满服务期船员:列表',
          },
        },
      ],
    },
    {
      title: '船员调配管理',
      icon: 'mdi-account-arrow-right',
      group: '/crew-deployment-management',
      children: [
        {
          path: '/crew-deployment-management/crew-shift-change',
          name: 'shiftChange-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/shiftChange/shiftChange-management.vue'
            ),
          meta: {
            title: '交班下船计划管理',
            access: '交班下船计划管理:列表',
          },
        },
        {
          path: '/crew-deployment-management/crew-shift-change-detail/:id',
          name: 'shiftChange-detail',
          component: () =>
            import(
              '@/views/crew-management/deployment/shiftChange/shiftChange-detail.vue'
            ),
          meta: {
            title: '交班下船计划详情',
            access: '交班下船计划管理:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-deployment-management/crew-onshift-change',
          name: 'onshiftChange-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/onshiftChange/onshiftChange-management.vue'
            ),
          meta: {
            title: '交班上船计划管理',
            access: '交班上船计划管理:列表',
          },
        },
        {
          path: '/crew-deployment-management/crew-onshift-change-detail/:id',
          name: 'onshiftChange-detail',
          component: () =>
            import(
              '@/views/crew-management/deployment/onshiftChange/onshiftChange-detail.vue'
            ),
          meta: {
            title: '交班上船计划详情',
            access: '交班上船计划管理:列表',
            notShowInNav: true,
          },
        },

        {
          path: '/crew-deployment-management/crew-sign-up-over',
          name: 'crewSignUp-over',
          component: () =>
            import(
              '@/views/crew-management/deployment/positionSignUp/crewSignUp-over.vue'
            ),
          meta: {
            title: '岗位报名记录',
            access: '岗位报名记录:列表',
          },
        },
        {
          path: '/crew-deployment-management/crew-position-match',
          name: 'positionMatch-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/positionMatch/positionMatch-management.vue'
            ),
          meta: {
            title: '岗位匹配管理',
            access: '岗位匹配管理:列表',
          },
        },
        {
          path: '/crew-deployment-management/crew-position-match-detail/:id',
          name: 'positionMatch-detail',
          component: () =>
            import(
              '@/views/crew-management/deployment/positionMatch/positionMatch-detail.vue'
            ),
          meta: {
            title: '岗位匹配具体详情',
            access: '岗位匹配管理:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-deployment-management/interview-management',
          name: 'interview-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/interView/interview-management.vue'
            ),
          meta: {
            title: '上船面试名单管理',
            access: '上船面试名单管理:列表',
          },
        },
        {
          path: '/crew-deployment-management/interview-detail/:id',
          name: 'interview-detail',
          component: () =>
            import(
              '@/views/crew-management/deployment/interView/interview-detail.vue'
            ),
          meta: {
            title: '面试船员信息详情',
            access: '上船面试名单管理:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-deployment-management/list-to-be-boarded-management',
          name: 'list-boat-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/list-to-boat/list-boat-management.vue'
            ),
          meta: {
            title: '待上船船员名单',
            access: '待上船船员名单:列表',
          },
        },
        {
          path: '/crew-deployment-management/list-to-be-boarded-detail/:id',
          name: 'list-boat-detail',
          component: () =>
            import(
              '@/views/crew-management/deployment/list-to-boat/list-boat-detail.vue'
            ),
          meta: {
            title: '待上船船员名单详情',
            access: '待上船船员名单:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-deployment-management/shift-overhand-management',
          name: 'shift-handover-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/shift-handover/shift-handover-management.vue'
            ),
          meta: {
            title: '交接班上下船名单',
            access: '交接班上下船名单:列表',
          },
        },
        {
          path: '/crew-deployment-management/shift-overhand-detail/:id',
          name: 'shift-handover-detail',
          component: () =>
            import(
              '@/views/crew-management/deployment/shift-handover/shift-handover-detail'
            ),
          meta: {
            title: '交接班上下船详情',
            access: '交接班上下船名单:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-deployment-management/deployment-history-management',
          name: 'deployment-history-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/deployhistory/deployment-history-management.vue'
            ),
          meta: {
            title: '船员调配记录管理',
            access: '船员调配记录管理:列表',
          },
        },
        {
          path: '/crew-deployment-management/deployment-detail/:id',
          name: 'deployment-detail',
          component: () =>
            import(
              '@/views/crew-management/deployment/deployhistory/deployment-detail'
            ),
          meta: {
            title: '船员调配记录详情',
            access: '船员调配记录管理:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-deployment-management/deployment-track-management',
          name: 'track-management',
          component: () =>
            import(
              '@/views/crew-management/deployment/deploytrack/track-management.vue'
            ),
          meta: {
            title: '船员跟踪记录管理',
            access: '船员跟踪记录管理:列表',
          },
        },
        {
          path: '/crew-deployment-management/deployment-track-detail/:id',
          name: 'track-detail',
          component: () =>
            import(
              '@/views/crew-management/deployment/deploytrack/track-detail.vue'
            ),
          meta: {
            title: '船员跟踪记录详情',
            access: '船员跟踪记录管理:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船员培训计划管理',
      icon: 'mdi-badge-account-outline',
      group: '/crew-account-daily-training',
      children: [
        {
          path: '/crew-account-daily-training/crew-daily-training',
          name: 'daily-training-management',
          component: () =>
            import(
              '@/views/crew-management/daily-training/daily-training-management'
            ),
          meta: {
            title: '日常培训方案',
            access: '船员日常培训:列表',
          },
        },
        {
          path: '/crew-account-daily-training/crew-daily-traing-detail/:id',
          name: 'daily-training-detail',
          component: () =>
            import(
              `@/views/crew-management/daily-training/daily-training-detail`
            ),
          meta: {
            title: '日常培训方案详情',
            access: '船员日常培训:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-account-daily-training/crew-pre-training',
          name: 'pre-job-training',
          component: () =>
            import('@/views/crew-management/daily-training/pre-job-training'),
          meta: {
            title: '岗前培训待选名单',
            access: '岗前培训:列表',
          },
        },
        {
          path: '/crew-account-daily-training/cultivate-history-management',
          name: 'cultivate-history-management',
          component: () =>
            import(
              '@/views/crew-management/cultivate-history/cultivate-history-management.vue'
            ),
          meta: {
            title: '培训记录',
            access: '培训记录:列表',
          },
        },
        {
          path: '/crew-account-daily-training/cultivate-history-detail/:id',
          name: 'cultivate-history-detail',
          component: () =>
            import(
              '@/views/crew-management/cultivate-history/cultivate-history-detail.vue'
            ),
          meta: {
            title: '培训记录详情',
            access: '培训记录:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船员证书信息',
      icon: 'mdi-card-account-details-star-outline',
      group: '/crew-certificate',
      children: [
        {
          path: '/certificate/information',
          name: 'certificate-management',
          component: () =>
            import(
              '@/views/crew-management/certificate/certificate-management'
            ),
          meta: {
            title: '船员证书',
            access: '船员证书:列表',
          },
        },

        {
          path: '/certificate/information-detail/:id',
          name: 'certificate-detail',
          component: () =>
            import('@/views/crew-management/certificate/certificate-detail'),
          meta: {
            title: '船员证书详情',
            access: '船员证书:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/crew-certificate/certificate-audit',
          name: 'certificate-audit',
          component: () =>
            import('@/views/crew-management/certificate/certificate-audit.vue'),
          meta: {
            title: '待审核证书',
            access: '船员证书审核:列表',
          },
        },
        {
          path: '/crew-certificate/certificate-audit-detail/:id',
          name: 'certificate-audit-detail',
          component: () =>
            import(
              '@/views/crew-management/certificate/certificate-audit-detail.vue'
            ),
          meta: {
            title: '待审核证书详情',
            access: '待审核证书详情:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船舶考评管理',
      icon: 'mdi-ship-wheel',
      group: '/ship-assessment',
      children: [
        {
          path: '/ship-assessment/ship-quarter-assessment-list',
          name: 'ship-quarter-list',
          component: () =>
            import(
              '@/views/crew-management/assess/quarter/ship-quarter-list.vue'
            ),
          meta: {
            title: '船舶季度考评',
            access: '船舶季度考评:列表',
          },
        },
        {
          path: '/ship-assessment/ship-quarter-assessment-detail/:id',
          name: 'ship-quarter-detail',
          component: () =>
            import(
              '@/views/crew-management/assess/quarter/ship-quarter-detail.vue'
            ),
          meta: {
            title: '船舶季度考评详情',
            access: '船舶季度考评:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/ship-assessment/ship-assessment-bonus-list',
          name: 'assessment-bonus-list',
          component: () =>
            import(
              '@/views/crew-management/assess/bonus/assessment-bonus-list.vue'
            ),
          meta: {
            title: '船舶考评标准',
            access: '船舶考评标准:列表',
          },
        },
        {
          path: '/ship-assessment/crew-quarter-assessment-list',
          name: 'crew-quarter-list',
          component: () =>
            import(
              '@/views/crew-management/assess/quarter/crew-quarter-list.vue'
            ),
          meta: {
            title: '船员季度考评',
            access: '船员季度考评:列表',
          },
        },
        {
          path: '/ship-assessment/crew-quarter-assessment-detail/:id',
          name: 'crew-quarter-detail',
          component: () =>
            import(
              '@/views/crew-management/assess/quarter/crew-quarter-detail.vue'
            ),
          meta: {
            title: '船员季度考评详情',
            access: '船员季度考评:列表',

            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '工资标准管理',
      icon: 'mdi-cash-multiple',
      group: '/wage-standard',
      children: [
        {
          path: '/wage-standard/wage-standard-management',
          name: 'wage-standard-management',
          component: () =>
            import(
              '@/views/crew-management/wage/wage-standard/wage-standard-management.vue'
            ),
          meta: {
            title: '工资标准管理',
            access: '工资标准管理:列表',
          },
        },
        {
          path: '/wage-standard/on-ship-days',
          name: 'on-ship-days',
          component: () =>
            import(
              '@/views/crew-management/wage/wage-standard/on-ship-days.vue'
            ),
          meta: {
            title: '十三期工资',
            access: '十三期工资:列表',
          },
        },
        {
          path: '/wage-standard/wage-standard-detail/:id',
          name: 'wage-standard-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/wage-standard/wage-standard-detail.vue'
            ),
          meta: {
            title: '工资标准详情',
            access: '工资标准管理:列表',
            notShowInNav: true,
          },
        },
        // {
        //   path: '/wage-standard/shipowner-wage-standard-management',
        //   name: 'shipowner-wage-standard-management',
        //   component: () =>
        //     import(
        //       '@/views/crew-management/wage/wage-standard/shipowner-wage-standard-management.vue'
        //     ),
        //   meta: {
        //     title: '船东工资标准管理',
        //     access: '船东工资标准管理:列表',
        //   },
        // },
        {
          path: '/wage-standard/shipowner-wage-standard-detail/:id',
          name: 'shipowner-wage-standard-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/wage-standard/shipowner-wage-standard-detail.vue'
            ),
          meta: {
            title: '工资标准详情',
            access: '工资标准管理:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/wage-standard/social-provident-fund-list',
          name: 'social-provident-fund-list',
          component: () =>
            import(
              '@/views/crew-management/wage/social-provident-fund/social-provident-fund-list.vue'
            ),
          meta: {
            title: '船员社保公积金信息',
            access: '船员社保公积金信息:列表',
          },
        },
        {
          path: '/wage-standard/social-provident-history-fund-list',
          name: 'social-provident-history-fund-list',
          component: () =>
            import(
              `@/views/crew-management/wage/social-provident-fund/social-provident-history-fund-list.vue`
            ),
          meta: {
            title: '船员历史社保公积金信息',
            access: '船员历史社保公积金信息:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/wage-standard/social-provident-fund-deduction-list',
          name: 'social-provident-fund-deduction-list',
          component: () =>
            import(
              '@/views/crew-management/wage/social-provident-fund-deduction/social-provident-fund-deduction-list.vue'
            ),
          meta: {
            title: '船员社保公积金补扣信息',
            access: '船员社保公积金补扣信息:列表',
          },
        },
        {
          path: '/wage-standard/social-provident-history-fund-deduction-list',
          name: 'social-provident-history-fund-deduction-list',
          component: () =>
            import(
              `@/views/crew-management/wage/social-provident-fund-deduction/social-provident-history-fund-deduction-list.vue`
            ),
          meta: {
            title: '船员历史社保公积金补扣信息',
            access: '船员历史社保公积金补扣信息:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/wage-standard/personal-income-tax-management',
          name: 'personal-income-tax-management',
          component: () =>
            import(
              '@/views/crew-management/wage/persona-income-tax/personal-income-tax-management.vue'
            ),
          meta: {
            title: '船员月度补扣项',
            access: '船员月度补扣项:列表',
          },
        },
        {
          path: '/wage-standard/personal-year-income-tax-management',
          name: 'personal-year-income-tax-management',
          component: () =>
            import(
              '@/views/crew-management/wage/persona-income-tax/personal-year-income-tax-management.vue'
            ),
          meta: {
            title: '船员年度补扣项',
            access: '船员年度补扣项:列表',
          },
        },
        {
          path: '/wage-standard/crew-meals-fee-management',
          name: 'crew-meals-fee-management',
          component: () =>
            import(
              '@/views/crew-management/wage/crew-meals-fee/crew-meals-fee-management.vue'
            ),
          meta: {
            title: '船员伙食费',
            access: '船员伙食费:列表',
          },
        },
        {
          path: '/wage-standard/crew-meals-fee-detail/:id',
          name: 'crew-meals-fee-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/crew-meals-fee/crew-meals-fee-detail.vue'
            ),
          meta: {
            title: '船员伙食费详情',
            access: '船员伙食费:详情',
            notShowInNav: true,
          },
        },
        {
          path: '/wage-standard/crew-boat-wage-management',
          name: 'crew-boat-wage-management',
          component: () =>
            import(
              '@/views/crew-management/wage/crew-boat-wage/crew-boat-wage-management.vue'
            ),
          meta: {
            title: '船员按船工资',
            access: '船员按船工资:列表',
          },
        },
        {
          path: '/wage-standard/crew-boat-wage-detail/:id',
          name: 'crew-boat-wage-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/crew-boat-wage/crew-boat-wage-detail.vue'
            ),
          meta: {
            title: '船员按船工资详情',
            access: '船员按船工资:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/wage-standard/on-boat-wage-management',
          name: 'on-boat-wage-management',
          component: () =>
            import(
              '@/views/crew-management/wage/crew-on-broad-wage/on-boat-wage-management.vue'
            ),
          meta: {
            title: '在船船员工资',
            access: '在船船员工资:列表',
          },
        },
      ],
    },
    {
      title: '费用管理',
      icon: 'mdi-receipt-text-outline',
      group: '/invoice-management',
      children: [
        {
          path: '/invoice-management/expense-items-management',
          name: 'expense-items-management',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-items-management.vue'
            ),
          meta: {
            title: '船员费用项目',
            access: '船员费用项目:列表',
          },
        },
        {
          path: '/invoice-management/expenses-standard-management',
          name: 'expenses-standard-management',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-standard/expenses-standard-management.vue'
            ),
          meta: { title: '账面管理费标准', access: '账面管理费标准:列表' },
        },
        {
          path: '/invoice-management/expenses-standard-detail/:id',
          name: 'expenses-standard-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-standard/expenses-standard-detail.vue'
            ),
          meta: {
            title: '账面管理费标准详情',
            access: '账面管理费标准:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/invoice-management/expenses-invoice-list',
          name: 'expenses-invoice-list',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expenses-invoice-list'
            ),
          meta: {
            title: '费用分摊',
            access: '费用分摊:列表',
          },
        },
        {
          path: '/invoice-management/invoice-sap-list',
          name: 'invoice-sap-list',
          component: () =>
            import('@/views/crew-management/wage/sap/invoice-sap-list.vue'),
          meta: {
            title: 'SAP报文',
            // access: 'SAP入库报文:列表',
            access: '船员费用SAP报文:列表',
          },
        },
        {
          path: '/invoice-management/expenses-invoice-detail/:id',
          name: 'expenses-invoice-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expenses-invoice-detail'
            ),
          meta: {
            title: '费用分摊详情',
            access: '费用分摊:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/invoice-management/expenses-sum-list',
          name: 'expenses-sum-list',
          component: () =>
            import('@/views/crew-management/wage/expense/expenses-sum-list'),
          meta: {
            title: '费用汇总',
            access: '费用汇总:列表',
          },
        },
        {
          path: '/invoice-management/expenses-sum-detail/:id',
          name: 'expenses-sum-detail',
          component: () =>
            import('@/views/crew-management/wage/expense/expenses-sum-detail'),
          meta: {
            title: '费用汇总详情',
            access: '费用汇总:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/invoice-management/expense-apply-management',
          name: 'expense-apply-management',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-apply-management.vue'
            ),
          meta: {
            title: '费用申请',
            access: '费用申请:列表',
          },
        },
        {
          path: '/invoice-management/expense-apply-detail/:id',
          name: 'expense-apply-detail',
          component: () =>
            import('@/views/crew-management/wage/expense/expense-apply-detail'),
          meta: {
            title: '费用申请详情',
            access: '费用申请:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/invoice-management/expense-records-management',
          name: 'expense-records-management',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-records-management.vue'
            ),
          meta: {
            title: '费用信息查询',
            access: '费用信息查询:列表',
          },
        },
        {
          path: '/invoice-management/special-crew-expense-item',
          name: 'special-crew-expense-item',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/special-crew-expense-item.vue'
            ),
          meta: {
            title: '特殊船员杂费',
            access: '特殊船员杂费:列表',
          },
        },
        {
          path: '/invoice-management/special-crew-expense',
          name: 'special-crew-expense',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/special-crew-expense.vue'
            ),
          meta: {
            title: '特殊船员杂费汇总',
            access: '特殊船员杂费汇总:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/invoice-management/special-crew-expense-detail/:id',
          name: 'special-crew-expense-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/special-crew-expense-detail.vue'
            ),
          meta: {
            title: '特殊船员杂费详情',
            access: '特殊船员杂费:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/invoice-management/expense-epiboly-crew',
          name: 'expense-epiboly-crew',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-epiboly-crew.vue'
            ),
          meta: {
            title: '外包船员费用',
            access: '外包船员费用:列表',
          },
        },
        {
          path: '/invoice-management/expense-items-external',
          name: 'expense-items-external',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-items-external.vue'
            ),
          meta: {
            title: '船员费用对外付款',
            access: '船员费用对外付款:列表',
          },
        },
        {
          path: '/invoice-management/expense-items-external-total',
          name: 'expense-items-external-total',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-items-external-total.vue'
            ),
          meta: {
            title: '船员费用对外付款汇总',
            access: '船员费用对外付款汇总:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/invoice-management/expense-items-external-total-detail/:id',
          name: 'expense-items-external-total-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-items-external-total-detail.vue'
            ),
          meta: {
            title: '船员费用对外付款汇总详情',
            access: '船员费用对外付款汇总:详情',
            notShowInNav: true,
          },
        },
        {
          path: '/invoice-management/expense-items-external-detail/:id',
          name: 'expense-items-external-detail',
          component: () =>
            import(
              '@/views/crew-management/wage/expense/expense-items-external-detail.vue'
            ),
          meta: {
            title: '船员费用对外付款详情',
            access: '船员费用对外付款:详情',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船东费用',
      icon: 'mdi-map-legend',
      group: '/owner-bill',
      children: [
        {
          path: '/owner-bill/owner-expenses-management',
          name: 'owner-expenses-management',
          component: () =>
            import(
              '@/views/crew-management/owner-expenses/owner-expenses-standards/owner-expenses-management.vue'
            ),
          meta: { title: '船东考核标准', access: '船东考核标准:列表' },
        },
        {
          path: '/owner-bill/owner-expenses-detail/:id',
          name: 'owner-expenses-detail',
          component: () =>
            import(
              '@/views/crew-management/owner-expenses/owner-expenses-standards/owner-expenses-detail.vue'
            ),
          meta: {
            title: '船东考核标准详情',
            access: '船东考核标准:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/owner-bill/owner-expenses-invoice-list',
          name: 'owner-expenses-invoice-list',
          component: () =>
            import(
              '@/views/crew-management/owner-expenses/owner-expenses-invoice/owner-expenses-invoice-list.vue'
            ),
          meta: {
            title: '船员公司绩效考核',
            access: '船员公司绩效考核:列表',
          },
        },
        {
          path: '/owner-bill/owner-expenses-invoice-detail/:id',
          name: 'owner-expenses-invoice-detail',
          component: () =>
            import(
              '@/views/crew-management/owner-expenses/owner-expenses-invoice/owner-expenses-invoice-detail.vue'
            ),
          meta: {
            title: '船员公司绩效考核详情',
            access: '船员公司绩效考核:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/owner-bill/owner-expenses-approve-list',
          name: 'owner-expenses-approve-list',
          component: () =>
            import(
              '@/views/crew-management/owner-expenses/owner-expenses-approve/owner-expenses-approve-list.vue'
            ),
          meta: {
            title: '船东费用月结审批',
            access: '船东费用月结审批:列表',
          },
        },
        {
          path: '/owner-bill/owner-expenses-approve-detail/:id',
          name: 'owner-expenses-approve-detail',
          component: () =>
            import(
              '@/views/crew-management/owner-expenses/owner-expenses-approve/owner-expenses-approve-detail.vue'
            ),
          meta: {
            title: '船东费用月结审批',
            access: '船东费用月结审批:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/owner-bill/company-income',
          name: 'company-income',
          component: () =>
            import(
              '@/views/crew-management/owner-expenses/owner-company-income/company-income.vue'
            ),
          meta: {
            title: '船员公司收入',
            access: '船员公司收入:列表',
          },
        },
        {
          path: '/invoice-management/company-income-detail/:id',
          name: 'company-income-detail',
          component: () =>
            import(
              '@//views/crew-management/owner-expenses/owner-company-income/company-income-detail'
            ),
          meta: {
            title: '船员公司收入详情',
            access: '船员公司收入:列表',
            notShowInNav: true,
          },
        },
      ],
    },
  ],
}
