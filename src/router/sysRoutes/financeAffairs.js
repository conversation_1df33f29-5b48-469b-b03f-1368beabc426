export default {
  path: '/finance-affairs',
  title: '费用管理',
  //'财务管理'
  icon: 'mdi-cash-register',
  access: '财务管理',
  children: [
    {
      title: '费用科目',
      icon: 'mdi-palette-swatch-variant',
      group: '/cost-subject',
      children: [
        {
          path: '/cost-subject/list',
          name: 'cost-subject-list',
          component: () =>
            import('@/views/finance-affairs/cost-subject/cost-subject-list'),
          meta: {
            title: '费用科目',
            access: '费用科目:列表',
          },
        },
        {
          path: '/cost-subject/cost-inner-list',
          name: 'cost-inner-list',
          component: () =>
            import('@/views/finance-affairs/cost-subject/cost-inner-list'),
          meta: {
            title: '坞修卡片',
            access: '内部订单号:列表',
          },
        },
        {
          path: '/cost-subject/cost-asset-list',
          name: 'cost-asset-list',
          component: () =>
            import('@/views/finance-affairs/cost-subject/cost-asset-list'),
          meta: {
            title: 'SAP资产清单',
            access: '内部订单号:列表',
          },
        },
      ],
    },
    {
      title: '统计分析',
      icon: 'mdi-table-network',
      group: '/budget',
      children: [
        {
          path: '/budget/budget-year-list',
          name: 'budget-year-list',
          component: () =>
            import('@/views/finance-affairs/budget/budget-year-list'),
          meta: {
            title: '年度费用预算',
            access: '年度费用预算:列表',
          },
        },
        {
          path: '/budget/budget-year-detail/:id',
          name: 'budget-year-detail',
          component: () =>
            import('@/views/finance-affairs/budget/budget-year-detail'),
          meta: {
            title: '年度费用预算详情',
            access: '年度费用预算:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/budget/budget-year-detail-adjust/:id',
          name: 'budget-year-detail-adjust',
          component: () =>
            import('@/views/finance-affairs/budget/budget-year-detail-adjust'),
          meta: {
            title: '年度费用预算详情',
            access: '年度费用预算:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/budget/budget-statics-list',
          name: 'budget-statics-list',
          component: () =>
            import('@/views/finance-affairs/budget/budget-statics-list'),
          meta: {
            title: '费用统计清单',
            access: '费用统计清单:列表',
          },
        },
        {
          path: '/budget/budget-execute-list',
          name: 'budget-execute-list',
          component: () =>
            import('@/views/finance-affairs/budget/budget-execute-list'),
          meta: {
            title: '预算执行分析',
            access: '预算执行分析:列表',
          },
        },
        {
          path: '/budget/a-purchase-compare',
          name: 'a-purchase-compare',
          component: () =>
            import('@/views/finance-affairs/budget/a-purchase-compare'),
          meta: {
            title: '单船采购单价',
            access: '单船采购单价:列表',
          },
        },
        {
          path: '/budget/ships-purchase-compare',
          name: 'ships-purchase-compare',
          component: () =>
            import('@/views/finance-affairs/budget/ships-purchase-compare'),
          meta: {
            title: '多船采购单价对比',
            access: '多船采购单价对比:列表',
          },
        },
      ],
    },
    {
      title: '发票录入',
      icon: 'mdi-content-duplicate',
      group: /cost-gen|batch-cost/,
      children: [
        {
          path: '/cost-gen/spare-list',
          name: 'cost-gen-spare',
          component: () =>
            import('@/views/finance-affairs/cost-gen/cost-gen-spare'),
          meta: {
            title: '备件订单发票录入',
            access: '备件订单生成项目:列表',
          },
        },
        {
          path: '/cost-gen/soil-list',
          name: 'cost-gen-soil',
          component: () =>
            import('@/views/finance-affairs/cost-gen/cost-gen-soil'),
          meta: {
            title: '滑油订单发票录入',
            access: '滑油订单生成项目:列表',
          },
        },
        {
          path: '/cost-gen/material-list',
          name: 'cost-gen-material',
          component: () =>
            import('@/views/finance-affairs/cost-gen/cost-gen-material'),
          meta: {
            title: '物料订单发票录入',
            access: '物料订单生成项目:列表',
          },
        },
        {
          path: '/cost-gen/voyage-list',
          name: 'cost-gen-voyage',
          component: () =>
            import('@/views/finance-affairs/cost-gen/cost-gen-voyage'),
          meta: {
            title: '航修单发票录入',
            access: '航修单生成项目:列表',
          },
        },
        {
          path: '/cost-gen/dock-list',
          name: 'cost-gen-dock',
          component: () =>
            import('@/views/finance-affairs/cost-gen/cost-gen-dock'),
          meta: {
            title: '坞修单发票录入',
            access: '坞修单生成项目:列表',
          },
        },
        {
          path: '/cost-gen/abudget-list',
          name: 'cost-gen-budget',
          component: () =>
            import('@/views/finance-affairs/cost-gen/cost-gen-budget'),
          meta: {
            title: '预算发票录入',
            access: '预算生成项目:列表',
          },
        },
        // {
        //   path: '/cost-gen/self-repair-list',
        //   name: 'cost-gen-self',
        //   component: () =>
        //     import('@/views/finance-affairs/cost-gen/cost-gen-self'),
        //   meta: {
        //     title: '自修单生成项目',
        //     access: '自修单生成项目:列表',
        //   },
        // },
        {
          path: '/batch-cost/batch-cost-list',
          name: 'batch-cost-list',
          component: () =>
            import('@/views/finance-affairs/batch-cost/batch-cost-list'),
          meta: {
            title: '批量生成预算&发票录入',
            access: '批量预算:列表',
          },
        },
        {
          path: '/batch-cost/batch-cost-detail/:id',
          name: 'batch-cost-detail',
          component: () =>
            import('@/views/finance-affairs/batch-cost/batch-cost-detail'),
          meta: {
            title: '批量预算详情',
            access: '批量预算:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '付款管理',
      // title: '费用管理',
      icon: 'mdi-format-list-group',
      group: /cost-project|cost-voucher|cost-sap|cost-payment/,
      children: [
        {
          path: '/cost-project/large',
          name: 'large-purchase-list',
          component: () =>
            import(
              '@/views/maritime-maintence/purchase-manage/large-purchase-list'
            ),
          meta: {
            title: '预算管理审批',
            access: '预算管理审批:列表',
          },
        },
        {
          path: '/cost-project/large/:id',
          name: 'large-purchase-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/purchase-manage/large-purchase-detail'
            ),
          meta: {
            title: '预算管理审批详情',
            access: '预算管理审批',
            notShowInNav: true,
          },
        },
        {
          path: '/cost-project/list',
          name: 'cost-project-list',
          component: () =>
            import('@/views/finance-affairs/cost-project/cost-project-list'),
          meta: {
            title: '发票行项目明细',
            access: '费用项目:列表',
          },
        },
        {
          path: '/cost-project/detail/:id',
          name: 'cost-project-detail',
          component: () =>
            import('@/views/finance-affairs/cost-project/cost-project-detail'),
          meta: {
            title: '费用项目-详情',
            access: '费用项目:列表',
            notShowInNav: true,
          },
        },
        // {
        //   path: '/batch-cost/batch-cost-list',
        //   name: 'batch-cost-list',
        //   component: () =>
        //     import('@/views/finance-affairs/batch-cost/batch-cost-list'),
        //   meta: {
        //     title: '批量预算',
        //     access: '批量预算:列表',
        //   },
        // },
        // {
        //   path: '/batch-cost/batch-cost-detail/:id',
        //   name: 'batch-cost-detail',
        //   component: () =>
        //     import('@/views/finance-affairs/batch-cost/batch-cost-detail'),
        //   meta: {
        //     title: '批量预算详情',
        //     access: '批量预算:列表',
        //     notShowInNav: true,
        //   },
        // },
        {
          path: '/cost-voucher/list',
          name: 'cost-voucher-list',
          component: () =>
            import('@/views/finance-affairs/cost-voucher/cost-voucher-list'),
          meta: {
            title: '发票管理',
            access: '费用凭证:列表',
          },
        },
        {
          path: '/cost-voucher/detail/:id',
          name: 'cost-voucher-detail',
          component: () =>
            import('@/views/finance-affairs/cost-voucher/cost-voucher-detail'),
          meta: {
            title: '发票管理-详情',
            access: '费用凭证:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/cost-voucher/reolad/:id',
          name: 'cost-voucher-reload',
          component: () =>
            import('@/views/finance-affairs/cost-voucher/cost-voucher-reload'),
          meta: {
            title: '发票管理-详情',
            access: '费用凭证:列表',
            notShowInNav: true,
          },
        },
        // {
        //   path: '/cost-sap-mes/list',
        //   name: 'cost-sap-mes-list',
        //   component: () =>
        //     import('@/views/finance-affairs/cost-sap-mes/cost-sap-mes-list'),
        //   meta: {
        //     title: '费用SAP报文',
        //     access: '费用SAP报文:列表',
        //   },
        // },
        {
          path: '/cost-sap-mes/list001/',
          name: 'cost-sap-mes-list001',
          component: () =>
            import('@/views/finance-affairs/cost-sap-mes/cost-sap-mes-list'),
          meta: {
            title: 'SAP入库报文',
            // access: 'SAP入库报文:列表',
            access: '费用SAP报文:列表',
            sapType: 'JMM001',
          },
        },
        {
          path: '/cost-sap-mes/list003',
          name: 'cost-sap-mes-list003',
          component: () =>
            import('@/views/finance-affairs/cost-sap-mes/cost-sap-mes-list'),
          meta: {
            title: 'SAP出库报文',
            // access: 'SAP出库报文:列表',
            access: '费用SAP报文:列表',
            sapType: 'JMM003',
          },
        },
        {
          path: '/cost-sap-mes/list002',
          name: 'cost-sap-mes-list002',
          component: () =>
            import('@/views/finance-affairs/cost-sap-mes/cost-sap-mes-list'),
          meta: {
            title: 'SAP发票报文',
            // access: 'SAP凭证报文:列表',
            access: '费用SAP报文:列表',
            sapType: 'JMM002',
            isBudget: false,
          },
        },
        {
          path: '/cost-sap-mes/list002b',
          name: 'cost-sap-mes-list002b',
          component: () =>
            import('@/views/finance-affairs/cost-sap-mes/cost-sap-mes-list'),
          meta: {
            title: 'SAP预算报文',
            // access: 'SAP预算报文:列表',
            access: '费用SAP报文:列表',
            sapType: 'JMM002',
            isBudget: true,
          },
        },
        {
          path: '/cost-sap-mes/list001new',
          name: 'cost-sap-mes-list001new',
          component: () =>
            import(
              '@/views/finance-affairs/cost-sap-mes/cost-sap-new-mes-list'
            ),
          meta: {
            title: 'SAP入库报文（new）',
            // access: 'SAP预算报文:列表',
            access: '费用SAP报文:列表',
            category: 'JMM001',
          },
        },
        {
          path: '/cost-sap-mes/list002new',
          name: 'cost-sap-mes-list002new',
          component: () =>
            import(
              '@/views/finance-affairs/cost-sap-mes/cost-sap-new-mes-list'
            ),
          meta: {
            title: 'SAP发票报文（new）',
            // access: 'SAP预算报文:列表',
            access: '费用SAP报文:列表',
            category: 'JMM002',
          },
        },
        {
          path: '/cost-sap-mes/list004',
          name: 'cost-sap-main-mes-list',
          component: () =>
            import(
              '@/views/finance-affairs/cost-sap-mes/cost-sap-main-mes-list'
            ),
          meta: {
            title: 'SAP主数据报文',
            // access: 'SAP预算报文:列表',
            access: '费用SAP报文:列表',
            category: 'JMM004',
          },
        },
        {
          path: '/cost-payment/list',
          name: 'cost-payment-list',
          component: () =>
            import('@/views/finance-affairs/cost-payment/cost-payment-list'),
          meta: {
            title: '付款审批',
            access: '付款审批:列表',
          },
        },
        {
          path: '/cost-payment/detail/:id',
          name: 'cost-payment-detail',
          component: () =>
            import('@/views/finance-affairs/cost-payment/cost-payment-detail'),
          meta: {
            title: '付款审批-详情',
            access: '付款审批:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船东费用部分',
      icon: 'mdi-inbox-full',
      group: '/ship-owner-cost',
      children: [
        {
          path: '/ship-owner-cost/hire-purchase-list',
          name: 'hire-purchase-list',
          component: () =>
            import('@/views/ship-owner/hire-purchase/hire-purchase-list'),
          meta: {
            title: '分期付款',
            access: '分期付款:列表',
          },
        },
        {
          path: '/ship-owner-cost/hire-purchase-detail/:id',
          name: 'hire-purchase-detail',
          component: () =>
            import('@/views/ship-owner/hire-purchase/hire-purchase-detail'),
          meta: {
            title: '分期付款',
            access: '分期付款:列表',

            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '船舶备用金',
      icon: 'mdi-account-cash-outline',
      group: '/ship-spare-money',
      children: [
        {
          path: '/ship-spare-money/spare-monet-info',
          name: 'spare-monet-info',
          component: () =>
            import('@/views/finance-affairs/spare-money/spare-monet-info'),
          meta: {
            title: '备用金设置',
            access: '备用金设置:列表',
          },
        },
        {
          path: '/ship-spare-money/captain-change-list',
          name: 'captain-change-list',
          component: () =>
            import(
              '@/views/finance-affairs/spare-money/captain/captain-change-list'
            ),
          meta: {
            title: '船长变更',
            access: '船长变更:列表',
          },
        },
        {
          path: '/ship-spare-money/captain-change-detail/:id',
          name: 'captain-change-detail',
          component: () =>
            import(
              '@/views/finance-affairs/spare-money/captain/captain-change-detail'
            ),
          meta: {
            title: '船长变更详情',
            access: '船长变更:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/ship-spare-money/spare-money-supply-list',
          name: 'spare-money-supply-list',
          component: () =>
            import(
              '@/views/finance-affairs/spare-money/supply/spare-money-supply-list'
            ),
          meta: {
            title: '备用金补充',
            access: '备用金补充:列表',
          },
        },
        {
          path: '/ship-spare-money/spare-money-supply-detail/:id',
          name: 'spare-money-supply-detail',
          component: () =>
            import(
              '@/views/finance-affairs/spare-money/supply/spare-money-supply-detail'
            ),
          meta: {
            title: '备用金补充-详情',
            access: '备用金补充:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/ship-spare-money/spare-money-consume-list',
          name: 'spare-money-consume-list',
          component: () =>
            import(
              '@/views/finance-affairs/spare-money/consume/spare-money-consume-list'
            ),
          meta: {
            title: '备用金消耗',
            access: '备用金消耗:列表',
          },
        },
        {
          path: '/ship-spare-money/spare-money-consume-detail/:id',
          name: 'spare-money-consume-detail',
          component: () =>
            import(
              '@/views/finance-affairs/spare-money/consume/spare-money-consume-detail'
            ),
          meta: {
            title: '备用金消耗-详情',
            access: '备用金消耗:列表',

            notShowInNav: true,
          },
        },
      ],
    },
  ],
}
