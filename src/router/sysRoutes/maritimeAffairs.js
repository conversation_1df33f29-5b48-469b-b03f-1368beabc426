export default {
  path: '/maritime-affairs',
  title: '海务管理',
  icon: 'mdi-anchor',
  access: '海务管理',
  children: [
    {
      title: '海务主管日常工作',
      icon: 'mdi-post-outline',
      group: '/daily-work',
      children: [
        {
          path: '/daily-work/list',
          name: 'daily-work-list',
          component: () =>
            import('@/views/maritime-affairs/daily-work/daily-work-list'),
          meta: {
            title: '海务主管日常工作',
            access: '海务主管日常工作:列表',
          },
        },
        {
          path: '/daily-work/detail/:id',
          name: 'daily-work-detail',
          component: () =>
            import('@/views/maritime-affairs/daily-work/daily-work-detail'),
          meta: {
            title: '海务主管日常工作-详情',
            access: '海务主管日常工作:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/daily-work-total',
          name: 'daily-work-statistic',
          component: () =>
            import('@/views/maritime-affairs/daily-work/daily-work-statistic'),
          meta: {
            title: '海务主管日常工作统计',
            access: '海务主管日常工作统计',
          },
        },
      ],
    },
    {
      title: '航次综合管理',
      icon: 'mdi-post-outline',
      group: '/departure-cargo-stowage',
      children: [
        {
          path: '/departure-cargo-stowage/list',
          name: 'departure-cargo-stowage-list',
          component: () =>
            import(
              '@/views/maritime-affairs/departure-cargo-stowage/departure-cargo-stowage-list'
            ),
          meta: {
            title: '货物积载信息',
            access: '货物积载信息:列表',
          },
        },
        {
          path: '/departure-cargo-stowage/detail/:id',
          name: 'departure-cargo-stowage-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/departure-cargo-stowage/departure-cargo-stowage-detail'
            ),
          meta: {
            title: '离货物积载信息-详情',
            access: '货物积载信息:列表',
            notShowInNav: true,
          },
        },
        // {
        //   path: '/departure-cargo-stowage-total',
        //   name: 'departure-cargo-stowage-statistic',
        //   component: () =>
        //     import(
        //       '@/views/maritime-affairs/departure-cargo-stowage/departure-cargo-stowage-statistic'
        //     ),
        //   meta: {
        //     title: '离港货物积载信息统计',
        //     access: '离港货物积载信息统计',
        //   },
        // },
        {
          path: '/departure-cargo-stowage/port-assessment/list',
          name: 'port-assessment-list',
          component: () =>
            import(
              '@/views/maritime-affairs/port-assessment/port-assessment-list'
            ),
          meta: {
            title: '港口评估',
            access: '港口评估:列表',
          },
        },
        {
          path: '/departure-cargo-stowage/port-assessment/detail/:id',
          name: 'port-assessment-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/port-assessment/port-assessment-detail'
            ),
          meta: {
            title: '港口评估-详情',
            access: '港口评估:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/departure-cargo-stowage/passage-plan/list',
          name: 'passage-plan-list',
          component: () =>
            import('@/views/maritime-affairs/passage-plan/passage-plan-list'),
          meta: {
            title: '航次计划',
            access: '航次计划:列表',
          },
        },
        {
          path: '/departure-cargo-stowage/passage-plan/detail/:id',
          name: 'passage-plan-detail',
          component: () =>
            import('@/views/maritime-affairs/passage-plan/passage-plan-detail'),
          meta: {
            title: '航次计划-详情',
            access: '航次计划:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    // {
    //   title: '港口评估',
    //   icon: 'mdi-post-outline',
    //   group: '/port-assessment',
    //   children: [
    //     {
    //       path: '/port-assessment/list',
    //       name: 'port-assessment-list',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/port-assessment/port-assessment-list'
    //         ),
    //       meta: {
    //         title: '港口评估',
    //         access: '港口评估:列表',
    //       },
    //     },
    //     {
    //       path: '/port-assessment/detail/:id',
    //       name: 'port-assessment-detail',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/port-assessment/port-assessment-detail'
    //         ),
    //       meta: {
    //         title: '港口评估-详情',
    //         access: '港口评估:列表',
    //         notShowInNav: true,
    //       },
    //     },
    //     // {
    //     //   path: '/departure-cargo-stowage-total',
    //     //   name: 'departure-cargo-stowage-statistic',
    //     //   component: () =>
    //     //     import(
    //     //       '@/views/maritime-affairs/departure-cargo-stowage/departure-cargo-stowage-statistic'
    //     //     ),
    //     //   meta: {
    //     //     title: '离港货物积载信息统计',
    //     //     access: '离港货物积载信息统计',
    //     //   },
    //     // },
    //   ],
    // },
    // {
    //   title: '航次计划',
    //   icon: 'mdi-post-outline',
    //   group: '/passage-plan',
    //   children: [
    //     {
    //       path: '/passage-plan/list',
    //       name: 'passage-plan-list',
    //       component: () =>
    //         import('@/views/maritime-affairs/passage-plan/passage-plan-list'),
    //       meta: {
    //         title: '航次计划',
    //         access: '航次计划:列表',
    //       },
    //     },
    //     {
    //       path: '/passage-plan/detail/:id',
    //       name: 'passage-plan-detail',
    //       component: () =>
    //         import('@/views/maritime-affairs/passage-plan/passage-plan-detail'),
    //       meta: {
    //         title: '航次计划-详情',
    //         access: '航次计划:列表',
    //         notShowInNav: true,
    //       },
    //     },
    //     // {
    //     //   path: '/departure-cargo-stowage-total',
    //     //   name: 'departure-cargo-stowage-statistic',
    //     //   component: () =>
    //     //     import(
    //     //       '@/views/maritime-affairs/departure-cargo-stowage/departure-cargo-stowage-statistic'
    //     //     ),
    //     //   meta: {
    //     //     title: '离港货物积载信息统计',
    //     //     access: '离港货物积载信息统计',
    //     //   },
    //     // },
    //   ],
    // },
    {
      title: '图书资料管理',
      icon: 'mdi-book-open',
      group: '/book-data',
      children: [
        // {
        //   path: '/book-data/bag/bgview',
        //   name: 'bag-view',
        //   component: () => import('@/views/maritime-affairs/bag/bag-view'),
        //   meta: {
        //     title: '船保部基础包及更新包',
        //     access: '船保部基础包及更新包:列表',
        //   },
        // },
        {
          path: '/book-data/bag/bagview',
          name: 'AIO-list',
          component: () => import('@/views/maritime-affairs/newAIO/AIO-list'),
          meta: {
            title: '最新AIO',
            access: '最新AIO:列表',
          },
        },
        {
          path: '/book-data/navigation-notice/list',
          name: 'navigation-notice-list',
          component: () =>
            import(
              '@/views/maritime-affairs/navigation-notice/navigation-notice-list'
            ),
          meta: {
            title: '航海通告',
            access: '航海通告:列表',
          },
        },
        {
          path: '/book-data/navigation-notice/detail/:id',
          name: 'navigation-notice-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/navigation-notice/navigation-notice-detail'
            ),
          meta: {
            title: '航海通告-详情',
            access: '航海通告:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/book-data/electronic-chart/list',
          name: 'electronic-chart-list',
          component: () =>
            import(
              '@/views/maritime-affairs/electronic-chart/electronic-chart-list'
            ),
          meta: {
            title: '电子海图-电子书',
            access: '电子海图:列表',
          },
        },
        {
          path: '/book-data/paper-chart/list',
          name: 'paper-chart-list',
          component: () =>
            import('@/views/maritime-affairs/paper-chart/paper-chart-list'),
          meta: {
            title: '纸版海图-订购及发船',
            access: '纸版海图:列表',
          },
        },
        {
          path: '/book-data/paper-chart/new',
          name: 'paper-chart-create',
          component: () =>
            import('@/views/maritime-affairs/paper-chart/paper-chart-start'),
          meta: {
            title: '纸版海图/书-新增',
            access: '纸版海图:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/book-data/paper-chart/:id',
          name: 'paper-chart-detail',
          component: () =>
            import('@/views/maritime-affairs/paper-chart/paper-chart-detail'),
          meta: {
            title: '纸版海图/书-编辑',
            access: '纸版海图:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/book-data/electronic-chart-season/list',
          name: 'electronic-chart-season-list',
          component: () =>
            import(
              '@/views/maritime-affairs/electronic-chart/electronic-chart-season-list'
            ),
          meta: {
            title: '电子海图-书-季度设置',
            access: '电子海图-季度设置:列表',
          },
        },
        {
          path: '/book-data/electronic-chart-season/:id',
          name: 'electronic-chart-season-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/electronic-chart/electronic-chart-season-detail'
            ),
          meta: {
            title: '电子海图/书-季度设置',
            access: '电子海图-季度设置:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/book-data/electronic-chart/new',
          name: 'electronic-chart-create',
          component: () =>
            import(
              '@/views/maritime-affairs/electronic-chart/electronic-chart-start'
            ),
          meta: {
            title: '电子海图/书-新增',
            access: '电子海图:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/book-data/electronic-chart/:id',
          name: 'electronic-chart-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/electronic-chart/electronic-chart-detail'
            ),
          meta: {
            title: '电子海图/书-编辑',
            access: '电子海图:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/book-data/ship-departure-management/departure',
          name: 'ship-data-list',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-departure-management/ship-data-list'
            ),
          meta: {
            title: '发船资料',
            access: '发船资料:列表',
          },
        },
        {
          path: '/book-data/ship-departure-management/:id',
          name: 'ship-data-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/ship-departure-management/ship-data-detail'
            ),
          meta: {
            title: '本轮发船资料详情',
            access: '发船资料:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '发文通函',
      icon: 'mdi-post-outline',
      group: '/circular',
      children: [
        {
          path: '/circular/list',
          name: 'circular-list',
          component: () =>
            import('@/views/maritime-affairs/circular/circular-list'),
          meta: {
            title: '船旗国通告',
            access: '一般通函:列表',
          },
        },
        {
          path: '/circular/detail/:id',
          name: 'circular-detail',
          component: () =>
            import('@/views/maritime-affairs/circular/circular-detail'),
          meta: {
            title: '船旗国通告-详情',
            access: '一般通函:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/circular-special/list',
          name: 'circular-special-list',
          component: () =>
            import('@/views/maritime-affairs/circular/circular-special-list'),
          meta: {
            title: '海务安全提示&公司内部通函',
            access: '海务安全通报&指定人员发文:列表',
          },
        },
        {
          path: '/circular-special/detail/:id',
          name: 'circular-special-detail',
          component: () =>
            import('@/views/maritime-affairs/circular/circular-special-detail'),
          meta: {
            title: '提示&通函-详情',
            access: '海务安全通报&指定人员发文:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/circular-appoint/detail/:id',
          name: 'circular-appoint-detail',
          component: () =>
            import('@/views/maritime-affairs/circular/circular-appoint-detail'),
          meta: {
            title: '提示&通函-详情',
            access: '海务安全通报&指定人员发文:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '资料数据库',
      icon: 'mdi-folder-text-outline',
      group: '/materials',
      children: [
        {
          path: '/materials/book-ch',
          name: 'materials-book-ch',
          component: () =>
            import('@/views/maritime-affairs/materials/book/book-ch'),
          meta: {
            title: '中版书',
            access: '中版书:列表',
          },
        },
        {
          path: '/materials/book-en',
          name: 'materials-book-en',
          component: () =>
            import('@/views/maritime-affairs/materials/book/book-en'),
          meta: {
            title: '英版书',
            access: '英版书:列表',
          },
        },
        {
          path: '/materials/book-jpn',
          name: 'materials-book-jp',
          component: () =>
            import('@/views/maritime-affairs/materials/book/book-jp'),
          meta: {
            title: '日版书',
            access: '日版书:列表',
          },
        },
        {
          path: '/materials/book-mari',
          name: 'materials-book-ma',
          component: () =>
            import('@/views/maritime-affairs/materials/book/book-ma'),
          meta: {
            title: '海事书',
            access: '海事书:列表',
          },
        },
        {
          path: '/materials/chart-en',
          name: 'materials-chart-en',
          component: () =>
            import('@/views/maritime-affairs/materials/chart/chart-en'),
          meta: {
            title: '英版图',
            access: '英版图:列表',
          },
        },
        {
          path: '/materials/chart-na',
          name: 'materials-chart-na',
          component: () =>
            import('@/views/maritime-affairs/materials/chart/chart-na'),
          meta: {
            title: '海军图',
            access: '海军图:列表',
          },
        },
        {
          path: '/materials/chart-ma',
          name: 'materials-chart-ma',
          component: () =>
            import('@/views/maritime-affairs/materials/chart/chart-ma'),
          meta: {
            title: '海事图',
            access: '海事图:列表',
          },
        },
        {
          path: '/materials/chart-jp',
          name: 'materials-chart-jp',
          component: () =>
            import('@/views/maritime-affairs/materials/chart/chart-jp'),
          meta: {
            title: '日版图',
            access: '日版图:列表',
          },
        },
      ],
    },
    {
      title: '本轮资料请单',
      icon: 'mdi-clipboard-list-outline',
      group: '/ship-materials',
      children: [
        {
          path: '/ship-materials/book',
          name: 'ship-book-list',
          component: () =>
            import('@/views/maritime-affairs/ship-materials/ship-book-list'),
          meta: {
            title: '本轮资料清单-书',
            access: '本轮资料清单-书:列表',
          },
        },
        {
          path: '/ship-materials/chart',
          name: 'ship-chart-list',
          component: () =>
            import('@/views/maritime-affairs/ship-materials/ship-chart-list'),
          meta: {
            title: '本轮资料清单-图',
            access: '本轮资料清单-图:列表',
          },
        },
        {
          path: '/ship-materials/apply-list',
          name: 'ship-apply-list',
          component: () =>
            import('@/views/maritime-affairs/ship-materials/ship-apply-list'),
          meta: {
            title: '本轮资料申请单',
            access: '本轮资料申请单:列表',
          },
        },
        {
          path: '/ship-materials/apply-detail/:id',
          name: 'ship-apply-detail',
          component: () =>
            import('@/views/maritime-affairs/ship-materials/ship-apply-detail'),
          meta: {
            title: '本轮资料申请单-详情',
            access: '本轮资料申请单:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '主管船资料清单',
      icon: 'mdi-clipboard-list-outline',
      group: '/sup-materials',
      children: [
        {
          path: '/sup-materials/book',
          name: 'sup-book-list',
          component: () =>
            import('@/views/maritime-affairs/sup-materials/sup-book-list'),
          meta: {
            title: '主管船资料清单-书',
            access: '主管船资料清单-书:列表',
          },
        },
        {
          path: '/sup-materials/chart',
          name: 'sup-chart-list',
          component: () =>
            import('@/views/maritime-affairs/sup-materials/sup-chart-list'),
          meta: {
            title: '主管船资料清单-图',
            access: '主管船资料清单-图:列表',
          },
        },
        {
          path: '/sup-materials/apply-list',
          name: 'sup-apply-list',
          component: () =>
            import('@/views/maritime-affairs/sup-materials/sup-apply-list'),
          meta: {
            title: '主管船资料申请单',
            access: '主管船资料申请单:列表',
          },
        },
        {
          path: '/sup-materials/apply-detail/:id',
          name: 'sup-apply-detail',
          component: () =>
            import('@/views/maritime-affairs/sup-materials/sup-apply-detail'),
          meta: {
            title: '主管船资料申请单-详情',
            access: '主管船资料申请单:列表',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '恶劣天气',
      icon: 'mdi-weather-lightning-rainy',
      group: '/bad-weather',
      children: [
        {
          path: '/bad-weather/no/list',
          name: 'bad-weather-list',
          component: () =>
            import('@/views/maritime-affairs/bad-weather/bad-weather-list'),
          meta: {
            title: '恶劣天气编号',
            access: '恶劣天气编号:列表',
          },
        },
        {
          path: '/bad-weather/no/:id',
          name: 'bad-weather-detail',
          component: () =>
            import('@/views/maritime-affairs/bad-weather/bad-weather-detail'),
          meta: {
            title: '恶劣天气编号-编辑',
            access: '恶劣天气编号:列表',
            notShowInNav: true,
          },
        },
        {
          path: '/bad-weather/affected-ship-list',
          name: 'affected-ship-list',
          component: () =>
            import('@/views/maritime-affairs/affected-ship/affected-ship-list'),
          meta: {
            title: '受影响船舶',
            access: '受影响船舶:列表',
          },
        },
        {
          path: '/bad-weather/affected-ship/new',
          name: 'affected-ship-start',
          component: () =>
            import(
              '@/views/maritime-affairs/affected-ship/affected-ship-start'
            ),
          meta: {
            title: '受影响船舶-新增',
            access: '受影响船舶:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/bad-weather/affected-ship/new-sail-detail',
          name: 'affected-start-sail-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/affected-ship/affected-start-sail-detail'
            ),
          meta: {
            title: '受影响船舶-新增船舶动态',
            access: '受影响船舶:新增船舶动态',
            notShowInNav: true,
          },
        },
        {
          path: '/bad-weather/affected-ship/:id',
          name: 'affected-ship-detail',
          component: () =>
            import(
              '@/views/maritime-affairs/affected-ship/affected-ship-detail'
            ),
          meta: {
            title: '受影响船舶-编辑',
            access: '受影响船舶:列表',

            notShowInNav: true,
          },
        },
        {
          path: '/bad-weather/total-bad-weather',
          name: 'total-bad-weather',
          component: () =>
            import('@/views/maritime-affairs/bad-weather/total-bad-weather'),
          meta: {
            title: '恶劣天气汇总',
            access: '恶劣天气汇总',
          },
        },
      ],
    },
    // {
    //   title: '安全检查',
    //   icon: 'mdi-shield-check',
    //   group: '/ship-security',
    //   children: [
    //     {
    //       path: '/ship-security-list',
    //       name: 'security-check-list',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/security-check/security-check-list'
    //         ),
    //       meta: {
    //         title: '安全检查',
    //         access: '安全检查:列表',
    //       },
    //     },
    //     {
    //       path: '/ship-security-report-1',
    //       name: 'security-check-report-1',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/security-check/security-check-report-1'
    //         ),
    //       meta: {
    //         title: '安全检查报表',
    //         access: '安全检查报表',
    //       },
    //     },
    //     {
    //       path: '/ship-security-report-2',
    //       name: 'security-check-report-2',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/security-check/security-check-report-2'
    //         ),
    //       meta: {
    //         title: '安全检查-不符合报告',
    //         access: '安全检查-不符合报告',
    //       },
    //     },
    //     {
    //       path: '/ship-security-report-3',
    //       name: 'security-check-report-3',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/security-check/security-check-report-3'
    //         ),
    //       meta: {
    //         title: '安全检查-不符合报告new',
    //         access: '不符合报告NEW',
    //       },
    //     },
    //     {
    //       path: '/ship-security-detail/:id',
    //       name: 'security-check-detail',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/security-check/security-check-detail'
    //         ),
    //       meta: {
    //         title: '安全检查-详情',
    //         access: '安全检查:列表',
    //         notShowInNav: true,
    //       },
    //     },
    //     {
    //       path: '/ship-security-total',
    //       name: 'security-check-statistic',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/security-check/security-check-statistic'
    //         ),
    //       meta: {
    //         title: '检查次数统计',
    //         access: '检查次数统计',
    //       },
    //     },
    //     {
    //       path: '/ship-security-question-total',
    //       name: 'security-check-ques-stat',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/security-check/security-check-ques-stat'
    //         ),
    //       meta: {
    //         title: '检查项目详细统计',
    //         access: '检查项目详细统计',
    //       },
    //     },
    //     {
    //       path: '/ship-security-dept-report-list-inspection',
    //       name: 'dept-report-list-inspection',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/ship-accident/dept-report-list-inspection'
    //         ),
    //       meta: {
    //         title: '港口国/船旗国检查报告',
    //         access: '港口国/船旗国检查报告',
    //       },
    //     },
    //     {
    //       path: '/ship-security-report-1new',
    //       name: 'security-check-report-1new',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/security-check/security-check-report-1new'
    //         ),
    //       meta: {
    //         title: '安全检查报表NEW',
    //         access: '安全检查报表NEW',
    //       },
    //     },
    //     {
    //       path: '/ship-security-dept_inspection/:id',
    //       name: 'dept-report-inspection-detail',
    //       component: () =>
    //         import(
    //           '@/views/ism-management/report/dept-report-inspection-detail'
    //         ),
    //       meta: {
    //         title: '港口国/船旗国检查报告',
    //         access: '港口国/船旗国检查报告',
    //         notShowInNav: true,
    //       },
    //     },
    //     {
    //       path: '/ship-dept-security-check/:id',
    //       name: 'dept-report-security-check',
    //       component: () =>
    //         import('@/views/ism-management/report/dept-report-security-check'),
    //       meta: {
    //         title: '安全检查报表NEW',
    //         access: '安全检查报表NEW',
    //         notShowInNav: true,
    //       },
    //     },
    //   ],
    // },
    // {
    //   title: '船舶事故',
    //   icon: 'mdi-sail-boat-sink',
    //   group: '/ship-accident',
    //   children: [
    //     {
    //       path: '/ship-accident-list',
    //       name: 'accident-list',
    //       component: () =>
    //         import('@/views/maritime-affairs/ship-accident/accident-list'),
    //       meta: {
    //         title: '船舶事故记录',
    //         access: '船舶事故记录:列表',
    //       },
    //     },
    //     {
    //       path: '/ship-accident-total',
    //       name: 'accident-total',
    //       component: () =>
    //         import('@/views/maritime-affairs/ship-accident/accident-total'),
    //       meta: {
    //         title: '船舶事故统计',
    //         access: '船舶事故统计',
    //       },
    //     },
    //     {
    //       path: '/ship-accident-report-list',
    //       name: 'accident-report-list',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/ship-accident/accident-report-list'
    //         ),
    //       meta: {
    //         title: '船舶事故-不符合报告审批',
    //         access: '船舶事故-不符合报告',
    //       },
    //     },
    //     {
    //       path: '/ship-accident-report-list-new',
    //       name: 'accident-report-list-new',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/ship-accident/accident-report-list-new'
    //         ),
    //       meta: {
    //         title: '船舶事故-不符合报告new',
    //         access: '不符合报告NEW',
    //       },
    //     },
    //     {
    //       path: '/ship-accident-report2-list',
    //       name: 'accident-report2-list',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/ship-accident/accident-report2-list'
    //         ),
    //       meta: {
    //         title: '海损机损事故报告审批',
    //         access: '船舶事故-事故报告:列表',
    //       },
    //     },
    //     {
    //       path: '/ship-accident/:id',
    //       name: 'accident-detail',
    //       component: () =>
    //         import('@/views/maritime-affairs/ship-accident/accident-detail'),
    //       meta: {
    //         title: '船舶事故-详情',
    //         access: '船舶事故记录:列表',
    //         notShowInNav: true,
    //       },
    //     },
    //     {
    //       path: '/ship-accident-sysreport/:id',
    //       name: 'accident-report-detail',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/ship-accident/sys-report/accident-report-detail'
    //         ),
    //       meta: {
    //         title: '机损海损事故报告',
    //         access: '船舶事故-事故报告:列表',

    //         notShowInNav: true,
    //       },
    //     },
    //     {
    //       path: '/ship-accident-casualties/list',
    //       name: 'accident-casualties-list',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/ship-accident/accident-casualties-list'
    //         ),
    //       meta: {
    //         title: '船舶事故-人身伤亡事故报告审批',
    //         access: '事故人员伤亡报表:列表',
    //       },
    //     },
    //     {
    //       path: '/ship-accident-casualties-sysreport/:id',
    //       name: 'casualties-report-detail',
    //       component: () =>
    //         import(
    //           '@/views/maritime-affairs/ship-accident/sys-report/casualties-report-detail'
    //         ),
    //       meta: {
    //         title: '船舶事故-人身伤亡事故报告审批',
    //         access: '事故人员伤亡报表:列表',

    //         notShowInNav: true,
    //       },
    //     },
    //   ],
    // },
  ],
}
