export default {
  path: '/maritime-maintence',
  title: '机务管理',
  icon: 'mdi-hammer-wrench',
  access: '机务管理',
  children: [
    {
      title: '备件管理',
      icon: 'mdi-cogs',
      group: /spare-parts|engine-manage/,
      children: [
        {
          path: '/engine-manage/equipment-list',
          name: 'engine-list',
          component: () =>
            import('@/views/maritime-maintence/spare-part/engine/engine-list'),
          meta: {
            title: '设备主体管理',
            access: '设备主体管理',
          },
        },
        {
          path: '/engine-manage/sub-equipment-list',
          name: 'sub-engine-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/engine/sub-engine-list'
            ),
          meta: {
            title: '子设备管理',
            access: '子设备管理',
          },
        },
        {
          path: '/engine-manage/sub-equipment-detail/:id',
          name: 'sub-engine-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/engine/sub-engine-detail'
            ),
          meta: {
            title: '子设备详情',
            access: '子设备管理',
            notShowInNav: true,
          },
        },
        {
          path: '/spare-parts/apply-list',
          name: 'spare-apply-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/apply/spare-apply-list'
            ),
          meta: {
            title: '备件申请',
            access: '备件申请',
          },
        },
        {
          path: '/spare-parts/apply-detail/:id',
          name: 'spare-apply-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/apply/spare-apply-detail'
            ),
          meta: {
            title: '备件申请详情',
            access: '备件申请',
            notShowInNav: true,
          },
        },
        {
          path: '/spare-parts/enquiry-list',
          name: 'spare-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/enquiry/spare-enquiry-list'
            ),
          meta: {
            title: '备件询价',
            access: '备件询价',
          },
        },
        {
          path: '/spare-parts/enquiry-detail/:id',
          name: 'spare-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/enquiry/spare-enquiry-detail'
            ),
          meta: {
            title: '备件询价详情',
            access: '备件询价',
            notShowInNav: true,
          },
        },
        // {
        //   path: '/spare-parts/union-price-list',
        //   name: 'union-price-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/spare-part/union-price/union-price-list'
        //     ),
        //   meta: {
        //     title: '批量报价',
        //     access: '备件批量报价',
        //   },
        // },
        // {
        //   path: '/spare-parts/union-price-new',
        //   name: 'union-price-detail',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/spare-part/union-price/union-price-detail'
        //     ),
        //   meta: {
        //     title: '批量报价-详情',
        //     access: '备件批量报价',
        //     notShowInNav: true,
        //   },
        // },
        {
          path: '/spare-parts/quote-list',
          name: 'spare-quote-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/quote/spare-quote-list'
            ),
          meta: {
            title: '备件报价',
            access: '备件报价',
          },
        },
        {
          path: '/spare-parts/quote-detail/:id',
          name: 'spare-quote-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/quote/spare-quote-detail'
            ),
          meta: {
            title: '备件报价详情',
            access: '备件报价',
            notShowInNav: true,
          },
        },
        {
          path: '/spare-parts/order-list',
          name: 'spare-order-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/order/spare-order-list'
            ),
          meta: {
            title: '备件订单',
            access: '备件订单',
          },
        },
        {
          path: '/spare-parts/order-detail/:id',
          name: 'spare-order-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/order/spare-order-detail'
            ),
          meta: {
            title: '备件订单详情',
            access: '备件订单',
            notShowInNav: true,
          },
        },
        {
          path: '/spare-parts/order-new',
          name: 'spare-order-new',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/order/spare-order-new'
            ),
          meta: {
            title: '备件订单详情',
            access: '备件订单',
            notShowInNav: true,
          },
        },
        {
          path: '/spare-parts/in-list',
          name: 'spare-in-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/stock-in/spare-in-list'
            ),
          meta: {
            title: '备件入库',
            access: '备件入库',
          },
        },
        {
          path: '/spare-parts/in-detail/:id',
          name: 'spare-in-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/stock-in/spare-in-detail'
            ),
          meta: {
            title: '备件入库详情',
            access: '备件入库',
            notShowInNav: true,
          },
        },
        {
          path: '/spare-parts/reduce-in-detail/:id',
          name: 'spare-reduce-in-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/stock-in/spare-reduce-in-detail'
            ),
          meta: {
            title: '备件调减入库详情',
            access: '备件入库',
            notShowInNav: true,
          },
        },
        {
          path: '/spare-parts/stock-list',
          name: 'spare-stock-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/stock/spare-stock-list'
            ),
          meta: {
            title: '备件库存',
            access: '备件库存',
          },
        },
        {
          path: '/spare-parts/out-list',
          name: 'spare-out-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/stock-out/spare-out-list'
            ),
          meta: {
            title: '备件消耗',
            access: '备件消耗',
          },
        },
        {
          path: '/spare-parts/out-detail/:id',
          name: 'spare-out-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/stock-out/spare-out-detail'
            ),
          meta: {
            title: '备件消耗-详细',
            access: '备件消耗',
            notShowInNav: true,
          },
        },
        {
          path: '/spare-parts/stock-check',
          name: 'stock-check-list',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/stock-check/stock-check-list'
            ),
          meta: {
            title: '备件盘点',
            access: '备件盘点',
          },
        },
        {
          path: '/spare-parts/stock-check-detail/:id',
          name: 'stock-check-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/spare-part/stock-check/stock-check-detail'
            ),
          meta: {
            title: '备件盘点详情',
            access: '备件盘点',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '滑油管理',
      icon: 'mdi-water-sync',
      group: '/lub-oil',
      children: [
        {
          path: '/lub-oil/lub-oil-list',
          name: 'slide-oil-list',
          component: () =>
            import('@/views/common-business/slide-oil/slide-oil-list'),
          meta: {
            title: '滑油基础信息',
            access: '滑油基础信息',
          },
        },
        {
          path: '/lub-oil/lub-oil-distribution',
          name: 'slide-oil-distribution',
          component: () =>
            import('@/views/common-business/slide-oil/slide-oil-distribution'),
          meta: {
            title: '滑油分配列表',
            access: '滑油分配列表',
          },
        },
        {
          path: '/lub-oil/soil-apply/list',
          name: 'soil-apply-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/apply/soil-apply-list'
            ),
          meta: {
            title: '滑油申请',
            access: '滑油申请',
          },
        },
        {
          path: '/lub-oil/soil-apply/detail/:id',
          name: 'soil-apply-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/apply/soil-apply-detail'
            ),
          meta: {
            title: '滑油申请-详情',
            access: '滑油申请',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/soil-enquiry/list',
          name: 'soil-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/enquiry/soil-enquiry-list'
            ),
          meta: {
            title: '滑油询价',
            access: '滑油询价',
          },
        },
        {
          path: '/lub-oil/soil-enquiry/detail/:id',
          name: 'soil-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/enquiry/soil-enquiry-detail'
            ),
          meta: {
            title: '滑油询价-详情',
            access: '滑油询价',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/union-price-list',
          name: 'soil-union-price-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/union-price/union-price-list'
            ),
          meta: {
            title: '滑油批量报价',
            access: '滑油批量报价',
          },
        },
        {
          path: '/lub-oil/union-price-new',
          name: 'soil-union-price-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/union-price/union-price-detail'
            ),
          meta: {
            title: '滑油批量报价-详情',
            access: '滑油批量报价',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/quote-list',
          name: 'soil-quote-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/quote/soil-quote-list'
            ),
          meta: {
            title: '滑油报价',
            access: '滑油报价',
          },
        },
        {
          path: '/lub-oil/quote-detail/:id',
          name: 'soil-quote-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/quote/soil-quote-detail'
            ),
          meta: {
            title: '滑油报价详情',
            access: '滑油报价',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/order-list',
          name: 'soil-order-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/order/soil-order-list'
            ),
          meta: {
            title: '滑油订单',
            access: '滑油订单',
          },
        },
        {
          path: '/lub-oil/order-detail/:id',
          name: 'soil-order-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/order/soil-order-detail'
            ),
          meta: {
            title: '滑油订单详情',
            access: '滑油订单',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/order-new',
          name: 'soil-order-new',
          component: () =>
            import('@/views/maritime-maintence/slide-oil/order/soil-order-new'),
          meta: {
            title: '滑油订单详情',
            notShowInNav: true,
            access: '滑油订单新增',
          },
        },
        {
          path: '/lub-oil/stock-list',
          name: 'soil-stock-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/stock/soil-stock-list'
            ),
          meta: {
            title: '滑油库存',
            access: '滑油库存',
          },
        },
        {
          path: '/lub-oil/in-list',
          name: 'soil-in-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/stock-in/soil-in-list'
            ),
          meta: {
            title: '滑油入库',
            access: '滑油入库',
          },
        },
        {
          path: '/lub-oil/in-detail/:id',
          name: 'soil-in-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/stock-in/soil-in-detail'
            ),
          meta: {
            title: '滑油入库详情',
            access: '滑油入库',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/reduce-in-detail/:id',
          name: 'soil-reduce-in-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/stock-in/soil-reduce-in-detail'
            ),
          meta: {
            title: '滑油调减入库详情',
            access: '滑油入库',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/out-list',
          name: 'soil-out-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/stock-out/soil-out-list'
            ),
          meta: {
            title: '滑油消耗',
            access: '滑油消耗',
          },
        },
        {
          path: '/lub-oil/out-detail/:id',
          name: 'soil-out-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/stock-out/soil-out-detail'
            ),
          meta: {
            title: '滑油消耗-详细',
            access: '滑油消耗',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/stock-check',
          name: 'soil-stock-check-list',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/stock-check/soil-check-list'
            ),
          meta: {
            title: '滑油盘点',
            access: '滑油盘点',
          },
        },
        {
          path: '/lub-oil/stock-check-detail/:id',
          name: 'soil-check-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/slide-oil/stock-check/soil-check-detail'
            ),
          meta: {
            title: '滑油盘点详情',
            access: '滑油盘点',
            notShowInNav: true,
          },
        },
        {
          path: '/lub-oil/grease-consum-list',
          name: 'grease-consum-list',
          component: () =>
            import(
              '@/views/maritime-maintence/system-reports/grease-consum/grease-consum-list'
            ),
          meta: {
            title: '滑油月度报表',
            access: '滑油消耗:列表',
          },
        },
      ],
    },
    {
      title: '物料管理',
      icon: 'mdi-format-paint',
      group: '/materials',
      children: [
        {
          path: '/materials/materials-type-list',
          name: 'materials-type-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/materials-manage/materials-type-list'
            ),
          meta: {
            title: '物料分类信息',
            access: '物料分类信息',
          },
        },
        {
          path: '/materials/materials-list',
          name: 'materials-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/materials-manage/materials-list'
            ),
          meta: {
            title: '物料信息',
            access: '物料信息',
          },
        },
        {
          path: '/materials/materials-apply/list',
          name: 'materials-apply-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/apply/materials-apply-list'
            ),
          meta: {
            title: '物料申请',
            access: '物料申请',
          },
        },
        {
          path: '/materials/materials-apply/detail/:id',
          name: 'materials-apply-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/apply/materials-apply-detail'
            ),
          meta: {
            title: '物料申请-详情',
            notShowInNav: true,
            access: '物料申请',
          },
        },
        {
          path: '/materials/materials-apply/detailExcel/:id',
          name: 'materials-apply-detail-excel',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/apply/materials-apply-detail-excel'
            ),
          meta: {
            title: '物料申请-详情',
            notShowInNav: true,
            access: '物料申请',
          },
        },
        {
          path: '/materials/materials-enquiry/list',
          name: 'materials-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/enquiry/materials-enquiry-list'
            ),
          meta: {
            title: '物料询价',
            access: '物料询价',
          },
        },
        {
          path: '/materials/materials-enquiry/detail/:id',
          name: 'materials-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/enquiry/materials-enquiry-detail'
            ),
          meta: {
            title: '物料询价-详情',
            notShowInNav: true,
            access: '物料询价',
          },
        },
        {
          path: '/materials/materials-quote/list',
          name: 'materials-quote-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/quote/materials-quote-list'
            ),
          meta: {
            title: '物料报价',
            access: '物料报价',
          },
        },
        {
          path: '/materials/materials-quote/detail/:id',
          name: 'materials-quote-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/quote/materials-quote-detail'
            ),
          meta: {
            title: '物料报价详情',
            notShowInNav: true,
            access: '物料报价',
          },
        },
        {
          path: '/materials/materials-union-price/list',
          name: 'materials-union-price-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/union-price/materials-union-price-list'
            ),
          meta: {
            title: '物料批量报价',
            access: '物料批量报价',
          },
        },
        {
          path: '/materials/materials-union-price/new',
          name: 'materials-union-price-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/union-price/materials-union-price-detail'
            ),
          meta: {
            title: '物料批量报价',
            notShowInNav: true,
            access: '物料批量报价',
          },
        },
        {
          path: '/materials/materials-order/list',
          name: 'materials-order-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/order/materials-order-list'
            ),
          meta: {
            title: '物料订单',
            access: '物料订单',
          },
        },
        {
          path: '/materials/materials-order/detail/:id',
          name: 'materials-order-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/order/materials-order-detail'
            ),
          meta: {
            title: '物料订单详情',
            notShowInNav: true,
            access: '物料订单',
          },
        },
        {
          path: '/materials/order-new',
          name: 'materials-order-new',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/order/materials-order-new'
            ),
          meta: {
            title: '物料订单详情',
            notShowInNav: true,
            access: '物料订单新增',
          },
        },
        {
          path: '/materials/materials-in/list',
          name: 'materials-in-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/stock-in/materials-in-list'
            ),
          meta: {
            title: '物料入库',
            access: '物料入库',
          },
        },
        {
          path: '/materials/materials-in/detail/:id',
          name: 'materials-in-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/stock-in/materials-in-detail'
            ),
          meta: {
            title: '物料入库详情',
            notShowInNav: true,
            access: '物料入库',
          },
        },
        {
          path: '/materials/materials-reduce-in/detail/:id',
          name: 'materials-reduce-in-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/stock-in/materials-reduce-in-detail'
            ),
          meta: {
            title: '物料入库详情',
            notShowInNav: true,
            access: '物料入库',
          },
        },
        {
          path: '/materials/materials-stock-list',
          name: 'materials-stock-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/stock/materials-stock-list'
            ),
          meta: {
            title: '物料库存',
            access: '物料库存',
          },
        },
        {
          path: '/materials/out-list',
          name: 'materials-out-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/stock-out/materials-out-list'
            ),
          meta: {
            title: '物料消耗',
            access: '物料消耗',
          },
        },
        {
          path: '/materials/out-detail/:id',
          name: 'materials-out-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/stock-out/materials-out-detail'
            ),
          meta: {
            title: '物料消耗详情',
            notShowInNav: true,
            access: '物料消耗',
          },
        },
        {
          path: '/materials/materials-stock-check-detail/:id',
          name: 'materials-stock-check-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/stock-check/materials-stock-check-detail'
            ),
          meta: {
            title: '物料盘点详情',
            notShowInNav: true,
            access: '物料盘点',
          },
        },
        {
          path: '/materials/materials-stock-check-list',
          name: 'materials-stock-check-list',
          component: () =>
            import(
              '@/views/maritime-maintence/materials/stock-check/materials-stock-check-list'
            ),
          meta: {
            title: '物料盘点',
            access: '物料盘点',
          },
        },
      ],
    },
    {
      title: '维护保养',
      icon: 'mdi-wrench-outline',
      group: '/maintenance',
      children: [
        {
          path: '/maintenance/yearplan',
          name: 'yearplan-list',
          component: () =>
            import('@/views/maritime-maintence/repair/yearplan-list'),
          meta: {
            title: '年度计划管理',
            access: '年度计划管理',
          },
        },
        {
          path: '/maintenance/components/yearplan-detail/:id',
          name: 'yearplan-detail',
          component: () =>
            import('@/views/maritime-maintence/repair/yearplan-detail'),
          meta: {
            title: '年度计划详情',
            access: '年度计划管理',
            notShowInNav: true,
          },
        },
        {
          path: '/maintenance/monthplan-list/monthplan',
          name: 'monthplan-list',
          component: () =>
            import('@/views/maritime-maintence/repair/monthplan-list'),
          meta: {
            title: '月度计划管理',
            access: '月度计划管理',
          },
        },
        //修改绑定了两个
        {
          path: '/maintenance/monthplan-detail/:id',
          name: 'monthplan-detail',
          component: () =>
            import('@/views/maritime-maintence/repair/monthplan-detail'),
          meta: {
            title: '月度计划详情',
            access: '月度计划管理',
            notShowInNav: true,
          },
        },
        {
          path: '/maintenance/oldrepair',
          name: 'oldrepair-list',
          component: () =>
            import('@/views/maritime-maintence/repair/oldrepair-list'),
          meta: {
            title: '维护保养历史记录',
            access: '维护保养历史记录',
          },
        },
        // {
        //   path: '/maintenance/equipment-init-list',
        //   name: 'equipment-init-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/equipment/equipment-init/equipment-init-list.vue'
        //     ),
        //   meta: {
        //     title: '初始化设备',
        //     access: '初始化项目',
        //   },
        // },
        // {
        //   path: '/maintenance/equipment-init-items/:shipCode/:equipmentName',
        //   name: 'equipment-init-items',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/equipment/equipment-init/equipment-init-items.vue'
        //     ),
        //   meta: {
        //     title: '待初始化项目',
        //     access: '待初始化项目',
        //     notShowInNav: true,
        //   },
        // },
        {
          path: '/maintenance/meter-reading-list',
          name: 'meter-reading-list',
          component: () =>
            import(
              '@/views/maritime-maintence/equipment/meter-reading/meter-reading-list.vue'
            ),
          meta: {
            title: '抄表管理',
            access: '抄表管理',
          },
        },
        {
          path: '/maintenance/equipment-management',
          name: 'equipment-management',
          component: () =>
            import(
              '@/views/maritime-maintence/equipment/equipment-search/equipment-management.vue'
            ),
          meta: {
            title: '历史抄表查询',
            access: '历史抄表查询',
            notShowInNav: true,
          },
        },
      ],
    },
    {
      title: '维修管理',
      icon: 'mdi-account-hard-hat-outline',
      group: '/repair-manage',
      children: [
        {
          path: '/repair-manage/self-repair-item',
          name: 'self-repair-item-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/self-repair/self-repair-item-list'
            ),
          meta: {
            title: '自修项目清单',
            access: '自修项目清单',
          },
        },
        // {
        //   path: '/repair-manage/self-repair-list',
        //   name: 'self-repair-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/repair-manage/self-repair/self-repair-list'
        //     ),
        //   meta: {
        //     title: '自修申请',
        //     access: '自修管理',
        //   },
        // },
        // {
        //   path: '/repair-manage/self-repair/:id',
        //   name: 'self-repair-detail',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/repair-manage/self-repair/self-repair-detail'
        //     ),
        //   meta: {
        //     title: '自修申请详情',
        //     access: '自修管理',
        //     notShowInNav: true,
        //   },
        // },
        {
          path: '/repair-manage/self-repair-bonus',
          name: 'self-repair-bonus',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/self-repair/self-repair-bonus'
            ),
          meta: {
            title: '自修奖申请',
            access: '自修奖申请',
          },
        },
        {
          path: '/repair-manage/self-repair-bonus-detail/:id',
          name: 'self-repair-bonus-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/self-repair/self-repair-bonus-detail'
            ),
          meta: {
            notShowInNav: true,
            title: '自修奖申请',
            access: '自修奖申请',
          },
          //代码跟界面对不上
        },
        {
          path: '/repair-manage/self-bonus-distribute-list',
          name: 'self-bonus-distribute-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/self-repair/self-bonus-distribute-list'
            ),
          meta: {
            title: '自修奖分配',
            access: '自修奖分配',
          },
        },
        {
          path: '/repair-manage/self-bonus-distribute-detail/:id',
          name: 'self-bonus-distribute-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/self-repair/self-bonus-distribute-detail'
            ),
          meta: {
            notShowInNav: true,
            title: '自修奖分配',
            access: '自修奖分配',
          },
        },
        {
          path: '/repair-manage/voyage-repair-apply-list',
          name: 'voyage-repair-apply-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/apply/voyage-repair-apply-list'
            ),
          meta: {
            title: '航修申请',
            access: '航修申请',
          },
        },
        {
          path: '/repair-manage/voyage-repair-apply-detail/:id',
          name: 'voyage-repair-apply-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/apply/voyage-repair-apply-detail'
            ),
          meta: {
            title: '航修申请详情',
            notShowInNav: true,
            access: '航修申请',
          },
        },
        {
          path: '/repair-manage/voyage-enquiry-list',
          name: 'voyage-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/enquiry/voyage-enquiry-list'
            ),
          meta: {
            title: '航修询价',
            access: '航修询价',
          },
        },
        {
          path: '/repair-manage/voyage-enquiry-detail/:id',
          name: 'voyage-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/enquiry/voyage-enquiry-detail'
            ),
          meta: {
            title: '航修询价详情',
            notShowInNav: true,
            access: '航修询价',
          },
        },
        {
          path: '/repair-manage/voyage-quote-list',
          name: 'voyage-quote-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/quote/voyage-quote-list'
            ),
          meta: {
            title: '航修报价',
            access: '航修报价',
          },
        },
        {
          path: '/repair-manage/voyage-quote-detail/:id',
          name: 'voyage-quote-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/quote/voyage-quote-detail'
            ),
          meta: {
            title: '航修报价详情',
            notShowInNav: true,
            access: '航修报价',
          },
        },
        {
          path: '/repair-manage/voyage-repair-list',
          name: 'voyage-repair-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/repair/voyage-repair-list'
            ),
          meta: {
            title: '航修订单',
            access: '航修修理单',
          },
        },
        {
          path: '/repair-manage/voyage-repair-detail/:id',
          name: 'voyage-repair-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/repair/voyage-repair-detail'
            ),
          meta: {
            title: '航修订单详情',
            notShowInNav: true,
            access: '航修修理单',
          },
        },
        {
          path: '/repair-manage/voyage-complete-list',
          name: 'voyage-complete-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/complete/voyage-complete-list'
            ),
          meta: {
            title: '航修完工单',
            access: '航修完工单',
          },
        },
        {
          path: '/repair-manage/voyage-complete-detail/:id',
          name: 'voyage-complete-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/voyage-repair/complete/voyage-complete-detail'
            ),
          meta: {
            title: '航修完工单详情',
            notShowInNav: true,
            access: '航修完工单',
          },
        },
      ],
    },
    {
      title: '坞修管理',
      icon: 'mdi-pier-crane',
      group: '/dock-manage',
      children: [
        {
          path: '/dock-manage/item-listType',
          name: 'dock-repair-itemType',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/_item/dock-repair-itemType'
            ),
          meta: {
            title: '坞修项目分类',
            access: '坞修项目分类',
          },
        },
        {
          path: '/dock-manage/item-list',
          name: 'dock-repair-item',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/_item/dock-repair-item'
            ),
          meta: {
            title: '坞修项目管理',
            access: '坞修项目管理',
          },
        },
        {
          path: '/dock-manage/apply-list',
          name: 'dock-repair-apply-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/apply/dock-repair-apply-list'
            ),
          meta: {
            title: '坞修申请',
            access: '坞修申请',
          },
        },
        {
          path: '/dock-manage/apply-detail/:id',
          name: 'dock-repair-apply-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/apply/dock-repair-apply-detail'
            ),
          meta: {
            title: '坞修申请详情',
            notShowInNav: true,
            access: '坞修申请',
          },
        },
        {
          path: '/dock-manage/enquiry-list',
          name: 'dock-enquiry-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/enquiry/dock-enquiry-list'
            ),
          meta: {
            title: '坞修询价',
            access: '坞修询价',
          },
        },
        {
          path: '/dock-manage/enquiry-detail/:id',
          name: 'dock-enquiry-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/enquiry/dock-enquiry-detail'
            ),
          meta: {
            title: '坞修询价详情',
            notShowInNav: true,
            access: '坞修询价',
          },
        },
        {
          path: '/dock-manage/dock-quote-list',
          name: 'dock-quote-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/quote/dock-quote-list'
            ),
          meta: {
            title: '坞修报价',
            access: '坞修报价',
          },
        },
        {
          path: '/dock-manage/dock-quote-detail/:id',
          name: 'dock-quote-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/quote/dock-quote-detail'
            ),
          meta: {
            title: '坞修报价详情',
            notShowInNav: true,
            access: '坞修报价',
          },
        },
        {
          path: '/dock-manage/dock-repair-list',
          name: 'dock-repair-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/repair/dock-repair-list'
            ),
          meta: {
            title: '坞修修理单',
            access: '坞修修理单',
          },
        },
        {
          path: '/dock-manage/dock-repair-detail/:id',
          name: 'dock-repair-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/repair/dock-repair-detail'
            ),
          meta: {
            title: '坞修修理单详情',
            notShowInNav: true,
            access: '坞修修理单',
          },
        },
        // {
        //   path: '/dock-manage/dock-repair-add-list',
        //   name: 'dock-repair-add-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/repair-manage/dock-repair/repair-add/dock-repair-add-list'
        //     ),
        //   meta: {
        //     title: '坞修增量工程',
        //     access: '坞修增量工程',
        //   },
        // },
        // {
        //   path: '/dock-manage/dock-repair-add-detail/:id',
        //   name: 'dock-repair-add-detail',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/repair-manage/dock-repair/repair-add/dock-repair-add-detail'
        //     ),
        //   meta: {
        //     title: '坞修增量工程详情',
        //     notShowInNav: true,
        //     access: '坞修增量工程',
        //   },
        // },
        {
          path: '/dock-manage/dock-complete-list',
          name: 'dock-complete-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/complete/dock-complete-list'
            ),
          meta: {
            title: '坞修完工单',
            access: '坞修完工单',
          },
        },
        {
          path: '/dock-manage/dock-complete-new',
          name: 'dock-complete-new',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/complete/dock-complete-new'
            ),
          meta: {
            title: '坞修完工单新增',
            notShowInNav: true,
            access: '坞修完工单',
          },
        },
        {
          path: '/dock-manage/dock-complete-detail/:id',
          name: 'dock-complete-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/complete/dock-complete-detail'
            ),
          meta: {
            title: '坞修完工单详情',
            notShowInNav: true,
            access: '坞修完工单',
          },
        },
        {
          path: '/dock-manage/dock-settlement-list',
          name: 'dock-settlement-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/settlement/dock-settlement-list'
            ),
          meta: {
            title: '坞修结算单',
            access: '坞修结算单',
          },
        },
        {
          path: '/dock-manage/dock-settlement-detail/:id',
          name: 'dock-settlement-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/settlement/dock-settlement-detail'
            ),
          meta: {
            title: '坞修结算单',
            notShowInNav: true,
            access: '坞修结算单',
          },
        },
        {
          path: '/dock-manage/dock-settlement-new',
          name: 'dock-settlement-new',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/settlement/dock-settlement-new'
            ),
          meta: {
            title: '坞修结算单详情',
            notShowInNav: true,
            access: '坞修结算单',
          },
        },
        {
          path: '/dock-manage/dock-final-accounts-list',
          name: 'dock-final-accounts-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/final-accounts/dock-final-accounts-list'
            ),
          meta: {
            title: '坞修预决算',
            access: '坞修预决算',
          },
        },
        {
          path: '/dock-manage/dock-final-accounts-detail/:id',
          name: 'dock-final-accounts-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/final-accounts/dock-final-accounts-detail'
            ),
          meta: {
            title: '坞修预决算详情',
            notShowInNav: true,
            access: '坞修预决算',
          },
        },
        {
          path: '/dock-manage/dock-plan-list',
          name: 'dock-plan-list',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/year-plan/dock-plan-list'
            ),
          meta: {
            title: '年度坞修计划',
            access: '年度坞修计划',
          },
        },
        {
          path: '/dock-manage/dock-plan-detail/:id',
          name: 'dock-plan-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/repair-manage/dock-repair/year-plan/dock-plan-detail'
            ),
          meta: {
            title: '年度坞修计划详情',
            notShowInNav: true,
            access: '年度坞修计划',
          },
        },
      ],
    },
    // {
    //   title: '预算管理审批',
    //   icon: 'mdi-purse-outline',
    //   group: '/purchase-manage',
    //   children: [
    //     // {
    //     //   path: '/purchase-manage/common',
    //     //   name: 'common-purchase-list',
    //     //   component: () =>
    //     //     import(
    //     //       '@/views/maritime-maintence/purchase-manage/common-purchase-list'
    //     //     ),
    //     //   meta: {
    //     //     title: '常规供应审批',
    //     //     access: '常规供应审批',
    //     //   },
    //     // },
    //     // {
    //     //   path: '/purchase-manage/common/:id',
    //     //   name: 'common-purchase-detail',
    //     //   component: () =>
    //     //     import(
    //     //       '@/views/maritime-maintence/purchase-manage/common-purchase-detail'
    //     //     ),
    //     //   meta: {
    //     //     title: '常规供应详情',
    //     //     access: '常规供应审批',
    //     //     notShowInNav: true,
    //     //   },
    //     // },
    //     {
    //       path: '/purchase-manage/large',
    //       name: 'large-purchase-list',
    //       component: () =>
    //         import(
    //           '@/views/maritime-maintence/purchase-manage/large-purchase-list'
    //         ),
    //       meta: {
    //         title: '预算管理审批',
    //         access: '预算管理审批',
    //       },
    //     },
    //     {
    //       path: '/purchase-manage/large/:id',
    //       name: 'large-purchase-detail',
    //       component: () =>
    //         import(
    //           '@/views/maritime-maintence/purchase-manage/large-purchase-detail'
    //         ),
    //       meta: {
    //         title: '预算管理审批详情',
    //         access: '预算管理审批',
    //         notShowInNav: true,
    //       },
    //     },
    //   ],
    // },
    {
      title: 'SAP管理',
      icon: 'mdi-alpha-s-box-outline',
      group: '/purchase-sap',
      children: [
        {
          path: '/purchase-sap/component-compare',
          name: 'component-compare',
          component: () =>
            import('@/views/maritime-maintence/purchase-sap/component-compare'),
          meta: {
            title: 'SAP库存对照',
            access: 'SAP库存对照',
          },
        },
      ],
    },
    {
      title: '机务体系管理',
      icon: 'mdi-file-chart-outline',
      group: '/main-sysreport',
      children: [
        {
          path: '/main-sysreport/fuel-report-list',
          name: 'fuel-report-list',
          component: () =>
            import(
              '@/views/maritime-maintence/system-reports/fuel-oil/fuel-report-list'
            ),
          meta: {
            title: '燃油报表管理',
            access: '燃油报表管理:列表',
          },
        },
        {
          path: '/main-sysreport/fuel-report-detail/:id',
          name: 'fuel-report-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/system-reports/fuel-oil/fuel-report-detail'
            ),
          meta: {
            title: '燃油报表详情',
            notShowInNav: true,
            access: '燃油报表管理:列表',
          },
        },
        {
          path: '/main-sysreport/fresh-water-list',
          name: 'fresh-water-list',
          component: () =>
            import(
              '@/views/maritime-maintence/system-reports/fresh-water/fresh-water-list'
            ),
          meta: {
            title: '淡水管理',
            access: '淡水管理:列表',
          },
        },
        // {
        //   path: '/main-sysreport/water-addition-list',
        //   name: 'water-addition-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/system-reports/water-addition/water-addition-list'
        //     ),
        //   meta: {
        //     title: '淡水加装',
        //     access: '淡水加装:列表',
        //   },
        // },
        {
          path: '/main-sysreport/garbage-accept-list',
          name: 'garbage-accept-list',
          component: () =>
            import(
              '@/views/maritime-maintence/system-reports/water-addition/garbage-accept-list'
            ),
          meta: {
            title: '垃圾接收',
            access: '垃圾接收:列表',
          },
        },
        // {
        //   path: '/main-sysreport/grease-consum-list',
        //   name: 'grease-consum-list',
        //   component: () =>
        //     import(
        //       '@/views/maritime-maintence/system-reports/grease-consum/grease-consum-list'
        //     ),
        //   meta: {
        //     title: '滑油月度报表',
        //     access: '滑油消耗:列表',
        //   },
        // },
        {
          path: '/main-sysreport/grease-consum-detail/:id',
          name: 'grease-consum-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/system-reports/grease-consum/grease-consum-detail'
            ),
          meta: {
            title: '滑油月度报表-详细',
            notShowInNav: true,
            access: '滑油消耗:列表',
          },
        },
      ],
    },
    {
      title: '机务日常工作报告',
      icon: 'mdi-file-chart-outline',
      group: '/ship-daily-work',
      children: [
        {
          path: '/ship-daily-work/ship-daily-work-list',
          name: 'ship-daily-work-list',
          component: () =>
            import(
              '@/views/maritime-maintence/ship-daily-work/ship-daily-work-list'
            ),
          meta: {
            title: '机务日常工作报告',
            access: '机务日常工作报告:列表',
          },
        },
        {
          path: '/ship-daily-work/ship-daily-work-detail/:id',
          name: 'ship-daily-work-detail',
          component: () =>
            import(
              '@/views/maritime-maintence/ship-daily-work/ship-daily-work-detail'
            ),
          meta: {
            title: '机务日常工作报告',
            notShowInNav: true,
            access: '机务日常工作报告:列表',
          },
        },
        {
          path: '/ship-daily-work/important-work-list',
          name: 'important-work-list',
          component: () =>
            import(
              '@/views/maritime-maintence/ship-daily-work/important-work-list'
            ),
          meta: {
            title: '重要工作跟踪',
            access: '重要工作跟踪:列表',
          },
        },
      ],
    },
  ],
}
