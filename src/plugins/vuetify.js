import '@mdi/font/css/materialdesignicons.css' // Ensure you are using css-loader
import Vue from 'vue'
import Vuetify from 'vuetify'
import 'vuetify/dist/vuetify.min.css'
import zhHans from 'vuetify/lib/locale/zh-Hans'
import {
  VTreeSelect,
  VCascader,
  VDataGridSelect,
  VmdView,
} from 'vuetify-toolkit/vuetify-toolkit.umd'

Vue.use(Vuetify, {
  VTreeSelect,
  VCascader,
  VDataGridSelect,
  VmdView,
})

export default new Vuetify({
  theme: {
    themes: {
      light: {
        primary: '#3399CC',
        secondary: '#666666',
        accent: '#82B1FF',
        error: '#FF5252',
        info: '#2196F3',
        success: '#4CAF50',
        warning: '#FFC107',
      },
    },
  },
  icons: {
    iconfont: 'mdi',
  },
  lang: {
    locales: { zhHans },
    current: 'zhHans',
  },
})
