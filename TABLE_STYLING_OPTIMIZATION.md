# 询价单详细列表表格样式优化总结

## 优化目标
美化询价单页面展开的详细列表表格，使其排列整齐、美观，提升用户体验。

## 主要优化内容

### 1. 表格结构优化

#### 列宽设置
- **修理费&加工费/其他费用/物料费用表格**:
  - 项目名称: 30%
  - 数量: 10%
  - 单价: 20%
  - 总价: 20%
  - 备注: 20%

- **备件费用表格**:
  - 备件名称: 15%
  - 备件编号: 15%
  - 图纸号: 12%
  - 序列号: 12%
  - 数量: 8%
  - 单价: 15%
  - 总价: 15%
  - 备注: 8%

#### 对齐方式
- **文本内容**: 左对齐 (`text-left`)
- **数字内容**: 右对齐 (`text-right`)
- **编号/代码**: 居中对齐 (`text-center`)
- **表头**: 居中对齐 (`text-center`)

### 2. 视觉样式优化

#### 表格边框和背景
```css
.detail-table {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}
```

#### 表头样式
```css
.detail-table .table-header th {
  background-color: #f5f5f5;
  font-weight: 600;
  border-bottom: 2px solid #e0e0e0;
  padding: 12px 8px;
  white-space: nowrap;
}
```

#### 表格行样式
```css
.detail-table .table-row td {
  padding: 10px 8px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  word-break: break-word;
}

.detail-table .table-row:hover {
  background-color: #f9f9f9;
}
```

### 3. 分类标题优化

#### 颜色区分
- **预估修理费&加工费**: 蓝色 (`primary--text`)
- **预估其他费用**: 绿色 (`success--text`)
- **预估备件费用**: 橙色 (`warning--text`)
- **预估物料费用**: 浅蓝色 (`info--text`)

#### 标题样式
```css
h4.mb-2 {
  font-size: 16px;
  font-weight: 600;
  border-left: 4px solid;
  padding-left: 12px;
  margin-bottom: 12px !important;
}
```

### 4. 交互体验优化

#### 悬停效果
- 表格行悬停时背景色变化
- 提升用户交互体验

#### 响应式设计
- 固定列宽确保表格整齐
- 文本换行处理避免内容溢出

### 5. 数据格式化

#### 货币显示
- 使用 `formatCurrency` 方法统一格式化
- 根据币种自动调整小数位数
- 千分位分隔符提高可读性

#### 空值处理
- 空值统一显示为 "-"
- 避免显示 undefined 或 null

## 优化效果

### 视觉效果
- ✅ 表格排列整齐，列宽固定
- ✅ 表头和数据行清晰区分
- ✅ 不同费用类型用颜色区分
- ✅ 悬停效果提升交互体验

### 数据展示
- ✅ 数字右对齐，便于比较
- ✅ 文本左对齐，便于阅读
- ✅ 编号居中对齐，整齐美观
- ✅ 货币格式统一，专业规范

### 用户体验
- ✅ 信息层次清晰
- ✅ 视觉效果美观
- ✅ 操作反馈及时
- ✅ 数据易于理解

## 技术实现

### CSS类应用
```vue
<v-simple-table dense class="detail-table">
  <thead>
    <tr class="table-header">
      <th class="text-center" style="width: 30%;">项目名称</th>
      <!-- 其他表头 -->
    </tr>
  </thead>
  <tbody>
    <tr class="table-row">
      <td class="text-left">{{ item.itemName || '-' }}</td>
      <td class="text-right">{{ formatCurrency(item.price, quote.ccyCode) }}</td>
      <!-- 其他数据 -->
    </tr>
  </tbody>
</v-simple-table>
```

### 样式继承
- 使用 `scoped` 样式避免全局污染
- 利用 Vuetify 的内置类进行基础样式
- 自定义样式补充特殊需求

这次优化大大提升了详细列表的视觉效果和用户体验，使数据展示更加专业和美观。
